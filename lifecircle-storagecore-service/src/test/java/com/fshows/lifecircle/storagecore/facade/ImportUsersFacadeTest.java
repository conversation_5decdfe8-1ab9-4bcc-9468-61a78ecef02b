/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.ImportUsersRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dao.RiskRecordDAO;
import com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.RiskRecordDO;
import com.fshows.lifecircle.storagecore.service.dal.lifecircle.dao.UsersTokenDAO;
import com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.UsersTokenDO;
import lombok.extern.slf4j.Slf4j;
import org.apache.poi.ss.formula.functions.T;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.jdbc.datasource.DataSourceTransactionManager;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.transaction.TransactionStatus;
import org.springframework.transaction.support.DefaultTransactionDefinition;

/**
 * <AUTHOR>
 * @version ImportUsersFacade.java, v 0.1 2024-09-20 16:32 chenxm
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class ImportUsersFacadeTest {
    
    @Autowired
    private ImportUsersFacade importUsersFacade;

    @Autowired
    @Qualifier("fsriskmanagementTransactionManager")
    private DataSourceTransactionManager fsriskmanagementTransactionManager;

    @Autowired
    @Qualifier("lifecircleTransactionManager")
    private DataSourceTransactionManager lifecircleTransactionManager;

    @Autowired
    RiskRecordDAO riskRecordDAO;

    @Autowired
    UsersTokenDAO usersTokenDAO;
    
    @Test
    public void test() throws InterruptedException {
        ImportUsersRequest importUsersRequest = new ImportUsersRequest();
        //importUsersRequest.setOssUrl("crm/admin/import/wanda/merchant/20240920/1726821017507【小黑瓶】连锁门店信息汇总表.xlsx");
        importUsersRequest.setOssUrl("crm/admin/import/wanda/merchant/20240927/1727417108217工作簿3.xlsx");
        importUsersFacade.importUsersInfo(importUsersRequest);

        Thread.sleep(10000);
    }
}