/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.test;

import com.fshows.lifecircle.storagecore.enums.ExportScaleEnum;
import com.fshows.lifecircle.storagecore.service.utils.ExcelExport;
import org.apache.poi.ss.usermodel.Row;
import org.junit.Before;
import org.junit.BeforeClass;
import org.junit.Test;
import org.springframework.boot.test.context.SpringBootTest;

import java.io.FileOutputStream;
import java.io.IOException;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.ArrayList;

/**
 * <AUTHOR>
 * @version ExcelExportTest.java, v 0.1 2024-01-29 16:55 ZFQ
 */
public class ExcelExportTest {
    ArrayList<String> headerList = new ArrayList<>();
    ArrayList<Integer> headerWidthList = new ArrayList<>();

    @Before
    public void init() {
        headerList.add("交易时间");
        headerList.add("商品类型");
        headerList.add("设备SN");
        headerList.add("名称");
        headerList.add("绑定商户");
        headerList.add("交易金额");
        headerList.add("奖励积分");
        headerList.add("封顶积分");
        headerList.add("待奖励积分");
//
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
//        headerWidthList.add(6000);
        try {
            Path path = Paths.get("D:/1.xlsx");
            if (Files.exists(path)) {
                Files.delete(path);
            }
            Files.createFile(path);
        } catch (IOException e) {
            throw new RuntimeException(e);
        }
    }
    @Test
  public void styleTest() {
      ExcelExport excelExport = new ExcelExport("硬件返佣列表数据", "", headerList, headerWidthList);
      Row row = excelExport.addRow();
      excelExport.addCell(row, 0, "2023-12");
      excelExport.addCell(row, 1, "设备");
      excelExport.addCell(row, 2, "530");
      excelExport.addCell(row, 3, "意锐加强版");
      excelExport.addCell(row, 4, "");
      excelExport.addNumberCell(row, 5, Double.valueOf(3977), ExportScaleEnum.TWO.getValue());
      excelExport.addNumberCell(row, 6, Double.valueOf(1000), ExportScaleEnum.TWO.getValue());
      excelExport.addNumberCell(row, 7, Double.valueOf(10000), ExportScaleEnum.TWO.getValue());
      excelExport.addNumberCell(row, 8, Double.valueOf(9000), ExportScaleEnum.TWO.getValue());
      try {
          FileOutputStream fileOutputStream = new FileOutputStream("D:/1.xlsx");
          excelExport.write(fileOutputStream);
          fileOutputStream.flush();
          fileOutputStream.close();
      } catch (Exception e) {
          throw new RuntimeException(e);
      }
  }
}
