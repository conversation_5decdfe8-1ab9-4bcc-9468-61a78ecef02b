/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.AliyunOssUtilFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.ImageCompressRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.AliyunOssUtilResponse;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import com.fshows.lifecircle.storagecore.service.domain.param.WxDownloadBillParam;
import com.fshows.lifecircle.storagecore.service.service.AliyunOssService;
import com.fshows.lifecircle.storagecore.service.service.HuifuAccountParseDataService;
import com.fshows.lifecircle.storagecore.service.service.WxDownloadBillService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @version WxDownloadBillServiceTest.java, v 0.1 2019-03-08 14:48
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class WxDownloadBillServiceTest {

    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private WxDownloadBillService wxDownloadBillService;
    @Autowired
    private AliyunOssService aliyunOssService;
    @Autowired
    private HuifuAccountParseDataService huifuAccountParseDataService;
    @Autowired
    private AliyunOssUtilFacade aliyunOssUtilFacade;

    @Test
    public void wxBillDownload() {
        WxDownloadBillParam param = new WxDownloadBillParam();
        param.setAppid("wxe9177240b021abbe");
        param.setBillDate("********");
        param.setBillType("ALL");
        param.setMchId("**********");
        long time = System.currentTimeMillis();
        System.out.println(wxDownloadBillService.downloadBillForWx(param));
        System.out.println("耗时：" + (System.currentTimeMillis() - time));
    }

    @Test
    public void testAliyun() {
        String image = "https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/qrorder/food/xhst/4.jpg";
        ImageCompressRequest request = new ImageCompressRequest();
        request.setFileUrl(image);
        AliyunOssUtilResponse aliyunOssUtilResponse = aliyunOssUtilFacade.uploadImageToAliyunOss(request);
        System.out.println(aliyunOssUtilResponse);

    }
}