/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import static org.junit.Assert.*;

import com.fshows.fsframework.core.utils.FsBeanUtil;
import com.fshows.lifecircle.storagecore.facade.domain.request.OperateExportValueRequest;
import com.fshows.lifecircle.storagecore.facade.DownloadSnExcelFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.ShipmentFileParseRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.StandingBookRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.StorageOrderExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.ExportCommonResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.StorageOrderExportResponse;
import com.fshows.lifecircle.storagecore.service.domain.param.StandingBookParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version StorageManageFacadeImplTest.java, v 0.1 2019-09-22 19:53 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StorageManageFacadeImplTest {

    @Autowired
    private StorageManageFacadeImpl storageManageFacadeImpl;

//    @Autowired
//    private DownloadSnExcelFacade downloadSnExcelFacade;


    @Test
    public void batchSnStoreRoomByExcel() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void uploadErrorSnExcel() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void shipmentFileParse() {
        ShipmentFileParseRequest shipmentFileParseRequest = new ShipmentFileParseRequest();
        shipmentFileParseRequest.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/hardwareadmingw/import/scanface/ChFJ2dbDMi.xlsx");
        shipmentFileParseRequest.setStorageOrder("SDB156924494676654");
        storageManageFacadeImpl.shipmentFileParse(shipmentFileParseRequest);
    }

    @Test
    public void inStoreroomDirectDetailListUpload() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void shipmentDetailExport() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void storageOrderExport() {
        StorageOrderExportRequest storageOrderExportRequest = new StorageOrderExportRequest();
        storageOrderExportRequest.setJobNumber("2867");
        storageOrderExportRequest.setExportType(1);
        storageOrderExportRequest.setOrderStatus(-1);
        storageOrderExportRequest.setDepot(-1);
        storageOrderExportRequest.setOrderType(1);
        storageOrderExportRequest.setPurchaseType(-1);
        storageOrderExportRequest.setBizType(1);
        storageOrderExportRequest.setExportType(1);
        StorageOrderExportResponse storageOrderExportResponse = storageManageFacadeImpl.storageOrderExport(storageOrderExportRequest);
        System.out.println(storageOrderExportResponse);
    }

    @Test
    public void standingBookExecute() {
        StandingBookRequest standingBookRequest = new StandingBookRequest();
        standingBookRequest.setStartTime("2020-04-13 00:00:00");
        standingBookRequest.setEndTime("2020-04-13 00:00:00");
        storageManageFacadeImpl.standingBookExecute(standingBookRequest);
    }

    @Test
    public void equipmentStockValueExport() {
        OperateExportValueRequest request = new OperateExportValueRequest();
        request.setJobNumber("12121");
        request.setPeriodsTime("2020-04-23");
        request.setUnionId("83483");
        storageManageFacadeImpl.equipmentStockValueExport(request);
    }
}