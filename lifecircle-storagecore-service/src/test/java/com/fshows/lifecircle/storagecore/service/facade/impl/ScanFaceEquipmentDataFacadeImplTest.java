/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import static org.junit.Assert.*;

import com.fshows.lifecircle.storagecore.facade.domain.request.ParseMerchantRemitFileRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.ParseScanFaceEquipmentFileRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.scheduling.annotation.EnableAsync;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ScanFaceEquipmentDataFacadeImplTest.java, v 0.1 2019-08-27 10:07 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
@EnableAsync
public class ScanFaceEquipmentDataFacadeImplTest {

    @Autowired
    private ScanFaceEquipmentDataFacadeImpl scanFaceEquipmentDataFacadeImpl;


    @Test
    public void parseScanFaceEquipmentFile() {
        ParseScanFaceEquipmentFileRequest request = new ParseScanFaceEquipmentFileRequest();
        request.setUserId("1232131");
        request.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/hardwareadmingw/import/scanface/FRMzh65WkK.xlsx");
        scanFaceEquipmentDataFacadeImpl.parseScanFaceEquipmentFile(request);
    }

    @Test
    public void parseMerchantRemitFile() {
        ParseMerchantRemitFileRequest parseMerchantRemitFileRequest = new ParseMerchantRemitFileRequest();
        parseMerchantRemitFileRequest.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/hardwareadmingw/import/scanface/72rd8x32er.xlsx");
        parseMerchantRemitFileRequest.setUserId("12345");
        scanFaceEquipmentDataFacadeImpl.parseMerchantRemitFile(parseMerchantRemitFileRequest);
    }

    @Test
    public void executeAlipayAntIotQuery() {
        scanFaceEquipmentDataFacadeImpl.executeAlipayAntIotQuery("");
    }
}