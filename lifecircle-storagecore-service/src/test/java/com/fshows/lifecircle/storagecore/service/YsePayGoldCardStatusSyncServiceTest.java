/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.domain.request.ysepay.YsePayGoldCardSyncRequest;
import com.fshows.lifecircle.storagecore.service.service.ysepay.YsePayGoldCardStatusSyncService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 *
 * <AUTHOR>
 * @version YsePayGoldCardStatusSyncServiceTest.java, v 0.1 2025-07-10 16:16 weikunpeng
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class YsePayGoldCardStatusSyncServiceTest {

    @Autowired
    private YsePayGoldCardStatusSyncService ysePayGoldCardStatusSyncService;


    @Test
    public void test() {
        YsePayGoldCardSyncRequest ysePayGoldCardSyncRequest = new YsePayGoldCardSyncRequest();
        ysePayGoldCardStatusSyncService.syncYseapyGoldCardStatus(ysePayGoldCardSyncRequest);
        ;
    }
}