package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.domain.param.YoudianMerchantGrantLogParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/23 17:18
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class YoudianMerchantGrantLogServiceTest {

    @Autowired
    YoudianMerchantGrantLogService youdianMerchantGrantLogService;

    @Test
    public void getMerchantGrantLogExportTest() {
        YoudianMerchantGrantLogParam param = new YoudianMerchantGrantLogParam();
        param.setStartTime("2020-01-01");
        param.setEndTime("2020-10-10");
        String fileUrl = youdianMerchantGrantLogService.getMerchantGrantLogExport(param);

        System.out.println(fileUrl);
    }
}
