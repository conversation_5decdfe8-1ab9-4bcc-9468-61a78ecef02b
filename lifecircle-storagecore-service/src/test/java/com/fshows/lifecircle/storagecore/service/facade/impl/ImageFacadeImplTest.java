/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import cn.hutool.json.JSONUtil;
import com.fshows.lifecircle.storagecore.facade.AlipayImageUploadFacade;
import com.fshows.lifecircle.storagecore.facade.ImageFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.AlipayBlueseaActivityImageUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.BatchImageCompressRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.ImageCompressRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.AlipayBlueseaActivityImageUploadResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.BatchImageCompressResponse;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ImageFacadeImplTest.java, v 0.1 2020-06-01 5:22 下午 wangjianguan
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ImageFacadeImplTest {

    @Autowired
    private ImageFacade imageFacade;

    @Autowired
    AlipayImageUploadFacade alipayImageUploadFacade;

    @Test
    public void compressDescFileSize() {
        BatchImageCompressRequest request = new BatchImageCompressRequest();
        ImageCompressRequest imageCompressRequest = new ImageCompressRequest();
        imageCompressRequest.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/admin/import/h5_block/7f0c67e68f30f12272f7c7de34ae466_1683515122328.jpg");
        imageCompressRequest.setFileName("12");
        request.setList(Lists.newArrayList(imageCompressRequest));
        request.setDescFileSize(2048L);
        BatchImageCompressResponse compress = imageFacade.compressDescFileSize(request);
        System.out.println(compress);
    }

    @Test
    public void compressV2() {
        BatchImageCompressRequest request = new BatchImageCompressRequest();
        ImageCompressRequest imageCompressRequest = new ImageCompressRequest();
        imageCompressRequest.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/images/store_image/lQLPJxOIQBk52Q7NCHDNDwCwy1DJsHqM7fEDg3TnvIBhAA_3840_2160.png");
        imageCompressRequest.setFileName("12");
        request.setList(Lists.newArrayList(imageCompressRequest));
        BatchImageCompressResponse compress = imageFacade.compressV2(request);
        System.out.println(compress);
    }


    @Test
    public void newBlueseaImageUploadTest() {
        AlipayBlueseaActivityImageUploadRequest request = new AlipayBlueseaActivityImageUploadRequest();
        request.setImageUrl("http://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/fs-mini-store/CRMFormalAliCloudOSSDirectory/activity-images/1590669865161.jpeg");

        AlipayBlueseaActivityImageUploadResponse response = alipayImageUploadFacade.blueseaActivityImageUpload(request);
        System.out.println("结果：" + JSONUtil.toJsonStr(response));

    }
}