/**
 * fshows.com
 * Copyright (C) 2013-2025 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import cn.hutool.core.collection.CollectionUtil;
import cn.hutool.core.io.FileUtil;
import cn.hutool.system.SystemUtil;
import com.fshows.fsframework.core.utils.OrderUtil;
import com.fshows.lifecircle.storagecore.enums.acct.PlatformCodeEnum;
import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.context.AcctPlatformDataLoadContext;
import com.fshows.lifecircle.storagecore.service.domain.param.acctbiz.datasync.PlatformDataDealParam;
import com.fshows.lifecircle.storagecore.service.domain.param.acctbiz.datasync.PlatformDataSyncParam;
import com.fshows.lifecircle.storagecore.service.service.acctbiz.datasync.PlatformDataDealService;
import com.fshows.lifecircle.storagecore.service.service.acctbiz.datasync.datahandler.fileread.handler.FileDownloadHandler;
import com.fshows.lifecircle.storagecore.service.service.acctbiz.datasync.datahandler.fileread.helper.TiktokFileReadHelper;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version PlatformDataDealServiceTest.java, v 0.1 2025-01-07 17:42 wangyi
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class PlatformDataDealServiceTest {

    @Autowired
    private PlatformDataDealService platformDataDealService;
    @Autowired
    private TiktokFileReadHelper tiktokFileReadHelper;

    @Test
    public void testPlatformDataDeal() {
        PlatformDataDealParam param = new PlatformDataDealParam();
        param.setConfigId("C20250331156103");
        param.setPlatformCode(PlatformCodeEnum.SIXUN.getCode());
        platformDataDealService.platformDataDeal(param);
    }

    @Test
    public void testPlatformDataSync() {
        PlatformDataSyncParam param = new PlatformDataSyncParam();
        param.setDataIdList(CollectionUtil.toList("20250506173128388907","20250506173128111922","20250506173128808499","20250506173128240507"));
        platformDataDealService.platformDataSync(param);
//        AcctPlatformDataLoadContext context = new AcctPlatformDataLoadContext();
//        context.setLocalPath("/Users/<USER>/Downloads/20250120204045072203.xlsx");
//        context.setDataId("123");
//        context.setDataSource("1");
//        context.setPlatformUserId("6990198902825682980");
//        //context.setCookie("csrf_session_id=b5a40f4ae85aa8e1bacf0c55df4752c9; is_hit_partitioned_cookie_canary=true; is_hit_partitioned_cookie_canary_ss=true; sid_guard_ls=44c095e6ea702a3ddc9f85f2eb8afa4c%7C1734692864%7C5184002%7CTue%2C+18-Feb-2025+11%3A07%3A46+GMT; uid_tt_ls=e2eee109b32f44b580f9bd1f2d3717d4; uid_tt_ss_ls=e2eee109b32f44b580f9bd1f2d3717d4; sid_tt_ls=44c095e6ea702a3ddc9f85f2eb8afa4c; sessionid_ls=44c095e6ea702a3ddc9f85f2eb8afa4c; sessionid_ss_ls=44c095e6ea702a3ddc9f85f2eb8afa4c; is_staff_user_ls=false; sid_ucp_v1_ls=1.0.0-KGI2OGJlZjU2MDJlZWI5ODU0YmU3NTVhNjg3MDNhOWY3MTA5NTI2NTYKGgjwkvCMjc2WAhCAoJW7BhjRwRIgDDgBQOsHGgJsZiIgNDRjMDk1ZTZlYTcwMmEzZGRjOWY4NWYyZWI4YWZhNGM; ssid_ucp_v1_ls=1.0.0-KGI2OGJlZjU2MDJlZWI5ODU0YmU3NTVhNjg3MDNhOWY3MTA5NTI2NTYKGgjwkvCMjc2WAhCAoJW7BhjRwRIgDDgBQOsHGgJsZiIgNDRjMDk1ZTZlYTcwMmEzZGRjOWY4NWYyZWI4YWZhNGM; _lxsdk_cuid=1912fb8a20fc8-09bf5a072b4eea-4c657b58-144000-1912fb8a20f74; _lxsdk=1912fb8a20fc8-09bf5a072b4eea-4c657b58-144000-1912fb8a20f74; _hc.v=94fde4c8-9932-46a9-d72f-abbbce306bd1.1723082516; passport_csrf_token=e38dc33d18781f9db9ae2207f0fc5b30; passport_csrf_token_default=e38dc33d18781f9db9ae2207f0fc5b30; ttwid=1%7CbGUXMIS1qB0wD5AgKm_XbKWnu7qlOB0ZAmhhfmQJ6_M%7C1734692865%7C62302072ab041e24d185d90740cad618ca20a708b989352adebb6b3a4967f463; be-token=hermes_token_auth_8be002264970d42644663f4bad0ee993; be-token-scene=1; odin_tt=dac0780b461f5f461bc2c21df8eb400068c0e51897e2ff3b432db81212d623e1b6d35f2cfacab5feb7382929a976b266");
//        System.out.println(tiktokFileReadHelper.loadTiktokFinanceFile(context));
    }

    public static void main(String[] args) {
        // 下载到本地
        // 创建文件夹
        String fileDir = SystemUtil.getUserInfo().getHomeDir() + "/acctbiz-platform-file/";
        if (!FileUtil.isDirectory(fileDir)) {
            FileUtil.mkdir(fileDir);
        }
        String tempFileName = OrderUtil.generateOrderSn() + ".xlsx";
        String fileUrl = "https://sf3-sign.douyinstatic.com/aweme-namek-secret/download_record/bill/7457137084487174144/%E8%B4%A6%E5%8D%95_2025-01-06_2025-01-06.xlsx?lk3s=139a54f3&x-expires=1738930606&x-signature=Z5EiPi6AEN1k7WdOx3VUnEdtrpk%3D";
        try {
            FileDownloadHandler.downloadFile(fileUrl,fileDir, tempFileName);
        } catch (Exception e) {
            System.err.println("文件下载失败: " + e.getMessage());
        }
    }
}