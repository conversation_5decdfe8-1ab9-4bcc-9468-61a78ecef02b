package com.fshows.lifecircle.storagecore.service.facade.impl;

import cn.hutool.core.codec.Base64;
import com.aliyun.oss.OSSClient;
import com.aliyun.oss.model.PutObjectResult;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.AliyunOssUtilFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.AliyunOssUploadZipRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.AliyunOssUtilRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.AliyunOssUploadZipResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.AliyunOssUtilResponse;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOssUtilModel;
import com.fshows.lifecircle.storagecore.service.utils.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import sun.misc.BASE64Decoder;
import sun.misc.BASE64Encoder;

import javax.imageio.ImageIO;
import javax.sql.rowset.serial.SerialBlob;
import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * <AUTHOR>
 * @version AliyunOssUtilFacadeTest.java, v 0.1 2019-09-10 3:48 下午 zhangshuan
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class AliyunOssUtilFacadeTest {

    static BASE64Encoder encoder = new sun.misc.BASE64Encoder();
    static BASE64Decoder decoder = new sun.misc.BASE64Decoder();
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private AliyunOssUtilFacade aliyunOssUtilFacade;

    public static SerialBlob decodeToImage(String imageString) throws Exception {
        BASE64Decoder decoder = new BASE64Decoder();
        byte[] imageByte = decoder.decodeBuffer(imageString);
        return new SerialBlob(imageByte);
    }

    /**
     * 测试上传
     */
    @Test
    public void testUpload() {
        File uploadFile = new File("/Users/<USER>/Downloads/身份证正面.jpg");
        BufferedImage bi;

//        byte[] bytes = null;
        String encode = null;
        try {
            bi = ImageIO.read(uploadFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);
            byte[] bytes = baos.toByteArray();
            encode = Base64.encode(bytes);
//            encode = encoder.encodeBuffer(bytes);
        } catch (Exception e) {

        }
        AliyunOssUtilRequest request = new AliyunOssUtilRequest();
        request.setFileName("身份证正面.jpg");
        request.setFileData(encode);
        request.setFilePath("marketcore/wxmerchantapply/query/");
        AliyunOssUtilResponse upload = aliyunOssUtilFacade.upload(request);
        System.out.println(upload.getFileUrl());
    }

    /**
     * 测试下载
     */
    @Test
    public void testDownload() {
//        aliyunOssUtilFacade.download()
        File f = new File("/Users/<USER>/Downloads/身份证正面.jpg");
        BufferedImage bi;
        try {
            bi = ImageIO.read(f);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, "jpg", baos);
            byte[] bytes = baos.toByteArray();
            String encode = encoder.encodeBuffer(bytes).trim();
            System.out.println();

            AliyunOssUtilRequest request = new AliyunOssUtilRequest();
            request.setFileName("身份证正面.jpg");
            request.setFileData(encode);
            request.setFilePath("marketcore/wxmerchantapply/query/");
            AliyunOssUtilResponse upload = aliyunOssUtilFacade.upload(request);
            System.out.println(upload.getFileUrl());
        } catch (IOException e) {
            e.printStackTrace();
        }

    }

    @Test
    public void testUploadByteImage() throws Exception {
        OSSClient ossClient = new OSSClient(sysConfig.getAliyunOssEndpoint(), sysConfig.getAliyunAccessKey(), sysConfig.getAliyunSecretKey());
//        String byteImage = "cGFnZXMvYXBwbHkvYXBpdzQvd2VsY29tZS93ZWxjb21lP2FwcGx5bWVudF9pZD14eHg=";
//        BASE64Decoder decoder = new BASE64Decoder();
//        byte[] decode = Base64.decode(byteImage);
        File uploadFile = new File("/Users/<USER>/Downloads/身份证正面.jpg");
        BufferedImage bi;
        byte[] bytes = null;
        String encode = null;
        try {
            bi = ImageIO.read(uploadFile);
            ByteArrayOutputStream baos = new ByteArrayOutputStream();
            ImageIO.write(bi, ".jpg", baos);
            bytes = baos.toByteArray();
            encode = Base64.encode(bytes);
        } catch (Exception e) {

        }
        SerialBlob serialBlob = decodeToImage(encode);
        InputStream binaryStream = serialBlob.getBinaryStream();
        ossClient.putObject(sysConfig.getAliyunOssOpenApiBucket(), "marketcore/wxmerchantapply/query/身份证正面.jpg", binaryStream);
//        model.setFileUrl("https://" + sysConfig.getAliyunOssOpenApiBucket() + "." + sysConfig.getAliyunOssEndpoint() + "/" + param.getFilePath() + param.getFileName());

    }

    @Test
    public void testGetUrl() {
        String base64 = "image/jpg;base64,/9j/4AAQSkZJRgABAQEAYABgAA";
        String substring = base64.substring(base64.lastIndexOf(","));
        System.out.println(substring);
    }

    @Test
    public void uploadZip() {
        List<String> fileUrlList = new ArrayList<>();
        fileUrlList.add("https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/crm/app/image/10003642e52c98e080095411a2378c60.jpg");
        fileUrlList.add("https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/crm/app/image/1003dd7f9ca5a81cb1708f1cbc25ef73.jpg");

        AliyunOssUploadZipRequest request = new AliyunOssUploadZipRequest();
        request.setFileUrlList(fileUrlList);
        AliyunOssUploadZipResponse response = aliyunOssUtilFacade.uploadZip(request);
        System.out.println(response.getZipFileUrl());
    }


    @Test
    public void uploadFiles() throws InterruptedException {

        AtomicInteger a = new AtomicInteger(0);

        ExecutorService executorService = Executors.newFixedThreadPool(20);
        OSSClient ossClient = new OSSClient(sysConfig.getAliyunOssEndpoint(), sysConfig.getAliyunAccessKey(), sysConfig.getAliyunSecretKey());
        List<File> files = (List<File>) FileUtils.listFiles(new File("/Users/<USER>/Downloads/lite"), null, true);
        for (File file : files) {
            executorService.submit(() -> {
                String absolutePath = file.getAbsolutePath();
                int voice = absolutePath.indexOf("voice");
                ossClient.putObject(sysConfig.getAliyunOssPublicBucketname(), absolutePath.substring(voice).replaceAll("\\\\", "/"), file);
                a.addAndGet(1);
            });
        }

        while (true) {
            System.out.println("upload >> " + a.get());

            if (a.get() >= 100000) {
                break;
            }

            Thread.sleep(1000);
        }

    }

    @Test
    public void buildVoiceFiles() throws InterruptedException {
        AtomicInteger a = new AtomicInteger(0);
        ExecutorService executorService = Executors.newFixedThreadPool(5);
        List<File> files = (List<File>) FileUtils.listFiles(new File("/Users/<USER>/Downloads/sk"), null, true);
        for (File file : files) {
            executorService.submit(() -> {
                int i = StringUtils.lastIndexOf(file.getName(), ".");
                if (i <= 0) return;
                String price = file.getName().substring(0, i);
                int index = new BigDecimal(price).divide(BigDecimal.valueOf(100)).intValue() + 1;
                System.out.println(file.getName() + " " + price + " " + index);
                try {
                    FileUtils.copyFileToDirectory(file, new File("/Users/<USER>/Downloads/lite/voice/pay/" + index), false);
                    LogUtil.info(log, "copy >> " + file.getName());
                    a.addAndGet(1);
                } catch (IOException e) {
                    e.printStackTrace();
                }
            });
        }


        while (true) {
            if (a.get() >= 100000) {
                break;
            }

            Thread.sleep(1000);
        }

        Collection<File> listFiles = FileUtils.listFiles(new File("/Users/<USER>/Downloads/lite/voice/pay/"), null, true);
        LogUtil.info(log, "copy size=" + listFiles.size());
    }

    @Test
    public void testUploaddByteImage() throws Exception {
        OSSClient ossClient = new OSSClient(sysConfig.getAliyunOssEndpoint(), sysConfig.getAliyunAccessKey(), sysConfig.getAliyunSecretKey());
        String localPath = "/Users/<USER>/Downloads/img.txt";
        List<String> lineList = FileUtil.parseTxtFile(localPath);
        String encode = lineList.get(0);
        SerialBlob serialBlob = decodeToImage(encode);
        InputStream binaryStream = serialBlob.getBinaryStream();
        PutObjectResult result = ossClient.putObject(sysConfig.getAliyunOssOpenApiBucket(), "marketcore/wxmerchantapply/query/身份证正面.jpg", binaryStream);
        AliyunOssUtilModel model = new AliyunOssUtilModel();
        if (null == result || StringUtils.isBlank(result.getETag())) {
            model.setSuccess(false);
        } else {
            model.setFileUrl(sysConfig.getQrorderOssBaseHost() + "/marketcore/wxmerchantapply/query/身份证正面.jpg");
            model.setSuccess(true);
            model.setETag(result.getETag());
        }
        System.out.println(model);
    }

}