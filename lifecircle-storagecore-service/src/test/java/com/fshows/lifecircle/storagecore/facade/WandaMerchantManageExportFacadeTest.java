/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.facade.domain.request.wanda.WandaMerchantConfirmImageRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.response.WandaMerchantConfirmImageResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version WandaMerchantManageExportFacadeTest.java, v 0.1 2022-10-27 上午11:05 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class WandaMerchantManageExportFacadeTest {

    @Autowired
    private WandaMerchantManageExportFacade wandaMerchantManageExportFacade;


    @Test
    public void batchCreateConfirmImage() {

        //----------- Arrange -----------//
        WandaMerchantConfirmImageRequest request = new WandaMerchantConfirmImageRequest();
        request.setStoreId(9000100163L);
        WandaMerchantConfirmImageResponse confirmImage = wandaMerchantManageExportFacade.createConfirmImage(request);
        //-----------   Act   -----------//
        System.out.println(JSON.toJSONString(confirmImage));

        //-----------  Assert -----------//
    }

    @Test
    public void sweetGreenClubImport() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void testBatchCreateConfirmImage() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void batchDismiss() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void exportMerchantList() {

        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }
}