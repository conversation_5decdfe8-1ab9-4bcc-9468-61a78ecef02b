package com.fshows.lifecircle.storagecore.service.test;/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */

import com.fshows.lifecircle.storagecore.service.utils.CSBUtil;
import com.fshows.lifecircle.storagecore.service.utils.GetFileMD5Util;
import com.fshows.lifecircle.storagecore.tools.DateUtil;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import sun.misc.BASE64Encoder;

import java.io.BufferedInputStream;
import java.io.File;
import java.io.FileInputStream;
import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.util.ArrayList;
import java.util.HashMap;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class PrepayCardUploadTest {

    public static ArrayList<HashMap<String, String>> initCopInfo(String uniqueNo) {
        //CSB url
        ArrayList<HashMap<String, String>> corpInfoList = new ArrayList<HashMap<String, String>>();
        // 存放公司信息map
        HashMap<String, String> elemen = new HashMap<String, String>();
        // 联网发行唯一标识
        elemen.put("uniqueNo", uniqueNo);
        // 调用csb的url
//        elemen.put("url", "http://101.132.39.129:8086/CSB");
        elemen.put("url", "http://183.194.242.27/CSB");
        // 连接csb发布服务的ak sk
//        elemen.put("ak", "e7ec2742cad4432d9700ecb8d56d28a0");
        elemen.put("ak", "e7ec2742cad4432d9700ecb8d56d28a0");
//        elemen.put("sk", "y8ONI4Ifx4iWreeOJGRBkK+Im2o=");
        elemen.put("sk", "y8ONI4Ifx4iWreeOJGRBkK+Im2o=");
        // 私钥
        elemen.put("privateKey",
                "MIICdwIBADANBgkqhkiG9w0BAQEFAASCAmEwggJdAgEAAoGBAIHmMbCmQjWcD9fYNK0eIm0J43qvvMJIUSYV7h+51hVJeTnQjdrRXIFSfKN08zxM0sXlU/4BYAqI/XvRjoo6YFxdzqN8DimHMh/dSW8yxzQoIrSekFSwiDJXBCd1X4gIXKcUr4/2/iEVJVMfhrAR+MuA0yeAz0qEQ36aXjKRVlUzAgMBAAECgYAYcuohVHmbu37j4kav2r8iBL1zBwahDplvcKs7/29C3GxbrFw2tjcSBCN/ZHCvhcoaAzrW0Q1xji0fk+afL7OZCimJPg0eonb/09Rikat9OyhTCvAmNJwOibjdqMqv0A+3NjBTafxoqDRw/IMN6qN6l0JSgC96QySkNebnvoYGAQJBAMJufGGB0Wj8+P/8duOU8vwc8rtM7JJUnHp3vkrUZMuzxNC+wtKAd5aQbqwcLe4uU+USyJW4edFolGIxWlQxhjMCQQCrCGraaTbbqESWcTgF2cMEGeJx+/f395/JV7q3L5RUmUq+JkWSPc9BAKnnqHiEL05QQBh4B5fh2BXFTLWXcvUBAkEAgPtyn+zS0uce+BKvfAJPv1gk3oQFXH2s4MGXB2UnCMtVAXXLIIIA0201Pg/jmGI7XXe8gTZD+dt6tDkxRzEjaQJAXzJ6N0Ok8zJ/qrUuF6OTtq6pqgQzi2PbHnikRt5G06fXQGuRActBls7Fw1m0au7hyi1C8VxYn65kv8LouHmWAQJBAIdONNVv6ItPDN07YwvDQQGrjCgdi8qzUSVU0hl4kc/xUSwGdqfS7rmYjvQ7IGzt6tWq1oLUG/JqsQr8gP2fHh0=");
        corpInfoList.add(elemen);
        return corpInfoList;
    }

    @Test
    public void test4() throws Exception {
        CSBUtil st = new CSBUtil();

        // 初始化企业配置信息
        String uniqueNo = "310112F5215942100667";
        HashMap<String, String> map = (HashMap) initCopInfo(uniqueNo).get(0);

        // 文件大小限制
        int length = 1024 * 1024 * 2;
        byte[] buffer = new byte[length];

        // 获取文件，创建文件流
        String path = "/Users/<USER>/Desktop/test.txt";
        File file = new File(path);
        InputStream ins = new BufferedInputStream(new FileInputStream(path));
        long fileLength = file.length();
        System.err.println("fileLength==" + fileLength);
        // 文件偏移量
        int offSet = 0;
        String gm = GetFileMD5Util.getFileMD5(path);
        try {
            int bytesRead = 0;
            String endFileFlag = "";
            String flwno = "";
            // 第一次 读取文件
            bytesRead = ins.read(buffer);
            if (bytesRead < length) {
                endFileFlag = "Y";
            } else {
                // 设置结束标志
                endFileFlag = "N";
            }
            // 除去空格， BASE64转码
            BASE64Encoder encoder = new BASE64Encoder();
            // 读取文件的byte字节流
            byte[] destbuff = new byte[bytesRead];
            System.arraycopy(buffer, 0, destbuff, 0, bytesRead);
            String aesBase64 = encoder.encode(destbuff).replaceAll("[\r\n]", "");

            // 拼接报送字符串
            String sendCardsJsonData = "{\"dataMap\":{\"systemId\":\"888666953\",\"systemName\":\"wanda\",\"fileMD5\":\"" + gm + "\",\"uniqueNo\":\"" + uniqueNo + "\","
                    + "\"fileSize\":\"" + fileLength + "\"," + "\"fileName\":\"link\"," + "\"fileOffset\":\"" + offSet
                    + "\"," + "\"uploadEnd\":\"" + endFileFlag + "\"," + "\"fileContent\":\"" + aesBase64 + "\",\"fileDate\":\"" + DateUtil.getTodayDate() + "\"}}";

            // 调用csb方法
            flwno = st.sendCSB((String) map.get("ak"), (String) map.get("sk"), (String) map.get("url"),
                    (String) map.get("privateKey"), (String) sendCardsJsonData, (String) map.get("uniqueNo"),
                    "fileUploadDataService");
            // boolean sendEndFlag = false;
            // 从文件中按字节读取内容，到文件尾部时read方法将返回-1
            while ((bytesRead = ins.read(buffer)) != -1) {
                // 如果长度小于2M
                offSet = offSet + bytesRead;
                if (bytesRead < length) {
                    // 设置结束标志
                    endFileFlag = "Y";
                    // sendEndFlag = true;
                } else {
                    endFileFlag = "N";
                }
                // 除去出空格
                encoder = new BASE64Encoder();
                destbuff = new byte[bytesRead];
                System.arraycopy(buffer, 0, destbuff, 0, bytesRead);
                aesBase64 = encoder.encode(destbuff).replaceAll("[\r\n]", "");
                // 拼接json字符串
                sendCardsJsonData = "{\"dataMap\":{\"uniqueNo\":\"" + uniqueNo + "\"," + "\"uploadFlowNo\":\"" + flwno
                        + "\"," + "\"fileOffset\":\"" + offSet + "\"," + "\"uploadEnd\":\"" + endFileFlag + "\","
                        + "\"fileContent\":\"" + aesBase64 + "\"}}";
                // 调用连接csb的方法
                st.sendCSB((String) map.get("ak"), (String) map.get("sk"), (String) map.get("url"),
                        (String) map.get("privateKey"), (String) sendCardsJsonData, (String) map.get("uniqueNo"),
                        "fileUploadDataService");
            }
        } catch (FileNotFoundException ex) {
            ex.printStackTrace();
        } catch (IOException ex) {
            ex.printStackTrace();
        } finally {
            // 关闭 InputStream
            try {
                if (ins != null)
                    ins.close();
            } catch (IOException ex) {
                ex.printStackTrace();
            }
        }

    }
}
