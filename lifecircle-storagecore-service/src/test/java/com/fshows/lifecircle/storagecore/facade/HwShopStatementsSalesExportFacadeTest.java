/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopStatementsSalesExportListRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopStatementsSalesExportFacadeTest.java, v 0.1 2021-11-04 21:22 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class HwShopStatementsSalesExportFacadeTest {

    @Autowired
    private HwShopStatementsSalesExportFacade hwShopStatementsSalesExportFacade;

    @Test
    public void exportStatementsSalesOrderList() {
        HwShopStatementsSalesExportListRequest request = new HwShopStatementsSalesExportListRequest();
        request.setSearchType(1);
        request.setJobNumber("4583");
        hwShopStatementsSalesExportFacade.exportStatementsSalesOrderList(request);
    }
}