/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.test;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.io.file.FileWriter;
import com.fshows.fsframework.core.constants.StringPool;
import com.fshows.lifecircle.storagecore.common.constants.StoragecoreConstants;
import com.fshows.lifecircle.storagecore.service.domain.dto.PrepayCardUploadSalesCardDTO;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.SystemUtils;

import java.io.File;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version TestString.java, v 0.1 2021-03-30 10:28 zhoujp
 */
public class TestString {
    public static void main(String[] args) {
//        String aa = "35.23,332.29,112.04,81.59,125.15,9.67,344.61,16.01,1366.81,26.07,14.95,57.77,28.42,1218.94,978.66,21.6,190.12,120.37,17.88,37,1139.33,75.39,69.6,70.55,60.18,87.67,839.14,825.14,850.91";
//
//        String bb = "35.23,265.83,100.84,57.11,112.64,7.74,275.69,11.21,1230.13,18.25,11.96,46.22,19.89,975.15,685.06,15.12,152.1,96.3,12.52,25.9,911.46,52.77,48.72,49.39,60.18,61.37,839.14,577.6,850.91";
//
//        String cc = "202101318029613,2021013180231582,2021013180234411,2021013180238723,2021013180239133,2021013180246138,2021013180247729,2021013180248834,2021013180252617,2021013180252731,2021013180253607,2021013180254149,2021013180255793,2021013180259095,2021013180259147,2021013180259148,2021013180259578,2021013180262301,2021013180263978,2021013180267141,2021013180269742,2021013180269928,2021013180271907,2021013180272230,2021013180273801,2021013180274079,2021013180275156,2021013180275423,2021013180277768";
//
//        String[] str_aa = aa.split(",");


        List<PrepayCardUploadSalesCardDTO> uploadSalesCardDTOList = new ArrayList<>();
        PrepayCardUploadSalesCardDTO cardDTO = new PrepayCardUploadSalesCardDTO();
        cardDTO.setApplyTime(20220809145122L);
        cardDTO.setBeginDate(20220809);
        cardDTO.setCardBalance("10.00");
        cardDTO.setCardID("123456");
        cardDTO.setCardMon("8.80");
        cardDTO.setCardNo("123456");
        cardDTO.setExpiryDate(20990809);
        cardDTO.setIsRegister(0);
        cardDTO.setSerialNo("sales-123456");


        PrepayCardUploadSalesCardDTO cardDTO2 = new PrepayCardUploadSalesCardDTO();
        cardDTO2.setApplyTime(20220809145122L);
        cardDTO2.setBeginDate(20220809);
        cardDTO2.setCardBalance("10.00");
        cardDTO2.setCardID("6789012");
        cardDTO2.setCardMon("8.80");
        cardDTO2.setCardNo("123456");
        cardDTO2.setExpiryDate(20990809);
        cardDTO2.setIsRegister(0);
        cardDTO2.setSerialNo("sales-6789012");
        uploadSalesCardDTOList.add(cardDTO);
        uploadSalesCardDTOList.add(cardDTO2);

        //文件保存路径
        String savePath = SystemUtils.getUserHome() + StoragecoreConstants.PREPAY_CARD_UPLOAD_FILE_DIR;
        //取文件名称
        String fileName = "20220914.txt";
        String fileFullName = savePath + fileName;
        System.out.println("fileFullName=" + fileFullName);
        String pipes = StringPool.PIPE + StringPool.PIPE;
        if (!FileUtil.exist(fileFullName)) {
            File touch = FileUtil.touch(fileFullName);
            FileWriter writer = new FileWriter(touch);
            for (PrepayCardUploadSalesCardDTO salesCardDTO : uploadSalesCardDTOList) {
                String oneLineData = StringUtils.join(
                        "0", pipes,
                        salesCardDTO.getSerialNo(), pipes,
                        salesCardDTO.getCardID(), pipes,
                        salesCardDTO.getCardNo(), pipes,
                        salesCardDTO.getCardMon(), pipes,
                        salesCardDTO.getCardBalance(), pipes,
                        salesCardDTO.getApplyTime(), pipes,
                        salesCardDTO.getBeginDate(), pipes,
                        salesCardDTO.getExpiryDate(), pipes,
                        salesCardDTO.getIsRegister(),
                        pipes, pipes, pipes, pipes, pipes, pipes, pipes, pipes, pipes,
                        StringPool.CRLF
                );
                writer.append(oneLineData);
            }
        }
        System.out.println("success");
    }
}