/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.LeshuaDirectBillFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.LeshuaDirectBillCheckRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.LeshuaDirectBillCheckResponse;
import com.fshows.lifecircle.storagecore.service.domain.model.AlipayDownloadBillModel;
import com.fshows.lifecircle.storagecore.service.domain.param.AlipayDownloadBillParam;
import com.fshows.lifecircle.storagecore.service.service.AlipayDownloadBillService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version AlipayDownloadBillServiceTest.java, v 0.1 2019-05-08 15:09 sus
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class AlipayDownloadBillServiceTest {
    String token = "203726";
    String appAuthToken = "201905BB1e27facfbc6444c2a1de5904abc79X09";
    @Autowired
    private AlipayDownloadBillService alipayDownloadBillService;

    @Autowired
    private LeshuaDirectBillFacade leshuaDirectBillFacade;

    @Test
    public void alipayBillDownload() {
        AlipayDownloadBillParam param = new AlipayDownloadBillParam();
        param.setToken(token);
        param.setAppAuthToken(appAuthToken);
        param.setBillDate("2019-05-07");
        long time = System.currentTimeMillis();
        AlipayDownloadBillModel model = alipayDownloadBillService.downloadBillForAlipay(param);
        System.out.println(model);
        System.out.println("耗时：" + (System.currentTimeMillis() - time));
    }

    @Test
    public void checkLeshuaDirectBillTest() {
        LeshuaDirectBillCheckRequest request = new LeshuaDirectBillCheckRequest();
        request.setTradeDate("2020-11-10");

        LeshuaDirectBillCheckResponse leshuaDirectBillCheckResponse = leshuaDirectBillFacade.checkLeshuaDirectBill(request);

        System.out.println(leshuaDirectBillCheckResponse);
    }


}