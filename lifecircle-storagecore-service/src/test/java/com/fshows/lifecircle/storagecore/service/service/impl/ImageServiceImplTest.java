/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.service.service.ImageService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ImageServiceImplTest.java, v 0.1 2020-06-01 5:44 下午 wangjianguan
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ImageServiceImplTest {

    @Autowired
    private ImageService imageService;

    @Test
    public void compress() {
    }

    @Test
    public void compressPixel() {
        String s = imageService.compressPixel("http://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/fs-mini-store/CRMFormalAliCloudOSSDirectory/activity-images/1590669865161.jpeg", 2160, 2000);

        LogUtil.info(log, "{}", s);
    }
}