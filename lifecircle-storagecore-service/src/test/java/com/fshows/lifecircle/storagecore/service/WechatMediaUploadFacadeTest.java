/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.WechatMediaUploadFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.WechatMchInfoRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.WechatMediaUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.WechatMarketUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;

/**
 * <AUTHOR>
 * @version WechatMediaUploadFacadeTest.java, v 0.1 2020-02-04 10:53 lixiang
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class WechatMediaUploadFacadeTest {

    @Autowired
    private WechatMediaUploadFacade wechatMediaUploadFacade;

    @Test
    public void upload() throws Exception {
        System.out.println(HMACSHA256("out_request_no0=202002201234567891&send_coupon_merchant=270860603&stock_id0=1206030000000003&key=20200218shouzhankeji199310135415", "20200218shouzhankeji199310135415"));


        String mchId = "1564335781";
        String mchSerialNo = "235C7EC59D6AFA9BBC2818401EF1678CBF6EC01E";
        String apiV3Key = "b2c6b39bb458b57e11d77760db3d4117";
        String privateKey =
                "MIIEvQIBADANBgkqhkiG9w0BAQEFAASCBKcwggSjAgEAAoIBAQCq2kVW+m6zsCPj\n" +
                        "758LUJ3+dAujr/VNQiT7EjyKBSVrdNH/GA5hdKPZi9BcV+xV9JlLM7NjJhqosuwO\n" +
                        "FH4Kdrl2WU3Csm5axCjaZdCnVdcdp22AnjGYgH/u6oY1PvZMe/utwelYCJ/X9mZO\n" +
                        "P+7iJ6aL7akyq5A3vTrHppGNWw/WsQQSr+/kYrUjnXbtU8LZWBAn5osq32iUWVFF\n" +
                        "gkqHqfqpWglJg8UCHuY1/d+B8duLEhq+xIzZi3pwNqL1FYL6PxPJo92yaMXgLKHj\n" +
                        "0hscmiTgTWOVotSkG/vPoI9Ribwu1NsVUez7SKEXEjv8gA8HDerOHBVBQX/PxeUY\n" +
                        "hsZgCIXjAgMBAAECggEBAJRDZfjcykRUjlC3GKWRCApQWelqQM0SoVTftnlvjdF6\n" +
                        "RuG/1nrr5WRb2CzKf+T3pi+EZh+caXYMDp0JW3dpH/h79ucGQI2zvGAKCs+rxkmN\n" +
                        "iZEbtp5IpLyH+H8sbEBjNzzKEdy7XzriMFWnVX4cOoMjcrjt+PDl3aRz/8tTQqNi\n" +
                        "/q0r5abpnEydcFRLSR/wXJBdn/NBWChjuHOgLlcVmri392rArCs3VOeFvpntZEG3\n" +
                        "uH+hVFSI33ZNgNcBazN9hto1FpDCbEYEMnHAzluPI7O7nTa4W8fcnmhTLMyy1JDu\n" +
                        "B7Len78nNPhsxg6wsloJT/nkTcro6hhwh88FozpY3AECgYEA1z69PsTCuGljo2Mr\n" +
                        "y4dSqtNnMDtdnHSlAHLu3XtrjjF48HrZKIxqSKREX9Ap9D9QMDSj/tQ/gbJiMs23\n" +
                        "wgvzkYlp0H6Zum+nLV99ngjegstlUc1FRyFbAyJzPpjXMtOBjjXJ1hah9+hT/xO7\n" +
                        "M1XcduyW2uUfDsoDA4py2PgZxsECgYEAyzPCyPMw6nEnAalccf6Kpq+qrLY+g5cr\n" +
                        "nYc9GXhm9q9S2qP+TgwIztZG69YGkf2YrIg12vL624piXC79hexY27PgF+hA/vEd\n" +
                        "BaYzkwYh1bVD5HiBMS2oos4C+szv5KB9gIIp1nGscKXAJ0BRCwiuwsQUloPJwrmD\n" +
                        "ygrCIw/eOaMCgYAhnKB8vskRdxOiHghmvfkAyAZjF8IkfmqNyWTXnUBMchlKpERo\n" +
                        "+pRWR6CNvbnvdDuXa4D4eDh6jlqbSFibqrYcUfL+JebnoCvg//pjxUrJ7e0iewWI\n" +
                        "eAL4WFbavY9NVo3yoR+lmN8uEU+ck342JABLaeKcyA6zhoQ191a73vJXgQKBgGLI\n" +
                        "wwTV1Q3j/EZ+gwaX6+ektqBH6JQoRXfLaxSax9Nc4DaSSuq+RvnmiYvU/FS0qCgX\n" +
                        "/7Z1MSbRW4fdADq+72O/Sk3VBuPpa2Z3qdLrxD8UY/EJ9CRoQfcDl8c9IdJPgzOa\n" +
                        "E6TAlkgdDBEI310x12Ov1yJk5wzXCw6JtF8/M5VpAoGAVEjR5vbidry6ihf2bOHL\n" +
                        "aqIBjMPIbXqmhdS9U2M97cmnDG6lI6+GLOet7nXTNhH8/+1DsHss+aHv+Zp3D+aX\n" +
                        "xwDphFw4CrOe9ETlIVUTWEfSjuUdurFOhYLXFjf3oJvu/L8M9lz2+j/XEDjwFPKO\n" +
                        "GZs4AcxiNsPOfHPHqzoLNDA=\n";

        WechatMchInfoRequest wechatMchInfoRequest = new WechatMchInfoRequest();
        wechatMchInfoRequest.setMchId(mchId);
        wechatMchInfoRequest.setApiV3Key(apiV3Key);
        wechatMchInfoRequest.setMchSerialNo(mchSerialNo);
        wechatMchInfoRequest.setPrivateKey(privateKey);


        WechatMediaUploadRequest request = new WechatMediaUploadRequest();
        request.setWechatMchInfoRequest(wechatMchInfoRequest);
        request.setImageUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/app/photo/upload/73e4b1c098badba75e1d1f444465f92f.jpg");
        WechatMarketUploadResponse response = wechatMediaUploadFacade.marketImageUpload(request);
        System.out.println(response);
    }


    public String HMACSHA256(String data, String key) throws Exception {

        Mac sha256_HMAC = Mac.getInstance("HmacSHA256");

        SecretKeySpec secret_key = new SecretKeySpec(key.getBytes("UTF-8"), "HmacSHA256");

        sha256_HMAC.init(secret_key);

        byte[] array = sha256_HMAC.doFinal(data.getBytes("UTF-8"));

        StringBuilder sb = new StringBuilder();

        for (byte item : array) {

            sb.append(Integer.toHexString((item & 0xFF) | 0x100).substring(1, 3));

        }

        return sb.toString().toUpperCase();

    }
}