/**
 * fshows.com
 * Copyright (C) 2013-2018 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.param.StoreAuditListExportFileParam;
import com.fshows.lifecircle.storagecore.service.service.impl.StoreAuditExportServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version StoreAuditExportServiceTest.java, v 0.1 2021-08-24 9:45 chenjn
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class StoreAuditExportServiceTest {

    @Autowired
    private StoreAuditExportServiceImpl storeAuditExportService;

    @Test
    public void testStoreAuditFileAudit() throws InterruptedException {
        StoreAuditListExportFileParam param = new StoreAuditListExportFileParam();
        param.setAuditType(-1);
        param.setOwnRun(-1);


        //storeAuditExportService.storeAuditFileAudit(param,"ceshi");
    }

}