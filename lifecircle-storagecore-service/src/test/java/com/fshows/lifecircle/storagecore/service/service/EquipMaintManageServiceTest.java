package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.param.alipaytouch.UploadWorkOrderBatchParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class EquipMaintManageServiceTest {

    @Autowired
    private EquipMaintManageService equipMaintManageService;

    @Test
    public void importWorkOrderChange() throws InterruptedException {
        UploadWorkOrderBatchParam param = new UploadWorkOrderBatchParam();
        param.setOssUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/admin/import/merchanttransfer/20240902/1725246904376变更作业员模版.xlsx");
        param.setOperatorId("2869773413B44FC29A939B6B030595E1");
        equipMaintManageService.importWorkOrderChange(param);
        Thread.sleep(1000000L);
    }
}