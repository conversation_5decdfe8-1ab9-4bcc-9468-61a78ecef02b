/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import cn.hutool.core.collection.CollectionUtil;
import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.service.domain.model.hwshop.HwShopGoodsSpuExportModel;
import com.fshows.lifecircle.storagecore.service.domain.param.hwshop.HwShopGoodsListExportParam;
import com.fshows.lifecircle.storagecore.service.service.export.ExportHandler;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @version HwShopGoodsListExportHandlerImplTest.java, v 0.1 2024-01-23 15:50 ZFQ
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class HwShopGoodsListExportHandlerImplTest {
    @Resource(name = "HwShopGoodsListExportHandler")
    private ExportHandler hwShopGoodsListExportHandlerImpl;

    @Test
    public void queryDataTest() {
        String json = "{\"goodsName\":\"丰收\",\"equipmentId\":\"\",\"goodsType\":\"\",\"status\":\"\",\"isInstallment\":\"\",\"isContainsExpress\":\"\",\"page\":1,\"pageSize\":10}";
        HwShopGoodsListExportParam param = JSON.parseObject(json, HwShopGoodsListExportParam.class);
        List list = hwShopGoodsListExportHandlerImpl.queryData(param);
        Assert.assertTrue(CollectionUtil.isNotEmpty(list));
        System.out.println(JSON.toJSONString(list));
    }
}
