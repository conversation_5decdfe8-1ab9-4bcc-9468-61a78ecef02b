package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.MerchantTransferBatchFileFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.ParseMerchantTransferBatchFileRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.ParseMerchantTransferBatchFileResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class MerchantTransferBatchFileFacadeImplTest {

    @Autowired
    private MerchantTransferBatchFileFacade merchantTransferBatchFileFacade;

    @Test
    public void parseMerchantTransferBatchFile() {
        ParseMerchantTransferBatchFileRequest request = new ParseMerchantTransferBatchFileRequest();
        request.setFileUrl("crm/admin/import/merchanttransfer/merchant_transfer_test.xlsx");
        request.setUserId("3C81C590E6AF46E59336580E231130B7");

        ParseMerchantTransferBatchFileResponse response = merchantTransferBatchFileFacade.parseMerchantTransferBatchFile(request);
        System.out.println(response);
    }

}