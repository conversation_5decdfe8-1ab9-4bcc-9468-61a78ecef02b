/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.intergration.client.IdentityAuthClient;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOssUtilModel;
import com.fshows.lifecircle.storagecore.service.domain.model.ImportCustomerModel;
import com.fshows.lifecircle.storagecore.service.domain.model.MybankUploadPhotoModel;
import com.fshows.lifecircle.storagecore.service.domain.model.WithdrawBillCheckModel;
import com.fshows.lifecircle.storagecore.service.domain.param.ImportCustomerParam;
import com.fshows.lifecircle.storagecore.service.domain.param.MybankUploadPhotoParam;
import com.fshows.lifecircle.storagecore.service.domain.param.WithdrawBillCheckParam;
import com.fshows.lifecircle.storagecore.service.service.AliyunOssService;
import com.fshows.lifecircle.storagecore.service.service.HuiFuAccountService;
import com.fshows.lifecircle.storagecore.service.service.HuiFuWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.HuifuAccountParseDataService;
import com.fshows.lifecircle.storagecore.service.service.MyBankWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.MybankPhotoService;
import com.fshows.lifecircle.storagecore.service.service.SxPayWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.SxpayShareWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.utils.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version MybankPhotoServiceImplTest.java, v 0.1 2019-04-03 16:16
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class MybankPhotoServiceImplTest {

    @Autowired
    MybankPhotoService mybankPhotoService;

    @Autowired
    private SysConfig sysConfig;


    @Autowired
    private MyBankWithdrawBillService myBankWithdrawBillService;

    @Autowired
    private HuiFuWithdrawBillService huiFuWithdrawBillService;

    @Autowired
    private HuiFuAccountService huiFuAccountService;
    @Autowired
    private AliyunOssService aliyunOssService;
    @Autowired
    private HuifuAccountParseDataService huifuAccountParseDataService;
    @Autowired
    private IdentityAuthClient identityAuthClient;
    @Autowired
    private SxPayWithdrawBillService sxPayWithdrawBillService;
    @Autowired
    private SxpayShareWithdrawBillService sxpayShareWithdrawBillService;


    @Test
    public void photoUploadServiceTest() {
        MybankUploadPhotoParam photoParam=new MybankUploadPhotoParam();
        photoParam.setPhotoType("01");
        photoParam.setPhotoUrl("http://img004.hc360.cn/g1/M07/2E/4E/wKhQMVLWRM6EXe_vAAAAAEF8g8Q191.JPG");
        MybankUploadPhotoModel mybankUploadPhotoModel= mybankPhotoService.uploadPhoto(photoParam);
        System.out.println("结果："+ JSON.toJSONString(mybankUploadPhotoModel));
    }

    @Test
    public void ftp() {
        try {
            /**
             * 'HOST' => '************',//HOST
             'PORT' => '1105',//端口号
             'USER' => 'sftp',//用户名
             'PASSWD' => 'Ek6!EST>6rG3',//密码
             */
            SFTPUtil sftpUtil = new SFTPUtil(
                   "sftp",
                   "Ek6!EST>6rG3",
                    "************",
                    "",
                    1105
            );
            //获取文件列表
            List<String> fileList = sftpUtil.list("/");
            System.out.println(fileList);
        } catch (Exception e) {
            System.out.println(e);
        }
    }

    @Test
    public void billCheck() {
        WithdrawBillCheckParam param=new WithdrawBillCheckParam();
        param.setTradeDate("********");
        WithdrawBillCheckModel withdrawBillCheckModel = myBankWithdrawBillService.withdrawBillCheck(param);
        System.out.println("结果："+ JSON.toJSONString(withdrawBillCheckModel));
    }

    @Test
    public void huifuTest() {
        WithdrawBillCheckParam param = new WithdrawBillCheckParam();
        param.setTradeDate("********");
        WithdrawBillCheckModel withdrawBillCheckModel = huiFuWithdrawBillService.withdrawBillCheck(param);
        System.out.println("结果：" + JSON.toJSONString(withdrawBillCheckModel));
    }

    @Test
    public void sxPayTest() {
        WithdrawBillCheckParam param = new WithdrawBillCheckParam();
        param.setTradeDate("********");
        WithdrawBillCheckModel withdrawBillCheckModel = sxPayWithdrawBillService.withdrawBillCheck(param);
        System.out.println("结果：" + JSON.toJSONString(withdrawBillCheckModel));
    }

    @Test
    public void sxPayShareTest() {
        WithdrawBillCheckParam param = new WithdrawBillCheckParam();
        param.setTradeDate("********");
        WithdrawBillCheckModel withdrawBillCheckModel = sxpayShareWithdrawBillService.shareWithdrawCheck(param);
        System.out.println("结果：" + JSON.toJSONString(withdrawBillCheckModel));
    }


    @Test
    public void customerTest() {

//        AuthentiationForm realNameVerifyForm = new AuthentiationForm();
//        realNameVerifyForm.setAccountName("林振宇");
//        realNameVerifyForm.setBankNo("6222021202000296781");
//        realNameVerifyForm.setIdcard("331081199405252617");
//        realNameVerifyForm.setMobile("***********");
//        realNameVerifyForm.setUid(1);
//        AuthentiationResult realNameVerifyFormResult = identityAuthClient.realNameVerifyWithOutMsg(realNameVerifyForm);
//        System.out.println(realNameVerifyFormResult);

        // fileUrl=huifu-account/2019-12-02/*************摩羲支付-客户信息.xlsx, channelType=1, bankType=3
        ImportCustomerParam importCustomerParam = new ImportCustomerParam();
        importCustomerParam.setBankType(3);
        importCustomerParam.setChannelType(1);
//        importCustomerParam.setFileUrl("huifu-account/2019-12-02/*************摩羲支付-客户信息.xlsx");
        importCustomerParam.setFileUrl("huifu-account/2019-12-03/*************摩羲支付-客户信息.xlsx");
        ImportCustomerModel withdrawBillCheckModel = huiFuAccountService.importCustomer(importCustomerParam);
        System.out.println(withdrawBillCheckModel);

    }

    @Test
    public void testUpload(){
        AliyunOssUtilModel aliyunOssUtilModel = huifuAccountParseDataService.uploadImageToAliyun("http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/6d1d519c6bf0ed7764750aa775fdf181.jpg", "crm/clue/protocol/");
        System.err.println(JSON.toJSON(aliyunOssUtilModel));
    }
}
