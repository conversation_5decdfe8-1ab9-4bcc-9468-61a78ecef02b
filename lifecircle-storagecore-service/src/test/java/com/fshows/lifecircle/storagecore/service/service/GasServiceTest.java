/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.domain.param.ExportGasOrderListParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version GasServiceTest.java, v 0.1 2021-12-06 3:47 下午
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class GasServiceTest {
    @Autowired
    private GasService gasService;

    /**
     * 导出燃气订单列表
     */
    @Test
    public void exportGasOrderListTest() {
        ExportGasOrderListParam exportGasOrderListParam = new ExportGasOrderListParam();
        exportGasOrderListParam.setStartTime("2021-05-10 00:00:00");
        exportGasOrderListParam.setEndTime("2021-10-10 23:59:59");
        gasService.exportGasOrderList(exportGasOrderListParam);
    }
}