/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.service.domain.model.harvest.CommonExportModel;
import com.fshows.lifecircle.storagecore.service.domain.model.harvest.EquipmentIntegralExportModel;
import com.fshows.lifecircle.storagecore.service.domain.model.harvest.EquipmentRewardExportModel;
import com.fshows.lifecircle.storagecore.service.domain.model.harvest.HarvestPlanEquipmentBatchImportModel;
import com.fshows.lifecircle.storagecore.service.domain.model.harvest.SalesmanEquipmentIntegralExportModel;
import com.fshows.lifecircle.storagecore.service.domain.param.harvest.EquipmentIntegralExportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.harvest.EquipmentRewardExportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.harvest.HarvestPlanEquipmentBatchImportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.harvest.SalesmanEquipmentIntegralExportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.harvest.SalesmanEquipmentRewardExportParam;
import com.fshows.lifecircle.storagecore.service.service.EquipmentActivityFileService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version EquipmentActivityFileServiceImplTest.java, v 0.1 2021-05-11 17:25
 */
@Slf4j
@SpringBootTest
@RunWith(SpringRunner.class)
public class EquipmentActivityFileServiceImplTest {
    @Autowired
    private EquipmentActivityFileService equipmentActivityFileService;

    @Test
    public void batchImportJoinHarvestActivityTest() {
        HarvestPlanEquipmentBatchImportParam param = new HarvestPlanEquipmentBatchImportParam();
        param.setUrl("crm/hardwareadmingw/import/scanface/丰收计划批量添加模板(1)_16218444446947aa6f1d2.xlsx");

        HarvestPlanEquipmentBatchImportModel model = equipmentActivityFileService.batchImportJoinHarvestActivity(param);
        LogUtil.info(log, "{}", model);
    }

    /**
     * 丰收计划硬件返佣导出
     * Impl.exportEquipmentReward >> 丰收计划硬件返佣导出接口入参equipmentRewardExportParam=
     * {"content":"","countType":"day","endDay":"2021-05-11","searchType":"equipmentSn","startDay":"2021-05-11","statisticsMonth":"2021-04"}
     */
    @Test
    public void exportEquipmentRewardTest() {
        EquipmentRewardExportParam equipmentRewardExportParam = new EquipmentRewardExportParam();
        equipmentRewardExportParam.setCountType("day");
        equipmentRewardExportParam.setStartDay("2021-05-11");
        equipmentRewardExportParam.setEndDay("2021-05-11");
        equipmentRewardExportParam.setSearchType("equipmentSn");
        equipmentRewardExportParam.setContent("");
        equipmentRewardExportParam.setEquipmentId(null);
        EquipmentRewardExportModel equipmentRewardExportModel = equipmentActivityFileService.exportEquipmentReward(equipmentRewardExportParam);

        LogUtil.info(log, "{}", equipmentRewardExportModel);
    }

    /**
     * 丰收计划硬件积分导出
     *
     */
    @Test
    public void exportEquipmentIntegralTest() {
        EquipmentIntegralExportParam equipmentIntegralExportParam = new EquipmentIntegralExportParam();
        equipmentIntegralExportParam.setStartDay("2021-05-11");
        equipmentIntegralExportParam.setEndDay("2021-05-11");
        equipmentIntegralExportParam.setEquipmentId(null);
        equipmentIntegralExportParam.setSearchType("equipmentSn");
        equipmentIntegralExportParam.setContent("");
        EquipmentIntegralExportModel equipmentIntegralExportModel = equipmentActivityFileService.exportEquipmentIntegral(equipmentIntegralExportParam);
        LogUtil.info(log, "{}", equipmentIntegralExportModel);
    }

    /**
     * 代理商后台硬件返佣导出接口
     */
    @Test
    public void exportSalesmanEquipmentRewardList() {
        SalesmanEquipmentRewardExportParam salesmanEquipmentRewardExportParam = new SalesmanEquipmentRewardExportParam();
        salesmanEquipmentRewardExportParam.setBelong(84044);
        salesmanEquipmentRewardExportParam.setStartDay("2022-03-01");
        salesmanEquipmentRewardExportParam.setEndDay("2022-03-01");
        salesmanEquipmentRewardExportParam.setSearchType("equipmentSn");
        salesmanEquipmentRewardExportParam.setContent("");
        salesmanEquipmentRewardExportParam.setEquipmentId(null);
        salesmanEquipmentRewardExportParam.setActivityType(2);
        CommonExportModel salesmanEquipmentRewardExportModel = equipmentActivityFileService.exportSalesmanEquipmentRewardList(salesmanEquipmentRewardExportParam);

        LogUtil.info(log, "{}", salesmanEquipmentRewardExportModel);
    }

    /**
     * 代理商后台硬件积分导出接口
     */
    @Test
    public void exportSalesmanEquipmentIntegral() {
        SalesmanEquipmentIntegralExportParam salesmanEquipmentIntegralExportParam = new SalesmanEquipmentIntegralExportParam();
        salesmanEquipmentIntegralExportParam.setSearchType("equipmentSn");
        salesmanEquipmentIntegralExportParam.setBelong(1562358);
        salesmanEquipmentIntegralExportParam.setContent("");
        salesmanEquipmentIntegralExportParam.setStartDay("2021-05-11");
        salesmanEquipmentIntegralExportParam.setEndDay("2021-05-11");
        salesmanEquipmentIntegralExportParam.setEquipmentId(null);
        SalesmanEquipmentIntegralExportModel salesmanEquipmentIntegralExportModel = equipmentActivityFileService.exportSalesmanEquipmentIntegral(salesmanEquipmentIntegralExportParam);

        LogUtil.info(log, "{}", salesmanEquipmentIntegralExportModel);
    }
}