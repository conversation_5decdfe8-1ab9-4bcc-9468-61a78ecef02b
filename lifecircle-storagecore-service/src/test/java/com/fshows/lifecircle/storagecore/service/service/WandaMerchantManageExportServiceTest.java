/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.param.wanda.WandaMerchantBatchConfirmImportParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version WandaMerchantManageExportServiceTest.java, v 0.1 2023-11-16 10:20 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class WandaMerchantManageExportServiceTest {

    @Autowired
    private WandaMerchantManageExportService wandaMerchantManageExportService;


    @Test
    public void batchCreateConfirmImage() {

        //----------- Arrange -----------//
        WandaMerchantBatchConfirmImportParam param = new WandaMerchantBatchConfirmImportParam();
        param.setOssUrl("crm/admin/import/wanda/merchant/20231116/1700101103193万达门店批量生成导入模板.xlsx");
        param.setBizKey("wanda");
        param.setUserId("123");
        param.setPollingType(40);
        wandaMerchantManageExportService.batchCreateConfirmImage(param);

        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }
}