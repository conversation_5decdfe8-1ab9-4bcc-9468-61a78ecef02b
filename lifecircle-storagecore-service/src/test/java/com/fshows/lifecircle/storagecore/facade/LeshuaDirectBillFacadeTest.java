/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version LeshuaDirectBillFacadeTest.java, v 0.1 2021-06-22 6:54 下午 youmingming
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LeshuaDirectBillFacadeTest {

    @Autowired
    private LeshuaDirectBillFacade leshuaDirectBillFacade;


    @Test
    public void downloadAndParseLeshuaShareBill() {
        String settleDate = "********";
        leshuaDirectBillFacade.downloadAndParseLeshuaShareBill(settleDate);
    }

    @Test
    public void downloadAndParseShandeBankBill() throws InterruptedException {
        String date="2021-11-18";
        leshuaDirectBillFacade.downloadAndParseShandeBankBill(date);
        Thread.sleep(1000*15);
    }

    @Test
    public void getShandeBankBillFileUrl() {
        String date="2021-11-18";
        String url = leshuaDirectBillFacade.getShandeBankBillFileUrl(69037, date);
        System.out.println(url);
    }
}