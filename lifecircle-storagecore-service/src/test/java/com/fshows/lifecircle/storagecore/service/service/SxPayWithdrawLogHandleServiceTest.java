/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version SxPayWithdrawLogHandleServiceTest.java, v 0.1 2021-07-30 4:16 下午 youmingming
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class SxPayWithdrawLogHandleServiceTest {

    @Autowired
    private SxPayWithdrawLogHandleService sxPayWithdrawLogHandleService;


    @Test
    public void handle() {
        String fileUrl = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/sxpay-settle-merchant.xlsx";
        sxPayWithdrawLogHandleService.handle(fileUrl);
    }
}