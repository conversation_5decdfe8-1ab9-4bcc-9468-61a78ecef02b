package com.fshows.lifecircle.storagecore.service.service.impl;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.common.constants.AccountConstant;
import com.fshows.lifecircle.storagecore.common.enums.HuifuCustomerTypeEnum;
import com.fshows.lifecircle.storagecore.intergration.client.domain.form.ImportCustomerDataForm;
import com.fshows.lifecircle.storagecore.service.domain.param.CheckImportDataParam;
import com.fshows.lifecircle.storagecore.service.domain.param.CustomerRegisterParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class HuifuAccountParseServiceImplTest {

    @Autowired
    private HuifuAccountParseServiceImpl huifuAccountParseService;

    /**
     * 子账户对公入驻
     */
    @Test
    public void customerRegister() {
        CustomerRegisterParam param = new CustomerRegisterParam();
        param.setAgentId(14);
        param.setMerchantOrderSn("**********");
        param.setAddress("胡同");
        param.setProvice("110000");
        param.setArea("110100");
        param.setCounty("110101");
        param.setUnionCode("************");
        param.setCustomerName("乐乐");
        param.setBankAcctType(AccountConstant.TO_PUBLIC);
        param.setCustomerType(HuifuCustomerTypeEnum.BUSINESS.getName());
        param.setCertType(AccountConstant.LICENSE);
        param.setLicenseNo("123");
        // 企业需要校验 营业执照图片
        param.setLicensePhoto("http://test-fshows-public.51youdian.com/data/ArticleImg/201909/*********/image/********/5d6c884490a9e.jpg");
        param.setLegalFrontPersonPic("http://test-fshows-public.51youdian.com/data/ArticleImg/201907/2508747/image/********/5d6e131cd80c8.jpg");
        param.setLegalBackPersonPic("http://test-fshows-public.51youdian.com/data/ArticleImg/201907/2508747/image/********/5d6e11c9322dc.jpg");
        // 店铺收银台
        param.setShopCheckstandPic("http://test-fshows-public.51youdian.com/temp/uploads/article/73922d9b8e486afe558be33da11f5fe2.png");
        param.setShopFrontPic("http://test-fshows-public.51youdian.com/temp/uploads/article/73922d9b8e486afe558be33da11f5fe2.png");
        param.setShopInsidePic("http://test-fshows-public.51youdian.com/temp/uploads/article/7c7c2c9f7e7d19a8f800e227f3d3735b.png");
        param.setCardFrontPic("http://test-fshows-public.51youdian.com/temp/uploads/article/7c7c2c9f7e7d19a8f800e227f3d3735b.png");
        param.setLegalPerson("李辉坚");
        param.setLegalCertId("350583199303134313");
        param.setLegalCertType("IDCARD");
        param.setCprRegNmCn("**********");
        param.setCardId("*****************");
        param.setCardName("开户人");
        param.setCertId("********");
        param.setPhone("***********");
        param.setBankName("开户行");
        param.setBranchName("开户支行");
        param.setLicenseTimeBegin("********");
        param.setLicenseTimeEnd("********");
        param.setUnityCategoryId(19);
        param.setUid(1112222);
        System.out.println(JSON.toJSONString(param));
        huifuAccountParseService.customerRegister(param);
    }


    @Test
    public void checkImportData() {
        CheckImportDataParam param = new CheckImportDataParam();
        param.setFileUrl("123");
        param.setBankType(3);
        param.setChannelType(1);
        List<ImportCustomerDataForm> list = new ArrayList<>();
        ImportCustomerDataForm form = new ImportCustomerDataForm();

        form.setStoreId("********");
        form.setAddress("胡同");
        form.setProvice("110000");
        form.setArea("110100");
        form.setCounty("110101");

        form.setUnionCode("************");
        form.setCustomerName("乐乐");
        form.setBankAcctType(AccountConstant.TO_PERSON);
        form.setCustomerType(HuifuCustomerTypeEnum.PERSON.getName());
        form.setCertType(AccountConstant.IDCARD);

        form.setLicenseNo("123");
        // 企业需要校验 营业执照图片
        form.setLicensePhoto("https://phpimg-test.51youdian.com/data/ArticleImg/201909/*********/image/********/5d6c884490a9e.jpg");

        form.setLegalFrontPersonPic("https://phpimg-test.51youdian.com/data/ArticleImg/201907/2508747/image/********/5d6e131cd80c8.jpg");
        form.setLegalBackPersonPic("https://phpimg-test.51youdian.com/data/ArticleImg/201907/2508747/image/********/5d6e11c9322dc.jpg");
        // 店铺收银台
        form.setShopCheckstandPic("https://phpimg-test.51youdian.com/temp/uploads/article/73922d9b8e486afe558be33da11f5fe2.png");
        form.setShopFrontPic("https://phpimg-test.51youdian.com/temp/uploads/article/73922d9b8e486afe558be33da11f5fe2.png");
        form.setShopInsidePic("https://phpimg-test.51youdian.com/temp/uploads/article/7c7c2c9f7e7d19a8f800e227f3d3735b.png");

        form.setLegalPerson("李辉坚");
        form.setLegalCertId("350583199303134313");
        form.setLegalCertType("IDCARD");
        form.setCprRegNmCn("**********");

        // 实名认证校验
        form.setCardId("6228481059674669572");
        form.setCardName("李秋红");
        form.setCertId("350583199512034343");
        form.setPhone("***********");
        form.setBankName("中国农业银行");

        form.setLicenseTimeBegin("********");
        form.setLicenseTimeEnd("********");

        form.setUnityCategoryId(19);
        form.setCardFrontPic("12312");

        list.add(form);
        param.setList(list);
        huifuAccountParseService.checkImportData(param);
    }
}