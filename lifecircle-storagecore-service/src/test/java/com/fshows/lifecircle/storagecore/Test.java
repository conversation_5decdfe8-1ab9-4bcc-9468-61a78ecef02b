package com.fshows.lifecircle.storagecore;/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */

import cn.hutool.core.io.FileUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fshows.fsframework.core.constants.StringPool;
import com.fshows.fsframework.core.utils.Md5Util;
import com.fshows.lifecircle.storagecore.service.enums.LeshuaTradeDirectionEnum;
import org.apache.commons.lang3.StringUtils;
import org.springframework.util.Base64Utils;

import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @version Test.java, v0.1 2021-10-29-2:15 下午 shijp
 */
public class Test {
    public static void main(String[] args) {
        String date="2021-10-29";
        String fileName = "1071736_shande_bank_2021-10-29.txt";
        String replace = LocalDate.parse(date).plusDays(-1).format(DateTimeFormatter.ISO_DATE);
        String newFileName = StringUtils.replace(fileName, date, replace);
        System.out.println(newFileName);
        String str = "***************,2021-12-20 21:15:53,****************,**********,55,5920,33,5887,进账,消费,刷卡支付,信用卡,标准类,************,************,016O2LLH,20211220211541758455,None,2021-12-20 21:15:53,,,,,UNKNOWN,,,5920,0,UNKNOWN,0,0,None,None,0,0,5500,,,*";
        String[] split = str.split(StringPool.COMMA);
        List<String[]> batch = new ArrayList<>();
        batch.add(split);
        List<String> collect = batch.stream().filter(i -> LeshuaTradeDirectionEnum.INCOME.getName().equals(i[8]))
                .map(i -> i[16]).filter(i -> StringUtils.isNotBlank(i)).collect(Collectors.toList());
        System.out.println(collect);

    }

    @org.junit.Test
    public void test() {
        String localPath = "C:\\Users\\<USER>\\Desktop\\testPic\\sfzf.jpg";
        byte[] bytes = FileUtil.readBytes(localPath);
        byte[] encode = Base64Utils.encode(bytes);
    }

    @org.junit.Test
    public void test1() {
        Map<String, String> map = new HashMap<>();
        String BANK_CARD = "LAKALA-964219-BANK_CARD-https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/app/photo/upload/*************.jpg";
        String FR_ID_CARD_BEHIND = "LAKALA-964219-FR_ID_CARD_BEHIND-https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-04-25/*************-SXBDGe6wMTbfRzxxCXTacnFMPhcndz6m.jpg";
        String OTHERS = "LAKALA-964219-OTHERS-https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/app/photo/upload/*************.jpg";
        String FR_ID_CARD_FRONT = "LAKALA-964219-FR_ID_CARD_FRONT-https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-04-25/*************-DHMytMTdZ88rPa2TGFb2RYNsiDb8ANSN.jpg";
        String ID_CARD_BEHIND = "LAKALA-964219-ID_CARD_BEHIND-https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-04-25/*************-ZWWTeJCGQKrJJrzrTstdxscEQJ72tNE3.jpg";
        String ID_CARD_FRONT = "LAKALA-964219-ID_CARD_FRONT-https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-04-25/*************-hRdFCdNQxQN3aN7i4pwAT3Z7tkQ7GACm.jpg";
        map.put("BANK_CARD", Md5Util.sign(BANK_CARD));
        map.put("FR_ID_CARD_BEHIND", Md5Util.sign(FR_ID_CARD_BEHIND));
        map.put("OTHERS", Md5Util.sign(OTHERS));
        map.put("FR_ID_CARD_FRONT", Md5Util.sign(FR_ID_CARD_FRONT));
        map.put("ID_CARD_BEHIND", Md5Util.sign(ID_CARD_BEHIND));
        map.put("ID_CARD_FRONT", Md5Util.sign(ID_CARD_FRONT));
        System.out.println(map);
    }

    @org.junit.Test

    public void testJson() {
        String str = "{\"failNum\":1,\"errorFileUrl\":\"https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/alipaytouch/workorder/LS202408211013341000%E6%89%A7%E8%A1%8C%E5%A4%B1%E8%B4%A5%E6%98%8E%E7%BB%86.xlsx?Expires=**********&OSSAccessKeyId=LTAI5tMacBHafqzmAfK4DdoK&Signature=zRTr9Car5arjzC5jWmBfywQPqfQ%3D\"}";
        JSONObject jsonObject = JSONUtil.parseObj(str);

    }
}
