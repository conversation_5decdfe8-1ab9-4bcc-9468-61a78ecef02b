package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.harvest.HarvestPlanEquipmentBatchImportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopBalanceListExportRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopBalanceExportFacadeTest.java, v 0.1 2021-10-12 16:59 lixingkai
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class EquipmentActivityFileFacadeTest {

    @Autowired
    private EquipmentActivityFileFacade equipmentActivityFileFacade;
    @Test
    public void test() {
        HarvestPlanEquipmentBatchImportRequest request = new HarvestPlanEquipmentBatchImportRequest();
        request.setUrl("crm/hardwareadmingw/import/scanface/harvest-test-20230817.xlsx");
        equipmentActivityFileFacade.batchImportJoinGoodsHarvestActivity(request);
    }
}