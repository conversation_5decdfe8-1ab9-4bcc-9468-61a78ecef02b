/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOcrIdcardModel;
import com.fshows.lifecircle.storagecore.service.domain.param.AliyunOcrIdcardParam;
import com.fshows.lifecircle.storagecore.service.service.AliyunOcrService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version AliyunOcrServiceImplTest.java, v 0.1 2020-03-02 16:32 lixiang
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class AliyunOcrServiceImplTest {

    @Autowired
    private AliyunOcrService aliyunOcrService;

    @Test
    public void ocrIdcard() {
        AliyunOcrIdcardParam param = new AliyunOcrIdcardParam();
        param.setIdcardImgUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/getimg.jpg");
        param.setSide("back");

        AliyunOcrIdcardModel model = aliyunOcrService.ocrIdcard(param);
        System.out.println(model);
    }


}