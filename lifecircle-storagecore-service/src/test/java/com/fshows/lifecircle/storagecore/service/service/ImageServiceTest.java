/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ImageServiceTest.java, v 0.1 2022-11-30 下午6:40 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class ImageServiceTest {

    @Autowired
    private ImageService imageService;


    @Test
    public void compressV2() {
        //String s = imageService.compressV2("https://img.51youdian.com/data/ArticleImg/202012/16922034/image/20201207/5fcd9fef6ab4e.jpg?v=20200611", 0.5f);
        //https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/20221130184318080.jpg
        //System.out.println(s);
        //String b = imageService.compressV2("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/member-import/merchant_img_1607313638463.jpeg", 0.5f);
        //https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/20221130184659765.jpg
        //System.out.println(b);
        //String c = imageService.compressV2("https://img.51youdian.com/data/ArticleImg/202012/16922034/image/20201207/5fcd9fda6f942.jpg?v=20200611", 0.5f);
        //https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/20221130185011742.jpg
        //System.out.println(c);
    }
}