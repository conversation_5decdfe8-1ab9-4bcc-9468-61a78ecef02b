/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.enums.acct.AcctBizParseFileTypeEnum;
import com.fshows.lifecircle.storagecore.facade.domain.request.acctbizparsefile.AcctBizExportFailFileRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.acctbizparsefile.AcctBizParseFileRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version AlipaySchoolCanteenActivityExportFacadeTest.java, v 0.1 2022-03-17 7:55 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class AcctbizParseFileFacadeTest {

    @Autowired
    private AcctBizParseFileFacade acctBizParseFileFacade;

    @Test
    public void commonParseFileTest() {
        AcctBizParseFileRequest request = new AcctBizParseFileRequest();
        request.setFileType(AcctBizParseFileTypeEnum.ACCOUNT_PRE_CREATE.getValue());
        request.setFileUrl("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/prepaycard/admin/upload/2024-11-02/ncAG4A2YENQN72d56kj1730511818055分账收款账户预创建模版.xlsx");
        System.out.println(acctBizParseFileFacade.commonParseFile(request));
    }

    @Test
    public void commonExportFailFileTest() {
        AcctBizExportFailFileRequest request = new AcctBizExportFailFileRequest();
        request.setFileType(AcctBizParseFileTypeEnum.ACCOUNT_PRE_CREATE.getValue());
        request.setJsonData("[{\"merchantName\":\"123\",\"phone\":\"321\",\"reason\":\"123\"}]");
        System.out.println(acctBizParseFileFacade.commonExportFailFile(request));
    }


    @Test
    public void commonParseFileAccountMerchantTest() {
        AcctBizParseFileRequest request = new AcctBizParseFileRequest();
        request.setFileType(AcctBizParseFileTypeEnum.MERCHANT_BATCH_CREATE.getValue());
        request.setFileUrl("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/prepaycard/admin/upload/2024-12-11/muban.xlsx");
        System.out.println(acctBizParseFileFacade.commonParseFile(request));
    }

    @Test
    public void commonExportFailFileAccountMerchantTest() {
        AcctBizExportFailFileRequest request = new AcctBizExportFailFileRequest();
        request.setFileType(AcctBizParseFileTypeEnum.MERCHANT_BATCH_CREATE.getValue());
        request.setJsonData("[{\"orgName\":\"***********\",\"contactName\":\"林振宇 test\",\"contactPhone\":\"***********\",\"merchantName\":\"杭州xxxx有限公司\",\"reason\":\"123\"}]");
        System.out.println(acctBizParseFileFacade.commonExportFailFile(request));
    }
}