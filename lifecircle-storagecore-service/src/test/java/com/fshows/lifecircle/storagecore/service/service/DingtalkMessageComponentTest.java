package com.fshows.lifecircle.storagecore.service.service;/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */

import com.fshows.lifecircle.storagecore.service.component.DingTalkMessageComponent;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version DingtalkMessageComponentTest.java, v0.1 2022-03-08-4:17 下午 shijp
 */

@SpringBootTest
@RunWith(SpringRunner.class)
public class DingtalkMessageComponentTest {
    @Autowired
    private SysConfig sysConfig;
    @Autowired
    private DingTalkMessageComponent dingTalkMessageComponent;

    @Test
    public void dingMessageTest(){
        String msg = "测试消息";
        dingTalkMessageComponent.dingMessage(sysConfig.getShandeBillDingtalkUrl(), sysConfig.getShandeBillDingtalkMobile(), msg);
    }
}
