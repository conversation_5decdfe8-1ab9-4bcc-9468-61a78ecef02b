package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.YoudianMerchantGrantLogExportFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.YoudianMerchantGrantLogExportRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version 1.0
 * @date 2020/5/25 16:01
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class YoudianMerchantGrantLogExportFacadeTest {

    @Autowired
    YoudianMerchantGrantLogExportFacade youdianMerchantGrantLogExportFacade;

    @Test
    public void getMerchantGrantLogExportTest() {
        YoudianMerchantGrantLogExportRequest request = new YoudianMerchantGrantLogExportRequest();
        request.setStartTime("2020-01-01");
        request.setEndTime("2020-10-10");
        String fileUrl = youdianMerchantGrantLogExportFacade.getMerchantGrantLogExport(request);

        System.out.println(fileUrl);
    }
}
