/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dao;

import static org.junit.Assert.*;

import com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentSnDO;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version HwEquipmentSnDAOTest.java, v 0.1 2019-09-22 20:56 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class HwEquipmentSnDAOTest {

    @Autowired
    private ExtraHwEquipmentSnDAO hwEquipmentSnDAO;


    @Test
    public void getByInitSnList() {

        List<String> snList = Lists.newArrayList();
        snList.add("QCF420190611019443");
        snList.add("QCF420190611019443");
        List<HwEquipmentSnDO> list = hwEquipmentSnDAO.getByInitSnList(snList);
        System.out.println(list);
    }
}