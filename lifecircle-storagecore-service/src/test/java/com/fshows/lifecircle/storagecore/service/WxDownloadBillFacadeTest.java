/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.WxDownloadBillFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.WxDownloadBillRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 *
 * <AUTHOR>
 * @version WxDownloadBillFacadeTest.java, v 0.1 2019-03-11 18:34
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class WxDownloadBillFacadeTest {
    @Autowired
    @Qualifier("wxDownloadBillFacadeImpl")
    private WxDownloadBillFacade wxDownloadBillFacade;

    @Test
    public void wxBillDownload() {
        WxDownloadBillRequest param = new WxDownloadBillRequest();
        param.setBillDate("20190318");
        //param.setBillType("ALL");
        param.setSubMchId("1525082561");
        long time = System.currentTimeMillis();
        System.out.println(wxDownloadBillFacade.downloadBillForWx(param));
        System.out.println("耗时："+(System.currentTimeMillis() - time));
    }

    @Test
    public void getwxBillDownloadUrlTest() {
        WxDownloadBillRequest param = new WxDownloadBillRequest();
        param.setBillDate("20190318");
        param.setSubMchId("1525082561");
        //param.setBillType("ALL");
        long time = System.currentTimeMillis();
        System.out.println(wxDownloadBillFacade.getwxBillDownloadUrl(param));
        System.out.println("耗时："+(System.currentTimeMillis() - time));
    }
}