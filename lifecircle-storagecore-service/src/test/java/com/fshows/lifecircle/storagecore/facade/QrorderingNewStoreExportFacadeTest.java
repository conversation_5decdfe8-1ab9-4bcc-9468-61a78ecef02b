package com.fshows.lifecircle.storagecore.facade;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.facade.domain.request.NewStoreRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.QrorderingNewStoreListRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.QrorderingNewStoreResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version QrorderingNewStoreExportFacadeTest.java, v 0.1 2021-11-10 10:54 lixingkai
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class QrorderingNewStoreExportFacadeTest {
    @Autowired
    private QrorderingNewStoreExportFacade qrorderingNewStoreExportFacade;

    @Test
    public void test(){
        QrorderingNewStoreListRequest request = new QrorderingNewStoreListRequest();
        List<NewStoreRequest> newStoreList = Lists.newArrayList();
        NewStoreRequest  newStoreRequest = new NewStoreRequest();
        newStoreRequest.setUid(3214670);
        newStoreRequest.setSmid("2088300361081149");
        NewStoreRequest  newStoreRequest2 = new NewStoreRequest();
        newStoreRequest2.setUid(32166496);
        newStoreRequest2.setSmid("2088210702914558");
        newStoreList.add(newStoreRequest);
        newStoreList.add(newStoreRequest2);
        request.setNewStoreList(newStoreList);
        QrorderingNewStoreResponse qrorderingNewStoreResponse = qrorderingNewStoreExportFacade.exportNewStoreList(request);
        System.out.println(JSON.toJSONString(qrorderingNewStoreResponse));
    }
}
