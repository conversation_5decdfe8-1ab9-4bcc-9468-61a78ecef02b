/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.param.trafficcard.TrafficSimCardListExportParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR> * @version IotTrafficSimCardExportTest.java, v 0.1 2024-12-20 10:42 吴安
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class IotTrafficSimCardServiceTest {
    @Autowired
    private IotTrafficSimCardService iotTrafficSimCardService;

    @Test
    public void testExport() {
        TrafficSimCardListExportParam param = new TrafficSimCardListExportParam();
        param.setAgentId(1563036);
        String s = iotTrafficSimCardService.listSimCardExport(param);
        System.out.println("s = " + s);
    }
}