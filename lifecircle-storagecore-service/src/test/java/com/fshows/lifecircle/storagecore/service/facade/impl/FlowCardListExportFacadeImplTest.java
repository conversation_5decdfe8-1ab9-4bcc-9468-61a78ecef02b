package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.domain.request.hardwarecenter.FlowCardListExportRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class FlowCardListExportFacadeImplTest {

    @Autowired
    private FlowCardListExportFacadeImpl flowCardListExportFacadeImpl;

    @Test
    public void export() {
        FlowCardListExportRequest request = new FlowCardListExportRequest();
        request.setJobNumber("zhangsan");
        //        request.setInitSn("F521041500571PMP");
//        request.setIccid("89860477012070260072");
//        request.setStartTime(FsDateUtil.formatDate("2022-11-01", "yyyy-MM-dd"));
//        request.setEndTime(FsDateUtil.formatDate("2022-11-30", "yyyy-MM-dd"));
//        request.setAgentId(1563166);
        request.setMerchantId(32165494);
        flowCardListExportFacadeImpl.export(request);
    }
}