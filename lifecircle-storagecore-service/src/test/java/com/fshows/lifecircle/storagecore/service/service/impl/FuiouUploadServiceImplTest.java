/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.model.fuiou.FuiouChangePicModel;
import com.fshows.lifecircle.storagecore.service.domain.model.fuiou.FuiouUploadPicModel;
import com.fshows.lifecircle.storagecore.service.domain.param.fuiou.FuiouChangePicParam;
import com.fshows.lifecircle.storagecore.service.domain.param.fuiou.FuiouUploadPicParam;
import com.fshows.lifecircle.storagecore.service.domain.param.fuiou.item.PictureInfoParam;
import com.fshows.lifecircle.storagecore.service.service.FuiouUploadService;
import org.apache.commons.lang3.StringUtils;
import org.apache.curator.shaded.com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * <AUTHOR>
 * @version FuiouUploadServiceImplTest.java, v 0.1 2023-09-25 19:25 zhubo
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class FuiouUploadServiceImplTest {
    @Autowired
    private FuiouUploadService fuiouUploadService;

    @Test
    public void test() {
        FuiouUploadPicParam param = new FuiouUploadPicParam();
        param.setMerchantNo("0007910F6463434");

        List<PictureInfoParam> paramList = buildImageMap();
        param.setList(paramList);

        FuiouUploadPicModel model = fuiouUploadService.uploadIncomePic(param);
        System.out.println("==============================================================");
        System.out.println(model);
    }

    @Test
    public void testChangePic() {
        FuiouChangePicParam param = new FuiouChangePicParam();
        param.setMchntCd("0002900F6482455");
        param.setFileName("法人身份证明正面");
        param.setUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-10-08/1696732157197-m58K6hFYzx4BADwDtkRRiS256mKS5rDd.jpeg");
        FuiouChangePicModel model = fuiouUploadService.changePic(param);
        System.out.println("==============================================================");
        System.out.println(model);
    }

    private List<PictureInfoParam> buildImageMap() {
        List<PictureInfoParam> infoFormList = Lists.newArrayList();
        //法人身份证正面
        String u1 = "https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-10-11/1697012545035-Ww3AywwXCmarffGDB8k85aiBG68GDjQ7.png";
        if (validateImageUrl(u1)) {
            infoFormList.add(createInfoForm(u1, "法人身份证明正面"));
        }
//        //法人身份证反面
//        String u2 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797579930-JJSNY7s3BXa3WfZ5BByXeW4eiyzCTemi.jpg";
//        if (validateImageUrl(u2)) {
//            infoFormList.add(createInfoForm(u2, "法人身份证明反面"));
//        }
//        //银行卡正面
//        String u3 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797658808-YR6PiYyhc2HdYEPdmCh27EhxyJ67sEy5.jpg";
//        if (validateImageUrl(u3)) {
//            infoFormList.add(createInfoForm(u3, "银行卡正面"));
//        }
//        //对私
//        //结算人身份证反面
//        if (validateImageUrl(u2)) {
//            infoFormList.add(createInfoForm(u2, "对私账户身份证明背面"));
//        }
//        //结算人身份证正面
//        if (validateImageUrl(u1)) {
//            infoFormList.add(createInfoForm(u1, "对私账户身份证明正面"));
//        }
//
//        //门头照
//        String u4 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797676510-WZ5xHib37QPQnEMzmYJt8NYcTRZYw8NX.jpg";
//        if (validateImageUrl(u4)) {
//            infoFormList.add(createInfoForm(u4, "门头照片"));
//        }
//
//        //店内照
//        String u5 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797682636-PNBk5ntKrZRH4R7SCR3hwe8CrkCHtrYC.jpg";
//        if (validateImageUrl(u5)) {
//            infoFormList.add(createInfoForm(u5, "门脸照片"));
//        }
//        //经营场所-含收银台
//        String u6 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797679680-sa8CXBzXH47SeRysTsprsQjBAGB33XP7.jpg";
//        if (validateImageUrl(u6)) {
//            infoFormList.add(createInfoForm(u6, "收银台图片"));
//        }
//        //个人和企业都需要传营业执照号
//        String u7 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797566538-KfkhCAiCDGrxpt5hQS8tybFHCDB2eztC.jpg";
//        if (validateImageUrl(u7)) {
//            infoFormList.add(createInfoForm(u7, "证件照片"));
//        }
//        //开户许可证
//        String u8 = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2023-09-27/1695797575047-EXf2EBBKp7hzTHjJ5Ym5JA3MBRJXYWpK.jpg";
//        if (validateImageUrl(u8)) {
//            infoFormList.add(createInfoForm(u8, "开户许可证"));
//        }
        return infoFormList;
    }

    private PictureInfoParam createInfoForm(String u1, String name) {
        PictureInfoParam infoParam = new PictureInfoParam();
        infoParam.setName(name);
        infoParam.setUrl(u1);
        return infoParam;
    }

    /**
     * 验证图片链接格式是否正确
     *
     * @param url 图片URL
     * @return 是否验证通过
     */
    private boolean validateImageUrl(String url) {
        return !StringUtils.isBlank(url) && url.startsWith("http");
    }
}
