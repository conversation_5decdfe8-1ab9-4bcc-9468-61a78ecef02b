/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.HxbUploadFileRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HxbUploadPicFacade.java, v 0.1 2024-10-26 15:22 buhao
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class HxbUploadPicFacadeTest {

    @Autowired
    private HxbUploadPicFacade hxbUploadPicFacade;


    @Test
    public void testUploadPic() {
        HxbUploadFileRequest request = new HxbUploadFileRequest();
        request.setId("f273eea9402a594daf2f39f405151123");
        request.setFileType("11");
        request.setImgUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2024-10-26/1729913715650-jXtsMGKriGdz2XQXsknm2Hmfi3Ddp7ii.jpg");
        hxbUploadPicFacade.uploadPic(request);
    }

}