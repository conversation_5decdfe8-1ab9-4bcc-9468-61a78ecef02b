/**
 * fshows.com
 * Copyright (C) 2013-2018 All Rights Reserved.
 */
package
        com.fshows.lifecircle.storagecore.service.test;

import com.fshows.fsframework.core.enums.IdcardSideEnum;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOcrBankCardModel;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOcrIdcardModel;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOcrLicenseModel;
import com.fshows.lifecircle.storagecore.service.domain.param.AliyunOcrBankCardParam;
import com.fshows.lifecircle.storagecore.service.domain.param.AliyunOcrIdcardParam;
import com.fshows.lifecircle.storagecore.service.domain.param.AliyunOcrLicenseParam;
import com.fshows.lifecircle.storagecore.service.service.impl.AliyunOcrServiceImpl;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.powermock.api.mockito.PowerMockito;
import org.powermock.core.classloader.annotations.PowerMockIgnore;
import org.powermock.modules.junit4.PowerMockRunner;

/**
 * <AUTHOR>
 * @version AliyunOcrServiceTest.java, v 0.1 2018-12-21 15:24
 */
@RunWith(PowerMockRunner.class)
@PowerMockIgnore({"javax.management.*", "javax.crypto.*", "javax.net.ssl.*"})
@Slf4j
public class AliyunOcrServiceTest {

    @InjectMocks
    private AliyunOcrServiceImpl aliyunOcrServiceImpl;

    @Mock
    private SysConfig sysConfig;

    @Test
    public void idcardOcrTest() {
        PowerMockito.when(sysConfig.getAliyunOcrAppcode()).thenReturn("758ee88f670b418e843cd905da7d7ef0");
        PowerMockito.when(sysConfig.getAliyunOcrIdCardHost()).thenReturn("https://dm-51.data.aliyun.com");
        PowerMockito.when(sysConfig.getAliyunOcrIdCardPath()).thenReturn("/rest/160601/ocr/ocr_idcard.json");

        AliyunOcrIdcardParam idcardParam = new AliyunOcrIdcardParam();
        idcardParam.setSide(IdcardSideEnum.FACE.getValue());
        idcardParam.setIdcardImgUrl("/201812/********/image/********/5c18b4f44390b.jpg?v=********");
        AliyunOcrIdcardModel faceModel = aliyunOcrServiceImpl.ocrIdcard(idcardParam);
        System.out.println(faceModel);

        idcardParam = new AliyunOcrIdcardParam();
        idcardParam.setSide(IdcardSideEnum.BACK.getValue());
        idcardParam.setIdcardImgUrl("/201812/********/image/********/5c18b4fe0bdfa.jpg?v=********");
        AliyunOcrIdcardModel backModel = aliyunOcrServiceImpl.ocrIdcard(idcardParam);
        System.out.println(backModel);
    }


    @Test
    public void ocrBankCardTest() {
        PowerMockito.when(sysConfig.getAliyunOcrAppcode()).thenReturn("2e5491ed1f68488981adaf4d2495652f");
        PowerMockito.when(sysConfig.getAliyunOcrBankCardHost()).thenReturn("https://api06.aliyun.venuscn.com");
        PowerMockito.when(sysConfig.getAliyunOcrBankCardPath()).thenReturn("/ocr/bank-card");
        AliyunOcrBankCardParam param = new AliyunOcrBankCardParam();
        param.setBankCardImgUrl("http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/8be1b9eb8d9a1a98de5ac43f2f0547bf.jpg");
        AliyunOcrBankCardModel model = aliyunOcrServiceImpl.ocrBankCard(param);
        System.out.println(model);
    }

    @Test
    public void ocrLicenseTest() {
        PowerMockito.when(sysConfig.getAliyunOcrAppcode()).thenReturn("2e5491ed1f68488981adaf4d2495652f");
        PowerMockito.when(sysConfig.getAliyunOcrLicenseHost()).thenReturn("http://dm-58.data.aliyun.com");
        PowerMockito.when(sysConfig.getAliyunOcrLicensePath()).thenReturn("/rest/160601/ocr/ocr_business_license.json");
        AliyunOcrLicenseParam param = new AliyunOcrLicenseParam();
        param.setLicenseImgUrl("https://cn.bing.com/th?id=OIP.1LFVKkZrQdQu8Y7govhNMQHaFP&pid=Api&rs=1");
        AliyunOcrLicenseModel model = aliyunOcrServiceImpl.ocrLicense(param);
        System.out.println(model);
    }



}