package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.facade.WechatComponentFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.QrcodeJumpDownloadRequest;
import com.fshows.lifecircle.storagecore.service.domain.param.CreateTestQrCodeParam;
import com.fshows.lifecircle.storagecore.service.domain.param.UploadMediaParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class WechatComponentServiceTest {

    @Autowired
    private WechatComponentFacade wechatComponentFacade;

    @Autowired
    private WechatComponentService wechatComponentService;

    @Test
    public void test_wechatComponentService() {
        QrcodeJumpDownloadRequest request = new QrcodeJumpDownloadRequest();
        request.setAccessToken("32_WG5haWPBukADBWZltjSUweS0jw96mdf8sTli43-7Q5Ft6E703HsHQwVfYusQe3IVbyVfSFkZsgOn3-1-EtIWD1KKvfQ_hzDrvEakkU6rsBgTLMHiA0335dM1spJxvbCrEEhtbGhb485-hqVRCGLeAHDPTY");
        wechatComponentFacade.qrcodeJumpDownload(request);
    }

    @Test
    public void test_createQrCode() {
        CreateTestQrCodeParam param = new CreateTestQrCodeParam();
        param.setAccessToken("32_BHqLa1HhB0wv8tTJ5H5oYV30eemLuF9n8aoITHA1KJIoWkJ_OhaGIrtrJ9bJYahg-RUjY8bOMqa8gu8BQEeO_NYt3ek1lbthV07CDOb7BaOP3kCzuo0Us3xyPzZ1JmbldGvbt72qh8UTlTxYPXXjAEDCKK");
        System.out.println(wechatComponentService.createTestQrCode(param));
    }

    @Test
    public void test_uploadMedia() {
        UploadMediaParam param = new UploadMediaParam();
        param.setMediaUrl("https://cdn.yuque.com/yuque/445/2018/png/118607/1525314951920-avatar/810d89a5-5ed9-4d3c-8fdd-c767e42c6e8c.png");
        param.setType("image");
        param.setAccessToken("33_YLMlJrtD93d3-2l-WltLDGnfvSNWj3Brvw8HKUVjQOXrKusl1i_SMGjYfFgRaP7GnlcNGjS5IgIKyiYeKKljiOiH2T5bX0pECd1lT3AmEmdkmD8Ue6EkoT3YeBcv5DmsVuP3IHqdyGmTzFapYNMhAJDJYX");
        System.out.println(wechatComponentService.uploadMedia(param));
    }
}