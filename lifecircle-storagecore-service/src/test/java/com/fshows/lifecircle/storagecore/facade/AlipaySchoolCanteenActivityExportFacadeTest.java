/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.domain.request.alipayschoolcanteen.AlipaySchoolCanteenActivityExportPollingRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.alipayschoolcanteen.AlipaySchoolCanteenActivityExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.ExportResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.alipayschoolcanteen.AlipaySchoolCanteenActivityExportPollingResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version AlipaySchoolCanteenActivityExportFacadeTest.java, v 0.1 2022-03-17 7:55 下午
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class AlipaySchoolCanteenActivityExportFacadeTest {
    @Autowired
    private AlipaySchoolCanteenActivityExportFacade alipaySchoolCanteenActivityExportFacade;

    @Test
    public void alipaySchoolCanteenActivityExportTest() {
        AlipaySchoolCanteenActivityExportRequest request = new AlipaySchoolCanteenActivityExportRequest();
        request.setCrmUserId("111111");
        request.setSearchType(1);
        request.setSearchContent("俊超商户");
        request.setActivityStatus(-1);
        request.setHasShopId(-1);
        ExportResponse response = alipaySchoolCanteenActivityExportFacade.alipaySchoolCanteenActivityExport(request);
        LogUtil.info(log, "response = {}", response);
    }

    /**
     * ExportResponse(lockKey=polling.type.lock.key.crm_admin_alipay_school_canteen_web.alipay.school.canteen.activity.web.export.111111.042a09a81f47540e644fef52d5a4a831, exportKey=polling.type.value.key.crm_admin_alipay_school_canteen_web.alipay.school.canteen.activity.web.export.111111.ab7e8722691c40efb11e422d61bf30b4.1)
     */
    @Test
    public void exportPollingTest() {
        AlipaySchoolCanteenActivityExportPollingRequest request = new AlipaySchoolCanteenActivityExportPollingRequest();
        request.setLockKey("polling.type.lock.key.crm_admin_alipay_school_canteen_web.alipay.school.canteen.activity.web.export.111111.042a09a81f47540e644fef52d5a4a831");
        request.setExportKey("polling.type.value.key.crm_admin_alipay_school_canteen_web.alipay.school.canteen.activity.web.export.111111.ab7e8722691c40efb11e422d61bf30b4.1");
        AlipaySchoolCanteenActivityExportPollingResponse response = alipaySchoolCanteenActivityExportFacade.exportPolling(request);

        LogUtil.info(log, "response = {}", response);
    }
}