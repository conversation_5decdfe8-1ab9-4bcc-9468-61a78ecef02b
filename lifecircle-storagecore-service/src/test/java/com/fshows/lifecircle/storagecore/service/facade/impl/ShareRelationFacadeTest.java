/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.ShareRelationFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.ImportShareRelationDataFormRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.ImportShareRelationRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.ShareRelationInitRequest;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version ShareRelationFacadeTest.java, v 0.1 2019-08-26 20:14
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class ShareRelationFacadeTest {

    @Autowired
    private ShareRelationFacade shareRelationFacade;

    @Test
    public void importShareRelationTest(){
        ImportShareRelationRequest request = new ImportShareRelationRequest();
        request.setBankType(3);
        request.setChannelType(1);
        request.setShareType(1);
        request.setFileUrl("huifu-account/2019-10-18/*************工作簿1.xlsx");
        shareRelationFacade.importShareRelation(request);
    }

    @Test
    public void importShareTest() {
        ShareRelationInitRequest request = new ShareRelationInitRequest();
        request.setBankType(3);
        request.setChannelType(1);
        request.setShareType(1);
        request.setMerchantOrderSn(System.currentTimeMillis() + "");
        List<ImportShareRelationDataFormRequest> lists = Lists.newArrayList();
        ImportShareRelationDataFormRequest request1 = new ImportShareRelationDataFormRequest();
        request1.setUid("3204297");
        request1.setProtocolPic("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/account/files/upload/temp/lifecircle-storagecore-service/img/KbZjndwjVf1573700136.jpgKbZjndwjVf1573700136");
        request1.setCustomerId("20191122140133860684");
        request1.setCustomerName("");
        request1.setStoreIdAndPortionStr("********,0.9999;********,1");
        lists.add(request1);
        request.setDataList(lists);

        shareRelationFacade.singleShareAccountRelation(request);
    }
}