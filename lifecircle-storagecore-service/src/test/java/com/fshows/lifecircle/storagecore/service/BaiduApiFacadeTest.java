/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.BaiduApiFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.BaiduImageRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.BaiduImageResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version BaiduApiFacadeTest.java, v 0.1 2019-08-08 21:49 CoderMa
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class BaiduApiFacadeTest {

    @Autowired
    private BaiduApiFacade baiduApiFacade;

    @Test
    public void searchBaiduImage() {
        BaiduImageRequest request = new BaiduImageRequest();
        request.setPage(1);
        request.setPageSize(10);
        request.setQueryWord("农家小炒肉");
        BaiduImageResponse response = baiduApiFacade.searchBaiduImage(request);
        System.out.println(response);
    }

}