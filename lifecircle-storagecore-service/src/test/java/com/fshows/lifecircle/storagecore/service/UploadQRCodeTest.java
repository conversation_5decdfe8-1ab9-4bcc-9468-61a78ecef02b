package com.fshows.lifecircle.storagecore.service;

import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import com.alibaba.fastjson.JSONObject;
import com.fshows.lifecircle.storagecore.service.domain.model.MinaCutEecelModel;
import com.fshows.lifecircle.storagecore.service.domain.model.receipt.ReceiptMerchantUploadModel;
import com.fshows.lifecircle.storagecore.service.domain.param.MinaCutExcelParam;
import com.fshows.lifecircle.storagecore.service.domain.param.MinaCutUploadExcelParam;
import com.fshows.lifecircle.storagecore.service.domain.param.receipt.ReceiptMerchantUploadParam;
import com.fshows.lifecircle.storagecore.service.service.DownloadSnExcelService;
import com.fshows.lifecircle.storagecore.service.service.MarketMemberService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.io.IOException;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version UploadQRCodeTest.java, v 0.1 2019-04-23 17:13
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class UploadQRCodeTest {

    @Autowired
    private MarketMemberService marketMemberService;

    @Autowired
    private DownloadSnExcelService downloadSnExcelService;


    @Test
    public void testGetStoreQRCode() {
        BigDecimal divide = new BigDecimal(1002).divide(new BigDecimal(100));
        System.out.println(divide);

//        GetStoreQRCodeParam param = new GetStoreQRCodeParam();
//        param.setUid(100100);
//        param.setStoreId(100101);
//        param.setToken("token100100token");
//
//        String response = marketMemberService.getStoreQRCode(param);
//        log.info("response={}",response);

    }

    @Test
    public void testQrCodeUtil(){
        QrConfig config = new QrConfig(732, 732);
        // 设置边距，既二维码和背景之间的边距
        config.setMargin(0);
        String content = "https://youdian1.51youdian.com/Wap/Index/Member/activateCard/handleType/1/store_id/100101";
        //生成二维码
        QrCodeUtil.generate(content, config, new File("/Users/<USER>/coders/fshows/1.jpg"));
    }

    @Test
    public void clientsMessageUploadTest() {
        ReceiptMerchantUploadParam param = new ReceiptMerchantUploadParam();
        param.setFileUrl("https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/college/admin/receipt/client/templates/%E7%A4%BA%E4%BE%8B%E8%A1%A8%E6%A0%BC%E6%A8%A1%E6%9D%BF%E6%A0%B7%E5%BC%8F2-0928.xlsx");
        downloadSnExcelService.clientsMessageUpload(param);
    }

    @Test
    public void clientsMessageUpload() {
        ReceiptMerchantUploadParam param = new ReceiptMerchantUploadParam();
        param.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/college/admin/upload/2022-03-21/1647846614021-jp88Ywx6GtTJDrNnNM2bPHnPjHc8DYPm.xlsx?Expires=1633027673&OSSAccessKeyId=LTAI5tMacBHafqzmAfK4DdoK&Signature=8310L+c95BZBCprkSDE2pCXpcS8=");
        param.setReceiptId("");
        param.setReceiptType(1);
        param.setUid(32144319);
        ReceiptMerchantUploadModel model = downloadSnExcelService.clientsMessageUpload(param);
        System.out.println(JSONObject.toJSONString(model));
    }

    @Test
    public void parseMinaCutExcel() throws IOException {
        MinaCutExcelParam param = new MinaCutExcelParam();
        param.setUrl("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/TEMP/cutMina/7-8 月份未实名商户名单.xlsx");
        MinaCutEecelModel model = downloadSnExcelService.parseMinaCutExcel(param);
        System.out.println(JSONObject.toJSONString(model));
    }

    @Test
    public void uploadMinaCutExcel() {
        MinaCutUploadExcelParam param = new MinaCutUploadExcelParam();
        List<Integer> storeIds = new ArrayList<>();
        storeIds.add(123);
        storeIds.add(125);
        storeIds.add(126);
        param.setStoreIds(storeIds);
        downloadSnExcelService.uploadMinaCutExcel(param);
    }
}
