/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import cn.hutool.core.util.StrUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.fsframework.extend.redis.RedisCache;
import com.fshows.lifecircle.storagecore.facade.EquipMaintManageFacade;
import com.fshows.lifecircle.storagecore.facade.constants.StorageManageConstant;
import com.fshows.lifecircle.storagecore.facade.domain.request.alipaytouch.UploadWorkOrderBatchRequest;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.redisson.api.RLock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version EquipMaintManageFacadeTest.java, v 0.1 2024-08-19 17:01 buhao
 */
@Slf4j
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class EquipMaintManageFacadeTest {
    @Autowired
    private EquipMaintManageFacade equipMaintManageFacade;

    @Autowired
    private RedisCache redisCache;

    @Test
    public void testUpload() throws InterruptedException {
        final String userId = "D18346J954254444438245G7";
        final String ossUrl = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/admin/import/merchanttransfer/20240820/1724136105804支付宝碰一下维护sn导入模板.xlsx";
        UploadWorkOrderBatchRequest request = new UploadWorkOrderBatchRequest();
        request.setOssUrl(ossUrl);
        request.setOperatorId(userId);
        equipMaintManageFacade.uploadWorkOrderBatch(request);


        String lockKey = StrUtil.format(StorageManageConstant.ALIPAY_WORK_ORDER_BATCH_UPLOAD_LOCK_KEY, userId);
        RLock redisLock = redisCache.getRedisLock(lockKey);
        while (redisLock.isLocked()) {
            TimeUnit.MILLISECONDS.sleep(500L);
        }

        String resKey = StrUtil.format(StorageManageConstant.ALIPAY_WORK_ORDER_BATCH_UPLOAD_RES_KEY, userId);
        String fileUrl = redisCache.getString(resKey);
        if (StrUtil.isNotBlank(fileUrl)) {
            LogUtil.info(log, "失败文件路径：" + fileUrl);
            redisCache.remove(fileUrl);
        }
    }

    @Test
    public void testRedis() {
        JSONObject jsonObject = new JSONObject();
        jsonObject.set("failNum", 1);
        jsonObject.set("errorFileUrl","https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/alipaytouch/workorder/LS202408211013341000%E6%89%A7%E8%A1%8C%E5%A4%B1%E8%B4%A5%E6%98%8E%E7%BB%86.xlsx?Expires=1724210018&OSSAccessKeyId=LTAI5tMacBHafqzmAfK4DdoK&Signature=zRTr9Car5arjzC5jWmBfywQPqfQ%3D");
        LogUtil.info(log, "sjzd:{}", jsonObject);
        redisCache.put("sjzd", jsonObject);
        String json = redisCache.getString("sjzd");
        redisCache.remove("sjzd");
        LogUtil.info(log, "json:{}", json);
        jsonObject = JSONUtil.parseObj(json);
        LogUtil.info(log, "jsonObject:{},{}", jsonObject.getInt("failNum"), jsonObject.getStr("errorFileUrl"));
    }



}