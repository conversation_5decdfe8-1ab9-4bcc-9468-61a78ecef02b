package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.AlipayActivityGoodsImportFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.activitygoods.ActivityGoodsImportCheckRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.activitygoods.ActivityGoodsImportCheckResultRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.activitygoods.FindActivityGoodsImportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.activitygoods.ActivityGoodsImportCheckResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.activitygoods.ActivityGoodsImportResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.activitygoods.QueryActivityGoodsImportCheckResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

@SpringBootTest
@RunWith(SpringRunner.class)
public class AlipayActivityGoodsImportFacadeImplTest {

    @Autowired
    private AlipayActivityGoodsImportFacade alipayActivityGoodsImportFacade;

    @Test
    public void activityGoodsImportCheck() throws InterruptedException {
        ActivityGoodsImportCheckRequest request = new ActivityGoodsImportCheckRequest();
        request.setUid(1);
        request.setImportFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/import001.xlsx");
        ActivityGoodsImportCheckResponse response = alipayActivityGoodsImportFacade.activityGoodsImportCheck(request);
        System.out.println(response);
        Thread.sleep(600000L);

    }

    @Test
    public void activityGoodsImportCheckResult() {
        ActivityGoodsImportCheckResultRequest request = new ActivityGoodsImportCheckResultRequest();
        request.setUid(1);
        request.setImportTaskNo("5f85a98bca2847e7a50873ef54122897");
        QueryActivityGoodsImportCheckResponse response = alipayActivityGoodsImportFacade.activityGoodsImportCheckResult(request);
        System.out.println(response);
    }

    @Test
    public void findActivityGoodsImport() {
        FindActivityGoodsImportRequest request = new FindActivityGoodsImportRequest();
        request.setUid(1);
        request.setImportTaskNo("ffad84bf2c5b457ca68670a234da457b");
        List<ActivityGoodsImportResponse> response = alipayActivityGoodsImportFacade.findActivityGoodsImport(request);
        System.out.println(response);
    }
}