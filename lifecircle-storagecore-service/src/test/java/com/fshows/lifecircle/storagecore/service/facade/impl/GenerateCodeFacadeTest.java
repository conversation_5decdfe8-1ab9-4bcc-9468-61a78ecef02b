/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.GenerateImagesUploadFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.GenerateImageUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.GenerateStoreImageRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.GenerateCodeResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version LoanFacadeTest.java, v 0.1 2019-07-26 10:56 CoderMa
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class GenerateCodeFacadeTest {

    @Autowired
    private GenerateImagesUploadFacade generateImagesUploadFacade;

    /**
     * 查询进件数据统计
     */
    @Test
    public void exportApplyList() {

        GenerateImageUploadRequest request = new GenerateImageUploadRequest();
        request.setExcelUrl("https://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/agentadmin/commission/2024-09-02/1725257861798-a34BKT7eXynFbEBiX5jcTaxh8GdzMMx8.xlsx");
        generateImagesUploadFacade.imageUpload(request);
    }


    @Test
    public void GenerateStoreImageRequest() {
        GenerateStoreImageRequest request = new GenerateStoreImageRequest();
        request.setCreateNum(100);
        request.setStoreId("202407021625340006705070326");
        request.setStoreName("冰镇大西瓜椰子章鱼丸子");
        GenerateCodeResponse generateCodeResponse = generateImagesUploadFacade.generateCode(request);
        System.err.println(generateCodeResponse.getCodeUrl());
    }


}
