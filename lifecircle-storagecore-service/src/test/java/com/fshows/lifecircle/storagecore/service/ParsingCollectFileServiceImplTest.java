/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.text.csv.CsvData;
import cn.hutool.core.text.csv.CsvReader;
import cn.hutool.core.text.csv.CsvRow;
import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.http.HttpUtil;
import cn.hutool.json.JSONObject;
import cn.hutool.json.JSONUtil;
import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import cn.hutool.system.SystemUtil;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.exception.CollectDataException;
import com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.RiskRecordDO;
import com.fshows.lifecircle.storagecore.service.domain.model.CollectFileDataModel;
import com.fshows.lifecircle.storagecore.service.domain.param.CollectFileDetailParam;
import com.fshows.lifecircle.storagecore.service.service.ParsingCollectFileService;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * <AUTHOR> Jian
 * @version ParsingCollectFileServiceImplTest.java, v0.1 2019-03-13 20:04 xujian
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ParsingCollectFileServiceImplTest {

    @Autowired
    private ParsingCollectFileService parsingCollectFileService;

    @Test
    public void parsingCollectFileTest() {

        CollectFileDetailParam collectFileDetailParam = new CollectFileDetailParam();
        collectFileDetailParam.setCollectDataId("ABC");

        CollectFileDataModel collectFileDataModel1 = parsingCollectFileService.getCollectFileDataById(collectFileDetailParam);
        Assert.assertNotNull(collectFileDataModel1);
    }

    @Test
    public void parsingCollectDataTest() {


        String url = "http://services-test-public-images.oss-cn-hangzhou.aliyuncs.com/merchantinfo-images/1552550343757投诉信息1.7.xls";

        String localUrl = null;
        String fileDir = SystemUtil.getUserInfo().getHomeDir() + "/oss-collect-file/";

        long size = HttpUtil.downloadFile(url, FileUtil.file(fileDir));
        if (size > 0) {
            //提取文件名
            String[] fileName = url.split("/");
            localUrl = fileDir.concat(fileName[fileName.length -1 ]);
        }
        ExcelReader reader = ExcelUtil.getReader(localUrl);
        List<List<Object>>dataList =  reader.read();
        dataList.remove(0);


        System.out.println(dataList);

        Assert.fail("123");
    }
}
