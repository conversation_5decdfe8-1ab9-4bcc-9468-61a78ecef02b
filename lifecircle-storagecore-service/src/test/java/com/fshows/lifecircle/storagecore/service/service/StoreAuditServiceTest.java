package com.fshows.lifecircle.storagecore.service.service;

import com.fshows.lifecircle.storagecore.service.domain.model.StoreAuditListModel;
import com.fshows.lifecircle.storagecore.service.domain.param.StoreAuditSearchParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version StoreAuditServiceTest.java, v 0.1 2021-09-01 11:10 chenjn
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class StoreAuditServiceTest {

    @Autowired
    private StoreAuditService storeAuditService;

    /**
     * 查询查询门店类别
     */
    @Test
    public void testGetStoreAuditList() {
        StoreAuditSearchParam param = new StoreAuditSearchParam();
        param.setJoinChannel(-1);
        param.setOwnRun(-1);
        param.setQueryType(1);
        param.setStoreStatus(2);
        param.setIsOpen(-1);
        param.setIncomeStatus(null);
        param.setStoreStatus(2);
        param.setJoinChannel(-1);
        param.setStartDate("2021-06-01");
        param.setEndDate("2021-09-01");
        StoreAuditListModel storeAuditList = storeAuditService.getStoreAuditList(param);
        System.out.println(storeAuditList.getList().size());
    }

}