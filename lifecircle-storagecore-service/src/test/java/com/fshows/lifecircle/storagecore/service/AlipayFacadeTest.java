package com.fshows.lifecircle.storagecore.service;
/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */

import com.fshows.lifecircle.storagecore.facade.AlipayDragonflyFacade;
import com.fshows.lifecircle.storagecore.facade.DownloadSnExcelFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.MaterialRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.MaterialResponse;
import com.fshows.lifecircle.storagecore.facade.exception.AccountException;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version AlipayFacadeTest.java, v 0.1 2019-10-24 13:35
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class AlipayFacadeTest {

    @Autowired
    private AlipayDragonflyFacade alipayFacade;

    @Autowired
    private DownloadSnExcelFacade downloadSnExcelFacade;

    @Test
    public void uploadMaterial() {
        MaterialRequest request = new MaterialRequest();
        //request.setMaterialUrl("https://fshows-shop.oss-cn-shanghai.aliyuncs.com/fshows/shop/img/101813/101813-1.jpg");
        //request.setMaterialUrl("https://fshows-shop.oss-cn-shanghai.aliyuncs.com/fshows/shop/temp/4.jpg");
        //request.setMaterialUrl("https://fshows-shop.oss-cn-shanghai.aliyuncs.com/fshows/shop/temp/18yanghao.png");
        request.setMaterialUrl("https://fshows-shop.oss-cn-shanghai.aliyuncs.com/fshows/shop/temp/haibao2.png");

        MaterialResponse response = alipayFacade.uploadMaterial(request);
        System.out.println("response === " + response);
    }

    @Test
    public void clientsMessageUploadTest() {
        throw AccountException.RECEIPT_TITLE_LENGTH_ERROR;
    }


}
