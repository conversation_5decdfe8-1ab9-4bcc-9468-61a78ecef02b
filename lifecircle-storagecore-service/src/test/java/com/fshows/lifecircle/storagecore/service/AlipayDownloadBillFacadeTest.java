/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.AlipayDownloadBillFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.AlipayDownloadBillRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.AlipayDownloadBillResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version AlipayDownloadBillFacadeTest.java, v 0.1 2019-05-08 15:44 sus
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class AlipayDownloadBillFacadeTest {
    String token = "203726";
    String appAuthToken = "201905BB1e27facfbc6444c2a1de5904abc79X09";

    @Autowired
    private AlipayDownloadBillFacade alipayDownloadBillFacade;

    /**
     * 下载支付宝对账单并上传到阿里云OSS
     */
    @Test
    public void downloadBillForAlipayTest() {
        AlipayDownloadBillRequest request = new AlipayDownloadBillRequest();
        request.setToken(token);
        request.setAppAuthToken(appAuthToken);
        request.setBillDate("2019-05-07");
        long time = System.currentTimeMillis();
        AlipayDownloadBillResponse response = alipayDownloadBillFacade.downloadBillForAlipay(request);
        System.out.println(response);
        System.out.println("耗时：" + (System.currentTimeMillis() - time));
    }

    @Test
    public void getAlipayBillDownloadUrlTest() {
        AlipayDownloadBillRequest request = new AlipayDownloadBillRequest();
        request.setToken(token);
        request.setAppAuthToken(appAuthToken);
        request.setBillDate("2019-05-07");
        long time = System.currentTimeMillis();
        AlipayDownloadBillResponse response = alipayDownloadBillFacade.getAlipayBillDownloadUrl(request);
        System.out.println(response);
        System.out.println("耗时：" + (System.currentTimeMillis() - time));
    }
}