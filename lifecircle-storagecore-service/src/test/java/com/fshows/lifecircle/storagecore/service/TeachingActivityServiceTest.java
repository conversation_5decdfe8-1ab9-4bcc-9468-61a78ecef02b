/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.model.ExportTeachingActivityListModel;
import com.fshows.lifecircle.storagecore.service.domain.param.ExportTeachingActivityListParam;
import com.fshows.lifecircle.storagecore.service.service.TeachingActivityService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version TeachingActivityServiceTest.java, v 0.1 2021-11-04 18:43 chenhh
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class TeachingActivityServiceTest {

    @Autowired
    TeachingActivityService teachingActivityService;

    @Test
    public void exportBatchReceiptOrderList() {
        ExportTeachingActivityListParam param = new ExportTeachingActivityListParam();
        param.setSearchType(2);
        param.setKeywords("");
        param.setSubmitEndTime(null);
        param.setSubmitEndTime(null);
        param.setBizStatus(-1);
        ExportTeachingActivityListModel model = teachingActivityService.exportTeachActivityList(param);
        System.out.println(model);
    }

}