package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.domain.request.ReturnFileDownloadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.ReturnFileDownloadResponse;
import com.fshows.lifecircle.storagecore.facade.enums.LiquidationTypeEnum;
import com.fshows.lifecircle.storagecore.service.domain.model.ReturnFileDownloadModel;
import com.fshows.lifecircle.storagecore.service.domain.param.ReturnFileDownloadParam;
import com.fshows.lifecircle.storagecore.service.service.impl.ReturnFileDownloadServiceImpl;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ReturnFileDownloadFacadeImplTest {

    @Autowired
    private ReturnFileDownloadFacadeImpl returnFileDownloadFacade;
    @Autowired
    private ReturnFileDownloadServiceImpl returnFileDownloadService;

    /**
     * 测试乐刷回盘
     */
    @Test
    public void leshuaReturnFileDownload() {
        String fileUrl = "http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/equipment-pkg/leshua20190507-1557213211000.zip";
        ReturnFileDownloadRequest request = new ReturnFileDownloadRequest();
        request.setFileUrl(fileUrl);
        request.setLiquidatorId("123");
        request.setTradeDate(20190501);
        request.setType(1);
        ReturnFileDownloadResponse response = returnFileDownloadFacade.leshuaReturnFileDownload(request);
        System.out.println(response);
    }

    /**
     * 测试钉钉推送
     */
    @Test
    public void testSendDingMessage() {
        returnFileDownloadService.sendDingMessage("乐刷", LiquidationTypeEnum.LESHUA_WX.getValue());
        returnFileDownloadService.sendDingMessage("随行付", LiquidationTypeEnum.SXPAY_WX.getValue());
    }

    /**
     * 测试随行付/衫德回盘
     */
    @Test
    public void sxPayReturnFileDownload() {
        String fileUrl = "http://www.mscript.com/Log/20190906/shande.zip";
        ReturnFileDownloadRequest request = new ReturnFileDownloadRequest();
        request.setFileUrl(fileUrl);
        request.setLiquidatorId("20190822123456789");
        request.setTradeDate(20190906);
        request.setType(1);
        ReturnFileDownloadResponse response = returnFileDownloadFacade.sxPayReturnFileDownload(request);
        System.out.println(response);
    }

    /**
     * 测试汇付回盘
     */
    @Test
    public void hfPayReturnFileDownload() {
        String fileUrl = "http://www.mscript.com/Log/20190618/huifu_counteroffer.zip?name=1";
        ReturnFileDownloadParam param = new ReturnFileDownloadParam();
        param.setFileUrl(fileUrl);
        param.setLiquidatorId("123");
        param.setTradeDate(20190618);
        param.setType(1);
        ReturnFileDownloadModel model = returnFileDownloadService.hfPayReturnFileDownload(param);
        System.out.println(model);
    }


}