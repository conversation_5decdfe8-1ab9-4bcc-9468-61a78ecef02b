/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.model.HwEquipmentBindWhiteListImportParseModel;
import com.fshows.lifecircle.storagecore.service.domain.model.prepaycard.CommonOperateModel;
import com.fshows.lifecircle.storagecore.service.domain.param.HwEquipmentBindWhiteListImportConfirmParam;
import com.fshows.lifecircle.storagecore.service.domain.param.HwEquipmentBindWhiteListImportParseParam;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwEquipmentBindWhiteImportServiceTest.java, v 0.1 2023-06-17 09:24 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class HwEquipmentBindWhiteImportServiceTest {

    @Autowired
    private HwEquipmentBindWhiteImportService hwEquipmentBindWhiteImportService;

    @Test
    public void importParse() {

        HwEquipmentBindWhiteListImportParseParam param = new HwEquipmentBindWhiteListImportParseParam();
        param.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/白名单批量导入模板.xlsx");
        //----------- Arrange -----------//
        HwEquipmentBindWhiteListImportParseModel importParseModel = hwEquipmentBindWhiteImportService.importParse(param);
        System.out.println(JSON.toJSONString(importParseModel));

        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }

    @Test
    public void importConfirm() {

        //----------- Arrange -----------//
        HwEquipmentBindWhiteListImportConfirmParam param = new HwEquipmentBindWhiteListImportConfirmParam();
        param.setJobNumber("4321");
        param.setRealName("张梦捷");
        param.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/白名单批量导入模板.xlsx");
        //----------- Arrange -----------//
        CommonOperateModel commonOperateModel = hwEquipmentBindWhiteImportService.importConfirm(param);
        System.out.println(JSON.toJSONString(commonOperateModel));


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }
}