/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.CommissionExportFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.SuperMerchantCommissionExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.CommonExportResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version CommissionExportFacadeTest.java, v 0.1 2020-09-08 11:55 zhoujp
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class CommissionExportFacadeTest {

    @Autowired
    private CommissionExportFacade commissionExportFacade;

    @Test
    public void superMerchantListExportTest(){
        SuperMerchantCommissionExportRequest request = new SuperMerchantCommissionExportRequest();
        request.setAgentId(0);
        request.setSearchType(1);
        request.setAgentType(0);
        request.setSearchContent("");
        request.setStartTime(20200801);
        request.setEndTime(20200830);
        CommonExportResponse response = commissionExportFacade.superMerchantListExport(request);
        System.out.println(response);
    }
}