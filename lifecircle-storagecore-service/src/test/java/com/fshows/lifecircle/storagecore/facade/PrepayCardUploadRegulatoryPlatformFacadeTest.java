/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.facade.domain.request.prepaycard.PrepayCardUploadRegulatoryRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.prepaycard.PrepayCardUploadResultQueryRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.prepaycard.PrepayCardUploadRegulatoryResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.prepaycard.PrepayCardUploadResultQueryResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import lombok.extern.slf4j.Slf4j;
import org.assertj.core.util.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version PrepayCardUploadRegulatoryPlatformFacadeTest.java, v 0.1 2022-09-15 上午9:39 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class PrepayCardUploadRegulatoryPlatformFacadeTest {

    @Autowired
    private PrepayCardUploadRegulatoryPlatformFacade prepayCardUploadRegulatoryPlatformFacade;
    @Autowired
    private SysConfig sysConfig;

    @Test
    public void uploadPrepayCardInfo() throws Exception {
        PrepayCardUploadRegulatoryRequest request = new PrepayCardUploadRegulatoryRequest();
        request.setCalculateStartTime(20220811);
        request.setCalculateEndTime(20220811);
        PrepayCardUploadRegulatoryResponse response = prepayCardUploadRegulatoryPlatformFacade.uploadPrepayCardInfo(request);
        System.out.println(JSON.toJSONString(response));
    }

    @Test
    public void queryUploadResult() throws Exception {
        PrepayCardUploadResultQueryRequest request = new PrepayCardUploadResultQueryRequest();
        request.setFlowNoList(Lists.newArrayList("202209151040481000027634"));
        PrepayCardUploadResultQueryResponse response = prepayCardUploadRegulatoryPlatformFacade.queryUploadResult(request);
        System.out.println(JSON.toJSONString(response));
    }
}