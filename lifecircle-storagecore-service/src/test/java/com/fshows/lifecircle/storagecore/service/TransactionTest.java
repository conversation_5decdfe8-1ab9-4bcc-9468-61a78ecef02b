/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.param.wanda.WandaMerchantSweetGreenClubImportParam;
import com.fshows.lifecircle.storagecore.service.service.WandaMerchantManageExportService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version TransactionTest.java, v 0.1 2024-04-22 15:03 ZFQ
 */
@SpringBootTest
@RunWith(SpringJUnit4ClassRunner.class)
public class TransactionTest {

    @Autowired
    private WandaMerchantManageExportService wandaMerchantManageExportService;

    @Test
    public void test1() {
        WandaMerchantSweetGreenClubImportParam param = new WandaMerchantSweetGreenClubImportParam();
        param.setOssUrl("crm/admin/import/wanda/merchant/20240422/1713769348279万达广场丙晟门店入驻 (3).xlsx");
        wandaMerchantManageExportService.batchSaveWandaStore(param);
    }

//    public void test2() {
//        TransactionStatus transaction = lifeCircleTransactionManager.getTransaction(new DefaultTransactionDefinition());
//        try {
//            lifecircleWandaDataSyncTempDAO.batchSaveWandaStore(wandaDataSyncTempDOList);
//            int i  = 1/0;
//            wandaDataSyncTempExtendDAO.batchSaveWandaStore(extendDOList);
//            lifeCircleTransactionManager.commit(transaction);
//        } catch (Exception e) {
//            lifeCircleTransactionManager.rollback(transaction);
//            throw e;
//        }
//    }

}
