/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.umpay.UmPayFilesUploadDirectBase64Request;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version UmPayPictureFacadeTest.java, v 0.1 2022-03-10 9:02 AM youmingming
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class UmPayPictureFacadeTest {

    @Autowired
    private UmPayPictureFacade umPayPictureFacade;


    @Test
    public void filesUploadDirect() {
        final UmPayFilesUploadDirectBase64Request request = new UmPayFilesUploadDirectBase64Request();
        final String url = "http://fs-crm-admin.oss-cn-hangzhou.aliyuncs.com/crm/app/image/CRMFormalAliCloudOSSDirectory/openAccount-images/*************.jpeg";
        request.setUrl(url);
        System.out.println(umPayPictureFacade.filesUploadDirect(request));
    }
}
