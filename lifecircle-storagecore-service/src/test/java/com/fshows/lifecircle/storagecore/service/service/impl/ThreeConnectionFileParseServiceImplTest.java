/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import static org.junit.Assert.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.UnsupportedEncodingException;

/**
 * <AUTHOR>
 * @version ThreeConnectionFileParseServiceImplTest.java, v 0.1 2020-03-02 17:52 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ThreeConnectionFileParseServiceImplTest {

    @Autowired
    private ThreeConnectionFileParseServiceImpl threeConnectionFileParseServiceImpl;

    @Test
    public void parseStatementAccountFile() throws Exception {
        threeConnectionFileParseServiceImpl.parseStatementAccountFile();
    }


    @Test
    public void getStatementAccountFileUrl() throws Exception {

        String url = threeConnectionFileParseServiceImpl.getStatementAccountFileUrl();
        System.out.println(url);
    }
}