/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.LoanFacade;
import com.fshows.lifecircle.storagecore.facade.MemberFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.MemberImportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.QueryApplyListRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.QueryLoanListRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.FileUrlResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version MemberFacadeTest.java, v 0.1 2019-07-26 10:56 wangyi
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class MemberFacadeTest {

    @Autowired
    private MemberFacade memberFacade;

    /**
     * 会员数据导入
     */
    @Test
    public void testMemberImport() {
        MemberImportRequest request = new MemberImportRequest();
        request.setFileName("test.xlsx");
        request.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/member-import/test_20191204121749.xlsx");
        request.setToken("203547");
        memberFacade.importMember(request);
    }

}