/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.model.ExportBatchReceiptOrderListModel;
import com.fshows.lifecircle.storagecore.service.domain.model.QrcodeDownLoadUrlModel;
import com.fshows.lifecircle.storagecore.service.domain.param.DownloadTableCodeBatchParam;
import com.fshows.lifecircle.storagecore.service.domain.param.ExportBatchReceiptOrderListParam;
import com.fshows.lifecircle.storagecore.service.service.QrcodeOrderService;
import com.fshows.lifecircle.storagecore.service.service.QrorderingReceiptOrderService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @version QrcodeOrderService.java, v 0.1 2021-08-04 14:11 zhanyiming
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class QrcodeOrderServiceTest {

    @Autowired
    QrcodeOrderService qrcodeOrderService;
    @Autowired
    QrorderingReceiptOrderService qrorderingReceiptOrderService;

    @Test
    public void downloadTableCodeBatch() {
        DownloadTableCodeBatchParam down = new DownloadTableCodeBatchParam();
        down.setNumber(2);
        List<Integer> list = new ArrayList<>();
        list.add(4639);
        list.add(4649);
        list.add(4659);
        list.add(4669);
        down.setMerchantIdList(list);
        QrcodeDownLoadUrlModel qrcodeDownLoadUrlModel = qrcodeOrderService.downloadTableCodeBatch(down);

        System.out.println(qrcodeDownLoadUrlModel.getUrl());
    }


    @Test
    public void exportBatchReceiptOrderList() {
        ExportBatchReceiptOrderListParam down = new ExportBatchReceiptOrderListParam();
        ArrayList<String> list = new ArrayList<>();
        list.add("202109281405557269340331122");
        down.setReceiptIds(list);
        down.setStartTime("2021-09-23");
        down.setEndTime("2021-09-29");
        ExportBatchReceiptOrderListModel model = qrorderingReceiptOrderService.exportBatchReceiptOrderList(down);
        System.out.println(model.getDownUrl());
    }
}