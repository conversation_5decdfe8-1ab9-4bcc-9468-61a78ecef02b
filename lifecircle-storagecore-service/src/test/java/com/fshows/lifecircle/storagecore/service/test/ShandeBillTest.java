package com.fshows.lifecircle.storagecore.service.test;/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */

import cn.hutool.core.text.csv.CsvUtil;
import cn.hutool.core.text.csv.CsvWriter;
import cn.hutool.core.util.CharsetUtil;
import com.fshows.fsframework.core.constants.StringPool;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.BufferedReader;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.LinkedList;
import java.util.List;

/**
 * <AUTHOR>
 * @version ShandeBillTest.java, v0.1 2021-12-17-10:02 上午 shijp
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
public class ShandeBillTest {

    @Test
    public void test1() {
        String[] newHeader = new String[]{"编号","杉德订单号","杉德退款单号"};
        Arrays.fill(newHeader,0,1,"杉德商户账户");
        System.out.println(Arrays.asList(newHeader).toString());
    }

    @Test
    public void test2() {
        String[] newHeader = new String[]{"杉德商户账户","杉德订单号","杉德退款单号"};
        List<String> list = Arrays.asList(newHeader);
        list.add(1,"list insert 1 index");
        System.out.println(list.toString());
    }

    @Test
    public void test3() {
        String header="BT:编号,交易时间,订单号,商户号,费率(万分之一),交易金额(分),手续费(分),结算金额(分),资金流向,交易类型,支付方式,卡类型,商户优惠类型,参考号,凭证号,终端号,第三方订单号,第三方退款号,支付时间,上游订单号(网、银联),上游订单号(微信、支付宝),付款银行,消费者标识,扫码支付交易类型,乐刷退款流水号,优惠活动,订单金额(分),优惠金额(分),代金券类型,操作类型,分账标识,终端编号,门店编号,补贴金额(分),提现手续费(分),费率(百万分之一),补贴费率(百万分之一),分期期数,分期商户加收手续费";
        String line="891401000019306,2021-12-15 11:51:26,8000424192821349,5562117928,30,298000,894,297106,退款,退货成功,微信支付,*,标准类,,*,*,20211215114918240069,20211215115125979202T,2021-12-15 11:51:35,50101100162021121515379424379,,,,MICROPAY,8000424192821349,,298000,0,UNKNOWN,1,0,None,None,0,0,3000,,,*";
        String[] nheader = header.split(StringPool.COMMA);
        Arrays.fill(nheader,0,1,"杉德商户账户");
        List<String> strings = Arrays.asList(nheader);
        LinkedList<String> list=new LinkedList<>(strings);
        list.add(1,"杉德订单号");
        list.add(2,"杉德退款单号");
        String [] String = (java.lang.String[]) list.toArray();
        System.out.println(strings.toString());
        System.out.println(list.toString());
    }

    @Test
    public void test4() {
        String tempFile = "/Users/<USER>/Downloads/69037_shande_bill_test.txt";
        String localFilePath = "/Users/<USER>/Downloads/69037_shande_bill_2021-12-16.txt";
        CsvWriter writer = CsvUtil.getWriter(tempFile, CharsetUtil.CHARSET_UTF_8);
        BufferedReader reader = cn.hutool.core.io.FileUtil.getReader(localFilePath, CharsetUtil.CHARSET_UTF_8);
        try {
            //跳过第一行
            reader.readLine();
            //写入文件头
            String title = reader.readLine();
            String[] header = title.split(StringPool.COMMA);
            Arrays.fill(header, 0, 1, "杉德商户账户");
            LinkedList list=new LinkedList(Arrays.asList(header));
            list.add(1, "杉德订单号");
            list.add(2, "杉德退款单号");
            //杉德对账单头
            writer.write((String[]) list.toArray());
            //批量处理
            List<String[]> batch=new ArrayList<>();
            String line = null;
            while ((line = reader.readLine()) != null) {
                String[] row = line.split(StringPool.COMMA);
                batch.add(row);
                if (batch.size() == 50) {
                    writer.write(batch);
                    batch.clear();
                }
            }
            //最后将剩余的数据写入
            if (batch.size() > 0) {
                writer.write(batch);
            }
        } catch (IOException e) {
        }finally {
            try {
                reader.close();
                writer.close();
            } catch (IOException e) {
            }
        }
    }
}
