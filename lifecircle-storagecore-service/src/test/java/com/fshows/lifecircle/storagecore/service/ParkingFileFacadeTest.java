/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import cn.hutool.poi.excel.ExcelReader;
import cn.hutool.poi.excel.ExcelUtil;
import com.fshows.lifecircle.storagecore.common.constants.ParkingConstant;
import com.fshows.lifecircle.storagecore.service.domain.model.ParkingInfoImportDataModel;

import java.io.File;
import java.util.List;

/**
 * <AUTHOR>
 * @version ParkingFileFacadeTest.java, v 0.1 2019-09-17 10:55 sus
 */
public class ParkingFileFacadeTest {
    public static void main(String[] args) {
        File file = new File("C:\\Users\\<USER>\\crm\\admin\\import\\activity\\parking\\20190917105308157.xlsx");
        ExcelReader reader = ExcelUtil.getReader(cn.hutool.core.io.FileUtil.file(file), ParkingConstant.PARKING_IMPORT_DEFAULT_EXCEL_SHEET);
        reader.setHeaderAlias(ParkingConstant.PARKING_IMPORT_TITLE_MAP);
        List<ParkingInfoImportDataModel> list = reader.read(ParkingConstant.PARKING_IMPORT_HEADER_ROW_INDEX,
                ParkingConstant.PARKING_IMPORT_START_ROW_INDEX, ParkingConstant.PARKING_IMPORT_END_ROW_INDEX, ParkingInfoImportDataModel.class);
        System.out.println(list);
    }
}