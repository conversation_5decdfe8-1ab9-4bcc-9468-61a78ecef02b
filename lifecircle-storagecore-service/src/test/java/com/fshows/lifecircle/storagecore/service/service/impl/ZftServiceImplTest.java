package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.dto.ZftSimpleDTO;
import com.fshows.lifecircle.storagecore.service.service.ZftService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;

/**
 * fshows.com
 * Copyright (C) 2013-2018 All Rights Reserved.
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class ZftServiceImplTest {

    @Autowired
    private ZftService zftService;

    @Test
    public void testParseExcelSimple() {
        zftService.parseExcelSimple("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/member-import/merchant_img_1642122736371.jpeg");
    }

    @Test
    public void testParseLocalExcel() {
        List<ZftSimpleDTO> list = zftService.parseLocalExcel("/Users/<USER>/Downloads/直付通商户进件模版(2).xlsx");
        System.out.println(list);
    }
}