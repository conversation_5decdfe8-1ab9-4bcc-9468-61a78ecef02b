/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.service.ScanFaceEquipmentDataService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.io.File;

/**
 * <AUTHOR>
 * @version ScanFaceEquipmentDataServiceImplTest.java, v 0.1 2019-08-15 16:02 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ScanFaceEquipmentDataServiceImplTest {

    @Autowired
    private ScanFaceEquipmentDataService scanFaceEquipmentDataServiceImpl;


    @Test
    public void executeData() {
        File file = new File("C:\\Users\\<USER>\\Desktop\\支付宝扫脸设备交易信息数据-小英.xlsx");
        scanFaceEquipmentDataServiceImpl.executeData(file, "29898", false);
    }

    @Test
    public void executeMerchantRemitData() {
        File file = new File("C:\\Users\\<USER>\\Desktop\\扫脸结算账单模板(1).xlsx");
        scanFaceEquipmentDataServiceImpl.executeMerchantRemitData(file,"1212121212");
    }

    @Test
    public void executeAlipayAntIotQuery() {
        scanFaceEquipmentDataServiceImpl.executeAlipayAntIotQuery("");
    }
}