/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.UnionActivityFacade;
import com.fshows.lifecircle.storagecore.facade.UnionPayActivityFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.UnionActivityPicUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.UnionPayActivityPicUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.UnionActivityPicUploadResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.UnionPayActivityPicUploadResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version UnionPayActivityFacadeImplTest.java, v 0.1 2020-06-16 4:43 下午 wangjianguan
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
@Slf4j
public class UnionPayActivityFacadeImplTest {

    @Autowired
    private UnionPayActivityFacade unionPayActivityFacade;
    @Autowired
    private UnionActivityFacade unionActivityFacade;


    @Test
    public void picUpload() {
        UnionPayActivityPicUploadRequest request = new UnionPayActivityPicUploadRequest();
        request.setPicName("身份证正面照");
        request.setPicture("http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/21d76f9c2f940c8ea3941f00e80fa389.jpg");
        request.setPicType("0001");
        request.setReqMsgId("REQ_" + System.currentTimeMillis());
        request.setOrganizationNum("C0002557");
        UnionPayActivityPicUploadResponse unionPayActivityPicUploadResponse = unionPayActivityFacade.picUpload(request);
        LogUtil.info(log, "unionPayActivityPicUploadResponse = {}", unionPayActivityPicUploadResponse);
    }

    /**
     * 银联红火活动报名
     * <p>
     * UnionActivityPicUploadResponse[respCode=0000,respMsg=Ok,signature=jUDjQr2LH0ofPFgAvxjck08eLaWAeTQp7pFvLdC7gTere0A7ndFWC5k7tJeqynJtSJVxWQMElUbiGX+YEZA2XSfouYoO7ihZsiNWsX0Ywil3o8PepLoleB/o/7nQMWyegaS7LFlNoIJXErqE8zEb8AZC7bddnhmDOHktgRPqDBM=,
     * fildId=1645495149223d787528]
     */
    @Test
    public void picUploadTest() {
        UnionActivityPicUploadRequest uploadRequest = new UnionActivityPicUploadRequest();
        // 服务商id
        uploadRequest.setPnrInsIdCd("C1000005");
        // 银联商户编号-- QRC440306477438
        uploadRequest.setMchntCode("QRC330100231422");
        // 门头照
        uploadRequest.setMchntPhoto("https://img.51youdian.com//data/ArticleImg/197001/image/20170212/58a0313fc797d.jpg");
        // 图片类型
        uploadRequest.setPhotoType("01");
        UnionActivityPicUploadResponse response = unionActivityFacade.picUpload(uploadRequest);
        LogUtil.info(log, "response = {}", response);
    }
}