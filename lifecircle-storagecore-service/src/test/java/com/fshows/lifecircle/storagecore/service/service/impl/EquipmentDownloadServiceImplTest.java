/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.param.EquipmentDownloadParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version EquipmentDownloadServiceImplTest.java, v 0.1 2020-06-02 10:09 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EquipmentDownloadServiceImplTest {

    @Autowired
    private EquipmentDownloadServiceImpl equipmentDownloadServiceImpl;


    @Test
    public void exportEquipmentInfoForOem() {

        EquipmentDownloadParam equipmentDownloadParam = new EquipmentDownloadParam();
        equipmentDownloadParam.setOwnRun(-1);
        equipmentDownloadParam.setOemId(9);

        equipmentDownloadServiceImpl.exportEquipmentInfoForOem(equipmentDownloadParam);
    }
}