/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.LoanFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.QueryApplyListRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.QueryLoanListRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.loan.FbankCreditStorageRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.loan.FbankLoanStorageRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.FileUrlResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version LoanFacadeTest.java, v 0.1 2019-07-26 10:56 CoderMa
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class LoanFacadeTest {

    @Autowired
    private LoanFacade loanFacade;

    /**
     * 查询进件数据统计
     */
    @Test
    public void exportApplyList() {
        QueryApplyListRequest request = new QueryApplyListRequest();
        FileUrlResponse response = loanFacade.exportApplyList(request);
        LogUtil.info(log, "response={}", response);
    }

    /**
     * 查询放款数据统计
     */
    @Test
    public void exportLoanList() {
        QueryLoanListRequest request = new QueryLoanListRequest();
        FileUrlResponse response = loanFacade.exportLoanList(request);
        LogUtil.info(log, "response={}", response);
    }

    /**
     * 富民银行放款数据导出
     */
    @Test
    public void exportFbankCreditList() {
        FbankCreditStorageRequest request = new FbankCreditStorageRequest();
//        request.setCompleteStartDate(**********);
//        request.setCompleteEndDate(**********);
//        request.setMerchantId(1);
//        request.setCreditStatus("S");
        FileUrlResponse response = loanFacade.exportFbankCreditApplyList(request);
        LogUtil.info(log, "response={}", response);
    }

    /**
     * 富民银行放款数据导出
     */
    @Test
    public void exportFbankLoanList() {
        FbankLoanStorageRequest request = new FbankLoanStorageRequest();
        request.setCompleteStartDate(**********);
        request.setCompleteEndDate(**********);
        request.setMerchantId(1);
        request.setTradeStatus("4");
        FileUrlResponse response = loanFacade.exportFbankLoanList(request);
        LogUtil.info(log, "response={}", response);
    }

}