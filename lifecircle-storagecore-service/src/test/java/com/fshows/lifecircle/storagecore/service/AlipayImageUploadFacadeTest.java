package com.fshows.lifecircle.storagecore.service;
/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */

import com.fshows.lifecircle.storagecore.facade.AlipayImageUploadFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.AlipayImageUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.AlipayZftImageUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.AlipayImageUploadResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.AlipayZftImageUploadResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version AlipayFacadeTest.java, v 0.1 2019-10-24 13:35
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class AlipayImageUploadFacadeTest {

    @Autowired
    private AlipayImageUploadFacade alipayImageUploadFacade;

    @Test
    public void uploadMaterial() {
        AlipayImageUploadRequest request = new AlipayImageUploadRequest();
        request.setImageUrl("http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/232dd34005ada925cd58bc316599ff02.jpg");

        AlipayImageUploadResponse response = alipayImageUploadFacade.imageUpload(request);
        System.out.println("imageId === " + response.getImageId());
    }

    @Test
    public void uploadZftMaterial() {
        AlipayZftImageUploadRequest request = new AlipayZftImageUploadRequest();
        request.setImageUrl("http://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/app/image/232dd34005ada925cd58bc316599ff02.jpg");

        AlipayZftImageUploadResponse response = alipayImageUploadFacade.zftImageUpload(request);
        System.out.println("imageId === " + response.getImageId());
    }

}
