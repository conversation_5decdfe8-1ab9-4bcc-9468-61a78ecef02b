/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.param.ReturnFileDownloadParam;
import com.fshows.lifecircle.storagecore.service.service.VbillReturnFileDownloadService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version VbillReturnFileDownloadServiceImplTest.java, v 0.1 2020-06-29 3:44 下午 youmingming
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class VbillReturnFileDownloadServiceImplTest {

    @Autowired
    VbillReturnFileDownloadService vbillReturnFileDownloadService;

    @Test
    public void vbillReturnFileDownload() {
        ReturnFileDownloadParam param = new ReturnFileDownloadParam();
        param.setFileUrl("https://openapi.tianquetech.com/capital/fileDownload/download?downUrl=https%3A%2F%2Fosscdn.suixingpay.com%2F0%2F2%2F32MDJhYjIyYWZ6N0NhbzJheDIwMjBfMDZfMjlfMTFfMDBfOGZhNTRjMTA0YjJkNDcwNTg2YzhlNWZjOTlhNjg5MDMyYUtpY21fYnVja2V0MmFiMQ.csv");
        param.setLiquidatorId("74861274");
        param.setTradeDate(20200628);
        param.setType(1);
        System.out.println(vbillReturnFileDownloadService.vbillReturnFileDownload(param));
    }


}
