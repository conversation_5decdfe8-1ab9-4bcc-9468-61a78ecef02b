/*
 * ailike.com
 * Copyright (C) 2022-2025 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.MerchantCredentialsBatchImportRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version MerchantCredentialsExpireFacadeTest.java, v 0.1 2025-02-18 11:18 zhangling
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class MerchantCredentialsExpireFacadeTest {

    @Autowired
    private MerchantCredentialsExpireFacade merchantCredentialsExpireFacade;

    @Test
    public void merchantCredentialsOrderBatchImportTest() {
        MerchantCredentialsBatchImportRequest request = new MerchantCredentialsBatchImportRequest();
        request.setFileUrl("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/merchantinfo-images/商户过期证件更新工单模板_17398488621433ecf2db9.xlsx");
        merchantCredentialsExpireFacade.merchantCredentialsOrderBatchImport(request);
    }
}