/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.param.PurchaseFileParseParam;
import com.fshows.lifecircle.storagecore.service.domain.param.StandingBookParam;
import com.fshows.lifecircle.storagecore.service.service.StorageManageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version StorageEquipmentStatisticsServiceImplTest.java, v 0.1 2020-03-31 17:40 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StorageEquipmentStatisticsServiceImplTest {

    @Autowired
    private StorageEquipmentStatisticsServiceImpl storageEquipmentStatisticsServiceImpl;

    @Autowired
    private StorageManageService storageManageService;


    @Test
    public void standingBookExecute() {
        StandingBookParam standingBookParam = new StandingBookParam();
        standingBookParam.setStartTime("2020-03-31 00:00:00");
        standingBookParam.setEndTime("2020-03-31 00:00:00");
        storageEquipmentStatisticsServiceImpl.standingBookExecute(standingBookParam);
    }

    @Test
    public void purchaseBatchParseTest() {
        PurchaseFileParseParam param = new PurchaseFileParseParam();
        param.setFileUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/hardwareadmingw/import/scanface/CfrTtamEAc.xlsx");
        param.setStorageOrder("SDN161397881084655");
        storageManageService.purchaseBatchParse(param);
    }
}