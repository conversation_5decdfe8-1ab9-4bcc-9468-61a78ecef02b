/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.LossStoreNoticeboardExportQueryRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.LossStoreNoticeboardExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.LossStoreNoticeboardExportQueryResponse;
import com.fshows.lifecircle.storagecore.facade.domain.response.LossStoreNoticeboardExportResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version LossStoreNoticeboardExportFacadeTest.java, v 0.1 2024-11-07 18:31 weikunpeng
 */
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class LossStoreNoticeboardExportFacadeTest {

    @Autowired
    private LossStoreNoticeboardExportFacade lossStoreNoticeboardExportFacade;

    @Test
    public void testAsyncExportLossStoreNoticeboard() {
        LossStoreNoticeboardExportRequest lossStoreNoticeboardExportRequest = new LossStoreNoticeboardExportRequest();
        lossStoreNoticeboardExportRequest.setSortType(1);
        lossStoreNoticeboardExportRequest.setUserId(16884);
        lossStoreNoticeboardExportRequest.setStatisticalDay(20241106L);
        lossStoreNoticeboardExportRequest.setUserType(3);
        LossStoreNoticeboardExportResponse lossStoreNoticeboardExportResponse = lossStoreNoticeboardExportFacade.asyncExportLossStoreNoticeboard(lossStoreNoticeboardExportRequest);
        System.out.println("lossStoreNoticeboardExportResponse = " + lossStoreNoticeboardExportResponse);
    }

    @Test
    public void test() {
        LossStoreNoticeboardExportQueryRequest lossStoreNoticeboardExportQueryRequest = new LossStoreNoticeboardExportQueryRequest();
        lossStoreNoticeboardExportQueryRequest.setUserId(16884);
        lossStoreNoticeboardExportQueryRequest.setDownLoadNumber("413C4541C34042CE9FEBCFC6893CDD40");
        LossStoreNoticeboardExportQueryResponse lossStoreNoticeboardExportQueryResponse = lossStoreNoticeboardExportFacade.lossStoreNoticeboardExportQuery(lossStoreNoticeboardExportQueryRequest);
        System.out.println("lossStoreNoticeboardExportQueryResponse = " + lossStoreNoticeboardExportQueryResponse);
    }
}