/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.EquipmentDownloadFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.EquipmentDownloadRequest;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version EquipmentDownloadFacadeImplTest.java, v 0.1 2019-12-27 16:07 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class EquipmentDownloadFacadeImplTest {

    @Autowired
    private EquipmentDownloadFacade equipmentDownloadFacadeImpl;


    @Test
    public void downloadEquipmentInfo() {
        EquipmentDownloadRequest equipmentDownloadRequest = new EquipmentDownloadRequest();
        equipmentDownloadRequest.setJobNumber("2876");
        equipmentDownloadFacadeImpl.downloadEquipmentInfo(equipmentDownloadRequest);
    }

    @Test
    public void exportEquipmentInfoForOem() {
        EquipmentDownloadRequest equipmentDownloadRequest = new EquipmentDownloadRequest();
        equipmentDownloadRequest.setOwnRun(-1);
        equipmentDownloadRequest.setOemId(9);
        equipmentDownloadRequest.setSnStatus(-1);
        equipmentDownloadFacadeImpl.exportEquipmentInfoForOem(equipmentDownloadRequest);
    }

    @Test
    public void downloadEquipmentInfo2() {
        EquipmentDownloadRequest equipmentDownloadRequest = new EquipmentDownloadRequest();
        equipmentDownloadRequest.setJobNumber("2876");
        equipmentDownloadRequest.setDepotIdList(Lists.newArrayList(1, 2));
        equipmentDownloadRequest.setEquipmentNameList(Lists.newArrayList("库存测试"));
        equipmentDownloadRequest.setStartCreateTime("2024-10-13");
        equipmentDownloadRequest.setEndCreateTime("2024-11-13");
        equipmentDownloadFacadeImpl.downloadEquipmentInfo(equipmentDownloadRequest);
    }
}