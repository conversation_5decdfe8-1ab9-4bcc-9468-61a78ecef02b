/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.alibaba.fastjson.JSON;
import com.beust.jcommander.internal.Lists;
import com.fshows.lifecircle.storagecore.intergration.client.IdentityAuthClient;
import com.fshows.lifecircle.storagecore.service.config.SysConfig;
import com.fshows.lifecircle.storagecore.service.domain.model.AliyunOssUtilModel;
import com.fshows.lifecircle.storagecore.service.domain.model.ImportCustomerModel;
import com.fshows.lifecircle.storagecore.service.domain.model.MybankUploadPhotoModel;
import com.fshows.lifecircle.storagecore.service.domain.model.WithdrawBillCheckModel;
import com.fshows.lifecircle.storagecore.service.domain.param.ImportCustomerParam;
import com.fshows.lifecircle.storagecore.service.domain.param.MybankUploadPhotoParam;
import com.fshows.lifecircle.storagecore.service.domain.param.WithdrawBillCheckParam;
import com.fshows.lifecircle.storagecore.service.domain.param.acctbiz.suporder.AcctBizSupOrderUploadParam;
import com.fshows.lifecircle.storagecore.service.service.AliyunOssService;
import com.fshows.lifecircle.storagecore.service.service.HuiFuAccountService;
import com.fshows.lifecircle.storagecore.service.service.HuiFuWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.HuifuAccountParseDataService;
import com.fshows.lifecircle.storagecore.service.service.MyBankWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.MybankPhotoService;
import com.fshows.lifecircle.storagecore.service.service.SxPayWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.SxpayShareWithdrawBillService;
import com.fshows.lifecircle.storagecore.service.service.acctbiz.suporder.AcctBizSupOrderUploadService;
import com.fshows.lifecircle.storagecore.service.utils.SFTPUtil;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.List;

/**
 *
 * <AUTHOR>
 * @version MybankPhotoServiceImplTest.java, v 0.1 2019-04-03 16:16
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class MybankSupOrderTest {

    @Autowired
    private AcctBizSupOrderUploadService acctBizSupOrderUploadService;



    @Test
    public void photoUploadServiceTest() {
        AcctBizSupOrderUploadParam param = new AcctBizSupOrderUploadParam();
//        param.setBlocId("B20250310134135206163");
//        param.setList(Lists.newArrayList("OTN2025031810265930575776"));
        acctBizSupOrderUploadService.uploadSupOrder(param);

    }
}
