/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.model.harvest.HarvestPlanAgentListBatchImportModel;
import com.fshows.lifecircle.storagecore.service.domain.param.HwHarvestPlanAgentListImportParam;
import com.fshows.lifecircle.storagecore.service.service.HwHarvestPlanAgentListService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

/**
 * <AUTHOR>
 * @version HwHarvestPlanAgentListServiceImplTest.java, v 0.1 2024-09-06 14:39 weikunpeng
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class HwHarvestPlanAgentListServiceImplTest {

    @Autowired
    private HwHarvestPlanAgentListService hwHarvestPlanAgentListService;


    @Test
    public void testHarvestPlanAgentListImport() {
        HwHarvestPlanAgentListImportParam hwHarvestPlanAgentListImportParam = new HwHarvestPlanAgentListImportParam();
        hwHarvestPlanAgentListImportParam.setCreater("魏坤鹏");
        hwHarvestPlanAgentListImportParam.setJobNumber("000001");
        hwHarvestPlanAgentListImportParam.setType(1);
        hwHarvestPlanAgentListImportParam.setUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/crm/import/%E9%BB%91%E7%99%BD%E5%90%8D%E5%8D%95%E6%A8%A1%E6%9D%BF202409.xlsx");
//        hwHarvestPlanAgentListImportParam.setUrl("crm/import/黑白名单模板202409.xlsx");
        HarvestPlanAgentListBatchImportModel harvestPlanAgentListBatchImportModel = hwHarvestPlanAgentListService.harvestPlanAgentListImport(hwHarvestPlanAgentListImportParam);
        System.out.println("harvestPlanAgentListBatchImportModel = " + harvestPlanAgentListBatchImportModel);
    }

}