package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.service.domain.model.msfpay.MsfpayUploadPicModel;
import com.fshows.lifecircle.storagecore.service.domain.param.msfpay.MsfpayUploadPicParam;
import com.fshows.lifecircle.storagecore.service.service.MsfpayUploadPicService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class MsfpayUploadPicServiceTest {
    @Autowired
    private MsfpayUploadPicService msfpayUploadPicService;

    @Test
    public void testUploadPic() {
        MsfpayUploadPicParam param = new MsfpayUploadPicParam();
        param.setUrl("C:\\Users\\<USER>\\Desktop\\1755498384716-ztTERyzwbTCkD8PdEjSs7N7QkBA4F5GR.jpg");
        MsfpayUploadPicModel model = msfpayUploadPicService.uploadPic(param);
        System.out.println(model);
    }
}
