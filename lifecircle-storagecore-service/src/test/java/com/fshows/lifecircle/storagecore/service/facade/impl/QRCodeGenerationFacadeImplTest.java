/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.QRCodeGenerationFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.GenerationQrCodeRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.GenerationQrCodeResponse;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version QRCodeGenerationFacadeImplTest.java, v 0.1 2020-05-25 12:20 下午 wangjianguan
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class QRCodeGenerationFacadeImplTest {

    @Autowired
    private QRCodeGenerationFacade qrCodeGenerationFacade;

    @Test
    public void generationQrCode() {
        GenerationQrCodeRequest generationQrCodeRequest = new GenerationQrCodeRequest();
        generationQrCodeRequest.setContent("https://www.yuque.com/dashboard/groups");
        generationQrCodeRequest.setHeight(300);
        generationQrCodeRequest.setWidth(300);
        GenerationQrCodeResponse generationQrCodeResponse = qrCodeGenerationFacade.generationQrCode(generationQrCodeRequest);
        LogUtil.info(log, "generationQrCodeResponse = {}", generationQrCodeResponse);
    }
}