/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopStockCostListExportRequest;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopStockCostOrderExportFacadeTest.java, v 0.1 2021-11-05 11:55 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class HwShopStockCostOrderExportFacadeTest {

    @Autowired
    private HwShopStockCostOrderExportFacade hwShopStockCostOrderExportFacade;

    @Test
    public void exportStockCostOrderList() {
        HwShopStockCostListExportRequest request = new HwShopStockCostListExportRequest();
        request.setJobNumber("4583");
        request.setStartTime("2021-11-01");
        request.setEndTime("2021-11-04");
        hwShopStockCostOrderExportFacade.exportStockCostOrderList(request);
    }
}