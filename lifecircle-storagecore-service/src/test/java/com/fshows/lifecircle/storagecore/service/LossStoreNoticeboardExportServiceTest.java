/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import cn.hutool.core.thread.ThreadUtil;
import com.fshows.lifecircle.storagecore.service.domain.model.LossStoreNoticeboardExportQueryModel;
import com.fshows.lifecircle.storagecore.service.domain.param.LossStoreNoticeboardExportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.LossStoreNoticeboardExportQueryParam;
import com.fshows.lifecircle.storagecore.service.service.LossStoreNoticeboardExportService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @version LossStoreNoticeboardExportServiceTest.java, v 0.1 2024-11-07 15:25 weikunpeng
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class LossStoreNoticeboardExportServiceTest {

    @Autowired
    LossStoreNoticeboardExportService lossStoreNoticeboardExportService;


    @Test
    public void testAsyncExportLossStoreNoticeboard() {
        LossStoreNoticeboardExportParam lossStoreNoticeboardExportParam = new LossStoreNoticeboardExportParam();
        lossStoreNoticeboardExportParam.setSortType(1);
        lossStoreNoticeboardExportParam.setUserId(16884);
        lossStoreNoticeboardExportParam.setStatisticalDay(20241106L);
        lossStoreNoticeboardExportParam.setUserType(3);
        lossStoreNoticeboardExportService.asyncExportLossStoreNoticeboard(lossStoreNoticeboardExportParam);
        ThreadUtil.sleep(10, TimeUnit.SECONDS);
    }


    @Test
    public void testLossStoreNoticeboardExportQuery() {
        //loss.store.notice.export.16884.F4581B668E1544F7B5B25C4182963E60
        LossStoreNoticeboardExportQueryParam lossStoreNoticeboardExportQueryParam = new LossStoreNoticeboardExportQueryParam();
        lossStoreNoticeboardExportQueryParam.setUserId(16884);
        lossStoreNoticeboardExportQueryParam.setDownLoadNumber("F4581B668E1544F7B5B25C4182963E60");
        LossStoreNoticeboardExportQueryModel lossStoreNoticeboardExportQueryModel = lossStoreNoticeboardExportService.lossStoreNoticeboardExportQuery(lossStoreNoticeboardExportQueryParam);
        System.out.println("lossStoreNoticeboardExportQueryModel = " + lossStoreNoticeboardExportQueryModel);
    }
}