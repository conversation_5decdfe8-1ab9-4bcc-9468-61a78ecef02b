package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.service.RealtmeDayTradeService;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
@Slf4j
public class RealtmeDayTradeServiceImplTest {

    @Autowired
    private RealtmeDayTradeService realtmeDayTradeService;

    @Test
    public void exportBstRealtmeDayTradeToDingDing(){
        realtmeDayTradeService.exportBstRealtimeDayTradeToDingDing("2021-12-03");
    }

}