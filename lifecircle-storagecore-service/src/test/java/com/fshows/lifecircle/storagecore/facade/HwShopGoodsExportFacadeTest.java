/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopGoodsListExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.hwshop.HwShopGoodsListExportResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopGoodsExportFacade.java, v 0.1 2021-09-29 15:49 zhangmj
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HwShopGoodsExportFacadeTest {

    @Autowired
    private HwShopGoodsExportFacade hwShopGoodsExportFacade;

    @Test
    public void test() {
        HwShopGoodsListExportRequest request = new HwShopGoodsListExportRequest();
        request.setUnionId("4583");
        request.setJobNumber("4583");
        request.setRealName("张梦捷");
        request.setGoodsName("zmjTestGoodsName");
        HwShopGoodsListExportResponse hwShopGoodsListExportResponse = hwShopGoodsExportFacade.exportGoodsList(request);

    }
}