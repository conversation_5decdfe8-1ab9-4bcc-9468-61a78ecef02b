/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import static org.junit.Assert.*;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.powermock.modules.junit4.PowerMockRunner;
import org.mockito.InjectMocks;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ThreeConnectionFileParseFacadeImplTest.java, v 0.1 2020-03-03 15:28 liyong
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class ThreeConnectionFileParseFacadeImplTest {

    @Autowired
    private ThreeConnectionFileParseFacadeImpl threeConnectionFileParseFacadeImpl;


    @Test
    public void parseStatementAccountFile() throws Exception {
        threeConnectionFileParseFacadeImpl.parseStatementAccountFile();
    }
}