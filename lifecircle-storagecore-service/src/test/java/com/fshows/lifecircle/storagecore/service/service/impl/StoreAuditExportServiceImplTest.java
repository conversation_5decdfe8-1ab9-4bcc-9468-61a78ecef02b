package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.fsframework.extend.redis.RedisCache;
import com.fshows.lifecircle.storagecore.service.domain.param.StoreAuditListExportFileParam;
import com.fshows.lifecircle.storagecore.service.service.StoreAuditExportService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;


/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class StoreAuditExportServiceImplTest {

    @Autowired
    private StoreAuditExportService storeAuditExportService;
    @Autowired
    private RedisCache redisCache;

    @Test
    public void asyncExportStoreAuditFile() {
        StoreAuditListExportFileParam storeAuditListExportFileParam = new StoreAuditListExportFileParam();
        storeAuditListExportFileParam.setUserId("dsfafasdfsdfads");
        storeAuditExportService.asyncExportStoreAuditFile(storeAuditListExportFileParam);

        try {
            Thread.sleep(100000000);
        } catch (InterruptedException e) {
            e.printStackTrace();
        }
    }
}
