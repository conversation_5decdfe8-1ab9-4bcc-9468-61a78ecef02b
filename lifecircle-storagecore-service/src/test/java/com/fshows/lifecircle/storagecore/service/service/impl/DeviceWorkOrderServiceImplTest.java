/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.model.devicework.PhotoSynthesisAndUploadOssModel;
import com.fshows.lifecircle.storagecore.service.domain.param.GenerateTwoDimensionalCodeParam;
import com.fshows.lifecircle.storagecore.service.domain.param.devicework.PhotoSynthesisAndUploadOssParam;
import com.fshows.lifecircle.storagecore.service.service.DeviceWorkOrderService;
import com.fshows.lifecircle.storagecore.service.service.ImageService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version DeviceWorkOrderServiceImplTest.java, v 0.1 2024-08-21 11:34 zhubo
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class DeviceWorkOrderServiceImplTest {

    @Autowired
    private DeviceWorkOrderService deviceWorkOrderService;
    @Autowired
    private ImageService imageService;

    @Test
    public void test() {
        PhotoSynthesisAndUploadOssParam param = new PhotoSynthesisAndUploadOssParam();
        param.setInitSn("20240812026");
        param.setEquipmentId(127504);
        for (int i = 0; i < 500; i++) {
            PhotoSynthesisAndUploadOssModel model = deviceWorkOrderService.photoSynthesisAndUploadOss(param);
            System.out.println(model);
        }


//        System.out.println("----------------------------------------------------------------------------------");
//        PhotoSynthesisAndUploadOssParam param1 = new PhotoSynthesisAndUploadOssParam();
//        param1.setInitSn("20240812026");
////        param1.setEquipmentModel("支付宝碰一下（台式—区代）");
//        long currented2 = System.currentTimeMillis();
//        System.out.println(currented2);
//        PhotoSynthesisAndUploadOssModel model2 = deviceWorkOrderService.photoSynthesisAndUploadOss(param1);
//        System.out.println(model2);
//        System.out.println(System.currentTimeMillis() - currented2);
    }

    @Test
    public void test1() {
        String url = "http://***********:5173?linkId=p+kwDXhrYBk3zfsfqxkBOCfrqV1loaQgx21anYTY4Gg=";
        String key = "merchant/autoincome/123435.png";
        GenerateTwoDimensionalCodeParam param = new GenerateTwoDimensionalCodeParam();
        param.setWidth(544);
        param.setHeight(544);
        param.setObjectKey(key);

        param.setContent(url);

        Boolean b = imageService.generateTwoDimensionalCodeAndMergePictures(param);
        System.out.println("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/" + key);
    }

    @Test
    public void test2() {
        String url = "http://***********:5173?linkId=p+kwDXhrYBk3zfsfqxkBOCfrqV1loaQgx21anYTY4Gg=";
        String key = "merchant/autoincome/123435.png";
        GenerateTwoDimensionalCodeParam param = new GenerateTwoDimensionalCodeParam();
        param.setWidth(544);
        param.setHeight(544);
        param.setObjectKey(key);

        param.setContent(url);

        Boolean b = imageService.generateTwoDimensionalCodeAndMergePictures(param);
        System.out.println("https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com/" + key);
    }
}
