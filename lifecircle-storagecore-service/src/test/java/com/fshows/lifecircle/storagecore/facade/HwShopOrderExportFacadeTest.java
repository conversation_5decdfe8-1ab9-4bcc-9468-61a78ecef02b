/**
 * fshows.com
 * Copyright (C) 2013-2021 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.facade;

import com.aliyun.openservices.shade.com.alibaba.fastjson.JSON;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopOrderListQueryRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.hwshop.HwShopOrderListExportResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopGoodsExportFacade.java, v 0.1 2021-09-29 15:49 zhanghl
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class HwShopOrderExportFacadeTest {

    @Autowired
    private HwShopOrderExportFacade hwShopOrderExportFacade;

    @Test
    public void test() {
        HwShopOrderListQueryRequest request = new HwShopOrderListQueryRequest();
        request.setJobNumber("3260");
        request.setStartTime("2021-10-11");
        request.setEndTime("2021-10-11");
        HwShopOrderListExportResponse response = hwShopOrderExportFacade.exportHwShopOrderList(request);
        LogUtil.info(log, "{}", JSON.toJSONString(response));
    }
}