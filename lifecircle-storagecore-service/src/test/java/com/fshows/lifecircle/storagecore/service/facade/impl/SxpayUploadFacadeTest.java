/**
 * fshows.com
 * Copyright (C) 2013-2019 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.facade.SxpayUploadFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.SxpayUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.SxpayUploadResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version SxpayUploadFacadeTest.java, v 0.1 2019-11-12 09:50
 */
@RunWith(SpringRunner.class)
@SpringBootTest
public class SxpayUploadFacadeTest {

    @Autowired
    private SxpayUploadFacade sxpayUploadFacade;

    @Test
    public void sxpayUploadTest() {
        SxpayUploadRequest request = new SxpayUploadRequest();
        request.setCustomerId("20191106110339875902");
        SxpayUploadResponse response = sxpayUploadFacade.sxpayUpload(request);
        System.out.println(">>>>>>>>>>>>>>>>>>>>>response = " + response);
    }
}