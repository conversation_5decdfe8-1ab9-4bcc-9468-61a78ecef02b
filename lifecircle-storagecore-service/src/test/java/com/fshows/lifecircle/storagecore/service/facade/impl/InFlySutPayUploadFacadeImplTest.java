/**
 * fshows.com
 * Copyright (C) 2013-2023 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.alibaba.fastjson.JSON;
import com.fshows.lifecircle.storagecore.facade.domain.request.inflysutpay.InFlySutPayMediaUploadRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.inflysutpay.InFlySutPayMediaUploadResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version InFlySutPayUploadFacadeImplTest.java, v 0.1 2023-05-23 上午11:14 zhangmj
 */
@Slf4j
@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class InFlySutPayUploadFacadeImplTest {

    @Autowired
    private InFlySutPayUploadFacadeImpl inFlySutPayUploadFacadeImpl;


    @Test
    public void mediaUpload() {
        InFlySutPayMediaUploadRequest request = new InFlySutPayMediaUploadRequest();
        request.setImageUrl("https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/20230526163138252.jpg");
        InFlySutPayMediaUploadResponse response = inFlySutPayUploadFacadeImpl.mediaUpload(request);
        System.out.println(JSON.toJSONString(response));
        //----------- Arrange -----------//


        //-----------   Act   -----------//


        //-----------  Assert -----------//
    }
}