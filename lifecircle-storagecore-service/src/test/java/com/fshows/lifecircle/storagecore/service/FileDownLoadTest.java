/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service;

import com.aliyun.oss.OSSClient;
import com.fshows.fsframework.core.utils.AliyunOssUtil;
import com.fshows.fsframework.core.utils.FsDateUtil;
import com.fshows.fsframework.core.utils.LogUtil;
import com.fshows.lifecircle.storagecore.facade.exception.StorageException;
import com.fshows.lifecircle.storagecore.service.domain.model.prepaycard.PrepayCardSalesImportParseDetailModel;
import com.fshows.lifecircle.storagecore.service.domain.model.prepaycard.PrepayCardSalesImportParseModel;
import com.fshows.lifecircle.storagecore.service.utils.ExcelUtil;
import com.fshows.lifecircle.storagecore.tools.FileUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.SystemUtils;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringJUnit4ClassRunner;

import java.io.File;
import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @version FileDownLoadTest.java, v 0.1 2022-10-19 10:38 zhoujp
 */
@RunWith(SpringJUnit4ClassRunner.class)
@SpringBootTest
@Slf4j
public class FileDownLoadTest {
    @Autowired
    @Qualifier("publicOssClient")
    private OSSClient publicOssClient;

    @Test
    public void downloadTest() {
        // 文件下载路径
        String downloadDir = SystemUtils.getUserHome() + "/test/";
        // 批量迁移批次号
        String batchNo = FsDateUtil.getReqDateyyyyMMddHHmmssSSS(new Date());
        System.out.println("downloadDir=" + downloadDir);
        // 创建下载文件
        File file = FileUtil.createDownloadFile(downloadDir, batchNo, ".xlsx");
        if (null == file) {
            throw StorageException.MERCHANT_TRANSFER_BATCH_ERROR.newInstance("解析批量商户迁移文件：文件创建失败");
        }
        try {
            AliyunOssUtil.downloadFile("fs-crm-admin", "crm/admin/import/merchant_transfer/20221019/1666146109579迁移.xlsx", file, publicOssClient);
            // 解析文件内容
            List<Integer> merchantIdList = ExcelUtil.parseMerchantTransferBatchFile(file);
            System.out.println("merchantIdList"+merchantIdList.size());
        } catch (Exception e) {
            e.printStackTrace();
        }
    }


    @Test
    public void myTest(){
        try{
            // 文件下载路径
            String downloadDir = SystemUtils.getUserHome() + "/test/";
            String batchNo = FsDateUtil.getReqDateyyyyMMddHHmmssSSS(new Date());
            System.out.println("downloadDir=" + downloadDir);
            // 创建下载文件
            File file = FileUtil.createDownloadFile(downloadDir, batchNo, ".xlsx");

            AliyunOssUtil.downloadFileKeep("lifecircle-ark-public","/prepaycard/admin/upload/2022-10-19/1666082836639.xls",file,publicOssClient);

            //解析导入文件
            PrepayCardSalesImportParseModel model = new PrepayCardSalesImportParseModel();
            String[] titleArr = new String[]{"cardNo", "cardAmount", "cardSpuName", "waterQrCodeUrl", "bindPhone"};
            List<PrepayCardSalesImportParseDetailModel> parseDetailModels = ExcelUtil.parse(file, titleArr, PrepayCardSalesImportParseDetailModel.class, true);
            System.out.println("parseDetailModels="+parseDetailModels);
        }catch (Exception e){
            throw StorageException.SALES_BING_PHONE_ERROR.newInstance("文件下载失败");
        }


    }

    public static void main(String[] args) {
        String ss = "/prepaycard/admin/upload/2022-10-19/1666082836639.xls";
        if(ss.startsWith("/")){
            System.out.println(ss.substring(1));
        }
    }
}