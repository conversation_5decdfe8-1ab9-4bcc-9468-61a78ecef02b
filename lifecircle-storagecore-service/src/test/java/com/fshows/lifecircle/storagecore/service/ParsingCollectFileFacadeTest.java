package com.fshows.lifecircle.storagecore.service;

import com.fshows.lifecircle.storagecore.facade.ParsingCollectFileFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.CollectFileParsingRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.MerchantListFileParsingRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.ParsingCollectFileResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version ParsingCollectFileFacadeTest.java, v 0.1 2019-10-18 6:36 下午 zhangshuan
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ParsingCollectFileFacadeTest {

    @Autowired
    private ParsingCollectFileFacade parsingCollectFileFacade;

    @Test
    public void testDownload() {
        MerchantListFileParsingRequest request = new MerchantListFileParsingRequest();
        request.setUrl("http://services-test-public-images.oss-cn-hangzhou.aliyuncs.com/merchantinfo-images/2019-10-18/批量导入 2.xlsx");
        ParsingCollectFileResponse parsingCollectFileResponse = parsingCollectFileFacade.parseMerchantListFile(request);
        System.out.println(parsingCollectFileResponse);
    }

    @Test
    public void testParsingCollectFile() {
        CollectFileParsingRequest collectFileParsingRequest = new CollectFileParsingRequest();
//        collectFileParsingRequest.setCollectDataId("59368802cae341f4bf3160e05f6c1231");
//        collectFileParsingRequest.setUsername("管理员");
        collectFileParsingRequest.setCollectDataId("c56e1084d9ec4447a794b56ddf6a67ab");
        collectFileParsingRequest.setUsername("管理员");
        parsingCollectFileFacade.parseCollectFile(collectFileParsingRequest);

        System.out.println("---------------");
    }

}