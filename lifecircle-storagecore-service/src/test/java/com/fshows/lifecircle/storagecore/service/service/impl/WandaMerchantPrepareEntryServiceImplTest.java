/**
 * fshows.com
 * Copyright (C) 2013-2024 All Rights Reserved.
 */
package com.fshows.lifecircle.storagecore.service.service.impl;

import com.fshows.lifecircle.storagecore.service.domain.model.wanda.WandaMerchantBatchPrepareEntryCheckModel;
import com.fshows.lifecircle.storagecore.service.domain.model.wanda.WandaMerchantBatchPrepareEntryPollingModel;
import com.fshows.lifecircle.storagecore.service.domain.param.wanda.WandaMerchantBatchPrepareEntryCheckParam;
import com.fshows.lifecircle.storagecore.service.domain.param.wanda.WandaMerchantBatchPrepareEntryImportParam;
import com.fshows.lifecircle.storagecore.service.domain.param.wanda.WandaMerchantBatchPrepareEntryPollingParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version WandaMerchantPrepareEntryServiceImplTest.java, v 0.1 2024-09-20 11:27 weikunpeng
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class WandaMerchantPrepareEntryServiceImplTest {

    @Autowired
    private WandaMerchantPrepareEntryServiceImpl wandaMerchantPrepareEntryService;


    @Test
    public void testWandaMerchantBatchPrepareEntryCheck() {
        WandaMerchantBatchPrepareEntryCheckParam wandaMerchantBatchPrepareEntryCheckParam = new WandaMerchantBatchPrepareEntryCheckParam();
        wandaMerchantBatchPrepareEntryCheckParam.setUrl("123/测试.xls");
        WandaMerchantBatchPrepareEntryCheckModel wandaMerchantBatchPrepareEntryCheckModel = wandaMerchantPrepareEntryService.wandaMerchantBatchPrepareEntryCheck(wandaMerchantBatchPrepareEntryCheckParam);
        System.out.println("wandaMerchantBatchPrepareEntryCheckModel = " + wandaMerchantBatchPrepareEntryCheckModel);
    }


    @Test
    public void testWandaMerchantBatchPrepareEntryImport() {
        WandaMerchantBatchPrepareEntryImportParam wandaMerchantBatchPrepareEntryImportParam = new WandaMerchantBatchPrepareEntryImportParam();
        wandaMerchantBatchPrepareEntryImportParam.setUrl("批量新增门店测试数据.xlsx");
        wandaMerchantBatchPrepareEntryImportParam.setUserId("123");
        wandaMerchantPrepareEntryService.wandaMerchantBatchPrepareEntryImport(wandaMerchantBatchPrepareEntryImportParam);
    }

    @Test
    public void testWandaMerchantBatchPrepareEntryPolling() {
        WandaMerchantBatchPrepareEntryPollingParam wandaMerchantBatchPrepareEntryPollingParam = new WandaMerchantBatchPrepareEntryPollingParam();
        wandaMerchantBatchPrepareEntryPollingParam.setUserId("123");
        WandaMerchantBatchPrepareEntryPollingModel wandaMerchantBatchPrepareEntryPollingModel = wandaMerchantPrepareEntryService.wandaMerchantBatchPrepareEntryPolling(wandaMerchantBatchPrepareEntryPollingParam);
        System.out.println("wandaMerchantBatchPrepareEntryPollingModel = " + wandaMerchantBatchPrepareEntryPollingModel);
    }
}