package com.fshows.lifecircle.storagecore.service.facade.impl;

import com.fshows.lifecircle.storagecore.common.enums.MessageTypeEnum;
import com.fshows.lifecircle.storagecore.facade.VoiceFacade;
import com.fshows.lifecircle.storagecore.facade.domain.request.VoiceFileRequest;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

import java.math.BigDecimal;
import java.util.UUID;

/**
 * fshows.com
 * Copyright (C) 2013-2020 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class VoiceFacadeImplTest {
    @Autowired
    private VoiceFacade voiceFacade;

    @Test
    public void testVoice() throws InterruptedException {

        VoiceFileRequest request = new VoiceFileRequest();
        request.setUuid(UUID.randomUUID().toString().replaceAll("-", ""));
        request.setPrice(BigDecimal.valueOf(1.87));
        request.setType(MessageTypeEnum.MEMBER_PAY.getValue());
        for (int i = 0; i < 5; i++) {
            if (i == 2) {
                request.setType(MessageTypeEnum.PAY.getValue());
            }
            voiceFacade.getVoiceFile(request);
            if (i == 2) {
                Thread.sleep(5000);
            }
        }
    }

}
