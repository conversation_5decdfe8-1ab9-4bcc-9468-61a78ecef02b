package com.fshows.lifecircle.storagecore.service.service.export.impl;

import com.fshows.lifecircle.storagecore.service.Application;
import com.fshows.lifecircle.storagecore.service.domain.param.FlowCardListExportParam;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

@SpringBootTest(classes = Application.class)
@RunWith(SpringRunner.class)
public class FlowCardListExportHandlerImplTest {

    @Autowired
    private FlowCardListExportHandlerImpl flowCardListExportHandlerImpl;

    @Test
    public void queryData() {
        FlowCardListExportParam param = new FlowCardListExportParam();
//        param.setInitSn("F521041500571PMP");
//        param.setIccid("89860477012070260072");
//        param.setStartTime(FsDateUtil.formatDate("2022-11-01", "yyyy-MM-dd"));
//        param.setEndTime(FsDateUtil.formatDate("2022-11-30", "yyyy-MM-dd"));
//        param.setAgentId(1563166);
        param.setMerchantId(32165494);
        flowCardListExportHandlerImpl.queryData(param);
    }
}