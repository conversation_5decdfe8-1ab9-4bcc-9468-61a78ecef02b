package com.fshows.lifecircle.storagecore.facade;

import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopBalanceListExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.request.hwshop.HwShopGoodsListExportRequest;
import com.fshows.lifecircle.storagecore.facade.domain.response.hwshop.HwShopGoodsListExportResponse;
import com.fshows.lifecircle.storagecore.service.Application;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * <AUTHOR>
 * @version HwShopBalanceExportFacadeTest.java, v 0.1 2021-10-12 16:59 lixingkai
 */
@RunWith(SpringRunner.class)
@SpringBootTest(classes = Application.class)
public class HwShopBalanceExportFacadeTest {

    @Autowired
    private HwShopBalanceExportFacade hwShopBalanceExportFacade;
    @Test
    public void test() {
        HwShopBalanceListExportRequest request = new HwShopBalanceListExportRequest();
        request.setUnionId("4583");
        request.setJobNumber("4583");
        request.setRealName("李兴凯");
        request.setOperateType(3);
        request.setOperatorName("测试");
        request.setStartTime("2021-10-11");
        request.setEndTime("2021-11-16");
        hwShopBalanceExportFacade.exportBalanceList(request);
    }
}