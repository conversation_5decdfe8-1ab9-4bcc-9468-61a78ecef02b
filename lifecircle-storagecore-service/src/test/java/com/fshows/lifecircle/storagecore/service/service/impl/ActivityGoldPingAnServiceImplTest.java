package com.fshows.lifecircle.storagecore.service.service.impl;

import com.alibaba.fastjson.JSONObject;
import com.fshows.lifecircle.storagecore.service.domain.model.activitygold.GoldPingAnFileDownloadModel;
import com.fshows.lifecircle.storagecore.service.domain.model.activitygold.GoldPingAnFileUploadModel;
import com.fshows.lifecircle.storagecore.service.domain.param.activitygold.GoldPingAnFileDownloadParam;
import com.fshows.lifecircle.storagecore.service.domain.param.activitygold.GoldPingAnFileUploadParam;
import com.fshows.lifecircle.storagecore.service.service.ActivityGoldPingAnService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.junit4.SpringRunner;

/**
 * fshows.com
 * Copyright (C) 2013-2022 All Rights Reserved.
 */
@SpringBootTest
@RunWith(SpringRunner.class)
public class ActivityGoldPingAnServiceImplTest {

    @Autowired
    private ActivityGoldPingAnService activityGoldPingAnService;

    @Test
    public void testFileUpload() {

        String str = "{\"expiredTime\":\"1663759697088\",\"fileName\":\"PA63070515120220921185817132660.csv\",\"list\":[{\"acctId\":\"\",\"actNm\":\"\",\"activityNumber\":\"202209062222534437003704510\",\"activityRegistrationType\":1,\"activityStatus\":2,\"activityTypeIndustryPolicy\":\"\",\"belong\":1563063,\"businessLicenseName\":\"123\",\"cardPhone\":\"***********\",\"cardSettleType\":1,\"cityName\":\"\",\"company\":\"项城市张超电动车销售店\",\"createTime\":1662474173000,\"cutChannelTime\":1662474173000,\"detailedAddress\":\"项城市张超电动车销售店\",\"everydayTrade\":0,\"id\":152,\"industry\":\"零售百货/百货商场\",\"isCardOpen\":1,\"isCutChannel\":0,\"isDel\":0,\"isSharePay\":0,\"liquidationType\":21,\"lsGoldActivityCode\":\"\",\"merchantAddress\":\"山西省长治市襄垣县\",\"merchantNo\":\"**********\",\"platformOrgId\":\"\",\"policyType\":\"平安银行上海分行-全国政策\",\"provinceName\":\"\",\"rejectInfo\":\"\",\"sharePayTime\":1662474173000,\"signProvince\":\"山西\",\"signRegion\":\"突破-东北\",\"storeHeadImg\":\"\",\"storeId\":0,\"storeIds\":\"\",\"storeInsideImg\":\"\",\"storeMoneyImg\":\"\",\"uid\":*********,\"unionCode\":\"\",\"updateTime\":1662706024000,\"username\":\"***********公立食堂\"}],\"operateType\":4,\"project\":\"THIRDORG\",\"reportType\":1,\"url\":\"https://bfiles-stg.pingan.com.cn/brcp/wefiles/cust/temp/temp_wefiles_udmp_upload.do\",\"uuid\":\"f609002d985a43bfbe9a2f4596840b12\",\"weFileToken\":\"dkaTQiylw3PUP4PSIpa57mjdXJuHyVEYbWwgvZy/5xkYLtqAaC5c6T+vdpwvAK6aV91ReYf91w8u8USlapph0HkmSlwvjdVXK+GhA/DBWU4T2po1X/HdRHFIjPyEzEom+hJxoNOlZf8zxWVedKeD6AZQsI0SrJRO2SAm28x7qEwx3I2etobf1LTFGLUdD+ieAAF64W7IbZBD2qP4UwIMdTw0UnGxckygwawVlL3WWXrNJ6QggjPvjdVupqevvWzb5jpDcjBRVJxWomvlr8ND2DstXwfsAdyJwPkpB2ZsVDixKicyUjOLK2WE6bVuoJ8J7eotpUUOmrI+CKBTqFPV9A==\"}";

        GoldPingAnFileUploadParam param = JSONObject.parseObject(str, GoldPingAnFileUploadParam.class);
        GoldPingAnFileUploadModel model = activityGoldPingAnService.fileUpload(param);
        System.out.println(model);
    }

    @Test
    public void testFileDownload() {

        String str = "{\"expiredTime\":\"1663815912727\",\"fileId\":\"F62a716b6d8734923a6b01e92ece94500\",\"operateType\":5,\"project\":\"THIRDORG\",\"reportType\":1,\"url\":\"https://bfiles-stg.pingan.com.cn/brcp/wefiles/cust/temp/temp_wefiles_udmp_download.do\",\"uuid\":\"12cca6da6bf340b3aae30b126c841c88\",\"weFileToken\":\"B9MaLbAXjVZVR44odblHiHQOQfNUHK3zoMbBcsJuvXQhVWYhPpoFGXNYug+kXdHtywSy85dEX9iQ8svtwN7zVfQ9VbfYlrE1LYhneoIl5T5qSH38eNiy4ZxTjh3KT7Gd9Kg9VcQXVfFY3TIi8IQzGTbn3G4f4zKzXhUsPAA09iEQlTSYUeF/HNutbacd6WrvngAr+M7sBSgGCXcQQ0UctHnsiSDCGYIEvlaJVirPx8AsCnJ12w2OTiGjfY0itJZcFG+3SRM3PiU0pvjUw26oRYbhi85vEBjpafxlCZUfg6MVMa16Xilymh8DDnrNmh/FM7OixHwY0MTEH+xXUqyT9A==\"}";

        GoldPingAnFileDownloadParam param = JSONObject.parseObject(str, GoldPingAnFileDownloadParam.class);
        GoldPingAnFileDownloadModel model = activityGoldPingAnService.fileDownload(param);
        System.out.println(model);
    }
}