package com.fshows.lifecircle.storagecore.service;

import cn.hutool.core.io.FileUtil;
import cn.hutool.core.util.ZipUtil;
import cn.hutool.extra.qrcode.QrCodeUtil;
import cn.hutool.extra.qrcode.QrConfig;
import cn.hutool.poi.excel.BigExcelWriter;
import cn.hutool.poi.excel.ExcelUtil;
import com.aliyun.oss.OSSClient;
import com.fshows.lifecircle.storagecore.common.enums.ProduceEnum;

import java.io.File;
import java.io.IOException;
import java.io.InputStream;
import java.net.URISyntaxException;
import java.net.URL;
import java.security.SecureRandom;
import java.text.SimpleDateFormat;
import java.util.Date;

/**
 * <AUTHOR>
 * @version Test.java, v 0.1 2019-04-28 18:06
 */
public class Test {

    @org.junit.Test
    public void testQrCodeUtil(){
        QrConfig config = new QrConfig(732, 732);
        // 设置边距，既二维码和背景之间的边距
        config.setMargin(0);
        String content = "https://youdian1.51youdian.com/Wap/Index/Member/activateCard/handleType/1/store_id/100101";
        //生成二维码
        QrCodeUtil.generate(content, config,new File("/Users/<USER>/coders/fshows/1.jpg"));
    }

    @org.junit.Test
    public void testZip(){

        String fileDir = "/Users/<USER>/temp/poser/2227";
        File zipFileUrl = ZipUtil.zip(fileDir,"/Users/<USER>/temp/poser/122271.zip");
        // 上传
        String zipFileKey = zipFileUrl.getPath();
        System.out.println(zipFileKey);
    }

    @org.junit.Test
    public void testReplaceUrl() throws URISyntaxException, IOException {
        String downloadUrl = "https://test-fshows-public.oss-cn-hangzhou.aliyuncs.com/productcenter/img/2020-07-16/dfkFsrFNcP.jpeg";
        String uri = downloadUrl.substring(downloadUrl.indexOf("//") + 2);
        uri = uri.substring(0, uri.indexOf("/"));
        String bucketName = uri.substring(0, uri.indexOf("."));
        String endpoint = "https://" + uri.substring(uri.indexOf(".") + 1);
        System.out.println(endpoint);
        System.out.println(bucketName);

//        String endpoint = "https://oss-cn-hangzhou.aliyuncs.com";
//        String bucketName = "lifecircle-ark-public";
        String accessKeyId = "LTAI4Fn1APHqdnEDpr49PjQv";
        String accessKeySecret = "******************************";
        // 获取文件的后缀名
        String suffixName = downloadUrl.substring(downloadUrl.lastIndexOf("."));
        // 生成上传文件名
        String finalFileName = System.currentTimeMillis() + "" + new SecureRandom().nextInt(0x0400) + suffixName;
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        String objectName = sdf.format(new Date()) + "/" + finalFileName;

        OSSClient ossClient = new OSSClient(endpoint, accessKeyId, accessKeySecret);
        InputStream inputStream = new URL(downloadUrl).openStream();
        ossClient.putObject(bucketName, objectName, inputStream);
        // 设置URL过期时间为10小时。
        Date expiration = new Date(System.currentTimeMillis() + 36000 * 1000);
        // 生成以GET方法访问的签名URL，访客可以直接通过浏览器访问相关内容。
        URL url = ossClient.generatePresignedUrl(bucketName, objectName, expiration);
        ossClient.shutdown();
        String replaceUrl = url.toString();
        System.out.println(replaceUrl);
        System.out.println(replaceUrl.substring(0, replaceUrl.indexOf("?")));
    }

    @org.junit.Test
    public void test1() {
        String downloadUrl = "https://lifecircle-ark-public.oss-cn-hangzhou.aliyuncs.com";
        String bucketName = downloadUrl.substring(downloadUrl.indexOf("//") + 2, downloadUrl.indexOf("."));
        String endpoint = "https://" + downloadUrl.substring(downloadUrl.indexOf(".") + 1);
        System.out.println(bucketName);
        System.out.println(endpoint);
    }

    @org.junit.Test
    public void testHash() {
        if (ProduceEnum.HUIFU_SUB_MERCHANT_REGISTER.equals(ProduceEnum.MEMBER_IMPORT_CALLBACK)) {
            System.out.println("结果一致");
        } else {
            System.out.println("结果不一致");
        }
    }


    @org.junit.Test
    public void testExport() {
        FileUtil.del("F:\\抖音核销数据.xlsx");
        BigExcelWriter writer = ExcelUtil.getBigWriter("F:\\抖音核销数据.xlsx");

    }
}
