<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.NewLifecircleBatchRegisterReportDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.NewLifecircleBatchRegisterReportDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="MCHID" property="mchid" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNAME" property="uname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_KEY" property="bankKey" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NUM" property="bankNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PUB_TO_PRI" property="pubToPri" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TRADE_TYPE" property="tradeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CURENT_DATE" property="curentDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TOTAL_TRADE_NUMS" property="totalTradeNums" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SUBSIDY" property="subsidy" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FUBEI_GAIN" property="fubeiGain" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LESHUA_GAIN" property="leshuaGain" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BALANCE_MONEY" property="balanceMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANNEL_CHARGES" property="channelCharges" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MERCHANT_CHARGES" property="merchantCharges" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_TRADE_MONEY" property="totalTradeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`MCHID`,`TOKEN`,`UNAME`,`BANK_KEY`,`BANK_NUM`,`BANK_NAME`,`ACCOUNT_NAME`,`UID`,`PUB_TO_PRI`,`TRADE_TYPE`,`CURENT_DATE`,`TOTAL_TRADE_NUMS`,`CREATE_TIME`,`UPDATE_TIME`,`SUBSIDY`,`FUBEI_GAIN`,`LESHUA_GAIN`,`BALANCE_MONEY`,`CHANNEL_CHARGES`,`MERCHANT_CHARGES`,`TOTAL_TRADE_MONEY`
    </sql>


            <!--insert:TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT-->
            <insert id="insert" >
            INSERT INTO TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="uname != null">`UNAME`,</if>
        <if test="bankKey != null">`BANK_KEY`,</if>
        <if test="bankNum != null">`BANK_NUM`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="pubToPri != null">`PUB_TO_PRI`,</if>
        <if test="tradeType != null">`TRADE_TYPE`,</if>
        <if test="curentDate != null">`CURENT_DATE`,</if>
        <if test="totalTradeNums != null">`TOTAL_TRADE_NUMS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="subsidy != null">`SUBSIDY`,</if>
        <if test="fubeiGain != null">`FUBEI_GAIN`,</if>
        <if test="leshuaGain != null">`LESHUA_GAIN`,</if>
        <if test="balanceMoney != null">`BALANCE_MONEY`,</if>
        <if test="channelCharges != null">`CHANNEL_CHARGES`,</if>
        <if test="merchantCharges != null">`MERCHANT_CHARGES`,</if>
        <if test="totalTradeMoney != null">`TOTAL_TRADE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="mchid != null">#{mchid,jdbcType=LONGVARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="uname != null">#{uname,jdbcType=VARCHAR},</if>
        <if test="bankKey != null">#{bankKey,jdbcType=VARCHAR},</if>
        <if test="bankNum != null">#{bankNum,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="pubToPri != null">#{pubToPri,jdbcType=TINYINT},</if>
        <if test="tradeType != null">#{tradeType,jdbcType=TINYINT},</if>
        <if test="curentDate != null">#{curentDate,jdbcType=INTEGER},</if>
        <if test="totalTradeNums != null">#{totalTradeNums,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="subsidy != null">#{subsidy,jdbcType=DECIMAL},</if>
        <if test="fubeiGain != null">#{fubeiGain,jdbcType=DECIMAL},</if>
        <if test="leshuaGain != null">#{leshuaGain,jdbcType=DECIMAL},</if>
        <if test="balanceMoney != null">#{balanceMoney,jdbcType=DECIMAL},</if>
        <if test="channelCharges != null">#{channelCharges,jdbcType=DECIMAL},</if>
        <if test="merchantCharges != null">#{merchantCharges,jdbcType=DECIMAL},</if>
        <if test="totalTradeMoney != null">#{totalTradeMoney,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据token和当前date查询批量登记挂账数据-->
            <select id="getByTokenAndCurrentDate" resultMap="BaseResultMap">
                    SELECT
        bank_name,bank_key
        FROM
        TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND curent_date = #{curentDate,jdbcType=INTEGER}
        LIMIT 1
            </select>

            <!--根据token和当前date查询批量登记挂账数据列表-->
            <select id="findByTokenAndCurrentDate" resultMap="BaseResultMap">
                    SELECT
        bank_name,bank_key,balance_money,subsidy,total_trade_money,merchant_charges,trade_type
        FROM
        TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND curent_date = #{curentDate,jdbcType=INTEGER}
            </select>
    </mapper>
