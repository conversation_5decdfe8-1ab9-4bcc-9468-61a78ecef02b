<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.ExceptionWithdrawDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.ExceptionWithdrawDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INFO" property="info" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_BANK" property="cardBank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FRONT_LOG_NO" property="frontLogNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRAN_TIME" property="tranTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_BANK_ID" property="bindBankId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BANK_RATE" property="bankRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CASH_AMOUNT" property="cashAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BANK_CHARGES" property="bankCharges" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INFO`,`TOKEN`,`CARD_NO`,`CARD_BANK`,`FRONT_LOG_NO`,`SERIAL_NUMBER`,`UID`,`STATUS`,`BANK_CODE`,`TRAN_TIME`,`BIND_BANK_ID`,`FINISH_TIME`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`BANK_RATE`,`CASH_AMOUNT`,`BANK_CHARGES`
    </sql>


            <!--insert:TP_EXCEPTION_WITHDRAW-->
            <insert id="insert" >
            INSERT INTO TP_EXCEPTION_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="info != null">`INFO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardBank != null">`CARD_BANK`,</if>
        <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
        <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="bankCode != null">`BANK_CODE`,</if>
        <if test="tranTime != null">`TRAN_TIME`,</if>
        <if test="bindBankId != null">`BIND_BANK_ID`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="bankRate != null">`BANK_RATE`,</if>
        <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        <if test="bankCharges != null">`BANK_CHARGES`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="info != null">#{info,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
        <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
        <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="status != null">#{status,jdbcType=INTEGER},</if>
        <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
        <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
        <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="bankRate != null">#{bankRate,jdbcType=DECIMAL},</if>
        <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        <if test="bankCharges != null">#{bankCharges,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--批量插入异常提现记录-->
            <insert id="insertBatch" >
                    INSERT INTO TP_EXCEPTION_WITHDRAW (
        token,
        uid,
        serial_number,
        cash_amount,
        status,
        finish_time,
        front_log_no,
        tran_time,
        liquidation_type,
        bind_bank_id,
        card_no,
        card_bank
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.cashAmount,jdbcType=DECIMAL},
            #{item.status,jdbcType=INTEGER},
            #{item.finishTime,jdbcType=INTEGER},
            #{item.frontLogNo,jdbcType=VARCHAR},
            #{item.tranTime,jdbcType=INTEGER},
            #{item.liquidationType,jdbcType=TINYINT},
            #{item.bindBankId,jdbcType=INTEGER},
            #{item.cardNo,jdbcType=VARCHAR},
            #{item.cardBank,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>

            <!--查询已经存在的异常提现单-->
            <select id="getExceptionWithdrawList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-EXCEPTION-WITHDRAW-GETEXCEPTIONWITHDRAWLIST*/  <include refid="Base_Column_List" /> FROM
        TP_EXCEPTION_WITHDRAW
        WHERE serial_number IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--批量插入异常提现记录-->
            <insert id="insertBatchBySxPay" >
                    INSERT INTO TP_EXCEPTION_WITHDRAW (
        token,
        uid,
        serial_number,
        cash_amount,
        status,
        front_log_no,
        tran_time,
        liquidation_type,
        bind_bank_id,
        card_no,
        card_bank
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.cashAmount,jdbcType=DECIMAL},
            #{item.status,jdbcType=INTEGER},
            #{item.frontLogNo,jdbcType=VARCHAR},
            #{item.tranTime,jdbcType=INTEGER},
            #{item.liquidationType,jdbcType=TINYINT},
            #{item.bindBankId,jdbcType=INTEGER},
            #{item.cardNo,jdbcType=VARCHAR},
            #{item.cardBank,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>
    </mapper>
