<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.UserLiquidationDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.UserLiquidationDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="STEP" property="step" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STATUS" property="status" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`ERROR_MSG`,`UID`,`STEP`,`STATUS`,`PAY_TYPE`,`USER_TYPE`,`BEGIN_TIME`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_USER_LIQUIDATION-->
    <insert id="insert">
        INSERT INTO TP_USER_LIQUIDATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="step != null">`STEP`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="step != null">#{step,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
</mapper>
