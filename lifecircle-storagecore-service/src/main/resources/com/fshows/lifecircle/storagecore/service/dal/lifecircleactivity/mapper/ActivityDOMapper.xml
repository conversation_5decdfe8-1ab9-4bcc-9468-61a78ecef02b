<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleactivity.mapper.ActivityDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircleactivity.dataobject.ActivityDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="NAME" property="name" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_TYPE" property="payType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_CODE" property="activityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SORT" property="sort" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALL_OEM" property="allOem" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ALL_AGENT" property="allAgent" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALLOW_OEM" property="allowOem" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_BRIDGE" property="isBridge" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NEED_CHECK" property="needCheck" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NEED_ENROLL" property="needEnroll" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMISSION_STATUS" property="commissionStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMISSION_END_TIME" property="commissionEndTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="COMMISSION_START_TIME" property="commissionStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`NAME`,`PAY_TYPE`,`ACTIVITY_ID`,`ACTIVITY_CODE`,`SORT`,`ALL_OEM`,`STATUS`,`END_TIME`,`ALL_AGENT`,`ALLOW_OEM`,`IS_BRIDGE`,`NEED_CHECK`,`START_TIME`,`NEED_ENROLL`,`COMMISSION_STATUS`,`COMMISSION_END_TIME`,`COMMISSION_START_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--getActivityDetailByActivityId-->
            <select id="getActivityDetailByActivityId" resultMap="BaseResultMap">
                    select /*MS-TP-ACTIVITY-GETACTIVITYDETAILBYACTIVITYID*/ <include refid="Base_Column_List" /> from tp_activity
        where activity_id = #{activityId,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
