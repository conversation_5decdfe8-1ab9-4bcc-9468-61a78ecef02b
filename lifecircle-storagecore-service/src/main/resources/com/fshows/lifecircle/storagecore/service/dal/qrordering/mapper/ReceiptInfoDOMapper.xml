<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.ReceiptInfoDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.ReceiptInfoDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QR_CODE" property="qrCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POSTER_URL" property="posterUrl" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="RECEIPT_ID" property="receiptId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_FORM" property="receiptForm" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="RECEIPT_IMAGE" property="receiptImage" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_TITLE" property="receiptTitle" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_EXPLAIN" property="receiptExplain" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_POSTER_URL" property="wechatPosterUrl" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SHOW_COUNT" property="showCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECEIPT_TYPE" property="receiptType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INITIATOR_UID" property="initiatorUid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECEIPT_STATUS" property="receiptStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="RECEIPT_MONEY" property="receiptMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QR_CODE`,`POSTER_URL`,`RECEIPT_ID`,`RECEIPT_FORM`,`RECEIPT_IMAGE`,`RECEIPT_TITLE`,`RECEIPT_EXPLAIN`,`WECHAT_POSTER_URL`,`DEL_FLAG`,`STORE_ID`,`SHOW_COUNT`,`MERCHANT_ID`,`RECEIPT_TYPE`,`INITIATOR_UID`,`RECEIPT_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`RECEIPT_MONEY`
    </sql>


            <!--insert:TP_RECEIPT_INFO-->
            <insert id="insert" >
                    INSERT INTO TP_RECEIPT_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qrCode != null">`QR_CODE`,</if>
            <if test="posterUrl != null">`POSTER_URL`,</if>
            <if test="receiptId != null">`RECEIPT_ID`,</if>
            <if test="receiptForm != null">`RECEIPT_FORM`,</if>
            <if test="receiptImage != null">`RECEIPT_IMAGE`,</if>
            <if test="receiptExplain != null">`RECEIPT_EXPLAIN`,</if>
            <if test="wechatPosterUrl != null">`WECHAT_POSTER_URL`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="showCount != null">`SHOW_COUNT`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="receiptType != null">`RECEIPT_TYPE`,</if>
            <if test="initiatorUid != null">`INITIATOR_UID`,</if>
            <if test="receiptStatus != null">`RECEIPT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="receiptMoney != null">`RECEIPT_MONEY`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qrCode != null">#{qrCode,jdbcType=VARCHAR},</if>
            <if test="posterUrl != null">#{posterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
            <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
            <if test="receiptImage != null">#{receiptImage,jdbcType=VARCHAR},</if>
            <if test="receiptExplain != null">#{receiptExplain,jdbcType=VARCHAR},</if>
            <if test="wechatPosterUrl != null">#{wechatPosterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="showCount != null">#{showCount,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="receiptType != null">#{receiptType,jdbcType=TINYINT},</if>
            <if test="initiatorUid != null">#{initiatorUid,jdbcType=INTEGER},</if>
            <if test="receiptStatus != null">#{receiptStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="receiptMoney != null">#{receiptMoney,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--收款单详情-->
            <select id="getReceiptListDetail" resultMap="BaseResultMap">
                    select /*MS-TP-RECEIPT-INFO-GETRECEIPTLISTDETAIL*/ <include refid="Base_Column_List" /> from tp_receipt_info
        where del_flag=0 and receipt_id=#{receiptId,jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据收款单id集合获取收款单详情-->
            <select id="getReceiptInfoByIds" resultMap="BaseResultMap">
                    select /*MS-TP-RECEIPT-INFO-GETRECEIPTINFOBYIDS*/ <include refid="Base_Column_List" /> from tp_receipt_info
        where del_flag=0 and receipt_id in
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
