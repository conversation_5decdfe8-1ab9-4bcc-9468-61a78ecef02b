<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopOrderStorageRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopOrderStorageRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DIRECTION" property="direction" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`HW_ORDER_SN`,`STORAGE_ORDER`,`IS_DEL`,`IS_TEST`,`DIRECTION`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_SHOP_ORDER_STORAGE_RECORD-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_ORDER_STORAGE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="direction != null">`DIRECTION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="direction != null">#{direction,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据出库单号查询订单号-->
            <select id="getByStorageOrder" resultType="java.lang.String">
                SELECT
                `HW_ORDER_SN`
                FROM
                HW_SHOP_ORDER_STORAGE_RECORD
                WHERE
                `STORAGE_ORDER` = #{storageOrder,jdbcType=VARCHAR}
                AND `IS_DEL` = 0
            </select>

            <!--批量查询订单出库单列表-->
            <select id="findStorageOrderList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
            </select>

            <!--根据出入库订单号查询订单列表-->
            <select id="findListByStorage" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        WHERE
        `storage_order` = #{storageOrder,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
            </select>

            <!--批量查询订单出库单列表-->
            <select id="findStorageOrderDOList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
            </select>
    </mapper>
