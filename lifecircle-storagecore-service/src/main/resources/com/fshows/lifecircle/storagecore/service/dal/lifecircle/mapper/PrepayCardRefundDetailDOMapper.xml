<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardRefundDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardRefundDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_NO" property="refundNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALES_ORDER_NO" property="salesOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_STATS" property="isStats" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_NUMBER" property="refundNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CARD_PRICE" property="cardPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CARD_AMOUNT" property="cardAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`REFUND_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`CARD_SPU_NAME`,`PUBLISH_ORG_ID`,`SALES_ORDER_NO`,`IS_DEL`,`IS_STATS`,`REFUND_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`CARD_PRICE`,`CARD_AMOUNT`
    </sql>


            <!--insert:TP_PREPAY_CARD_REFUND_DETAIL-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="refundNo != null">`REFUND_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--批量插入记录-->
            <insert id="insertBatch" >
                    INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            refund_no,
            sales_order_no,
            card_spu_id,
            card_spu_name,
            card_sku_id,
            card_amount,
            card_price,
            refund_number,
            org_id,
            publish_org_id
        </trim>
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.refundNo,jdbcType=VARCHAR},
                #{item.salesOrderNo,jdbcType=VARCHAR},
                #{item.cardSpuId,jdbcType=VARCHAR},
                #{item.cardSpuName,jdbcType=VARCHAR},
                #{item.cardSkuId,jdbcType=VARCHAR},
                #{item.cardAmount,jdbcType=DECIMAL},
                #{item.cardPrice,jdbcType=DECIMAL},
                #{item.refundNumber,jdbcType=INTEGER},
                #{item.orgId,jdbcType=VARCHAR},
                #{item.publishOrgId,jdbcType=VARCHAR},
            </trim>
        </foreach>
            </insert>
    </mapper>
