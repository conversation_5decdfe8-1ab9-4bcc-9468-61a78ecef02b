<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.AccountWithdrawDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.AccountWithdrawDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="FEE" property="fee" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="AMOUNT" property="amount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SPC_ID" property="spcId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE" property="source" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WALLET_ID" property="walletId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VOUCHER_NO" property="voucherNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VOUCHER_URL" property="voucherUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_NO" property="withdrawNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INNER_REASON" property="innerReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_DATE" property="withdrawDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_MODE" property="withdrawMode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_WITHDRAW_NO" property="outWithdrawNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_BRANCH_CODE" property="bankBranchCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_CODE" property="settleBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_STATUS" property="withdrawStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_ID" property="settleAccountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NO" property="settleAccountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VIRTUAL_WALLET_ID" property="virtualWalletId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_WITHDRAW_NO" property="platformWithdrawNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRACK_WITHDRAW_STATUS" property="trackWithdrawStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_REFUNDED" property="isRefunded" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATOR_TYPE" property="operatorType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FEE`,`AMOUNT`,`SP_ID`,`SPC_ID`,`REASON`,`REMARK`,`SOURCE`,`BANK_NAME`,`WALLET_ID`,`ACCOUNT_ID`,`VOUCHER_NO`,`OPERATOR_ID`,`VOUCHER_URL`,`WITHDRAW_NO`,`ACCOUNT_NAME`,`CHANNEL_CODE`,`INNER_REASON`,`WITHDRAW_DATE`,`WITHDRAW_MODE`,`OUT_WITHDRAW_NO`,`BANK_BRANCH_CODE`,`SETTLE_BANK_CODE`,`WITHDRAW_STATUS`,`SETTLE_ACCOUNT_ID`,`SETTLE_ACCOUNT_NO`,`VIRTUAL_WALLET_ID`,`PLATFORM_WITHDRAW_NO`,`TRACK_WITHDRAW_STATUS`,`DEL_FLAG`,`IS_REFUNDED`,`SETTLE_TYPE`,`OPERATOR_TYPE`,`CREATE_TIME`,`FINISH_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_ACCOUNT_WITHDRAW-->
            <insert id="insert" >
            INSERT INTO ACC_ACCOUNT_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="amount != null">`AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="reason != null">`REASON`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="walletId != null">`WALLET_ID`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="voucherNo != null">`VOUCHER_NO`,</if>
        <if test="operatorId != null">`OPERATOR_ID`,</if>
        <if test="voucherUrl != null">`VOUCHER_URL`,</if>
        <if test="withdrawNo != null">`WITHDRAW_NO`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="innerReason != null">`INNER_REASON`,</if>
        <if test="withdrawDate != null">`WITHDRAW_DATE`,</if>
        <if test="withdrawMode != null">`WITHDRAW_MODE`,</if>
        <if test="outWithdrawNo != null">`OUT_WITHDRAW_NO`,</if>
        <if test="bankBranchCode != null">`BANK_BRANCH_CODE`,</if>
        <if test="settleBankCode != null">`SETTLE_BANK_CODE`,</if>
        <if test="withdrawStatus != null">`WITHDRAW_STATUS`,</if>
        <if test="settleAccountId != null">`SETTLE_ACCOUNT_ID`,</if>
        <if test="settleAccountNo != null">`SETTLE_ACCOUNT_NO`,</if>
        <if test="virtualWalletId != null">`VIRTUAL_WALLET_ID`,</if>
        <if test="platformWithdrawNo != null">`PLATFORM_WITHDRAW_NO`,</if>
        <if test="trackWithdrawStatus != null">`TRACK_WITHDRAW_STATUS`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="isRefunded != null">`IS_REFUNDED`,</if>
        <if test="settleType != null">`SETTLE_TYPE`,</if>
        <if test="operatorType != null">`OPERATOR_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fee != null">#{fee,jdbcType=BIGINT},</if>
        <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="walletId != null">#{walletId,jdbcType=VARCHAR},</if>
        <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
        <if test="voucherNo != null">#{voucherNo,jdbcType=VARCHAR},</if>
        <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
        <if test="voucherUrl != null">#{voucherUrl,jdbcType=VARCHAR},</if>
        <if test="withdrawNo != null">#{withdrawNo,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="innerReason != null">#{innerReason,jdbcType=VARCHAR},</if>
        <if test="withdrawDate != null">#{withdrawDate,jdbcType=VARCHAR},</if>
        <if test="withdrawMode != null">#{withdrawMode,jdbcType=VARCHAR},</if>
        <if test="outWithdrawNo != null">#{outWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="bankBranchCode != null">#{bankBranchCode,jdbcType=VARCHAR},</if>
        <if test="settleBankCode != null">#{settleBankCode,jdbcType=VARCHAR},</if>
        <if test="withdrawStatus != null">#{withdrawStatus,jdbcType=VARCHAR},</if>
        <if test="settleAccountId != null">#{settleAccountId,jdbcType=VARCHAR},</if>
        <if test="settleAccountNo != null">#{settleAccountNo,jdbcType=VARCHAR},</if>
        <if test="virtualWalletId != null">#{virtualWalletId,jdbcType=VARCHAR},</if>
        <if test="platformWithdrawNo != null">#{platformWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="trackWithdrawStatus != null">#{trackWithdrawStatus,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="isRefunded != null">#{isRefunded,jdbcType=TINYINT},</if>
        <if test="settleType != null">#{settleType,jdbcType=TINYINT},</if>
        <if test="operatorType != null">#{operatorType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--更新提现单信息-->
            <update id="updateByWithdrawNo" >
                    update /*MS-ACC-ACCOUNT-WITHDRAW-UPDATEBYWITHDRAWNO*/ acc_account_withdraw
        <set>
            <if test="fee != null">fee = #{fee,jdbcType=BIGINT},</if>
            <if test="amount != null">amount = #{amount,jdbcType=BIGINT},</if>
            <if test="spId != null">sp_id = #{spId,jdbcType=VARCHAR},</if>
            <if test="spcId != null">spc_id = #{spcId,jdbcType=VARCHAR},</if>
            <if test="reason != null">reason =#{reason,jdbcType=VARCHAR},</if>
            <if test="innerReason != null">inner_reason =#{innerReason,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="withdrawStatus != null">withdraw_status = #{withdrawStatus,jdbcType=VARCHAR},</if>
            <if test="platformWithdrawNo != null">platform_withdraw_no = #{platformWithdrawNo,jdbcType=VARCHAR},</if>
            <if test="trackWithdrawStatus != null">track_withdraw_status = #{trackWithdrawStatus,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">del_flag = #{delFlag,jdbcType=TINYINT},</if>
            <if test="isRefunded != null">is_refunded = #{isRefunded,jdbcType=TINYINT},</if>
            <if test="finishTime != null">finish_time =#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="voucherUrl != null">voucher_url =#{voucherUrl,jdbcType=VARCHAR},</if>
            <if test="voucherNo != null">voucher_no =#{voucherNo,jdbcType=VARCHAR},</if>
            <if test="settleAccountNo != null">settle_account_no =#{settleAccountNo,jdbcType=VARCHAR},</if>
            <if test="bankBranchCode != null">bank_branch_code =#{bankBranchCode,jdbcType=VARCHAR},</if>
            <if test="bankName != null">bank_name =#{bankName,jdbcType=VARCHAR},</if>
            <if test="settleBankCode != null">settle_bank_code =#{settleBankCode,jdbcType=VARCHAR},</if>
        </set>
        where withdraw_no = #{withdrawNo,jdbcType=VARCHAR}
            </update>

            <!--根据提现单号查询提现单-->
            <select id="getByWithdrawNo" resultMap="BaseResultMap">
                    select /*MS-ACC-ACCOUNT-WITHDRAW-GETBYWITHDRAWNO*/ <include refid="Base_Column_List" />
        from acc_account_withdraw
        where withdraw_no = #{withdrawNo,jdbcType=VARCHAR}
        and del_flag = 1
        limit 1
            </select>
    </mapper>
