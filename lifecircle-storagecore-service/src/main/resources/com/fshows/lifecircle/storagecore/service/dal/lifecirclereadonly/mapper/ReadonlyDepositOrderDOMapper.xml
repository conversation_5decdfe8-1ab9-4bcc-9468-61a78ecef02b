<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyDepositOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyDepositOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BODY" property="body" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT7" property="ext7" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT8" property="ext8" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT9" property="ext9" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT10" property="ext10" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_ID" property="cardId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POS_FLOW_ID" property="posFlowId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_NUM" property="channelNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POS_BATCH_NO" property="posBatchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POS_AUTH_CODE" property="posAuthCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEPOSIT_ORDER_SN" property="depositOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_SN" property="platformOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFERENCE_NUMBER" property="referenceNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_DEPOSIT_ORDER_SN" property="merchantDepositOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_TYPE" property="cardType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="THAW_STATUS" property="thawStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEPOSIT_TYPE" property="depositType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_STATUS" property="tradeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PRINT_TICKET" property="isPrintTicket" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEPOSIT_TRADE_TYPE" property="depositTradeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEPOSIT_REVOKE_TIME" property="depositRevokeTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_TIME" property="tradeTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REVOKE_TIME" property="revokeTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="DEPOSIT_TIME" property="depositTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="THAW_PRICE" property="thawPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CONSUME_PRICE" property="consumePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DEPOSIT_PRICE" property="depositPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BODY`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`EXT7`,`EXT8`,`EXT9`,`EXT10`,`TOKEN`,`CARD_ID`,`REMARK`,`BANK_NAME`,`DEVICE_NO`,`SUB_MCH_ID`,`POS_FLOW_ID`,`CHANNEL_NUM`,`POS_BATCH_NO`,`POS_AUTH_CODE`,`DEPOSIT_ORDER_SN`,`MERCHANT_ORDER_SN`,`PLATFORM_ORDER_SN`,`REFERENCE_NUMBER`,`MERCHANT_DEPOSIT_ORDER_SN`,`UID`,`AGENT_ID`,`CHANNEL`,`GRANT_ID`,`PAY_TYPE`,`STORE_ID`,`CARD_TYPE`,`CASHIER_ID`,`THAW_STATUS`,`DEPOSIT_TYPE`,`ORDER_STATUS`,`SUB_CONFIG_ID`,`TRADE_STATUS`,`IS_PRINT_TICKET`,`DEPOSIT_TRADE_TYPE`,`DEPOSIT_REVOKE_TIME`,`TRADE_TIME`,`CREATE_TIME`,`REVOKE_TIME`,`UPDATE_TIME`,`DEPOSIT_TIME`,`THAW_PRICE`,`CONSUME_PRICE`,`DEPOSIT_PRICE`
    </sql>


            <!--insert:TP_DEPOSIT_ORDER-->
            <insert id="insert" >
            INSERT INTO TP_DEPOSIT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="body != null">`BODY`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="ext5 != null">`EXT5`,</if>
        <if test="ext6 != null">`EXT6`,</if>
        <if test="ext7 != null">`EXT7`,</if>
        <if test="ext8 != null">`EXT8`,</if>
        <if test="ext9 != null">`EXT9`,</if>
        <if test="ext10 != null">`EXT10`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardId != null">`CARD_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="deviceNo != null">`DEVICE_NO`,</if>
        <if test="subMchId != null">`SUB_MCH_ID`,</if>
        <if test="posFlowId != null">`POS_FLOW_ID`,</if>
        <if test="channelNum != null">`CHANNEL_NUM`,</if>
        <if test="posBatchNo != null">`POS_BATCH_NO`,</if>
        <if test="posAuthCode != null">`POS_AUTH_CODE`,</if>
        <if test="depositOrderSn != null">`DEPOSIT_ORDER_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="platformOrderSn != null">`PLATFORM_ORDER_SN`,</if>
        <if test="referenceNumber != null">`REFERENCE_NUMBER`,</if>
        <if test="merchantDepositOrderSn != null">`MERCHANT_DEPOSIT_ORDER_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="grantId != null">`GRANT_ID`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="thawStatus != null">`THAW_STATUS`,</if>
        <if test="depositType != null">`DEPOSIT_TYPE`,</if>
        <if test="orderStatus != null">`ORDER_STATUS`,</if>
        <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
        <if test="tradeStatus != null">`TRADE_STATUS`,</if>
        <if test="isPrintTicket != null">`IS_PRINT_TICKET`,</if>
        <if test="depositTradeType != null">`DEPOSIT_TRADE_TYPE`,</if>
        <if test="depositRevokeTime != null">`DEPOSIT_REVOKE_TIME`,</if>
        <if test="tradeTime != null">`TRADE_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="revokeTime != null">`REVOKE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="depositTime != null">`DEPOSIT_TIME`,</if>
        <if test="thawPrice != null">`THAW_PRICE`,</if>
        <if test="consumePrice != null">`CONSUME_PRICE`,</if>
        <if test="depositPrice != null">`DEPOSIT_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="body != null">#{body,jdbcType=VARCHAR},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
        <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
        <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
        <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
        <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
        <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
        <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
        <if test="posFlowId != null">#{posFlowId,jdbcType=VARCHAR},</if>
        <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
        <if test="posBatchNo != null">#{posBatchNo,jdbcType=VARCHAR},</if>
        <if test="posAuthCode != null">#{posAuthCode,jdbcType=VARCHAR},</if>
        <if test="depositOrderSn != null">#{depositOrderSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="platformOrderSn != null">#{platformOrderSn,jdbcType=VARCHAR},</if>
        <if test="referenceNumber != null">#{referenceNumber,jdbcType=VARCHAR},</if>
        <if test="merchantDepositOrderSn != null">#{merchantDepositOrderSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="thawStatus != null">#{thawStatus,jdbcType=TINYINT},</if>
        <if test="depositType != null">#{depositType,jdbcType=TINYINT},</if>
        <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
        <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
        <if test="tradeStatus != null">#{tradeStatus,jdbcType=TINYINT},</if>
        <if test="isPrintTicket != null">#{isPrintTicket,jdbcType=TINYINT},</if>
        <if test="depositTradeType != null">#{depositTradeType,jdbcType=TINYINT},</if>
        <if test="depositRevokeTime != null">#{depositRevokeTime,jdbcType=INTEGER},</if>
        <if test="tradeTime != null">#{tradeTime,jdbcType=TIMESTAMP},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="revokeTime != null">#{revokeTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="depositTime != null">#{depositTime,jdbcType=TIMESTAMP},</if>
        <if test="thawPrice != null">#{thawPrice,jdbcType=DECIMAL},</if>
        <if test="consumePrice != null">#{consumePrice,jdbcType=DECIMAL},</if>
        <if test="depositPrice != null">#{depositPrice,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据预授权订单号查询商户预授权订单号-->
            <select id="getMerchantDepositOrderSnListByDepositOrderSn" resultType="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantOrderSnDO">
                    select /*MS-TP-DEPOSIT-ORDER-GETMERCHANTDEPOSITORDERSNLISTBYDEPOSITORDERSN*/ deposit_order_sn as orderSn,merchant_deposit_order_sn as merchantOrderSn
        from tp_deposit_order
        where deposit_order_sn in
        <foreach collection="list" open="(" item="depositOrderSn" separator="," close=")">
            #{depositOrderSn,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
