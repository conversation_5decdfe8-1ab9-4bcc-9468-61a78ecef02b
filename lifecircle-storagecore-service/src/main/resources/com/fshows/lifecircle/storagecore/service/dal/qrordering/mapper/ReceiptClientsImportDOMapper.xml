<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.ReceiptClientsImportDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.ReceiptClientsImportDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="RECEIPT_ID" property="receiptId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CLIENT_MARKERS" property="clientMarkers" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CLIENT_MESSAGE" property="clientMessage" jdbcType="LONGVARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="RECEIVABLE_AMOUNT" property="receivableAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


        <sql id="Base_Column_List">
            `ID`,`BATCH_NO`,`RECEIPT_ID`,`CLIENT_MARKERS`,`CLIENT_MESSAGE`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`,`RECEIVABLE_AMOUNT`
        </sql>


        <!--insert:TP_RECEIPT_CLIENTS_IMPORT-->
        <insert id="insert">
            INSERT INTO TP_RECEIPT_CLIENTS_IMPORT
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="receiptId != null">`RECEIPT_ID`,</if>
                <if test="clientMarkers != null">`CLIENT_MARKERS`,</if>
                <if test="clientMessage != null">`CLIENT_MESSAGE`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="receivableAmount != null">`RECEIVABLE_AMOUNT`,</if>
            </trim>
        VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
                <if test="clientMarkers != null">#{clientMarkers,jdbcType=VARCHAR},</if>
                <if test="clientMessage != null">#{clientMessage,jdbcType=LONGVARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="receivableAmount != null">#{receivableAmount,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--根据收款单id取一条顾客导入表数据-->
        <select id="getImportDOByReceiptId" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            TP_RECEIPT_CLIENTS_IMPORT
            WHERE
            RECEIPT_ID = #{receiptId,jdbcType=VARCHAR}
            AND IS_DEL = 0
            LIMIT 1
        </select>

        <!--根据收款单id查询导入表数据-->
        <select id="findClientMarkersByReceiptId" resultType="java.lang.String">
            SELECT
            CLIENT_MARKERS
            FROM
            TP_RECEIPT_CLIENTS_IMPORT
            WHERE
            RECEIPT_ID = #{receiptId,jdbcType=VARCHAR}
            AND IS_DEL = 0
        </select>
    </mapper>
