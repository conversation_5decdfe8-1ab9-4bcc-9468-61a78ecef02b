<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.SinanActivityPolicyTypeInfoDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityPolicyTypeInfoDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="CARD_BIN" property="cardBin" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="END_TIME" property="endTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="START_TIME" property="startTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="TEMPLET_ID" property="templetId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EXPENSE_ACCOUNT" property="expenseAccount" jdbcType="VARCHAR"
            javaType="String"/>

            <result column="POLICY_TYPE_NAME" property="policyTypeName" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="LESHUA_ACTIVITY_CODE" property="leshuaActivityCode" jdbcType="VARCHAR"
            javaType="String"/>

            <result column="ACTIVITY_INDUSTRY_TYPE" property="activityIndustryType" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="TYPE" property="type" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IS_BEAR_TAX" property="isBearTax" jdbcType="TINYINT"
            javaType="Integer"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="GOLD_CARD_SETTLE_TYPE" property="goldCardSettleType" jdbcType="TINYINT"
            javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

    <result column="WX_FEE_RATE" property="wxFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="ZFB_FEE_RATE" property="zfbFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="DEBIT_FEE_RATE" property="debitFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="MAX_ALLOWANCE" property="maxAllowance" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="CREDIT_FEE_RATE" property="creditFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="ALLOWANCE_AMOUNT_MAX" property="allowanceAmountMax" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="ALLOWANCE_AMOUNT_MIN" property="allowanceAmountMin" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="CLOUD_PAY_DBASIC_FEE_RATE" property="cloudPayDbasicFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="CLOUD_PAY_DOVERFLOW_FEE_RATE" property="cloudPayDoverflowFeeRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`CARD_BIN`,`END_TIME`,`START_TIME`,`TEMPLET_ID`,`EXPENSE_ACCOUNT`,`POLICY_TYPE_NAME`,`LESHUA_ACTIVITY_CODE`,`ACTIVITY_INDUSTRY_TYPE`,`TYPE`,`IS_DEL`,`IS_BEAR_TAX`,`ACTIVITY_TYPE`,`LIQUIDATION_TYPE`,`GOLD_CARD_SETTLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`WX_FEE_RATE`,`ZFB_FEE_RATE`,`DEBIT_FEE_RATE`,`MAX_ALLOWANCE`,`CREDIT_FEE_RATE`,`ALLOWANCE_AMOUNT_MAX`,`ALLOWANCE_AMOUNT_MIN`,`CLOUD_PAY_DBASIC_FEE_RATE`,`CLOUD_PAY_DOVERFLOW_FEE_RATE`
    </sql>


            <!--getPolicyNameByPolicy-->
            <select id="getPolicyNameByPolicy" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETPOLICYNAMEBYPOLICY*/ id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
            </select>

            <!--getActivityNameByActivity-->
            <select id="getActivityNameByActivity" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETACTIVITYNAMEBYACTIVITY*/ id,activity_type,activity_industry_type from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=2
            </select>

            <!--getPolicyName-->
            <select id="getPolicyName" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETPOLICYNAME*/ id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
        and policy_type_name=#{policyTypeName,jdbcType=VARCHAR}
            </select>

            <!--getActivityName-->
            <select id="getActivityName" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETACTIVITYNAME*/ id,activity_type,activity_industry_type from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=2
        and policy_type_name=#{policyTypeName,jdbcType=VARCHAR}
            </select>

            <!--getActivityNameById-->
            <select id="getActivityNameById" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETACTIVITYNAMEBYID*/ id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
        and id=#{id,jdbcType=INTEGER}
            </select>

    <!--getActivityByIdIgnoreIsDel-->
    <select id="getActivityByIdIgnoreIsDel" resultMap="BaseResultMap">
        select /*MS-LM-SINAN-ACTIVITY-POLICY-TYPE-INFO-GETACTIVITYBYIDIGNOREISDEL*/ id,activity_type,policy_type_name
        from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO
        where
        activity_type=1
        and id=#{id,jdbcType=INTEGER}
    </select>
    </mapper>
