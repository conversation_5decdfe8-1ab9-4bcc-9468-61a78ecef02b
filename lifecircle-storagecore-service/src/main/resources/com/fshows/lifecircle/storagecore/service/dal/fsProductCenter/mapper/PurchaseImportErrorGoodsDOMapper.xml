<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.mapper.PurchaseImportErrorGoodsDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.dataobject.PurchaseImportErrorGoodsDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CODE" property="code" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GS_UID" property="gsUid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRICE" property="price" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOOD_NAME" property="goodName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GS_STORE_ID" property="gsStoreId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ERROR_MESSAGE" property="errorMessage" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="NUM" property="num" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CODE`,`GS_UID`,`PRICE`,`GOOD_NAME`,`GS_STORE_ID`,`BUSINESS_ID`,`ERROR_MESSAGE`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`,`NUM`
    </sql>


            <!--insert:GS_PURCHASE_IMPORT_ERROR_GOODS-->
            <insert id="insert" >
            INSERT INTO GS_PURCHASE_IMPORT_ERROR_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="code != null">`CODE`,</if>
        <if test="gsUid != null">`GS_UID`,</if>
        <if test="price != null">`PRICE`,</if>
        <if test="goodName != null">`GOOD_NAME`,</if>
        <if test="gsStoreId != null">`GS_STORE_ID`,</if>
        <if test="businessId != null">`BUSINESS_ID`,</if>
        <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="num != null">`NUM`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="code != null">#{code,jdbcType=VARCHAR},</if>
        <if test="gsUid != null">#{gsUid,jdbcType=VARCHAR},</if>
        <if test="price != null">#{price,jdbcType=VARCHAR},</if>
        <if test="goodName != null">#{goodName,jdbcType=VARCHAR},</if>
        <if test="gsStoreId != null">#{gsStoreId,jdbcType=VARCHAR},</if>
        <if test="businessId != null">#{businessId,jdbcType=VARCHAR},</if>
        <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="num != null">#{num,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据业务id获取对应的商品信息-->
            <select id="findGoodsListByBusinessId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM GS_PURCHASE_IMPORT_ERROR_GOODS
        WHERE BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
        AND IS_DEL = 0
            </select>
    </mapper>
