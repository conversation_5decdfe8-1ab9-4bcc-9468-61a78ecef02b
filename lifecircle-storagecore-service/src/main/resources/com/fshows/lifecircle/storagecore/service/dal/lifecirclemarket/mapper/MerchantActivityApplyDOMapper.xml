<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.MerchantActivityApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.MerchantActivityApplyDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="APPLY_NO" property="applyNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_APPLY_NO" property="outApplyNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_REBATE_REJECT_REASON" property="activityRebateRejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_OPEN_TIME" property="activityOpenTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_SIGN_TIME" property="activitySignTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_APPLY_TIME" property="activityRebateApplyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_BEGIN_MONTH" property="activityRebateBeginMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_APPLY_STATUS" property="activityRebateApplyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPLY_NO`,`COMPANY`,`SUB_MCH_ID`,`USERNAME`,`ACTIVITY_ID`,`MERCHANT_NO`,`OUT_APPLY_NO`,`REJECT_REASON`,`ACTIVITY_REBATE_REJECT_REASON`,`UID`,`IS_DEL`,`BELONG`,`CHANNEL_ID`,`ACTIVITY_STATUS`,`ACTIVITY_OPEN_TIME`,`ACTIVITY_SIGN_TIME`,`ACTIVITY_REBATE_APPLY_TIME`,`ACTIVITY_REBATE_BEGIN_MONTH`,`ACTIVITY_REBATE_APPLY_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_MERCHANT_ACTIVITY_APPLY-->
            <insert id="insert" >
                    INSERT INTO LM_MERCHANT_ACTIVITY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="applyNo != null">`APPLY_NO`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="outApplyNo != null">`OUT_APPLY_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
            <if test="activityOpenTime != null">`ACTIVITY_OPEN_TIME`,</if>
            <if test="activitySignTime != null">`ACTIVITY_SIGN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="applyNo != null">#{applyNo,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="outApplyNo != null">#{outApplyNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
            <if test="activityOpenTime != null">#{activityOpenTime,jdbcType=INTEGER},</if>
            <if test="activitySignTime != null">#{activitySignTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--getMerchantActivityApplyByApplyNo-->
            <select id="getMerchantActivityApplyByApplyNo" resultMap="BaseResultMap">
                    SELECT /*MS-LM-MERCHANT-ACTIVITY-APPLY-GETMERCHANTACTIVITYAPPLYBYAPPLYNO*/  <include refid="Base_Column_List" />
        FROM lm_merchant_activity_apply
        WHERE apply_no = #{applyNo,jdbcType=VARCHAR}
        LIMIT 1;
            </select>

            <!--getMerchantActivityApplyByUidSubMchId-->
            <select id="getMerchantActivityApplyByUidSubMchId" resultMap="BaseResultMap">
                    SELECT /*MS-LM-MERCHANT-ACTIVITY-APPLY-GETMERCHANTACTIVITYAPPLYBYUIDSUBMCHID*/  <include refid="Base_Column_List" />
        FROM lm_merchant_activity_apply
        WHERE uid = #{uid,jdbcType=INTEGER}
        and sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        and activity_id IN
        <foreach close=")" collection="activityIdList" index="index" item="activityId" open="(" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        LIMIT 1;
            </select>

            <!--updateApplyById-->
            <update id="updateApplyById" >
                    update /*MS-LM-MERCHANT-ACTIVITY-APPLY-UPDATEAPPLYBYID*/ LM_MERCHANT_ACTIVITY_APPLY
        set update_time = now()
        <if test="outApplyNo != null">
            ,OUT_APPLY_NO = #{outApplyNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            ,ACTIVITY_STATUS = #{activityStatus,jdbcType=TINYINT}
        </if>
        <if test="activitySignTime != null">
            ,ACTIVITY_SIGN_TIME = #{activitySignTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenTime != null">
            ,ACTIVITY_OPEN_TIME = #{activityOpenTime,jdbcType=INTEGER}
        </if>
        <if test="rejectReason != null">
            ,REJECT_REASON = #{rejectReason,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=BIGINT}
            </update>

            <!--findMerchantActivityApply-->
            <select id="findMerchantActivityApply" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.MarketActivityListDTO">
                    SELECT /*MS-LM-MERCHANT-ACTIVITY-APPLY-FINDMERCHANTACTIVITYAPPLY*/  <include refid="Base_Column_List" />
        FROM lm_merchant_activity_apply
        WHERE is_del = 0
        <if test="uid != null">
            AND UID = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null and username !='' ">
            AND USERNAME LIKE CONCAT( #{username,jdbcType=VARCHAR}, '%' )
        </if>
        <if test="subMchId != null and subMchId != ''">
            AND sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="merchantNo != null and merchantNo != ''">
            AND merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            AND activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="submitStartTime != null and submitEndTime != null">
            AND activity_sign_time BETWEEN #{submitStartTime,jdbcType=INTEGER} and #{submitEndTime,jdbcType=INTEGER}
        </if>
        ORDER BY activity_sign_time DESC
            </select>

            <!--市场活动报名列表 pageCount-->
            <select id="findMarketActivityCrmListPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 lm_merchant_activity_apply apply
        LEFT JOIN lm_merchant_activity_apply_ext ext ON apply.apply_no = ext.apply_no
        WHERE apply.is_del= 0
        AND apply.activity_id IN
        <foreach close=")" collection="activityIdList" index="index" item="activityId" open="(" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null">
            AND apply.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="subMchId != null">
            AND apply.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="merchantNo != null">
            AND apply.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            AND apply.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activitySignStartTime != null">
            AND apply.activity_sign_time <![CDATA[ >= ]]> #{activitySignStartTime,jdbcType=INTEGER}
        </if>
        <if test="activitySignEndTime != null">
            AND apply.activity_sign_time <![CDATA[ <= ]]> #{activitySignEndTime,jdbcType=INTEGER}
        </if>
            </select>
            <!--市场活动报名列表 pageResult-->
            <select id="findMarketActivityCrmListPageResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.MarketActivityApplyListDTO">
                    SELECT
        apply.id,
        apply.activity_sign_time activitySignTime,
        apply.activity_open_time activityOpenTime,
        apply.uid,
        apply.username,
        apply.company,
        apply.sub_mch_id subMchId,
        apply.merchant_no merchantNo,
        apply.activity_status activityStatus,
        apply.activity_id activityId,
        apply.reject_reason rejectReason,
        ext.merchant_attribute merchantAttribute,
        ext.apply_info_attribute applyInfoAttribute
        FROM lm_merchant_activity_apply apply
        LEFT JOIN lm_merchant_activity_apply_ext ext ON apply.apply_no = ext.apply_no
        WHERE apply.is_del= 0
        AND apply.activity_id IN
        <foreach close=")" collection="activityIdList" index="index" item="activityId" open="(" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null">
            AND apply.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="subMchId != null">
            AND apply.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="merchantNo != null">
            AND apply.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            AND apply.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activitySignStartTime != null">
            AND apply.activity_sign_time <![CDATA[ >= ]]> #{activitySignStartTime,jdbcType=INTEGER}
        </if>
        <if test="activitySignEndTime != null">
            AND apply.activity_sign_time <![CDATA[ <= ]]> #{activitySignEndTime,jdbcType=INTEGER}
        </if>
            limit #{startRow},#{limit}
            </select>

            <!--updateRebateApplyInfoById-->
            <update id="updateRebateApplyInfoById" >
                    update /*MS-LM-MERCHANT-ACTIVITY-APPLY-UPDATEREBATEAPPLYINFOBYID*/ LM_MERCHANT_ACTIVITY_APPLY
        set update_time = now()
        <if test="activityRebateApplyStatus != null">
            ,ACTIVITY_REBATE_APPLY_STATUS = #{activityRebateApplyStatus,jdbcType=TINYINT}
        </if>
        <if test="activityRebateApplyTime != null">
            ,ACTIVITY_REBATE_APPLY_TIME = #{activityRebateApplyTime,jdbcType=INTEGER}
        </if>
        <if test="activityRebateBeginMonth != null">
            ,ACTIVITY_REBATE_BEGIN_MONTH = #{activityRebateBeginMonth,jdbcType=INTEGER}
        </if>
        <if test="activityRebateRejectReason != null">
            ,ACTIVITY_REBATE_REJECT_REASON = #{activityRebateRejectReason,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=BIGINT}
            </update>
    </mapper>
