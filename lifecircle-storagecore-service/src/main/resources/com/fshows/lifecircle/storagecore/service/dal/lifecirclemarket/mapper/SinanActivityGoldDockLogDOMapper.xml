<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.SinanActivityGoldDockLogDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityGoldDockLogDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="URL" property="url" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OTHER_MSG" property="otherMsg" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="DEAL_DATE" property="dealDate" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="REPORT_TYPE" property="reportType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="TARGET_TYPE" property="targetType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`URL`,`OTHER_MSG`,`IS_DEL`,`DEAL_DATE`,`REPORT_TYPE`,`TARGET_TYPE`,`OPERATE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_SINAN_ACTIVITY_GOLD_DOCK_LOG-->
    <insert id="insert">
        INSERT INTO LM_SINAN_ACTIVITY_GOLD_DOCK_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="url != null">`URL`,</if>
            <if test="otherMsg != null">`OTHER_MSG`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="dealDate != null">`DEAL_DATE`,</if>
            <if test="reportType != null">`REPORT_TYPE`,</if>
            <if test="targetType != null">`TARGET_TYPE`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="url != null">#{url,jdbcType=VARCHAR},</if>
            <if test="otherMsg != null">#{otherMsg,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="dealDate != null">#{dealDate,jdbcType=INTEGER},</if>
            <if test="reportType != null">#{reportType,jdbcType=TINYINT},</if>
            <if test="targetType != null">#{targetType,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
</mapper>
