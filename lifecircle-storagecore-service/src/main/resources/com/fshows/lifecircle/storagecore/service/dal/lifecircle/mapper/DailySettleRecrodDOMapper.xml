<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.DailySettleRecrodDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.DailySettleRecrodDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="MNO" property="mno" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TAG" property="tag" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_DT" property="settleDt" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REF_REASON" property="refReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_DATE" property="tradeDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_TIME" property="settleTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BNK_NO" property="settleBnkNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_NM" property="settleBankNm" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_NO" property="settleBankNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_STATUS" property="settleStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGIN_ORDER_SN" property="originOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REAL_BANK_HOLDER" property="realBankHolder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REL_BANK_ACCOUNT" property="relBankAccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NM" property="settleAccountNm" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_TYPE" property="settleAccountType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BILL_FLAG" property="billFlag" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECREATE_FLAG" property="recreateFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AMOUNT" property="amount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PLAN_SETTLE_AMOUNT" property="planSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REAL_SETTLE_AMOUNT" property="realSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SUSPEND_SETTLE_AMOUNT" property="suspendSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`MNO`,`TAG`,`ORG_ID`,`SERIAL_NO`,`SETTLE_DT`,`REF_REASON`,`TRADE_DATE`,`SETTLE_TIME`,`SETTLE_BNK_NO`,`SETTLE_BANK_NM`,`SETTLE_BANK_NO`,`SETTLE_STATUS`,`ORIGIN_ORDER_SN`,`REAL_BANK_HOLDER`,`REL_BANK_ACCOUNT`,`SETTLE_ACCOUNT_NM`,`SETTLE_ACCOUNT_TYPE`,`CHANNEL`,`BILL_FLAG`,`RECREATE_FLAG`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`AMOUNT`,`PLAN_SETTLE_AMOUNT`,`REAL_SETTLE_AMOUNT`,`SUSPEND_SETTLE_AMOUNT`
    </sql>


            <!--insert:TP_DAILY_SETTLE_RECROD-->
            <insert id="insert" >
                    INSERT INTO TP_DAILY_SETTLE_RECROD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mno != null">`MNO`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="serialNo != null">`SERIAL_NO`,</if>
            <if test="settleDt != null">`SETTLE_DT`,</if>
            <if test="refReason != null">`REF_REASON`,</if>
            <if test="settleTime != null">`SETTLE_TIME`,</if>
            <if test="settleBnkNo != null">`SETTLE_BNK_NO`,</if>
            <if test="settleBankNm != null">`SETTLE_BANK_NM`,</if>
            <if test="settleBankNo != null">`SETTLE_BANK_NO`,</if>
            <if test="settleStatus != null">`SETTLE_STATUS`,</if>
            <if test="settleAccountNm != null">`SETTLE_ACCOUNT_NM`,</if>
            <if test="settleAccountType != null">`SETTLE_ACCOUNT_TYPE`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="amount != null">`AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="mno != null">#{mno,jdbcType=VARCHAR},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="settleDt != null">#{settleDt,jdbcType=VARCHAR},</if>
            <if test="refReason != null">#{refReason,jdbcType=VARCHAR},</if>
            <if test="settleTime != null">#{settleTime,jdbcType=VARCHAR},</if>
            <if test="settleBnkNo != null">#{settleBnkNo,jdbcType=VARCHAR},</if>
            <if test="settleBankNm != null">#{settleBankNm,jdbcType=VARCHAR},</if>
            <if test="settleBankNo != null">#{settleBankNo,jdbcType=VARCHAR},</if>
            <if test="settleStatus != null">#{settleStatus,jdbcType=VARCHAR},</if>
            <if test="settleAccountNm != null">#{settleAccountNm,jdbcType=VARCHAR},</if>
            <if test="settleAccountType != null">#{settleAccountType,jdbcType=VARCHAR},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--批量插入记录-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    replace INTO TP_DAILY_SETTLE_RECROD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `MNO`,
            `ORG_ID`,
            `SERIAL_NO`,
            `SETTLE_DT`,
            `REF_REASON`,
            `SETTLE_TIME`,
            `SETTLE_BNK_NO`,
            `SETTLE_BANK_NM`,
            `SETTLE_BANK_NO`,
            `SETTLE_STATUS`,
            `SETTLE_ACCOUNT_NM`,
            `SETTLE_ACCOUNT_TYPE`,
            `CHANNEL`,
            `FEE`,
            `AMOUNT`
        </trim>
        VALUES
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{record.mno,jdbcType=VARCHAR},
                #{record.orgId,jdbcType=VARCHAR},
                #{record.serialNo,jdbcType=VARCHAR},
                #{record.settleDt,jdbcType=VARCHAR},
                #{record.refReason,jdbcType=VARCHAR},
                #{record.settleTime,jdbcType=VARCHAR},
                #{record.settleBnkNo,jdbcType=VARCHAR},
                #{record.settleBankNm,jdbcType=VARCHAR},
                #{record.settleBankNo,jdbcType=VARCHAR},
                #{record.settleStatus,jdbcType=VARCHAR},
                #{record.settleAccountNm,jdbcType=VARCHAR},
                #{record.settleAccountType,jdbcType=VARCHAR},
                #{record.channel,jdbcType=TINYINT},
                #{record.fee,jdbcType=DECIMAL},
                #{record.amount,jdbcType=DECIMAL},
            </trim>
        </foreach>
            </select>
    </mapper>
