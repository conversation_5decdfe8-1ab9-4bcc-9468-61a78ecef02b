<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CrmBankcardSettleDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CrmBankcardSettleDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TERM_LIST" property="termList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALLINPAY_MCH_ID" property="allinpayMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_CARD_SETTLE_ID" property="bankCardSettleId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALLINPAY_SETTLE_STATUS" property="allinpaySettleStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALLINPAY_SETTLE_FAIL_MSG" property="allinpaySettleFailMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALLINPAY_PROTOCOL_STATUS" property="allinpayProtocolStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_STATUS" property="settleStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PROTOCOL_STATUS" property="protocolStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALLINPAY_SETTLE_CALLBACK_STATUS" property="allinpaySettleCallbackStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALLINPAY_PROTOCOL_CALLBACK_STATUS" property="allinpayProtocolCallbackStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SETTLE_TIME" property="settleTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PROTOCOL_TIME" property="protocolTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SETTLE_STATUS_UPDATE_TIME" property="settleStatusUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="ALLINPAY_SETTLE_CALLBACK_TIME" property="allinpaySettleCallbackTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="ALLINPAY_PROTOCOL_CALLBACK_TIME" property="allinpayProtocolCallbackTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARKS`,`CREATE_BY`,`TERM_LIST`,`UPDATE_BY`,`ALLINPAY_MCH_ID`,`BANK_CARD_SETTLE_ID`,`ALLINPAY_SETTLE_STATUS`,`ALLINPAY_SETTLE_FAIL_MSG`,`ALLINPAY_PROTOCOL_STATUS`,`MERCHANT_ID`,`SETTLE_STATUS`,`PROTOCOL_STATUS`,`ALLINPAY_SETTLE_CALLBACK_STATUS`,`ALLINPAY_PROTOCOL_CALLBACK_STATUS`,`CREATE_TIME`,`SETTLE_TIME`,`UPDATE_TIME`,`PROTOCOL_TIME`,`SETTLE_STATUS_UPDATE_TIME`,`ALLINPAY_SETTLE_CALLBACK_TIME`,`ALLINPAY_PROTOCOL_CALLBACK_TIME`
    </sql>


            <!--insert:TP_CRM_BANKCARD_SETTLE-->
            <insert id="insert" >
                    INSERT INTO TP_CRM_BANKCARD_SETTLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="termList != null">`TERM_LIST`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="allinpayMchId != null">`ALLINPAY_MCH_ID`,</if>
            <if test="bankCardSettleId != null">`BANK_CARD_SETTLE_ID`,</if>
            <if test="allinpaySettleStatus != null">`ALLINPAY_SETTLE_STATUS`,</if>
            <if test="allinpaySettleFailMsg != null">`ALLINPAY_SETTLE_FAIL_MSG`,</if>
            <if test="allinpayProtocolStatus != null">`ALLINPAY_PROTOCOL_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="settleStatus != null">`SETTLE_STATUS`,</if>
            <if test="protocolStatus != null">`PROTOCOL_STATUS`,</if>
            <if test="allinpaySettleCallbackStatus != null">`ALLINPAY_SETTLE_CALLBACK_STATUS`,</if>
            <if test="allinpayProtocolCallbackStatus != null">`ALLINPAY_PROTOCOL_CALLBACK_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="settleTime != null">`SETTLE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="protocolTime != null">`PROTOCOL_TIME`,</if>
            <if test="settleStatusUpdateTime != null">`SETTLE_STATUS_UPDATE_TIME`,</if>
            <if test="allinpaySettleCallbackTime != null">`ALLINPAY_SETTLE_CALLBACK_TIME`,</if>
            <if test="allinpayProtocolCallbackTime != null">`ALLINPAY_PROTOCOL_CALLBACK_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="termList != null">#{termList,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="allinpayMchId != null">#{allinpayMchId,jdbcType=VARCHAR},</if>
            <if test="bankCardSettleId != null">#{bankCardSettleId,jdbcType=VARCHAR},</if>
            <if test="allinpaySettleStatus != null">#{allinpaySettleStatus,jdbcType=VARCHAR},</if>
            <if test="allinpaySettleFailMsg != null">#{allinpaySettleFailMsg,jdbcType=VARCHAR},</if>
            <if test="allinpayProtocolStatus != null">#{allinpayProtocolStatus,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="settleStatus != null">#{settleStatus,jdbcType=TINYINT},</if>
            <if test="protocolStatus != null">#{protocolStatus,jdbcType=TINYINT},</if>
            <if test="allinpaySettleCallbackStatus != null">#{allinpaySettleCallbackStatus,jdbcType=TINYINT},</if>
            <if test="allinpayProtocolCallbackStatus != null">#{allinpayProtocolCallbackStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="settleTime != null">#{settleTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="protocolTime != null">#{protocolTime,jdbcType=TIMESTAMP},</if>
            <if test="settleStatusUpdateTime != null">#{settleStatusUpdateTime,jdbcType=TIMESTAMP},</if>
            <if test="allinpaySettleCallbackTime != null">#{allinpaySettleCallbackTime,jdbcType=TIMESTAMP},</if>
            <if test="allinpayProtocolCallbackTime != null">#{allinpayProtocolCallbackTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据通联商户号查询商户id-->
            <select id="getByAllinpay_mch_id" resultType="Integer">
                    select /*MS-TP-CRM-BANKCARD-SETTLE-GETBYALLINPAY-MCH-ID*/ merchant_id from tp_crm_bankcard_settle where allinpay_mch_id = #{allinpayMchId,jdbcType=VARCHAR} limit 1
            </select>
    </mapper>
