<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtraUnionUserStoreDOMapper">
    
    <!-- 获取出来当前司南上代理商所管理的未隐藏的商户和在线的门店列表 -->
    <select id="getAgentOnlineStoreAndShowUsers"
            parameterType="com.fshows.lifecircle.storagecore.service.domain.param.GetOnlineStoreAndShowUsersParam"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.UnionUserNewStoreDO">
        select
        tpusers.id as uid,
        tpusers.belong as ubelong,
        tpusers.salesman as usalesman,
        tpusers.market_id as umarketId,
        tpuser.id as belongId,
        tpuser.username as belongName,
        tpstore.store_id as storeId,
        tpstore.store_name as storeName,
        tpstore.tel as storeMobile,
        tpaccount.type as accountType,
        tpaccount.account_name as accountName,
        tpaccount.legal_person as legalName
        from tp_users tpusers
        left join tp_user tpuser on tpuser.id = tpusers.salesman
        left join tp_user_right_control tpuserright on tpuserright.key_id = tpusers.id
        left join tp_lifecircle_account tpaccount on tpaccount.uid = tpusers.id
        left join tp_wxuser tpwxuser on tpusers.id = tpwxuser.uid
        left join tp_lifecircle_store tpstore on tpwxuser.token = tpstore.token
        <where>
            tpusers.belong = #{belong, jdbcType=INTEGER}
            and tpuserright.is_show_list = 1
            and tpstore.create_time <![CDATA[ < ]]> #{createTime,jdbcType=BIGINT}
            and tpstore.is_online != 0
            <if test="storeNameLike !=null and storeNameLike != ''">
                and tpstore.store_name LIKE CONCAT('%',#{storeNameLike,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>


    <!-- 获取出来当前司南上受理商所管理的未隐藏的商户和在线的门店列表 -->
    <select id="getSalesmanOnlineStoreAndShowUsers"
            parameterType="com.fshows.lifecircle.storagecore.service.domain.param.GetOnlineStoreAndShowUsersParam"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.UnionUserNewStoreDO">
        select
        tpusers.id as uid,
        tpusers.belong as ubelong,
        tpusers.salesman as usalesman,
        tpusers.market_id as umarketId,
        tpuser.id as belongId,
        tpuser.username as belongName,
        tpstore.store_id as storeId,
        tpstore.store_name as storeName,
        tpstore.tel as storeMobile,
        tpaccount.type as accountType,
        tpaccount.account_name as accountName,
        tpaccount.legal_person as legalName
        from tp_users tpusers
        left join tp_user tpuser on tpuser.id = tpusers.market_id
        left join tp_user_right_control tpuserright on tpuserright.key_id = tpusers.id
        left join tp_lifecircle_account tpaccount on tpaccount.uid = tpusers.id
        left join tp_wxuser tpwxuser on tpusers.id = tpwxuser.uid
        left join tp_lifecircle_store tpstore on tpwxuser.token = tpstore.token
        <where>
            tpusers.salesman = #{salesman, jdbcType=INTEGER}
            and tpuserright.is_show_list = 1
            and tpstore.create_time <![CDATA[ < ]]> #{createTime,jdbcType=BIGINT}
            and tpstore.is_online != 0
            <if test="storeNameLike !=null and storeNameLike != ''">
                and tpstore.store_name LIKE CONCAT('%',#{storeNameLike,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>

    <!-- 获取出来当前司南上市场经理所管理的未隐藏的商户和在线的门店列表 -->
    <select id="getMarketOnlineStoreAndShowUsers"
            parameterType="com.fshows.lifecircle.storagecore.service.domain.param.GetOnlineStoreAndShowUsersParam"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.UnionUserNewStoreDO">
        select
        tpusers.id as uid,
        tpusers.belong as ubelong,
        tpusers.salesman as usalesman,
        tpusers.market_id as umarketId,
        tpuser.id as belongId,
        tpuser.username as belongName,
        tpstore.store_id as storeId,
        tpstore.store_name as storeName,
        tpstore.tel as storeMobile,
        tpaccount.type as accountType,
        tpaccount.account_name as accountName,
        tpaccount.legal_person as legalName
        from tp_users tpusers
        left join tp_user tpuser on tpuser.id = tpusers.market_id
        left join tp_user_right_control tpuserright on tpuserright.key_id = tpusers.id
        left join tp_lifecircle_account tpaccount on tpaccount.uid = tpusers.id
        left join tp_wxuser tpwxuser on tpusers.id = tpwxuser.uid
        left join tp_lifecircle_store tpstore on tpwxuser.token = tpstore.token
        <where>
            tpusers.market_id = #{marketId, jdbcType=INTEGER}
            and tpuserright.is_show_list = 1
            and tpstore.create_time <![CDATA[ < ]]> #{createTime,jdbcType=BIGINT}
            and tpstore.is_online != 0
            <if test="storeNameLike !=null and storeNameLike != ''">
                and tpstore.store_name LIKE CONCAT('%',#{storeNameLike,jdbcType=VARCHAR},'%')
            </if>
        </where>
    </select>
</mapper>
