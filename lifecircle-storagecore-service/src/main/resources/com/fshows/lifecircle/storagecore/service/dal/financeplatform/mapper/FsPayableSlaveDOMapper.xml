<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.FsPayableSlaveDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.FsPayableSlaveDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="ERP_SYNC_TIMESTAMP" property="erpSyncTimestamp" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PAYABLE_SUB_NUM" property="payableSubNum" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PAYABLE_BILL_NUM" property="payableBillNum" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="DEDUCT_DETAIL_URL" property="deductDetailUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="NORMAL_DETAIL_URL" property="normalDetailUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BILL_DATE" property="billDate" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="BILL_STATUS" property="billStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ERP_SYNC_STATUS" property="erpSyncStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="BUSINESS_STATUS" property="businessStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="EXCEL_SYNC_STATUS" property="excelSyncStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ERP_SYNC_FAIL_TIMES" property="erpSyncFailTimes" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="EXCEL_SYNC_FAIL_TIMES" property="excelSyncFailTimes" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="BILL_AMOUNT" property="billAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="LOCK_AMOUNT" property="lockAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="PAID_AMOUNT" property="paidAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="SYNC_ERP_AMOUNT" property="syncErpAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="PAYABLE_COMMISSION" property="payableCommission" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="DEDUCTION_COMMISSION" property="deductionCommission" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>

    <resultMap id="CommissionPayableInfoMap"
               type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.resultmap.CommissionPayableInfoMap">

        <result column="fee_code" property="feeCode" javaType="java.lang.String"/>

        <result column="payable_sub_num" property="payableSubNum" javaType="java.lang.String"/>

        <result column="fee_project_name" property="feeProjectName" javaType="java.lang.String"/>

        <result column="payable_bill_num" property="payableBillNum" javaType="java.lang.String"/>

        <result column="payment_object_id" property="paymentObjectId" javaType="java.lang.String"/>

        <result column="bill_date" property="billDate" javaType="java.lang.Integer"/>

        <result column="is_details" property="isDetails" javaType="java.lang.Integer"/>

        <result column="is_t1_settle" property="isT1Settle" javaType="java.lang.Integer"/>

        <result column="business_status" property="businessStatus" javaType="java.lang.Integer"/>

        <result column="normal_trade_amount" property="normalTradeAmount" javaType="java.math.BigDecimal"/>

        <result column="payable_commission" property="payableCommission" javaType="java.math.BigDecimal"/>

        <result column="refund_trade_amount" property="refundTradeAmount" javaType="java.math.BigDecimal"/>

        <result column="deduction_commission" property="deductionCommission" javaType="java.math.BigDecimal"/>

        <result column="settlement_coefficient" property="settlementCoefficient" javaType="java.math.BigDecimal"/>

        <result column="actual_payable_commission" property="actualPayableCommission" javaType="java.math.BigDecimal"/>
    </resultMap>

    <sql id="Base_Column_List">
        `ID`
        ,`ERP_SYNC_TIMESTAMP`,`FEE_CODE`,`PAYABLE_SUB_NUM`,`PAYABLE_BILL_NUM`,`DEDUCT_DETAIL_URL`,`NORMAL_DETAIL_URL`,`BILL_DATE`,`BILL_STATUS`,`ERP_SYNC_STATUS`,`BUSINESS_STATUS`,`EXCEL_SYNC_STATUS`,`ERP_SYNC_FAIL_TIMES`,`EXCEL_SYNC_FAIL_TIMES`,`CREATE_TIME`,`UPDATE_TIME`,`BILL_AMOUNT`,`LOCK_AMOUNT`,`PAID_AMOUNT`,`SYNC_ERP_AMOUNT`,`NORMAL_TRADE_AMOUNT`,`PAYABLE_COMMISSION`,`REFUND_TRADE_AMOUNT`,`DEDUCTION_COMMISSION`
    </sql>


    <!--insert:FS_PAYABLE_SLAVE-->
    <insert id="insert">
        INSERT INTO FS_PAYABLE_SLAVE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="erpSyncTimestamp != null">`ERP_SYNC_TIMESTAMP`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="payableSubNum != null">`PAYABLE_SUB_NUM`,</if>
            <if test="payableBillNum != null">`PAYABLE_BILL_NUM`,</if>
            <if test="deductDetailUrl != null">`DEDUCT_DETAIL_URL`,</if>
            <if test="normalDetailUrl != null">`NORMAL_DETAIL_URL`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="billStatus != null">`BILL_STATUS`,</if>
            <if test="erpSyncStatus != null">`ERP_SYNC_STATUS`,</if>
            <if test="businessStatus != null">`BUSINESS_STATUS`,</if>
            <if test="excelSyncStatus != null">`EXCEL_SYNC_STATUS`,</if>
            <if test="erpSyncFailTimes != null">`ERP_SYNC_FAIL_TIMES`,</if>
            <if test="excelSyncFailTimes != null">`EXCEL_SYNC_FAIL_TIMES`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="billAmount != null">`BILL_AMOUNT`,</if>
            <if test="lockAmount != null">`LOCK_AMOUNT`,</if>
            <if test="paidAmount != null">`PAID_AMOUNT`,</if>
            <if test="syncErpAmount != null">`SYNC_ERP_AMOUNT`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="erpSyncTimestamp != null">#{erpSyncTimestamp,jdbcType=BIGINT},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="payableSubNum != null">#{payableSubNum,jdbcType=VARCHAR},</if>
            <if test="payableBillNum != null">#{payableBillNum,jdbcType=VARCHAR},</if>
            <if test="deductDetailUrl != null">#{deductDetailUrl,jdbcType=VARCHAR},</if>
            <if test="normalDetailUrl != null">#{normalDetailUrl,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=INTEGER},</if>
            <if test="billStatus != null">#{billStatus,jdbcType=TINYINT},</if>
            <if test="erpSyncStatus != null">#{erpSyncStatus,jdbcType=TINYINT},</if>
            <if test="businessStatus != null">#{businessStatus,jdbcType=TINYINT},</if>
            <if test="excelSyncStatus != null">#{excelSyncStatus,jdbcType=TINYINT},</if>
            <if test="erpSyncFailTimes != null">#{erpSyncFailTimes,jdbcType=TINYINT},</if>
            <if test="excelSyncFailTimes != null">#{excelSyncFailTimes,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="billAmount != null">#{billAmount,jdbcType=DECIMAL},</if>
            <if test="lockAmount != null">#{lockAmount,jdbcType=DECIMAL},</if>
            <if test="paidAmount != null">#{paidAmount,jdbcType=DECIMAL},</if>
            <if test="syncErpAmount != null">#{syncErpAmount,jdbcType=DECIMAL},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--查询代理商佣金应付信息-->
    <select id="getCommissionPayableInfo" resultMap="CommissionPayableInfoMap">
        select /*MS-FS-PAYABLE-SLAVE-GETCOMMISSIONPAYABLEINFO*/
        a.payable_sub_num,b.payment_object_id,a.bill_date,b.settlement_coefficient,a.normal_trade_amount,
        a.refund_trade_amount,a.bill_amount
        actual_payable_commission,a.payable_commission,a.deduction_commission,a.fee_code,b.business_num,fp.is_t1_settle
        from fs_payable_slave a
        JOIN fs_payable_master b on a.payable_bill_num = b.payable_bill_num
        JOIN fp_agent_commission_month fp on fp.business_num=b.business_num
        where a.payable_sub_num = #{payableSubNum,jdbcType=VARCHAR}
        and b.payment_object_id = #{agentId,jdbcType=VARCHAR}
        and b.bill_type = 1
        and b.source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        and b.payment_object_type = 1 and b.sync_bill_flag = 1
    </select>
</mapper>
