<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmChannelPolicySettlementGroupMemberDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmChannelPolicySettlementGroupMemberDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GROUP_ID`,`IS_DEL`,`USER_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--insertBatch-->
            <insert id="insertBatch" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER(GROUP_ID,USER_ID) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.userId,jdbcType=INTEGER}
            )
        </foreach>
            </insert>

            <!--查询结算组成员列表-->
            <select id="findSettlementGroupMemberList" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.SettlementGroupMemberListDTO">
                    select
        t1.user_id as userId,
        t2.username,
        t1.create_time as createTime,
        t2.companyname as company
        from lm_crm_channel_policy_settlement_group_member t1
        left join tp_user t2 on t1.user_id = t2.id
        where t1.group_id = #{groupId,jdbcType=VARCHAR}
        <if test="userId != null">
            and t1.user_id = #{userId,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            and t2.username = #{username,jdbcType=VARCHAR}
        </if>
        and t1.is_del = 0
            </select>
    </mapper>
