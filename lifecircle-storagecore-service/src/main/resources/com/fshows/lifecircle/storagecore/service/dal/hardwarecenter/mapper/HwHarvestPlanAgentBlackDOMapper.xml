<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwHarvestPlanAgentBlackDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwHarvestPlanAgentBlackDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="REMARK" property="remark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_CLOSE" property="isClose" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ACTIVITY_ID" property="activityId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`REMARK`,`JOB_NUMBER`,`OPERATOR_NAME`,`AGENT_ID`,`IS_CLOSE`,`ACTIVITY_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:HW_HARVEST_PLAN_AGENT_WHITE-->
    <insert id="insert">
        INSERT INTO HW_HARVEST_PLAN_AGENT_BLACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                agent_id,
            </if>
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="isClose != null">
                is_close,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="jobNumber != null">
                job_number,
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name,
            </if>
            <if test="remark != null and remark != ''">
                remark
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                #{agentId, jdbcType=INTEGER},
            </if>
            <if test="activityId != null">
                #{activityId, jdbcType=INTEGER},
            </if>
            <if test="isClose != null">
                #{isClose, jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime, jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=TIMESTAMP},
            </if>
            <if test="jobNumber != null">
                #{jobNumber, jdbcType=INTEGER},
            </if>
            <if test="operatorName != null and operatorName != ''">
                #{operatorName, jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </insert>

    <!--根据agentId和activityId查询-->
    <select id="getOneByAgentIdAndActivityId" resultMap="BaseResultMap">
        select /*MS-HW-HARVEST-PLAN-AGENT-BLACK-GETONEBYAGENTIDANDACTIVITYID*/
        <include refid="Base_Column_List"/>
        from HW_HARVEST_PLAN_AGENT_BLACK
        where AGENT_ID = #{agentId, jdbcType=INTEGER}
        and ACTIVITY_ID = #{activityId, jdbcType=INTEGER}
        and IS_CLOSE = 0 LIMIT 1
    </select>

    <!--批量插入白名单数据-->
    <insert id="insertBatch">
        INSERT INTO HW_HARVEST_PLAN_AGENT_BLACK
        (agent_id, activity_id, job_number, operator_name, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.agentId, jdbcType=INTEGER},
            #{item.activityId, jdbcType=INTEGER},
            #{item.jobNumber, jdbcType=INTEGER},
            #{item.operatorName, jdbcType=VARCHAR},
            #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </insert>
</mapper>
