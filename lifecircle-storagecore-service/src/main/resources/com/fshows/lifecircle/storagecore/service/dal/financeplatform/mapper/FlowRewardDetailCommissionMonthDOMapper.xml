<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.FlowRewardDetailCommissionMonthDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.FlowRewardDetailCommissionMonthDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLEMENT_AGENT_USERNAME" property="settlementAgentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CURRENT_ASSESSMENT_QUARTER" property="currentAssessmentQuarter" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HIGHEST_AVG_MONTHLY_FLOW_QUARTER" property="highestAvgMonthlyFlowQuarter" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLEMENT_AGENT_ID" property="settlementAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="NET_INCREASE_FLOW" property="netIncreaseFlow" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SMALL_MERCHANT_RATIO" property="smallMerchantRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLEMENT_COEFFICIENT" property="settlementCoefficient" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CURRENT_AVG_QUARTERLY_FLOW" property="currentAvgQuarterlyFlow" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="HIGHEST_AVG_QUARTERLY_FLOW" property="highestAvgQuarterlyFlow" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`SOURCE_TYPE`,`AGENT_USERNAME`,`AGENT_COMPANYNAME`,`SETTLEMENT_AGENT_USERNAME`,`CURRENT_ASSESSMENT_QUARTER`,`HIGHEST_AVG_MONTHLY_FLOW_QUARTER`,`AGENT_ID`,`PT_MONTH`,`BUSINESS_DATE`,`SETTLEMENT_AGENT_ID`,`CREATE_TIME`,`UPDATE_TIME`,`NET_INCREASE_FLOW`,`SMALL_MERCHANT_RATIO`,`SETTLEMENT_COEFFICIENT`,`ACTUAL_PAYABLE_COMMISSION`,`CURRENT_AVG_QUARTERLY_FLOW`,`HIGHEST_AVG_QUARTERLY_FLOW`
    </sql>


            <!--insert:FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH-->
            <insert id="insert" >
                    INSERT INTO FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="currentAssessmentQuarter != null">`CURRENT_ASSESSMENT_QUARTER`,</if>
            <if test="highestAvgMonthlyFlowQuarter != null">`HIGHEST_AVG_MONTHLY_FLOW_QUARTER`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="netIncreaseFlow != null">`NET_INCREASE_FLOW`,</if>
            <if test="smallMerchantRatio != null">`SMALL_MERCHANT_RATIO`,</if>
            <if test="settlementCoefficient != null">`SETTLEMENT_COEFFICIENT`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
            <if test="currentAvgQuarterlyFlow != null">`CURRENT_AVG_QUARTERLY_FLOW`,</if>
            <if test="highestAvgQuarterlyFlow != null">`HIGHEST_AVG_QUARTERLY_FLOW`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="currentAssessmentQuarter != null">#{currentAssessmentQuarter,jdbcType=VARCHAR},</if>
            <if test="highestAvgMonthlyFlowQuarter != null">#{highestAvgMonthlyFlowQuarter,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="netIncreaseFlow != null">#{netIncreaseFlow,jdbcType=DECIMAL},</if>
            <if test="smallMerchantRatio != null">#{smallMerchantRatio,jdbcType=DECIMAL},</if>
            <if test="settlementCoefficient != null">#{settlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
            <if test="currentAvgQuarterlyFlow != null">#{currentAvgQuarterlyFlow,jdbcType=DECIMAL},</if>
            <if test="highestAvgQuarterlyFlow != null">#{highestAvgQuarterlyFlow,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--查询代理商流水奖励明细佣金-->
            <select id="getAgentFlowRewardCommissionList" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from fp_flow_reward_detail_commission_month
        where settlement_agent_id = #{agentId,jdbcType=INTEGER}
        AND business_date = #{businessDate,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        and fee_code = #{feeCode,jdbcType=VARCHAR}
            </select>
    </mapper>
