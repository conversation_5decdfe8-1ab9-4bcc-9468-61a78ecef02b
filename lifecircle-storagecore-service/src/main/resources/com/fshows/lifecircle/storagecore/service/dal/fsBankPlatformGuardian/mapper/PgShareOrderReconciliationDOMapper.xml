<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.mapper.PgShareOrderReconciliationDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.dataobject.PgShareOrderReconciliationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="IS_DEL" property="isDel" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FAIL_TYPE" property="failType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM" property="platform" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DEAL_STATE" property="dealState" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_REQ_NO" property="shareReqNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FB_FINISH_TIME" property="fbFinishTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FB_SHARE_STATE" property="fbShareState" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_REQ_NO" property="platformReqNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_FINISH_TIME" property="platformFinishTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_SHARE_STATE" property="platformShareState" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="HANDLE_DATE" property="handleDate" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="ABNORMAL_FUND" property="abnormalFund" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="FB_TOTAL_AMOUNT" property="fbTotalAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="PLATFORM_TOTAL_AMOUNT" property="platformTotalAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`IS_DEL`,`TOKEN`,`REASON`,`ORDER_SN`,`FAIL_TYPE`,`PLATFORM`,`DEAL_STATE`,`MERCHANT_NO`,`SHARE_REQ_NO`,`FB_FINISH_TIME`,`FB_SHARE_STATE`,`PLATFORM_REQ_NO`,`PLATFORM_FINISH_TIME`,`PLATFORM_SHARE_STATE`,`UID`,`CHANNEL`,`HANDLE_DATE`,`CREATE_TIME`,`UPDATE_TIME`,`ABNORMAL_FUND`,`FB_TOTAL_AMOUNT`,`PLATFORM_TOTAL_AMOUNT`
        </sql>


        <!--insert:PG_SHARE_ORDER_RECONCILIATION-->
        <insert id="insert">
            INSERT INTO PG_SHARE_ORDER_RECONCILIATION
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="token != null">`TOKEN`,</if>
                <if test="reason != null">`REASON`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="failType != null">`FAIL_TYPE`,</if>
                <if test="platform != null">`PLATFORM`,</if>
                <if test="dealState != null">`DEAL_STATE`,</if>
                <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="fbShareState != null">`FB_SHARE_STATE`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="platformShareState != null">`PLATFORM_SHARE_STATE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="handleDate != null">`HANDLE_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbFinishTime != null">`FB_FINISH_TIME`,</if>
            <if test="platformFinishTime != null">`PLATFORM_FINISH_TIME`,</if>
            <if test="abnormalFund != null">`ABNORMAL_FUND`,</if>
            <if test="fbTotalAmount != null">`FB_TOTAL_AMOUNT`,</if>
            <if test="platformTotalAmount != null">`PLATFORM_TOTAL_AMOUNT`,</if>
            <if test="merchantNo != null">`merchant_no`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="failType != null">#{failType,jdbcType=VARCHAR},</if>
            <if test="platform != null">#{platform,jdbcType=VARCHAR},</if>
            <if test="dealState != null">#{dealState,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="fbShareState != null">#{fbShareState,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="platformShareState != null">#{platformShareState,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="handleDate != null">#{handleDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbFinishTime != null">#{fbFinishTime,jdbcType=VARCHAR},</if>
            <if test="platformFinishTime != null">#{platformFinishTime,jdbcType=VARCHAR},</if>
            <if test="abnormalFund != null">#{abnormalFund,jdbcType=DECIMAL},</if>
            <if test="fbTotalAmount != null">#{fbTotalAmount,jdbcType=DECIMAL},</if>
            <if test="platformTotalAmount != null">#{platformTotalAmount,jdbcType=DECIMAL},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
        </trim>
        </insert>

        <!--批量插入数据-->
        <select id="batchInsert" resultMap="BaseResultMap">
            INSERT INTO PG_SHARE_ORDER_RECONCILIATION
            <trim prefix="(" suffix=")" suffixOverrides=",">
                `TOKEN`,
                `REASON`,
                `ORDER_SN`,
                `FAIL_TYPE`,
                `PLATFORM`,
                `SHARE_REQ_NO`,
                `FB_SHARE_STATE`,
                `PLATFORM_REQ_NO`,
                `PLATFORM_SHARE_STATE`,
                `UID`,
                `CHANNEL`,
                `HANDLE_DATE`,
                `FB_FINISH_TIME`,
                `PLATFORM_FINISH_TIME`,
                `ABNORMAL_FUND`,
                `FB_TOTAL_AMOUNT`,
                `PLATFORM_TOTAL_AMOUNT`,
                `merchant_no`,
            </trim>
        VALUES
        <foreach collection="list" item="shareOrder" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{shareOrder.token,jdbcType=VARCHAR},
                #{shareOrder.reason,jdbcType=VARCHAR},
                #{shareOrder.orderSn,jdbcType=VARCHAR},
                #{shareOrder.failType,jdbcType=VARCHAR},
                #{shareOrder.platform,jdbcType=VARCHAR},
                #{shareOrder.shareReqNo,jdbcType=VARCHAR},
                #{shareOrder.fbShareState,jdbcType=VARCHAR},
                #{shareOrder.platformReqNo,jdbcType=VARCHAR},
                #{shareOrder.platformShareState,jdbcType=VARCHAR},
                #{shareOrder.uid,jdbcType=INTEGER},
                #{shareOrder.channel,jdbcType=TINYINT},
                #{shareOrder.handleDate,jdbcType=INTEGER},
                #{shareOrder.fbFinishTime,jdbcType=VARCHAR},
                #{shareOrder.platformFinishTime,jdbcType=VARCHAR},
                #{shareOrder.abnormalFund,jdbcType=DECIMAL},
                #{shareOrder.fbTotalAmount,jdbcType=DECIMAL},
                #{shareOrder.platformTotalAmount,jdbcType=DECIMAL},
                #{shareOrder.merchantNo,jdbcType=VARCHAR},
            </trim>
        </foreach>
        </select>
    </mapper>
