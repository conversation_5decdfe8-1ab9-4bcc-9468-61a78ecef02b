<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayMakeCardLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayMakeCardLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MAKE_NUMBER" property="makeNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MAKE_STATUS" property="makeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BATCH_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`OPERATE_ID`,`CARD_SPU_NAME`,`OPERATE_NAME`,`PUBLISH_ORG_ID`,`IS_DEL`,`FINISH_TIME`,`MAKE_NUMBER`,`MAKE_STATUS`,`SUBMIT_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_MAKE_CARD_LOG-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_MAKE_CARD_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="makeNumber != null">`MAKE_NUMBER`,</if>
            <if test="makeStatus != null">`MAKE_STATUS`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="makeNumber != null">#{makeNumber,jdbcType=INTEGER},</if>
            <if test="makeStatus != null">#{makeStatus,jdbcType=TINYINT},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--批量插入制卡详情-->
            <insert id="insertMakeCardBatch" >
                    INSERT INTO
        tp_prepay_make_card_log (
        batch_no,
        card_spu_id,
        card_spu_name,
        card_sku_id,
        publish_org_id,
        make_number,
        operate_id,
        operate_name,
        submit_time,
        make_status
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.cardSpuId,jdbcType=VARCHAR},
            #{item.cardSpuName,jdbcType=VARCHAR},
            #{item.cardSkuId,jdbcType=VARCHAR},
            #{item.publishOrgId,jdbcType=VARCHAR},
            #{item.makeNumber,jdbcType=INTEGER},
            #{item.operateId,jdbcType=VARCHAR},
            #{item.operateName,jdbcType=VARCHAR},
            #{item.submitTime,jdbcType=INTEGER},
            #{item.makeStatus,jdbcType=TINYINT}
            )
        </foreach>
            </insert>

            <!--根据制卡批次号修改制卡状态-->
            <update id="updateMakeCardByBatchNo" >
                    update
        tp_prepay_make_card_log
        set
        make_status = #{makeStatus,jdbcType=TINYINT}
        <if test="finishTime !=null ">
            ,finish_time = #{finishTime,jdbcType=INTEGER}
        </if>
        where
        is_del = 0
        and batch_no = #{batchNo,jdbcType=VARCHAR}
            </update>
    </mapper>
