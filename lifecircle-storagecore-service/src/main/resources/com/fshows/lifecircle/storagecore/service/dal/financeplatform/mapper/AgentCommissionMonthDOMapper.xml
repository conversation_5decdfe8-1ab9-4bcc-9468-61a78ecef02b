<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.AgentCommissionMonthDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.AgentCommissionMonthDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_NUM" property="businessNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CPM_NUM" property="cpmNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DATA_TYPE" property="dataType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REVIEW_STATUS" property="reviewStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_HFIVE" property="exposureNumHfive" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NORMAL_TRADE_COUNT" property="normalTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_TRADE_COUNT" property="refundTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_APPLET" property="exposureNumApplet" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TAX_RATE" property="taxRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PAYABLE_COMMISSION" property="payableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DEDUCTION_COMMISSION" property="deductionCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLEMENT_COEFFICIENT" property="settlementCoefficient" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`REMARKS`,`SOURCE_TYPE`,`BUSINESS_NUM`,`AGENT_USERNAME`,`AGENT_COMPANYNAME`,`CPM_NUM`,`AGENT_ID`,`PT_MONTH`,`DATA_TYPE`,`BUSINESS_DATE`,`REVIEW_STATUS`,`EXPOSURE_NUM_HFIVE`,`NORMAL_TRADE_COUNT`,`REFUND_TRADE_COUNT`,`EXPOSURE_NUM_APPLET`,`CREATE_TIME`,`UPDATE_TIME`,`TAX_RATE`,`NORMAL_TRADE_AMOUNT`,`PAYABLE_COMMISSION`,`REFUND_TRADE_AMOUNT`,`DEDUCTION_COMMISSION`,`SETTLEMENT_COEFFICIENT`,`ACTUAL_PAYABLE_COMMISSION`
    </sql>


            <!--根据唯一约束UniqBusinessNum获取数据:fp_agent_commission_month-->
            <select id="getByUniqBusinessNum" resultMap="BaseResultMap">
                    SELECT /*MS-FP-AGENT-COMMISSION-MONTH-GETBYUNIQBUSINESSNUM*/  <include refid="Base_Column_List" />
        FROM fp_agent_commission_month
        WHERE
        <![CDATA[
            BUSINESS_NUM    = #{businessNum,jdbcType=VARCHAR}
        ]]>
            </select>
    </mapper>
