<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmAlipayTouchTaskDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmAlipayTouchTaskDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BIND_TIME" property="bindTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PEND_TIME" property="pendTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="LIMIT_TIME" property="limitTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="ACCEPT_TIME" property="acceptTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="NOTIFY_TIME" property="notifyTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="RECEIVE_TIME" property="receiveTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="DISTRIBUTE_TIME" property="distributeTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SN_LIST" property="snList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TASK_NO" property="taskNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHOP_NAME" property="shopName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SMID_LIST" property="smidList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PIC" property="storePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_ID" property="productId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FB_SMID_LIST" property="fbSmidList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_NAME" property="marketName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON_DESC" property="reasonDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_TAGS" property="productTags" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_DESC" property="activityDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_NAME" property="districtName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_NAME" property="salesmanName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_MOBILE" property="contactMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INTERIOR_PHOTO" property="interiorPhoto" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REGISTER_PHOTO" property="registerPhoto" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FB_MERCHANT_ADDRESS" property="fbMerchantAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_MERCHANT_ADDRESS" property="alipayMerchantAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WORKER_ID" property="workerId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TASK_STATUS" property="taskStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_TYPE" property="merchantType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PENDING_STATUS" property="pendingStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BIND_TIME`,`PEND_TIME`,`LIMIT_TIME`,`ACCEPT_TIME`,`NOTIFY_TIME`,`RECEIVE_TIME`,`DISTRIBUTE_TIME`,`SN_LIST`,`TASK_NO`,`CITY_CODE`,`CITY_NAME`,`SHOP_NAME`,`SMID_LIST`,`STORE_PIC`,`USERNAME`,`AGENT_NAME`,`PRODUCT_ID`,`STORE_NAME`,`ACTIVITY_ID`,`FB_SMID_LIST`,`MARKET_NAME`,`REASON_DESC`,`CONTACT_NAME`,`PRODUCT_NAME`,`PRODUCT_TAGS`,`ACTIVITY_DESC`,`ACTIVITY_TYPE`,`DISTRICT_CODE`,`DISTRICT_NAME`,`PROVINCE_CODE`,`PROVINCE_NAME`,`SALESMAN_NAME`,`CONTACT_MOBILE`,`INTERIOR_PHOTO`,`REGISTER_PHOTO`,`FB_MERCHANT_ADDRESS`,`ALIPAY_MERCHANT_ADDRESS`,`UID`,`IS_DEL`,`AGENT_ID`,`MARKET_ID`,`WORKER_ID`,`SALESMAN_ID`,`TASK_STATUS`,`MERCHANT_TYPE`,`PENDING_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_ALIPAY_TOUCH_TASK-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_ALIPAY_TOUCH_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="pendTime != null">`PEND_TIME`,</if>
            <if test="limitTime != null">`LIMIT_TIME`,</if>
            <if test="acceptTime != null">`ACCEPT_TIME`,</if>
            <if test="notifyTime != null">`NOTIFY_TIME`,</if>
            <if test="receiveTime != null">`RECEIVE_TIME`,</if>
            <if test="distributeTime != null">`DISTRIBUTE_TIME`,</if>
            <if test="snList != null">`SN_LIST`,</if>
            <if test="taskNo != null">`TASK_NO`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="shopName != null">`SHOP_NAME`,</if>
            <if test="smidList != null">`SMID_LIST`,</if>
            <if test="storePic != null">`STORE_PIC`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="productId != null">`PRODUCT_ID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="fbSmidList != null">`FB_SMID_LIST`,</if>
            <if test="marketName != null">`MARKET_NAME`,</if>
            <if test="reasonDesc != null">`REASON_DESC`,</if>
            <if test="contactName != null">`CONTACT_NAME`,</if>
            <if test="productName != null">`PRODUCT_NAME`,</if>
            <if test="productTags != null">`PRODUCT_TAGS`,</if>
            <if test="districtCode != null">`DISTRICT_CODE`,</if>
            <if test="districtName != null">`DISTRICT_NAME`,</if>
            <if test="provinceCode != null">`PROVINCE_CODE`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="salesmanName != null">`SALESMAN_NAME`,</if>
            <if test="contactMobile != null">`CONTACT_MOBILE`,</if>
            <if test="interiorPhoto != null">`INTERIOR_PHOTO`,</if>
            <if test="registerPhoto != null">`REGISTER_PHOTO`,</if>
            <if test="fbMerchantAddress != null">`FB_MERCHANT_ADDRESS`,</if>
            <if test="alipayMerchantAddress != null">`ALIPAY_MERCHANT_ADDRESS`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="taskStatus != null">`TASK_STATUS`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="pendingStatus != null">`PENDING_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=BIGINT},</if>
            <if test="pendTime != null">#{pendTime,jdbcType=BIGINT},</if>
            <if test="limitTime != null">#{limitTime,jdbcType=BIGINT},</if>
            <if test="acceptTime != null">#{acceptTime,jdbcType=BIGINT},</if>
            <if test="notifyTime != null">#{notifyTime,jdbcType=BIGINT},</if>
            <if test="receiveTime != null">#{receiveTime,jdbcType=BIGINT},</if>
            <if test="distributeTime != null">#{distributeTime,jdbcType=BIGINT},</if>
            <if test="snList != null">#{snList,jdbcType=VARCHAR},</if>
            <if test="taskNo != null">#{taskNo,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="shopName != null">#{shopName,jdbcType=VARCHAR},</if>
            <if test="smidList != null">#{smidList,jdbcType=VARCHAR},</if>
            <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="productId != null">#{productId,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="fbSmidList != null">#{fbSmidList,jdbcType=VARCHAR},</if>
            <if test="marketName != null">#{marketName,jdbcType=VARCHAR},</if>
            <if test="reasonDesc != null">#{reasonDesc,jdbcType=VARCHAR},</if>
            <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="productTags != null">#{productTags,jdbcType=VARCHAR},</if>
            <if test="districtCode != null">#{districtCode,jdbcType=VARCHAR},</if>
            <if test="districtName != null">#{districtName,jdbcType=VARCHAR},</if>
            <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="salesmanName != null">#{salesmanName,jdbcType=VARCHAR},</if>
            <if test="contactMobile != null">#{contactMobile,jdbcType=VARCHAR},</if>
            <if test="interiorPhoto != null">#{interiorPhoto,jdbcType=VARCHAR},</if>
            <if test="registerPhoto != null">#{registerPhoto,jdbcType=VARCHAR},</if>
            <if test="fbMerchantAddress != null">#{fbMerchantAddress,jdbcType=VARCHAR},</if>
            <if test="alipayMerchantAddress != null">#{alipayMerchantAddress,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="taskStatus != null">#{taskStatus,jdbcType=TINYINT},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
            <if test="pendingStatus != null">#{pendingStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--findTaskList-->
            <select id="findTaskList" resultMap="BaseResultMap">
                    select /*MS-LM-CRM-ALIPAY-TOUCH-TASK-FINDTASKLIST*/ <include refid="Base_Column_List" />
        from lm_crm_alipay_touch_task
        where is_del = 0
        <if test="taskNo != null and taskNo != ''">and task_no = #{taskNo,jdbcType=VARCHAR}</if>
        <if test="agentId != null">and agent_id = #{agentId,jdbcType=INTEGER}</if>
        <if test="salesmanId != null">
            and salesman_id = #{salesmanId,jdbcType=INTEGER}
        </if>
        <if test="salesmanName != null and salesmanName != ''">
            and salesman_name like CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="marketId != null">
            and market_id = #{marketId,jdbcType=INTEGER}
        </if>
        <if test="marketName != null and marketName != ''">
            and market_name like CONCAT(#{marketName,jdbcType=VARCHAR},'%')
        </if>
        <if test="notifyTimeStart != null and notifyTimeEnd != null">
            and notify_time between #{notifyTimeStart,jdbcType=BIGINT} and #{notifyTimeEnd,jdbcType=BIGINT}
        </if>
        <if test="taskStatus != null">and task_status = #{taskStatus,jdbcType=TINYINT}</if>
        <if test="contactName != null and contactName!= ''">and contact_name = #{contactName,jdbcType=VARCHAR}</if>
        <if test="contactMobile != null and contactMobile!= ''">and contact_mobile = #{contactMobile,jdbcType=VARCHAR}
        </if>
        <if test="storeName != null and storeName != ''">
            and store_name like CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="merchantType != null">and merchant_type = #{merchantType,jdbcType=TINYINT}</if>
        <if test="uid != null">and uid = #{uid,jdbcType=INTEGER}</if>
        <if test="username != null and username != ''">and username = #{username,jdbcType=VARCHAR}</if>
        <if test="cityName != null and cityName != ''">
            and city_name = like CONCAT(#{cityName,jdbcType=VARCHAR},'%')
        </if>
        <if test="provinceCode != null and provinceCode !=''">
            and province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode !=''">
            and district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="pendingStatus != null">
            and pending_status = #{pendingStatus,jdbcType=INTEGER}
        </if>
        <if test="districtName != null and districtName != ''">
            and district_name = #{districtName,jdbcType=VARCHAR}
        </if>
        <if test="activityType != null and activityType != ''">
            and activity_type = #{activityType,jdbcType=VARCHAR}
        </if>
        order by notify_time desc
        limit 1000
            </select>
    </mapper>
