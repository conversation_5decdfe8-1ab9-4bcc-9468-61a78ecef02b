<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.WechatDirectEcommerceApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.WechatDirectEcommerceApplyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="SIGN_URL" property="signUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCHID" property="subMchid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_RATE" property="applyRate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_NAME" property="idCardName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUBMIT_NAME" property="submitName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_BANK" property="accountBank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLYMENT_ID" property="applymentId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_LAT" property="bizStoreLat" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_LNG" property="bizStoreLng" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON" property="legalPerson" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUBJECT_TYPE" property="subjectType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_AREA_CODE" property="bankAreaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_BRANCH_ID" property="bankBranchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_CITY_CODE" property="bankCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_NAME" property="bizStoreName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_EMAIL" property="contactEmail" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_NUMBER" property="idCardNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MAX_APPLY_RATE" property="maxApplyRate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NAME" property="merchantName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MIN_APPLY_RATE" property="minApplyRate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERVICE_PHONE" property="servicePhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NUMBER" property="accountNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_BACK_PIC" property="idCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_NUMBER" property="licenseNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_LICENSE_PIC" property="orgLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLYMENT_STATE" property="applymentState" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLEMENT_CODE" property="settlementCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_ADDRESS" property="bizStoreAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_CASH_PIC" property="bizStoreCashPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_ID_NUMBER" property="contactIdNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_PERIOD_END" property="idCardPeriodEnd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUBJECT_TYPE_NAME" property="subjectTypeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_PROVINCE_CODE" property="bankProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_AREA_CODE" property="bizStoreAreaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_CITY_CODE" property="bizStoreCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_FRONTAL_PIC" property="idCardFrontalPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_LICENSE_NUMBER" property="orgLicenseNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SPECIAL_SIMPLE_TIP" property="specialSimpleTip" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLYMENT_STATE_MSG" property="applymentStateMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_INDOOR_PIC" property="bizStoreIndoorPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_PERIOD_BEGIN" property="idCardPeriodBegin" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_SHORTNAME" property="merchantShortname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QUALIFICATION_TYPE" property="qualificationType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_MOBILE_PHONE" property="contactMobilePhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC_MEDIAL_ID" property="licensePicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_ENTRANCE_PIC" property="bizStoreEntrancePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_WECHAT_OPENID" property="contactWechatOpenid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_LICENSE_PERIOD_END" property="orgLicensePeriodEnd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_PROVINCE_CODE" property="bizStoreProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_BACK_PIC_MEDIAL_ID" property="idCardBackPicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_LICENSE_PERIOD_BEGIN" property="orgLicensePeriodBegin" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_LICENSE_PIC_MEDIAL_ID" property="orgLicensePicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_CASH_PIC_MEDIAL_ID" property="bizStoreCashPicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_CARD_FRONTAL_PIC_MEDIAL_ID" property="idCardFrontalPicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_INDOOR_PIC_MEDIAL_ID" property="bizStoreIndoorPicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_STORE_ENTRANCE_PIC_MEDIAL_ID" property="bizStoreEntrancePicMedialId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ADD_SOURCE" property="addSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUBMIT_STEP" property="submitStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUDIT_STATUS" property="auditStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPECIAL_NEED" property="specialNeed" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPECIAL_PIC_MAX" property="specialPicMax" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BANK_ACCOUNT_TYPE" property="bankAccountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LICENSE_UNIT_FLAG" property="licenseUnitFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBJECT_TYPE_VALUE" property="subjectTypeValue" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CAN_MODIFY_APPLY_RATE" property="canModifyApplyRate" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ID_CARD_PERIOD_IS_LONG" property="idCardPeriodIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORG_LICENSE_PERIOD_IS_LONG" property="orgLicensePeriodIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SIGN_URL`,`BANK_NAME`,`SUB_MCHID`,`APPLY_RATE`,`ACTIVITY_ID`,`ID_CARD_NAME`,`LICENSE_PIC`,`SUBMIT_NAME`,`ACCOUNT_BANK`,`ACCOUNT_NAME`,`APPLYMENT_ID`,`BIZ_STORE_LAT`,`BIZ_STORE_LNG`,`CONTACT_NAME`,`LEGAL_PERSON`,`SUBJECT_TYPE`,`BANK_AREA_CODE`,`BANK_BRANCH_ID`,`BANK_CITY_CODE`,`BIZ_STORE_NAME`,`BUSINESS_CODE`,`CONTACT_EMAIL`,`ID_CARD_NUMBER`,`MAX_APPLY_RATE`,`MERCHANT_NAME`,`MIN_APPLY_RATE`,`SERVICE_PHONE`,`ACCOUNT_NUMBER`,`ID_CARD_BACK_PIC`,`LICENSE_NUMBER`,`ORG_LICENSE_PIC`,`APPLYMENT_STATE`,`SETTLEMENT_CODE`,`BIZ_STORE_ADDRESS`,`BIZ_STORE_CASH_PIC`,`CONTACT_ID_NUMBER`,`ID_CARD_PERIOD_END`,`SUBJECT_TYPE_NAME`,`BANK_PROVINCE_CODE`,`BIZ_STORE_AREA_CODE`,`BIZ_STORE_CITY_CODE`,`ID_CARD_FRONTAL_PIC`,`ORG_LICENSE_NUMBER`,`SPECIAL_SIMPLE_TIP`,`APPLYMENT_STATE_MSG`,`BIZ_STORE_INDOOR_PIC`,`ID_CARD_PERIOD_BEGIN`,`MERCHANT_SHORTNAME`,`QUALIFICATION_TYPE`,`CONTACT_MOBILE_PHONE`,`LICENSE_PIC_MEDIAL_ID`,`BIZ_STORE_ENTRANCE_PIC`,`CONTACT_WECHAT_OPENID`,`ORG_LICENSE_PERIOD_END`,`BIZ_STORE_PROVINCE_CODE`,`ID_CARD_BACK_PIC_MEDIAL_ID`,`ORG_LICENSE_PERIOD_BEGIN`,`ORG_LICENSE_PIC_MEDIAL_ID`,`BIZ_STORE_CASH_PIC_MEDIAL_ID`,`ID_CARD_FRONTAL_PIC_MEDIAL_ID`,`BIZ_STORE_INDOOR_PIC_MEDIAL_ID`,`BIZ_STORE_ENTRANCE_PIC_MEDIAL_ID`,`UID`,`IS_DEL`,`ADD_SOURCE`,`CHANNEL_ID`,`SUBMIT_STEP`,`APPLY_STATUS`,`AUDIT_STATUS`,`SPECIAL_NEED`,`SPECIAL_PIC_MAX`,`BANK_ACCOUNT_TYPE`,`LICENSE_UNIT_FLAG`,`SUBJECT_TYPE_VALUE`,`CAN_MODIFY_APPLY_RATE`,`ID_CARD_PERIOD_IS_LONG`,`ORG_LICENSE_PERIOD_IS_LONG`,`CREATE_TIME`,`SUBMIT_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_WECHAT_DIRECT_ECOMMERCE_APPLY-->
            <insert id="insert" >
                    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_WECHAT_DIRECT_ECOMMERCE_APPLY(
            ID
            ,BANK_NAME
            ,SUB_MCHID
            ,ACTIVITY_ID
            ,ID_CARD_NAME
            ,LICENSE_PIC
            ,SUBMIT_NAME
            ,ACCOUNT_BANK
            ,ACCOUNT_NAME
            ,APPLYMENT_ID
            ,BIZ_STORE_LAT
            ,BIZ_STORE_LNG
            ,CONTACT_NAME
            ,LEGAL_PERSON
            ,ACTIVITY_RATE
            ,BANK_AREA_CODE
            ,BANK_BRANCH_ID
            ,BANK_CITY_CODE
            ,BIZ_STORE_NAME
            ,BUSINESS_CODE
            ,CONTACT_EMAIL
            ,ID_CARD_NUMBER
            ,MERCHANT_NAME
            ,SERVICE_PHONE
            ,SETTLEMENT_ID
            ,ACCOUNT_NUMBER
            ,ID_CARD_BACK_PIC
            ,LICENSE_NUMBER
            ,ORG_LICENSE_PIC
            ,APPLYMENT_STATE
            ,BIZ_STORE_ADDRESS
            ,BIZ_STORE_CASH_PIC
            ,CONTACT_ID_NUMBER
            ,ID_CARD_PERIOD_END
            ,BANK_PROVINCE_CODE
            ,BIZ_STORE_AREA_CODE
            ,BIZ_STORE_CITY_CODE
            ,ID_CARD_FRONTAL_PIC
            ,ORG_LICENSE_NUMBER
            ,APPLYMENT_STATE_MSG
            ,BIZ_STORE_INDOOR_PIC
            ,ID_CARD_PERIOD_BEGIN
            ,MERCHANT_SHORTNAME
            ,CONTACT_MOBILE_PHONE
            ,BIZ_STORE_ENTRANCE_PIC
            ,CONTACT_WECHAT_OPENID
            ,ORG_LICENSE_PERIOD_END
            ,QUALIFICATION_TYPE_ID
            ,BIZ_STORE_PROVINCE_CODE
            ,ORG_LICENSE_PERIOD_BEGIN
            ,UID
            ,IS_DEL
            ,ADD_SOURCE
            ,SUBJECT_TYPE
            ,APPLYMENT_STATUS
            ,BANK_ACCOUNT_TYPE
            ,LICENSE_UNIT_FLAG
            ,SUBJECT_TYPE_VALUE
            ,ID_CARD_PERIOD_IS_LONG
            ,ORG_LICENSE_PERIOD_IS_LONG
            ,CREATE_TIME
            ,SUBMIT_TIME
            ,UPDATE_TIME
        )VALUES(
             #{id,jdbcType=INTEGER}
            , #{bankName,jdbcType=VARCHAR}
            , #{subMchid,jdbcType=VARCHAR}
            , #{activityId,jdbcType=VARCHAR}
            , #{idCardName,jdbcType=VARCHAR}
            , #{licensePic,jdbcType=VARCHAR}
            , #{submitName,jdbcType=VARCHAR}
            , #{accountBank,jdbcType=VARCHAR}
            , #{accountName,jdbcType=VARCHAR}
            , #{applymentId,jdbcType=VARCHAR}
            , #{bizStoreLat,jdbcType=VARCHAR}
            , #{bizStoreLng,jdbcType=VARCHAR}
            , #{contactName,jdbcType=VARCHAR}
            , #{legalPerson,jdbcType=VARCHAR}
            , #{activityRate,jdbcType=VARCHAR}
            , #{bankAreaCode,jdbcType=VARCHAR}
            , #{bankBranchId,jdbcType=VARCHAR}
            , #{bankCityCode,jdbcType=VARCHAR}
            , #{bizStoreName,jdbcType=VARCHAR}
            , #{businessCode,jdbcType=VARCHAR}
            , #{contactEmail,jdbcType=VARCHAR}
            , #{idCardNumber,jdbcType=VARCHAR}
            , #{merchantName,jdbcType=VARCHAR}
            , #{servicePhone,jdbcType=VARCHAR}
            , #{settlementId,jdbcType=VARCHAR}
            , #{accountNumber,jdbcType=VARCHAR}
            , #{idCardBackPic,jdbcType=VARCHAR}
            , #{licenseNumber,jdbcType=VARCHAR}
            , #{orgLicensePic,jdbcType=VARCHAR}
            , #{applymentState,jdbcType=VARCHAR}
            , #{bizStoreAddress,jdbcType=VARCHAR}
            , #{bizStoreCashPic,jdbcType=VARCHAR}
            , #{contactIdNumber,jdbcType=VARCHAR}
            , #{idCardPeriodEnd,jdbcType=VARCHAR}
            , #{bankProvinceCode,jdbcType=VARCHAR}
            , #{bizStoreAreaCode,jdbcType=VARCHAR}
            , #{bizStoreCityCode,jdbcType=VARCHAR}
            , #{idCardFrontalPic,jdbcType=VARCHAR}
            , #{orgLicenseNumber,jdbcType=VARCHAR}
            , #{applymentStateMsg,jdbcType=VARCHAR}
            , #{bizStoreIndoorPic,jdbcType=VARCHAR}
            , #{idCardPeriodBegin,jdbcType=VARCHAR}
            , #{merchantShortname,jdbcType=VARCHAR}
            , #{contactMobilePhone,jdbcType=VARCHAR}
            , #{bizStoreEntrancePic,jdbcType=VARCHAR}
            , #{contactWechatOpenid,jdbcType=VARCHAR}
            , #{orgLicensePeriodEnd,jdbcType=VARCHAR}
            , #{qualificationTypeId,jdbcType=VARCHAR}
            , #{bizStoreProvinceCode,jdbcType=VARCHAR}
            , #{orgLicensePeriodBegin,jdbcType=VARCHAR}
            , #{uid,jdbcType=INTEGER}
            , #{isDel,jdbcType=TINYINT}
            , #{addSource,jdbcType=TINYINT}
            , #{subjectType,jdbcType=TINYINT}
            , #{applymentStatus,jdbcType=TINYINT}
            , #{bankAccountType,jdbcType=TINYINT}
            , #{licenseUnitFlag,jdbcType=TINYINT}
            , #{subjectTypeValue,jdbcType=TINYINT}
            , #{idCardPeriodIsLong,jdbcType=TINYINT}
            , #{orgLicensePeriodIsLong,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{submitTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
            </insert>

            <!--update table:TP_WECHAT_DIRECT_ECOMMERCE_APPLY-->
            <update id="update" >
                    UPDATE /*MS-TP-WECHAT-DIRECT-ECOMMERCE-APPLY-UPDATE*/ TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        SET
            BANK_NAME       = #{bankName,jdbcType=VARCHAR}
            ,SUB_MCHID       = #{subMchid,jdbcType=VARCHAR}
            ,ACTIVITY_ID     = #{activityId,jdbcType=VARCHAR}
            ,ID_CARD_NAME    = #{idCardName,jdbcType=VARCHAR}
            ,LICENSE_PIC     = #{licensePic,jdbcType=VARCHAR}
            ,SUBMIT_NAME     = #{submitName,jdbcType=VARCHAR}
            ,ACCOUNT_BANK    = #{accountBank,jdbcType=VARCHAR}
            ,ACCOUNT_NAME    = #{accountName,jdbcType=VARCHAR}
            ,APPLYMENT_ID    = #{applymentId,jdbcType=VARCHAR}
            ,BIZ_STORE_LAT   = #{bizStoreLat,jdbcType=VARCHAR}
            ,BIZ_STORE_LNG   = #{bizStoreLng,jdbcType=VARCHAR}
            ,CONTACT_NAME    = #{contactName,jdbcType=VARCHAR}
            ,LEGAL_PERSON    = #{legalPerson,jdbcType=VARCHAR}
            ,ACTIVITY_RATE   = #{activityRate,jdbcType=VARCHAR}
            ,BANK_AREA_CODE  = #{bankAreaCode,jdbcType=VARCHAR}
            ,BANK_BRANCH_ID  = #{bankBranchId,jdbcType=VARCHAR}
            ,BANK_CITY_CODE  = #{bankCityCode,jdbcType=VARCHAR}
            ,BIZ_STORE_NAME  = #{bizStoreName,jdbcType=VARCHAR}
            ,BUSINESS_CODE   = #{businessCode,jdbcType=VARCHAR}
            ,CONTACT_EMAIL   = #{contactEmail,jdbcType=VARCHAR}
            ,ID_CARD_NUMBER  = #{idCardNumber,jdbcType=VARCHAR}
            ,MERCHANT_NAME   = #{merchantName,jdbcType=VARCHAR}
            ,SERVICE_PHONE   = #{servicePhone,jdbcType=VARCHAR}
            ,SETTLEMENT_ID   = #{settlementId,jdbcType=VARCHAR}
            ,ACCOUNT_NUMBER  = #{accountNumber,jdbcType=VARCHAR}
            ,ID_CARD_BACK_PIC = #{idCardBackPic,jdbcType=VARCHAR}
            ,LICENSE_NUMBER  = #{licenseNumber,jdbcType=VARCHAR}
            ,ORG_LICENSE_PIC = #{orgLicensePic,jdbcType=VARCHAR}
            ,APPLYMENT_STATE = #{applymentState,jdbcType=VARCHAR}
            ,BIZ_STORE_ADDRESS = #{bizStoreAddress,jdbcType=VARCHAR}
            ,BIZ_STORE_CASH_PIC = #{bizStoreCashPic,jdbcType=VARCHAR}
            ,CONTACT_ID_NUMBER = #{contactIdNumber,jdbcType=VARCHAR}
            ,ID_CARD_PERIOD_END = #{idCardPeriodEnd,jdbcType=VARCHAR}
            ,BANK_PROVINCE_CODE = #{bankProvinceCode,jdbcType=VARCHAR}
            ,BIZ_STORE_AREA_CODE = #{bizStoreAreaCode,jdbcType=VARCHAR}
            ,BIZ_STORE_CITY_CODE = #{bizStoreCityCode,jdbcType=VARCHAR}
            ,ID_CARD_FRONTAL_PIC = #{idCardFrontalPic,jdbcType=VARCHAR}
            ,ORG_LICENSE_NUMBER = #{orgLicenseNumber,jdbcType=VARCHAR}
            ,APPLYMENT_STATE_MSG = #{applymentStateMsg,jdbcType=VARCHAR}
            ,BIZ_STORE_INDOOR_PIC = #{bizStoreIndoorPic,jdbcType=VARCHAR}
            ,ID_CARD_PERIOD_BEGIN = #{idCardPeriodBegin,jdbcType=VARCHAR}
            ,MERCHANT_SHORTNAME = #{merchantShortname,jdbcType=VARCHAR}
            ,CONTACT_MOBILE_PHONE = #{contactMobilePhone,jdbcType=VARCHAR}
            ,BIZ_STORE_ENTRANCE_PIC = #{bizStoreEntrancePic,jdbcType=VARCHAR}
            ,CONTACT_WECHAT_OPENID = #{contactWechatOpenid,jdbcType=VARCHAR}
            ,ORG_LICENSE_PERIOD_END = #{orgLicensePeriodEnd,jdbcType=VARCHAR}
            ,QUALIFICATION_TYPE_ID = #{qualificationTypeId,jdbcType=VARCHAR}
            ,BIZ_STORE_PROVINCE_CODE = #{bizStoreProvinceCode,jdbcType=VARCHAR}
            ,ORG_LICENSE_PERIOD_BEGIN = #{orgLicensePeriodBegin,jdbcType=VARCHAR}
            ,UID             = #{uid,jdbcType=INTEGER}
            ,IS_DEL          = #{isDel,jdbcType=TINYINT}
            ,ADD_SOURCE      = #{addSource,jdbcType=TINYINT}
            ,SUBJECT_TYPE    = #{subjectType,jdbcType=TINYINT}
            ,APPLYMENT_STATUS = #{applymentStatus,jdbcType=TINYINT}
            ,BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=TINYINT}
            ,LICENSE_UNIT_FLAG = #{licenseUnitFlag,jdbcType=TINYINT}
            ,SUBJECT_TYPE_VALUE = #{subjectTypeValue,jdbcType=TINYINT}
            ,ID_CARD_PERIOD_IS_LONG = #{idCardPeriodIsLong,jdbcType=TINYINT}
            ,ORG_LICENSE_PERIOD_IS_LONG = #{orgLicensePeriodIsLong,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,SUBMIT_TIME     = #{submitTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=INTEGER}
            </update>

            <!--delete:TP_WECHAT_DIRECT_ECOMMERCE_APPLY-->
            <delete id="deleteById" >
                    DELETE /*MS-TP-WECHAT-DIRECT-ECOMMERCE-APPLY-DELETEBYID*/ FROM
            TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE
            ID = #{id,jdbcType=INTEGER}
            </delete>

            <!--get:TP_WECHAT_DIRECT_ECOMMERCE_APPLY-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-WECHAT-DIRECT-ECOMMERCE-APPLY-GETBYID*/  <include refid="Base_Column_List" />
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE
            ID = #{id,jdbcType=INTEGER}
            </select>

            <!--根据子商户号查询-->
            <select id="getBySubMchIdList" resultType="java.lang.String">
                    SELECT /*MS-TP-WECHAT-DIRECT-ECOMMERCE-APPLY-GETBYSUBMCHIDLIST*/  sub_mchid
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE sub_mchid IN
        <foreach close=")" collection="list" index="index" item="subMchId" open="(" separator=",">
            #{subMchId,jdbcType=VARCHAR}
        </foreach>
        AND is_del = 0
            </select>

            <!--根据子商户号统计-->
            <select id="countBySubMchId" resultType="java.lang.Integer">
                    SELECT /*MS-TP-WECHAT-DIRECT-ECOMMERCE-APPLY-COUNTBYSUBMCHID*/  COUNT(*)
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE sub_mchid = #{subMchid,jdbcType=VARCHAR}
        AND is_del = 0
            </select>

            <!--Web 添加商户-->
            <insert id="insertMerchantWeb" >
                    INSERT INTO tp_wechat_direct_ecommerce_apply
        (
         sub_mchid
        ,channel_id
        ,submit_name
        ,uid
        ,add_source
        ,apply_status
        ,audit_status
        ,create_time
        ,submit_time
        ,submit_step
        ,update_time
        )
        VALUES
        (
          #{subMchid,jdbcType=VARCHAR}
        , #{channelId,jdbcType=INTEGER}
        , #{submitName,jdbcType=VARCHAR}
        , #{uid,jdbcType=INTEGER}
        , #{addSource,jdbcType=TINYINT}
        , #{applyStatus,jdbcType=TINYINT}
        , #{auditStatus,jdbcType=TINYINT}
        , #{createTime,jdbcType=TIMESTAMP}
        , #{submitTime,jdbcType=TIMESTAMP}
        , #{submitStep,jdbcType=INTEGER}
        , #{updateTime,jdbcType=TIMESTAMP}
        )
            </insert>

            <!--Web 添加商户批量插入-->
            <insert id="insertMerchantWebBatch" >
                    INSERT INTO tp_wechat_direct_ecommerce_apply(
        sub_mchid
        ,channel_id
        ,submit_name
        ,uid
        ,add_source
        ,apply_status
        ,audit_status
        ,create_time
        ,submit_time
        ,submit_step
        ,update_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
              #{item.subMchid,jdbcType=VARCHAR}
            , #{item.channelId,jdbcType=INTEGER}
            , #{item.submitName,jdbcType=VARCHAR}
            , #{item.uid,jdbcType=INTEGER}
            , #{item.addSource,jdbcType=TINYINT}
            , #{item.applyStatus,jdbcType=TINYINT}
            , #{item.auditStatus,jdbcType=TINYINT}
            , #{item.createTime,jdbcType=TIMESTAMP}
            , #{item.submitTime,jdbcType=TIMESTAMP}
            , #{item.submitStep,jdbcType=INTEGER}
            , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
            </insert>
    </mapper>
