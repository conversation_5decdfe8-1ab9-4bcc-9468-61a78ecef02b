<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.ExtraHwEquipmentSnDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentSnDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SYSTEM_SN" property="systemSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="SN_STATUS" property="snStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
                javaType="Integer"/>

    </resultMap>

    <resultMap id="EquipmentSnInfoMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentSnInfoMap">

        <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

        <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

        <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>
    </resultMap>

    <sql id="Base_Column_List">
        `ID`
        ,
        `INIT_SN`,
        `SYSTEM_SN`,
        `IS_DEL`,
        `AGENT_ID`,
        `GRANT_ID`,
        `SN_STATUS`,
        `EQUIPMENT_ID`,
        `CREATE_TIME`,
        `UPDATE_TIME`
    </sql>


    <!--集合in查询-->
    <select id="getByInitSnList" resultMap="BaseResultMap">
        select /*MS-HW-EQUIPMENT-SN-GETBYINITSNLIST*/ id,init_sn,operate_type,sn_status,depot,equipment_id from
        hw_equipment_sn
        where is_del = 0
        <if test="snList!=null and snList.size()&gt;0">
            and init_sn in
            <foreach collection="snList" item="sn" separator="," open="(" close=")">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
    </select>

    <!--根据id批量获取SN信息-->
    <select id="getSnListById" resultMap="BaseResultMap">
        SELECT /*MS-HW-EQUIPMENT-SN-GETSNLISTBYID*/  <include refid="Base_Column_List" />
        FROM hw_equipment_sn
        WHERE is_del = 0
        AND id in
        <foreach collection="list" open="(" close=")" item="id" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--非直营扫脸活动导出-->
    <select id="getNoDirectScanFaceListExport" resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceInfoDO">
        SELECT
        esn.id AS snId,
        esn.equipment_id AS equipmentId,
        esn.store_id AS snStoreId,
        face.store_id AS faceStoreId,
        esn.init_sn AS systemSn,
        eq.equipment_model AS equipmentModel,
        esn.sn_status AS snStatus,
        face.light_status AS lightStatus,
        face.light_time AS lightTime,
        face.activity_status AS activityStatus,
        face.activity_time AS activityTime,
        us.username AS merchantName,
        us.market_id AS marketId,
        store.store_name AS storeName,
        u.username AS grantName,
        face.equipment_dau AS equipmentDau,
        face.transaction_num AS transactionNum,
        face.transaction_money AS transactionMoney,
        face.code_scan_num AS codeScanNum,
        face.face_scan_dau AS faceScanDau,
        esn.bind_time AS bindTime,
        us.id as merchantId
        FROM hw_equipment_sn esn
        LEFT JOIN hw_equipment eq ON esn.equipment_id = eq.id
        LEFT JOIN tp_lifecircle_store store ON store.store_id = esn.store_id
        LEFT JOIN tp_users us ON us.id = esn.uid
        LEFT JOIN tp_user u ON u.id = esn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face ON face.agent_id = esn.agent_id AND face.equipment_sn = esn.init_sn
        <trim prefix="where" prefixOverrides="and|or">
            AND eq.equipment_type = 4
            AND esn.agent_id = #{agentId,jdbcType=INTEGER}
            <if test="snStatus !=null and snStatus == 2">
                AND esn.sn_status = 5
            </if>
            <if test="snStatus !=null and snStatus == 1">
                AND esn.sn_status = 4
            </if>
            <if test="snStatus ==null">
                AND esn.sn_status IN(4,5)
            </if>
            <if test="activityStatus !=null and activityStatus == 2">
                AND face.activity_status = #{activityStatus,jdbcType=TINYINT}
            </if>
            <if test="activityStatus !=null and activityStatus == 1">
                AND (face.activity_status = 1 or face.activity_status is null)
            </if>
            <if test="grantId !=null">
                AND esn.grant_id = #{grantId,jdbcType=INTEGER}
            </if>
            <if test="systemSn !=null and systemSn!=''">
                AND esn.init_sn = #{systemSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentSetId !=null">
                AND eq.id = #{equipmentSetId,jdbcType=INTEGER}
            </if>
            <if test="storeName !=null and storeName !=''">
                AND store.store_name like CONCAT(#{storeName,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantName !=null and merchantName !=''">
                AND us.username like CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
            </if>
            <if test="activityStartTime !=null and activityEndTime !=null">
                AND face.activity_time between #{activityStartTime,jdbcType=TIMESTAMP} AND #{activityEndTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
        order by esn.create_time desc
    </select>

    <!--直营扫脸活动导出-->
    <select id="getDirectScanFaceListExport" resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceInfoDO">
        SELECT
        esn.id AS snId,
        esn.equipment_id AS equipmentId,
        esn.store_id AS snStoreId,
        face.store_id AS faceStoreId,
        esn.init_sn AS systemSn,
        eq.equipment_model AS equipmentModel,
        esn.sn_status AS snStatus,
        face.light_status AS lightStatus,
        face.light_time AS lightTime,
        face.activity_status AS activityStatus,
        face.activity_time AS activityTime,
        us.username AS merchantName,
        us.market_id AS marketId,
        store.store_name AS storeName,
        u.username AS grantName,
        face.equipment_dau AS equipmentDau,
        face.transaction_num AS transactionNum,
        face.transaction_money AS transactionMoney,
        face.code_scan_num AS codeScanNum,
        face.face_scan_dau AS faceScanDau,
        esn.bind_time AS bindTime,
        us.id as merchant_id
        FROM hw_equipment_sn esn
        LEFT JOIN hw_equipment eq ON esn.equipment_id = eq.id
        LEFT JOIN tp_user u ON u.id = esn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face ON face.agent_id = esn.agent_id AND face.equipment_sn = esn.init_sn
        LEFT JOIN tp_lifecircle_store store ON store.store_id = face.store_id
        LEFT JOIN tp_users us ON us.id = face.uid
        <trim prefix="where" prefixOverrides="and|or">
            and eq.equipment_type = 4
            and esn.agent_id = #{agentId,jdbcType=INTEGER}
            <if test="snStatus!=null and snStatus == 2">
                AND esn.sn_status = 5
            </if>
            <if test="snStatus!=null and snStatus == 1">
                AND esn.sn_status = 4
            </if>
            <if test="snStatus ==null">
                AND esn.sn_status IN(4,5)
            </if>
            <if test="activityStatus!=null and activityStatus==2">
                AND face.activity_status = #{activityStatus,jdbcType=TINYINT}
            </if>
            <if test="activityStatus!=null and activityStatus==1">
                AND (face.activity_status = 1 or face.activity_status is null)
            </if>
            <if test="grantId!=null">
                AND esn.grant_id = #{grantId,jdbcType=INTEGER}
            </if>
            <if test="equipmentSetId !=null">
                AND eq.id = #{equipmentSetId,jdbcType=INTEGER}
            </if>
            <if test="systemSn!=null and systemSn!=''">
                AND esn.init_sn = #{systemSn,jdbcType=VARCHAR}
            </if>
            <if test="storeName!=null and storeName!=''">
                AND store.store_name like CONCAT(#{storeName,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantName!=null and merchantName!=''">
                AND us.username like CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
            </if>
            <if test="activityStartTime!=null and activityEndTime!=null">
                AND face.activity_time between #{activityStartTime,jdbcType=TIMESTAMP} AND #{activityEndTime,jdbcType=TIMESTAMP}
            </if>
        </trim>
        order by esn.create_time desc
    </select>

    <!--新代理商后台硬件管理设备列表查询-->
    <select id="getAgentEquipmentList" resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AgentEquipmentInfoListMap">
        SELECT sn.init_sn AS initSn,
        eq.equipment_name AS equipmentName,
        eq.equipment_model AS equipmentModel,
        us.username AS merchantName,
        store.store_name AS storeName,
        u.username AS grantName,
        sn.bind_time AS bindTime,
        sn.sn_status AS snStatus
        FROM hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user u on sn.grant_id = u.id
        LEFT JOIN tp_users us on sn.uid = us.id
        LEFT JOIN tp_lifecircle_store store on sn.store_id = store.store_id
        WHERE sn.agent_id = #{agentId,jdbcType=INTEGER}
        and sn.is_del = 0
        <if test="equipmentSn != null and equipmentSn !=''">
            and sn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="snStatus != null and snStatus == 0">
            and sn.sn_status = 2
        </if>
        <if test="snStatus != null and snStatus == 1">
            and sn.sn_status = 5
        </if>
        <if test="snStatus != null and snStatus == -1">
            and sn.sn_status in (2,5)
        </if>
        <if test="grantId != null and grantId != -1">
            and sn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        <if test="id != null and id != -1">
            and eq.id = #{id,jdbcType=INTEGER}
        </if>
        <if test="equipmentName != null and equipmentName != ''">
            and eq.equipment_name = #{equipmentName,jdbcType=VARCHAR}
        </if>
        <if test="bindStartTime != null and bindEndTime != null">
            and sn.bind_time BETWEEN #{bindStartTime,jdbcType=TIMESTAMP} AND #{bindEndTime,jdbcType=TIMESTAMP}
        </if>
        order by sn.id desc
    </select>

    <insert id="insertBatchForOem">
        INSERT INTO
        hw_equipment_sn(`equipment_id`,`init_sn`,`system_sn`,`sn_status`,`operate_type`,`depot`,`oem_name`,`oem_id`)
        VALUES
        <foreach collection="list" item="sn" index="index" separator=",">
            (
            #{sn.equipmentId,jdbcType=INTEGER},
            #{sn.initSn,jdbcType=VARCHAR},
            #{sn.systemSn,jdbcType=VARCHAR},
            #{sn.snStatus,jdbcType=INTEGER},
            #{sn.operateType,jdbcType=TINYINT},
            #{sn.depot,jdbcType=TINYINT},
            #{sn.oemName,jdbcType=VARCHAR},
            #{sn.oemId,jdbcType=INTEGER}
            )
        </foreach>
    </insert>
</mapper>
