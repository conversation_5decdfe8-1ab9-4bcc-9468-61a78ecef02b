<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingGroupActivityGoodsDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingGroupActivityGoodsDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CODE" property="code" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UNIT" property="unit" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SPU_ID" property="spuId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PICTURE" property="picture" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CATEGORY_ID" property="categoryId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ONLINE_SPU_ID" property="onlineSpuId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GROUP_ACTIVITY_ID" property="groupActivityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GROUP_ACTIVITY_GOODS_ID" property="groupActivityGoodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="LEFT_STOCK" property="leftStock" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="OUT_STORE_ID" property="outStoreId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PURCHASE_LIMIT" property="purchaseLimit" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="ACTIVITY_PRICE" property="activityPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CODE`,`UNIT`,`SPU_ID`,`PICTURE`,`GOODS_NAME`,`CATEGORY_ID`,`ONLINE_SPU_ID`,`CATEGORY_NAME`,`GROUP_ACTIVITY_ID`,`GROUP_ACTIVITY_GOODS_ID`,`UID`,`DEL_FLAG`,`LEFT_STOCK`,`OUT_STORE_ID`,`PURCHASE_LIMIT`,`CREATE_TIME`,`UPDATE_TIME`,`ACTIVITY_PRICE`,`ORIGINAL_PRICE`
    </sql>


    <!--根据团购商品ID查询门店团购商品-->
    <select id="getGroupActivityGoods" resultMap="BaseResultMap">
        SELECT /*MS-TP-QRORDERING-GROUP-ACTIVITY-GOODS-GETGROUPACTIVITYGOODS*/
        <include refid="Base_Column_List"/>
        FROM tp_qrordering_group_activity_goods
        WHERE
        `out_store_id` = #{outStoreId,jdbcType=INTEGER}
        AND `group_activity_id` = #{groupActivityId,jdbcType=VARCHAR}
        AND `group_activity_goods_id` = #{groupActivityGoodsId,jdbcType=VARCHAR}
        AND del_flag = 0
    </select>
</mapper>
