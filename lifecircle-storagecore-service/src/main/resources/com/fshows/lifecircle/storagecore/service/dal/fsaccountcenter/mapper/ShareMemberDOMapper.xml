<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.ShareMemberDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.ShareMemberDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="ACTIVE" property="active" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SHARE_DESC" property="shareDesc" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="USE_STATUS" property="useStatus" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PROTOCOL_PIC" property="protocolPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SIGN_END_DATE" property="signEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_REASON" property="importReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_STATUS" property="importStatus" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SHARE_MEMBER_ID" property="shareMemberId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SIGN_START_DATE" property="signStartDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="JOIN_TYPE" property="joinType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="AUDIT_TIME" property="auditTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="SHARE_RELATION_TYPE" property="shareRelationType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


    <sql id="Base_Column_List">
    `ID`,`ACTIVE`,`GROUP_ID`,`SHARE_DESC`,`USE_STATUS`,`CUSTOMER_ID`,`AUDIT_STATUS`,`PROTOCOL_PIC`,`SIGN_END_DATE`,`IMPORT_REASON`,`IMPORT_STATUS`,`REJECT_REASON`,`SHARE_MEMBER_ID`,`SIGN_START_DATE`,`JOIN_TYPE`,`AUDIT_TIME`,`SHARE_RELATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_SHARE_MEMBER-->
            <insert id="insert" >
                    INSERT INTO TP_SHARE_MEMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="protocolPic != null">`PROTOCOL_PIC`,</if>
            <if test="signStartDate != null">`SIGN_START_DATE`,</if>
            <if test="signEndDate != null">`SIGN_END_DATE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="importStatus != null">`IMPORT_STATUS`,</if>
            <if test="importReason != null">`IMPORT_REASON`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="protocolPic != null">#{protocolPic,jdbcType=VARCHAR},</if>
            <if test="signStartDate != null">#{signStartDate,jdbcType=VARCHAR},</if>
            <if test="signEndDate != null">#{signEndDate,jdbcType=VARCHAR},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="importStatus != null">#{importStatus,jdbcType=VARCHAR},</if>
            <if test="importReason != null">#{importReason,jdbcType=VARCHAR},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据customerId和groupId查询生效的分账成员-->
            <select id="getByCustomerIdAndGroupId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-SHARE-MEMBER-GETBYCUSTOMERIDANDGROUPID*/  <include refid="Base_Column_List" />
        FROM
        tp_share_member
        WHERE customer_id = #{customerId,jdbcType=VARCHAR}
        AND group_id = #{groupId,jdbcType=VARCHAR}
        AND (active = 'YES' OR active = 'STOP')
        limit 1
            </select>

            <!--批量插入-->
            <insert id="insertBatch" >
                    INSERT INTO tp_share_member
        (
                share_member_id,group_id,customer_id,sign_start_date,sign_end_date,
                audit_status,import_status,import_reason,active,protocol_pic
                )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shareMemberId,jdbcType=VARCHAR},
            #{item.groupId,jdbcType=VARCHAR},
            #{item.customerId,jdbcType=VARCHAR},
            #{item.signStartDate,jdbcType=VARCHAR},
            #{item.signEndDate,jdbcType=VARCHAR},
            #{item.auditStatus,jdbcType=VARCHAR},
            #{item.importStatus,jdbcType=VARCHAR},
            #{item.importReason,jdbcType=VARCHAR},
            #{item.active,jdbcType=VARCHAR},
            #{item.protocolPic,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>

    <!--根据分账组 ID 获得状态正常的分组成员个数-->
    <select id="countBySuccess" resultType="int">
                    SELECT
        COUNT(*)
        FROM
        tp_share_member
        WHERE
        audit_status = 'PASS'
        AND
        active = 'YES'
        AND
        GROUP_ID = #{groupId,jdbcType=VARCHAR}
            </select>

    <!--根据groupId查询分账成员-->
    <select id="getShareMemberByShareMemberIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tp_share_member
        where
        share_member_id in
        <foreach close=")" collection="list" index="index" item="shareMemberId" open="(" separator=",">
            #{shareMemberId,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--根据groupId查询分账成员-->
    <select id="getShareMemberByGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tp_share_member
        where
        group_id = #{groupId,jdbcType=VARCHAR}
    </select>
</mapper>
