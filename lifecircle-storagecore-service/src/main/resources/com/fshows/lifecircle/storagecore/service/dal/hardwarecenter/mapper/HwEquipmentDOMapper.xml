<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="OEM_NAME" property="oemName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SUPPLIER_ID" property="supplierId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EQUIPMENT_PIC" property="equipmentPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EQUIPMENT_FIRM" property="equipmentFirm" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EQUIPMENT_NAME" property="equipmentName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EQUIPMENT_MODEL" property="equipmentModel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_PREFIX" property="equipmentPrefix" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCESS_ORGANIZATION" property="accessOrganization" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_INTRODUCE" property="equipmentIntroduce" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OEM_ID" property="oemId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TP_KEY" property="tpKey" jdbcType="INTEGER"
                    javaType="Integer"/>

    <result column="APP_SHOW" property="appShow" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="PLATFORM" property="platform" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="DETAIL_FLAG" property="detailFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="DEVICE_TYPE" property="deviceType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="RECEIPT_TOOL" property="receiptTool" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="TRADE_CHANNEL" property="tradeChannel" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="EQUIPMENT_TYPE" property="equipmentType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="OTHER_PLATFORM" property="otherPlatform" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IS_UNION_AUTHENTICATION" property="isUnionAuthentication" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="EQUIPMENT_PRICE" property="equipmentPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OEM_NAME`,`OPERATOR`,`JOB_NUMBER`,`SUPPLIER_ID`,`EQUIPMENT_PIC`,`EQUIPMENT_FIRM`,`EQUIPMENT_NAME`,`EQUIPMENT_MODEL`,`EQUIPMENT_PREFIX`,`ACCESS_ORGANIZATION`,`EQUIPMENT_INTRODUCE`,`OEM_ID`,`TP_KEY`,`APP_SHOW`,`PLATFORM`,`DETAIL_FLAG`,`DEVICE_TYPE`,`RECEIPT_TOOL`,`TRADE_CHANNEL`,`EQUIPMENT_TYPE`,`OTHER_PLATFORM`,`IS_UNION_AUTHENTICATION`,`CREATE_TIME`,`UPDATE_TIME`,`EQUIPMENT_PRICE`
    </sql>


            <!--insert:HW_EQUIPMENT-->
            <insert id="insert" >
            INSERT INTO HW_EQUIPMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="jobNumber != null">`JOB_NUMBER`,</if>
        <if test="equipmentPic != null">`EQUIPMENT_PIC`,</if>
        <if test="equipmentFirm != null">`EQUIPMENT_FIRM`,</if>
        <if test="equipmentName != null">`EQUIPMENT_NAME`,</if>
        <if test="equipmentModel != null">`EQUIPMENT_MODEL`,</if>
        <if test="equipmentPrefix != null">`EQUIPMENT_PREFIX`,</if>
        <if test="equipmentIntroduce != null">`EQUIPMENT_INTRODUCE`,</if>
        <if test="appShow != null">`APP_SHOW`,</if>
        <if test="equipmentType != null">`EQUIPMENT_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="equipmentPrice != null">`EQUIPMENT_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
        <if test="equipmentPic != null">#{equipmentPic,jdbcType=VARCHAR},</if>
        <if test="equipmentFirm != null">#{equipmentFirm,jdbcType=VARCHAR},</if>
        <if test="equipmentName != null">#{equipmentName,jdbcType=VARCHAR},</if>
        <if test="equipmentModel != null">#{equipmentModel,jdbcType=VARCHAR},</if>
        <if test="equipmentPrefix != null">#{equipmentPrefix,jdbcType=VARCHAR},</if>
        <if test="equipmentIntroduce != null">#{equipmentIntroduce,jdbcType=VARCHAR},</if>
        <if test="appShow != null">#{appShow,jdbcType=TINYINT},</if>
        <if test="equipmentType != null">#{equipmentType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="equipmentPrice != null">#{equipmentPrice,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据设备Id获取设备信息-->
            <select id="getEquipmentById" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-GETEQUIPMENTBYID*/  <include refid="Base_Column_List" />
        FROM HW_EQUIPMENT
        WHERE id = #{id,jdbcType=INTEGER}
            </select>

            <!--库存清单列表导出-->
    <select id="getExportInventoryDetailedList"
            resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.InventoryDetailedListModelMap">
        SELECT /*MS-HW-EQUIPMENT-GETEXPORTINVENTORYDETAILEDLIST*/ d.systemSn AS systemSn,d.equipmentName AS
        equipmentName,d.equipmentModel AS equipmentModel,d.orderNo AS
        orderNo,d.stockOrder AS stockOrder,o.biz_type AS bizType,o.operate_type AS operateType
        FROM
        (
        SELECT /*MS-HW-EQUIPMENT-GETEXPORTINVENTORYDETAILEDLIST*/
        c.equipmentId,c.equipmentModel,c.equipmentName,c.systemSn,r.order_no AS orderNo,r.stock_order AS
        stockOrder
        FROM
        (
        SELECT /*MS-HW-EQUIPMENT-GETEXPORTINVENTORYDETAILEDLIST*/ e.id as equipmentId , e.equipment_model AS
        equipmentModel,e.equipment_name AS equipmentName,s.system_sn
        AS systemSn,s.id AS snId
        FROM hw_equipment as e
        LEFT JOIN hw_equipment_sn as s
        ON e.id = s.equipment_id
        WHERE s.sn_status = 2
        AND s.is_del = 0
        <if test="depot != null">
            AND s.depot != #{depot, jdbcType=TINYINT}
        </if>
        AND e.id IN
        <foreach close=")" collection="list" index="index" item="equipmentId" open="(" separator=",">
            #{equipmentId,jdbcType=INTEGER}
        </foreach>
        ) c
        LEFT JOIN hw_equipment_order_relation AS r
        ON c.snId = r.sn_id
        WHERE r.is_del = 0
        ) d
        LEFT JOIN hw_equipment_storage_order o
        ON d.orderNo = o.storage_order
        WHERE o.is_del = 0
        AND o.order_type = 1
    </select>

            <!--获取所有设备-->
    <select id="getEquipmentNameList" resultMap="BaseResultMap">
        SELECT /*MS-HW-EQUIPMENT-GETEQUIPMENTNAMELIST*/
        <include refid="Base_Column_List"/>
        FROM hw_equipment
        GROUP BY equipment_name
    </select>

    <!--查询所有设备型号信息-->
    <select id="getAll" resultMap="BaseResultMap">
        select /*MS-HW-EQUIPMENT-GETALL*/
        <include refid="Base_Column_List"/>
        from hw_equipment
    </select>

    <!--根据id获取设备信息-->
    <select id="findEquipmentByIdList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM HW_EQUIPMENT
        WHERE `ID` IN
        <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </select>

    <!--findByInitSnList-->
    <select id="findByInitSnList"
            resultType="com.fshows.lifecircle.storagecore.service.domain.model.InitSnEquipmentModel">
        select /*MS-HW-EQUIPMENT-FINDBYINITSNLIST*/ a.equipment_model,b.init_sn
        from hw_equipment a
        inner join hw_equipment_sn b on a.id=b.equipment_id
        where b.init_sn in
        <foreach collection="list" item="initSn" separator="," open="(" close=")">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
