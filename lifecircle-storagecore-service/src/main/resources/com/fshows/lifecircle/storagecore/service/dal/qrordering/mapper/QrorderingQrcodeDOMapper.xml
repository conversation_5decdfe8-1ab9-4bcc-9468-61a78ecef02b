<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingQrcodeDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingQrcodeDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="QRCODE" property="qrcode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="QRCODE_TYPE" property="qrcodeType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`QRCODE`,`DEL_FLAG`,`QRCODE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_QRORDERING_QRCODE-->
    <insert id="insert">
        INSERT INTO TP_QRORDERING_QRCODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="qrcodeType != null">`QRCODE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="qrcodeType != null">#{qrcodeType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--批量插入-->
    <insert id="insertBatch">
        INSERT INTO TP_QRORDERING_QRCODE(
        QRCODE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.qrcode,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>

    <!--批量插入-->
    <insert id="insertBatchWithQrcodeType">
        INSERT INTO
        TP_QRORDERING_QRCODE
        (
        QRCODE,
        QRCODE_TYPE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.qrcode,jdbcType=VARCHAR},
            #{item.qrcodeType,jdbcType=TINYINT}
            )
        </foreach>
    </insert>
</mapper>
