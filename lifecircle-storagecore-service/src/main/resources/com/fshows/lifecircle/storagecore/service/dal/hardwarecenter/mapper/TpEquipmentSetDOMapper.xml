<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentSetDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentSetDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EQUIPMENT_PIC" property="equipmentPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_MODEL" property="equipmentModel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_INTRODUCE" property="equipmentIntroduce" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APP_SHOW" property="appShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CATEGORY" property="category" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATOR" property="operator" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_INITIAL" property="isInitial" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LEASE_PRICE" property="leasePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MIN_SALE_PRICE" property="minSalePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="equipmentModelMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentModelMap">

                <result column="equipment_model" property="equipmentModel" javaType="String"/>

                <result column="equipment_id" property="equipmentId" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`EQUIPMENT_PIC`,`EQUIPMENT_MODEL`,`EQUIPMENT_INTRODUCE`,`IS_DEL`,`APP_SHOW`,`CATEGORY`,`OPERATOR`,`IS_INITIAL`,`START_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`EQUIPMENT_ID`,`LEASE_PRICE`,`MIN_SALE_PRICE`
    </sql>


            <!--批量查询设备型号-->
            <select id="getEquipmentModelBatch" resultMap="equipmentModelMap">
                SELECT /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELBATCH*/
                eset.equipment_id,
                eset.equipment_model
                FROM
                ( SELECT /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELBATCH*/
                <include refid="Base_Column_List"/>
                FROM tp_equipment_set WHERE start_time &lt; UNIX_TIMESTAMP( NOW( ) ) AND is_del = 0 ORDER BY
                create_time ASC ) eset
                WHERE eset.equipment_id IN
                <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                    #{item,jdbcType=INTEGER}
                </foreach>
                GROUP BY
                eset.equipment_id
            </select>

    <!--根据设备id获取硬件型号列表-->
    <select id="getEquipmentModelById" resultMap="BaseResultMap">
        SELECT /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELBYID*/ id,equipment_model from
        (SELECT /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELBYID*/ eset.* from tp_equipment eq
        LEFT JOIN tp_equipment_set eset on eq.id = eset.equipment_id
        where eq.equipment_name_id = #{equipmentId,jdbcType=INTEGER}
        and start_time &lt; UNIX_TIMESTAMP(NOW()) and eset.is_del = 0 and eq.is_del = 0
        ORDER BY eset.create_time DESC) e
        GROUP BY e.equipment_id
    </select>

    <!--查询所有设备型号列表-->
    <select id="getEquipmentModelList" resultMap="BaseResultMap">
        select /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELLIST*/ id,equipment_id,equipment_model
        from
        (select /*MS-TP-EQUIPMENT-SET-GETEQUIPMENTMODELLIST*/ id,equipment_id,equipment_model
        from tp_equipment_set
        where start_time <![CDATA[<]]> unix_timestamp(now()) and is_del =0
        ORDER BY create_time DESC) e
        GROUP BY equipment_id
    </select>
</mapper>
