<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.mapper.PgShareBillFileDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.dataobject.PgShareBillFileDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="STATE" property="state" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CHANNEL" property="channel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="HANDLE_DATE" property="handleDate" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="SHARE_AMOUNT" property="shareAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="SHARE_INCOME" property="shareIncome" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`STATE`,`FILE_URL`,`CHANNEL`,`HANDLE_DATE`,`CREATE_TIME`,`UPDATE_TIME`,`SHARE_AMOUNT`,`SHARE_INCOME`
    </sql>


    <!--insert:PG_SHARE_BILL_FILE-->
    <insert id="insert">
        INSERT INTO PG_SHARE_BILL_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="state != null">`STATE`,</if>
            <if test="fileUrl != null">`FILE_URL`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="handleDate != null">`HANDLE_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="shareAmount != null">`SHARE_AMOUNT`,</if>
            <if test="shareIncome != null">`SHARE_INCOME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="state != null">#{state,jdbcType=VARCHAR},</if>
            <if test="fileUrl != null">#{fileUrl,jdbcType=VARCHAR},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="handleDate != null">#{handleDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="shareAmount != null">#{shareAmount,jdbcType=DECIMAL},</if>
            <if test="shareIncome != null">#{shareIncome,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--通过handle_date和channel来修改state-->
    <update id="updateStateAndAmountByHandleDateAndChannel">
        update /*MS-PG-SHARE-BILL-FILE-UPDATESTATEANDAMOUNTBYHANDLEDATEANDCHANNEL*/ PG_SHARE_BILL_FILE
        set state        = #{state,jdbcType=VARCHAR},
            SHARE_AMOUNT = #{shareAmount,jdbcType=DECIMAL},
            SHARE_INCOME = #{shareIncome,jdbcType=DECIMAL}
        where handle_date = #{handleDate,jdbcType=INTEGER}
          and channel = #{channel,jdbcType=TINYINT}
    </update>

    <!--通过handle_date,channel获取分账结算单-->
    <select id="getByHandleDateAndChannel" resultMap="BaseResultMap">
        select /*MS-PG-SHARE-BILL-FILE-GETBYHANDLEDATEANDCHANNEL*/
        <include refid="Base_Column_List"/>
        from PG_SHARE_BILL_FILE
        where
        handle_date = #{handleDate,jdbcType=INTEGER}
        and
        channel = #{channel,jdbcType=TINYINT}
        limit 1
    </select>
</mapper>
