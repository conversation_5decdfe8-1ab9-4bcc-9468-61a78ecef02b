<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardConsumeDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardConsumeDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="APPID" property="appid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT01" property="ext01" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT02" property="ext02" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT03" property="ext03" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT04" property="ext04" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT05" property="ext05" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT06" property="ext06" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OPEN_ID" property="openId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UNION_ID" property="unionId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FUBEI_UNION_ID" property="fubeiUnionId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LEVEL01_ORG_ID" property="level01OrgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LEVEL02_ORG_ID" property="level02OrgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LEVEL03_ORG_ID" property="level03OrgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LEVEL04_ORG_ID" property="level04OrgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_STATUS" property="payStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="REFUND_TIME" property="refundTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="FEE" property="fee" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPID`,`EXT01`,`EXT02`,`EXT03`,`EXT04`,`EXT05`,`EXT06`,`TOKEN`,`CARD_NO`,`OPEN_ID`,`ORDER_SN`,`UNION_ID`,`FUBEI_UNION_ID`,`LEVEL01_ORG_ID`,`LEVEL02_ORG_ID`,`LEVEL03_ORG_ID`,`LEVEL04_ORG_ID`,`PUBLISH_ORG_ID`,`UID`,`IS_DEL`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`ORDER_TYPE`,`PAY_STATUS`,`REFUND_TIME`,`REFUND_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`ORDER_PRICE`,`REFUND_MONEY`
    </sql>


    <!--insert:TP_PREPAY_CARD_CONSUME-->
    <insert id="insert">
        INSERT INTO TP_PREPAY_CARD_CONSUME
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="ext01 != null">`EXT01`,</if>
            <if test="ext02 != null">`EXT02`,</if>
            <if test="ext03 != null">`EXT03`,</if>
            <if test="ext04 != null">`EXT04`,</if>
            <if test="ext05 != null">`EXT05`,</if>
            <if test="ext06 != null">`EXT06`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="fubeiUnionId != null">`FUBEI_UNION_ID`,</if>
            <if test="level01OrgId != null">`LEVEL01_ORG_ID`,</if>
            <if test="level02OrgId != null">`LEVEL02_ORG_ID`,</if>
            <if test="level03OrgId != null">`LEVEL03_ORG_ID`,</if>
            <if test="level04OrgId != null">`LEVEL04_ORG_ID`,</if>
            <if test="publishOrgId != null">`PUBLISH_ORG_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="refundTime != null">`REFUND_TIME`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="refundMoney != null">`REFUND_MONEY`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="ext01 != null">#{ext01,jdbcType=VARCHAR},</if>
            <if test="ext02 != null">#{ext02,jdbcType=VARCHAR},</if>
            <if test="ext03 != null">#{ext03,jdbcType=VARCHAR},</if>
            <if test="ext04 != null">#{ext04,jdbcType=VARCHAR},</if>
            <if test="ext05 != null">#{ext05,jdbcType=VARCHAR},</if>
            <if test="ext06 != null">#{ext06,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
            <if test="fubeiUnionId != null">#{fubeiUnionId,jdbcType=VARCHAR},</if>
            <if test="level01OrgId != null">#{level01OrgId,jdbcType=VARCHAR},</if>
            <if test="level02OrgId != null">#{level02OrgId,jdbcType=VARCHAR},</if>
            <if test="level03OrgId != null">#{level03OrgId,jdbcType=VARCHAR},</if>
            <if test="level04OrgId != null">#{level04OrgId,jdbcType=VARCHAR},</if>
            <if test="publishOrgId != null">#{publishOrgId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="refundTime != null">#{refundTime,jdbcType=INTEGER},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--查询预付卡消费记录列表-->
    <select id="findPrepayCardConsumeList" resultMap="BaseResultMap">
        SELECT /*MS-TP-PREPAY-CARD-CONSUME-FINDPREPAYCARDCONSUMELIST*/
        <include refid="Base_Column_List"/>
        FROM `tp_prepay_card_consume`
        WHERE `publish_org_id` in
        <foreach close=")" collection="list" index="index" item="publishOrgId" open="(" separator=",">
            #{publishOrgId, jdbcType=VARCHAR}
        </foreach>
        and create_time <![CDATA[ >= ]]> #{startTime, jdbcType=TIMESTAMP}
        and create_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
    </select>
</mapper>
