<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmChannelPolicySettlementGroupDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmChannelPolicySettlementGroupDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_ID" property="policyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GROUP_ID`,`POLICY_ID`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--insertBatch-->
            <insert id="insertBatch" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP(GROUP_ID,POLICY_ID) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.policyId,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>

            <!--根据政策ID和用户ID查询结算组信息-->
            <select id="getByPolicyIdAndUserId" resultMap="BaseResultMap">
                    select /*MS-LM-CRM-CHANNEL-POLICY-SETTLEMENT-GROUP-GETBYPOLICYIDANDUSERID*/ t1.group_id
        from lm_crm_channel_policy_settlement_group t1
        left join lm_crm_channel_policy_settlement_group_member t2
        on t1.group_id = t2.group_id
        where t1.policy_id = #{policyId,jdbcType=VARCHAR}
        and t2.user_id = #{userId,jdbcType=INTEGER}
        and t1.is_del = 0
        and t2.is_del = 0
        limit 1
            </select>
    </mapper>
