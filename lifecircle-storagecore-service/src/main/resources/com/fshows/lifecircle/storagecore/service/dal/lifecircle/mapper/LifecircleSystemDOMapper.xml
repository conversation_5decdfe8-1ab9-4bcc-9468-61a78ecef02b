<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleSystemDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleSystemDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="KEYS" property="keys" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TITLE" property="title" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MESSAGES" property="messages" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STR_VALUES" property="strValues" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WHITE_LIST" property="whiteList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VALUES" property="values" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`KEYS`,`TITLE`,`MESSAGES`,`STR_VALUES`,`WHITE_LIST`,`VALUES`
    </sql>


            <!--insert:TP_LIFECIRCLE_SYSTEM-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_SYSTEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="keys != null">`KEYS`,</if>
        <if test="title != null">`TITLE`,</if>
        <if test="messages != null">`MESSAGES`,</if>
        <if test="strValues != null">`STR_VALUES`,</if>
        <if test="whiteList != null">`WHITE_LIST`,</if>
        <if test="values != null">`VALUES`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="keys != null">#{keys,jdbcType=VARCHAR},</if>
        <if test="title != null">#{title,jdbcType=VARCHAR},</if>
        <if test="messages != null">#{messages,jdbcType=VARCHAR},</if>
        <if test="strValues != null">#{strValues,jdbcType=VARCHAR},</if>
        <if test="whiteList != null">#{whiteList,jdbcType=VARCHAR},</if>
        <if test="values != null">#{values,jdbcType=TINYINT},</if>
    </trim>
            </insert>

            <!--根据 keys 查询记录-->
            <select id="getByKeys" resultMap="BaseResultMap">
                    SELECT
        id
        ,`keys`
        ,title
        ,`values`
        ,messages
        ,str_values
        ,white_list
        FROM tp_lifecircle_system
        WHERE
        `keys` = #{keys,jdbcType=VARCHAR}
        limit 1
            </select>
    </mapper>
