<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleAccountChangeLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleAccountChangeLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="MEMO" property="memo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OP_TYPE" property="opType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANGE_TYPE" property="changeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WITHDRAW_ID" property="withdrawId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BALANCE" property="balance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_BALANCE" property="afterBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FROZEN_AMOUNT" property="frozenAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AVAILABLE_BALANCE" property="availableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_FROZEN_AMOUNT" property="afterFrozenAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_AVAILABLE_BALANCE" property="afterAvailableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`MEMO`,`TOKEN`,`ORDER_SN`,`REFUND_SN`,`UID`,`OP_TYPE`,`CHANGE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`WITHDRAW_ID`,`BALANCE`,`AFTER_BALANCE`,`FROZEN_AMOUNT`,`AVAILABLE_BALANCE`,`AFTER_FROZEN_AMOUNT`,`AFTER_AVAILABLE_BALANCE`
    </sql>


            <!--insert:TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="memo != null">`MEMO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="opType != null">`OP_TYPE`,</if>
        <if test="changeType != null">`CHANGE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="withdrawId != null">`WITHDRAW_ID`,</if>
        <if test="balance != null">`BALANCE`,</if>
        <if test="afterBalance != null">`AFTER_BALANCE`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="availableBalance != null">`AVAILABLE_BALANCE`,</if>
        <if test="afterFrozenAmount != null">`AFTER_FROZEN_AMOUNT`,</if>
        <if test="afterAvailableBalance != null">`AFTER_AVAILABLE_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="memo != null">#{memo,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="opType != null">#{opType,jdbcType=TINYINT},</if>
        <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="withdrawId != null">#{withdrawId,jdbcType=INTEGER},</if>
        <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        <if test="afterBalance != null">#{afterBalance,jdbcType=DECIMAL},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=DECIMAL},</if>
        <if test="availableBalance != null">#{availableBalance,jdbcType=DECIMAL},</if>
        <if test="afterFrozenAmount != null">#{afterFrozenAmount,jdbcType=DECIMAL},</if>
        <if test="afterAvailableBalance != null">#{afterAvailableBalance,jdbcType=DECIMAL},</if>
    </trim>
            </insert>
    </mapper>
