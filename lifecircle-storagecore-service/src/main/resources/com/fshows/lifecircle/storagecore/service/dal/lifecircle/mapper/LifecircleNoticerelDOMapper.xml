<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleNoticerelDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleNoticerelDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="POSTER_URL" property="posterUrl" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="TEMPLATE_POSTER_URL" property="templatePosterUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USERS_ID" property="usersId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NOTICE_ID" property="noticeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHILDREN_ID" property="childrenId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_RECOMMEND" property="isRecommend" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`POSTER_URL`,`TEMPLATE_POSTER_URL`,`SORT`,`IS_SHOW`,`STATUS`,`STORE_ID`,`USERS_ID`,`NOTICE_ID`,`CHILDREN_ID`,`CREATE_TIME`,`IS_RECOMMEND`,`ACTIVITY_TYPE`
    </sql>


            <!--insert:TP_LIFECIRCLE_NOTICEREL-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_NOTICEREL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="posterUrl != null">`POSTER_URL`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="usersId != null">`USERS_ID`,</if>
            <if test="noticeId != null">`NOTICE_ID`,</if>
            <if test="childrenId != null">`CHILDREN_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isRecommend != null">`IS_RECOMMEND`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="posterUrl != null">#{posterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="usersId != null">#{usersId,jdbcType=INTEGER},</if>
            <if test="noticeId != null">#{noticeId,jdbcType=INTEGER},</if>
            <if test="childrenId != null">#{childrenId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isRecommend != null">#{isRecommend,jdbcType=TINYINT},</if>
            <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
        </trim>
            </insert>

            <!--根据活动 ID 获得海报地址列表-->
            <select id="findPosterUrlByActivityId" resultType="String">
                    SELECT
        poster_url
        FROM
        TP_LIFECIRCLE_NOTICEREL
        WHERE
        notice_id = #{noticeId,jdbcType=INTEGER}
            </select>
    </mapper>
