<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopRefundOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopRefundOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_REFUND_ORDER_SN" property="hwRefundOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_TYPE" property="refundType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_SCORE" property="refundScore" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_NUMBER" property="refundNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ALL_MATERIAL" property="isAllMaterial" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_PAY_TIME" property="refundPayTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_ORDER_STATUS" property="refundOrderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FINAL_GOODS_SUMPRICE" property="finalGoodsSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_BALANCE_PRICE" property="refundBalancePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_PAYMENT_PRICE" property="refundPaymentPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_GOODS_SUMPRICE" property="refundGoodsSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REAL_NAME`,`USERNAME`,`HW_ORDER_SN`,`JOB_NUMBER`,`HW_REFUND_ORDER_SN`,`IS_DEL`,`IS_TEST`,`USER_ID`,`USER_TYPE`,`REFUND_TYPE`,`REFUND_SCORE`,`REFUND_NUMBER`,`IS_ALL_MATERIAL`,`REFUND_PAY_TIME`,`REFUND_ORDER_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`FINAL_GOODS_SUMPRICE`,`REFUND_BALANCE_PRICE`,`REFUND_PAYMENT_PRICE`,`REFUND_GOODS_SUMPRICE`
    </sql>


            <!--insert:HW_SHOP_REFUND_ORDER-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_REFUND_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="hwRefundOrderSn != null">`HW_REFUND_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="refundType != null">`REFUND_TYPE`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="refundPayTime != null">`REFUND_PAY_TIME`,</if>
            <if test="refundOrderStatus != null">`REFUND_ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finalGoodsSumprice != null">`FINAL_GOODS_SUMPRICE`,</if>
            <if test="refundBalancePrice != null">`REFUND_BALANCE_PRICE`,</if>
            <if test="refundPaymentPrice != null">`REFUND_PAYMENT_PRICE`,</if>
            <if test="refundGoodsSumprice != null">`REFUND_GOODS_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="hwRefundOrderSn != null">#{hwRefundOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="refundType != null">#{refundType,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="refundPayTime != null">#{refundPayTime,jdbcType=INTEGER},</if>
            <if test="refundOrderStatus != null">#{refundOrderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finalGoodsSumprice != null">#{finalGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="refundBalancePrice != null">#{refundBalancePrice,jdbcType=DECIMAL},</if>
            <if test="refundPaymentPrice != null">#{refundPaymentPrice,jdbcType=DECIMAL},</if>
            <if test="refundGoodsSumprice != null">#{refundGoodsSumprice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--分页查询退货订单页面 pageCount-->
            <select id="findRefundOrderPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 `hw_shop_refund_order`
        <where>
            is_del= 0
            <if test="hwOrderSn != null and hwOrderSn != '' ">
                AND hw_order_sn = #{hwOrderSn, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="refundOrderStatus != null and refundOrderStatus != 0 ">
                AND refund_order_status= #{refundOrderStatus, jdbcType=INTEGER}
            </if>
            <if test="isTest != null">
                AND is_test= #{isTest, jdbcType=INTEGER}
            </if>
            <if test="startDate != null">
                AND `CREATE_TIME` <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND `CREATE_TIME` <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="username != null and username != '' ">
                AND username = #{username, jdbcType=VARCHAR}
            </if>
            <if test="hwRefundOrderSnList != null and hwRefundOrderSnList.size() &gt; 0">
                AND hw_refund_order_sn IN
                <foreach collection="hwRefundOrderSnList" open="(" close=")" item="hwRefundOrderSn" separator=",">
                    #{hwRefundOrderSn,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        
            </select>
            <!--分页查询退货订单页面 pageResult-->
            <select id="findRefundOrderPageResult"  resultMap="BaseResultMap">
                    SELECT /*MS-HW-SHOP-REFUND-ORDER-FINDREFUNDORDERPAGE*/  <include refid="Base_Column_List" />
        FROM `hw_shop_refund_order`
        <where>
            is_del= 0
            <if test="hwOrderSn != null and hwOrderSn != '' ">
                AND hw_order_sn = #{hwOrderSn, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="refundOrderStatus != null and refundOrderStatus != 0 ">
                AND refund_order_status= #{refundOrderStatus, jdbcType=INTEGER}
            </if>
            <if test="isTest != null">
                AND is_test= #{isTest, jdbcType=INTEGER}
            </if>
            <if test="startDate != null">
                AND `CREATE_TIME` <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND `CREATE_TIME` <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="username != null and username != '' ">
                AND username = #{username, jdbcType=VARCHAR}
            </if>
            <if test="hwRefundOrderSnList != null and hwRefundOrderSnList.size() &gt; 0">
                AND hw_refund_order_sn IN
                <foreach collection="hwRefundOrderSnList" open="(" close=")" item="hwRefundOrderSn" separator=",">
                    #{hwRefundOrderSn,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>
    </mapper>
