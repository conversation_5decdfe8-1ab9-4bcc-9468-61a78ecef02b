<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtraCrmLossStoreDOMapper">

    <resultMap id="BaseResultMap"  type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.ExtCrmLossStoreDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>
        <result column="TOKEN" property="token" jdbcType="VARCHAR" javaType="String"/>
        <result column="UID" property="uid" jdbcType="INTEGER" javaType="Integer"/>
        <result column="IS_DEL" property="isDel" jdbcType="TINYINT" javaType="Integer"/>
        <result column="STORE_ID" property="storeId" jdbcType="INTEGER" javaType="Integer"/>
        <result column="OFF_PERCENT" property="offPercent" jdbcType="INTEGER" javaType="Integer"/>
        <result column="STATISTICS_DAY" property="statisticsDay" jdbcType="INTEGER" javaType="Integer"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="CURRENT_DAY_AMOUNT" property="currentDayAmount" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result column="LAST_WEEK_AVERAGE_DAILY" property="lastWeekAverageDaily" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        ID,TOKEN,UID,IS_DEL,STORE_ID,OFF_PERCENT,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,CURRENT_DAY_AMOUNT,LAST_WEEK_AVERAGE_DAILY
    </sql>

    <!--根据门店ID集合和上周日均交易额以及掉量百分比过滤数据信息 pageResult-->
    <select id="getLossStorePageByStoreIdAndAmountAndOffPercentResult"  resultMap="BaseResultMap">
        SELECT /*MS-LM-CRM-LOSS-STORE-GETLOSSSTOREPAGEBYSTOREIDANDAMOUNTANDOFFPERCENT*/  <include refid="Base_Column_List" /> FROM LM_CRM_LOSS_STORE
        WHERE store_id in
        <foreach collection="storeIds" item="storeId" open="(" separator="," close=")">
            #{storeId, jdbcType=INTEGER}
        </foreach>
        and statistics_day = #{statisticalDay,jdbcType=BIGINT}
        and is_del = 0
        and last_week_average_daily &gt;= #{lastWeekAverageDaily,jdbcType=INTEGER}
        and off_percent &gt;= #{offPercent,jdbcType=INTEGER}
        <if test="sortType != null and sortType == 1">
            ORDER BY off_percent DESC
        </if>
        <if test="sortType != null and sortType == 2">
            ORDER BY last_week_average_daily DESC
        </if>
    </select>
</mapper>
