<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BlocOrganizeInfoDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BlocOrganizeInfoDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CODE" property="code" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORG_NAME" property="orgName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FULL_PATH" property="fullPath" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PARENT_ID" property="parentId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FULL_NAME_PATH" property="fullNamePath" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ORG_TYPE" property="orgType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`CODE`,`ORG_ID`,`BLOC_ID`,`ORG_NAME`,`FULL_PATH`,`PARENT_ID`,`FULL_NAME_PATH`,`IS_DEL`,`ORG_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--根据orgId获取出来组织信息-->
    <select id="getOneByOrgId" resultMap="BaseResultMap">
        select /*MS-TP-BLOC-ORGANIZE-INFO-GETONEBYORGID*/
        <include refid="Base_Column_List"/>
        from tp_bloc_organize_info where org_id = #{orgId, jdbcType=VARCHAR} limit 1
    </select>
</mapper>
