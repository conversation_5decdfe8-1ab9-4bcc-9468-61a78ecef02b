<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleAccountDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleAccountDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDCARD" property="idcard" jdbcType="CHAR"
        javaType="String"/>

            <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_BACK" property="cardBack" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_TIME" property="cardTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISCRIBE" property="discribe" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_PLACE" property="cardPlace" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSEID" property="licenseid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_ID" property="categoryId" jdbcType="CHAR"
        javaType="String"/>

            <result column="CUST_ACCT_ID" property="custAcctId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDCARDPHOTO" property="idcardphoto" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_IMG" property="identityImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON" property="legalPerson" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSENAME" property="licensename" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSEPHOTO" property="licensephoto" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESIDENCE_ADDRESS" property="residenceAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTH_TIME" property="authTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ERR_COUNT" property="errCount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SUBMIT" property="isSubmit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTH_STATUS" property="authStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HANDLE_TIME" property="handleTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UNITY_CAT_ID" property="unityCatId" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXAMINE_TYPE" property="examineType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="JOIN_CHANNEL" property="joinChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CUST_ACCT_TIME" property="custAcctTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LICENSEROUND" property="licenseround" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="VERIFY_STATUS" property="verifyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TODAY_INCOME_UPDATE" property="todayIncomeUpdate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_IDENTITY_TYPE" property="settleIdentityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REVIEW_ACCOUNT_REJECTION_STATUS" property="reviewAccountRejectionStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BALANCE" property="balance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="IMBALANCE" property="imbalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PREV_AMOUNT" property="prevAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TODAY_INCOME" property="todayIncome" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FROZEN_AMOUNT" property="frozenAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COMMISSION_RATE" property="commissionRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AVAILABLE_BALANCE" property="availableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`IDCARD`,`MOBILE`,`CARD_BACK`,`CARD_TIME`,`DISCRIBE`,`CARD_PLACE`,`LICENSEID`,`CATEGORY_ID`,`CUST_ACCT_ID`,`ACCOUNT_NAME`,`IDCARDPHOTO`,`IDENTITY_IMG`,`LEGAL_PERSON`,`LICENSENAME`,`LICENSEPHOTO`,`RESIDENCE_ADDRESS`,`UID`,`TYPE`,`AUTH_TIME`,`ERR_COUNT`,`IS_SUBMIT`,`AUTH_STATUS`,`HANDLE_TIME`,`SETTLE_MODE`,`UNITY_CAT_ID`,`UPDATE_TIME`,`EXAMINE_TYPE`,`JOIN_CHANNEL`,`CUST_ACCT_TIME`,`LICENSEROUND`,`VERIFY_STATUS`,`TODAY_INCOME_UPDATE`,`SETTLE_IDENTITY_TYPE`,`REVIEW_ACCOUNT_REJECTION_STATUS`,`CREATE_TIME`,`BALANCE`,`IMBALANCE`,`PREV_AMOUNT`,`TODAY_INCOME`,`TOTAL_AMOUNT`,`FROZEN_AMOUNT`,`COMMISSION_RATE`,`AVAILABLE_BALANCE`
    </sql>


            <!--insert:TP_LIFECIRCLE_ACCOUNT-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="imbalance != null">`IMBALANCE`,</if>
            <if test="prevAmount != null">`PREV_AMOUNT`,</if>
            <if test="todayIncome != null">`TODAY_INCOME`,</if>
            <if test="totalAmount != null">`TOTAL_AMOUNT`,</if>
            <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
            <if test="commissionRate != null">`COMMISSION_RATE`,</if>
            <if test="availableBalance != null">`AVAILABLE_BALANCE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="idcard != null">`IDCARD`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="cardBack != null">`CARD_BACK`,</if>
            <if test="cardTime != null">`CARD_TIME`,</if>
            <if test="discribe != null">`DISCRIBE`,</if>
            <if test="cardPlace != null">`CARD_PLACE`,</if>
            <if test="licenseid != null">`LICENSEID`,</if>
            <if test="categoryId != null">`CATEGORY_ID`,</if>
            <if test="custAcctId != null">`CUST_ACCT_ID`,</if>
            <if test="accountName != null">`ACCOUNT_NAME`,</if>
            <if test="idcardphoto != null">`IDCARDPHOTO`,</if>
            <if test="identityImg != null">`IDENTITY_IMG`,</if>
            <if test="legalPerson != null">`LEGAL_PERSON`,</if>
            <if test="licensename != null">`LICENSENAME`,</if>
            <if test="licensephoto != null">`LICENSEPHOTO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="errCount != null">`ERR_COUNT`,</if>
            <if test="isSubmit != null">`IS_SUBMIT`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="handleTime != null">`HANDLE_TIME`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="examineType != null">`EXAMINE_TYPE`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="custAcctTime != null">`CUST_ACCT_TIME`,</if>
            <if test="licenseround != null">`LICENSEROUND`,</if>
            <if test="verifyStatus != null">`VERIFY_STATUS`,</if>
            <if test="todayIncomeUpdate != null">`TODAY_INCOME_UPDATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="imbalance != null">#{imbalance,jdbcType=DECIMAL},</if>
            <if test="prevAmount != null">#{prevAmount,jdbcType=DECIMAL},</if>
            <if test="todayIncome != null">#{todayIncome,jdbcType=DECIMAL},</if>
            <if test="totalAmount != null">#{totalAmount,jdbcType=DECIMAL},</if>
            <if test="frozenAmount != null">#{frozenAmount,jdbcType=DECIMAL},</if>
            <if test="commissionRate != null">#{commissionRate,jdbcType=DECIMAL},</if>
            <if test="availableBalance != null">#{availableBalance,jdbcType=DECIMAL},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="idcard != null">#{idcard,jdbcType=CHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="cardBack != null">#{cardBack,jdbcType=VARCHAR},</if>
            <if test="cardTime != null">#{cardTime,jdbcType=VARCHAR},</if>
            <if test="discribe != null">#{discribe,jdbcType=VARCHAR},</if>
            <if test="cardPlace != null">#{cardPlace,jdbcType=VARCHAR},</if>
            <if test="licenseid != null">#{licenseid,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=CHAR},</if>
            <if test="custAcctId != null">#{custAcctId,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="idcardphoto != null">#{idcardphoto,jdbcType=VARCHAR},</if>
            <if test="identityImg != null">#{identityImg,jdbcType=VARCHAR},</if>
            <if test="legalPerson != null">#{legalPerson,jdbcType=VARCHAR},</if>
            <if test="licensename != null">#{licensename,jdbcType=VARCHAR},</if>
            <if test="licensephoto != null">#{licensephoto,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=INTEGER},</if>
            <if test="errCount != null">#{errCount,jdbcType=INTEGER},</if>
            <if test="isSubmit != null">#{isSubmit,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="handleTime != null">#{handleTime,jdbcType=INTEGER},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="examineType != null">#{examineType,jdbcType=TINYINT},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="custAcctTime != null">#{custAcctTime,jdbcType=INTEGER},</if>
            <if test="licenseround != null">#{licenseround,jdbcType=SMALLINT},</if>
            <if test="verifyStatus != null">#{verifyStatus,jdbcType=TINYINT},</if>
            <if test="todayIncomeUpdate != null">#{todayIncomeUpdate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据 uid 列表获得 token 列表-->
            <select id="batchFindTokenByUidList" resultType="String">
                    SELECT
        token
        FROM
        tp_lifecircle_account
        <where>
            uid IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </where>
            </select>

            <!--根据商户token更新商户的总余额-->
            <update id="updateBalanceUpdateTimeByToken" >
                    UPDATE /*MS-TP-LIFECIRCLE-ACCOUNT-UPDATEBALANCEUPDATETIMEBYTOKEN*/ TP_LIFECIRCLE_ACCOUNT SET balance = balance - #{tradeMoney,jdbcType=DECIMAL}, update_time = #{updateTime,jdbcType=INTEGER} WHERE token = #{token,jdbcType=VARCHAR}
            </update>

            <!--根据用户token 查询 ACCOUNT 信息-->
            <select id="getByTokenForUpdate" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-ACCOUNT-GETBYTOKENFORUPDATE*/  <include refid="Base_Column_List" /> FROM TP_LIFECIRCLE_ACCOUNT WHERE TOKEN = #{token,jdbcType=VARCHAR} FOR UPDATE
            </select>

            <!--根据用户 ID 查询 ACCOUNT 信息-->
            <select id="getByUid" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM TP_LIFECIRCLE_ACCOUNT
        WHERE
        UID = #{uid,jdbcType=INTEGER}
            </select>
    </mapper>
