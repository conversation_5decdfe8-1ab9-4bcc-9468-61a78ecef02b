<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ServiceProviderChannelDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ServiceProviderChannelDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="SPC_ID" property="spcId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVE" property="active" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USER_ID" property="userId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_ACCOUNT_ID" property="outAccountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXTEND_CONFIGURATION" property="extendConfiguration" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_SYNC" property="isSync" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ROLE_TYPE" property="roleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEFAULT" property="isDefault" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIG_STATUS" property="configStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_AFFILIATION" property="isAffiliation" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FUND_SETTLE_MODE" property="fundSettleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WITHDRAWAL_MODE" property="withdrawalMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PROFIT_SHARE_MODE" property="profitShareMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ENABLE_ACCOUNT_WALLET" property="enableAccountWallet" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SPC_ID`,`ACTIVE`,`USER_ID`,`CHANNEL_CODE`,`OUT_ACCOUNT_ID`,`EXTEND_CONFIGURATION`,`IS_SYNC`,`DEL_FLAG`,`ROLE_TYPE`,`IS_DEFAULT`,`CONFIG_STATUS`,`IS_AFFILIATION`,`FUND_SETTLE_MODE`,`WITHDRAWAL_MODE`,`PROFIT_SHARE_MODE`,`ENABLE_ACCOUNT_WALLET`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_SERVICE_PROVIDER_CHANNEL-->
            <insert id="insert" >
            INSERT INTO ACC_SERVICE_PROVIDER_CHANNEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="active != null">`ACTIVE`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="outAccountId != null">`OUT_ACCOUNT_ID`,</if>
        <if test="extendConfiguration != null">`EXTEND_CONFIGURATION`,</if>
        <if test="isSync != null">`IS_SYNC`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="roleType != null">`ROLE_TYPE`,</if>
        <if test="isDefault != null">`IS_DEFAULT`,</if>
        <if test="configStatus != null">`CONFIG_STATUS`,</if>
        <if test="isAffiliation != null">`IS_AFFILIATION`,</if>
        <if test="fundSettleMode != null">`FUND_SETTLE_MODE`,</if>
        <if test="withdrawalMode != null">`WITHDRAWAL_MODE`,</if>
        <if test="profitShareMode != null">`PROFIT_SHARE_MODE`,</if>
        <if test="enableAccountWallet != null">`ENABLE_ACCOUNT_WALLET`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="active != null">#{active,jdbcType=VARCHAR},</if>
        <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="outAccountId != null">#{outAccountId,jdbcType=VARCHAR},</if>
        <if test="extendConfiguration != null">#{extendConfiguration,jdbcType=VARCHAR},</if>
        <if test="isSync != null">#{isSync,jdbcType=TINYINT},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
        <if test="isDefault != null">#{isDefault,jdbcType=TINYINT},</if>
        <if test="configStatus != null">#{configStatus,jdbcType=TINYINT},</if>
        <if test="isAffiliation != null">#{isAffiliation,jdbcType=TINYINT},</if>
        <if test="fundSettleMode != null">#{fundSettleMode,jdbcType=TINYINT},</if>
        <if test="withdrawalMode != null">#{withdrawalMode,jdbcType=TINYINT},</if>
        <if test="profitShareMode != null">#{profitShareMode,jdbcType=TINYINT},</if>
        <if test="enableAccountWallet != null">#{enableAccountWallet,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据spcId查询渠道配置信息-->
            <select id="getBySpcId" resultMap="BaseResultMap">
                    select /*MS-ACC-SERVICE-PROVIDER-CHANNEL-GETBYSPCID*/ <include refid="Base_Column_List" />
        from acc_service_provider_channel
        where spc_id = #{spcId,jdbcType=VARCHAR}
        and active = 'YES' and del_flag = 1
        limit 1
            </select>
    </mapper>
