<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentWorkBatchDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentWorkBatchDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WORK_BATCH_NO" property="workBatchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_FILE_URL" property="importFileUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NUMBER" property="operatorNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OPERATOR`,`WORK_BATCH_NO`,`IMPORT_FILE_URL`,`OPERATOR_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_WORK_BATCH-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_WORK_BATCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="workBatchNo != null">`WORK_BATCH_NO`,</if>
            <if test="importFileUrl != null">`IMPORT_FILE_URL`,</if>
            <if test="operatorNumber != null">`OPERATOR_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="workBatchNo != null">#{workBatchNo,jdbcType=VARCHAR},</if>
            <if test="importFileUrl != null">#{importFileUrl,jdbcType=VARCHAR},</if>
            <if test="operatorNumber != null">#{operatorNumber,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>
    </mapper>
