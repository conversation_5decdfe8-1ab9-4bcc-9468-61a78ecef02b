<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingActivityGoodsDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingActivityGoodsDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="UNIT" property="unit" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SPU_ID" property="spuId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PICTURE" property="picture" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_STORE_ID" property="outStoreId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ONLINE_SPU_ID" property="onlineSpuId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACTIVITY_GOODS_ID" property="activityGoodsId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PURCHASE_LIMIT" property="purchaseLimit" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="ACTIVITY_PRICE" property="activityPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`UNIT`,`SPU_ID`,`PICTURE`,`GOODS_NAME`,`ACTIVITY_ID`,`OUT_STORE_ID`,`DESCRIPTION`,`ONLINE_SPU_ID`,`ACTIVITY_GOODS_ID`,`UID`,`DEL_FLAG`,`PURCHASE_LIMIT`,`CREATE_TIME`,`UPDATE_TIME`,`ACTIVITY_PRICE`,`ORIGINAL_PRICE`
    </sql>


        <!--insert:TP_QRORDERING_ACTIVITY_GOODS-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_ACTIVITY_GOODS
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="unit != null">`UNIT`,</if>
                <if test="picture != null">`PICTURE`,</if>
                <if test="goodsName != null">`GOODS_NAME`,</if>
                <if test="activityId != null">`ACTIVITY_ID`,</if>
                <if test="description != null">`DESCRIPTION`,</if>
                <if test="activityGoodsId != null">`ACTIVITY_GOODS_ID`,</if>
                <if test="uid != null">`UID`,</if>
                <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="purchaseLimit != null">`PURCHASE_LIMIT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="activityPrice != null">`ACTIVITY_PRICE`,</if>
            <if test="originalPrice != null">`ORIGINAL_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="unit != null">#{unit,jdbcType=VARCHAR},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="activityGoodsId != null">#{activityGoodsId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="purchaseLimit != null">#{purchaseLimit,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="activityPrice != null">#{activityPrice,jdbcType=DECIMAL},</if>
            <if test="originalPrice != null">#{originalPrice,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据活动id查询活动商品-->
        <select id="findByActivityGoodsIds" resultMap="BaseResultMap">
            select
            ACTIVITY_GOODS_ID,
            GOODS_NAME,
            ACTIVITY_PRICE,
            UNIT
            from TP_QRORDERING_ACTIVITY_GOODS
            where ACTIVITY_GOODS_ID in
            <foreach collection="list" item="activityGoodsId" open="(" close=")" separator=",">
                #{activityGoodsId,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
