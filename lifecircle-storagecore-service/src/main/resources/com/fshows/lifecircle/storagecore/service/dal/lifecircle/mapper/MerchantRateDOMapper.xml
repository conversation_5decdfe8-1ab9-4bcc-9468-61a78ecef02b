<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantRateDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantRateDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="LOG_ID" property="logId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SYNC_STATE" property="syncState" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXIST_MAX_FEE" property="existMaxFee" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_EFFECTIVE" property="isEffective" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_CHANNEL" property="paymentChannel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXIST_SALESMAN_COST" property="existSalesmanCost" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXIST_WITHDRAWAL_FEE" property="existWithdrawalFee" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FB_RATE" property="fbRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="OEM_RATE" property="oemRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_FB_FEE" property="maxFbFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_RATE" property="agentRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_OEM_FEE" property="maxOemFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANNEL_RATE" property="channelRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_AGENT_FEE" property="maxAgentFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PAYMENT_RATE" property="paymentRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SALESMAN_RATE" property="salesmanRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_CHANNEL_FEE" property="maxChannelFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_PAYMENT_FEE" property="maxPaymentFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="WITHDRAWAL_FEE" property="withdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_SALESMAN_FEE" property="maxSalesmanFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FB_WITHDRAWAL_FEE" property="fbWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="OEM_WITHDRAWAL_FEE" property="oemWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_WITHDRAWAL_FEE" property="agentWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANNEL_WITHDRAWAL_FEE" property="channelWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SALESMAN_WITHDRAWAL_FEE" property="salesmanWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`LOG_ID`,`SYNC_STATE`,`EXIST_MAX_FEE`,`IS_EFFECTIVE`,`PRODUCT_CODE`,`PAYMENT_CHANNEL`,`EXIST_SALESMAN_COST`,`EXIST_WITHDRAWAL_FEE`,`UID`,`PAY_TYPE`,`BEGIN_TIME`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`FB_RATE`,`OEM_RATE`,`MAX_FB_FEE`,`AGENT_RATE`,`MAX_OEM_FEE`,`CHANNEL_RATE`,`MAX_AGENT_FEE`,`PAYMENT_RATE`,`SALESMAN_RATE`,`MAX_CHANNEL_FEE`,`MAX_PAYMENT_FEE`,`WITHDRAWAL_FEE`,`MAX_SALESMAN_FEE`,`FB_WITHDRAWAL_FEE`,`OEM_WITHDRAWAL_FEE`,`AGENT_WITHDRAWAL_FEE`,`CHANNEL_WITHDRAWAL_FEE`,`SALESMAN_WITHDRAWAL_FEE`
    </sql>


            <!--insert:TP_MERCHANT_RATE-->
            <insert id="insert" >
                    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT /*MS-TP-MERCHANT-RATE-INSERT*/  LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_MERCHANT_RATE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="syncState != null">`SYNC_STATE`,</if>
            <if test="existMaxFee != null">`EXIST_MAX_FEE`,</if>
            <if test="isEffective != null">`IS_EFFECTIVE`,</if>
            <if test="paymentChannel != null">`PAYMENT_CHANNEL`,</if>
            <if test="productCode != null">`PRODUCT_CODE`,</if>
            <if test="existSalesmanCost != null">`EXIST_SALESMAN_COST`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbRate != null">`FB_RATE`,</if>
            <if test="oemRate != null">`OEM_RATE`,</if>
            <if test="maxFbFee != null">`MAX_FB_FEE`,</if>
            <if test="agentRate != null">`AGENT_RATE`,</if>
            <if test="maxOemFee != null">`MAX_OEM_FEE`,</if>
            <if test="channelRate != null">`CHANNEL_RATE`,</if>
            <if test="maxAgentFee != null">`MAX_AGENT_FEE`,</if>
            <if test="paymentRate != null">`PAYMENT_RATE`,</if>
            <if test="withdrawalFee != null">`WITHDRAWAL_FEE`,</if>
            <if test="salesmanRate != null">`SALESMAN_RATE`,</if>
            <if test="maxChannelFee != null">`MAX_CHANNEL_FEE`,</if>
            <if test="maxPaymentFee != null">`MAX_PAYMENT_FEE`,</if>
            <if test="maxSalesmanFee != null">`MAX_SALESMAN_FEE`,</if>
            <if test="fbWithdrawalFee != null">`FB_WITHDRAWAL_FEE`,</if>
            <if test="agentWithdrawalFee != null">`AGENT_WITHDRAWAL_FEE`,</if>
            <if test="channelWithdrawalFee != null">`CHANNEL_WITHDRAWAL_FEE`,</if>
            <if test="logId != null">`LOG_ID`,</if>
            <if test="oemWithdrawalFee != null">`oem_withdrawal_fee`,</if>
            <if test="salesmanWithdrawalFee != null">`salesman_withdrawal_fee`,</if>
            <if test="existWithdrawalFee != null">`exist_withdrawal_fee`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="syncState != null">#{syncState,jdbcType=VARCHAR},</if>
            <if test="existMaxFee != null">#{existMaxFee,jdbcType=VARCHAR},</if>
            <if test="isEffective != null">#{isEffective,jdbcType=VARCHAR},</if>
            <if test="paymentChannel != null">#{paymentChannel,jdbcType=VARCHAR},</if>
            <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
            <if test="existSalesmanCost != null">#{existSalesmanCost,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbRate != null">#{fbRate,jdbcType=DECIMAL},</if>
            <if test="oemRate != null">#{oemRate,jdbcType=DECIMAL},</if>
            <if test="maxFbFee != null">#{maxFbFee,jdbcType=DECIMAL},</if>
            <if test="agentRate != null">#{agentRate,jdbcType=DECIMAL},</if>
            <if test="maxOemFee != null">#{maxOemFee,jdbcType=DECIMAL},</if>
            <if test="channelRate != null">#{channelRate,jdbcType=DECIMAL},</if>
            <if test="maxAgentFee != null">#{maxAgentFee,jdbcType=DECIMAL},</if>
            <if test="paymentRate != null">#{paymentRate,jdbcType=DECIMAL},</if>
            <if test="withdrawalFee != null">#{withdrawalFee,jdbcType=DECIMAL},</if>
            <if test="salesmanRate != null">#{salesmanRate,jdbcType=DECIMAL},</if>
            <if test="maxChannelFee != null">#{maxChannelFee,jdbcType=DECIMAL},</if>
            <if test="maxPaymentFee != null">#{maxPaymentFee,jdbcType=DECIMAL},</if>
            <if test="maxSalesmanFee != null">#{maxSalesmanFee,jdbcType=DECIMAL},</if>
            <if test="fbWithdrawalFee != null">#{fbWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="agentWithdrawalFee != null">#{agentWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="channelWithdrawalFee != null">#{channelWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="logId != null">#{logId,jdbcType=VARCHAR},</if>
            <if test="oemWithdrawalFee != null">#{oemWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="salesmanWithdrawalFee != null">#{salesmanWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="existWithdrawalFee != null">#{existWithdrawalFee,jdbcType=VARCHAR},</if>
        </trim>
            </insert>
    </mapper>
