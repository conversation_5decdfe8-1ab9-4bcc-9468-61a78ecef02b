<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyLifecircleRefundDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyLifecircleRefundDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_CODE" property="refundCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_INFO" property="refundInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FACE_DEVICE_SN" property="faceDeviceSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_SN" property="platformOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_REFUND_SN" property="merchantRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_REFUND_SN" property="platformRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATOR_ORDER_SN" property="liquidatorOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_REFUND_ORDER_SN" property="goodsRefundOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATOR_REFUND_SN" property="liquidatorRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HANDLER" property="handler" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_MORE_DAY" property="isMoreDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_TYPE" property="consumeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LASTEST_TIME" property="lastestTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PART_REFUND" property="isPartRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_AGENT_ID" property="refundAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SPECIAL_SETTLE" property="specialSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_CHANNEL" property="consumeChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POSITION_REFUND" property="isPositionRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORGANIZATION_TYPE" property="organizationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_FAILED_DEAL" property="refundFailedDeal" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_UPDATE_TIME" property="refundUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REFUND" property="refund" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CASH_FEE" property="cashFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LIFE_FEE" property="lifeFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_FEE" property="agentFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="POUNDAGE" property="poundage" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_RATE_FEE" property="agentRateFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RECHARGEACT_AMOUNT" property="rechargeactAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`TOKEN`,`REMARK`,`ORDER_SN`,`DEVICE_NO`,`REFUND_SN`,`NOTIFY_URL`,`ACTIVITY_ID`,`REFUND_CODE`,`REFUND_INFO`,`FACE_DEVICE_SN`,`MERCHANT_ORDER_SN`,`PLATFORM_ORDER_SN`,`MERCHANT_REFUND_SN`,`PLATFORM_REFUND_SN`,`LIQUIDATOR_ORDER_SN`,`GOODS_REFUND_ORDER_SN`,`LIQUIDATOR_REFUND_SN`,`USER_ID`,`CHANNEL`,`GRANT_ID`,`HANDLER`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`MARKET_ID`,`CASHIER_ID`,`IS_MORE_DAY`,`CREATE_TIME`,`SETTLE_MODE`,`CONSUME_TYPE`,`LASTEST_TIME`,`SUB_CONFIG_ID`,`IS_PART_REFUND`,`REFUND_STATUS`,`REFUND_AGENT_ID`,`SPECIAL_SETTLE`,`CONSUME_CHANNEL`,`LIQUIDATION_TYPE`,`IS_POSITION_REFUND`,`ORGANIZATION_TYPE`,`REFUND_FAILED_DEAL`,`REFUND_UPDATE_TIME`,`REFUND`,`CASH_FEE`,`LIFE_FEE`,`AGENT_FEE`,`POUNDAGE`,`REFUND_MONEY`,`AGENT_RATE_FEE`,`RECHARGEACT_AMOUNT`
    </sql>


            <!--insert:TP_LIFECIRCLE_REFUND-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="ext5 != null">`EXT5`,</if>
        <if test="ext6 != null">`EXT6`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="deviceNo != null">`DEVICE_NO`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="notifyUrl != null">`NOTIFY_URL`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="refundCode != null">`REFUND_CODE`,</if>
        <if test="refundInfo != null">`REFUND_INFO`,</if>
        <if test="faceDeviceSn != null">`FACE_DEVICE_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="platformOrderSn != null">`PLATFORM_ORDER_SN`,</if>
        <if test="merchantRefundSn != null">`MERCHANT_REFUND_SN`,</if>
        <if test="platformRefundSn != null">`PLATFORM_REFUND_SN`,</if>
        <if test="liquidatorOrderSn != null">`LIQUIDATOR_ORDER_SN`,</if>
        <if test="goodsRefundOrderSn != null">`GOODS_REFUND_ORDER_SN`,</if>
        <if test="liquidatorRefundSn != null">`LIQUIDATOR_REFUND_SN`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="grantId != null">`GRANT_ID`,</if>
        <if test="handler != null">`HANDLER`,</if>
        <if test="payTime != null">`PAY_TIME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="marketId != null">`MARKET_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="isMoreDay != null">`IS_MORE_DAY`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="settleMode != null">`SETTLE_MODE`,</if>
        <if test="consumeType != null">`CONSUME_TYPE`,</if>
        <if test="lastestTime != null">`LASTEST_TIME`,</if>
        <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
        <if test="isPartRefund != null">`IS_PART_REFUND`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="refundAgentId != null">`REFUND_AGENT_ID`,</if>
        <if test="specialSettle != null">`SPECIAL_SETTLE`,</if>
        <if test="consumeChannel != null">`CONSUME_CHANNEL`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="isPositionRefund != null">`IS_POSITION_REFUND`,</if>
        <if test="organizationType != null">`ORGANIZATION_TYPE`,</if>
        <if test="refundFailedDeal != null">`REFUND_FAILED_DEAL`,</if>
        <if test="refundUpdateTime != null">`REFUND_UPDATE_TIME`,</if>
        <if test="refund != null">`REFUND`,</if>
        <if test="cashFee != null">`CASH_FEE`,</if>
        <if test="lifeFee != null">`LIFE_FEE`,</if>
        <if test="agentFee != null">`AGENT_FEE`,</if>
        <if test="poundage != null">`POUNDAGE`,</if>
        <if test="refundMoney != null">`REFUND_MONEY`,</if>
        <if test="agentRateFee != null">`AGENT_RATE_FEE`,</if>
        <if test="rechargeactAmount != null">`RECHARGEACT_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
        <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="notifyUrl != null">#{notifyUrl,jdbcType=VARCHAR},</if>
        <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
        <if test="refundCode != null">#{refundCode,jdbcType=VARCHAR},</if>
        <if test="refundInfo != null">#{refundInfo,jdbcType=VARCHAR},</if>
        <if test="faceDeviceSn != null">#{faceDeviceSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="platformOrderSn != null">#{platformOrderSn,jdbcType=VARCHAR},</if>
        <if test="merchantRefundSn != null">#{merchantRefundSn,jdbcType=VARCHAR},</if>
        <if test="platformRefundSn != null">#{platformRefundSn,jdbcType=VARCHAR},</if>
        <if test="liquidatorOrderSn != null">#{liquidatorOrderSn,jdbcType=VARCHAR},</if>
        <if test="goodsRefundOrderSn != null">#{goodsRefundOrderSn,jdbcType=VARCHAR},</if>
        <if test="liquidatorRefundSn != null">#{liquidatorRefundSn,jdbcType=VARCHAR},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
        <if test="handler != null">#{handler,jdbcType=INTEGER},</if>
        <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="isMoreDay != null">#{isMoreDay,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
        <if test="consumeType != null">#{consumeType,jdbcType=TINYINT},</if>
        <if test="lastestTime != null">#{lastestTime,jdbcType=INTEGER},</if>
        <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
        <if test="isPartRefund != null">#{isPartRefund,jdbcType=TINYINT},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="refundAgentId != null">#{refundAgentId,jdbcType=INTEGER},</if>
        <if test="specialSettle != null">#{specialSettle,jdbcType=TINYINT},</if>
        <if test="consumeChannel != null">#{consumeChannel,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="isPositionRefund != null">#{isPositionRefund,jdbcType=TINYINT},</if>
        <if test="organizationType != null">#{organizationType,jdbcType=TINYINT},</if>
        <if test="refundFailedDeal != null">#{refundFailedDeal,jdbcType=TINYINT},</if>
        <if test="refundUpdateTime != null">#{refundUpdateTime,jdbcType=TIMESTAMP},</if>
        <if test="refund != null">#{refund,jdbcType=DECIMAL},</if>
        <if test="cashFee != null">#{cashFee,jdbcType=DECIMAL},</if>
        <if test="lifeFee != null">#{lifeFee,jdbcType=DECIMAL},</if>
        <if test="agentFee != null">#{agentFee,jdbcType=DECIMAL},</if>
        <if test="poundage != null">#{poundage,jdbcType=DECIMAL},</if>
        <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
        <if test="agentRateFee != null">#{agentRateFee,jdbcType=DECIMAL},</if>
        <if test="rechargeactAmount != null">#{rechargeactAmount,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据订单号查询商户订单号-->
            <select id="getMerchantRefundSnListByOrderSn" resultType="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantRefundSnDO">
                    select
        refund_sn as refundSn,merchant_refund_sn as merchantRefundSn,merchant_order_sn as merchantOrderSn
        from tp_lifecircle_refund
        where
        refund_sn in
        <foreach collection="list" open="(" item="refundSn" separator="," close=")">
            #{refundSn,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
