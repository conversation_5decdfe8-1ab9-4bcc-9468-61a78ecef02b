<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.WechatDirectBillDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.WechatDirectBillDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_DATE" property="billDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_TYPE" property="billType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OSS_BUCKET" property="ossBucket" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_FILE_URL" property="billFileUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_STATUS" property="billStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`BILL_DATE`,`BILL_TYPE`,`SUB_MCH_ID`,`OSS_BUCKET`,`BILL_FILE_URL`,`BILL_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_WECHAT_DIRECT_BILL-->
            <insert id="insert" >
                    INSERT INTO TP_WECHAT_DIRECT_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="billType != null">`BILL_TYPE`,</if>
            <if test="subMchId != null">`SUB_MCH_ID`,</if>
            <if test="billStatus != null">`BILL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="ossBucket != null">`OSS_BUCKET`,</if>
            <if test="billFileUrl != null">`BILL_FILE_URL`,</if>
            <if test="remark != null">`REMARK`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="billDate != null">#{billDate,jdbcType=INTEGER},</if>
            <if test="billType != null">#{billType,jdbcType=VARCHAR},</if>
            <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
            <if test="billStatus != null">#{billStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="ossBucket != null">#{ossBucket,jdbcType=VARCHAR},</if>
            <if test="billFileUrl != null">#{billFileUrl,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据子商户号和账单日期查询账单-->
            <select id="queryBillRecordByParam" resultMap="BaseResultMap">
                    SELECT /*MS-TP-WECHAT-DIRECT-BILL-QUERYBILLRECORDBYPARAM*/  <include refid="Base_Column_List" /> FROM TP_WECHAT_DIRECT_BILL
        WHERE BILL_TYPE = #{billType,jdbcType=VARCHAR}
        AND SUB_MCH_ID = #{subMchId,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER} limit 1
            </select>

            <!--将对账单上传状态变更为上传成功-->
            <update id="updateBillStatusToSuccess" >
                    UPDATE /*MS-TP-WECHAT-DIRECT-BILL-UPDATEBILLSTATUSTOSUCCESS*/ TP_WECHAT_DIRECT_BILL
        SET BILL_STATUS = #{billStatus,jdbcType=INTEGER},
        BILL_FILE_URL = #{billFileUrl,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR}
        WHERE BILL_TYPE = #{billType,jdbcType=VARCHAR}
        AND SUB_MCH_ID = #{subMchId,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER}
            </update>
    </mapper>
