<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AgentSettletmentFileDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AgentSettletmentFileDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="FILE_KEY" property="fileKey" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FILE_TYPE" property="fileType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_DATE" property="settleDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FILE_KEY`,`AGENT_ID`,`FILE_TYPE`,`SETTLE_DATE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_AGENT_SETTLETMENT_FILE-->
            <insert id="insert" >
            INSERT INTO TP_AGENT_SETTLETMENT_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fileKey != null">`FILE_KEY`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="fileType != null">`FILE_TYPE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fileKey != null">#{fileKey,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="fileType != null">#{fileType,jdbcType=TINYINT},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--查询代理商某天的结算记录-->
            <select id="getSettlementDataByAgentId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_AGENT_SETTLETMENT_FILE
        WHERE
        agent_id = #{agentId, jdbcType=INTEGER}
        AND settle_date = #{settleDate,jdbcType=INTEGER}
        LIMIT 1
            </select>
    </mapper>
