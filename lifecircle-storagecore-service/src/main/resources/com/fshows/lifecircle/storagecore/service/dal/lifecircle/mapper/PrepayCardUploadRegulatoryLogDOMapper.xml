<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardUploadRegulatoryLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardUploadRegulatoryLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FLOW_NO" property="flowNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_ID_LIST" property="orgIdList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_NUMBER" property="orgNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FILE_OSS_URL" property="fileOssUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPLOAD_TIME" property="uploadTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPLOAD_STATUS" property="uploadStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CALCULATE_END_TIME" property="calculateEndTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CALCULATE_START_TIME" property="calculateStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FLOW_NO`,`ORG_ID_LIST`,`ORG_NUMBER`,`FILE_OSS_URL`,`IS_DEL`,`UPLOAD_TIME`,`UPLOAD_STATUS`,`CALCULATE_END_TIME`,`CALCULATE_START_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="flowNo != null">`FLOW_NO`,</if>
            <if test="orgIdList != null">`ORG_ID_LIST`,</if>
            <if test="orgNumber != null">`ORG_NUMBER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="uploadTime != null">`UPLOAD_TIME`,</if>
            <if test="uploadStatus != null">`UPLOAD_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="calculateEndTime != null">`CALCULATE_END_TIME`,</if>
            <if test="calculateStartTime != null">`CALCULATE_START_TIME`,</if>
            <if test="fileOssUrl != null">`file_oss_url`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="flowNo != null">#{flowNo,jdbcType=VARCHAR},</if>
            <if test="orgIdList != null">#{orgIdList,jdbcType=VARCHAR},</if>
            <if test="orgNumber != null">#{orgNumber,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="uploadTime != null">#{uploadTime,jdbcType=INTEGER},</if>
            <if test="uploadStatus != null">#{uploadStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="calculateEndTime != null">#{calculateEndTime,jdbcType=TIMESTAMP},</if>
            <if test="calculateStartTime != null">#{calculateStartTime,jdbcType=TIMESTAMP},</if>
            <if test="fileOssUrl != null">#{fileOssUrl,jdbcType=VARCHAR},</if>
        </trim>
            </insert>
    </mapper>
