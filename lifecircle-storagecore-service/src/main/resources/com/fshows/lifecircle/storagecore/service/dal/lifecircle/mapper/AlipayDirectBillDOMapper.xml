<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AlipayDirectBillDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AlipayDirectBillDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_DATE" property="billDate" jdbcType="CHAR"
        javaType="String"/>

            <result column="BILL_TYPE" property="billType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OSS_BUCKET" property="ossBucket" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_FILE_URL" property="billFileUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_STATUS" property="billStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`REMARK`,`BILL_DATE`,`BILL_TYPE`,`OSS_BUCKET`,`BILL_FILE_URL`,`BILL_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_ALIPAY_DIRECT_BILL-->
            <insert id="insert" >
                    INSERT INTO TP_ALIPAY_DIRECT_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="billType != null">`BILL_TYPE`,</if>
            <if test="ossBucket != null">`OSS_BUCKET`,</if>
            <if test="billFileUrl != null">`BILL_FILE_URL`,</if>
            <if test="billStatus != null">`BILL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=CHAR},</if>
            <if test="billType != null">#{billType,jdbcType=VARCHAR},</if>
            <if test="ossBucket != null">#{ossBucket,jdbcType=VARCHAR},</if>
            <if test="billFileUrl != null">#{billFileUrl,jdbcType=VARCHAR},</if>
            <if test="billStatus != null">#{billStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据商户token和账单日期查询账单-->
            <select id="queryBillRecordByParam" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_ALIPAY_DIRECT_BILL
        WHERE TOKEN = #{token,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER}
        AND BILL_TYPE = #{billType,jdbcType=VARCHAR}
        limit 1
            </select>

            <!--将对账单上传状态变更为上传成功-->
            <update id="updateBillStatusToSuccess" >
                    UPDATE
        TP_ALIPAY_DIRECT_BILL
        SET BILL_STATUS = #{billStatus,jdbcType=INTEGER},
        BILL_FILE_URL = #{billFileUrl,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR}
        WHERE TOKEN = #{token,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER}
        AND BILL_TYPE = #{billType,jdbcType=VARCHAR}
            </update>
    </mapper>
