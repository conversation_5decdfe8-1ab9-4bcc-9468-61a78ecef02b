<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleStoreDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleStoreDO">

            <result column="STORE_LAT" property="storeLat" jdbcType="REAL"
        javaType="Float"/>

            <result column="STORE_LNG" property="storeLng" jdbcType="REAL"
        javaType="Float"/>

            <result column="KEY" property="key" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TEL" property="tel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY" property="city" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTE" property="note" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COUNTY" property="county" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="END_TIME" property="endTime" jdbcType="CHAR"
        javaType="String"/>

            <result column="OPERATE" property="operate" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="SERVICE" property="service" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE" property="province" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OBJECTION" property="objection" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECOMMEND" property="recommend" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="START_TIME" property="startTime" jdbcType="CHAR"
        javaType="String"/>

            <result column="STORE_AREA" property="storeArea" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_LOGO" property="storeLogo" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DENIAL_TYPE" property="denialType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BRIEF" property="storeBrief" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_IMAGE" property="storeImage" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="EXAMINE_JSON" property="examineJson" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="SERVICE_MORE" property="serviceMore" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_NUMBER" property="licenseNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MAIN_STORE_INFO" property="mainStoreInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REC_ID" property="recId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_HORN" property="isHorn" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AVG_PRICE" property="avgPrice" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ONLINE" property="isOnline" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="JOIN_TIME" property="joinTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRINT_NUM" property="printNum" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_TO" property="settleTo" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTOPRINT" property="autoprint" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_LOG_ID" property="bindLogId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRINT_CODE" property="printCode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_TYPE" property="storeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CATEGORYID" property="categoryid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PROMOTED" property="isPromoted" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UNITY_CAT_ID" property="unityCatId" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="EXAMINE_TIME" property="examineTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="JOIN_CHANNEL" property="joinChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRINTER_TYPE" property="printerType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_STATUS" property="storeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_BIND_KOUBEI" property="isBindKoubei" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_NEED_INCOME" property="isNeedIncome" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TAKEPARTIN" property="isTakepartin" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TAKING_ORDERS" property="takingOrders" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EXAMINE_STATUS" property="examineStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_BELONG_ID" property="storeBelongId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DISPLAY_COMMENT" property="displayComment" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDEX_RECOMMEND" property="indexRecommend" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PREFERENTIAL_ID" property="preferentialId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLEMENT_TYPE" property="settlementType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_HORN_CANCEL_PAY" property="isHornCancelPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_START_TIME" property="settleStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TAKING_ORDERS_TYPE" property="takingOrdersType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_QUALIFICATION" property="merchantQualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `STORE_LAT`,`STORE_LNG`,`KEY`,`TEL`,`CITY`,`NOTE`,`TOKEN`,`COUNTY`,`ADDRESS`,`END_TIME`,`OPERATE`,`SERVICE`,`DEVICE_NO`,`PROVINCE`,`BRAND_NAME`,`OBJECTION`,`RECOMMEND`,`START_TIME`,`STORE_AREA`,`STORE_LOGO`,`STORE_NAME`,`BRANCH_NAME`,`DENIAL_TYPE`,`STORE_BRIEF`,`STORE_IMAGE`,`EXAMINE_JSON`,`SERVICE_MORE`,`LICENSE_NUMBER`,`MAIN_STORE_INFO`,`REC_ID`,`IS_HORN`,`IS_SHOW`,`AGENT_ID`,`STORE_ID`,`AVG_PRICE`,`IS_ONLINE`,`JOIN_TIME`,`PRINT_NUM`,`SETTLE_TO`,`AUTOPRINT`,`BIND_LOG_ID`,`PRINT_CODE`,`STORE_TYPE`,`CATEGORYID`,`CREATE_TIME`,`IS_PROMOTED`,`UNITY_CAT_ID`,`EXAMINE_TIME`,`JOIN_CHANNEL`,`PRINTER_TYPE`,`STORE_STATUS`,`IS_BIND_KOUBEI`,`IS_NEED_INCOME`,`IS_TAKEPARTIN`,`TAKING_ORDERS`,`EXAMINE_STATUS`,`STORE_BELONG_ID`,`DISPLAY_COMMENT`,`INDEX_RECOMMEND`,`PREFERENTIAL_ID`,`SETTLEMENT_TYPE`,`IS_HORN_CANCEL_PAY`,`SETTLE_START_TIME`,`TAKING_ORDERS_TYPE`,`MERCHANT_QUALIFICATION`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_STORE-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_STORE
                <selectKey keyProperty="storeId" order="AFTER" resultType="java.lang.Integer">
                    SELECT
                    LAST_INSERT_ID()
                </selectKey>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeLat != null">`STORE_LAT`,</if>
            <if test="storeLng != null">`STORE_LNG`,</if>
            <if test="key != null">`KEY`,</if>
            <if test="tel != null">`TEL`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="county != null">`COUNTY`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="operate != null">`OPERATE`,</if>
            <if test="service != null">`SERVICE`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="brandName != null">`BRAND_NAME`,</if>
            <if test="objection != null">`OBJECTION`,</if>
            <if test="recommend != null">`RECOMMEND`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="storeArea != null">`STORE_AREA`,</if>
            <if test="storeLogo != null">`STORE_LOGO`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="denialType != null">`DENIAL_TYPE`,</if>
            <if test="storeBrief != null">`STORE_BRIEF`,</if>
            <if test="storeImage != null">`STORE_IMAGE`,</if>
            <if test="examineJson != null">`EXAMINE_JSON`,</if>
            <if test="serviceMore != null">`SERVICE_MORE`,</if>
            <if test="licenseNumber != null">`LICENSE_NUMBER`,</if>
            <if test="mainStoreInfo != null">`MAIN_STORE_INFO`,</if>
            <if test="recId != null">`REC_ID`,</if>
            <if test="isHorn != null">`IS_HORN`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="avgPrice != null">`AVG_PRICE`,</if>
            <if test="isOnline != null">`IS_ONLINE`,</if>
            <if test="joinTime != null">`JOIN_TIME`,</if>
            <if test="printNum != null">`PRINT_NUM`,</if>
            <if test="autoprint != null">`AUTOPRINT`,</if>
            <if test="bindLogId != null">`BIND_LOG_ID`,</if>
            <if test="printCode != null">`PRINT_CODE`,</if>
            <if test="categoryid != null">`CATEGORYID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isPromoted != null">`IS_PROMOTED`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="examineTime != null">`EXAMINE_TIME`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="printerType != null">`PRINTER_TYPE`,</if>
            <if test="storeStatus != null">`STORE_STATUS`,</if>
            <if test="isBindKoubei != null">`IS_BIND_KOUBEI`,</if>
            <if test="isTakepartin != null">`IS_TAKEPARTIN`,</if>
            <if test="takingOrders != null">`TAKING_ORDERS`,</if>
            <if test="examineStatus != null">`EXAMINE_STATUS`,</if>
            <if test="storeBelongId != null">`STORE_BELONG_ID`,</if>
            <if test="displayComment != null">`DISPLAY_COMMENT`,</if>
            <if test="indexRecommend != null">`INDEX_RECOMMEND`,</if>
            <if test="preferentialId != null">`PREFERENTIAL_ID`,</if>
            <if test="isHornCancelPay != null">`IS_HORN_CANCEL_PAY`,</if>
            <if test="takingOrdersType != null">`TAKING_ORDERS_TYPE`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeLat != null">#{storeLat,jdbcType=REAL},</if>
            <if test="storeLng != null">#{storeLng,jdbcType=REAL},</if>
            <if test="key != null">#{key,jdbcType=VARCHAR},</if>
            <if test="tel != null">#{tel,jdbcType=VARCHAR},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="county != null">#{county,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="endTime != null">#{endTime,jdbcType=CHAR},</if>
            <if test="operate != null">#{operate,jdbcType=LONGVARCHAR},</if>
            <if test="service != null">#{service,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="brandName != null">#{brandName,jdbcType=VARCHAR},</if>
            <if test="objection != null">#{objection,jdbcType=VARCHAR},</if>
            <if test="recommend != null">#{recommend,jdbcType=VARCHAR},</if>
            <if test="startTime != null">#{startTime,jdbcType=CHAR},</if>
            <if test="storeArea != null">#{storeArea,jdbcType=VARCHAR},</if>
            <if test="storeLogo != null">#{storeLogo,jdbcType=LONGVARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="denialType != null">#{denialType,jdbcType=VARCHAR},</if>
            <if test="storeBrief != null">#{storeBrief,jdbcType=VARCHAR},</if>
            <if test="storeImage != null">#{storeImage,jdbcType=LONGVARCHAR},</if>
            <if test="examineJson != null">#{examineJson,jdbcType=LONGVARCHAR},</if>
            <if test="serviceMore != null">#{serviceMore,jdbcType=VARCHAR},</if>
            <if test="licenseNumber != null">#{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="mainStoreInfo != null">#{mainStoreInfo,jdbcType=VARCHAR},</if>
            <if test="recId != null">#{recId,jdbcType=INTEGER},</if>
            <if test="isHorn != null">#{isHorn,jdbcType=TINYINT},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="avgPrice != null">#{avgPrice,jdbcType=INTEGER},</if>
            <if test="isOnline != null">#{isOnline,jdbcType=TINYINT},</if>
            <if test="joinTime != null">#{joinTime,jdbcType=INTEGER},</if>
            <if test="printNum != null">#{printNum,jdbcType=TINYINT},</if>
            <if test="autoprint != null">#{autoprint,jdbcType=TINYINT},</if>
            <if test="bindLogId != null">#{bindLogId,jdbcType=INTEGER},</if>
            <if test="printCode != null">#{printCode,jdbcType=TINYINT},</if>
            <if test="categoryid != null">#{categoryid,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isPromoted != null">#{isPromoted,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="examineTime != null">#{examineTime,jdbcType=INTEGER},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="printerType != null">#{printerType,jdbcType=TINYINT},</if>
            <if test="storeStatus != null">#{storeStatus,jdbcType=TINYINT},</if>
            <if test="isBindKoubei != null">#{isBindKoubei,jdbcType=TINYINT},</if>
            <if test="isTakepartin != null">#{isTakepartin,jdbcType=TINYINT},</if>
            <if test="takingOrders != null">#{takingOrders,jdbcType=TINYINT},</if>
            <if test="examineStatus != null">#{examineStatus,jdbcType=TINYINT},</if>
            <if test="storeBelongId != null">#{storeBelongId,jdbcType=INTEGER},</if>
            <if test="displayComment != null">#{displayComment,jdbcType=TINYINT},</if>
            <if test="indexRecommend != null">#{indexRecommend,jdbcType=TINYINT},</if>
            <if test="preferentialId != null">#{preferentialId,jdbcType=INTEGER},</if>
            <if test="isHornCancelPay != null">#{isHornCancelPay,jdbcType=TINYINT},</if>
            <if test="takingOrdersType != null">#{takingOrdersType,jdbcType=TINYINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--update table:TP_LIFECIRCLE_STORE-->
            <update id="update" >
                    UPDATE /*MS-TP-LIFECIRCLE-STORE-UPDATE*/ TP_LIFECIRCLE_STORE
        SET
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="key != null">`KEY` = #{key,jdbcType=VARCHAR},</if>
            <if test="tel != null">TEL = #{tel,jdbcType=VARCHAR},</if>
            <if test="city != null">CITY = #{city,jdbcType=VARCHAR},</if>
            <if test="note != null">NOTE = #{note,jdbcType=VARCHAR},</if>
            <if test="recId != null">REC_ID = #{recId,jdbcType=INTEGER},</if>
            <if test="token != null">TOKEN = #{token,jdbcType=VARCHAR},</if>
            <if test="county != null">COUNTY = #{county,jdbcType=VARCHAR},</if>
            <if test="isHorn != null">IS_HORN = #{isHorn,jdbcType=TINYINT},</if>
            <if test="isShow != null">IS_SHOW = #{isShow,jdbcType=TINYINT},</if>
            <if test="address != null">ADDRESS = #{address,jdbcType=VARCHAR},</if>
            <if test="agentId != null">AGENT_ID = #{agentId,jdbcType=INTEGER},</if>
            <if test="endTime != null">END_TIME = #{endTime,jdbcType=CHAR},</if>
            <if test="operate != null">OPERATE = #{operate,jdbcType=LONGVARCHAR},</if>
            <if test="service != null">SERVICE = #{service,jdbcType=VARCHAR},</if>
            <if test="avgPrice != null">AVG_PRICE = #{avgPrice,jdbcType=INTEGER},</if>
            <if test="deviceNo != null">DEVICE_NO = #{deviceNo,jdbcType=VARCHAR},</if>
            <if test="isOnline != null">IS_ONLINE = #{isOnline,jdbcType=TINYINT},</if>
            <if test="joinTime != null">JOIN_TIME = #{joinTime,jdbcType=INTEGER},</if>
            <if test="printNum != null">PRINT_NUM = #{printNum,jdbcType=TINYINT},</if>
            <if test="province != null">PROVINCE = #{province,jdbcType=VARCHAR},</if>
            <if test="storeLat != null">STORE_LAT = #{storeLat,jdbcType=REAL},</if>
            <if test="storeLng != null">STORE_LNG = #{storeLng,jdbcType=REAL},</if>
            <if test="autoprint != null">AUTOPRINT = #{autoprint,jdbcType=TINYINT},</if>
            <if test="bindLogId != null">BIND_LOG_ID = #{bindLogId,jdbcType=INTEGER},</if>
            <if test="brandName != null">BRAND_NAME = #{brandName,jdbcType=VARCHAR},</if>
            <if test="objection != null">OBJECTION = #{objection,jdbcType=VARCHAR},</if>
            <if test="printCode != null">PRINT_CODE = #{printCode,jdbcType=TINYINT},</if>
            <if test="recommend != null">RECOMMEND = #{recommend,jdbcType=VARCHAR},</if>
            <if test="startTime != null">START_TIME = #{startTime,jdbcType=CHAR},</if>
            <if test="storeArea != null">STORE_AREA = #{storeArea,jdbcType=VARCHAR},</if>
            <if test="storeLogo != null">STORE_LOGO = #{storeLogo,jdbcType=LONGVARCHAR},</if>
            <if test="storeName != null">STORE_NAME = #{storeName,jdbcType=VARCHAR},</if>
            <if test="branchName != null">BRANCH_NAME = #{branchName,jdbcType=VARCHAR},</if>
            <if test="categoryid != null">CATEGORYID = #{categoryid,jdbcType=INTEGER},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime,jdbcType=INTEGER},</if>
            <if test="isPromoted != null">IS_PROMOTED = #{isPromoted,jdbcType=TINYINT},</if>
            <if test="storeBrief != null">STORE_BRIEF = #{storeBrief,jdbcType=VARCHAR},</if>
            <if test="storeImage != null">STORE_IMAGE = #{storeImage,jdbcType=LONGVARCHAR},</if>
            <if test="unityCatId != null">UNITY_CAT_ID = #{unityCatId,jdbcType=SMALLINT},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="examineJson != null">EXAMINE_JSON = #{examineJson,jdbcType=LONGVARCHAR},</if>
            <if test="examineTime != null">EXAMINE_TIME = #{examineTime,jdbcType=INTEGER},</if>
            <if test="joinChannel != null">JOIN_CHANNEL = #{joinChannel,jdbcType=TINYINT},</if>
            <if test="printerType != null">PRINTER_TYPE = #{printerType,jdbcType=TINYINT},</if>
            <if test="serviceMore != null">SERVICE_MORE = #{serviceMore,jdbcType=VARCHAR},</if>
            <if test="storeStatus != null">STORE_STATUS = #{storeStatus,jdbcType=TINYINT},</if>
            <if test="isBindKoubei != null">IS_BIND_KOUBEI = #{isBindKoubei,jdbcType=TINYINT},</if>
            <if test="isTakepartin != null">IS_TAKEPARTIN = #{isTakepartin,jdbcType=TINYINT},</if>
            <if test="takingOrders != null">TAKING_ORDERS = #{takingOrders,jdbcType=TINYINT},</if>
            <if test="examineStatus != null">EXAMINE_STATUS = #{examineStatus,jdbcType=TINYINT},</if>
            <if test="licenseNumber != null">LICENSE_NUMBER = #{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="mainStoreInfo != null">MAIN_STORE_INFO = #{mainStoreInfo,jdbcType=VARCHAR},</if>
            <if test="storeBelongId != null">STORE_BELONG_ID = #{storeBelongId,jdbcType=INTEGER},</if>
            <if test="displayComment != null">DISPLAY_COMMENT = #{displayComment,jdbcType=TINYINT},</if>
            <if test="indexRecommend != null">INDEX_RECOMMEND = #{indexRecommend,jdbcType=TINYINT},</if>
            <if test="preferentialId != null">PREFERENTIAL_ID = #{preferentialId,jdbcType=INTEGER},</if>
            <if test="isHornCancelPay != null">IS_HORN_CANCEL_PAY = #{isHornCancelPay,jdbcType=TINYINT},</if>
            <if test="takingOrdersType != null">TAKING_ORDERS_TYPE = #{takingOrdersType,jdbcType=TINYINT},</if>
            <if test="settlementType != null">SETTLEMENT_TYPE = #{settlementType,jdbcType=TINYINT},</if>
            <if test="merchantQualification != null">MERCHANT_QUALIFICATION = #{merchantQualification,jdbcType=TINYINT},</if>
        </trim>
        WHERE
        STORE_ID = #{storeId,jdbcType=INTEGER}
            </update>

            <!--根据 store_id 查询记录-->
            <select id="getByStoreId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-STORE-GETBYSTOREID*/  <include refid="Base_Column_List" />
        FROM tp_lifecircle_store
        WHERE
        `store_id` = #{storeId,jdbcType=INTEGER}
        limit 1
            </select>

            <!--根据storeId获取门店名称-->
            <select id="getStoreNameByStoreId" resultMap="BaseResultMap">
                    SELECT
        `store_name` AS storeName
        FROM tp_lifecircle_store
        WHERE
        `store_id` = #{storeId,jdbcType=INTEGER}
        limit 1
            </select>

            <!--根据门店id查询门店名称-->
            <select id="findStoreNameByStoreIds" resultMap="BaseResultMap">
                    SELECT
        STORE_ID,
        STORE_NAME,
        AUTOPRINT
        FROM TP_LIFECIRCLE_STORE
        WHERE
        STORE_ID IN
        <foreach collection="list" item="storeId" open="(" close=")" separator=",">
            #{storeId,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--根据UID查询首家门店ID-->
            <select id="getFirstStoreIdByUid" resultType="java.lang.Integer">
                    SELECT
        store.STORE_ID
        FROM
        TP_LIFECIRCLE_STORE store
        INNER JOIN TP_USERS users ON store.TOKEN = users.USERS_TOKEN
        WHERE
        users.`ID` = #{uid,jdbcType=INTEGER}
        AND users.PARENT_ID = 0
        AND users.INCOME_STATUS IN (3,5)
        ORDER BY
        store.STORE_ID ASC
        LIMIT 1
            </select>

    <!--根据店铺名称获取店铺信息-->
    <select id="getOneByStoreName" resultMap="BaseResultMap">
        select /*MS-TP-LIFECIRCLE-STORE-GETONEBYSTORENAME*/ STORE_LAT,
                                                            STORE_LNG,
                                                            TEL,
                                                            CITY,
                                                            NOTE,
                                                            TOKEN,
                                                            COUNTY,
                                                            ADDRESS,
                                                            END_TIME,
                                                            OPERATE,
                                                            SERVICE,
                                                            DEVICE_NO,
                                                            PROVINCE,
                                                            BRAND_NAME,
                                                            OBJECTION,
                                                            RECOMMEND,
                                                            START_TIME,
                                                            STORE_AREA,
                                                            STORE_LOGO,
                                                            STORE_NAME,
                                                            BRANCH_NAME,
                                                            DENIAL_TYPE,
                                                            STORE_BRIEF,
                                                            STORE_IMAGE,
                                                            EXAMINE_JSON,
                                                            SERVICE_MORE,
                                                            LICENSE_NUMBER,
                                                            MAIN_STORE_INFO,
                                                            REC_ID,
                                                            IS_HORN,
                                                            IS_SHOW,
                                                            AGENT_ID,
                                                            STORE_ID,
                                                            AVG_PRICE,
                                                            IS_ONLINE,
                                                            JOIN_TIME,
                                                            PRINT_NUM,
                                                            SETTLE_TO,
                                                            AUTOPRINT,
                                                            BIND_LOG_ID,
                                                            PRINT_CODE,
                                                            STORE_TYPE,
                                                            CATEGORYID,
                                                            CREATE_TIME,
                                                            IS_PROMOTED,
                                                            UNITY_CAT_ID,
                                                            EXAMINE_TIME,
                                                            JOIN_CHANNEL,
                                                            PRINTER_TYPE,
                                                            STORE_STATUS,
                                                            IS_BIND_KOUBEI,
                                                            IS_NEED_INCOME,
                                                            IS_TAKEPARTIN,
                                                            TAKING_ORDERS,
                                                            EXAMINE_STATUS,
                                                            STORE_BELONG_ID,
                                                            DISPLAY_COMMENT,
                                                            INDEX_RECOMMEND,
                                                            PREFERENTIAL_ID,
                                                            SETTLEMENT_TYPE,
                                                            IS_HORN_CANCEL_PAY,
                                                            SETTLE_START_TIME,
                                                            TAKING_ORDERS_TYPE,
                                                            MERCHANT_QUALIFICATION,
                                                            UPDATE_TIME
        from tp_lifecircle_store
        where store_name = #{storeName,jdbcType=VARCHAR} limit 1
    </select>
    </mapper>
