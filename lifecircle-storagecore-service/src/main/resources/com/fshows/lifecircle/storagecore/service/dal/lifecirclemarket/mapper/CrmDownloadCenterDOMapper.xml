<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmDownloadCenterDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmDownloadCenterDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="FILE_URL" property="fileUrl" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DOWNLOAD_ID" property="downloadId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STATUS" property="status" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FILE_URL`,`REMARKS`,`CREATE_BY`,`FILE_NAME`,`UPDATE_BY`,`DOWNLOAD_ID`,`STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_CRM_DOWNLOAD_CENTER-->
    <insert id="insert">
        INSERT INTO LM_CRM_DOWNLOAD_CENTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="fileUrl != null">`FILE_URL`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="downloadId != null">`DOWNLOAD_ID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="fileUrl != null">#{fileUrl,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="downloadId != null">#{downloadId,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--insert:LM_CRM_DOWNLOAD_CENTER-->
    <insert id="insertObj">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_CRM_DOWNLOAD_CENTER(
        ID
        ,FILE_URL
        ,REMARKS
        ,CREATE_BY
        ,FILE_NAME
        ,UPDATE_BY
        ,DOWNLOAD_ID
        ,STATUS
        ,CREATE_TIME
        ,UPDATE_TIME
        )VALUES(
        #{id,jdbcType=INTEGER}
        , #{fileUrl,jdbcType=VARCHAR}
        , #{remarks,jdbcType=VARCHAR}
        , #{createBy,jdbcType=VARCHAR}
        , #{fileName,jdbcType=VARCHAR}
        , #{updateBy,jdbcType=VARCHAR}
        , #{downloadId,jdbcType=VARCHAR}
        , #{status,jdbcType=TINYINT}
        , #{createTime,jdbcType=TIMESTAMP}
        , #{updateTime,jdbcType=TIMESTAMP}
        )
    </insert>

    <!--update table:LM_CRM_DOWNLOAD_CENTER-->
    <update id="update">
                    UPDATE /*MS-LM-CRM-DOWNLOAD-CENTER-UPDATE*/ LM_CRM_DOWNLOAD_CENTER
        SET
        file_url = #{fileUrl,jdbcType=VARCHAR}
        ,file_name = #{fileName,jdbcType=VARCHAR}
        ,STATUS = #{status,jdbcType=TINYINT}
        ,UPDATE_BY = #{updateBy,jdbcType=VARCHAR}
        ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
        DOWNLOAD_ID = #{downloadId,jdbcType=INTEGER}
            </update>
</mapper>
