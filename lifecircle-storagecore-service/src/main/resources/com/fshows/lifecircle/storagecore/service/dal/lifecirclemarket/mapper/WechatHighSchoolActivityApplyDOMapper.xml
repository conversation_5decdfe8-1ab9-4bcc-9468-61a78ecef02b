<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.WechatHighSchoolActivityApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.WechatHighSchoolActivityApplyDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_INFO" property="rejectInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_HEAD_PIC" property="storeHeadPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_INDOOR_PIC" property="storeIndoorPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MANAGE_QUALIFICATION_PIC" property="manageQualificationPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCHOOL_QUALIFICATION_PIC" property="schoolQualificationPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COOPERATION_AGREEMENT_PIC" property="cooperationAgreementPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_REBATE_REJECT_REASON" property="activityRebateRejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUBJECT_TYPE" property="subjectType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_OPEN_TIME" property="activityOpenTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_SIGN_TIME" property="activitySignTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_APPLY_TIME" property="activityRebateApplyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_BEGIN_MONTH" property="activityRebateBeginMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_REBATE_APPLY_STATUS" property="activityRebateApplyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPLY_ID`,`COMPANY`,`SUB_MCH_ID`,`USERNAME`,`SHORT_NAME`,`ACTIVITY_ID`,`LICENSE_PIC`,`MERCHANT_NO`,`REJECT_INFO`,`STORE_HEAD_PIC`,`STORE_INDOOR_PIC`,`MANAGE_QUALIFICATION_PIC`,`SCHOOL_QUALIFICATION_PIC`,`COOPERATION_AGREEMENT_PIC`,`ACTIVITY_REBATE_REJECT_REASON`,`UID`,`IS_DEL`,`BELONG`,`SUBJECT_TYPE`,`ACTIVITY_STATUS`,`ACTIVITY_OPEN_TIME`,`ACTIVITY_SIGN_TIME`,`ACTIVITY_REBATE_APPLY_TIME`,`ACTIVITY_REBATE_BEGIN_MONTH`,`ACTIVITY_REBATE_APPLY_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--查询-->
            <select id="findCrmApplyList" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from lm_wechat_high_school_activity_apply
        where activity_status in (2, 3, 4, 5, 6, 7, 8)
        <if test="uid != null">
            and uid =#{uid, jdbcType=INTEGER}
        </if>
        <if test="username != null and '' != username">
            and username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and '' != company">
            and company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="list != null and list.size() &gt; 0">
            and belong in
            <foreach collection="list" separator="," open="(" close=")" index="index" item="belong">
                #{belong, jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="subjectType != null">
            and subject_type = #{subjectType, jdbcType=INTEGER}
        </if>
        <if test="activityStatus != null">
            and activity_status = #{activityStatus,jdbcType=TINYINT}
        </if>
        <if test="activityOpenEndTime != null">
            AND activity_sign_time <![CDATA[ <= ]]>  #{activityOpenEndTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenStartTime != null">
            AND activity_sign_time <![CDATA[ >= ]]>  #{activityOpenStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStartTime != null">
            AND activity_rebate_apply_time <![CDATA[ >= ]]>  #{rebateApplyStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyEndTime != null">
            AND activity_rebate_apply_time <![CDATA[ <= ]]>  #{rebateApplyEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStatus != null">
            and activity_rebate_apply_status = #{rebateApplyStatus,jdbcType=TINYINT}
        </if>
        <if test="rebateBeginMonth != null">
            and activity_rebate_begin_month = #{rebateBeginMonth,jdbcType=INTEGER}
        </if>
        order by activity_sign_time desc, uid desc
            </select>

            <!--getMerchantActivityApplyByUidSubMchId-->
            <select id="getMerchantActivityApplyByUidSubMchId" resultMap="BaseResultMap">
                    SELECT /*MS-LM-WECHAT-HIGH-SCHOOL-ACTIVITY-APPLY-GETMERCHANTACTIVITYAPPLYBYUIDSUBMCHID*/  <include refid="Base_Column_List" />
        FROM lm_wechat_high_school_activity_apply
        WHERE uid = #{uid,jdbcType=INTEGER}
        and sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1;
            </select>

            <!--updateRebateApplyInfoById-->
            <update id="updateRebateApplyInfoById" >
                    update /*MS-LM-WECHAT-HIGH-SCHOOL-ACTIVITY-APPLY-UPDATEREBATEAPPLYINFOBYID*/ lm_wechat_high_school_activity_apply
        set update_time = now()
        <if test="activityRebateApplyStatus != null">
            ,ACTIVITY_REBATE_APPLY_STATUS = #{activityRebateApplyStatus,jdbcType=TINYINT}
        </if>
        <if test="activityRebateApplyTime != null">
            ,ACTIVITY_REBATE_APPLY_TIME = #{activityRebateApplyTime,jdbcType=INTEGER}
        </if>
        <if test="activityRebateBeginMonth != null">
            ,ACTIVITY_REBATE_BEGIN_MONTH = #{activityRebateBeginMonth,jdbcType=INTEGER}
        </if>
        <if test="activityRebateRejectReason != null">
            ,ACTIVITY_REBATE_REJECT_REASON = #{activityRebateRejectReason,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=BIGINT}
            </update>
    </mapper>
