<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpUserDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpUserDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QQ" property="qq" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TEL" property="tel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA" property="area" jdbcType="CHAR"
        javaType="String"/>

            <result column="BANK" property="bank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY" property="city" jdbcType="CHAR"
        javaType="String"/>

            <result column="LOGO" property="logo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EMAIL" property="email" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEVEL" property="level" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNAME" property="uname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PEOPLE" property="people" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UTOKEN" property="utoken" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT" property="wechat" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NUM" property="cardNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_WEB" property="cardWeb" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS" property="business" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARDNAME" property="cardname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACTS" property="contacts" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FULL_PATH" property="fullPath" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NICKNAME" property="nickname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PASSWORD" property="password" jdbcType="CHAR"
        javaType="String"/>

            <result column="PROVINCE" property="province" jdbcType="CHAR"
        javaType="String"/>

            <result column="TURNOVER" property="turnover" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRIVILEGES" property="privileges" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFESSION" property="profession" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_ADDRESS" property="cardAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANYNAME" property="companyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_LOGIN_IP" property="lastLoginIp" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_AREA" property="businessArea" jdbcType="CHAR"
        javaType="String"/>

            <result column="BUSINESS_CITY" property="businessCity" jdbcType="CHAR"
        javaType="String"/>

            <result column="CUSTOMER_NOTE" property="customerNote" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_LOCATION" property="lastLocation" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VERIFYIMAGES" property="verifyimages" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAYACCOUNT" property="alipayaccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPEN_API_CALLBACK" property="openApiCallback" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_PROVINCE" property="businessProvince" jdbcType="CHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASH" property="cash" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ROLE" property="role" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPEN" property="isOpen" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PASS" property="isPass" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OWN_RUN" property="ownRun" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_JDPAY" property="isJdpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_WHITE" property="isWhite" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VIPTIME" property="viptime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHILD_NUM" property="childNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GROUP_NUM" property="groupNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ALIPAY" property="isAlipay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SELF_FEE" property="isSelfFee" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIG_TYPE" property="configType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATETIME" property="createtime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPEN_MINA" property="isOpenMina" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SALESMAN" property="isSalesman" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW_TIPS" property="isShowTips" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LOAN_STATUS" property="loanStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACCOUNT_TYPE" property="accountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_QUICK_CASH" property="isQuickCash" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALESMAN_TAG" property="salesmanTag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USE_GROUP_NUM" property="useGroupNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CURRENT_LEVEL" property="currentLevel" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALES_STAFF_ID" property="salesStaffId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SCAN_SERVICE" property="isScanService" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CUSTOMER_SERVICE" property="customerService" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPENAPI_ACCESS" property="isOpenapiAccess" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SUPER_SALESMAN" property="isSuperSalesman" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ADVERTISEMENT_NUM" property="advertisementNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OPERATION_SERVICE" property="operationService" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NEW_VERSION_USERS_NUM" property="newVersionUsersNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_TAG_START_DAY" property="salesmanTagStartDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FINANCE" property="finance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ADVERTISEMENT_BALANCE" property="advertisementBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QQ`,`TEL`,`AREA`,`BANK`,`CITY`,`LOGO`,`EMAIL`,`LEVEL`,`PHONE`,`UNAME`,`PEOPLE`,`REMARK`,`UTOKEN`,`WECHAT`,`ADDRESS`,`CARD_NUM`,`CARD_WEB`,`BUSINESS`,`CARDNAME`,`CONTACTS`,`FULL_PATH`,`NICKNAME`,`PASSWORD`,`PROVINCE`,`TURNOVER`,`USERNAME`,`PRIVILEGES`,`PROFESSION`,`CARD_ADDRESS`,`COMPANYNAME`,`LAST_LOGIN_IP`,`BUSINESS_AREA`,`BUSINESS_CITY`,`CUSTOMER_NOTE`,`LAST_LOCATION`,`VERIFYIMAGES`,`ALIPAYACCOUNT`,`OPEN_API_CALLBACK`,`BUSINESS_PROVINCE`,`UID`,`CASH`,`ROLE`,`TYPE`,`BELONG`,`IS_OPEN`,`IS_PASS`,`OWN_RUN`,`STATUS`,`IS_JDPAY`,`IS_WHITE`,`VIPTIME`,`CHILD_NUM`,`GROUP_NUM`,`IS_ALIPAY`,`PARENT_ID`,`PLATFORM`,`IS_SELF_FEE`,`CONFIG_TYPE`,`CREATETIME`,`IS_OPEN_MINA`,`IS_SALESMAN`,`IS_SHOW_TIPS`,`LOAN_STATUS`,`ACCOUNT_TYPE`,`IS_QUICK_CASH`,`SALESMAN_TAG`,`SUB_CONFIG_ID`,`USE_GROUP_NUM`,`CURRENT_LEVEL`,`SALES_STAFF_ID`,`IS_SCAN_SERVICE`,`LAST_LOGIN_TIME`,`CUSTOMER_SERVICE`,`IS_OPENAPI_ACCESS`,`IS_SUPER_SALESMAN`,`SUPER_SALESMAN_ID`,`ADVERTISEMENT_NUM`,`OPERATION_SERVICE`,`NEW_VERSION_USERS_NUM`,`SALESMAN_TAG_START_DAY`,`CREATE_TIME`,`UPDATE_TIME`,`FINANCE`,`ADVERTISEMENT_BALANCE`
    </sql>


            <!--insert:TP_USER-->
            <insert id="insert" >
                    INSERT INTO TP_USER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="tel != null">`TEL`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="bank != null">`BANK`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="logo != null">`LOGO`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="level != null">`LEVEL`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="uname != null">`UNAME`,</if>
            <if test="people != null">`PEOPLE`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="utoken != null">`UTOKEN`,</if>
            <if test="wechat != null">`WECHAT`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="cardNum != null">`CARD_NUM`,</if>
            <if test="cardWeb != null">`CARD_WEB`,</if>
            <if test="business != null">`BUSINESS`,</if>
            <if test="cardname != null">`CARDNAME`,</if>
            <if test="contacts != null">`CONTACTS`,</if>
            <if test="password != null">`PASSWORD`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="turnover != null">`TURNOVER`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="privileges != null">`PRIVILEGES`,</if>
            <if test="profession != null">`PROFESSION`,</if>
            <if test="cardAddress != null">`CARD_ADDRESS`,</if>
            <if test="companyname != null">`COMPANYNAME`,</if>
            <if test="lastLoginIp != null">`LAST_LOGIN_IP`,</if>
            <if test="customerNote != null">`CUSTOMER_NOTE`,</if>
            <if test="lastLocation != null">`LAST_LOCATION`,</if>
            <if test="verifyimages != null">`VERIFYIMAGES`,</if>
            <if test="alipayaccount != null">`ALIPAYACCOUNT`,</if>
            <if test="openApiCallback != null">`OPEN_API_CALLBACK`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="cash != null">`CASH`,</if>
            <if test="role != null">`ROLE`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="isOpen != null">`IS_OPEN`,</if>
            <if test="isPass != null">`IS_PASS`,</if>
            <if test="ownRun != null">`OWN_RUN`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="isJdpay != null">`IS_JDPAY`,</if>
            <if test="isWhite != null">`IS_WHITE`,</if>
            <if test="viptime != null">`VIPTIME`,</if>
            <if test="childNum != null">`CHILD_NUM`,</if>
            <if test="groupNum != null">`GROUP_NUM`,</if>
            <if test="isAlipay != null">`IS_ALIPAY`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="isSelfFee != null">`IS_SELF_FEE`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="isOpenMina != null">`IS_OPEN_MINA`,</if>
            <if test="isSalesman != null">`IS_SALESMAN`,</if>
            <if test="isShowTips != null">`IS_SHOW_TIPS`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="isQuickCash != null">`IS_QUICK_CASH`,</if>
            <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
            <if test="useGroupNum != null">`USE_GROUP_NUM`,</if>
            <if test="salesStaffId != null">`SALES_STAFF_ID`,</if>
            <if test="isScanService != null">`IS_SCAN_SERVICE`,</if>
            <if test="lastLoginTime != null">`LAST_LOGIN_TIME`,</if>
            <if test="customerService != null">`CUSTOMER_SERVICE`,</if>
            <if test="isOpenapiAccess != null">`IS_OPENAPI_ACCESS`,</if>
            <if test="isSuperSalesman != null">`IS_SUPER_SALESMAN`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="advertisementNum != null">`ADVERTISEMENT_NUM`,</if>
            <if test="operationService != null">`OPERATION_SERVICE`,</if>
            <if test="newVersionUsersNum != null">`NEW_VERSION_USERS_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finance != null">`FINANCE`,</if>
            <if test="advertisementBalance != null">`ADVERTISEMENT_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qq != null">#{qq,jdbcType=VARCHAR},</if>
            <if test="tel != null">#{tel,jdbcType=VARCHAR},</if>
            <if test="area != null">#{area,jdbcType=CHAR},</if>
            <if test="bank != null">#{bank,jdbcType=VARCHAR},</if>
            <if test="city != null">#{city,jdbcType=CHAR},</if>
            <if test="logo != null">#{logo,jdbcType=VARCHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="level != null">#{level,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="uname != null">#{uname,jdbcType=VARCHAR},</if>
            <if test="people != null">#{people,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="utoken != null">#{utoken,jdbcType=VARCHAR},</if>
            <if test="wechat != null">#{wechat,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="cardNum != null">#{cardNum,jdbcType=VARCHAR},</if>
            <if test="cardWeb != null">#{cardWeb,jdbcType=VARCHAR},</if>
            <if test="business != null">#{business,jdbcType=VARCHAR},</if>
            <if test="cardname != null">#{cardname,jdbcType=VARCHAR},</if>
            <if test="contacts != null">#{contacts,jdbcType=VARCHAR},</if>
            <if test="password != null">#{password,jdbcType=CHAR},</if>
            <if test="province != null">#{province,jdbcType=CHAR},</if>
            <if test="turnover != null">#{turnover,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="privileges != null">#{privileges,jdbcType=VARCHAR},</if>
            <if test="profession != null">#{profession,jdbcType=VARCHAR},</if>
            <if test="cardAddress != null">#{cardAddress,jdbcType=VARCHAR},</if>
            <if test="companyname != null">#{companyname,jdbcType=VARCHAR},</if>
            <if test="lastLoginIp != null">#{lastLoginIp,jdbcType=VARCHAR},</if>
            <if test="customerNote != null">#{customerNote,jdbcType=VARCHAR},</if>
            <if test="lastLocation != null">#{lastLocation,jdbcType=VARCHAR},</if>
            <if test="verifyimages != null">#{verifyimages,jdbcType=VARCHAR},</if>
            <if test="alipayaccount != null">#{alipayaccount,jdbcType=VARCHAR},</if>
            <if test="openApiCallback != null">#{openApiCallback,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="cash != null">#{cash,jdbcType=INTEGER},</if>
            <if test="role != null">#{role,jdbcType=SMALLINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="isOpen != null">#{isOpen,jdbcType=TINYINT},</if>
            <if test="isPass != null">#{isPass,jdbcType=TINYINT},</if>
            <if test="ownRun != null">#{ownRun,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="isJdpay != null">#{isJdpay,jdbcType=TINYINT},</if>
            <if test="isWhite != null">#{isWhite,jdbcType=TINYINT},</if>
            <if test="viptime != null">#{viptime,jdbcType=INTEGER},</if>
            <if test="childNum != null">#{childNum,jdbcType=INTEGER},</if>
            <if test="groupNum != null">#{groupNum,jdbcType=INTEGER},</if>
            <if test="isAlipay != null">#{isAlipay,jdbcType=TINYINT},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="isSelfFee != null">#{isSelfFee,jdbcType=TINYINT},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="createtime != null">#{createtime,jdbcType=INTEGER},</if>
            <if test="isOpenMina != null">#{isOpenMina,jdbcType=TINYINT},</if>
            <if test="isSalesman != null">#{isSalesman,jdbcType=TINYINT},</if>
            <if test="isShowTips != null">#{isShowTips,jdbcType=TINYINT},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=TINYINT},</if>
            <if test="accountType != null">#{accountType,jdbcType=TINYINT},</if>
            <if test="isQuickCash != null">#{isQuickCash,jdbcType=TINYINT},</if>
            <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
            <if test="useGroupNum != null">#{useGroupNum,jdbcType=INTEGER},</if>
            <if test="salesStaffId != null">#{salesStaffId,jdbcType=INTEGER},</if>
            <if test="isScanService != null">#{isScanService,jdbcType=TINYINT},</if>
            <if test="lastLoginTime != null">#{lastLoginTime,jdbcType=INTEGER},</if>
            <if test="customerService != null">#{customerService,jdbcType=INTEGER},</if>
            <if test="isOpenapiAccess != null">#{isOpenapiAccess,jdbcType=TINYINT},</if>
            <if test="isSuperSalesman != null">#{isSuperSalesman,jdbcType=TINYINT},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="advertisementNum != null">#{advertisementNum,jdbcType=INTEGER},</if>
            <if test="operationService != null">#{operationService,jdbcType=INTEGER},</if>
            <if test="newVersionUsersNum != null">#{newVersionUsersNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finance != null">#{finance,jdbcType=DECIMAL},</if>
            <if test="advertisementBalance != null">#{advertisementBalance,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据代理商id查询代理商信息-->
            <select id="getAgentByAgentId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-GETAGENTBYAGENTID*/  <include refid="Base_Column_List" /> from tp_user
        where id = #{agentId,jdbcType=INTEGER}
        and role = 10 and status = 1
            </select>

            <!--根据id获取用户信息-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-GETBYID*/  <include refid="Base_Column_List" />
        from tp_user
        where id = #{id,jdbcType=INTEGER}
            </select>

            <!--查询代理商/授理商集合-->
            <select id="findAgentOrSalesmanList" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.AgentDTO">
                    select
        t1.id,t1.username
        from
        tp_user t1
        WHERE
        t1.id in
        <foreach collection="list" item="salesmanId" open="(" close=")" separator=",">
            #{salesmanId,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--根据belong与授理商账号查询授理商id集合-->
            <select id="findSalesmanIdListByBelongAndName" resultType="java.lang.Integer">
                    SELECT
        `id`
        FROM
        tp_user
        WHERE
        belong = #{belong,jdbcType=INTEGER}
        AND username like CONCAT(#{content,jdbcType=VARCHAR},'%')
        AND platform in (0,3)
        AND is_salesman = 1
        AND role = 10
            </select>

            <!--根据名称查询获取代理商ID列表-->
            <select id="findAgentIdListByName" resultType="java.lang.Integer">
                    SELECT
        `id`
        FROM
        tp_user
        WHERE
        platform IN ( 0, 3 )
        AND is_salesman = 0
        AND role = 10
        AND username LIKE CONCAT(#{content,jdbcType=VARCHAR},'%')
            </select>

            <!--根据名称查询获取授理商ID列表-->
            <select id="findSalesManIdListByName" resultType="java.lang.Integer">
                    SELECT
        `id`
        FROM
        tp_user
        WHERE
        platform IN ( 0, 3 )
        AND is_salesman = 1
        AND role = 10
        AND username LIKE CONCAT(#{content,jdbcType=VARCHAR},'%')
            </select>

            <!--根据名称查询获取市场经理ID列表-->
            <select id="findMarketIdListByName" resultType="java.lang.Integer">
                    SELECT
        `id`
        FROM
        tp_user
        WHERE
        platform IN ( 0, 3 )
        AND level = '市场经理'
        AND role = 10
        AND username LIKE CONCAT(#{content,jdbcType=VARCHAR},'%')
            </select>
    </mapper>
