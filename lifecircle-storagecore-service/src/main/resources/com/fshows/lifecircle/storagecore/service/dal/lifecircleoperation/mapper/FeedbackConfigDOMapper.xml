<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.mapper.FeedbackConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.dataobject.FeedbackConfigDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="NAME" property="name" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE" property="source" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QIYU_URL" property="qiyuUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`NAME`,`SOURCE`,`QIYU_URL`,`TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_FEEDBACK_CONFIG-->
            <insert id="insert" >
                    INSERT INTO TP_FEEDBACK_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="qiyuUrl != null">`QIYU_URL`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="qiyuUrl != null">#{qiyuUrl,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取意见反馈配置列表-->
            <select id="getFeedBackConfigList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-FEEDBACK-CONFIG-GETFEEDBACKCONFIGLIST*/  <include refid="Base_Column_List" /> FROM TP_FEEDBACK_CONFIG
            </select>
    </mapper>
