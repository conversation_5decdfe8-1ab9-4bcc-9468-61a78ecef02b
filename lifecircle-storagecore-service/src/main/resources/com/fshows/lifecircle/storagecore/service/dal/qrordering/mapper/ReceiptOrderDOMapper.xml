<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.ReceiptOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.ReceiptOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PREPAY_ID" property="prepayId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_ID" property="receiptId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_TOKEN_NO" property="shareTokenNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_PHONE" property="customerPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_PAY_ORDER_NO" property="shortPayOrderNo" jdbcType="CHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_METHOD" property="payMethod" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PAY_STATUS" property="payStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SCENE_VALUE" property="sceneValue" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_SOURCE" property="orderSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IMPORT_CUSTOMER_ID" property="importCustomerId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RATE_FEE" property="rateFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_SUMPRICE" property="orderSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`REMARK`,`ORDER_NO`,`PREPAY_ID`,`RECEIPT_ID`,`CUSTOMER_ID`,`PAY_ORDER_NO`,`SHARE_TOKEN_NO`,`CUSTOMER_PHONE`,`SHORT_PAY_ORDER_NO`,`DEL_FLAG`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`PAY_METHOD`,`PAY_STATUS`,`SCENE_VALUE`,`ORDER_SOURCE`,`ORDER_STATUS`,`REFUND_STATUS`,`IMPORT_CUSTOMER_ID`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`RATE_FEE`,`ORDER_PRICE`,`ORDER_SUMPRICE`
    </sql>


            <!--insert:TP_RECEIPT_ORDER-->
            <insert id="insert" >
                    INSERT INTO TP_RECEIPT_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="prepayId != null">`PREPAY_ID`,</if>
            <if test="receiptId != null">`RECEIPT_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="prepayId != null">#{prepayId,jdbcType=VARCHAR},</if>
            <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--收款单订单列表-->
            <select id="findReceiptOrderLists" resultMap="BaseResultMap">
                    select /*MS-TP-RECEIPT-ORDER-FINDRECEIPTORDERLISTS*/ <include refid="Base_Column_List" /> from tp_receipt_order
        where order_status = 1 and pay_status=2 and receipt_id=#{receiptId,jdbcType=VARCHAR}
            </select>

            <!--根据收款单id集合获取收款单详情-->
            <select id="getReceiptInfoByIds" resultType="com.fshows.lifecircle.storagecore.service.domain.model.ReceiptOrderInfoModel">
                    select
        tro.order_no as orderNo,
        tro.pay_order_no as payOrderNo,
        tro.receipt_id as receiptId,
        tro.customer_id as customerId,
        tro.store_id as storeId,
        tro.order_status as orderStatus,
        tro.order_price as orderPrice,
        tro.pay_status as payStatus,
        tro.order_sumprice as orderSumprice,
        tro.pay_type as payType,
        tro.pay_time as payTime,
        tro.prepay_id as prepayId,
        tro.remark as remark,
        tro.create_time as createTime,
        tro.update_time as updateTime,
        tro.refund_status as refundStatus,
        troe.receipt_form as receiptForm,
        troe.form_value as formValue,
        troe.nick_name as nickName
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.pay_status = 2
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--根据收款单id集合获取收款单订单数量-->
            <select id="getReceiptOrderCount" resultType="java.lang.Integer">
                    select
         count(*) 
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--根据收款单id集合获取收款单详情分页查询 pageCount-->
            <select id="getReceiptInfoByIdsLimitCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.pay_status = 2
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
            </select>
            <!--根据收款单id集合获取收款单详情分页查询 pageResult-->
            <select id="getReceiptInfoByIdsLimitResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.model.ReceiptOrderInfoModel">
                    select
        tro.order_no as orderNo,
        tro.pay_order_no as payOrderNo,
        tro.receipt_id as receiptId,
        tro.customer_id as customerId,
        tro.store_id as storeId,
        tro.order_status as orderStatus,
        tro.order_price as orderPrice,
        tro.pay_status as payStatus,
        tro.order_sumprice as orderSumprice,
        tro.pay_type as payType,
        tro.pay_time as payTime,
        tro.prepay_id as prepayId,
        tro.remark as remark,
        tro.create_time as createTime,
        tro.update_time as updateTime,
        tro.refund_status as refundStatus,
        troe.receipt_form as receiptForm,
        troe.form_value as formValue,
        troe.nick_name as nickName
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.pay_status = 2
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
            limit #{startRow},#{limit}
            </select>

            <!--selectFilterOrder-->
            <select id="selectFilterOrder" resultMap="BaseResultMap">
                    select /*MS-TP-RECEIPT-ORDER-SELECTFILTERORDER*/ <include refid="Base_Column_List" /> from tp_receipt_order
        where receipt_id=#{receiptId,jdbcType=VARCHAR}
        and pay_time &gt;= #{startTime,jdbcType=INTEGER}
        and pay_time <![CDATA[<=]]>  #{endTime,jdbcType=INTEGER}
        and order_status = 1
        and pay_status=2
        <if test="refundStatus != null and !refundStatus.contains(-1)">
            and refund_status in
            <foreach collection="refundStatus" item="status" open="(" close=")" separator=",">
                #{status,jdbcType=INTEGER}
            </foreach>
        </if>
            </select>
    </mapper>
