<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmAlipayTouchTaskLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmAlipayTouchTaskLogDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TASK_NO" property="taskNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_DESC" property="operateDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATOR_TYPE" property="operatorType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATE_STATUS" property="operateStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`TASK_NO`,`ERROR_MSG`,`OPERATOR_ID`,`OPERATE_DESC`,`OPERATOR_NAME`,`OPERATE_TYPE`,`OPERATOR_TYPE`,`OPERATE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_ALIPAY_TOUCH_TASK_LOG-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_ALIPAY_TOUCH_TASK_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="taskNo != null">`TASK_NO`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="operateDesc != null">`OPERATE_DESC`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="operatorType != null">`OPERATOR_TYPE`,</if>
            <if test="operateStatus != null">`OPERATE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="taskNo != null">#{taskNo,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="operateDesc != null">#{operateDesc,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="operatorType != null">#{operatorType,jdbcType=TINYINT},</if>
            <if test="operateStatus != null">#{operateStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--查询任务挂起原因-->
            <select id="findPendingReasonByTaskNoList" resultMap="BaseResultMap">
                    select /*MS-LM-CRM-ALIPAY-TOUCH-TASK-LOG-FINDPENDINGREASONBYTASKNOLIST*/ TASK_NO,ERROR_MSG
        from lm_crm_alipay_touch_task_log
        where TASK_NO IN
        <foreach collection="list" item="taskNo" open="(" separator="," close=")">
            #{taskNo,jdbcType=VARCHAR}
        </foreach>
        and OPERATE_TYPE = 9
        order by create_time desc
            </select>
    </mapper>
