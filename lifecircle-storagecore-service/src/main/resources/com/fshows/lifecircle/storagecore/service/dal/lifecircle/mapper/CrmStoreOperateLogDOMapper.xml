<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CrmStoreOperateLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CrmStoreOperateLogDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="RECORD_ID" property="recordId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SNAPSHOOT_ID" property="snapshootId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISMISS_REASON" property="dismissReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ONLINE" property="isOnline" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRODUCT_NAME" property="productName" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="OPERATE_TIME" property="operateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`RECORD_ID`,`SNAPSHOOT_ID`,`TOKEN`,`OPERATOR_ID`,`OPERATOR_NAME`,`DISMISS_REASON`,`UID`,`STORE_ID`,`IS_ONLINE`,`OPERATE_TYPE`,`PRODUCT_NAME`,`CREATE_TIME`,`UPDATE_TIME`,`OPERATE_TIME`
    </sql>


            <!--insert:TP_CRM_STORE_OPERATE_LOG-->
            <insert id="insert" >
                    INSERT INTO TP_CRM_STORE_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            UID,
            TOKEN,
            STORE_ID,
            IS_ONLINE,
            <if test="recordId != null">RECORD_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            OPERATOR_ID,
            <if test="updateTime != null">UPDATE_TIME,</if>
            OPERATE_TIME,
            OPERATE_TYPE,
            PRODUCT_NAME,
            <if test="snapshootId != null">SNAPSHOOT_ID,</if>
            OPERATOR_NAME,
            <if test="dismissReason != null">DISMISS_REASON,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            #{uid,jdbcType=INTEGER},
            #{token,jdbcType=VARCHAR},
            #{storeId,jdbcType=INTEGER},
            #{isOnline,jdbcType=TINYINT},
            <if test="recordId != null">#{recordId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            #{operatorId,jdbcType=VARCHAR},
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            #{operateTime,jdbcType=TIMESTAMP},
            #{operateType,jdbcType=INTEGER},
            #{productName,jdbcType=INTEGER},
            <if test="snapshootId != null">#{snapshootId,jdbcType=BIGINT},</if>
            #{operatorName,jdbcType=VARCHAR},
            <if test="dismissReason != null">#{dismissReason,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--update table:TP_CRM_STORE_OPERATE_LOG-->
            <update id="update" >
                    UPDATE /*MS-TP-CRM-STORE-OPERATE-LOG-UPDATE*/ TP_CRM_STORE_OPERATE_LOG
        SET
            UID             = #{uid,jdbcType=INTEGER}
            ,TOKEN           = #{token,jdbcType=VARCHAR}
            ,STORE_ID        = #{storeId,jdbcType=INTEGER}
            ,IS_ONLINE       = #{isOnline,jdbcType=TINYINT}
            ,RECORD_ID       = #{recordId,jdbcType=BIGINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,OPERATOR_ID     = #{operatorId,jdbcType=VARCHAR}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            ,OPERATE_TIME    = #{operateTime,jdbcType=TIMESTAMP}
            ,OPERATE_TYPE    = #{operateType,jdbcType=INTEGER}
            ,PRODUCT_NAME    = #{productName,jdbcType=INTEGER}
            ,SNAPSHOOT_ID    = #{snapshootId,jdbcType=BIGINT}
            ,OPERATOR_NAME   = #{operatorName,jdbcType=VARCHAR}
            ,DISMISS_REASON  = #{dismissReason,jdbcType=VARCHAR}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
            </update>

            <!--delete:TP_CRM_STORE_OPERATE_LOG-->
            <delete id="deleteByPrimary" >
                    DELETE /*MS-TP-CRM-STORE-OPERATE-LOG-DELETEBYPRIMARY*/ FROM
            TP_CRM_STORE_OPERATE_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
            </delete>

            <!--get:TP_CRM_STORE_OPERATE_LOG-->
            <select id="getByPrimary" resultMap="BaseResultMap">
                    SELECT /*MS-TP-CRM-STORE-OPERATE-LOG-GETBYPRIMARY*/  <include refid="Base_Column_List" />
        FROM TP_CRM_STORE_OPERATE_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
            </select>

            <!--根据门店id日志信息-->
            <select id="getStoreLogByStoreId" resultMap="BaseResultMap">
                    select /*MS-TP-CRM-STORE-OPERATE-LOG-GETSTORELOGBYSTOREID*/ <include refid="Base_Column_List" /> from tp_crm_store_operate_log where store_id = #{storeId,jdbcType=INTEGER} order by operate_time ASC
            </select>
    </mapper>
