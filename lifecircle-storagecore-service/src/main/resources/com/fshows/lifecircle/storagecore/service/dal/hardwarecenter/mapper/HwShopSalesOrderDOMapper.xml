<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopSalesOrderDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopSalesOrderDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="DEPOT" property="depot" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="USERNAME" property="username" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EXTEND_INFO" property="extendInfo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SALES_ORDER" property="salesOrder" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="RELATION_ORDER_NO" property="relationOrderNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SCORE" property="score" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="USER_ID" property="userId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SALES_TYPE" property="salesType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="BIZ_PAY_TIME" property="bizPayTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="GOODS_SN_NUMBER" property="goodsSnNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="BIZ_TIME" property="bizTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="COST_PRICE" property="costPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="SALES_RATE" property="salesRate" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="EXPRESS_FEE" property="expressFee" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="SALES_PRICE" property="salesPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="COST_UNIT_PRICE" property="costUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="ONLINE_PAY_PRICE" property="onlinePayPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="SALES_UNIT_PRICE" property="salesUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="BALANCE_PAY_PRICE" property="balancePayPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="OFFLINE_PAY_PRICE" property="offlinePayPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`DEPOT`,`USERNAME`,`GOODS_NAME`,`EXTEND_INFO`,`GOODS_SPU_ID`,`SALES_ORDER`,`STORAGE_ORDER`,`RELATION_ORDER_NO`,`IS_DEL`,`SCORE`,`IS_TEST`,`USER_ID`,`USER_TYPE`,`SALES_TYPE`,`BIZ_PAY_TIME`,`EQUIPMENT_ID`,`GOODS_SN_NUMBER`,`BIZ_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`COST_PRICE`,`SALES_RATE`,`EXPRESS_FEE`,`SALES_PRICE`,`COST_UNIT_PRICE`,`ONLINE_PAY_PRICE`,`SALES_UNIT_PRICE`,`BALANCE_PAY_PRICE`,`OFFLINE_PAY_PRICE`
        </sql>


        <!--insert:HW_SHOP_SALES_ORDER-->
        <insert id="insert">
            INSERT INTO HW_SHOP_SALES_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="username != null">`USERNAME`,</if>
                <if test="goodsName != null">`GOODS_NAME`,</if>
                <if test="extendInfo != null">`EXTEND_INFO`,</if>
                <if test="salesOrder != null">`SALES_ORDER`,</if>
                <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
                <if test="depot != null">`DEPOT`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="isTest != null">`IS_TEST`,</if>
                <if test="userId != null">`USER_ID`,</if>
                <if test="userType != null">`USER_TYPE`,</if>
                <if test="salesType != null">`SALES_TYPE`,</if>
                <if test="bizPayTime != null">`BIZ_PAY_TIME`,</if>
                <if test="goodsSnNumber != null">`GOODS_SN_NUMBER`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="costPrice != null">`COST_PRICE`,</if>
                <if test="salesRate != null">`SALES_RATE`,</if>
                <if test="expressFee != null">`EXPRESS_FEE`,</if>
                <if test="salesPrice != null">`SALES_PRICE`,</if>
                <if test="costUnitPrice != null">`COST_UNIT_PRICE`,</if>
                <if test="onlinePayPrice != null">`ONLINE_PAY_PRICE`,</if>
                <if test="salesUnitPrice != null">`SALES_UNIT_PRICE`,</if>
                <if test="balancePayPrice != null">`BALANCE_PAY_PRICE`,</if>
                <if test="offlinePayPrice != null">`OFFLINE_PAY_PRICE`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="username != null">#{username,jdbcType=VARCHAR},</if>
                <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
                <if test="extendInfo != null">#{extendInfo,jdbcType=VARCHAR},</if>
                <if test="salesOrder != null">#{salesOrder,jdbcType=VARCHAR},</if>
                <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
                <if test="depot != null">#{depot,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="isTest != null">#{isTest,jdbcType=TINYINT},</if>
                <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
                <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
                <if test="salesType != null">#{salesType,jdbcType=TINYINT},</if>
                <if test="bizPayTime != null">#{bizPayTime,jdbcType=INTEGER},</if>
                <if test="goodsSnNumber != null">#{goodsSnNumber,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="costPrice != null">#{costPrice,jdbcType=DECIMAL},</if>
                <if test="salesRate != null">#{salesRate,jdbcType=DECIMAL},</if>
                <if test="expressFee != null">#{expressFee,jdbcType=DECIMAL},</if>
                <if test="salesPrice != null">#{salesPrice,jdbcType=DECIMAL},</if>
                <if test="costUnitPrice != null">#{costUnitPrice,jdbcType=DECIMAL},</if>
                <if test="onlinePayPrice != null">#{onlinePayPrice,jdbcType=DECIMAL},</if>
                <if test="salesUnitPrice != null">#{salesUnitPrice,jdbcType=DECIMAL},</if>
                <if test="balancePayPrice != null">#{balancePayPrice,jdbcType=DECIMAL},</if>
                <if test="offlinePayPrice != null">#{offlinePayPrice,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--分页查询硬件商品订单列表 pageCount-->
        <select id="findSalesOrderPageListCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            HW_SHOP_SALES_ORDER
            WHERE
            `IS_DEL` = 0
            <if test="relationOrderNo != null and relationOrderNo != '' ">
                AND `RELATION_ORDER_NO` = #{relationOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="list != null and list.size() &gt; 0 ">
                AND `RELATION_ORDER_NO` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="username != null and username != '' ">
                AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="goodsName != null and goodsName != '' ">
                AND `GOODS_NAME` LIKE concat(#{goodsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="storageStartTime != null">
                AND `BIZ_TIME` <![CDATA[>=]]> #{storageStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="storageEndTime != null">
                AND `BIZ_TIME` <![CDATA[<=]]> #{storageEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="bizPayStartTime != null">
                AND `BIZ_PAY_TIME` <![CDATA[>=]]> #{bizPayStartTime,jdbcType=INTEGER}
            </if>
            <if test="bizPayEndTime != null">
                AND `BIZ_PAY_TIME` <![CDATA[<=]]> #{bizPayEndTime,jdbcType=INTEGER}
            </if>
            <if test="userId != null">
                AND `USER_ID` = #{userId,jdbcType=INTEGER}
            </if>
            <if test="userType != null">
                AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
            </if>
            <if test="salesTypeList != null and salesTypeList.size() &gt; 0 ">
                AND `SALES_TYPE` IN
                <foreach collection="salesTypeList" item="salesType" open="(" close=")" separator=",">
                    #{salesType, jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="isTest != null">
                AND is_test= #{isTest, jdbcType=INTEGER}
            </if>

        </select>
        <!--分页查询硬件商品订单列表 pageResult-->
        <select id="findSalesOrderPageListResult" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            HW_SHOP_SALES_ORDER
            WHERE
            `IS_DEL` = 0
            <if test="relationOrderNo != null and relationOrderNo != '' ">
                AND `RELATION_ORDER_NO` = #{relationOrderNo,jdbcType=VARCHAR}
            </if>
            <if test="list != null and list.size() &gt; 0 ">
                AND `RELATION_ORDER_NO` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="username != null and username != '' ">
                AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="goodsName != null and goodsName != '' ">
                AND `GOODS_NAME` LIKE concat(#{goodsName,jdbcType=VARCHAR},'%')
            </if>
            <if test="storageStartTime != null">
                AND `BIZ_TIME` <![CDATA[>=]]> #{storageStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="storageEndTime != null">
                AND `BIZ_TIME` <![CDATA[<=]]> #{storageEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="bizPayStartTime != null">
                AND `BIZ_PAY_TIME` <![CDATA[>=]]> #{bizPayStartTime,jdbcType=INTEGER}
            </if>
            <if test="bizPayEndTime != null">
                AND `BIZ_PAY_TIME` <![CDATA[<=]]> #{bizPayEndTime,jdbcType=INTEGER}
            </if>
            <if test="userId != null">
                AND `USER_ID` = #{userId,jdbcType=INTEGER}
            </if>
            <if test="userType != null">
                AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
            </if>
            <if test="salesTypeList != null and salesTypeList.size() &gt; 0 ">
                AND `SALES_TYPE` IN
                <foreach collection="salesTypeList" item="salesType" open="(" close=")" separator=",">
                    #{salesType, jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="isTest != null">
                AND is_test= #{isTest, jdbcType=INTEGER}
            </if>
            ORDER BY `BIZ_TIME` DESC
            limit #{startRow},#{limit}
        </select>
    </mapper>
