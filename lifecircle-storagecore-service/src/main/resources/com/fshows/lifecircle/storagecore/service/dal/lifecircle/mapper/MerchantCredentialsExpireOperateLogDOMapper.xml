<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantCredentialsExpireOperateLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantCredentialsExpireOperateLogDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="CONTENT" property="content" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECORD_ID" property="recordId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CONTENT`,`OPERATOR`,`RECORD_ID`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG-->
            <insert id="insert" >
            INSERT INTO TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="content != null">`CONTENT`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="recordId != null">`RECORD_ID`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="content != null">#{content,jdbcType=VARCHAR},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>
    </mapper>
