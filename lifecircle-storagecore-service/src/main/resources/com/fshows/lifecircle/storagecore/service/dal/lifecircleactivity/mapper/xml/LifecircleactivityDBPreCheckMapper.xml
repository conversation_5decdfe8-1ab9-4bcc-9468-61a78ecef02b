<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleactivity.mapper.LifecircleactivityDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 20 as fg,'TP_ACTIVITY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,PAY_TYPE,ACTIVITY_ID,ACTIVITY_CODE,SORT,ALL_OEM,STATUS,END_TIME,ALL_AGENT,ALLOW_OEM,IS_BRIDGE,NEED_CHECK,START_TIME,NEED_ENROLL,COMMISSION_STATUS,COMMISSION_END_TIME,COMMISSION_START_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_ACTIVITY'
            AND COLUMN_NAME in('ID','NAME','PAY_TYPE','ACTIVITY_ID','ACTIVITY_CODE','SORT','ALL_OEM','STATUS','END_TIME','ALL_AGENT','ALLOW_OEM','IS_BRIDGE','NEED_CHECK','START_TIME','NEED_ENROLL','COMMISSION_STATUS','COMMISSION_END_TIME','COMMISSION_START_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'TP_ACTIVITY_PERMISSION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,DESCRIBE,ACTIVITY_ID,ROLE,STATUS,USER_ID,END_TIME,START_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_ACTIVITY_PERMISSION'
            AND COLUMN_NAME in('ID','DESCRIBE','ACTIVITY_ID','ROLE','STATUS','USER_ID','END_TIME','START_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
