<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ActivityTemplateConfigDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ActivityTemplateConfigDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="CONFIG_NAME" property="configName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CONFIG_VALUE" property="configValue" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TEMPLATE_CODE" property="templateCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TEMPLATE_NAME" property="templateName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TEMPLATE_TYPE" property="templateType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CONFIG_ATTRIBUTE" property="configAttribute" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CONFIG_MAPPING_FILE" property="configMappingFile" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SORT_SCORE" property="sortScore" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="EFFECTIVE_STATUS" property="effectiveStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`CONFIG_NAME`,`CONFIG_VALUE`,`TEMPLATE_CODE`,`TEMPLATE_NAME`,`TEMPLATE_TYPE`,`CONFIG_ATTRIBUTE`,`CONFIG_MAPPING_FILE`,`IS_DEL`,`SORT_SCORE`,`EFFECTIVE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_ACTIVITY_TEMPLATE_CONFIG-->
    <insert id="insert">
        INSERT INTO LM_ACTIVITY_TEMPLATE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="configName != null">`CONFIG_NAME`,</if>
            <if test="configValue != null">`CONFIG_VALUE`,</if>
            <if test="templateCode != null">`TEMPLATE_CODE`,</if>
            <if test="templateName != null">`TEMPLATE_NAME`,</if>
            <if test="templateType != null">`TEMPLATE_TYPE`,</if>
            <if test="configAttribute != null">`CONFIG_ATTRIBUTE`,</if>
            <if test="configMappingFile != null">`CONFIG_MAPPING_FILE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="sortScore != null">`SORT_SCORE`,</if>
            <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="configName != null">#{configName,jdbcType=VARCHAR},</if>
            <if test="configValue != null">#{configValue,jdbcType=VARCHAR},</if>
            <if test="templateCode != null">#{templateCode,jdbcType=VARCHAR},</if>
            <if test="templateName != null">#{templateName,jdbcType=VARCHAR},</if>
            <if test="templateType != null">#{templateType,jdbcType=VARCHAR},</if>
            <if test="configAttribute != null">#{configAttribute,jdbcType=VARCHAR},</if>
            <if test="configMappingFile != null">#{configMappingFile,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="sortScore != null">#{sortScore,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
</mapper>
