<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LeshuaCounterofferFileReportDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LeshuaCounterofferFileReportDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TAG" property="tag" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ROLE_TYPE" property="roleType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_HOLDER" property="bankHolder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REAL_CARD_NO" property="realCardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESULT_DESC" property="resultDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UPDATE_DATE" property="updateDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COUPLET_NUMBER" property="coupletNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGIN_ORDER_SN" property="originOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_VOUCHER" property="withdrawVoucher" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FLAG" property="flag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ACCT" property="isAcct" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BILL_FLAG" property="billFlag" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_DATE" property="tradeDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECREATE_FLAG" property="recreateFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_BANK_WITHDRAWING" property="isBankWithdrawing" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TRADE_MONEY" property="tradeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FEE_SUBSIDY_AMOUNT" property="feeSubsidyAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PLAN_SETTLE_AMOUNT" property="planSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REAL_SETTLE_AMOUNT" property="realSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SUSPEND_SETTLE_AMOUNT" property="suspendSettleAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TAG`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`TOKEN`,`CARD_NO`,`ORDER_SN`,`BANK_NAME`,`ROLE_TYPE`,`ORDER_TYPE`,`BANK_HOLDER`,`MERCHANT_NO`,`REAL_CARD_NO`,`RESULT_DESC`,`UPDATE_DATE`,`COUPLET_NUMBER`,`ORIGIN_ORDER_SN`,`WITHDRAW_VOUCHER`,`UID`,`FLAG`,`IS_ACCT`,`CHANNEL`,`BILL_FLAG`,`TRADE_DATE`,`RECREATE_FLAG`,`IS_BANK_WITHDRAWING`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`TRADE_MONEY`,`FEE_SUBSIDY_AMOUNT`,`PLAN_SETTLE_AMOUNT`,`REAL_SETTLE_AMOUNT`,`SUSPEND_SETTLE_AMOUNT`
    </sql>


            <!--insert:TP_LESHUA_COUNTEROFFER_FILE_REPORT-->
            <insert id="insert" >
            INSERT INTO TP_LESHUA_COUNTEROFFER_FILE_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="resultDesc != null">`RESULT_DESC`,</if>
        <if test="updateDate != null">`UPDATE_DATE`,</if>
        <if test="coupletNumber != null">`COUPLET_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="flag != null">`FLAG`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="tradeMoney != null">`TRADE_MONEY`,</if>
        <if test="isAcct != null">`IS_ACCT`,</if>
        <if test="channel != null">`CHANNEL`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="resultDesc != null">#{resultDesc,jdbcType=VARCHAR},</if>
        <if test="updateDate != null">#{updateDate,jdbcType=VARCHAR},</if>
        <if test="coupletNumber != null">#{coupletNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="flag != null">#{flag,jdbcType=TINYINT},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="tradeMoney != null">#{tradeMoney,jdbcType=DECIMAL},</if>
        <if test="isAcct != null">#{isAcct,jdbcType=TINYINT},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
    </trim>
            </insert>

            <!--根据订单号获取回盘记录-->
            <select id="getByOrderSn" resultMap="BaseResultMap">
                    SELECT
        id, flag, token, uid, trade_money, bank_name, card_no, result_desc, update_date
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        order_sn = #{orderSn, jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据订单号更新重新打款记录状态-->
            <update id="updateFlagByOrderSn" >
                    UPDATE
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        SET
        flag = #{flag,jdbcType=INTEGER},
        result_desc = #{resultDesc,jdbcType=VARCHAR},
        update_date = #{updateDate,jdbcType=VARCHAR}
        WHERE
        order_sn = #{orderSn, jdbcType=VARCHAR}
            </update>

            <!--根据商户token和交易日期获取回盘记录-->
            <select id="getByTokenAndTradeDate" resultMap="BaseResultMap">
                    SELECT
        sum(trade_money) as trade_money
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND
        trade_date = #{tradeDate,jdbcType=INTEGER}
        LIMIT 1
            </select>

            <!--查询商户某天的结算记录-->
            <select id="selectSettlementDataByToken" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        token = #{token, jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
        AND role_type = "MAIN"
        AND order_type = "SETTLEMENT"
            </select>
    </mapper>
