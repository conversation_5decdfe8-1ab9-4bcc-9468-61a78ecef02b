<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.mapper.MerchantMarketStoreQrcodeDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.dataobject.MerchantMarketStoreQrcodeDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="TOKEN" property="token" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="QRCODE_URL" property="qrcodeUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="TEMPLATE_QRCODE_URL" property="templateQrcodeUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UID" property="uid" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`QRCODE_URL`,`TEMPLATE_QRCODE_URL`,`UID`,`STORE_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MERCHANT_MARKET_STORE_QRCODE-->
            <insert id="insert" >
                    INSERT INTO TP_MERCHANT_MARKET_STORE_QRCODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="qrcodeUrl != null">`QRCODE_URL`,</if>
            <if test="templateQrcodeUrl != null">`TEMPLATE_QRCODE_URL`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="qrcodeUrl != null">#{qrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="templateQrcodeUrl != null">#{templateQrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

    <!--获取门店二维码-->
    <select id="getStoreQRCode" resultMap="BaseResultMap">
        SELECT /*MS-TP-MERCHANT-MARKET-STORE-QRCODE-GETSTOREQRCODE*/
        <include refid="Base_Column_List"/>
        FROM tp_merchant_market_store_qrcode
        WHERE uid=#{uid,jdbcType=INTEGER} and token=#{token,jdbcType=VARCHAR} and store_id=#{storeId,jdbcType=INTEGER}
        LIMIT 1
    </select>

    <!--更新二维码-->
    <update id="updateQrcodeUrl">
        UPDATE /*MS-TP-MERCHANT-MARKET-STORE-QRCODE-UPDATEQRCODEURL*/ tp_merchant_market_store_qrcode
        SET `QRCODE_URL` = #{qrcodeUrl,jdbcType=VARCHAR}
        WHERE `ID` = #{id,jdbcType=BIGINT}
    </update>

    <!--更新二维码-->
    <update id="updateTemplateQrcodeUrl">
        UPDATE /*MS-TP-MERCHANT-MARKET-STORE-QRCODE-UPDATETEMPLATEQRCODEURL*/ tp_merchant_market_store_qrcode
        SET `TEMPLATE_QRCODE_URL` = #{templateQrcodeUrl,jdbcType=VARCHAR}
        WHERE `ID` = #{id,jdbcType=BIGINT}
    </update>
</mapper>
