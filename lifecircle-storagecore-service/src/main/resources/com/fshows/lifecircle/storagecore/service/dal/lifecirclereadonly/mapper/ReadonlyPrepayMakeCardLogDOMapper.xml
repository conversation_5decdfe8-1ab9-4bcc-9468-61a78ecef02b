<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayMakeCardLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayMakeCardLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MAKE_NUMBER" property="makeNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MAKE_STATUS" property="makeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BATCH_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`OPERATE_ID`,`CARD_SPU_NAME`,`OPERATE_NAME`,`PUBLISH_ORG_ID`,`IS_DEL`,`FINISH_TIME`,`MAKE_NUMBER`,`MAKE_STATUS`,`SUBMIT_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--根据批次号查询制卡信息-->
            <select id="getMakeCarLogByBatchNo" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        tp_prepay_make_card_log
        where
        is_del = 0
        and batch_no = #{batchNo,jdbcType=VARCHAR}
            </select>
    </mapper>
