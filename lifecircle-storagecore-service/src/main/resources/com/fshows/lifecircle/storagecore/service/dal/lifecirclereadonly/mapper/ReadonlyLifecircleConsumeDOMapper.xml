<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyLifecircleConsumeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyLifecircleConsumeDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPENID" property="openid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISCOUNT" property="discount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_TOKEN" property="payToken" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CALL_BACK_URL" property="callBackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MODE" property="mode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MCHID" property="mchid" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RED_ID" property="redId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKREAD" property="markread" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRINT_NUM" property="printNum" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RED_PRE_ID" property="redPreId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_STATUS" property="payStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DISCOUNT_ID" property="discountId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECHARGEACT_ID" property="rechargeactId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PREFERENTIAL_ID" property="preferentialId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REPAIR_ORDER_STATUS" property="repairOrderStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME_AUTO" property="updateTimeAuto" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND" property="refund" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CASH_FEE" property="cashFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RATE_FEE" property="rateFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RED_MONEY" property="redMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COUPON_FEE" property="couponFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COMMISSION_FEE" property="commissionFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DISCOUNT_MONEY" property="discountMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_SUMPRICE" property="orderSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AUTOWIPINGZERO" property="autowipingzero" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ADDITIONAL_PRICE" property="additionalPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COMMISSION_RATE_FEE" property="commissionRateFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RECHARGEACT_AMOUNT" property="rechargeactAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PREFERENTIAL_AMOUNT" property="preferentialAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`TOKEN`,`OPENID`,`REMARK`,`ORDER_SN`,`BANK_TYPE`,`DISCOUNT`,`PAY_TOKEN`,`CALL_BACK_URL`,`MERCHANT_ORDER_SN`,`UID`,`MODE`,`TYPE`,`MCHID`,`RED_ID`,`USER_ID`,`AGENT_ID`,`CHANNEL`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`MARKREAD`,`PRINT_NUM`,`RED_PRE_ID`,`CASHIER_ID`,`PAY_STATUS`,`CREATE_TIME`,`DISCOUNT_ID`,`REFUND_STATUS`,`RECHARGEACT_ID`,`PREFERENTIAL_ID`,`REPAIR_ORDER_STATUS`,`UPDATE_TIME_AUTO`,`FEE`,`REFUND`,`CASH_FEE`,`RATE_FEE`,`RED_MONEY`,`COUPON_FEE`,`ORDER_PRICE`,`COMMISSION_FEE`,`DISCOUNT_MONEY`,`ORDER_SUMPRICE`,`AUTOWIPINGZERO`,`ADDITIONAL_PRICE`,`COMMISSION_RATE_FEE`,`RECHARGEACT_AMOUNT`,`PREFERENTIAL_AMOUNT`
    </sql>


            <!--insert:TP_LIFECIRCLE_CONSUME-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_CONSUME
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="openid != null">`OPENID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="bankType != null">`BANK_TYPE`,</if>
        <if test="discount != null">`DISCOUNT`,</if>
        <if test="payToken != null">`PAY_TOKEN`,</if>
        <if test="callBackUrl != null">`CALL_BACK_URL`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="mode != null">`MODE`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="redId != null">`RED_ID`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="payTime != null">`PAY_TIME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="markread != null">`MARKREAD`,</if>
        <if test="printNum != null">`PRINT_NUM`,</if>
        <if test="redPreId != null">`RED_PRE_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="payStatus != null">`PAY_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="discountId != null">`DISCOUNT_ID`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="rechargeactId != null">`RECHARGEACT_ID`,</if>
        <if test="preferentialId != null">`PREFERENTIAL_ID`,</if>
        <if test="repairOrderStatus != null">`REPAIR_ORDER_STATUS`,</if>
        <if test="updateTimeAuto != null">`UPDATE_TIME_AUTO`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="refund != null">`REFUND`,</if>
        <if test="cashFee != null">`CASH_FEE`,</if>
        <if test="rateFee != null">`RATE_FEE`,</if>
        <if test="redMoney != null">`RED_MONEY`,</if>
        <if test="couponFee != null">`COUPON_FEE`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="commissionFee != null">`COMMISSION_FEE`,</if>
        <if test="discountMoney != null">`DISCOUNT_MONEY`,</if>
        <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
        <if test="autowipingzero != null">`AUTOWIPINGZERO`,</if>
        <if test="additionalPrice != null">`ADDITIONAL_PRICE`,</if>
        <if test="commissionRateFee != null">`COMMISSION_RATE_FEE`,</if>
        <if test="rechargeactAmount != null">`RECHARGEACT_AMOUNT`,</if>
        <if test="preferentialAmount != null">`PREFERENTIAL_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="openid != null">#{openid,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="bankType != null">#{bankType,jdbcType=VARCHAR},</if>
        <if test="discount != null">#{discount,jdbcType=VARCHAR},</if>
        <if test="payToken != null">#{payToken,jdbcType=VARCHAR},</if>
        <if test="callBackUrl != null">#{callBackUrl,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="mode != null">#{mode,jdbcType=TINYINT},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="mchid != null">#{mchid,jdbcType=TINYINT},</if>
        <if test="redId != null">#{redId,jdbcType=INTEGER},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="markread != null">#{markread,jdbcType=TINYINT},</if>
        <if test="printNum != null">#{printNum,jdbcType=TINYINT},</if>
        <if test="redPreId != null">#{redPreId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="discountId != null">#{discountId,jdbcType=INTEGER},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="rechargeactId != null">#{rechargeactId,jdbcType=INTEGER},</if>
        <if test="preferentialId != null">#{preferentialId,jdbcType=INTEGER},</if>
        <if test="repairOrderStatus != null">#{repairOrderStatus,jdbcType=INTEGER},</if>
        <if test="updateTimeAuto != null">#{updateTimeAuto,jdbcType=TIMESTAMP},</if>
        <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
        <if test="refund != null">#{refund,jdbcType=DECIMAL},</if>
        <if test="cashFee != null">#{cashFee,jdbcType=DECIMAL},</if>
        <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
        <if test="redMoney != null">#{redMoney,jdbcType=DECIMAL},</if>
        <if test="couponFee != null">#{couponFee,jdbcType=DECIMAL},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="commissionFee != null">#{commissionFee,jdbcType=DECIMAL},</if>
        <if test="discountMoney != null">#{discountMoney,jdbcType=DECIMAL},</if>
        <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
        <if test="autowipingzero != null">#{autowipingzero,jdbcType=DECIMAL},</if>
        <if test="additionalPrice != null">#{additionalPrice,jdbcType=DECIMAL},</if>
        <if test="commissionRateFee != null">#{commissionRateFee,jdbcType=DECIMAL},</if>
        <if test="rechargeactAmount != null">#{rechargeactAmount,jdbcType=DECIMAL},</if>
        <if test="preferentialAmount != null">#{preferentialAmount,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据订单号查询商户订单号-->
            <select id="getMerchantOrderSnListByOrderSn" resultType="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantOrderSnDO">
                    select
        order_sn as orderSn,merchant_order_sn as merchantOrderSn
        from tp_lifecircle_consume
        where
        order_sn in
        <foreach collection="list" open="(" item="orderSn" separator="," close=")">
            #{orderSn,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
