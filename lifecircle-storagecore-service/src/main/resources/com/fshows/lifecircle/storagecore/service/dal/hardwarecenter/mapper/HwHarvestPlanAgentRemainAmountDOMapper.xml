<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwHarvestPlanAgentRemainAmountDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwHarvestPlanAgentRemainAmountDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="IS_STANDARD" property="isStandard" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="REMAIN_AMOUNT" property="remainAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`INIT_SN`,`IS_DEL`,`IS_STANDARD`,`CREATE_TIME`,`UPDATE_TIME`,`REMAIN_AMOUNT`
        </sql>


        <!--insert:HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT-->
        <insert id="insert">
            INSERT INTO HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="initSn != null">`INIT_SN`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="isStandard != null">`IS_STANDARD`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="remainAmount != null">`REMAIN_AMOUNT`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="isStandard != null">#{isStandard,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="remainAmount != null">#{remainAmount,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--批量插入设备结算余额信息-->
        <insert id="insertBatch">
            INSERT INTO
            HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT
            (`INIT_SN`,`REMAIN_AMOUNT`)
            VALUES
            <foreach collection="list" item="item" separator=",">
                (
                #{item.initSn,jdbcType=VARCHAR},
                #{item.remainAmount,jdbcType=DECIMAL}
                )
            </foreach>
        </insert>
    </mapper>
