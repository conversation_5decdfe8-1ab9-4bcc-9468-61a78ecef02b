<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ExtReadonlyMerchantAuthorizeApplyDOMapper">

    <!--分页查询商户实名认证列表 pageCount-->
    <select id="findMerchantAuthorizeCrmListByPageCount" resultType="int">
        SELECT
        COUNT(*) AS total
        FROM
        tp_merchant_authorize_apply apply
        LEFT JOIN tp_users users
        ON apply.uid = users.id
        LEFT JOIN tp_user u
        ON users.belong = u.id
        WHERE apply.is_del = 0
        AND users.parent_id = 0
        <if test="authorizeType != null">
            AND apply.authorize_type = #{authorizeType, jdbcType=INTEGER}
        </if>
        <if test="startDate != null and startDate != ''">
            AND apply.create_time <![CDATA[ >= ]]> #{startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null and endDate != ''">
            AND apply.create_time <![CDATA[ <= ]]>  #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="subMchid != null and subMchid != ''">
            AND apply.sub_mchid = #{subMchid,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and company != ''">
            AND users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName != null and belongName != '' ">
            AND u.username LIKE CONCAT (#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="auditStatus != null and auditStatus != -1  and auditStatus != 0">
            AND apply.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="applyChannel!=null and applyChannel !=0">
            AND apply.apply_channel=#{applyChannel,jdbcType=INTEGER}
        </if>

    </select>
</mapper>
