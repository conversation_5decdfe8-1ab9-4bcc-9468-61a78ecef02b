<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentModelCommissionRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentModelCommissionRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRODUCT_TYPE" property="productType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMISSION_TYPE" property="commissionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_NEED_PARSE_URL" property="isNeedParseUrl" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="COMMISSION" property="commission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COMMISSION_UNIT" property="commissionUnit" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`IS_DEL`,`ACTIVITY_ID`,`EQUIPMENT_ID`,`PRODUCT_TYPE`,`COMMISSION_TYPE`,`IS_NEED_PARSE_URL`,`CREATE_TIME`,`UPDATE_TIME`,`COMMISSION`,`COMMISSION_UNIT`
    </sql>


            <!--insert:HW_EQUIPMENT_MODEL_COMMISSION_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_MODEL_COMMISSION_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="isNeedParseUrl != null">`IS_NEED_PARSE_URL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="commission != null">`COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="isNeedParseUrl != null">#{isNeedParseUrl,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="commission != null">#{commission,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--查询全部设备最高返佣金额-->
            <select id="findEquipmentCommissionList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_EQUIPMENT_MODEL_COMMISSION_RELATION
        WHERE IS_DEL = 0
            </select>

            <!--查询全部设备最高返佣金额-->
            <select id="getByActivityIdAndEquipmentIdAndProductType" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-MODEL-COMMISSION-RELATION-GETBYACTIVITYIDANDEQUIPMENTIDANDPRODUCTTYPE*/  <include refid="Base_Column_List" />
        FROM `hw_equipment_model_commission_relation`
        WHERE activity_id = #{activityId,jdbcType=INTEGER}
        AND equipment_id = #{equipmentId,jdbcType=INTEGER}
        AND product_type = #{product_type,jdbcType=INTEGER}
        AND is_del = 0
        LIMIT 1
            </select>
    </mapper>
