<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleMinaConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleMinaConfigDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APPID" property="appid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCHID" property="mchid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUIDATION_CONFIG_STATUS" property="liquidationConfigStatus" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPID`,`MCHID`,`UID`,`CHANNEL_ID`,`CREATE_TIME`,`UPDATE_TIME`,`LIQUIDATION_CONFIG_STATUS`
    </sql>


            <!--insert:TP_LIFECIRCLE_MINA_CONFIG-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_MINA_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="appid != null">`APPID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="liquidationConfigStatus != null">`LIQUIDATION_CONFIG_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="liquidationConfigStatus != null">#{liquidationConfigStatus,jdbcType=TINYINT},</if>
    </trim>
            </insert>

            <!--根据子商户号查询商户id-->
            <select id="getBySubMchId" resultType="java.lang.Integer">
                    SELECT
        uid
        FROM
        tp_lifecircle_mina_config
        WHERE
        mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
