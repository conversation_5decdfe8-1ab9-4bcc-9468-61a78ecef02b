<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.FinanceplatformDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 35 as fg,'FP_EQUIPMENT_COMMISSION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,OEM_NAME,PT_MONTH,EQUIPMENT_SN,BUSINESS_UUID,AGENT_USERNAME,BUSINESS_EXT_NUM,MARKET_USERNAME,MERCHANT_USERNAME,SALESMAN_USERNAME,BELONG_AGENT_USERNAME,BELONG_MARKET_USERNAME,SUPER_SALESMAN_USERNAME,BELONG_SALESMAN_USERNAME,BELONG_SUPER_SALESMAN_USERNAME,UID,OEM_ID,OWN_RUN,AGENT_ID,DATA_TYPE,MARKET_ID,SALESMAN_ID,BUSINESS_DATE,BELONG_AGENT_ID,BELONG_MARKET_ID,SUPER_SALESMAN_ID,BELONG_SALESMAN_ID,VALID_TRADE_NUMBER,EFFECTIVE_USER_COUNT,BELONG_SUPER_SALESMAN_ID,CREATE_TIME,UPDATE_TIME,REBATE_AMOUNT,VALID_TRADE_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_EQUIPMENT_COMMISSION'
            AND COLUMN_NAME in('ID','FEE_CODE','OEM_NAME','PT_MONTH','EQUIPMENT_SN','BUSINESS_UUID','AGENT_USERNAME','BUSINESS_EXT_NUM','MARKET_USERNAME','MERCHANT_USERNAME','SALESMAN_USERNAME','BELONG_AGENT_USERNAME','BELONG_MARKET_USERNAME','SUPER_SALESMAN_USERNAME','BELONG_SALESMAN_USERNAME','BELONG_SUPER_SALESMAN_USERNAME','UID','OEM_ID','OWN_RUN','AGENT_ID','DATA_TYPE','MARKET_ID','SALESMAN_ID','BUSINESS_DATE','BELONG_AGENT_ID','BELONG_MARKET_ID','SUPER_SALESMAN_ID','BELONG_SALESMAN_ID','VALID_TRADE_NUMBER','EFFECTIVE_USER_COUNT','BELONG_SUPER_SALESMAN_ID','CREATE_TIME','UPDATE_TIME','REBATE_AMOUNT','VALID_TRADE_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 23 as fg,'FS_PROJECT_FEE_CODE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,STOP_TIME,DS_NAME,REMARK,FEE_CODE,FEE_TYPE,STORE_CODE,PROJECT_FEE_ID,ERP_SUBJECT_CODE,FEE_PROJECT_NAME,STATUS,IS_SYNC_U8,IS_DETAILS,SYNC_U8_TYPE,ERP_SYNC_STATUS,COMMISSION_RULES_FLAG,FEE_PROJECT_ATTRIBUTE,PAYMENT_CHECK_ACCOUNT,PROJECT_CATEGORY_CODE,PROJECT_BIG_CATEGORY_CODE,CREATE_TIME,UPDATE_TIME,FEE_RATE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FS_PROJECT_FEE_CODE'
            AND COLUMN_NAME in('ID','STOP_TIME','DS_NAME','REMARK','FEE_CODE','FEE_TYPE','STORE_CODE','PROJECT_FEE_ID','ERP_SUBJECT_CODE','FEE_PROJECT_NAME','STATUS','IS_SYNC_U8','IS_DETAILS','SYNC_U8_TYPE','ERP_SYNC_STATUS','COMMISSION_RULES_FLAG','FEE_PROJECT_ATTRIBUTE','PAYMENT_CHECK_ACCOUNT','PROJECT_CATEGORY_CODE','PROJECT_BIG_CATEGORY_CODE','CREATE_TIME','UPDATE_TIME','FEE_RATE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'FP_PARTNER_BILL_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,AGENT_USERNAME,PARTNER_USERNAME,AGENT_ID,PT_MONTH,PARTNER_ID,BUSINESS_DATE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,CREATE_TIME,UPDATE_TIME,COEFFICIENT_DIFF,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,ACTUAL_PAYABLE_COMMISSION,AGENT_SETTLEMENT_COEFFICIENT,PARTNER_SETTLEMENT_COEFFICIENT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_PARTNER_BILL_DETAIL'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','AGENT_USERNAME','PARTNER_USERNAME','AGENT_ID','PT_MONTH','PARTNER_ID','BUSINESS_DATE','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','CREATE_TIME','UPDATE_TIME','COEFFICIENT_DIFF','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION','ACTUAL_PAYABLE_COMMISSION','AGENT_SETTLEMENT_COEFFICIENT','PARTNER_SETTLEMENT_COEFFICIENT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 31 as fg,'FP_MERCHANT_COMMISSION_MONTH' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,AGENT_USERNAME,MARKET_MANAGER,AGENT_COMPANYNAME,MERCHANT_USERNAME,SALESMAN_USERNAME,SUPER_SALESMAN_USERNAME,UID,CPM_NUM,AGENT_ID,PT_MONTH,MARKET_ID,IS_T1_SETTLE,SALESMAN_ID,BUSINESS_DATE,SUPER_SALESMAN_ID,EXPOSURE_NUM_HFIVE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,EXPOSURE_NUM_APPLET,CREATE_TIME,UPDATE_TIME,GRANT_FEE,MARKET_FEE,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,ACTUAL_PAYABLE_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_MERCHANT_COMMISSION_MONTH'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','AGENT_USERNAME','MARKET_MANAGER','AGENT_COMPANYNAME','MERCHANT_USERNAME','SALESMAN_USERNAME','SUPER_SALESMAN_USERNAME','UID','CPM_NUM','AGENT_ID','PT_MONTH','MARKET_ID','IS_T1_SETTLE','SALESMAN_ID','BUSINESS_DATE','SUPER_SALESMAN_ID','EXPOSURE_NUM_HFIVE','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','EXPOSURE_NUM_APPLET','CREATE_TIME','UPDATE_TIME','GRANT_FEE','MARKET_FEE','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION','ACTUAL_PAYABLE_COMMISSION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 29 as fg,'fp_agent_commission_month' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,REMARKS,SOURCE_TYPE,BUSINESS_NUM,AGENT_USERNAME,AGENT_COMPANYNAME,PT_DAY,CPM_NUM,AGENT_ID,PT_MONTH,DATA_TYPE,IS_T1_SETTLE,BUSINESS_DATE,REVIEW_STATUS,EXPOSURE_NUM_HFIVE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,EXPOSURE_NUM_APPLET,CREATE_TIME,UPDATE_TIME,TAX_RATE,GRANT_FEE,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,SETTLEMENT_COEFFICIENT,ACTUAL_PAYABLE_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'fp_agent_commission_month'
            AND COLUMN_NAME in('ID','FEE_CODE','REMARKS','SOURCE_TYPE','BUSINESS_NUM','AGENT_USERNAME','AGENT_COMPANYNAME','PT_DAY','CPM_NUM','AGENT_ID','PT_MONTH','DATA_TYPE','IS_T1_SETTLE','BUSINESS_DATE','REVIEW_STATUS','EXPOSURE_NUM_HFIVE','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','EXPOSURE_NUM_APPLET','CREATE_TIME','UPDATE_TIME','TAX_RATE','GRANT_FEE','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION','SETTLEMENT_COEFFICIENT','ACTUAL_PAYABLE_COMMISSION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 31 as fg,'FP_MERCHANT_COMMISSION_DAY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,AGENT_USERNAME,MARKET_MANAGER,AGENT_COMPANYNAME,MERCHANT_USERNAME,SALESMAN_USERNAME,SUPER_SALESMAN_USERNAME,UID,PT_DAY,CPM_NUM,AGENT_ID,MARKET_ID,IS_T1_SETTLE,SALESMAN_ID,BUSINESS_DATE,SUPER_SALESMAN_ID,EXPOSURE_NUM_HFIVE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,EXPOSURE_NUM_APPLET,CREATE_TIME,UPDATE_TIME,GRANT_FEE,MARKET_FEE,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,ACTUAL_PAYABLE_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_MERCHANT_COMMISSION_DAY'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','AGENT_USERNAME','MARKET_MANAGER','AGENT_COMPANYNAME','MERCHANT_USERNAME','SALESMAN_USERNAME','SUPER_SALESMAN_USERNAME','UID','PT_DAY','CPM_NUM','AGENT_ID','MARKET_ID','IS_T1_SETTLE','SALESMAN_ID','BUSINESS_DATE','SUPER_SALESMAN_ID','EXPOSURE_NUM_HFIVE','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','EXPOSURE_NUM_APPLET','CREATE_TIME','UPDATE_TIME','GRANT_FEE','MARKET_FEE','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION','ACTUAL_PAYABLE_COMMISSION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 27 as fg,'FP_STORE_COMMISSION_MONTH' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,MERCHANT_TYPE,AGENT_USERNAME,MARKET_MANAGER,STORE_USERNAME,AGENT_COMPANYNAME,MERCHANT_USERNAME,SALESMAN_USERNAME,SUPER_SALESMAN_USERNAME,UID,AGENT_ID,PT_MONTH,STORE_ID,MARKET_ID,SALESMAN_ID,BUSINESS_DATE,SUPER_SALESMAN_ID,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,CREATE_TIME,UPDATE_TIME,NORMAL_TRADE_AMOUNT,REFUND_TRADE_AMOUNT,SETTLEMENT_COEFFICIENT,ACTUAL_PAYABLE_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_STORE_COMMISSION_MONTH'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','MERCHANT_TYPE','AGENT_USERNAME','MARKET_MANAGER','STORE_USERNAME','AGENT_COMPANYNAME','MERCHANT_USERNAME','SALESMAN_USERNAME','SUPER_SALESMAN_USERNAME','UID','AGENT_ID','PT_MONTH','STORE_ID','MARKET_ID','SALESMAN_ID','BUSINESS_DATE','SUPER_SALESMAN_ID','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','CREATE_TIME','UPDATE_TIME','NORMAL_TRADE_AMOUNT','REFUND_TRADE_AMOUNT','SETTLEMENT_COEFFICIENT','ACTUAL_PAYABLE_COMMISSION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 29 as fg,'FP_MERCHANT_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,FEE_CODE,ORDER_SN,ORDER_TYPE,AGENT_USERNAME,MARKET_MANAGER,AGENT_COMPANYNAME,MERCHANT_USERNAME,SALESMAN_USERNAME,SUPER_SALESMAN_USERNAME,UID,AGENT_ID,PAY_TYPE,MARKET_ID,SALESMAN_ID,BUSINESS_DATE,ORDER_RISK_TYPE,SUPER_SALESMAN_ID,PAY_TIME,CREATE_TIME,UPDATE_TIME,EXT4,RATE_NUM,ORDER_PRICE,ORDER_SUMPRICE,ACTUAL_PAYABLE_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_MERCHANT_ORDER'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','FEE_CODE','ORDER_SN','ORDER_TYPE','AGENT_USERNAME','MARKET_MANAGER','AGENT_COMPANYNAME','MERCHANT_USERNAME','SALESMAN_USERNAME','SUPER_SALESMAN_USERNAME','UID','AGENT_ID','PAY_TYPE','MARKET_ID','SALESMAN_ID','BUSINESS_DATE','ORDER_RISK_TYPE','SUPER_SALESMAN_ID','PAY_TIME','CREATE_TIME','UPDATE_TIME','EXT4','RATE_NUM','ORDER_PRICE','ORDER_SUMPRICE','ACTUAL_PAYABLE_COMMISSION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'FP_AGENT_COMMISSION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,AGENT_ID,ERP_SLAVE_SYNC_TIMESTAMP,ERP_MASTER_SYNC_TIMESTAMP,PT_MONTH,SOURCE_TYPE,BUSINESS_EXT_NUM,PROJECT_FEE_CODE,COMMISSION_SLAVE_ID,SYNC_BILL_FLAG,TAX_PROCESS_FLAG,AGENT_MERGE_TIMES,ERP_SLAVE_SYNC_STATUS,ERP_MASTER_SYNC_STATUS,ERP_SLAVE_SYNC_FAIL_TIMES,ERP_MASTER_SYNC_FAIL_TIMES,CREATE_TIME,UPDATE_TIME,BUSINESS_TIME,AMOUNT,TAX_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_AGENT_COMMISSION'
            AND COLUMN_NAME in('ID','AGENT_ID','ERP_SLAVE_SYNC_TIMESTAMP','ERP_MASTER_SYNC_TIMESTAMP','PT_MONTH','SOURCE_TYPE','BUSINESS_EXT_NUM','PROJECT_FEE_CODE','COMMISSION_SLAVE_ID','SYNC_BILL_FLAG','TAX_PROCESS_FLAG','AGENT_MERGE_TIMES','ERP_SLAVE_SYNC_STATUS','ERP_MASTER_SYNC_STATUS','ERP_SLAVE_SYNC_FAIL_TIMES','ERP_MASTER_SYNC_FAIL_TIMES','CREATE_TIME','UPDATE_TIME','BUSINESS_TIME','AMOUNT','TAX_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'FP_SALESMAN_BILL_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,AGENT_USERNAME,SALESMAN_USERNAME,AGENT_ID,PT_MONTH,SALESMAN_ID,BUSINESS_DATE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT,CREATE_TIME,UPDATE_TIME,COEFFICIENT_DIFF,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,ACTUAL_PAYABLE_COMMISSION,AGENT_SETTLEMENT_COEFFICIENT,SALESMAN_SETTLEMENT_COEFFICIENT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_SALESMAN_BILL_DETAIL'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','AGENT_USERNAME','SALESMAN_USERNAME','AGENT_ID','PT_MONTH','SALESMAN_ID','BUSINESS_DATE','NORMAL_TRADE_COUNT','REFUND_TRADE_COUNT','CREATE_TIME','UPDATE_TIME','COEFFICIENT_DIFF','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION','ACTUAL_PAYABLE_COMMISSION','AGENT_SETTLEMENT_COEFFICIENT','SALESMAN_SETTLEMENT_COEFFICIENT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE_CODE,SOURCE_TYPE,AGENT_USERNAME,AGENT_COMPANYNAME,SETTLEMENT_AGENT_USERNAME,CURRENT_ASSESSMENT_QUARTER,HIGHEST_AVG_MONTHLY_FLOW_QUARTER,AGENT_ID,PT_MONTH,BUSINESS_DATE,SETTLEMENT_AGENT_ID,CREATE_TIME,UPDATE_TIME,NET_INCREASE_FLOW,SMALL_MERCHANT_RATIO,SETTLEMENT_COEFFICIENT,ACTUAL_PAYABLE_COMMISSION,CURRENT_AVG_QUARTERLY_FLOW,HIGHEST_AVG_QUARTERLY_FLOW' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH'
            AND COLUMN_NAME in('ID','FEE_CODE','SOURCE_TYPE','AGENT_USERNAME','AGENT_COMPANYNAME','SETTLEMENT_AGENT_USERNAME','CURRENT_ASSESSMENT_QUARTER','HIGHEST_AVG_MONTHLY_FLOW_QUARTER','AGENT_ID','PT_MONTH','BUSINESS_DATE','SETTLEMENT_AGENT_ID','CREATE_TIME','UPDATE_TIME','NET_INCREASE_FLOW','SMALL_MERCHANT_RATIO','SETTLEMENT_COEFFICIENT','ACTUAL_PAYABLE_COMMISSION','CURRENT_AVG_QUARTERLY_FLOW','HIGHEST_AVG_QUARTERLY_FLOW')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 25 as fg,'FS_PAYABLE_SLAVE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ERP_SYNC_TIMESTAMP,FEE_CODE,PAYABLE_SUB_NUM,PAYABLE_BILL_NUM,DEDUCT_DETAIL_URL,NORMAL_DETAIL_URL,BILL_DATE,BILL_STATUS,IS_T1_SETTLE,ERP_SYNC_STATUS,BUSINESS_STATUS,EXCEL_SYNC_STATUS,ERP_SYNC_FAIL_TIMES,EXCEL_SYNC_FAIL_TIMES,CREATE_TIME,UPDATE_TIME,BILL_AMOUNT,LOCK_AMOUNT,PAID_AMOUNT,SYNC_ERP_AMOUNT,NORMAL_TRADE_AMOUNT,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FS_PAYABLE_SLAVE'
            AND COLUMN_NAME in('ID','ERP_SYNC_TIMESTAMP','FEE_CODE','PAYABLE_SUB_NUM','PAYABLE_BILL_NUM','DEDUCT_DETAIL_URL','NORMAL_DETAIL_URL','BILL_DATE','BILL_STATUS','IS_T1_SETTLE','ERP_SYNC_STATUS','BUSINESS_STATUS','EXCEL_SYNC_STATUS','ERP_SYNC_FAIL_TIMES','EXCEL_SYNC_FAIL_TIMES','CREATE_TIME','UPDATE_TIME','BILL_AMOUNT','LOCK_AMOUNT','PAID_AMOUNT','SYNC_ERP_AMOUNT','NORMAL_TRADE_AMOUNT','PAYABLE_COMMISSION','REFUND_TRADE_AMOUNT','DEDUCTION_COMMISSION')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
