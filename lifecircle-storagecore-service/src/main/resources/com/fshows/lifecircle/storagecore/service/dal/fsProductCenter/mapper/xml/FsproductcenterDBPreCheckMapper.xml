<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.mapper.FsproductcenterDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 12 as fg,'GS_PURCHASE_IMPORT_ERROR_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CODE,GS_UID,PRICE,GOOD_NAME,GS_STORE_ID,BUSINESS_ID,ERROR_MESSAGE,IS_DEL,CREATE_TIME,UPDATE_TIME,NUM' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'GS_PURCHASE_IMPORT_ERROR_GOODS'
            AND COLUMN_NAME in('ID','CODE','GS_UID','PRICE','GOOD_NAME','GS_STORE_ID','BUSINESS_ID','ERROR_MESSAGE','IS_DEL','CREATE_TIME','UPDATE_TIME','NUM')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 34 as fg,'GS_ONLINE_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PLU,SKU,SPU,CODE,EXT1,EXT2,EXT3,NAME,GS_UID,SPU_ID,REMARK,GOODS_ID,CREATE_BY,GS_STORE_ID,SIMPLE_SPELL,ONLINE_GOODS_ID,ONLINE_SUB_SPU_ID,ONLINE_CATEGORY_ID,IS_DEL,SALE_COUNT,CATEGORY_ID,SALE_WEIGHT,SELL_STATUS,IS_UNLIMITED_STOCK,CREATE_TIME,UPDATE_TIME,REVISE_PRICE_TIME,UPDATE_PRICE_TIME,PRICE,STOCK_NUM,SALE_MONEY,STOCK_PRICE,REVISE_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'GS_ONLINE_GOODS'
            AND COLUMN_NAME in('ID','PLU','SKU','SPU','CODE','EXT1','EXT2','EXT3','NAME','GS_UID','SPU_ID','REMARK','GOODS_ID','CREATE_BY','GS_STORE_ID','SIMPLE_SPELL','ONLINE_GOODS_ID','ONLINE_SUB_SPU_ID','ONLINE_CATEGORY_ID','IS_DEL','SALE_COUNT','CATEGORY_ID','SALE_WEIGHT','SELL_STATUS','IS_UNLIMITED_STOCK','CREATE_TIME','UPDATE_TIME','REVISE_PRICE_TIME','UPDATE_PRICE_TIME','PRICE','STOCK_NUM','SALE_MONEY','STOCK_PRICE','REVISE_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 14 as fg,'GS_STOCK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GS_UID,GOODS_ID,CREATE_BY,UPDATE_BY,GOODS_NAME,GS_STORE_ID,GS_STORE_NAME,DEL_FLAG,IS_UNLIMITED_STOCK,CREATE_TIME,UPDATE_TIME,STOCK_NUM,AVERAGE_COST' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'GS_STOCK'
            AND COLUMN_NAME in('ID','GS_UID','GOODS_ID','CREATE_BY','UPDATE_BY','GOODS_NAME','GS_STORE_ID','GS_STORE_NAME','DEL_FLAG','IS_UNLIMITED_STOCK','CREATE_TIME','UPDATE_TIME','STOCK_NUM','AVERAGE_COST')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'GS_STOCK_SOLD_LIMIT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GS_UID,GOODS_ID,GS_STORE_ID,IS_DEL,DAILY_RESET,RESET_SOLD_NUM,SOLD_NUM_LIMIT,CURRENT_SOLD_NUM,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'GS_STOCK_SOLD_LIMIT'
            AND COLUMN_NAME in('ID','GS_UID','GOODS_ID','GS_STORE_ID','IS_DEL','DAILY_RESET','RESET_SOLD_NUM','SOLD_NUM_LIMIT','CURRENT_SOLD_NUM','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
