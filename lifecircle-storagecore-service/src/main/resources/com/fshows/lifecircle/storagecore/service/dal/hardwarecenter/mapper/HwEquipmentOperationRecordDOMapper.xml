<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentOperationRecordDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentOperationRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CREATER" property="creater" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATE_CONTENT" property="operateContent" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATE_MODULE" property="operateModule" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`REMARK`,`CREATER`,`JOB_NUMBER`,`OPERATE_CONTENT`,`OPERATE_MODULE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:HW_EQUIPMENT_OPERATION_RECORD-->
    <insert id="insert">
        INSERT INTO HW_EQUIPMENT_OPERATION_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="operateContent != null">`OPERATE_CONTENT`,</if>
            <if test="operateModule != null">`OPERATE_MODULE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="operateContent != null">#{operateContent,jdbcType=VARCHAR},</if>
            <if test="operateModule != null">#{operateModule,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--insertBatch:HW_EQUIPMENT_OPERATION_RECORD-->
    <insert id="insertBatch">
        INSERT INTO HW_EQUIPMENT_OPERATION_RECORD
        (operate_module, operate_content, creater, job_number, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.operateModule, jdbcType=TINYINT},
            #{item.operateContent, jdbcType=VARCHAR},
            #{item.creater, jdbcType=VARCHAR},
            #{item.jobNumber, jdbcType=VARCHAR},
            #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </insert>
    </mapper>
