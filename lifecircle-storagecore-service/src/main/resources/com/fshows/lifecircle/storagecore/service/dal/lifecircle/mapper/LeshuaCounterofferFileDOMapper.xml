<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LeshuaCounterofferFileDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LeshuaCounterofferFileDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TRADE_NUM" property="tradeNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_DATE" property="tradeDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEAL_STATUS" property="dealStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TRADE_MONEY" property="tradeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FILE_NAME`,`PLATFORM_ORG_ID`,`CHANNEL`,`TRADE_NUM`,`TRADE_DATE`,`DEAL_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`TRADE_MONEY`
    </sql>


            <!--insert:TP_LESHUA_COUNTEROFFER_FILE-->
            <insert id="insert" >
                <selectKey keyProperty="id" resultType="int" order="AFTER">
        SELECT /*MS-TP-LESHUA-COUNTEROFFER-FILE-INSERT*/  LAST_INSERT_ID() as id
    </selectKey>
    INSERT INTO TP_LESHUA_COUNTEROFFER_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="tradeMoney != null">`TRADE_MONEY`,</if>
        <if test="fileName != null">`FILE_NAME`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="tradeNum != null">`TRADE_NUM`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="dealStatus != null">`DEAL_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="platformOrgId != null">`platform_org_id`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="tradeMoney != null">#{tradeMoney,jdbcType=DECIMAL},</if>
        <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="tradeNum != null">#{tradeNum,jdbcType=INTEGER},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="dealStatus != null">#{dealStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR}</if>
    </trim>
            </insert>

            <!--根据交易时间和通道查询回盘文件记录-->
            <select id="getByTradeDateAndChannelAndPlatformOrgId" resultMap="BaseResultMap">
                    SELECT
        id,file_name,deal_status
        FROM
        TP_LESHUA_COUNTEROFFER_FILE
        WHERE
        trade_date = #{tradeDate,jdbcType=INTEGER}
        AND channel = #{channel,jdbcType=TINYINT}
        and platform_org_id = #{platformOrgId,jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据id更新回盘文件记录总金额和总记录数-->
            <update id="updateTradeMoneyById" >
                    UPDATE
            TP_LESHUA_COUNTEROFFER_FILE
        SET
            trade_money = #{tradeMoney,jdbcType=DECIMAL},
            trade_num = #{tradeNum,jdbcType=INTEGER},
            file_name = #{fileName,jdbcType=VARCHAR}
        WHERE
            id = #{id,jdbcType=INTEGER}
            </update>

            <!--根据id更新回盘文件记录的处理状态-->
            <update id="updateDealStatusById" >
                    UPDATE
        TP_LESHUA_COUNTEROFFER_FILE
        SET
        deal_status = #{dealStatus,jdbcType=TINYINT}
        WHERE
        id = #{id,jdbcType=INTEGER}
            </update>

            <!--通过id更新trade_moeny,trade_num和deal_status-->
            <update id="updateTradeMoneyAndDealStatusById" >
                    UPDATE
        TP_LESHUA_COUNTEROFFER_FILE
        SET
        trade_money = #{tradeMoney,jdbcType=DECIMAL},
        trade_num = #{tradeNum,jdbcType=INTEGER},
        deal_status = #{dealStatus,jdbcType=TINYINT}
        WHERE
        id = #{id,jdbcType=INTEGER}
            </update>
    </mapper>
