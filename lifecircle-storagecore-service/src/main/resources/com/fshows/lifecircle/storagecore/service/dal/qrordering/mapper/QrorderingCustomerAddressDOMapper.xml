<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingCustomerAddressDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingCustomerAddressDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="LATITUDE" property="latitude" jdbcType="REAL"
                    javaType="Float"/>

            <result column="LONGITUDE" property="longitude" jdbcType="REAL"
                    javaType="Float"/>

            <result column="ADDRESS_ID" property="addressId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="NUMBER_PLATE" property="numberPlate" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_NAME" property="receiptName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_PHONE" property="receiptPhone" jdbcType="CHAR"
                    javaType="String"/>

            <result column="RECEIPT_ADDRESS" property="receiptAddress" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SEX" property="sex" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="USE_TIME" property="useTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`LATITUDE`,`LONGITUDE`,`ADDRESS_ID`,`CUSTOMER_ID`,`NUMBER_PLATE`,`RECEIPT_NAME`,`RECEIPT_PHONE`,`RECEIPT_ADDRESS`,`SEX`,`DEL_FLAG`,`USE_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--insert:TP_QRORDERING_CUSTOMER_ADDRESS-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_CUSTOMER_ADDRESS
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="latitude != null">`LATITUDE`,</if>
                <if test="longitude != null">`LONGITUDE`,</if>
                <if test="addressId != null">`ADDRESS_ID`,</if>
                <if test="customerId != null">`CUSTOMER_ID`,</if>
                <if test="numberPlate != null">`NUMBER_PLATE`,</if>
                <if test="receiptName != null">`RECEIPT_NAME`,</if>
                <if test="receiptPhone != null">`RECEIPT_PHONE`,</if>
                <if test="receiptAddress != null">`RECEIPT_ADDRESS`,</if>
            <if test="sex != null">`SEX`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="latitude != null">#{latitude,jdbcType=REAL},</if>
            <if test="longitude != null">#{longitude,jdbcType=REAL},</if>
            <if test="addressId != null">#{addressId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="numberPlate != null">#{numberPlate,jdbcType=VARCHAR},</if>
            <if test="receiptName != null">#{receiptName,jdbcType=VARCHAR},</if>
            <if test="receiptPhone != null">#{receiptPhone,jdbcType=CHAR},</if>
            <if test="receiptAddress != null">#{receiptAddress,jdbcType=VARCHAR},</if>
            <if test="sex != null">#{sex,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--通过address_id列表查询配送地址信息-->
        <select id="findAllByAddressIds" resultMap="BaseResultMap">
            SELECT /*MS-TP-QRORDERING-CUSTOMER-ADDRESS-FINDALLBYADDRESSIDS*/
            <include refid="Base_Column_List"/>
            FROM
            TP_QRORDERING_CUSTOMER_ADDRESS
            WHERE
            address_id in
            <foreach collection="list" item="addressId" separator="," open="(" close=")">
                #{addressId,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
