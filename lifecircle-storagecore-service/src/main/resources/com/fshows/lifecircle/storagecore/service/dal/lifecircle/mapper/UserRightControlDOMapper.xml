<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.UserRightControlDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.UserRightControlDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="COMMODITY_APP_ID" property="commodityAppId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_ALIPAY_PRE_PAY" property="isAlipayPrePay" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="MYBANK_ISV_ORG_ID" property="mybankIsvOrgId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MYBANK_ACCOUNT_NO" property="mybankAccountNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMMODITY_APP_PATH" property="commodityAppPath" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ALIPAY_PREPAY_ORGID" property="alipayPrepayOrgid" jdbcType="VARCHAR"
            javaType="String"/>

            <result column="KEY_ID" property="keyId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PUSH" property="isPush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SIGN" property="isSign" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SEND_ME" property="sendMe" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_QQPAY" property="isQqpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_YIPAY" property="isYipay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VIEW_NUM" property="viewNum" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZERO_FEE" property="zeroFee" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PRE_PAY" property="isPrePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ZFT_PAY" property="isZftPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ENCRYPT" property="isEncrypt" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_H5PAY_WX" property="isH5payWx" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_OPENAPI" property="isOpenapi" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MCHID_MODE" property="mchidMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_SHOW" property="shareShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZF_END_TIME" property="zfEndTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DCEP_SWITCH" property="dcepSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW_LIST" property="isShowList" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_UNIONPAY" property="isUnionpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_WITHDRAW" property="isWithdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MODIFY_TIME" property="modifyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USE_THEN_PAY" property="useThenPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FREE_YOUDIAN" property="freeYoudian" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SCAN_S0_PAY" property="isScanS0Pay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZF_START_TIME" property="zfStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ALIPAY_AUTH" property="isAlipayAuth" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_COMMISSION" property="isCommission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POS_CARD_PAY" property="isPosCardPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POS_PRE_AUTH" property="isPosPreAuth" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMODITY_MODE" property="commodityMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DISABLE_COUPON" property="disableCoupon" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POS_S0_CARD_PAY" property="isPosS0CardPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_VERIFICATION" property="isVerification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SYNC_ALIPAY_CARD" property="syncAlipayCard" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_YZT_SWITCH" property="alipayYztSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ONLY_ATTENTION" property="isOnlyAttention" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_HELP_VIDEO_ID" property="lastHelpVideoId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKETING_SWITCH" property="marketingSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_AUTO_BATCH" property="mybankAutoBatch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_COMMISSION" property="orderCommission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QUICK_CASH_SWITCH" property="quickCashSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLOSE_ZERO_FEE_TIME" property="closeZeroFeeTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CUSTOMIZED_MINA" property="isCustomizedMina" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DOUYIN_WRITEOFF" property="isDouyinWriteoff" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_INDIRECT_PRE_PAY" property="isIndirectPrePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PREPAY_CARD_SWITCH" property="prepayCardSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPECIAL_RATE_POWER" property="specialRatePower" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BATCH_REFUND_SWITCH" property="batchRefundSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CUSTOMER_ADD_FRIEND" property="customerAddFriend" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EASY_PAY_D0_WITHDRAW" property="easyPayD0Withdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_NEW_MEMBER_SYSTEM" property="isNewMemberSystem" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKETING_ACTIVITY" property="marketingActivity" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RATE_FEE_SYNC_SWITCH" property="rateFeeSyncSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_PREPAY_SWITCH" property="alipayPrepaySwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MEITUAN_GROUP_SWITCH" property="meituanGroupSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_VOUCHER_SWITCH" property="alipayVoucherSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_FUNDS_UNFREEZE" property="mybankFundsUnfreeze" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QRORDERING_BROADCAST" property="qrorderingBroadcast" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_MERCHANT_ACCESS" property="superMerchantAccess" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_COMPLAINT_SWITCH" property="alipayComplaintSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDEPENDENT_SETTLEMENT" property="independentSettlement" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SCAN_S0_PAY_LESHUA_FLAG" property="isScanS0PayLeshuaFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_AUTO_REPLENISHMENT" property="mybankAutoReplenishment" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_BALANCE_PERMISSION" property="mybankBalancePermission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QRORDERING_BROADCAST_CYCLE" property="qrorderingBroadcastCycle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WANDA_COUPON_WRITE_OFF_SWITCH" property="wandaCouponWriteOffSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QRORDERING_MISSING_BROADCAST" property="qrorderingMissingBroadcast" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`COMMODITY_APP_ID`,`IS_ALIPAY_PRE_PAY`,`MYBANK_ISV_ORG_ID`,`MYBANK_ACCOUNT_NO`,`COMMODITY_APP_PATH`,`ALIPAY_PREPAY_ORGID`,`KEY_ID`,`IS_PUSH`,`IS_SIGN`,`SEND_ME`,`IS_QQPAY`,`IS_YIPAY`,`VIEW_NUM`,`ZERO_FEE`,`IS_PRE_PAY`,`IS_ZFT_PAY`,`IS_ENCRYPT`,`IS_H5PAY_WX`,`IS_OPENAPI`,`MCHID_MODE`,`SHARE_SHOW`,`ZF_END_TIME`,`CREATE_TIME`,`DCEP_SWITCH`,`IS_SHOW_LIST`,`IS_UNIONPAY`,`IS_WITHDRAW`,`MODIFY_TIME`,`USE_THEN_PAY`,`FREE_YOUDIAN`,`IS_SCAN_S0_PAY`,`ZF_START_TIME`,`IS_ALIPAY_AUTH`,`IS_COMMISSION`,`IS_POS_CARD_PAY`,`IS_POS_PRE_AUTH`,`COMMODITY_MODE`,`DISABLE_COUPON`,`IS_POS_S0_CARD_PAY`,`IS_VERIFICATION`,`SYNC_ALIPAY_CARD`,`ALIPAY_YZT_SWITCH`,`IS_ONLY_ATTENTION`,`LAST_HELP_VIDEO_ID`,`MARKETING_SWITCH`,`MYBANK_AUTO_BATCH`,`ORDER_COMMISSION`,`QUICK_CASH_SWITCH`,`CLOSE_ZERO_FEE_TIME`,`IS_CUSTOMIZED_MINA`,`IS_DOUYIN_WRITEOFF`,`IS_INDIRECT_PRE_PAY`,`PREPAY_CARD_SWITCH`,`SPECIAL_RATE_POWER`,`BATCH_REFUND_SWITCH`,`CUSTOMER_ADD_FRIEND`,`EASY_PAY_D0_WITHDRAW`,`IS_NEW_MEMBER_SYSTEM`,`MARKETING_ACTIVITY`,`RATE_FEE_SYNC_SWITCH`,`ALIPAY_PREPAY_SWITCH`,`MEITUAN_GROUP_SWITCH`,`ALIPAY_VOUCHER_SWITCH`,`MYBANK_FUNDS_UNFREEZE`,`QRORDERING_BROADCAST`,`SUPER_MERCHANT_ACCESS`,`ALIPAY_COMPLAINT_SWITCH`,`INDEPENDENT_SETTLEMENT`,`IS_SCAN_S0_PAY_LESHUA_FLAG`,`MYBANK_AUTO_REPLENISHMENT`,`MYBANK_BALANCE_PERMISSION`,`QRORDERING_BROADCAST_CYCLE`,`WANDA_COUPON_WRITE_OFF_SWITCH`,`QRORDERING_MISSING_BROADCAST`
    </sql>


            <!--insert:TP_USER_RIGHT_CONTROL-->
            <insert id="insert" >
                    INSERT INTO TP_USER_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isAlipayPrePay != null">`IS_ALIPAY_PRE_PAY`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="isSign != null">`IS_SIGN`,</if>
            <if test="sendMe != null">`SEND_ME`,</if>
            <if test="isQqpay != null">`IS_QQPAY`,</if>
            <if test="isYipay != null">`IS_YIPAY`,</if>
            <if test="viewNum != null">`VIEW_NUM`,</if>
            <if test="zeroFee != null">`ZERO_FEE`,</if>
            <if test="isPrePay != null">`IS_PRE_PAY`,</if>
            <if test="isEncrypt != null">`IS_ENCRYPT`,</if>
            <if test="isH5payWx != null">`IS_H5PAY_WX`,</if>
            <if test="isOpenapi != null">`IS_OPENAPI`,</if>
            <if test="mchidMode != null">`MCHID_MODE`,</if>
            <if test="zfEndTime != null">`ZF_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isUnionpay != null">`IS_UNIONPAY`,</if>
            <if test="isWithdraw != null">`IS_WITHDRAW`,</if>
            <if test="modifyTime != null">`MODIFY_TIME`,</if>
            <if test="freeYoudian != null">`FREE_YOUDIAN`,</if>
            <if test="zfStartTime != null">`ZF_START_TIME`,</if>
            <if test="isAlipayAuth != null">`IS_ALIPAY_AUTH`,</if>
            <if test="isVerification != null">`IS_VERIFICATION`,</if>
            <if test="syncAlipayCard != null">`SYNC_ALIPAY_CARD`,</if>
            <if test="isOnlyAttention != null">`IS_ONLY_ATTENTION`,</if>
            <if test="lastHelpVideoId != null">`LAST_HELP_VIDEO_ID`,</if>
            <if test="marketingSwitch != null">`MARKETING_SWITCH`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="closeZeroFeeTime != null">`CLOSE_ZERO_FEE_TIME`,</if>
            <if test="isCustomizedMina != null">`IS_CUSTOMIZED_MINA`,</if>
            <if test="specialRatePower != null">`SPECIAL_RATE_POWER`,</if>
            <if test="customerAddFriend != null">`CUSTOMER_ADD_FRIEND`,</if>
            <if test="isNewMemberSystem != null">`IS_NEW_MEMBER_SYSTEM`,</if>
            <if test="marketingActivity != null">`MARKETING_ACTIVITY`,</if>
            <if test="qrorderingBroadcast != null">`QRORDERING_BROADCAST`,</if>
            <if test="superMerchantAccess != null">`SUPER_MERCHANT_ACCESS`,</if>
            <if test="qrorderingBroadcastCycle != null">`QRORDERING_BROADCAST_CYCLE`,</if>
            <if test="qrorderingMissingBroadcast != null">`QRORDERING_MISSING_BROADCAST`,</if>
            <if test="isDouyinWriteoff != null">`IS_DOUYIN_WRITEOFF`,</if>
            <if test="meituanGroupSwitch != null">`MEITUAN_GROUP_SWITCH`,</if>
            <if test="alipayVoucherSwitch != null">`alipay_voucher_switch`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isAlipayPrePay != null">#{isAlipayPrePay,jdbcType=VARCHAR},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="isSign != null">#{isSign,jdbcType=TINYINT},</if>
            <if test="sendMe != null">#{sendMe,jdbcType=TINYINT},</if>
            <if test="isQqpay != null">#{isQqpay,jdbcType=TINYINT},</if>
            <if test="isYipay != null">#{isYipay,jdbcType=TINYINT},</if>
            <if test="viewNum != null">#{viewNum,jdbcType=TINYINT},</if>
            <if test="zeroFee != null">#{zeroFee,jdbcType=TINYINT},</if>
            <if test="isPrePay != null">#{isPrePay,jdbcType=TINYINT},</if>
            <if test="isEncrypt != null">#{isEncrypt,jdbcType=TINYINT},</if>
            <if test="isH5payWx != null">#{isH5payWx,jdbcType=TINYINT},</if>
            <if test="isOpenapi != null">#{isOpenapi,jdbcType=TINYINT},</if>
            <if test="mchidMode != null">#{mchidMode,jdbcType=TINYINT},</if>
            <if test="zfEndTime != null">#{zfEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isUnionpay != null">#{isUnionpay,jdbcType=TINYINT},</if>
            <if test="isWithdraw != null">#{isWithdraw,jdbcType=TINYINT},</if>
            <if test="modifyTime != null">#{modifyTime,jdbcType=INTEGER},</if>
            <if test="freeYoudian != null">#{freeYoudian,jdbcType=TINYINT},</if>
            <if test="zfStartTime != null">#{zfStartTime,jdbcType=INTEGER},</if>
            <if test="isAlipayAuth != null">#{isAlipayAuth,jdbcType=TINYINT},</if>
            <if test="isVerification != null">#{isVerification,jdbcType=TINYINT},</if>
            <if test="syncAlipayCard != null">#{syncAlipayCard,jdbcType=TINYINT},</if>
            <if test="isOnlyAttention != null">#{isOnlyAttention,jdbcType=TINYINT},</if>
            <if test="lastHelpVideoId != null">#{lastHelpVideoId,jdbcType=INTEGER},</if>
            <if test="marketingSwitch != null">#{marketingSwitch,jdbcType=TINYINT},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="closeZeroFeeTime != null">#{closeZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="isCustomizedMina != null">#{isCustomizedMina,jdbcType=TINYINT},</if>
            <if test="specialRatePower != null">#{specialRatePower,jdbcType=TINYINT},</if>
            <if test="customerAddFriend != null">#{customerAddFriend,jdbcType=TINYINT},</if>
            <if test="isNewMemberSystem != null">#{isNewMemberSystem,jdbcType=TINYINT},</if>
            <if test="marketingActivity != null">#{marketingActivity,jdbcType=TINYINT},</if>
            <if test="qrorderingBroadcast != null">#{qrorderingBroadcast,jdbcType=TINYINT},</if>
            <if test="superMerchantAccess != null">#{superMerchantAccess,jdbcType=TINYINT},</if>
            <if test="qrorderingBroadcastCycle != null">#{qrorderingBroadcastCycle,jdbcType=TINYINT},</if>
            <if test="qrorderingMissingBroadcast != null">#{qrorderingMissingBroadcast,jdbcType=TINYINT},</if>
            <if test="isDouyinWriteoff != null">#{isDouyinWriteoff,jdbcType=TINYINT},</if>
            <if test="meituanGroupSwitch != null">#{meituanGroupSwitch,jdbcType=TINYINT},</if>
            <if test="alipayVoucherSwitch != null">#{alipayVoucherSwitch,jdbcType=TINYINT},</if>
        </trim>
            </insert>

            <!--根据用户IDS[keyId]查找用户权限信息列表-->
            <select id="getUserRightControlByKeyIds" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-RIGHT-CONTROL-GETUSERRIGHTCONTROLBYKEYIDS*/  <include refid="Base_Column_List" /> FROM TP_USER_RIGHT_CONTROL
        WHERE key_id IN
        <foreach collection="list" item="uid" index="index" separator="," open="(" close=")">
            #{uid, jdbcType=INTEGER}
        </foreach>
            </select>
    </mapper>
