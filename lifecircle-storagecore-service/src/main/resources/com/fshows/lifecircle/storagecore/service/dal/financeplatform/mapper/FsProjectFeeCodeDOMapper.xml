<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.FsProjectFeeCodeDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.FsProjectFeeCodeDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="STOP_TIME" property="stopTime" jdbcType="BIGINT"
                javaType="Long"/>

        <result column="REMARK" property="remark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FEE_TYPE" property="feeType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_CODE" property="storeCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PROJECT_FEE_ID" property="projectFeeId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ERP_SUBJECT_CODE" property="erpSubjectCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FEE_PROJECT_NAME" property="feeProjectName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STATUS" property="status" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_DETAILS" property="isDetails" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ERP_SYNC_STATUS" property="erpSyncStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="COMMISSION_RULES_FLAG" property="commissionRulesFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="FEE_PROJECT_ATTRIBUTE" property="feeProjectAttribute" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAYMENT_CHECK_ACCOUNT" property="paymentCheckAccount" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PROJECT_CATEGORY_CODE" property="projectCategoryCode" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PROJECT_BIG_CATEGORY_CODE" property="projectBigCategoryCode" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="FEE_RATE" property="feeRate" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`STOP_TIME`,`REMARK`,`FEE_CODE`,`FEE_TYPE`,`STORE_CODE`,`PROJECT_FEE_ID`,`ERP_SUBJECT_CODE`,`FEE_PROJECT_NAME`,`STATUS`,`IS_DETAILS`,`ERP_SYNC_STATUS`,`COMMISSION_RULES_FLAG`,`FEE_PROJECT_ATTRIBUTE`,`PAYMENT_CHECK_ACCOUNT`,`PROJECT_CATEGORY_CODE`,`PROJECT_BIG_CATEGORY_CODE`,`CREATE_TIME`,`UPDATE_TIME`,`FEE_RATE`
    </sql>


    <!--insert:FS_PROJECT_FEE_CODE-->
    <insert id="insert">
        INSERT INTO FS_PROJECT_FEE_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="stopTime != null">`STOP_TIME`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="feeType != null">`FEE_TYPE`,</if>
            <if test="storeCode != null">`STORE_CODE`,</if>
            <if test="projectFeeId != null">`PROJECT_FEE_ID`,</if>
            <if test="erpSubjectCode != null">`ERP_SUBJECT_CODE`,</if>
            <if test="feeProjectName != null">`FEE_PROJECT_NAME`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="isDetails != null">`IS_DETAILS`,</if>
            <if test="erpSyncStatus != null">`ERP_SYNC_STATUS`,</if>
            <if test="commissionRulesFlag != null">`COMMISSION_RULES_FLAG`,</if>
            <if test="feeProjectAttribute != null">`FEE_PROJECT_ATTRIBUTE`,</if>
            <if test="paymentCheckAccount != null">`PAYMENT_CHECK_ACCOUNT`,</if>
            <if test="projectCategoryCode != null">`PROJECT_CATEGORY_CODE`,</if>
            <if test="projectBigCategoryCode != null">`PROJECT_BIG_CATEGORY_CODE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeRate != null">`FEE_RATE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="stopTime != null">#{stopTime,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="feeType != null">#{feeType,jdbcType=VARCHAR},</if>
            <if test="storeCode != null">#{storeCode,jdbcType=VARCHAR},</if>
            <if test="projectFeeId != null">#{projectFeeId,jdbcType=VARCHAR},</if>
            <if test="erpSubjectCode != null">#{erpSubjectCode,jdbcType=VARCHAR},</if>
            <if test="feeProjectName != null">#{feeProjectName,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="isDetails != null">#{isDetails,jdbcType=TINYINT},</if>
            <if test="erpSyncStatus != null">#{erpSyncStatus,jdbcType=TINYINT},</if>
            <if test="commissionRulesFlag != null">#{commissionRulesFlag,jdbcType=TINYINT},</if>
            <if test="feeProjectAttribute != null">#{feeProjectAttribute,jdbcType=TINYINT},</if>
            <if test="paymentCheckAccount != null">#{paymentCheckAccount,jdbcType=TINYINT},</if>
            <if test="projectCategoryCode != null">#{projectCategoryCode,jdbcType=TINYINT},</if>
            <if test="projectBigCategoryCode != null">#{projectBigCategoryCode,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeRate != null">#{feeRate,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--根据费用编码查询费用信息-->
    <select id="getFeeInfoByFeeCode" resultMap="BaseResultMap">
        select /*MS-FS-PROJECT-FEE-CODE-GETFEEINFOBYFEECODE*/
        <include refid="Base_Column_List"/>
        from fs_project_fee_code
        where fee_code = #{feeCode,jdbcType=VARCHAR}
        and status = 1
        limit 1
    </select>
</mapper>
