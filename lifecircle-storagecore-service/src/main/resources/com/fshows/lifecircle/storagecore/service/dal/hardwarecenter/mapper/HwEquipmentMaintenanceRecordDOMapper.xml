<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentMaintenanceRecordDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentMaintenanceRecordDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RIGHT_ID" property="rightId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OLD_INIT_SN" property="oldInitSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DONE" property="done" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`INIT_SN`,`RIGHT_ID`,`OLD_INIT_SN`,`DONE`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:HW_EQUIPMENT_MAINTENANCE_RECORD-->
    <insert id="insert">
        INSERT INTO HW_EQUIPMENT_MAINTENANCE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="rightId != null">`RIGHT_ID`,</if>
            <if test="oldInitSn != null">`OLD_INIT_SN`,</if>
            <if test="done != null">`DONE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="rightId != null">#{rightId,jdbcType=VARCHAR},</if>
            <if test="oldInitSn != null">#{oldInitSn,jdbcType=VARCHAR},</if>
            <if test="done != null">#{done,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--findDeviceMaintenanceList-->
    <select id="findDeviceMaintenanceList" resultMap="BaseResultMap">
        select /*MS-HW-EQUIPMENT-MAINTENANCE-RECORD-FINDDEVICEMAINTENANCELIST*/ a.* from hw_equipment_maintenance_record
        a
        inner join hw_equipment_sn b on a.init_sn=b.init_sn
        where a.is_del=0
        <if test="initSn != null and initSn!=''">
            and a.init_sn=#{initSn,jdbcType=VARCHAR}
        </if>
        <if test="oldInitSn !=null and oldInitSn!=''">
            and a.old_init_sn=#{oldInitSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId !=null">
            and b.equipment_id=#{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="startTime !=null and endTime !=null">
            and a.create_time between #{startTime,jdbcType=TIMESTAMP}
            and #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by a.id desc limit 10000
    </select>
</mapper>
