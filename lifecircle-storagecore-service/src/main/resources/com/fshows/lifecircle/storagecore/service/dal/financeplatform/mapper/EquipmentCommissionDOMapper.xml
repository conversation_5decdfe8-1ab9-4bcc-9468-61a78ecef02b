<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.EquipmentCommissionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.EquipmentCommissionDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OEM_NAME" property="oemName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_UUID" property="businessUuid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_EXT_NUM" property="businessExtNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_USERNAME" property="marketUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BELONG_AGENT_USERNAME" property="belongAgentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BELONG_MARKET_USERNAME" property="belongMarketUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPER_SALESMAN_USERNAME" property="superSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BELONG_SALESMAN_USERNAME" property="belongSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BELONG_SUPER_SALESMAN_USERNAME" property="belongSuperSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OEM_ID" property="oemId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OWN_RUN" property="ownRun" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DATA_TYPE" property="dataType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG_AGENT_ID" property="belongAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG_MARKET_ID" property="belongMarketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG_SALESMAN_ID" property="belongSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="VALID_TRADE_NUMBER" property="validTradeNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EFFECTIVE_USER_COUNT" property="effectiveUserCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG_SUPER_SALESMAN_ID" property="belongSuperSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REBATE_AMOUNT" property="rebateAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="VALID_TRADE_AMOUNT" property="validTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="EquipmentCommissionMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.resultmap.EquipmentCommissionMap">

                <result column="fee_code" property="feeCode" javaType="String"/>

                <result column="equipment_sn" property="equipmentSn" javaType="String"/>

                <result column="agent_username" property="agentUsername" javaType="String"/>

                <result column="fee_project_name" property="feeProjectName" javaType="String"/>

                <result column="market_username" property="marketUsername" javaType="String"/>

                <result column="merchant_username" property="merchantUsername" javaType="String"/>

                <result column="salesman_username" property="salesmanUsername" javaType="String"/>

                <result column="belong_agent_username" property="belongAgentUsername" javaType="String"/>

                <result column="belong_market_username" property="belongMarketUsername" javaType="String"/>

                <result column="super_salesman_username" property="superSalesmanUsername" javaType="String"/>

                <result column="belong_salesman_username" property="belongSalesmanUsername" javaType="String"/>

                <result column="belong_super_salesman_username" property="belongSuperSalesmanUsername" javaType="String"/>

                <result column="uid" property="uid" javaType="Integer"/>

                <result column="agent_id" property="agentId" javaType="Integer"/>

                <result column="market_id" property="marketId" javaType="Integer"/>

                <result column="salesman_id" property="salesmanId" javaType="Integer"/>

                <result column="business_date" property="businessDate" javaType="Integer"/>

                <result column="commissionId" property="commissionid" javaType="Integer"/>

                <result column="belong_agent_id" property="belongAgentId" javaType="Integer"/>

                <result column="belong_market_id" property="belongMarketId" javaType="Integer"/>

                <result column="super_salesman_id" property="superSalesmanId" javaType="Integer"/>

                <result column="belong_salesman_id" property="belongSalesmanId" javaType="Integer"/>

                <result column="valid_trade_number" property="validTradeNumber" javaType="Integer"/>

                <result column="effective_user_count" property="effectiveUserCount" javaType="Integer"/>

                <result column="belong_super_salesman_id" property="belongSuperSalesmanId" javaType="Integer"/>

                <result column="rebate_amount" property="rebateAmount" javaType="java.math.BigDecimal"/>

                <result column="valid_trade_amount" property="validTradeAmount" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`OEM_NAME`,`PT_MONTH`,`EQUIPMENT_SN`,`BUSINESS_UUID`,`AGENT_USERNAME`,`BUSINESS_EXT_NUM`,`MARKET_USERNAME`,`MERCHANT_USERNAME`,`SALESMAN_USERNAME`,`BELONG_AGENT_USERNAME`,`BELONG_MARKET_USERNAME`,`SUPER_SALESMAN_USERNAME`,`BELONG_SALESMAN_USERNAME`,`BELONG_SUPER_SALESMAN_USERNAME`,`UID`,`OEM_ID`,`OWN_RUN`,`AGENT_ID`,`DATA_TYPE`,`MARKET_ID`,`SALESMAN_ID`,`BUSINESS_DATE`,`BELONG_AGENT_ID`,`BELONG_MARKET_ID`,`SUPER_SALESMAN_ID`,`BELONG_SALESMAN_ID`,`VALID_TRADE_NUMBER`,`EFFECTIVE_USER_COUNT`,`BELONG_SUPER_SALESMAN_ID`,`CREATE_TIME`,`UPDATE_TIME`,`REBATE_AMOUNT`,`VALID_TRADE_AMOUNT`
    </sql>


            <!--条件查询-->
            <select id="listByCondition" resultMap="EquipmentCommissionMap">
                    SELECT
        a.id as commissionId,
        a.equipment_sn,
        a.agent_id,
        a.agent_username,
        a.business_date,
        a.super_salesman_id,
        a.super_salesman_username,
        a.salesman_id,
        a.salesman_username,
        a.uid,
        a.merchant_username,
        a.effective_user_count,
        a.fee_code,
        b.fee_project_name,
        a.rebate_amount,
        a.valid_trade_number,
        a.valid_trade_amount,
        a.market_id,
        a.market_username,
        a.belong_agent_id,
        a.belong_agent_username,
        a.belong_super_salesman_id,
        a.belong_super_salesman_username,
        a.belong_salesman_id,
        a.belong_salesman_username,
        a.belong_market_id,
        a.belong_market_username
        FROM
        fp_equipment_commission a
        INNER JOIN fs_project_fee_code b ON a.fee_code = b.fee_code
        <where>
            <if test="agentId != null">
                AND a.agent_id = #{agentId,jdbcType=BIGINT}
            </if>
            <if test="businessExtNum != null">
                AND a.business_ext_num = #{businessExtNum,jdbcType=VARCHAR}
            </if>
            <if test="businessData != null">
                AND a.business_date = #{businessData,jdbcType=INTEGER}
            </if>
            <if test="feeCode != null">
                AND a.fee_code = #{feeCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY a.business_date desc
            </select>
    </mapper>
