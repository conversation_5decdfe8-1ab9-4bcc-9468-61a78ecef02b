<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.SupplementWithdrawDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.SupplementWithdrawDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INFO" property="info" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADMIN_ID" property="adminId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_BANK" property="cardBank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FRONT_LOG_NO" property="frontLogNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACCOUNT_ID" property="bankAccountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NEW_SERIAL_NUMBER" property="newSerialNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASH_TYPE" property="cashType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRAN_TIME" property="tranTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_BANK_ID" property="bindBankId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASH_STATUS" property="cashStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INFO`,`TOKEN`,`CARD_NO`,`ADMIN_ID`,`CARD_BANK`,`FRONT_LOG_NO`,`ACCOUNT_NAME`,`SERIAL_NUMBER`,`BANK_ACCOUNT_ID`,`NEW_SERIAL_NUMBER`,`UID`,`BANK_CODE`,`BANK_TYPE`,`CASH_TYPE`,`TRAN_TIME`,`BIND_BANK_ID`,`CASH_STATUS`,`FINISH_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_SUPPLEMENT_WITHDRAW-->
            <insert id="insert" >
            INSERT INTO TP_SUPPLEMENT_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="info != null">`INFO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardBank != null">`CARD_BANK`,</if>
        <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
        <if test="bankAccountId != null">`BANK_ACCOUNT_ID`,</if>
        <if test="newSerialNumber != null">`NEW_SERIAL_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="adminId != null">`ADMIN_ID`,</if>
        <if test="bankCode != null">`BANK_CODE`,</if>
        <if test="cashType != null">`CASH_TYPE`,</if>
        <if test="tranTime != null">`TRAN_TIME`,</if>
        <if test="cashStatus != null">`CASH_STATUS`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="info != null">#{info,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
        <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
        <if test="bankAccountId != null">#{bankAccountId,jdbcType=VARCHAR},</if>
        <if test="newSerialNumber != null">#{newSerialNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="adminId != null">#{adminId,jdbcType=INTEGER},</if>
        <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
        <if test="cashType != null">#{cashType,jdbcType=INTEGER},</if>
        <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
        <if test="cashStatus != null">#{cashStatus,jdbcType=TINYINT},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--批量查询状态不为成功的提现数据-->
            <select id="getDiffSupplementWithdrawList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-SUPPLEMENT-WITHDRAW-GETDIFFSUPPLEMENTWITHDRAWLIST*/  <include refid="Base_Column_List" /> FROM
        TP_SUPPLEMENT_WITHDRAW
        WHERE NEW_SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
        AND cash_status in (1,2,3,4,5,6)
            </select>

            <!--更新补提现状态和时间(单条)-->
            <update id="updateDiffWithdraw" >
                        update /*MS-TP-SUPPLEMENT-WITHDRAW-UPDATEDIFFWITHDRAW*/ TP_SUPPLEMENT_WITHDRAW
            set
            finish_time = #{finishTime,jdbcType=INTEGER},
            tran_time = #{tranTime,jdbcType=INTEGER},
            cash_status  = 0
            where NEW_SERIAL_NUMBER=#{newSerialNumber,jdbcType=VARCHAR}
            </update>

            <!--更新补提现状态和时间(单条)-->
            <update id="updateDiffWithdrawBySxPay" >
                    update /*MS-TP-SUPPLEMENT-WITHDRAW-UPDATEDIFFWITHDRAWBYSXPAY*/ TP_SUPPLEMENT_WITHDRAW
        set
        tran_time = #{tranTime,jdbcType=INTEGER},
        cash_status = 0
        where NEW_SERIAL_NUMBER=#{newSerialNumber,jdbcType=VARCHAR}
            </update>
    </mapper>
