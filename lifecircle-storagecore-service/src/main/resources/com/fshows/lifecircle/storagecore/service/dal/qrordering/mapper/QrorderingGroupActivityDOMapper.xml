<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingGroupActivityDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingGroupActivityDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="GROUP_ACTIVITY_ID" property="groupActivityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GROUP_ACTIVITY_NAME" property="groupActivityName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GROUP_ACTIVITY_CONTENT" property="groupActivityContent" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="GROUP_ACTIVITY_PICTURE" property="groupActivityPicture" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_AFTER_SHOW" property="payAfterShow" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CAN_OUT_DELIVERY" property="canOutDelivery" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ACTIVITY_PUSH_FLAG" property="activityPushFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="DEFAULT_OUT_STORE_ID" property="defaultOutStoreId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="GROUP_ACTIVITY_CLOSED" property="groupActivityClosed" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="GROUP_ACTIVITY_END_TIME" property="groupActivityEndTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="GROUP_ACTIVITY_START_TIME" property="groupActivityStartTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GROUP_ACTIVITY_ID`,`GROUP_ACTIVITY_NAME`,`GROUP_ACTIVITY_CONTENT`,`GROUP_ACTIVITY_PICTURE`,`UID`,`DEL_FLAG`,`PAY_AFTER_SHOW`,`CAN_OUT_DELIVERY`,`ACTIVITY_PUSH_FLAG`,`DEFAULT_OUT_STORE_ID`,`GROUP_ACTIVITY_CLOSED`,`CREATE_TIME`,`UPDATE_TIME`,`GROUP_ACTIVITY_END_TIME`,`GROUP_ACTIVITY_START_TIME`
    </sql>


    <!--insert:TP_QRORDERING_GROUP_ACTIVITY-->
    <insert id="insert">
        INSERT INTO TP_QRORDERING_GROUP_ACTIVITY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupActivityId != null">`GROUP_ACTIVITY_ID`,</if>
            <if test="groupActivityName != null">`GROUP_ACTIVITY_NAME`,</if>
            <if test="groupActivityContent != null">`GROUP_ACTIVITY_CONTENT`,</if>
            <if test="groupActivityPicture != null">`GROUP_ACTIVITY_PICTURE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payAfterShow != null">`PAY_AFTER_SHOW`,</if>
            <if test="canOutDelivery != null">`CAN_OUT_DELIVERY`,</if>
            <if test="activityPushFlag != null">`ACTIVITY_PUSH_FLAG`,</if>
            <if test="defaultOutStoreId != null">`DEFAULT_OUT_STORE_ID`,</if>
            <if test="groupActivityClosed != null">`GROUP_ACTIVITY_CLOSED`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="groupActivityEndTime != null">`GROUP_ACTIVITY_END_TIME`,</if>
            <if test="groupActivityStartTime != null">`GROUP_ACTIVITY_START_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupActivityId != null">#{groupActivityId,jdbcType=VARCHAR},</if>
            <if test="groupActivityName != null">#{groupActivityName,jdbcType=VARCHAR},</if>
            <if test="groupActivityContent != null">#{groupActivityContent,jdbcType=LONGVARCHAR},</if>
            <if test="groupActivityPicture != null">#{groupActivityPicture,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payAfterShow != null">#{payAfterShow,jdbcType=TINYINT},</if>
            <if test="canOutDelivery != null">#{canOutDelivery,jdbcType=TINYINT},</if>
            <if test="activityPushFlag != null">#{activityPushFlag,jdbcType=TINYINT},</if>
            <if test="defaultOutStoreId != null">#{defaultOutStoreId,jdbcType=INTEGER},</if>
            <if test="groupActivityClosed != null">#{groupActivityClosed,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="groupActivityEndTime != null">#{groupActivityEndTime,jdbcType=TIMESTAMP},</if>
            <if test="groupActivityStartTime != null">#{groupActivityStartTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--查询活动-->
    <select id="listActivityByActivityId" resultMap="BaseResultMap">
        SELECT /*MS-TP-QRORDERING-GROUP-ACTIVITY-LISTACTIVITYBYACTIVITYID*/
        <include refid="Base_Column_List"/>
        FROM tp_qrordering_group_activity
        WHERE group_activity_id IN
        <foreach collection="list" item="groupActivityId" open="(" close=")" separator=",">
            #{groupActivityId,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </select>
</mapper>
