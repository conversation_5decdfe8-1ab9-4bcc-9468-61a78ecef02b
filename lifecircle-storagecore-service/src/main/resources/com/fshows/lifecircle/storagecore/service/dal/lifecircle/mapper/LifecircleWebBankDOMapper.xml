<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleWebBankDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleWebBankDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APP_LOGO" property="appLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BG_COLOR" property="bgColor" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIG_LOGO" property="bigLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FULL_NAME" property="fullName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LITTLE_LOGO" property="littleLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_PACKAGE_IMG" property="bankPackageImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_PACKAGE_LOGO" property="bankPackageLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ABBREVIATION" property="bankAbbreviation" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BG_ID" property="bgId" jdbcType="INTEGER"
        javaType="Integer"/>
    </resultMap>

        <resultMap id="pWebBank" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.PWebBankDO">

                <result column="bank_no" property="bankNo" javaType="String"/>

                <result column="bank_code" property="bankCode" javaType="String"/>

                <result column="short_name" property="shortName" javaType="String"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`BANK_NO`,`APP_LOGO`,`BG_COLOR`,`BIG_LOGO`,`FULL_NAME`,`SHORT_NAME`,`LITTLE_LOGO`,`BANK_PACKAGE_IMG`,`BANK_PACKAGE_LOGO`,`BANK_ABBREVIATION`,`BG_ID`
    </sql>


            <!--insert:TP_LIFECIRCLE_WEB_BANK-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_WEB_BANK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bgId != null">`BG_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="appLogo != null">`APP_LOGO`,</if>
            <if test="bgColor != null">`BG_COLOR`,</if>
            <if test="bigLogo != null">`BIG_LOGO`,</if>
            <if test="fullName != null">`FULL_NAME`,</if>
            <if test="shortName != null">`SHORT_NAME`,</if>
            <if test="littleLogo != null">`LITTLE_LOGO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bgId != null">#{bgId,jdbcType=INTEGER},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="appLogo != null">#{appLogo,jdbcType=VARCHAR},</if>
            <if test="bgColor != null">#{bgColor,jdbcType=VARCHAR},</if>
            <if test="bigLogo != null">#{bigLogo,jdbcType=VARCHAR},</if>
            <if test="fullName != null">#{fullName,jdbcType=VARCHAR},</if>
            <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
            <if test="littleLogo != null">#{littleLogo,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据shortName得到银行卡明细-->
            <select id="getBankInfoByShortName" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        tp_lifecircle_web_bank
        where
        full_name like concat('%', #{shortName,jdbcType=VARCHAR} , '%')
        limit 1
            </select>

            <!--根据银行代码查询-->
            <select id="getByBankNo" resultMap="BaseResultMap">
                    SELECT
        id,
        bg_id,
        bank_no,
        app_logo,
        bg_color,
        big_logo,
        full_name,
        short_name,
        little_logo,
        bank_package_logo,
        bank_package_img
        FROM tp_lifecircle_web_bank
        WHERE bank_no = #{bankNo,jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据shortName得到银行卡明细-->
            <select id="findBankInfoByShortName" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        tp_lifecircle_web_bank
        where
        short_name = #{shortName,jdbcType=VARCHAR}
        limit 1
            </select>

            <!--getAllBank-->
            <select id="getAllBank" resultMap="pWebBank">
                    SELECT /*MS-TP-LIFECIRCLE-WEB-BANK-GETALLBANK*/  wb.bank_no,wb.short_name,pb.bank_code
        FROM tp_lifecircle_web_bank wb
        LEFT JOIN tp_pa_bank_code pb
        on wb.short_name=pb.bank_name
        <trim prefix="where" prefixOverrides="AND|OR">
            <if test="shortName !=null and shortName !=''">
                wb.short_name like CONCAT('%',#{shortName,jdbcType=VARCHAR},'%')
            </if>
        </trim>
            </select>

            <!--update:updateBankImgByFullName-->
            <update id="updateBankImgByFullName" >
                    update /*MS-TP-LIFECIRCLE-WEB-BANK-UPDATEBANKIMGBYFULLNAME*/ tp_lifecircle_web_bank set bank_package_logo=#{bankPackageLogo,jdbcType=VARCHAR},
        bank_package_img=#{bankPackageImg,jdbcType=VARCHAR}
        where full_name = #{fullName,jdbcType=VARCHAR}
            </update>
    </mapper>
