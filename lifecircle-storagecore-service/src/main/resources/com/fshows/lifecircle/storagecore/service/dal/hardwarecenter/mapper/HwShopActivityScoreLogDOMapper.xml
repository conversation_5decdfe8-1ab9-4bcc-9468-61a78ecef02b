<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopActivityScoreLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopActivityScoreLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_LOG_ID" property="operateLogId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELATION_ORDER_NO" property="relationOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SCORE" property="score" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANGE_SCORE" property="changeScore" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OPERATOR`,`ACCOUNT_NAME`,`OPERATE_LOG_ID`,`OPERATOR_NAME`,`RELATION_ORDER_NO`,`IS_DEL`,`SCORE`,`ACCOUNT_ID`,`ACTIVITY_ID`,`CHANGE_SCORE`,`OPERATE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_SHOP_ACTIVITY_SCORE_LOG-->
            <insert id="insert" >
            INSERT INTO HW_SHOP_ACTIVITY_SCORE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="operateLogId != null">`OPERATE_LOG_ID`,</if>
        <if test="operatorName != null">`OPERATOR_NAME`,</if>
        <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="score != null">`SCORE`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="changeScore != null">`CHANGE_SCORE`,</if>
        <if test="operateType != null">`OPERATE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="operateLogId != null">#{operateLogId,jdbcType=VARCHAR},</if>
        <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
        <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="score != null">#{score,jdbcType=INTEGER},</if>
        <if test="accountId != null">#{accountId,jdbcType=INTEGER},</if>
        <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
        <if test="changeScore != null">#{changeScore,jdbcType=INTEGER},</if>
        <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--查询活动积分流水分页列表 pageCount-->
            <select id="findActivityScoreLogPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 `hw_shop_activity_score_log`
        where is_del = 0
        <if test="activityId != null and activityId &gt; 0">
            AND activity_id = #{activityId, jdbcType=INTEGER}
        </if>
        <if test="accountName != null and accountName != '' ">
            AND account_name LIKE CONCAT (#{accountName,jdbcType=VARCHAR},'%')
        </if>
        <if test="operateType != null and operateType != 0 ">
            AND operate_type = #{operateType,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            AND `create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND `create_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        
            </select>
            <!--查询活动积分流水分页列表 pageResult-->
            <select id="findActivityScoreLogPageResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.model.hwshop.HwShopScoreLogExportModel">
                    select
        `CREATE_TIME` as operateTime,
        `OPERATE_TYPE` as operateType,
        `ACTIVITY_ID` as activityId,
        `ACCOUNT_ID` as accountId,
        `ACCOUNT_NAME` as accountName,
        `RELATION_ORDER_NO` as orderSn,
        `SCORE` as afterChangeScore,
        `CHANGE_SCORE` as changeScore
        from `hw_shop_activity_score_log`
        where is_del = 0
        <if test="activityId != null and activityId &gt; 0">
            AND activity_id = #{activityId, jdbcType=INTEGER}
        </if>
        <if test="accountName != null and accountName != '' ">
            AND account_name LIKE CONCAT (#{accountName,jdbcType=VARCHAR},'%')
        </if>
        <if test="operateType != null and operateType != 0 ">
            AND operate_type = #{operateType,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            AND `create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND `create_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY `create_time` DESC, id desc
            limit #{startRow},#{limit}
            </select>
    </mapper>
