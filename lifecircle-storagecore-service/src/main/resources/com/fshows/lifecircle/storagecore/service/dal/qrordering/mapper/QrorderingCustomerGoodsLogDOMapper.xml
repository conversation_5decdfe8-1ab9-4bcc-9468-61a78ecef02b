<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingCustomerGoodsLogDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingCustomerGoodsLogDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OUT_STORE_ID" property="outStoreId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ACTIVITY_GOODS_ID" property="activityGoodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SALE_NUMBER" property="saleNumber" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ACTIVITY_FLAG" property="activityFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_ID`,`STORE_ID`,`GOODS_NAME`,`ACTIVITY_ID`,`CUSTOMER_ID`,`OUT_STORE_ID`,`PAY_ORDER_NO`,`ACTIVITY_GOODS_ID`,`DEL_FLAG`,`SALE_NUMBER`,`ACTIVITY_FLAG`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--根据门店，活动，活动商品获取团购接龙活动订单支付号-->
    <select id="getGroupActivityLogList"
            resultType="com.fshows.lifecircle.storagecore.service.dal.qrordering.resultmap.GroupActivityGoodsCustomerLogDO">
                    SELECT
        log.`out_store_id` as outStoreId,
        log.`activity_id` as activityId ,
        log.`activity_goods_id` as activityGoodsId,
        log.`sale_number` as saleNumber,
        dishorder.`take_order_flag` as takeOrderFlag
        from `tp_qrordering_customer_goods_log` log
        LEFT JOIN `tp_qrordering_pay_order` payorder on log.`pay_order_no` = payorder.`pay_order_no`
        LEFT JOIN `tp_qrordering_dish_order` dishorder on payorder.`pay_order_no` = dishorder.`pay_order_no`
        WHERE log.activity_id = #{activityId,jdbcType=VARCHAR}
        AND log.activity_goods_id = #{activityGoodsId,jdbcType=VARCHAR}
        AND log.out_store_id = #{outStoreId,jdbcType=VARCHAR}
        AND log.del_flag = 0
            </select>
</mapper>
