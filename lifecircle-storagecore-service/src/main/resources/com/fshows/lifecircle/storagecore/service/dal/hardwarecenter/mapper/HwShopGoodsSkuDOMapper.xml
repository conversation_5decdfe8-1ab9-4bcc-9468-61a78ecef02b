<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsSkuDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsSkuDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GOODS_SKU_ID" property="goodsSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="END_NUMBER" property="endNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_NUMBER" property="startNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SKU_PRICE" property="skuPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_SKU_ID`,`GOODS_SPU_ID`,`IS_DEL`,`END_NUMBER`,`START_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`SKU_PRICE`
    </sql>


            <!--insert:HW_SHOP_GOODS_SKU-->
            <insert id="insert" >
            INSERT INTO HW_SHOP_GOODS_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
        <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="endNumber != null">`END_NUMBER`,</if>
        <if test="startNumber != null">`START_NUMBER`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="skuPrice != null">`SKU_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
        <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="endNumber != null">#{endNumber,jdbcType=INTEGER},</if>
        <if test="startNumber != null">#{startNumber,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="skuPrice != null">#{skuPrice,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据spuId查询对应sku列表-->
            <select id="querySkuListByGoodsSpuId" resultMap="BaseResultMap">
                    SELECT /*MS-HW-SHOP-GOODS-SKU-QUERYSKULISTBYGOODSSPUID*/  <include refid="Base_Column_List" /> FROM HW_SHOP_GOODS_SKU
        WHERE is_del = 0
        AND goods_spu_id = #{goodsSpuId, jdbcType=VARCHAR};
            </select>
    </mapper>
