<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.TeachingActivityApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.TeachingActivityApplyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BIZ_NO" property="bizNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCHID" property="mchid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_IMG" property="storeImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SPECIAL_IMG" property="specialImg" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_LEASE_IMG" property="storeLeaseImg" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="LESHUA_MERCHANT_NO" property="leshuaMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIZ_STATUS" property="bizStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APPLY_SUCCESS_TIME" property="applySuccessTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="MERCHANT_RATE" property="merchantRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BIZ_NO`,`MCHID`,`ADDRESS`,`APPLY_ID`,`COMPANY`,`STORE_IMG`,`USERNAME`,`SPECIAL_IMG`,`APPLY_STATUS`,`REJECT_REASON`,`STORE_LEASE_IMG`,`LESHUA_MERCHANT_NO`,`UID`,`DEL_FLAG`,`STORE_ID`,`BIZ_STATUS`,`SUBMIT_TIME`,`APPLY_SUCCESS_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`MERCHANT_RATE`
    </sql>


            <!--insert:TP_TEACHING_ACTIVITY_APPLY-->
            <insert id="insert" >
                    INSERT INTO TP_TEACHING_ACTIVITY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bizNo != null">`BIZ_NO`,</if>
            <if test="mchid != null">`MCHID`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="storeImg != null">`STORE_IMG`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="specialImg != null">`SPECIAL_IMG`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="storeLeaseImg != null">`STORE_LEASE_IMG`,</if>
            <if test="leshuaMerchantNo != null">`LESHUA_MERCHANT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bizStatus != null">`BIZ_STATUS`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="applySuccessTime != null">`APPLY_SUCCESS_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="merchantRate != null">`MERCHANT_RATE`,</if>
            <if test="address != null">`ADDRESS`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bizNo != null">#{bizNo,jdbcType=VARCHAR},</if>
            <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
            <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="storeImg != null">#{storeImg,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="specialImg != null">#{specialImg,jdbcType=LONGVARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="storeLeaseImg != null">#{storeLeaseImg,jdbcType=LONGVARCHAR},</if>
            <if test="leshuaMerchantNo != null">#{leshuaMerchantNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bizStatus != null">#{bizStatus,jdbcType=TINYINT},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=INTEGER},</if>
            <if test="applySuccessTime != null">#{applySuccessTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="merchantRate != null">#{merchantRate,jdbcType=DECIMAL},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据条件查询教培活动列表-->
            <select id="findListByMultiCondition" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.TeachingActivityListDTO">
                    SELECT
        id,
        mchid,
        company,
        store_img as storeImg,
        username,
        special_img as specialImg,
        reject_reason as rejectReason,
        store_lease_img as storeLeaseImg,
        leshua_merchant_no as leshuaMerchantNo,
        uid,
        biz_status as bizStatus,
        submit_time as submitTime,
        apply_success_time as applySuccessTime,
        create_time as createTime,
        address
        FROM TP_TEACHING_ACTIVITY_APPLY
        WHERE
        del_flag = 0
        <if test="uid != null">
            AND UID = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null and username !='' ">
            AND USERNAME LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="mchid != null and mchid != ''">
            AND MCHID = #{mchid,jdbcType=VARCHAR}
        </if>
        <if test="leshuaMerchantNo != null and leshuaMerchantNo != ''">
            AND LESHUA_MERCHANT_NO = #{leshuaMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="bizStatus != null">
            AND BIZ_STATUS = #{bizStatus,jdbcType=INTEGER}
        </if>
        <if test="submitStartTime != null and submitEndTime != null">
            and submit_time between #{submitStartTime,jdbcType=INTEGER} and #{submitEndTime,jdbcType=INTEGER}
        </if>
        ORDER BY submit_time DESC
            </select>

            <!--getTeachingActivityDetailById-->
            <select id="getTeachingActivityDetailById" resultMap="BaseResultMap">
                    select /*MS-TP-TEACHING-ACTIVITY-APPLY-GETTEACHINGACTIVITYDETAILBYID*/ <include refid="Base_Column_List" /> from TP_TEACHING_ACTIVITY_APPLY
        WHERE id = #{id,jdbcType=INTEGER}
            </select>
    </mapper>
