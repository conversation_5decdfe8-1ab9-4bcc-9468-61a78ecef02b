<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AlipayMerchantInfoDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AlipayMerchantInfoDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QRA" property="qra" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QRC" property="qrc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUPS" property="cups" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MEMO" property="memo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MSID" property="msid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NAME" property="name" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIAS_NAME" property="aliasName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_ID" property="categoryId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXTERNAL_ID" property="externalId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_EMAIL" property="contactEmail" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_PHONE" property="contactPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERVICE_PHONE" property="servicePhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_MOBILE" property="contactMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MERCHANT_ID" property="subMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_MERCHANT_NO" property="unionMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_MERCHANT_ID" property="platformMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATION_MERCHANT_ID" property="liquidationMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="INDIRECT_LEVEL" property="indirectLevel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPGRADE_STATUS" property="upgradeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_AUTH_STATUS" property="alipayAuthStatus" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QRA`,`QRC`,`CUPS`,`MEMO`,`MSID`,`NAME`,`TOKEN`,`ALIAS_NAME`,`SHORT_NAME`,`CATEGORY_ID`,`EXTERNAL_ID`,`CONTACT_NAME`,`CONTACT_EMAIL`,`CONTACT_PHONE`,`SERVICE_PHONE`,`CONTACT_MOBILE`,`SUB_MERCHANT_ID`,`UNION_MERCHANT_NO`,`PLATFORM_MERCHANT_ID`,`LIQUIDATION_MERCHANT_ID`,`UID`,`CREATE_TIME`,`UPDATE_TIME`,`INDIRECT_LEVEL`,`UPGRADE_STATUS`,`LIQUIDATION_TYPE`,`ALIPAY_AUTH_STATUS`
    </sql>


            <!--insert:TP_ALIPAY_MERCHANT_INFO-->
            <insert id="insert" >
            INSERT INTO TP_ALIPAY_MERCHANT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="memo != null">`MEMO`,</if>
        <if test="msid != null">`MSID`,</if>
        <if test="name != null">`NAME`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="aliasName != null">`ALIAS_NAME`,</if>
        <if test="shortName != null">`SHORT_NAME`,</if>
        <if test="categoryId != null">`CATEGORY_ID`,</if>
        <if test="externalId != null">`EXTERNAL_ID`,</if>
        <if test="contactName != null">`CONTACT_NAME`,</if>
        <if test="contactEmail != null">`CONTACT_EMAIL`,</if>
        <if test="contactPhone != null">`CONTACT_PHONE`,</if>
        <if test="servicePhone != null">`SERVICE_PHONE`,</if>
        <if test="contactMobile != null">`CONTACT_MOBILE`,</if>
        <if test="subMerchantId != null">`SUB_MERCHANT_ID`,</if>
        <if test="liquidationMerchantId != null">`LIQUIDATION_MERCHANT_ID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="indirectLevel != null">`INDIRECT_LEVEL`,</if>
        <if test="upgradeStatus != null">`UPGRADE_STATUS`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="alipayAuthStatus != null">`ALIPAY_AUTH_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="memo != null">#{memo,jdbcType=VARCHAR},</if>
        <if test="msid != null">#{msid,jdbcType=VARCHAR},</if>
        <if test="name != null">#{name,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="aliasName != null">#{aliasName,jdbcType=VARCHAR},</if>
        <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
        <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
        <if test="externalId != null">#{externalId,jdbcType=VARCHAR},</if>
        <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
        <if test="contactEmail != null">#{contactEmail,jdbcType=VARCHAR},</if>
        <if test="contactPhone != null">#{contactPhone,jdbcType=VARCHAR},</if>
        <if test="servicePhone != null">#{servicePhone,jdbcType=VARCHAR},</if>
        <if test="contactMobile != null">#{contactMobile,jdbcType=VARCHAR},</if>
        <if test="subMerchantId != null">#{subMerchantId,jdbcType=VARCHAR},</if>
        <if test="liquidationMerchantId != null">#{liquidationMerchantId,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="indirectLevel != null">#{indirectLevel,jdbcType=TINYINT},</if>
        <if test="upgradeStatus != null">#{upgradeStatus,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="alipayAuthStatus != null">#{alipayAuthStatus,jdbcType=TINYINT},</if>
    </trim>
            </insert>

            <!--根据清算平台商户号查询商户信息-->
            <select id="getByLiquidationMerchantId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_ALIPAY_MERCHANT_INFO
        WHERE
        liquidation_merchant_id = #{liquidationMerchantId, jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据随行付商户号查询商户号-->
            <select id="getUidByUnionMerchantNo" resultType="java.lang.Integer">
                    SELECT /*MS-TP-ALIPAY-MERCHANT-INFO-GETUIDBYUNIONMERCHANTNO*/  uid
        FROM TP_ALIPAY_MERCHANT_INFO
        WHERE
        union_merchant_no
        = #{unionMerchantNo,jdbcType=VARCHAR}
        limit 1
            </select>
    </mapper>
