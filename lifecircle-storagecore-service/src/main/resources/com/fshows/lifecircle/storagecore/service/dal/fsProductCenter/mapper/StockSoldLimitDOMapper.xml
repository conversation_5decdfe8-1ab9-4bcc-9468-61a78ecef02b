<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.mapper.StockSoldLimitDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.dataobject.StockSoldLimitDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="GS_UID" property="gsUid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GS_STORE_ID" property="gsStoreId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="DAILY_RESET" property="dailyReset" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="RESET_SOLD_NUM" property="resetSoldNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="SOLD_NUM_LIMIT" property="soldNumLimit" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CURRENT_SOLD_NUM" property="currentSoldNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GS_UID`,`GOODS_ID`,`GS_STORE_ID`,`IS_DEL`,`DAILY_RESET`,`RESET_SOLD_NUM`,`SOLD_NUM_LIMIT`,`CURRENT_SOLD_NUM`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--get:gs_stock_sold_limit-->
    <select id="getSoldLimitByGoodsIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gs_stock_sold_limit
        where goods_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and is_del = 0
    </select>
</mapper>
