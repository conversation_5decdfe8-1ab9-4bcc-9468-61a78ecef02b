<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ProfitShareOrderDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ProfitShareOrderDetailDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="RELATION_ORDER_ID" property="relationOrderId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PROFIT_SHARE_PRICE" property="profitSharePrice" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BILL_NO" property="storeBillNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RULE_RECORD_ID" property="ruleRecordId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_NO" property="platformOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_STORE_ID" property="platformStoreId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIVE_MERCHANT_ID" property="receiveMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_DETAIL_ID" property="profitShareDetailId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_MERCHANT_ID" property="profitShareMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_ORDER_DETAIL_ID" property="profitShareOrderDetailId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEAL_STATUS" property="dealStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="RATIO" property="ratio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SERVICE_FEE_RATIO" property="serviceFeeRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORDER_PRICE`,`RELATION_ORDER_ID`,`PROFIT_SHARE_PRICE`,`SP_ID`,`TYPE`,`BLOC_ID`,`ACCOUNT_ID`,`ORDER_TYPE`,`STORE_BILL_NO`,`PLATFORM_CODE`,`RULE_RECORD_ID`,`PLATFORM_ORDER_NO`,`PLATFORM_STORE_ID`,`RECEIVE_MERCHANT_ID`,`PROFIT_SHARE_DETAIL_ID`,`PROFIT_SHARE_MERCHANT_ID`,`PROFIT_SHARE_ORDER_DETAIL_ID`,`DEL_FLAG`,`DEAL_STATUS`,`SETTLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`RATIO`,`SERVICE_FEE_RATIO`
    </sql>


            <!--insert:ACC_PROFIT_SHARE_ORDER_DETAIL-->
            <insert id="insert" >
                    INSERT INTO ACC_PROFIT_SHARE_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="profitSharePrice != null">`PROFIT_SHARE_PRICE`,</if>
            <if test="spId != null">`SP_ID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="accountId != null">`ACCOUNT_ID`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
            <if test="platformCode != null">`PLATFORM_CODE`,</if>
            <if test="platformOrderNo != null">`PLATFORM_ORDER_NO`,</if>
            <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
            <if test="relationOrderId != null">`RELATION_ORDER_ID`,</if>
            <if test="receiveMerchantId != null">`RECEIVE_MERCHANT_ID`,</if>
            <if test="profitShareDetailId != null">`PROFIT_SHARE_DETAIL_ID`,</if>
            <if test="profitShareMerchantId != null">`PROFIT_SHARE_MERCHANT_ID`,</if>
            <if test="profitShareOrderDetailId != null">`PROFIT_SHARE_ORDER_DETAIL_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="dealStatus != null">`DEAL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="ratio != null">`RATIO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=BIGINT},</if>
            <if test="profitSharePrice != null">#{profitSharePrice,jdbcType=BIGINT},</if>
            <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
            <if test="orderType != null">#{orderType,jdbcType=VARCHAR},</if>
            <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
            <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
            <if test="platformOrderNo != null">#{platformOrderNo,jdbcType=VARCHAR},</if>
            <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
            <if test="relationOrderId != null">#{relationOrderId,jdbcType=BIGINT},</if>
            <if test="receiveMerchantId != null">#{receiveMerchantId,jdbcType=VARCHAR},</if>
            <if test="profitShareDetailId != null">#{profitShareDetailId,jdbcType=VARCHAR},</if>
            <if test="profitShareMerchantId != null">#{profitShareMerchantId,jdbcType=VARCHAR},</if>
            <if test="profitShareOrderDetailId != null">#{profitShareOrderDetailId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="dealStatus != null">#{dealStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="ratio != null">#{ratio,jdbcType=DECIMAL},</if>
        </trim>
            </insert>
    </mapper>
