<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsInstallmentSnDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsInstallmentSnDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="INSTALLMENT_ID" property="installmentId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`EQUIPMENT_SN`,`INSTALLMENT_ID`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:HW_SHOP_GOODS_INSTALLMENT_SN-->
        <insert id="insert">
            INSERT INTO HW_SHOP_GOODS_INSTALLMENT_SN
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
                <if test="installmentId != null">`INSTALLMENT_ID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
                <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            </trim>
        </insert>

        <!--根据SN查询分期ID-->
        <select id="getInstallmentIdBySn" resultMap="BaseResultMap">
            SELECT /*MS-HW-SHOP-GOODS-INSTALLMENT-SN-GETINSTALLMENTIDBYSN*/
            <include refid="Base_Column_List"/>
            FROM hw_shop_goods_installment_sn
            WHERE is_del = 0
            AND equipment_sn = #{equipmentSn, jdbcType=VARCHAR}
        </select>

        <!--根据分期ID查询sn列表-->
        <select id="findInstallmentIdByInstallmentIdList" resultMap="BaseResultMap">
            SELECT /*MS-HW-SHOP-GOODS-INSTALLMENT-SN-FINDINSTALLMENTIDBYINSTALLMENTIDLIST*/
            <include refid="Base_Column_List"/>
            FROM hw_shop_goods_installment_sn
            WHERE is_del = 0
            <if test="list != null and list.size() &gt; 0 ">
                AND installment_id IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </select>
    </mapper>
