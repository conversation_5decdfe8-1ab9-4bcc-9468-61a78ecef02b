<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentWorkOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentWorkOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AUDIT_REMARK" property="auditRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LOGISTICS_NO" property="logisticsNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_REMARK" property="orderRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WORK_BATCH_NO" property="workBatchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WORK_ORDER_SN" property="workOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CANCEL_REASON" property="cancelReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ABNORMAL_REMARK" property="abnormalRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MAINTENANCE_REMARK" property="maintenanceRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PEND" property="pend" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_STOP" property="isStop" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SOURCE" property="source" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BACK_WAY" property="backWay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FB_UNBIND" property="fbUnbind" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WORK_TYPE" property="workType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WORKER_ID" property="workerId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WORK_SCENE" property="workScene" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZFB_UNBIND" property="zfbUnbind" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ASSIGN_TIME" property="assignTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ABNORMAL_TYPE" property="abnormalType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONTINUE_WORK" property="continueWork" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BACK_WAREHOUSE" property="backWarehouse" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZFB_UNBIND_STATUS" property="zfbUnbindStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`AUDIT_REMARK`,`LOGISTICS_NO`,`ORDER_REMARK`,`WORK_BATCH_NO`,`WORK_ORDER_SN`,`CANCEL_REASON`,`ABNORMAL_REMARK`,`MAINTENANCE_REMARK`,`PEND`,`IS_DEL`,`IS_STOP`,`SOURCE`,`AGENT_ID`,`BACK_WAY`,`GRANT_ID`,`STORE_ID`,`FB_UNBIND`,`WORK_TYPE`,`WORKER_ID`,`WORK_SCENE`,`ZFB_UNBIND`,`ASSIGN_TIME`,`ORDER_STATUS`,`ABNORMAL_TYPE`,`CONTINUE_WORK`,`BACK_WAREHOUSE`,`ZFB_UNBIND_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_WORK_ORDER-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_WORK_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="auditRemark != null">`AUDIT_REMARK`,</if>
            <if test="logisticsNo != null">`LOGISTICS_NO`,</if>
            <if test="orderRemark != null">`ORDER_REMARK`,</if>
            <if test="workBatchNo != null">`WORK_BATCH_NO`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="abnormalRemark != null">`ABNORMAL_REMARK`,</if>
            <if test="maintenanceRemark != null">`MAINTENANCE_REMARK`,</if>
            <if test="pend != null">`PEND`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="backWay != null">`BACK_WAY`,</if>
            <if test="fbUnbind != null">`FB_UNBIND`,</if>
            <if test="workType != null">`WORK_TYPE`,</if>
            <if test="workerId != null">`WORKER_ID`,</if>
            <if test="zfbUnbind != null">`ZFB_UNBIND`,</if>
            <if test="assignTime != null">`ASSIGN_TIME`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="abnormalType != null">`ABNORMAL_TYPE`,</if>
            <if test="backWarehouse != null">`BACK_WAREHOUSE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="auditRemark != null">#{auditRemark,jdbcType=VARCHAR},</if>
            <if test="logisticsNo != null">#{logisticsNo,jdbcType=VARCHAR},</if>
            <if test="orderRemark != null">#{orderRemark,jdbcType=VARCHAR},</if>
            <if test="workBatchNo != null">#{workBatchNo,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="abnormalRemark != null">#{abnormalRemark,jdbcType=VARCHAR},</if>
            <if test="maintenanceRemark != null">#{maintenanceRemark,jdbcType=VARCHAR},</if>
            <if test="pend != null">#{pend,jdbcType=TINYINT},</if>
            <if test="source != null">#{source,jdbcType=TINYINT},</if>
            <if test="backWay != null">#{backWay,jdbcType=TINYINT},</if>
            <if test="fbUnbind != null">#{fbUnbind,jdbcType=TINYINT},</if>
            <if test="workType != null">#{workType,jdbcType=TINYINT},</if>
            <if test="workerId != null">#{workerId,jdbcType=INTEGER},</if>
            <if test="zfbUnbind != null">#{zfbUnbind,jdbcType=TINYINT},</if>
            <if test="assignTime != null">#{assignTime,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="abnormalType != null">#{abnormalType,jdbcType=TINYINT},</if>
            <if test="backWarehouse != null">#{backWarehouse,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--批量新增-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO HW_EQUIPMENT_WORK_ORDER
        (INIT_SN,AGENT_ID,GRANT_ID,WORKER_ID,WORK_TYPE,ORDER_REMARK,WORK_BATCH_NO,WORK_ORDER_SN,SOURCE,FB_UNBIND,ZFB_UNBIND,ORDER_STATUS,ASSIGN_TIME,STORE_ID)
        values
        <foreach collection="list" item="order" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{order.initSn,jdbcType=VARCHAR},
                #{order.agentId,jdbcType=INTEGER},
                #{order.grantId,jdbcType=INTEGER},
                #{order.workerId,jdbcType=INTEGER},
                #{order.workType,jdbcType=TINYINT},
                #{order.orderRemark,jdbcType=VARCHAR},
                #{order.workBatchNo,jdbcType=VARCHAR},
                #{order.workOrderSn,jdbcType=VARCHAR},
                #{order.source,jdbcType=TINYINT},
                #{order.fbUnbind,jdbcType=TINYINT},
                #{order.zfbUnbind,jdbcType=TINYINT},
                #{order.orderStatus,jdbcType=TINYINT},
                #{order.assignTime,jdbcType=INTEGER},
                #{order.storeId,jdbcType=INTEGER},
            </trim>
        </foreach>
            </select>

            <!--updateSelective-->
            <update id="updateSelective" >
                    update /*MS-HW-EQUIPMENT-WORK-ORDER-UPDATESELECTIVE*/ hw_equipment_work_order
        <trim prefix="set" suffixOverrides=",">
            <if test="orderRemark != null">`ORDER_REMARK`=#{orderRemark,jdbcType=VARCHAR},</if>
            <if test="workerId != null">`WORKER_ID`=#{workerId,jdbcType=INTEGER},</if>
        </trim>
        where work_order_sn=#{workOrderSn,jdbcType=VARCHAR}
            </update>

            <!--getByInitSnAndStatusNotEnd-->
            <select id="getByInitSnAndStatusNotEnd" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-WORK-ORDER-GETBYINITSNANDSTATUSNOTEND*/  <include refid="Base_Column_List" /> FROM HW_EQUIPMENT_WORK_ORDER
        WHERE INIT_SN = #{initSn,jdbcType=VARCHAR}
        and IS_DEL = 0 and order_status !=5 order by id desc limit 1
            </select>

            <!--根据设备SN查询未完成的工单-->
            <select id="countUnFinishByInitSn" resultType="java.lang.Integer">
                    select /*MS-HW-EQUIPMENT-WORK-ORDER-COUNTUNFINISHBYINITSN*/  count(*) 
        from hw_equipment_work_order
        where init_sn=#{initSn,jdbcType=VARCHAR} and is_del=0 and order_status != 5
            </select>

            <!--查询设备最后一条工单-->
            <select id="getLastWorkOrderByInitSn" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-WORK-ORDER-GETLASTWORKORDERBYINITSN*/  <include refid="Base_Column_List" /> FROM HW_EQUIPMENT_WORK_ORDER
        WHERE INIT_SN = #{initSn,jdbcType=VARCHAR}
        and IS_DEL = 0 order by id desc limit 1
            </select>
    </mapper>
