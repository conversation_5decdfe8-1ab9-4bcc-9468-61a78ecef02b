<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwAlipayTouchEquipActivityDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwAlipayTouchEquipActivityDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_MODEL" property="equipmentModel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`HW_ORDER_SN`,`EQUIPMENT_MODEL`,`IS_DEL`,`AGENT_ID`,`SALESMAN_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_ALIPAY_TOUCH_EQUIP_ACTIVITY-->
            <insert id="insert" >
                    INSERT INTO HW_ALIPAY_TOUCH_EQUIP_ACTIVITY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="equipmentModel != null">`EQUIPMENT_MODEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="equipmentModel != null">#{equipmentModel,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--分页查询 pageCount-->
            <select id="pageActivityListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 HW_ALIPAY_TOUCH_EQUIP_ACTIVITY a
        left join tp_user b on a.agent_id = b.id
        left join tp_user c on a.salesman_id = c.id
        <where>
            a.is_del = 0
            <if test="initSn != null and initSn != ''">
                and a.init_sn = #{initSn,jdbcType=VARCHAR}
            </if>
            <if test="hwOrderSn != null and hwOrderSn != ''">
                and a.hw_order_sn = #{hwOrderSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentModel != null and equipmentModel != ''">
                and a.equipment_model = #{equipmentModel,jdbcType=VARCHAR}
            </if>
            <if test="agentId != null">
                and a.agent_id = #{agentId,jdbcType=INTEGER}
            </if>
            <if test="salesmanId != null">
                and a.salesman_id = #{salesmanId,jdbcType=INTEGER}
            </if>
            <if test="agentName != null and agentName != ''">
                and b.username = #{agentName, jdbcType=VARCHAR}
            </if>
            <if test="salesmanName != null and salesmanName != ''">
                and c.username = #{salesmanName, jdbcType=VARCHAR}
            </if>
            <if test="endTime != null">
                and a.create_time &lt; #{endTime, jdbcType=TIMESTAMP}
            </if>
            <if test="startTime != null">
                and a.create_time &gt; #{startTime, jdbcType=TIMESTAMP}
            </if>
        </where>
        
            </select>
            <!--分页查询 pageResult-->
            <select id="pageActivityListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.AlipayTouchActivityItemDTO">
                    select /*MS-HW-ALIPAY-TOUCH-EQUIP-ACTIVITY-PAGEACTIVITYLIST*/ a.init_sn,a.hw_order_sn,a.equipment_model,a.agent_id,a.salesman_id,
        DATE_FORMAT(a.create_time, '%Y-%m-%d %H:%i') as 'createTime',
        b.username as 'agentName', c.username as 'salesmanName'
        from HW_ALIPAY_TOUCH_EQUIP_ACTIVITY a
        left join tp_user b on a.agent_id = b.id
        left join tp_user c on a.salesman_id = c.id
        <where>
            a.is_del = 0
            <if test="initSn != null and initSn != ''">
                and a.init_sn = #{initSn,jdbcType=VARCHAR}
            </if>
            <if test="hwOrderSn != null and hwOrderSn != ''">
                and a.hw_order_sn = #{hwOrderSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentModel != null and equipmentModel != ''">
                and a.equipment_model = #{equipmentModel,jdbcType=VARCHAR}
            </if>
            <if test="agentId != null">
                and a.agent_id = #{agentId,jdbcType=INTEGER}
            </if>
            <if test="salesmanId != null">
                and a.salesman_id = #{salesmanId,jdbcType=INTEGER}
            </if>
            <if test="agentName != null and agentName != ''">
                and b.username = #{agentName, jdbcType=VARCHAR}
            </if>
            <if test="salesmanName != null and salesmanName != ''">
                and c.username = #{salesmanName, jdbcType=VARCHAR}
            </if>
            <if test="endTime != null">
                and a.create_time &lt; #{endTime, jdbcType=TIMESTAMP}
            </if>
            <if test="startTime != null">
                and a.create_time &gt; #{startTime, jdbcType=TIMESTAMP}
            </if>
        </where>
        order by a.id desc
            limit #{startRow},#{limit}
            </select>

            <!--根据设备sn查询-->
            <select id="getByInitSn" resultMap="BaseResultMap">
                    select /*MS-HW-ALIPAY-TOUCH-EQUIP-ACTIVITY-GETBYINITSN*/ <include refid="Base_Column_List" /> from HW_ALIPAY_TOUCH_EQUIP_ACTIVITY where init_sn = #{initSn, jdbcType=VARCHAR} and is_del = 0
            </select>

            <!--batchInsert:HW_ALIPAY_TOUCH_EQUIP_ACTIVITY-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO HW_ALIPAY_TOUCH_EQUIP_ACTIVITY (init_sn,hw_order_sn,equipment_model,agent_id,salesman_id)
        values
        <foreach collection="list" separator="," item="item">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.initSn,jdbcType=VARCHAR},
                #{item.hwOrderSn,jdbcType=VARCHAR},
                #{item.equipmentModel,jdbcType=VARCHAR},
                #{item.agentId,jdbcType=INTEGER},
                #{item.salesmanId,jdbcType=INTEGER}
            </trim>
        </foreach>
            </select>
    </mapper>
