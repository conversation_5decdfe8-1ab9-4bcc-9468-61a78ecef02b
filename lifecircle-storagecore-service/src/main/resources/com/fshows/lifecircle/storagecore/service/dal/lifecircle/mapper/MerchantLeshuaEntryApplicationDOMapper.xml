<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantLeshuaEntryApplicationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantLeshuaEntryApplicationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="QRA" property="qra" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QRC" property="qrc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUPS" property="cups" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCC_CODE" property="mccCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PIC" property="storePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_PID" property="alipayPid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OTHER_PICS" property="otherPics" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_SMID" property="alipaySmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_NO" property="identityNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_AGENT_ID" property="subAgentId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_NAME" property="identityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HAND_IDCARD_PIC" property="handIdcardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HEAD_MERCHANT_NO" property="headMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INSIDE_SCENE_PIC" property="insideScenePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_MOBILE" property="merchantMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_SUB_MCH_ID" property="wechatSubMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LETTER_OF_AUTH_PIC" property="letterOfAuthPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_LICENSE_NO" property="settleLicenseNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_PLACE_PIC" property="businessPlacePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANKCARD_NO" property="settleBankcardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_CHANNEL_NUM" property="wechatChannelNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_OPENING_PIC" property="accountOpeningPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_TELEPHONE" property="customerTelephone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_SHORT_NAME" property="merchantShortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NAME" property="settleAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_REJECT_REASON" property="unionRejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANKCARD_POSITIVE_PIC" property="bankcardPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_CODE" property="businessLicenseCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_NAME" property="businessLicenseName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_LICENSE_MOBILE" property="settleLicenseMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_ADDRESS" property="businessLicenseAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_END_TIME" property="businessLicenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_OPPOSITE_PIC" property="legalPersonOppositePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_POSITIVE_PIC" property="legalPersonPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_START_TIME" property="businessLicenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_LICENSE_END_TIME" property="legalPersonLicenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_END_TIME" property="settlePersonIdcardEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_LICENSE_START_TIME" property="legalPersonLicenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_START_TIME" property="settlePersonIdcardStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_OPPOSITE_PIC" property="settlePersonIdcardOppositePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_POSITIVE_PIC" property="settlePersonIdcardPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PA_CITY_ID" property="paCityId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_TO" property="settleTo" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHOP_TYPE" property="shopType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_FEE_ID" property="userFeeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_CODE_ID" property="bankCodeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CONFIG_STEP" property="configStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DATA_SOURCE" property="dataSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PA_COUNTY_ID" property="paCountyId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IDENTITY_TYPE" property="identityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PA_PROVINCE_ID" property="paProvinceId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WECHANT_CHANNEL_ID" property="wechantChannelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_ACCOUNT_FLAT" property="settleAccountFlat" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_ACCOUNT_TYPE" property="settleAccountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHORT_NAME_MODIFY_TIME" property="shortNameModifyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MERCHANT_QUALIFICATION" property="merchantQualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UNION_RATE" property="unionRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ALIPAY_RATE" property="alipayRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="WECHAT_RATE" property="wechatRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QRA`,`QRC`,`CUPS`,`ADDRESS`,`MCC_CODE`,`BANK_CODE`,`CITY_CODE`,`STORE_PIC`,`ALIPAY_PID`,`OTHER_PICS`,`UNION_CODE`,`ALIPAY_SMID`,`BANK_NUMBER`,`IDENTITY_NO`,`LICENSE_PIC`,`MERCHANT_NO`,`SUB_AGENT_ID`,`DISTRICT_CODE`,`IDENTITY_NAME`,`PROVINCE_CODE`,`REJECT_REASON`,`HAND_IDCARD_PIC`,`PLATFORM_ORG_ID`,`HEAD_MERCHANT_NO`,`INSIDE_SCENE_PIC`,`MERCHANT_MOBILE`,`WECHAT_SUB_MCH_ID`,`LETTER_OF_AUTH_PIC`,`SETTLE_LICENSE_NO`,`BUSINESS_PLACE_PIC`,`SETTLE_BANKCARD_NO`,`WECHAT_CHANNEL_NUM`,`ACCOUNT_OPENING_PIC`,`CUSTOMER_TELEPHONE`,`MERCHANT_SHORT_NAME`,`SETTLE_ACCOUNT_NAME`,`UNION_REJECT_REASON`,`BANKCARD_POSITIVE_PIC`,`BUSINESS_LICENSE_CODE`,`BUSINESS_LICENSE_NAME`,`SETTLE_LICENSE_MOBILE`,`BUSINESS_LICENSE_ADDRESS`,`BUSINESS_LICENSE_END_TIME`,`LEGAL_PERSON_OPPOSITE_PIC`,`LEGAL_PERSON_POSITIVE_PIC`,`BUSINESS_LICENSE_START_TIME`,`LEGAL_PERSON_LICENSE_END_TIME`,`SETTLE_PERSON_IDCARD_END_TIME`,`LEGAL_PERSON_LICENSE_START_TIME`,`SETTLE_PERSON_IDCARD_START_TIME`,`SETTLE_PERSON_IDCARD_OPPOSITE_PIC`,`SETTLE_PERSON_IDCARD_POSITIVE_PIC`,`UID`,`IS_DEL`,`STORE_ID`,`PA_CITY_ID`,`SETTLE_TO`,`SHOP_TYPE`,`USER_FEE_ID`,`BANK_CODE_ID`,`CONFIG_STEP`,`DATA_SOURCE`,`PA_COUNTY_ID`,`APPLY_STATUS`,`IDENTITY_TYPE`,`PA_PROVINCE_ID`,`WECHANT_CHANNEL_ID`,`SETTLE_ACCOUNT_FLAT`,`SETTLE_ACCOUNT_TYPE`,`SHORT_NAME_MODIFY_TIME`,`MERCHANT_QUALIFICATION`,`CREATE_TIME`,`UPDATE_TIME`,`UNION_RATE`,`ALIPAY_RATE`,`WECHAT_RATE`
    </sql>


            <!--获取高校独立结算信息-->
            <select id="getByAlipaySmid" resultMap="BaseResultMap">
                    select /*MS-TP-MERCHANT-LESHUA-ENTRY-APPLICATION-GETBYALIPAYSMID*/ <include refid="Base_Column_List" /> from TP_MERCHANT_LESHUA_ENTRY_APPLICATION
        where uid = #{uid,jdbcType=INTEGER} and alipay_smid = #{alipaySmid,jdbcType=VARCHAR}
            </select>

            <!--根据乐刷商户获取申请单-->
            <select id="getByMerchantNo" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MERCHANT-LESHUA-ENTRY-APPLICATION-GETBYMERCHANTNO*/  <include refid="Base_Column_List" />
        FROM `tp_merchant_leshua_entry_application`
        WHERE `merchant_no`= #{merchantNo,jdbcType=VARCHAR}
        LIMIT 1;
            </select>

            <!--根据商户ID,门店ID以及总行类型获取申请单-->
            <select id="getByUidAndStoreIdAndShopType" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MERCHANT-LESHUA-ENTRY-APPLICATION-GETBYUIDANDSTOREIDANDSHOPTYPE*/  <include refid="Base_Column_List" />
        FROM `tp_merchant_leshua_entry_application`
        where uid = #{uid,jdbcType=INTEGER}
        <if test="storeId != null">
            and `store_id` = #{storeId,jdbcType=INTEGER}
        </if>
        <if test="shopType != null">
            and shop_type = #{shopType,jdbcType=INTEGER}
        </if>
        ORDER BY id asc
        LIMIT 1;
            </select>
    </mapper>
