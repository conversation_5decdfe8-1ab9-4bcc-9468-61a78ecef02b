<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.PartnerBillDetailDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.PartnerBillDetailDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PARTNER_USERNAME" property="partnerUsername" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PT_MONTH" property="ptMonth" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PARTNER_ID" property="partnerId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="NORMAL_TRADE_COUNT" property="normalTradeCount" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="REFUND_TRADE_COUNT" property="refundTradeCount" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="COEFFICIENT_DIFF" property="coefficientDiff" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PAYABLE_COMMISSION" property="payableCommission" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="DEDUCTION_COMMISSION" property="deductionCommission" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="AGENT_SETTLEMENT_COEFFICIENT" property="agentSettlementCoefficient" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PARTNER_SETTLEMENT_COEFFICIENT" property="partnerSettlementCoefficient" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`FEE_CODE`,`SOURCE_TYPE`,`AGENT_USERNAME`,`PARTNER_USERNAME`,`AGENT_ID`,`PT_MONTH`,`PARTNER_ID`,`BUSINESS_DATE`,`NORMAL_TRADE_COUNT`,`REFUND_TRADE_COUNT`,`CREATE_TIME`,`UPDATE_TIME`,`COEFFICIENT_DIFF`,`NORMAL_TRADE_AMOUNT`,`PAYABLE_COMMISSION`,`REFUND_TRADE_AMOUNT`,`DEDUCTION_COMMISSION`,`ACTUAL_PAYABLE_COMMISSION`,`AGENT_SETTLEMENT_COEFFICIENT`,`PARTNER_SETTLEMENT_COEFFICIENT`
    </sql>


    <!--insert:FP_PARTNER_BILL_DETAIL-->
    <insert id="insert">
        INSERT INTO FP_PARTNER_BILL_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="partnerUsername != null">`PARTNER_USERNAME`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="partnerId != null">`PARTNER_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="coefficientDiff != null">`COEFFICIENT_DIFF`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
            <if test="agentSettlementCoefficient != null">`AGENT_SETTLEMENT_COEFFICIENT`,</if>
            <if test="partnerSettlementCoefficient != null">`PARTNER_SETTLEMENT_COEFFICIENT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="partnerUsername != null">#{partnerUsername,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="partnerId != null">#{partnerId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="coefficientDiff != null">#{coefficientDiff,jdbcType=DECIMAL},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
            <if test="agentSettlementCoefficient != null">#{agentSettlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="partnerSettlementCoefficient != null">#{partnerSettlementCoefficient,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--findPartnerCommissionList-->
    <select id="findPartnerCommissionList" resultMap="BaseResultMap">
        select /*MS-FP-PARTNER-BILL-DETAIL-FINDPARTNERCOMMISSIONLIST*/
        <include refid="Base_Column_List"/>
        from fp_partner_bill_detail
        where agent_id = #{agentId,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and business_date= #{businessDate,jdbcType=INTEGER}
        and source_type in
        <foreach collection="sourceTypeList" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </select>
</mapper>
