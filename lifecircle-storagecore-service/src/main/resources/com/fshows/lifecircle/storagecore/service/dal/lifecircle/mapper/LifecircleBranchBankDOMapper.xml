<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleBranchBankDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleBranchBankDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_FULL_NAME" property="bankFullName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_SHORT_NAME" property="bankShortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GENERAL_BANK_CODE" property="generalBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BANK_CODE`,`BANK_NAME`,`CITY_CODE`,`BANK_FULL_NAME`,`PROVINCE_CODE`,`BANK_SHORT_NAME`,`GENERAL_BANK_CODE`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--根据bankCode查询支行信息-->
            <select id="getByBankCode" resultMap="BaseResultMap">
                    select /*MS-TP-LIFECIRCLE-BRANCH-BANK-GETBYBANKCODE*/ <include refid="Base_Column_List" />
        from
        TP_LIFECIRCLE_BRANCH_BANK
        where
        bank_code = #{bankCode,jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据bankName查询支行信息-->
            <select id="getByBankName" resultMap="BaseResultMap">
                    select /*MS-TP-LIFECIRCLE-BRANCH-BANK-GETBYBANKNAME*/ <include refid="Base_Column_List" />
        from
        TP_LIFECIRCLE_BRANCH_BANK
        where
        bank_name = #{bankName,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
