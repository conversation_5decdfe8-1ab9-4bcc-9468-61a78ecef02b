<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsInstallmentNumberDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsInstallmentNumberDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ATTACHMENT_URL" property="attachmentUrl" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="INSTALLMENT_ID" property="installmentId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="REPAYMENT_TYPE" property="repaymentType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="REPAYMENT_STATUS" property="repaymentStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="REAL_REPAYMENT_TIME" property="realRepaymentTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="PREDICT_REPAYMENT_DATE" property="predictRepaymentDate" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="REAL_REPAYMENT_AMOUNT" property="realRepaymentAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="PREDICT_REPAYMENT_AMOUNT" property="predictRepaymentAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`REMARK`,`ATTACHMENT_URL`,`INSTALLMENT_ID`,`IS_DEL`,`REPAYMENT_TYPE`,`REPAYMENT_STATUS`,`REAL_REPAYMENT_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`PREDICT_REPAYMENT_DATE`,`REAL_REPAYMENT_AMOUNT`,`PREDICT_REPAYMENT_AMOUNT`
        </sql>


        <!--insert:HW_SHOP_GOODS_INSTALLMENT_NUMBER-->
        <insert id="insert">
            INSERT INTO HW_SHOP_GOODS_INSTALLMENT_NUMBER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="remark != null">`REMARK`,</if>
                <if test="attachmentUrl != null">`ATTACHMENT_URL`,</if>
                <if test="installmentId != null">`INSTALLMENT_ID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="repaymentType != null">`REPAYMENT_TYPE`,</if>
                <if test="repaymentStatus != null">`REPAYMENT_STATUS`,</if>
                <if test="realRepaymentTime != null">`REAL_REPAYMENT_TIME`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="predictRepaymentDate != null">`PREDICT_REPAYMENT_DATE`,</if>
            <if test="realRepaymentAmount != null">`REAL_REPAYMENT_AMOUNT`,</if>
            <if test="predictRepaymentAmount != null">`PREDICT_REPAYMENT_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="attachmentUrl != null">#{attachmentUrl,jdbcType=VARCHAR},</if>
            <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="repaymentType != null">#{repaymentType,jdbcType=TINYINT},</if>
            <if test="repaymentStatus != null">#{repaymentStatus,jdbcType=TINYINT},</if>
            <if test="realRepaymentTime != null">#{realRepaymentTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="predictRepaymentDate != null">#{predictRepaymentDate,jdbcType=TIMESTAMP},</if>
            <if test="realRepaymentAmount != null">#{realRepaymentAmount,jdbcType=DECIMAL},</if>
            <if test="predictRepaymentAmount != null">#{predictRepaymentAmount,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据ID查询分期订单列表-->
        <select id="findInstallmentNumberListByRepaymentDate" resultMap="BaseResultMap">
            SELECT /*MS-HW-SHOP-GOODS-INSTALLMENT-NUMBER-FINDINSTALLMENTNUMBERLISTBYREPAYMENTDATE*/
            <include refid="Base_Column_List"/>
            FROM hw_shop_goods_installment_number
            WHERE is_del = 0
            <if test="predictRepaymentStartDate != null">
                AND predict_repayment_date <![CDATA[>=]]> #{predictRepaymentStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="predictRepaymentEndDate != null">
                AND predict_repayment_date <![CDATA[<=]]> #{predictRepaymentEndDate,jdbcType=TIMESTAMP}
            </if>
        </select>

        <!--根据ID查询分期订单列表-->
        <select id="findInstallmentNumberListByInstallmentIdList" resultMap="BaseResultMap">
            SELECT /*MS-HW-SHOP-GOODS-INSTALLMENT-NUMBER-FINDINSTALLMENTNUMBERLISTBYINSTALLMENTIDLIST*/
            <include refid="Base_Column_List"/>
            FROM hw_shop_goods_installment_number
            WHERE is_del = 0
            AND installment_id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
