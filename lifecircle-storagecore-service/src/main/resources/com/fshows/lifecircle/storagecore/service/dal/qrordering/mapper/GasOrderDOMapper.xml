<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.GasOrderDOMapper">

    <resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.GasOrderDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="PID" property="pid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="INST_NO" property="instNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="USER_ID" property="userId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ADDRESS" property="address" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BILL_KEY" property="billKey" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BIZ_TYPE" property="bizType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BILL_DATE" property="billDate" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CARD_INFO" property="cardInfo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEVICE_SN" property="deviceSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OWNER_NAME" property="ownerName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REQUEST_ID" property="requestId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CHARGE_INST" property="chargeInst" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRICE_LEVEL" property="priceLevel" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SUB_BIZ_TYPE" property="subBizType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SUPPLIER_ID" property="supplierId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXTEND_FIELD" property="extendField" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FACTORY_CODE" property="factoryCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FUBEI_ORDER_SN" property="fubeiOrderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SUPPLIER_NAME" property="supplierName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FUBEI_REFUND_SN" property="fubeiRefundSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORGANIZATION_NO" property="organizationNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="USER_IDENTITY_CODE" property="userIdentityCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CODE_TYPE" property="codeType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PAY_STATUS" property="payStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="TRADE_DATE" property="tradeDate" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PURCHASE_ID" property="purchaseId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="REFUND_TIME" property="refundTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CHARGE_MODEL" property="chargeModel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="WRITE_STATUS" property="writeStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="WRITE_OFF_STATUS" property="writeOffStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="RATE" property="rate" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PRICE" property="price" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="BALANCE" property="balance" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="CARD_VAL" property="cardVal" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="RATE_FEE" property="rateFee" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PAY_AMOUNT" property="payAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="FINE_AMOUNT" property="fineAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="ALLOW_AMOUNT" property="allowAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="LADDER_AMOUNT" property="ladderAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="RECEIPT_AMOUNT" property="receiptAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="RECHARGE_VALUE" property="rechargeValue" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="RECHARGE_AMOUNT" property="rechargeAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`PID`,`TOKEN`,`BILL_NO`,`CARD_NO`,`INST_NO`,`USER_ID`,`ADDRESS`,`BILL_KEY`,`BIZ_TYPE`,`ORDER_SN`,`BILL_DATE`,`CARD_INFO`,`DEVICE_SN`,`OWNER_NAME`,`REQUEST_ID`,`CHARGE_INST`,`PRICE_LEVEL`,`SUB_BIZ_TYPE`,`SUPPLIER_ID`,`EXTEND_FIELD`,`FACTORY_CODE`,`FUBEI_ORDER_SN`,`SUPPLIER_NAME`,`FUBEI_REFUND_SN`,`ORGANIZATION_NO`,`USER_IDENTITY_CODE`,`DEL_FLAG`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`CODE_TYPE`,`PAY_STATUS`,`TRADE_DATE`,`MERCHANT_ID`,`PURCHASE_ID`,`REFUND_TIME`,`CHARGE_MODEL`,`WRITE_STATUS`,`REFUND_STATUS`,`WRITE_OFF_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`RATE`,`PRICE`,`BALANCE`,`CARD_VAL`,`RATE_FEE`,`PAY_AMOUNT`,`FINE_AMOUNT`,`ALLOW_AMOUNT`,`LADDER_AMOUNT`,`RECEIPT_AMOUNT`,`RECHARGE_VALUE`,`RECHARGE_AMOUNT`
    </sql>


    <!--insert:TP_GAS_ORDER-->
    <insert id="insert">
        INSERT INTO TP_GAS_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="billNo != null">`BILL_NO`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="instNo != null">`INST_NO`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="billKey != null">`BILL_KEY`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="cardInfo != null">`CARD_INFO`,</if>
            <if test="deviceSn != null">`DEVICE_SN`,</if>
            <if test="ownerName != null">`OWNER_NAME`,</if>
            <if test="requestId != null">`REQUEST_ID`,</if>
            <if test="chargeInst != null">`CHARGE_INST`,</if>
            <if test="priceLevel != null">`PRICE_LEVEL`,</if>
            <if test="subBizType != null">`SUB_BIZ_TYPE`,</if>
            <if test="supplierId != null">`SUPPLIER_ID`,</if>
            <if test="extendField != null">`EXTEND_FIELD`,</if>
            <if test="factoryCode != null">`FACTORY_CODE`,</if>
            <if test="fubeiOrderSn != null">`FUBEI_ORDER_SN`,</if>
            <if test="supplierName != null">`SUPPLIER_NAME`,</if>
            <if test="fubeiRefundSn != null">`FUBEI_REFUND_SN`,</if>
            <if test="organizationNo != null">`ORGANIZATION_NO`,</if>
            <if test="userIdentityCode != null">`USER_IDENTITY_CODE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="tradeDate != null">`TRADE_DATE`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="purchaseId != null">`PURCHASE_ID`,</if>
            <if test="refundTime != null">`REFUND_TIME`,</if>
            <if test="chargeModel != null">`CHARGE_MODEL`,</if>
            <if test="writeStatus != null">`WRITE_STATUS`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="writeOffStatus != null">`WRITE_OFF_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="rate != null">`RATE`,</if>
            <if test="price != null">`PRICE`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="cardVal != null">`CARD_VAL`,</if>
            <if test="rateFee != null">`RATE_FEE`,</if>
            <if test="payAmount != null">`PAY_AMOUNT`,</if>
            <if test="fineAmount != null">`FINE_AMOUNT`,</if>
            <if test="allowAmount != null">`ALLOW_AMOUNT`,</if>
            <if test="ladderAmount != null">`LADDER_AMOUNT`,</if>
            <if test="receiptAmount != null">`RECEIPT_AMOUNT`,</if>
            <if test="rechargeValue != null">`RECHARGE_VALUE`,</if>
            <if test="rechargeAmount != null">`RECHARGE_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="pid != null">#{pid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="instNo != null">#{instNo,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="billKey != null">#{billKey,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=VARCHAR},</if>
            <if test="cardInfo != null">#{cardInfo,jdbcType=VARCHAR},</if>
            <if test="deviceSn != null">#{deviceSn,jdbcType=VARCHAR},</if>
            <if test="ownerName != null">#{ownerName,jdbcType=VARCHAR},</if>
            <if test="requestId != null">#{requestId,jdbcType=VARCHAR},</if>
            <if test="chargeInst != null">#{chargeInst,jdbcType=VARCHAR},</if>
            <if test="priceLevel != null">#{priceLevel,jdbcType=VARCHAR},</if>
            <if test="subBizType != null">#{subBizType,jdbcType=VARCHAR},</if>
            <if test="supplierId != null">#{supplierId,jdbcType=VARCHAR},</if>
            <if test="extendField != null">#{extendField,jdbcType=VARCHAR},</if>
            <if test="factoryCode != null">#{factoryCode,jdbcType=VARCHAR},</if>
            <if test="fubeiOrderSn != null">#{fubeiOrderSn,jdbcType=VARCHAR},</if>
            <if test="supplierName != null">#{supplierName,jdbcType=VARCHAR},</if>
            <if test="fubeiRefundSn != null">#{fubeiRefundSn,jdbcType=VARCHAR},</if>
            <if test="organizationNo != null">#{organizationNo,jdbcType=VARCHAR},</if>
            <if test="userIdentityCode != null">#{userIdentityCode,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="purchaseId != null">#{purchaseId,jdbcType=INTEGER},</if>
            <if test="refundTime != null">#{refundTime,jdbcType=INTEGER},</if>
            <if test="chargeModel != null">#{chargeModel,jdbcType=TINYINT},</if>
            <if test="writeStatus != null">#{writeStatus,jdbcType=TINYINT},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="writeOffStatus != null">#{writeOffStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="rate != null">#{rate,jdbcType=DECIMAL},</if>
            <if test="price != null">#{price,jdbcType=DECIMAL},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="cardVal != null">#{cardVal,jdbcType=DECIMAL},</if>
            <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
            <if test="payAmount != null">#{payAmount,jdbcType=DECIMAL},</if>
            <if test="fineAmount != null">#{fineAmount,jdbcType=DECIMAL},</if>
            <if test="allowAmount != null">#{allowAmount,jdbcType=DECIMAL},</if>
            <if test="ladderAmount != null">#{ladderAmount,jdbcType=DECIMAL},</if>
            <if test="receiptAmount != null">#{receiptAmount,jdbcType=DECIMAL},</if>
            <if test="rechargeValue != null">#{rechargeValue,jdbcType=DECIMAL},</if>
            <if test="rechargeAmount != null">#{rechargeAmount,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--查询支付成功待充值和充值成功，实际写卡失败-->
    <select id="findGasOrder" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.GasOrderDTO">
        SELECT order_sn         orderSn,
               store_id         storeId,
               device_sn        deviceSn,
               pay_time         payTime,
               bill_key         billKey,
               owner_name       ownerName,
               pay_status       payStatus,
               write_off_status writeOffStatus
        from tp_gas_order
        where pay_status = 1
          and write_off_status = 0
          and create_time between #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
        union all
        SELECT order_sn         orderSn,
               store_id         storeId,
               device_sn        deviceSn,
               pay_time         payTime,
               bill_key         billKey,
               owner_name       ownerName,
               pay_status       payStatus,
               write_off_status writeOffStatus
        from tp_gas_order
        where write_status = 2
          and create_time between #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
    </select>
</mapper>
