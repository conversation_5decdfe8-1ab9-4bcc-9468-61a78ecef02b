<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleActivityMchidDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleActivityMchidDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NAME" property="merchantName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STEP" property="step" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_FINISH" property="isFinish" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_STATUS" property="storeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_STATUS" property="merchantStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WECHANT_AUTH_STATUS" property="wechantAuthStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="AUTH_SYNC_TIME" property="authSyncTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`ERROR_MSG`,`SUB_MCH_ID`,`SHORT_NAME`,`ACTIVITY_ID`,`MERCHANT_NAME`,`UID`,`STEP`,`IS_DEL`,`STORE_ID`,`IS_FINISH`,`CHANNEL_ID`,`STORE_STATUS`,`MERCHANT_STATUS`,`LIQUIDATION_TYPE`,`WECHANT_AUTH_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`AUTH_SYNC_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_ACTIVITY_MCHID-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_ACTIVITY_MCHID
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="errorMsg != null">`ERROR_MSG`,</if>
        <if test="subMchId != null">`SUB_MCH_ID`,</if>
        <if test="shortName != null">`SHORT_NAME`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="merchantName != null">`MERCHANT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="step != null">`STEP`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="isFinish != null">`IS_FINISH`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="storeStatus != null">`STORE_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
        <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
        <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
        <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
        <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="step != null">#{step,jdbcType=TINYINT},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="storeStatus != null">#{storeStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据子商户号查询商户id-->
            <select id="getBySubMchId" resultType="java.lang.Integer">
                    SELECT
        uid
        FROM
        TP_LIFECIRCLE_ACTIVITY_MCHID
        WHERE
        sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1
            </select>

            <!--根据子商户号查询信息-->
            <select id="getInfoBySubMchId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_LIFECIRCLE_ACTIVITY_MCHID
        WHERE
        sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
