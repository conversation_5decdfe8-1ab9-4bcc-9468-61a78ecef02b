<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmChannelPolicyFormDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmChannelPolicyFormDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_ID" property="policyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_NAME" property="policyName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_FORM_ID" property="policyFormId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_MANAGER" property="provinceManager" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATIONAL_NOTES" property="operationalNotes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELATED_SIGN_ID_LIST" property="relatedSignIdList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_PROOF_URL_LIST" property="paymentProofUrlList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUDIT_STATUS" property="auditStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RELATED_AGENT_ID" property="relatedAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLEMENT_START_TIME" property="settlementStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`POLICY_ID`,`POLICY_NAME`,`POLICY_FORM_ID`,`PROVINCE_MANAGER`,`OPERATIONAL_NOTES`,`RELATED_SIGN_ID_LIST`,`PAYMENT_PROOF_URL_LIST`,`IS_DEL`,`USER_ID`,`AGENT_ID`,`GRANT_ID`,`MARKET_ID`,`PARENT_ID`,`USER_TYPE`,`AUDIT_STATUS`,`RELATED_AGENT_ID`,`SETTLEMENT_START_TIME`,`CREATE_TIME`,`SUBMIT_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_CHANNEL_POLICY_FORM-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY_FORM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="policyName != null">`POLICY_NAME`,</if>
            <if test="policyFormId != null">`POLICY_FORM_ID`,</if>
            <if test="provinceManager != null">`PROVINCE_MANAGER`,</if>
            <if test="operationalNotes != null">`OPERATIONAL_NOTES`,</if>
            <if test="relatedSignIdList != null">`RELATED_SIGN_ID_LIST`,</if>
            <if test="paymentProofUrlList != null">`PAYMENT_PROOF_URL_LIST`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="relatedAgentId != null">`RELATED_AGENT_ID`,</if>
            <if test="settlementStartTime != null">`SETTLEMENT_START_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="policyName != null">#{policyName,jdbcType=VARCHAR},</if>
            <if test="policyFormId != null">#{policyFormId,jdbcType=VARCHAR},</if>
            <if test="provinceManager != null">#{provinceManager,jdbcType=VARCHAR},</if>
            <if test="operationalNotes != null">#{operationalNotes,jdbcType=VARCHAR},</if>
            <if test="relatedSignIdList != null">#{relatedSignIdList,jdbcType=VARCHAR},</if>
            <if test="paymentProofUrlList != null">#{paymentProofUrlList,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="relatedAgentId != null">#{relatedAgentId,jdbcType=INTEGER},</if>
            <if test="settlementStartTime != null">#{settlementStartTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--批量插入-->
            <insert id="insertBatch" >
                    INSERT INTO
        LM_CRM_CHANNEL_POLICY_FORM(POLICY_ID,POLICY_NAME,POLICY_FORM_ID,USER_ID,PARENT_ID,AUDIT_STATUS,SUBMIT_TIME,SETTLEMENT_START_TIME,OPERATIONAL_NOTES)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.policyId,jdbcType=VARCHAR},
            #{item.policyName,jdbcType=VARCHAR},
            #{item.policyFormId,jdbcType=VARCHAR},
            #{item.userId,jdbcType=INTEGER},
            #{item.parentId,jdbcType=INTEGER},
            #{item.auditStatus,jdbcType=TINYINT},
            #{item.submitTime,jdbcType=TIMESTAMP},
            #{item.settlementStartTime,jdbcType=INTEGER},
            #{item.operationalNotes,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>

            <!--查询报名代理商上级-->
            <select id="getParentIdByAgentIdAndPolicyId" resultMap="BaseResultMap">
                    SELECT
        parent_id
        FROM LM_CRM_CHANNEL_POLICY_FORM
        WHERE
        user_id = #{agentId,jdbcType=INTEGER}
        and policy_id = #{policyId,jdbcType=VARCHAR}
        and parent_id != 0
        and is_del = 0
        LIMIT 1
            </select>

            <!--查询报名代理商-->
            <select id="getByAgentIdAndPolicyId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM LM_CRM_CHANNEL_POLICY_FORM
        WHERE
        user_id = #{agentId,jdbcType=INTEGER}
        and policy_id = #{policyId,jdbcType=VARCHAR}
        and is_del = 0
        LIMIT 1
            </select>
    </mapper>
