<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsInstallmentDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsInstallmentDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CANCEL_REASON" property="cancelReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_ACCOUNT" property="orderAccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INSTALLMENT_ID" property="installmentId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FINISH_TYPE" property="finishType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_ACCOUNT_ID" property="orderAccountId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="INSTALLMENT_TYPE" property="installmentType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REPAYMENT_STATUS" property="repaymentStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GOODS_INSTALLMENT" property="goodsInstallment" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GOODS_TOTAL_NUMBER" property="goodsTotalNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="INSTALLMENT_END_DATE" property="installmentEndDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="INSTALLMENT_START_DATE" property="installmentStartDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REMAINING_INSTALLMENT" property="remainingInstallment" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_TIME" property="orderTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REMAINING_AMOUNT" property="remainingAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="OUTSTANDING_AMOUNT" property="outstandingAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORDER_NO`,`AGENT_NAME`,`GOODS_NAME`,`GOODS_SPU_ID`,`CANCEL_REASON`,`ORDER_ACCOUNT`,`INSTALLMENT_ID`,`IS_DEL`,`AGENT_ID`,`FINISH_TYPE`,`ORDER_ACCOUNT_ID`,`INSTALLMENT_TYPE`,`REPAYMENT_STATUS`,`GOODS_INSTALLMENT`,`GOODS_TOTAL_NUMBER`,`INSTALLMENT_END_DATE`,`INSTALLMENT_START_DATE`,`REMAINING_INSTALLMENT`,`ORDER_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`REMAINING_AMOUNT`,`OUTSTANDING_AMOUNT`
    </sql>


            <!--insert:HW_SHOP_GOODS_INSTALLMENT-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_GOODS_INSTALLMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="orderAccount != null">`ORDER_ACCOUNT`,</if>
            <if test="installmentId != null">`INSTALLMENT_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="orderAccountId != null">`ORDER_ACCOUNT_ID`,</if>
            <if test="repaymentStatus != null">`REPAYMENT_STATUS`,</if>
            <if test="goodsInstallment != null">`GOODS_INSTALLMENT`,</if>
            <if test="goodsTotalNumber != null">`GOODS_TOTAL_NUMBER`,</if>
            <if test="installmentEndDate != null">`INSTALLMENT_END_DATE`,</if>
            <if test="installmentStartDate != null">`INSTALLMENT_START_DATE`,</if>
            <if test="remainingInstallment != null">`REMAINING_INSTALLMENT`,</if>
            <if test="orderTime != null">`ORDER_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="remainingAmount != null">`REMAINING_AMOUNT`,</if>
            <if test="outstandingAmount != null">`OUTSTANDING_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="orderAccount != null">#{orderAccount,jdbcType=VARCHAR},</if>
            <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="orderAccountId != null">#{orderAccountId,jdbcType=INTEGER},</if>
            <if test="repaymentStatus != null">#{repaymentStatus,jdbcType=TINYINT},</if>
            <if test="goodsInstallment != null">#{goodsInstallment,jdbcType=INTEGER},</if>
            <if test="goodsTotalNumber != null">#{goodsTotalNumber,jdbcType=INTEGER},</if>
            <if test="installmentEndDate != null">#{installmentEndDate,jdbcType=INTEGER},</if>
            <if test="installmentStartDate != null">#{installmentStartDate,jdbcType=INTEGER},</if>
            <if test="remainingInstallment != null">#{remainingInstallment,jdbcType=INTEGER},</if>
            <if test="orderTime != null">#{orderTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="remainingAmount != null">#{remainingAmount,jdbcType=DECIMAL},</if>
            <if test="outstandingAmount != null">#{outstandingAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据条件查询分期列表 pageCount-->
            <select id="findInstallmentPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 hw_shop_goods_installment
        WHERE is_del = 0
        <if test="orderNo != null">
            AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderAccountId != null">
            AND order_account_id = #{orderAccountId,jdbcType=INTEGER}
        </if>
        <if test="orderAccount != null">
            AND order_account LIKE CONCAT (#{orderAccount,jdbcType=VARCHAR}, '%')
        </if>
        <if test="installmentIdList != null and installmentIdList.size() &gt; 0 ">
            AND installment_id IN
            <foreach collection="installmentIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="repaymentStatus != null">
            AND repayment_status = #{repaymentStatus,jdbcType=TINYINT}
        </if>
        <if test="installmentType != null">
            AND installment_type = #{installmentType,jdbcType=TINYINT}
        </if>
        <if test="finishType != null">
            AND finish_type = #{finishType,jdbcType=TINYINT}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="orderEndTime != null">
            AND order_time <![CDATA[<=]]> #{orderEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="installmentStartDate != null">
            AND installment_start_date <![CDATA[>=]]> #{installmentStartDate,jdbcType=INTEGER}
        </if>
        <if test="installmentEndDate != null">
            AND installment_end_date <![CDATA[<=]]> #{installmentEndDate,jdbcType=INTEGER}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
            </select>
            <!--根据条件查询分期列表 pageResult-->
            <select id="findInstallmentPageResult"  resultMap="BaseResultMap">
                    SELECT /*MS-HW-SHOP-GOODS-INSTALLMENT-FINDINSTALLMENTPAGE*/  <include refid="Base_Column_List" />
        FROM hw_shop_goods_installment
        WHERE is_del = 0
        <if test="orderNo != null">
            AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderAccountId != null">
            AND order_account_id = #{orderAccountId,jdbcType=INTEGER}
        </if>
        <if test="orderAccount != null">
            AND order_account LIKE CONCAT (#{orderAccount,jdbcType=VARCHAR}, '%')
        </if>
        <if test="installmentIdList != null and installmentIdList.size() &gt; 0 ">
            AND installment_id IN
            <foreach collection="installmentIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="repaymentStatus != null">
            AND repayment_status = #{repaymentStatus,jdbcType=TINYINT}
        </if>
        <if test="installmentType != null">
            AND installment_type = #{installmentType,jdbcType=TINYINT}
        </if>
        <if test="finishType != null">
            AND finish_type = #{finishType,jdbcType=TINYINT}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="orderEndTime != null">
            AND order_time <![CDATA[<=]]> #{orderEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="installmentStartDate != null">
            AND installment_start_date <![CDATA[>=]]> #{installmentStartDate,jdbcType=INTEGER}
        </if>
        <if test="installmentEndDate != null">
            AND installment_end_date <![CDATA[<=]]> #{installmentEndDate,jdbcType=INTEGER}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
            limit #{startRow},#{limit}
            </select>
    </mapper>
