<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentActivityDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentActivityDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="SPU_IDS" property="spuIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="END_TIME" property="endTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="START_TIME" property="startTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_NAME" property="activityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_IDS" property="equipmentIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SPU_IDS`,`END_TIME`,`START_TIME`,`ACTIVITY_NAME`,`EQUIPMENT_IDS`,`OPERATOR_NAME`,`IS_DEL`,`OPERATOR_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_ACTIVITY-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_ACTIVITY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="activityName != null">`ACTIVITY_NAME`,</if>
            <if test="equipmentIds != null">`EQUIPMENT_IDS`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=VARCHAR},</if>
            <if test="startTime != null">#{startTime,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="equipmentIds != null">#{equipmentIds,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取活动列表-->
            <select id="findActivityList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_EQUIPMENT_ACTIVITY
        WHERE IS_DEL = 0
        ORDER BY `ID` DESC
            </select>

    <!--根据活动名称列表获取活动列表-->
    <select id="findActivityByActivityNameList" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        HW_EQUIPMENT_ACTIVITY
        WHERE ACTIVITY_NAME IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        AND IS_DEL = 0
    </select>
    </mapper>
