<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmChannelPolicyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmChannelPolicyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="AGENT_TAG" property="agentTag" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_ID" property="policyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_NAME" property="policyName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_DESC" property="paymentDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_SAIL_TAG" property="agentSailTag" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_LIST_URL" property="importListUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_FILE_URL" property="policyFileUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_WHITE_LIST" property="agentWhiteList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_PROOF_URL" property="paymentProofUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_DESCRIPTION" property="policyDescription" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXCLUDE_POLICY_ID_LIST" property="excludePolicyIdList" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_CATEGORY" property="grantCategory" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_DIMENSION" property="userDimension" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_CATEGORY" property="marketCategory" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HAS_PAYMENT_PROOF" property="hasPaymentProof" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="END_DATE" property="endDate" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="START_DATE" property="startDate" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`AGENT_TAG`,`POLICY_ID`,`POLICY_NAME`,`PAYMENT_DESC`,`AGENT_SAIL_TAG`,`IMPORT_LIST_URL`,`POLICY_FILE_URL`,`AGENT_WHITE_LIST`,`PAYMENT_PROOF_URL`,`POLICY_DESCRIPTION`,`EXCLUDE_POLICY_ID_LIST`,`IS_DEL`,`IS_SHOW`,`GRANT_CATEGORY`,`USER_DIMENSION`,`MARKET_CATEGORY`,`HAS_PAYMENT_PROOF`,`END_DATE`,`START_DATE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_CHANNEL_POLICY-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_CHANNEL_POLICY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="agentTag != null">`AGENT_TAG`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="policyName != null">`POLICY_NAME`,</if>
            <if test="paymentDesc != null">`PAYMENT_DESC`,</if>
            <if test="agentSailTag != null">`AGENT_SAIL_TAG`,</if>
            <if test="importListUrl != null">`IMPORT_LIST_URL`,</if>
            <if test="policyFileUrl != null">`POLICY_FILE_URL`,</if>
            <if test="agentWhiteList != null">`AGENT_WHITE_LIST`,</if>
            <if test="paymentProofUrl != null">`PAYMENT_PROOF_URL`,</if>
            <if test="policyDescription != null">`POLICY_DESCRIPTION`,</if>
            <if test="excludePolicyIdList != null">`EXCLUDE_POLICY_ID_LIST`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="grantCategory != null">`GRANT_CATEGORY`,</if>
            <if test="userDimension != null">`USER_DIMENSION`,</if>
            <if test="marketCategory != null">`MARKET_CATEGORY`,</if>
            <if test="hasPaymentProof != null">`HAS_PAYMENT_PROOF`,</if>
            <if test="endDate != null">`END_DATE`,</if>
            <if test="startDate != null">`START_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="agentTag != null">#{agentTag,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="policyName != null">#{policyName,jdbcType=VARCHAR},</if>
            <if test="paymentDesc != null">#{paymentDesc,jdbcType=VARCHAR},</if>
            <if test="agentSailTag != null">#{agentSailTag,jdbcType=VARCHAR},</if>
            <if test="importListUrl != null">#{importListUrl,jdbcType=VARCHAR},</if>
            <if test="policyFileUrl != null">#{policyFileUrl,jdbcType=VARCHAR},</if>
            <if test="agentWhiteList != null">#{agentWhiteList,jdbcType=VARCHAR},</if>
            <if test="paymentProofUrl != null">#{paymentProofUrl,jdbcType=VARCHAR},</if>
            <if test="policyDescription != null">#{policyDescription,jdbcType=VARCHAR},</if>
            <if test="excludePolicyIdList != null">#{excludePolicyIdList,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="grantCategory != null">#{grantCategory,jdbcType=TINYINT},</if>
            <if test="userDimension != null">#{userDimension,jdbcType=TINYINT},</if>
            <if test="marketCategory != null">#{marketCategory,jdbcType=TINYINT},</if>
            <if test="hasPaymentProof != null">#{hasPaymentProof,jdbcType=TINYINT},</if>
            <if test="endDate != null">#{endDate,jdbcType=TIMESTAMP},</if>
            <if test="startDate != null">#{startDate,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据政策名称查询-->
            <select id="findByPolicyNameList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM LM_CRM_CHANNEL_POLICY
        WHERE is_del = 0
        AND `POLICY_NAME` IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
