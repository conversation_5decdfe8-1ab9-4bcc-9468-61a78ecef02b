<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="NOTE" property="note" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QR_CODE" property="qrCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTE_IMG" property="noteImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECIPIENT" property="recipient" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NOTE" property="operateNote" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRANSFER_PICTURES" property="transferPictures" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FINANCE_AUDIT_PICTURES" property="financeAuditPictures" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_REFUND" property="isRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ADDRESS_ID" property="addressId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HW_PAY_TIME" property="hwPayTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HW_PAY_TYPE" property="hwPayType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FINAL_SCORE" property="finalScore" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HW_PAY_STATUS" property="hwPayStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HW_ORDER_STATUS" property="hwOrderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ALL_MATERIAL" property="isAllMaterial" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_INSTALLMENT" property="isInstallment" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HW_CLIENT_ORDER_STATUS" property="hwClientOrderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BALANCE_PRICE" property="balancePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PAYMENT_PRICE" property="paymentPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BASE_EXPRESS_FEE" property="baseExpressFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FINAL_EXPRESS_FEE" property="finalExpressFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BASE_GOODS_SUMPRICE" property="baseGoodsSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BASE_ORDER_SUMPRICE" property="baseOrderSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FINAL_GOODS_SUMPRICE" property="finalGoodsSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FINAL_ORDER_SUMPRICE" property="finalOrderSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`NOTE`,`PHONE`,`QR_CODE`,`ADDRESS`,`NOTE_IMG`,`ORDER_SN`,`AREA_NAME`,`CITY_NAME`,`USERNAME`,`HW_ORDER_SN`,`RECIPIENT`,`OPERATE_NOTE`,`PROVINCE_NAME`,`TRANSFER_PICTURES`,`FINANCE_AUDIT_PICTURES`,`UID`,`IS_DEL`,`IS_TEST`,`USER_ID`,`STORE_ID`,`IS_REFUND`,`USER_TYPE`,`ADDRESS_ID`,`HW_PAY_TIME`,`HW_PAY_TYPE`,`FINAL_SCORE`,`HW_PAY_STATUS`,`HW_ORDER_STATUS`,`IS_ALL_MATERIAL`,`IS_INSTALLMENT`,`HW_CLIENT_ORDER_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`BALANCE_PRICE`,`PAYMENT_PRICE`,`BASE_EXPRESS_FEE`,`FINAL_EXPRESS_FEE`,`BASE_GOODS_SUMPRICE`,`BASE_ORDER_SUMPRICE`,`FINAL_GOODS_SUMPRICE`,`FINAL_ORDER_SUMPRICE`
    </sql>


            <!--insert:HW_SHOP_ORDER-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="operateNote != null">`OPERATE_NOTE`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="noteImg != null">`NOTE_IMG`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="areaName != null">`AREA_NAME`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="recipient != null">`RECIPIENT`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="transferPictures != null">`TRANSFER_PICTURES`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="hwPayTime != null">`HW_PAY_TIME`,</if>
            <if test="hwPayType != null">`HW_PAY_TYPE`,</if>
            <if test="hwOrderStatus != null">`HW_ORDER_STATUS`,</if>
            <if test="isInstallment != null">`IS_INSTALLMENT`,</if>
            <if test="hwClientOrderStatus != null">`HW_CLIENT_ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balancePrice != null">`BALANCE_PRICE`,</if>
            <if test="paymentPrice != null">`PAYMENT_PRICE`,</if>
            <if test="baseExpressFee != null">`BASE_EXPRESS_FEE`,</if>
            <if test="finalExpressFee != null">`FINAL_EXPRESS_FEE`,</if>
            <if test="baseGoodsSumprice != null">`BASE_GOODS_SUMPRICE`,</if>
            <if test="baseOrderSumprice != null">`BASE_ORDER_SUMPRICE`,</if>
            <if test="finalGoodsSumprice != null">`FINAL_GOODS_SUMPRICE`,</if>
            <if test="finalOrderSumprice != null">`FINAL_ORDER_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="operateNote != null">#{operateNote,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="noteImg != null">#{noteImg,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="areaName != null">#{areaName,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="recipient != null">#{recipient,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="transferPictures != null">#{transferPictures,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="hwPayTime != null">#{hwPayTime,jdbcType=INTEGER},</if>
            <if test="hwPayType != null">#{hwPayType,jdbcType=TINYINT},</if>
            <if test="hwOrderStatus != null">#{hwOrderStatus,jdbcType=TINYINT},</if>
            <if test="isInstallment != null">#{isInstallment,jdbcType=TINYINT},</if>
            <if test="hwClientOrderStatus != null">#{hwClientOrderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balancePrice != null">#{balancePrice,jdbcType=DECIMAL},</if>
            <if test="paymentPrice != null">#{paymentPrice,jdbcType=DECIMAL},</if>
            <if test="baseExpressFee != null">#{baseExpressFee,jdbcType=DECIMAL},</if>
            <if test="finalExpressFee != null">#{finalExpressFee,jdbcType=DECIMAL},</if>
            <if test="baseGoodsSumprice != null">#{baseGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="baseOrderSumprice != null">#{baseOrderSumprice,jdbcType=DECIMAL},</if>
            <if test="finalGoodsSumprice != null">#{finalGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="finalOrderSumprice != null">#{finalOrderSumprice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--分页查询硬件商品订单列表 pageCount-->
            <select id="findOrderPageListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM

        HW_SHOP_ORDER
        WHERE
        `IS_DEL` = 0
        <if test="hwOrderSn != null and hwOrderSn != '' ">
            AND `HW_ORDER_SN` = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="list != null and list.size() &gt; 0">
            AND `HW_ORDER_SN` IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="username != null and username != '' ">
            AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="isInstallment != null">
            AND `IS_INSTALLMENT` = #{isInstallment,jdbcType=TINYINT}
        </if>
        <if test="userType != null">
            <choose>
                <when test="userType == 1">
                    AND `USER_TYPE` in (1,2)
                </when>
                <otherwise>
                    AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
                </otherwise>
            </choose>
        </if>
        <if test="hwPayType != null">
            AND `HW_PAY_TYPE` = #{hwPayType,jdbcType=TINYINT}
        </if>
        <if test="hwOrderStatus != null">
            AND `HW_ORDER_STATUS` = #{hwOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="hwClientOrderStatus != null">
            AND `HW_CLIENT_ORDER_STATUS` = #{hwClientOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="startDate != null">
            AND `CREATE_TIME` <![CDATA[ >= ]]> #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            AND `CREATE_TIME` <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="isTest != null">
            AND `IS_TEST` = #{isTest, jdbcType=INTEGER}
        </if>
        
            </select>
            <!--分页查询硬件商品订单列表 pageResult-->
            <select id="findOrderPageListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.HwShopOrderDetailDTO">
                    SELECT
        CREATE_TIME as createTime,
        HW_ORDER_SN as hwOrderSn,
        USERNAME as username,
        USER_TYPE as userType,
        NOTE as note,
        OPERATE_NOTE as operateNote,
        NOTE_IMG as noteImg,
        IS_INSTALLMENT as isInstallment,
        FINAL_GOODS_SUMPRICE as finalGoodsSumprice,
        FINAL_EXPRESS_FEE as finalExpressFee,
        FINAL_ORDER_SUMPRICE as finalOrderSumprice,
        BALANCE_PRICE as balancePrice,
        PAYMENT_PRICE as paymentPrice,
        HW_PAY_TYPE as hwPayType,
        HW_ORDER_STATUS as hwOrderStatus,
        RECIPIENT as recipient,
        PHONE as phone,
        PROVINCE_NAME as provinceName,
        CITY_NAME as cityName,
        AREA_NAME as areaName,
        ADDRESS as address,
        ORDER_SN as orderSn,
        FINAL_SCORE as finalScore
        FROM
        HW_SHOP_ORDER
        WHERE
        `IS_DEL` = 0
        <if test="hwOrderSn != null and hwOrderSn != '' ">
            AND `HW_ORDER_SN` = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="list != null and list.size() &gt; 0">
            AND `HW_ORDER_SN` IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="username != null and username != '' ">
            AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="isInstallment != null">
            AND `IS_INSTALLMENT` = #{isInstallment,jdbcType=TINYINT}
        </if>
        <if test="userType != null">
            <choose>
                <when test="userType == 1">
                    AND `USER_TYPE` in (1,2)
                </when>
                <otherwise>
                    AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
                </otherwise>
            </choose>
        </if>
        <if test="hwPayType != null">
            AND `HW_PAY_TYPE` = #{hwPayType,jdbcType=TINYINT}
        </if>
        <if test="hwOrderStatus != null">
            AND `HW_ORDER_STATUS` = #{hwOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="hwClientOrderStatus != null">
            AND `HW_CLIENT_ORDER_STATUS` = #{hwClientOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="startDate != null">
            AND `CREATE_TIME` <![CDATA[ >= ]]> #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            AND `CREATE_TIME` <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="isTest != null">
            AND `IS_TEST` = #{isTest, jdbcType=INTEGER}
        </if>
        ORDER BY `ID` DESC
            limit #{startRow},#{limit}
            </select>

            <!--根据订单查询订单列表-->
            <select id="findOrderListByHwOrderSn" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER
        <where>
            <if test="list != null and list.size() &gt; 0 ">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
            </select>

            <!--查询订单完整信息-->
            <select id="getOrderFullInfo" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER
        WHERE
        `HW_ORDER_SN` = #{hwOrderSn,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
            </select>

            <!--根据snId查询订单信息-->
            <select id="getLastBySnId" resultMap="BaseResultMap">
                    SELECT /*MS-HW-SHOP-ORDER-GETLASTBYSNID*/  od.* FROM hw_shop_order od
        INNER JOIN  hw_shop_order_storage_record record on record.hw_order_sn = od.hw_order_sn
        INNER JOIN hw_equipment_order_relation rela on rela.order_no = record.storage_order
        WHERE rela.sn_id = #{snId,jdbcType=INTEGER}
        AND od.IS_DEL = 0
        order by od.id desc
        limit 1
            </select>
    </mapper>
