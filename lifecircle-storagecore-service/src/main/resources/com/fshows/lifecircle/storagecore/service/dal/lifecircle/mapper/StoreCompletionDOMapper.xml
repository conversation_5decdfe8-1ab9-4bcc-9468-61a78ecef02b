<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.StoreCompletionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.StoreCompletionDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PIC_MONEY" property="picMoney" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PIC_INSIDE" property="picInside" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_READ" property="isRead" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`ERROR_MSG`,`PIC_MONEY`,`PIC_INSIDE`,`UID`,`IS_READ`,`STORE_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_STORE_COMPLETION-->
            <insert id="insert" >
                    INSERT INTO TP_STORE_COMPLETION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="picMoney != null">`PIC_MONEY`,</if>
            <if test="picInside != null">`PIC_INSIDE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isRead != null">`IS_READ`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="picMoney != null">#{picMoney,jdbcType=VARCHAR},</if>
            <if test="picInside != null">#{picInside,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isRead != null">#{isRead,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据 store_id 查询记录-->
            <select id="getByStoreId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-STORE-COMPLETION-GETBYSTOREID*/  `pic_inside`,`pic_money`
        FROM TP_STORE_COMPLETION
        WHERE
        STORE_ID
        = #{storeId,jdbcType=INTEGER}
        order by create_time desc
        LIMIT 1
            </select>
    </mapper>
