<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwHarvestPlanAgentDayRewardNewDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwHarvestPlanAgentDayRewardNewDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="BIGINT"
                    javaType="Long"/>

            <result column="PT_DAY" property="ptDay" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="STATISTICS_DAY" property="statisticsDay" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="DAY_AMOUNT" property="dayAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="DAY_REWARD" property="dayReward" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`ACTIVITY_ID`,`PT_DAY`,`INIT_SN`,`UID`,`IS_DEL`,`BELONG`,`EQUIPMENT_ID`,`STATISTICS_DAY`,`CREATE_TIME`,`UPDATE_TIME`,`DAY_AMOUNT`,`DAY_REWARD`
        </sql>


        <!--insert:HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW-->
        <insert id="insert">
            INSERT INTO HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="ptDay != null">`PT_DAY`,</if>
                <if test="initSn != null">`INIT_SN`,</if>
                <if test="uid != null">`UID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="belong != null">`BELONG`,</if>
                <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
                <if test="statisticsDay != null">`STATISTICS_DAY`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="dayAmount != null">`DAY_AMOUNT`,</if>
            <if test="dayReward != null">`DAY_REWARD`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ptDay != null">#{ptDay,jdbcType=VARCHAR},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="statisticsDay != null">#{statisticsDay,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="dayAmount != null">#{dayAmount,jdbcType=DECIMAL},</if>
            <if test="dayReward != null">#{dayReward,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--分页查询代理商日佣金 pageCount-->
        <select id="findRewardListCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW t1
            left join
            hw_equipment t2
            on
            t1.equipment_id = t2.id
            where
            t1.statistics_day <![CDATA[>=]]> #{startDay,jdbcType=INTEGER}
            and t1.statistics_day <![CDATA[<=]]> #{endDay,jdbcType=INTEGER}
            <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
            <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
                and t1.belong in
                <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                    #{belong,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
                and t1.uid in
                <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="activityIdList != null and activityIdList.size() &gt; 0 ">
                AND t1.activity_id IN
                <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                    #{activityId,jdbcType=INTEGER}
                </foreach>
            </if>
            and t1.is_del = 0

        </select>
        <!--分页查询代理商日佣金 pageResult-->
        <select id="findRewardListResult" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.RewardDTO">
            select
            t1.statistics_day as statistics,
            t1.init_sn as equipmentSn,
            t2.equipment_model as equipmentModel,
            cast(t1.day_amount as char) as tradeAmount,
            cast(t1.day_reward as char) as awardAmount,
            t1.uid as merchantId,
            t1.belong as agentId
            from
            HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW t1
            left join
        hw_equipment t2
        on
        t1.equipment_id = t2.id
        where
        t1.statistics_day <![CDATA[>=]]> #{startDay,jdbcType=INTEGER}
        and t1.statistics_day <![CDATA[<=]]> #{endDay,jdbcType=INTEGER}
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
            <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
                and t1.belong in
                <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                    #{belong,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
                and t1.uid in
                <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="activityIdList != null and activityIdList.size() &gt; 0 ">
                AND t1.activity_id IN
                <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                    #{activityId,jdbcType=INTEGER}
                </foreach>
            </if>
            and t1.is_del = 0
            ORDER BY t1.`statistics_day` DESC , t1.`day_amount` DESC
            limit #{startRow},#{limit}
        </select>
    </mapper>
