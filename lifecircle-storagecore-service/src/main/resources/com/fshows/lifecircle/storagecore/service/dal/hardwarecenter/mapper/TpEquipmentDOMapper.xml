<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EQUIPMENT_FIRM" property="equipmentFirm" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EQUIPMENT_PREFIX" property="equipmentPrefix" jdbcType="CHAR"
                    javaType="String"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="OPERATOR" property="operator" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EQUIPMENT_TYPE" property="equipmentType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="EQUIPMENT_NAME_ID" property="equipmentNameId" jdbcType="INTEGER"
                    javaType="Integer"/>
        </resultMap>

        <resultMap id="EquipmentNameListMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentNameListMap">

            <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

            <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>

        <sql id="Base_Column_List">
            `ID`,`EQUIPMENT_FIRM`,`EQUIPMENT_PREFIX`,`SORT`,`IS_DEL`,`OPERATOR`,`PLATFORM`,`CREATE_TIME`,`UPDATE_TIME`,`EQUIPMENT_TYPE`,`EQUIPMENT_NAME_ID`
        </sql>


        <!--insert:TP_EQUIPMENT-->
        <insert id="insert">
            INSERT INTO TP_EQUIPMENT
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="equipmentFirm != null">`EQUIPMENT_FIRM`,</if>
                <if test="equipmentPrefix != null">`EQUIPMENT_PREFIX`,</if>
                <if test="sort != null">`SORT`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="operator != null">`OPERATOR`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="equipmentType != null">`EQUIPMENT_TYPE`,</if>
            <if test="equipmentNameId != null">`EQUIPMENT_NAME_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="equipmentFirm != null">#{equipmentFirm,jdbcType=VARCHAR},</if>
            <if test="equipmentPrefix != null">#{equipmentPrefix,jdbcType=CHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operator != null">#{operator,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="equipmentType != null">#{equipmentType,jdbcType=TINYINT},</if>
            <if test="equipmentNameId != null">#{equipmentNameId,jdbcType=INTEGER},</if>
        </trim>
        </insert>

        <!--查询所有设备名称列表-->
        <select id="getEquipmentNameList" resultMap="EquipmentNameListMap">
            select /*MS-TP-EQUIPMENT-GETEQUIPMENTNAMELIST*/ a.id
            equipment_id,a.equipment_firm,a.equipment_name_id,b.equipment_name
            from tp_equipment a
            LEFT JOIN tp_equipment_name b on b.id = a.equipment_name_id
        </select>
    </mapper>
