<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.SharePayBillDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.SharePayBillDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_ID" property="orderId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BILL_DATE" property="billDate" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_MODE" property="shareMode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_ROLE" property="shareRole" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_TYPE" property="shareType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_BILL_NO" property="shareBillNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_STATUS" property="shareStatus" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHANNEL_BILL_NO" property="channelBillNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_MEMBER_ID" property="shareMemberId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_PAY_SUB_NO" property="sharePaySubNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="WITHDRAW_STATUS" property="withdrawStatus" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_SERIAL_NUMBER" property="outSerialNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REQ_SERIAL_NUMBER" property="reqSerialNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="WITHDRAW_SERIAL_NUMBER" property="withdrawSerialNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="SETTLE_END_TIME" property="settleEndTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="SETTLE_START_TIME" property="settleStartTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="FEE_AMOUNT" property="feeAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="SHARE_PRICE" property="sharePrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="REAL_SHARE_PRICE" property="realSharePrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`TOKEN`,`REASON`,`ORDER_ID`,`REMARKS`,`BILL_DATE`,`SHARE_MODE`,`SHARE_ROLE`,`SHARE_TYPE`,`CUSTOMER_ID`,`OPERATOR_ID`,`SETTLE_TYPE`,`SHARE_BILL_NO`,`SHARE_STATUS`,`OPERATOR_NAME`,`CHANNEL_BILL_NO`,`SHARE_MEMBER_ID`,`SHARE_PAY_SUB_NO`,`WITHDRAW_STATUS`,`OUT_SERIAL_NUMBER`,`REQ_SERIAL_NUMBER`,`WITHDRAW_SERIAL_NUMBER`,`UID`,`CREATE_TIME`,`FINISH_TIME`,`UPDATE_TIME`,`SETTLE_END_TIME`,`SETTLE_START_TIME`,`FEE_AMOUNT`,`ORDER_PRICE`,`SHARE_PRICE`,`REAL_SHARE_PRICE`
        </sql>


        <!--insert:TP_SHARE_PAY_BILL-->
        <insert id="insert">
            INSERT INTO TP_SHARE_PAY_BILL
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="token != null">`TOKEN`,</if>
                <if test="reason != null">`REASON`,</if>
                <if test="orderId != null">`ORDER_ID`,</if>
                <if test="remarks != null">`REMARKS`,</if>
                <if test="billDate != null">`BILL_DATE`,</if>
                <if test="shareRole != null">`SHARE_ROLE`,</if>
                <if test="shareType != null">`SHARE_TYPE`,</if>
                <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="settleType != null">`SETTLE_TYPE`,</if>
            <if test="shareBillNo != null">`SHARE_BILL_NO`,</if>
            <if test="shareStatus != null">`SHARE_STATUS`,</if>
            <if test="channelBillNo != null">`CHANNEL_BILL_NO`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="sharePaySubNo != null">`SHARE_PAY_SUB_NO`,</if>
            <if test="withdrawStatus != null">`WITHDRAW_STATUS`,</if>
            <if test="reqSerialNumber != null">`REQ_SERIAL_NUMBER`,</if>
            <if test="withdrawSerialNumber != null">`WITHDRAW_SERIAL_NUMBER`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="settleEndTime != null">`SETTLE_END_TIME`,</if>
            <if test="settleStartTime != null">`SETTLE_START_TIME`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="realSharePrice != null">`REAL_SHARE_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderId != null">#{orderId,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=VARCHAR},</if>
            <if test="shareRole != null">#{shareRole,jdbcType=VARCHAR},</if>
            <if test="shareType != null">#{shareType,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="settleType != null">#{settleType,jdbcType=VARCHAR},</if>
            <if test="shareBillNo != null">#{shareBillNo,jdbcType=VARCHAR},</if>
            <if test="shareStatus != null">#{shareStatus,jdbcType=VARCHAR},</if>
            <if test="channelBillNo != null">#{channelBillNo,jdbcType=VARCHAR},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="sharePaySubNo != null">#{sharePaySubNo,jdbcType=VARCHAR},</if>
            <if test="withdrawStatus != null">#{withdrawStatus,jdbcType=VARCHAR},</if>
            <if test="reqSerialNumber != null">#{reqSerialNumber,jdbcType=VARCHAR},</if>
            <if test="withdrawSerialNumber != null">#{withdrawSerialNumber,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="settleEndTime != null">#{settleEndTime,jdbcType=TIMESTAMP},</if>
            <if test="settleStartTime != null">#{settleStartTime,jdbcType=TIMESTAMP},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="realSharePrice != null">#{realSharePrice,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据分账单号和分账状态查询分账账单-->
        <select id="getListBySharePaySubNoAndShareStatus" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from
            tp_share_pay_bill
            where
            share_pay_sub_no in
            <foreach close=")" collection="list" index="index" item="sharePaySubNo" open="(" separator=",">
                #{sharePaySubNo,jdbcType=VARCHAR}
            </foreach>
        </select>

        <!--根据分账单号更新分账状态-->
        <update id="updateShareStatusByShareBillNo">
            update
                tp_share_pay_bill
            set share_status = #{shareStatus,jdbcType=VARCHAR},
                finish_time  = #{finishTime,jdbcType=TIMESTAMP}
            where share_bill_no = #{shareBillNo,jdbcType=VARCHAR}
        </update>
    </mapper>
