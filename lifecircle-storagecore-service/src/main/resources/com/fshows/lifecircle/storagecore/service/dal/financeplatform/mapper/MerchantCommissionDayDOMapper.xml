<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.MerchantCommissionDayDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.MerchantCommissionDayDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_MANAGER" property="marketManager" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPER_SALESMAN_USERNAME" property="superSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PT_DAY" property="ptDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CPM_NUM" property="cpmNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_T1_SETTLE" property="isT1Settle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_HFIVE" property="exposureNumHfive" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NORMAL_TRADE_COUNT" property="normalTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_TRADE_COUNT" property="refundTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_APPLET" property="exposureNumApplet" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="GRANT_FEE" property="grantFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PAYABLE_COMMISSION" property="payableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DEDUCTION_COMMISSION" property="deductionCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="CommissionPayableDetailMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.resultmap.CommissionPayableDetailMap">

                <result column="account_name" property="accountName" javaType="java.lang.String"/>

                <result column="cpm_num" property="cpmNum" javaType="java.lang.Integer"/>

                <result column="account_id" property="accountId" javaType="java.lang.Integer"/>

                <result column="exposure_num_hfive" property="exposureNumHfive" javaType="java.lang.Integer"/>

                <result column="exposure_total_num" property="exposureTotalNum" javaType="java.lang.Integer"/>

                <result column="normal_trade_count" property="normalTradeCount" javaType="java.lang.Integer"/>

                <result column="exposure_num_applet" property="exposureNumApplet" javaType="java.lang.Integer"/>

                <result column="agent_fee" property="agentFee" javaType="java.math.BigDecimal"/>

                <result column="grant_fee" property="grantFee" javaType="java.math.BigDecimal"/>

                <result column="normal_trade_amount" property="normalTradeAmount" javaType="java.math.BigDecimal"/>

                <result column="payable_commission" property="payableCommission" javaType="java.math.BigDecimal"/>

                <result column="deduction_commission" property="deductionCommission" javaType="java.math.BigDecimal"/>

                <result column="actual_payable_commission" property="actualPayableCommission" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`SOURCE_TYPE`,`AGENT_USERNAME`,`MARKET_MANAGER`,`AGENT_COMPANYNAME`,`MERCHANT_USERNAME`,`SALESMAN_USERNAME`,`SUPER_SALESMAN_USERNAME`,`UID`,`PT_DAY`,`CPM_NUM`,`AGENT_ID`,`MARKET_ID`,`IS_T1_SETTLE`,`SALESMAN_ID`,`BUSINESS_DATE`,`SUPER_SALESMAN_ID`,`EXPOSURE_NUM_HFIVE`,`NORMAL_TRADE_COUNT`,`REFUND_TRADE_COUNT`,`EXPOSURE_NUM_APPLET`,`CREATE_TIME`,`UPDATE_TIME`,`GRANT_FEE`,`NORMAL_TRADE_AMOUNT`,`PAYABLE_COMMISSION`,`REFUND_TRADE_AMOUNT`,`DEDUCTION_COMMISSION`,`ACTUAL_PAYABLE_COMMISSION`
    </sql>


            <!--根据代理商id查询代理商月佣金列表-->
            <select id="getAgentCommissionList" resultMap="CommissionPayableDetailMap">
                select
                agent_id account_id,
                agent_username account_name,
                sum(normal_trade_count) normal_trade_count,
                sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
                sum(payable_commission) payable_commission,
                sum(deduction_commission) deduction_commission,
                sum(actual_payable_commission) actual_payable_commission,
                sum(exposure_num_hfive) exposure_num_hfive,
                sum(exposure_num_applet) exposure_num_applet,
                cpm_num,
                sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
                sum(grant_fee) grant_fee,
                sum(payable_commission) - sum(grant_fee) as agent_fee
                from fp_merchant_commission_day
                where agent_id = #{agentId,jdbcType=INTEGER}
                and business_date = #{businessDate,jdbcType=INTEGER}
                and fee_code = #{feeCode,jdbcType=VARCHAR}
                and salesman_id = -1 and super_salesman_id = -1
                and source_type in
                <foreach collection="list" item="sourceType" open="(" separator="," close=")">
                    #{sourceType,jdbcType=VARCHAR}
                </foreach>
                group by agent_id
                order by create_time desc
            </select>

    <!--根据代理商id查询授理商月佣金列表-->
    <select id="getSalesmanCommissionList" resultMap="CommissionPayableDetailMap">
        select
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
    </select>

    <!--根据代理商id查询超级授理商月佣金列表-->
    <select id="getSuperSalesmanCommissionList" resultMap="CommissionPayableDetailMap">
        select
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
    </select>

            <!--根据代理商id查询商户日佣金列表 pageCount-->
            <select id="getMerchantCommissionListPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        
            </select>
            <!--根据代理商id查询商户日佣金列表 pageResult-->
            <select id="getMerchantCommissionListPageResult"  resultMap="BaseResultMap">
                    select /*MS-FP-MERCHANT-COMMISSION-DAY-GETMERCHANTCOMMISSIONLISTPAGE*/ <include refid="Base_Column_List" /> from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
            limit #{startRow},#{limit}
            </select>

            <!--超级商户佣金列表查询-->
            <select id="getSuperMerchantCommissionList" resultMap="BaseResultMap">
                    select /*MS-FP-MERCHANT-COMMISSION-DAY-GETSUPERMERCHANTCOMMISSIONLIST*/ <include refid="Base_Column_List" /> from fp_merchant_commission_day
        where fee_code = 712
        <if test="agentId != null and agentId != ''">
            and agent_id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="salesmanId != null and salesmanId != ''">
            and (salesman_id = #{salesmanId,jdbcType=INTEGER} or super_salesman_id = #{salesmanId,jdbcType=INTEGER})
        </if>
        <if test="merchantId != null and merchantId != ''">
            and uid = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="merchantName != null and merchantName != ''">
            and merchant_username = #{merchantName,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime != ''">
            and business_date &gt;= #{startTime,jdbcType=INTEGER}
        </if>
        <if test="endTime != null and endTime != ''">
            and business_date &lt;= #{endTime,jdbcType=INTEGER}
        </if>
        order by create_time desc
            </select>

            <!--根据代理商id查询商户月佣金列表-->
            <select id="getMerchantCommissionList" resultMap="BaseResultMap">
                    select /*MS-FP-MERCHANT-COMMISSION-DAY-GETMERCHANTCOMMISSIONLIST*/ <include refid="Base_Column_List" /> from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type = #{sourceType,jdbcType=VARCHAR}
        order by create_time desc
            </select>
    </mapper>
