<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CrmMerchantTransferBatchRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CrmMerchantTransferBatchRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`USER_ID`,`ADDRESS`,`BATCH_NO`,`COMPANY`,`REMARKS`,`CREATE_BY`,`UPDATE_BY`,`USERNAME`,`MERCHANT_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD-->
            <insert id="insert" >
                    INSERT INTO TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>
    </mapper>
