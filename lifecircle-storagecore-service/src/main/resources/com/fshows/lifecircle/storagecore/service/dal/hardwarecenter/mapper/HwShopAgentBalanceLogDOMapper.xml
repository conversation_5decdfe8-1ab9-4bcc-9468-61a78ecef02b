<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopAgentBalanceLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopAgentBalanceLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXTEND_INFO" property="extendInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ACCOUNT" property="agentAccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_LOG_ID" property="operateLogId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELATION_ORDER_NO" property="relationOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_MATCH" property="isMatch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BALANCE" property="balance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_AMOUNT" property="changeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OPERATOR`,`JOB_NUMBER`,`EXTEND_INFO`,`AGENT_ACCOUNT`,`OPERATE_LOG_ID`,`OPERATOR_NAME`,`RELATION_ORDER_NO`,`IS_DEL`,`IS_TEST`,`AGENT_ID`,`IS_MATCH`,`OPERATE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`BALANCE`,`CHANGE_AMOUNT`
    </sql>


            <!--insert:HW_SHOP_AGENT_BALANCE_LOG-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_AGENT_BALANCE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="extendInfo != null">`EXTEND_INFO`,</if>
            <if test="agentAccount != null">`AGENT_ACCOUNT`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isMatch != null">`IS_MATCH`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="operateLogId != null">`OPERATE_LOG_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="changeAmount != null">`CHANGE_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="extendInfo != null">#{extendInfo,jdbcType=VARCHAR},</if>
            <if test="agentAccount != null">#{agentAccount,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isMatch != null">#{isMatch,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="operateLogId != null">#{operateLogId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="changeAmount != null">#{changeAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--分页查询设备返佣金额关联关系列表 pageCount-->
            <select id="findBalancePageByBelongCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
        `hw_shop_agent_balance_log`
        WHERE
        `agent_id` = #{belong, jdbcType=INTEGER}
        
            </select>
            <!--分页查询设备返佣金额关联关系列表 pageResult-->
            <select id="findBalancePageByBelongResult"  resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        `hw_shop_agent_balance_log`
        WHERE
        `agent_id` = #{belong, jdbcType=INTEGER}
        ORDER BY
        `update_time` DESC
            limit #{startRow},#{limit}
            </select>

            <!--分页查询余额列表 pageCount-->
            <select id="findBalanceListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 `hw_shop_agent_balance_log`
        where is_del = 0
        <if test="agentAccount != null and agentAccount != '' ">
            AND agent_account LIKE CONCAT (#{agentAccount,jdbcType=VARCHAR},'%')
        </if>
        <if test="operatorName != null and operatorName != '' ">
            AND operator_name LIKE CONCAT (#{operatorName,jdbcType=VARCHAR},'%')
        </if>
                <if test="operateType != null and operateType != 0 ">
                    AND operate_type = #{operateType,jdbcType=INTEGER}
                </if>
                <if test="startTime != null">
                    AND `update_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    AND `update_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="isTest != null ">
                    AND is_test = #{isTest,jdbcType=TINYINT}
                </if>

            </select>
            <!--分页查询余额列表 pageResult-->
            <select id="findBalanceListResult"  resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-AGENT-BALANCE-LOG-FINDBALANCELIST*/ <include refid="Base_Column_List" /> from `hw_shop_agent_balance_log`
        where is_del = 0
        <if test="agentAccount != null and agentAccount != '' ">
            AND agent_account LIKE CONCAT (#{agentAccount,jdbcType=VARCHAR},'%')
        </if>
        <if test="operatorName != null and operatorName != '' ">
            AND operator_name LIKE CONCAT (#{operatorName,jdbcType=VARCHAR},'%')
        </if>
                <if test="operateType != null and operateType != 0 ">
                    AND operate_type = #{operateType,jdbcType=INTEGER}
                </if>
                <if test="startTime != null">
                    AND `update_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
                </if>
                <if test="endTime != null">
                    AND `update_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
                </if>
                <if test="isTest != null ">
                    AND is_test = #{isTest,jdbcType=TINYINT}
                </if>
                ORDER BY `update_time`DESC,id DESC
                limit #{startRow},#{limit}
            </select>

            <!--查询余额详情-->
            <select id="getBalanceByOperateLogId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        `hw_shop_agent_balance_log`
        WHERE
        `operate_log_id` = #{operateLogId, jdbcType=VARCHAR}
        and is_del = 0
        limit 1
            </select>

            <!--修改余额流水中代理商信息-->
            <update id="updateAgentInfoByOperateLogId" >
                    UPDATE /*MS-HW-SHOP-AGENT-BALANCE-LOG-UPDATEAGENTINFOBYOPERATELOGID*/ `hw_shop_agent_balance_log`
        <set>
            <if test="agentId != null">
                agent_id = #{agentId, jdbcType=INTEGER},
            </if>
            <if test="agentAccount != null">
                agent_account = #{agentAccount,jdbcType=VARCHAR},
            </if>
            update_time = now(),
            is_match = 0
        </set>
        WHERE
        `operate_log_id` = #{operateLogId, jdbcType=VARCHAR}
        and is_del = 0
            </update>
    </mapper>
