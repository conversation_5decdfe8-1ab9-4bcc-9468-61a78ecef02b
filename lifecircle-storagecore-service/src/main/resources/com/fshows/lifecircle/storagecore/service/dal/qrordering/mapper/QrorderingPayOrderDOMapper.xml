<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingPayOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingPayOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT7" property="ext7" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT8" property="ext8" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT9" property="ext9" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT10" property="ext10" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PREPAY_ID" property="prepayId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_STORE_ID" property="outStoreId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CALL_BACK_URL" property="callBackUrl" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DISH_ORDER_NO" property="dishOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MODE" property="mode" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PAY_STATUS" property="payStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ACTIVITY_FLAG" property="activityFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="RATE_FEE" property="rateFee" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="DISCOUNT" property="discount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="RED_PACKETS" property="redPackets" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="COUPON_MONEY" property="couponMoney" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORDER_SUMPRICE" property="orderSumprice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="PLATFORM_DISCOUNT" property="platformDiscount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`EXT7`,`EXT8`,`EXT9`,`EXT10`,`STORE_ID`,`PREPAY_ID`,`CUSTOMER_ID`,`OUT_STORE_ID`,`PAY_ORDER_NO`,`CALL_BACK_URL`,`DISH_ORDER_NO`,`MODE`,`TYPE`,`DEL_FLAG`,`PAY_TIME`,`PAY_TYPE`,`PAY_STATUS`,`ACTIVITY_FLAG`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`RATE_FEE`,`DISCOUNT`,`ORDER_PRICE`,`RED_PACKETS`,`COUPON_MONEY`,`ORDER_SUMPRICE`,`PLATFORM_DISCOUNT`
    </sql>


        <!--insert:TP_QRORDERING_PAY_ORDER-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_PAY_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="ext1 != null">`EXT1`,</if>
                <if test="ext2 != null">`EXT2`,</if>
                <if test="ext3 != null">`EXT3`,</if>
                <if test="ext4 != null">`EXT4`,</if>
                <if test="ext5 != null">`EXT5`,</if>
                <if test="ext6 != null">`EXT6`,</if>
                <if test="ext7 != null">`EXT7`,</if>
                <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="prepayId != null">`PREPAY_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="outStoreId != null">`OUT_STORE_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="callBackUrl != null">`CALL_BACK_URL`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="mode != null">`MODE`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="activityFlag != null">`ACTIVITY_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="rateFee != null">`RATE_FEE`,</if>
            <if test="discount != null">`DISCOUNT`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="redPackets != null">`RED_PACKETS`,</if>
            <if test="couponMoney != null">`COUPON_MONEY`,</if>
            <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
            <if test="platformDiscount != null">`PLATFORM_DISCOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="prepayId != null">#{prepayId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="outStoreId != null">#{outStoreId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="callBackUrl != null">#{callBackUrl,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="mode != null">#{mode,jdbcType=TINYINT},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="activityFlag != null">#{activityFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
            <if test="discount != null">#{discount,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="redPackets != null">#{redPackets,jdbcType=DECIMAL},</if>
            <if test="couponMoney != null">#{couponMoney,jdbcType=DECIMAL},</if>
            <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
            <if test="platformDiscount != null">#{platformDiscount,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据订单号列表批量查询订单详情-->
        <select id="listByPayOrderNoList" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            tp_qrordering_pay_order
            WHERE
            pay_order_no in
            <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
                #{payOrderNo,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
