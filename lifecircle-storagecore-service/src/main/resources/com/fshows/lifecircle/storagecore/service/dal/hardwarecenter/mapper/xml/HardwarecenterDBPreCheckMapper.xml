<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HardwarecenterDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 9 as fg,'TP_EQUIPMENT_SN_CHANGE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SN_ID,OP_TYPE,AGENT_ID,STORE_ID,STATE_DESC,CREATE_TIME,HANDLE_TYPE,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_SN_CHANGE_LOG'
            AND COLUMN_NAME in('ID','SN_ID','OP_TYPE','AGENT_ID','STORE_ID','STATE_DESC','CREATE_TIME','HANDLE_TYPE','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 5 as fg,'HW_EQUIPMENT_TRANSFER_BLACK_LIST' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,AGENT_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_TRANSFER_BLACK_LIST'
            AND COLUMN_NAME in('ID','INIT_SN','AGENT_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 7 as fg,'HW_EQUIPMENT_WORK_BATCH' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OPERATOR,WORK_BATCH_NO,IMPORT_FILE_URL,OPERATOR_NUMBER,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_WORK_BATCH'
            AND COLUMN_NAME in('ID','OPERATOR','WORK_BATCH_NO','IMPORT_FILE_URL','OPERATOR_NUMBER','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 16 as fg,'HW_FLOW_CARD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ICCID,INIT_SN,SIM_CARD,OPERATOR,IS_DEL,CARD_TYPE,TOTAL_FLOW,BEYOND_FLOW,CARD_STATUS,LAST_QUERY_DAY,RESIDUAL_FLOW,EFFECTIVE_TIME,EXPIRATION_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_FLOW_CARD'
            AND COLUMN_NAME in('ID','ICCID','INIT_SN','SIM_CARD','OPERATOR','IS_DEL','CARD_TYPE','TOTAL_FLOW','BEYOND_FLOW','CARD_STATUS','LAST_QUERY_DAY','RESIDUAL_FLOW','EFFECTIVE_TIME','EXPIRATION_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'HW_EQUIPMENT_RELATE_AGENT_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,CREATER,OEM_AFTER,JOB_NUMBER,OEM_BEFORE,EQUIPMENT_SN,BIZ_TYPE,NEW_AGENT_ID,OLD_AGENT_ID,RELATE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_RELATE_AGENT_LOG'
            AND COLUMN_NAME in('ID','REMARK','CREATER','OEM_AFTER','JOB_NUMBER','OEM_BEFORE','EQUIPMENT_SN','BIZ_TYPE','NEW_AGENT_ID','OLD_AGENT_ID','RELATE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,SALESMAN,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL'
            AND COLUMN_NAME in('ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','SALESMAN','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 26 as fg,'HW_EQUIPMENT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OEM_NAME,OPERATOR,JOB_NUMBER,SUPPLIER_ID,EQUIPMENT_PIC,EQUIPMENT_FIRM,EQUIPMENT_NAME,EQUIPMENT_MODEL,EQUIPMENT_PREFIX,ACCESS_ORGANIZATION,EQUIPMENT_INTRODUCE,OEM_ID,TP_KEY,APP_SHOW,PLATFORM,DETAIL_FLAG,DEVICE_TYPE,RECEIPT_TOOL,TRADE_CHANNEL,EQUIPMENT_TYPE,OTHER_PLATFORM,IS_UNION_AUTHENTICATION,CREATE_TIME,UPDATE_TIME,EQUIPMENT_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT'
            AND COLUMN_NAME in('ID','OEM_NAME','OPERATOR','JOB_NUMBER','SUPPLIER_ID','EQUIPMENT_PIC','EQUIPMENT_FIRM','EQUIPMENT_NAME','EQUIPMENT_MODEL','EQUIPMENT_PREFIX','ACCESS_ORGANIZATION','EQUIPMENT_INTRODUCE','OEM_ID','TP_KEY','APP_SHOW','PLATFORM','DETAIL_FLAG','DEVICE_TYPE','RECEIPT_TOOL','TRADE_CHANNEL','EQUIPMENT_TYPE','OTHER_PLATFORM','IS_UNION_AUTHENTICATION','CREATE_TIME','UPDATE_TIME','EQUIPMENT_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'HW_SHOP_AGENT_BALANCE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OPERATOR,JOB_NUMBER,EXTEND_INFO,AGENT_ACCOUNT,OPERATE_LOG_ID,OPERATOR_NAME,RELATION_ORDER_NO,IS_DEL,IS_TEST,AGENT_ID,IS_MATCH,OPERATE_TYPE,CREATE_TIME,UPDATE_TIME,BALANCE,CHANGE_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_AGENT_BALANCE_LOG'
            AND COLUMN_NAME in('ID','OPERATOR','JOB_NUMBER','EXTEND_INFO','AGENT_ACCOUNT','OPERATE_LOG_ID','OPERATOR_NAME','RELATION_ORDER_NO','IS_DEL','IS_TEST','AGENT_ID','IS_MATCH','OPERATE_TYPE','CREATE_TIME','UPDATE_TIME','BALANCE','CHANGE_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'HW_HARVEST_PLAN_AGENT_DAY_REWARD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT,DAY_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_DAY_REWARD'
            AND COLUMN_NAME in('ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT','DAY_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'HW_SHOP_ORDER_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_NAME,HW_ORDER_SN,GOODS_SKU_ID,GOODS_SPU_ID,IS_DEL,SCORE,IS_TEST,IS_GROUP,GOODS_TYPE,EQUIPMENT_ID,GOODS_NUMBER,GROUP_NUMBER,CAN_JOIN_ACTIVITY_NUMBER,HAD_JOIN_ACTIVITY_NUMBER,CREATE_TIME,UPDATE_TIME,BASE_PRICE,FINAL_PRICE,BASE_SUMPRICE,FINAL_SUMPRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_ORDER_DETAIL'
            AND COLUMN_NAME in('ID','GOODS_NAME','HW_ORDER_SN','GOODS_SKU_ID','GOODS_SPU_ID','IS_DEL','SCORE','IS_TEST','IS_GROUP','GOODS_TYPE','EQUIPMENT_ID','GOODS_NUMBER','GROUP_NUMBER','CAN_JOIN_ACTIVITY_NUMBER','HAD_JOIN_ACTIVITY_NUMBER','CREATE_TIME','UPDATE_TIME','BASE_PRICE','FINAL_PRICE','BASE_SUMPRICE','FINAL_SUMPRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 5 as fg,'TP_EQUIPMENT_NAME' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_NAME,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_NAME'
            AND COLUMN_NAME in('ID','EQUIPMENT_NAME','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_ALIPAY_TOUCH_EQUIP_ACTIVITY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,HW_ORDER_SN,EQUIPMENT_MODEL,IS_DEL,AGENT_ID,SALESMAN_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_ALIPAY_TOUCH_EQUIP_ACTIVITY'
            AND COLUMN_NAME in('ID','INIT_SN','HW_ORDER_SN','EQUIPMENT_MODEL','IS_DEL','AGENT_ID','SALESMAN_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_EQUIPMENT_MAINTENANCE_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,RIGHT_ID,OLD_INIT_SN,ORGIN_INIT_SN,DONE,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_MAINTENANCE_RECORD'
            AND COLUMN_NAME in('ID','INIT_SN','RIGHT_ID','OLD_INIT_SN','ORGIN_INIT_SN','DONE','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'HW_SCAN_HAND_EQUIPMENT_BIND_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,COMPANY,OPERATOR,USERNAME,AGENT_USERNAME,SALESMAN_USERNAME,UID,IS_DEL,AGENT_ID,SALESMAN_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SCAN_HAND_EQUIPMENT_BIND_RELATION'
            AND COLUMN_NAME in('ID','INIT_SN','COMPANY','OPERATOR','USERNAME','AGENT_USERNAME','SALESMAN_USERNAME','UID','IS_DEL','AGENT_ID','SALESMAN_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 103 as fg,'TP_USERS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INDIRECT,SALESPERCENT,DISTRIBUTOR_SALESPERCENT,MP,QQ,AREA,CITY,EMAIL,PHONE,LASTIP,MOBILE,PEOPLE,QRCODE,STATUS,ADDRESS,COMPANY,CONTACT,STORE_ID,BANK_NAME,BANK_USER,CREATEIP,LASTTIME,PASSWORD,PLUGSAVE,PROVINCE,REAL_NAME,USERNAME,ALIPAYNUM,BANK_ACOUNT,INDUSTRY_ID,USERS_TOKEN,FUND_PASSWORD,COMPANYADDRESS,USERS_HEADERPIC,PROTOCOL_VERSION,FUND_PASSWORD_SALT,STORE_DEFAULT_LOGO,GID,FOCUS,MONEY,AMOUNT,BELONG,DIYNUM,ISPUSH,ROLE_ID,VENDOR,API_USER,CARD_NUM,IS_APPLY,IS_JDPAY,UNION_ID,VIPTIME,IS_ALIPAY,MARKET_ID,PARENT_ID,PAY_LIMIT,PLATFORM,SALESMAN,WX_STATUS,APPLY_TIME,IS_CONFIRM,VERSION_ID,CONFIG_TYPE,CONNECTNUM,CREATETIME,DEALAMOUNT,IS_GROUP_BUY,IS_OPEN_MINA,IS_PROTOCOL,LOAN_STATUS,ONLINETIME,PROTOCOL_ID,SALER_AUDIT,VOICE_ON_OFF,ACTIVITYNUM,CASHOUT_LOCK,CONFIRM_TIME,IS_QUICK_CASH,MCARD_STATUS,SUB_CONFIG_ID,TOTALSMSNUM,INCOME_STATUS,MERCHANT_TYPE,PROTOCOL_TIME,PS_MODIFY_TIME,TOTALSMSUSED,TRANSFER_TIME,IS_SCAN_SERVICE,ISEMSCNPLPUSH,PW_RESET_STATUS,RECHARGE_LIMIT,WECHAT_CARD_NUM,ATTACHMENTSIZE,AUTO_WITHDRAWAL,LASTLOGINMONTH,LIFECIRCLE_TIME,LIQUIDATION_TYPE,CARD_CREATE_STATUS,LATESTONLINETIME,IS_SERVICENO_ACCESS,UPDATE_TIME,FINANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_USERS'
            AND COLUMN_NAME in('ID','INDIRECT','SALESPERCENT','DISTRIBUTOR_SALESPERCENT','MP','QQ','AREA','CITY','EMAIL','PHONE','LASTIP','MOBILE','PEOPLE','QRCODE','STATUS','ADDRESS','COMPANY','CONTACT','STORE_ID','BANK_NAME','BANK_USER','CREATEIP','LASTTIME','PASSWORD','PLUGSAVE','PROVINCE','REAL_NAME','USERNAME','ALIPAYNUM','BANK_ACOUNT','INDUSTRY_ID','USERS_TOKEN','FUND_PASSWORD','COMPANYADDRESS','USERS_HEADERPIC','PROTOCOL_VERSION','FUND_PASSWORD_SALT','STORE_DEFAULT_LOGO','GID','FOCUS','MONEY','AMOUNT','BELONG','DIYNUM','ISPUSH','ROLE_ID','VENDOR','API_USER','CARD_NUM','IS_APPLY','IS_JDPAY','UNION_ID','VIPTIME','IS_ALIPAY','MARKET_ID','PARENT_ID','PAY_LIMIT','PLATFORM','SALESMAN','WX_STATUS','APPLY_TIME','IS_CONFIRM','VERSION_ID','CONFIG_TYPE','CONNECTNUM','CREATETIME','DEALAMOUNT','IS_GROUP_BUY','IS_OPEN_MINA','IS_PROTOCOL','LOAN_STATUS','ONLINETIME','PROTOCOL_ID','SALER_AUDIT','VOICE_ON_OFF','ACTIVITYNUM','CASHOUT_LOCK','CONFIRM_TIME','IS_QUICK_CASH','MCARD_STATUS','SUB_CONFIG_ID','TOTALSMSNUM','INCOME_STATUS','MERCHANT_TYPE','PROTOCOL_TIME','PS_MODIFY_TIME','TOTALSMSUSED','TRANSFER_TIME','IS_SCAN_SERVICE','ISEMSCNPLPUSH','PW_RESET_STATUS','RECHARGE_LIMIT','WECHAT_CARD_NUM','ATTACHMENTSIZE','AUTO_WITHDRAWAL','LASTLOGINMONTH','LIFECIRCLE_TIME','LIQUIDATION_TYPE','CARD_CREATE_STATUS','LATESTONLINETIME','IS_SERVICENO_ACCESS','UPDATE_TIME','FINANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'HW_SHOP_REFUND_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REAL_NAME,USERNAME,HW_ORDER_SN,JOB_NUMBER,HW_REFUND_ORDER_SN,IS_DEL,IS_TEST,USER_ID,USER_TYPE,REFUND_TYPE,REFUND_SCORE,REFUND_NUMBER,IS_ALL_MATERIAL,REFUND_PAY_TIME,REFUND_ORDER_STATUS,CREATE_TIME,UPDATE_TIME,FINAL_GOODS_SUMPRICE,REFUND_BALANCE_PRICE,REFUND_PAYMENT_PRICE,REFUND_GOODS_SUMPRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_REFUND_ORDER'
            AND COLUMN_NAME in('ID','REAL_NAME','USERNAME','HW_ORDER_SN','JOB_NUMBER','HW_REFUND_ORDER_SN','IS_DEL','IS_TEST','USER_ID','USER_TYPE','REFUND_TYPE','REFUND_SCORE','REFUND_NUMBER','IS_ALL_MATERIAL','REFUND_PAY_TIME','REFUND_ORDER_STATUS','CREATE_TIME','UPDATE_TIME','FINAL_GOODS_SUMPRICE','REFUND_BALANCE_PRICE','REFUND_PAYMENT_PRICE','REFUND_GOODS_SUMPRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'HW_EQUIPMENT_ORDER_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORDER_NO,STOCK_ORDER,SN_ID,DEPOT,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_ORDER_RELATION'
            AND COLUMN_NAME in('ID','ORDER_NO','STOCK_ORDER','SN_ID','DEPOT','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'HW_TRAFFIC_CARD_DETAIL_INFO' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ICCID,MSISDN,REMARK,SP_CODE,CARRIER,SIM_TYPE,BIND_INIT_SN,EXPIRY_DATE,ACCOUNT_STATUS,ACTIVE,AGENT_ID,BIND_UID,DATA_WARN,DELETE_WARN,EXPIRY_WARN,BIND_AGENT_ID,CREATE_TIME,UPDATE_TIME,DATA_PLAN,DATA_USAGE,DATA_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_TRAFFIC_CARD_DETAIL_INFO'
            AND COLUMN_NAME in('ID','ICCID','MSISDN','REMARK','SP_CODE','CARRIER','SIM_TYPE','BIND_INIT_SN','EXPIRY_DATE','ACCOUNT_STATUS','ACTIVE','AGENT_ID','BIND_UID','DATA_WARN','DELETE_WARN','EXPIRY_WARN','BIND_AGENT_ID','CREATE_TIME','UPDATE_TIME','DATA_PLAN','DATA_USAGE','DATA_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'TP_EQUIPMENT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_FIRM,EQUIPMENT_PREFIX,SORT,IS_DEL,OPERATOR,PLATFORM,CREATE_TIME,UPDATE_TIME,EQUIPMENT_TYPE,EQUIPMENT_NAME_ID' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT'
            AND COLUMN_NAME in('ID','EQUIPMENT_FIRM','EQUIPMENT_PREFIX','SORT','IS_DEL','OPERATOR','PLATFORM','CREATE_TIME','UPDATE_TIME','EQUIPMENT_TYPE','EQUIPMENT_NAME_ID')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CREATE_BY,UPDATE_BY,EQUIPMENT_SN,AGENT_ID,CODE_SCAN_NUM,FACE_SCAN_DAU,EQUIPMENT_DAU,TRANSACTION_NUM,CREATE_TIME,UPDATE_TIME,TRANSACTION_TIME,TRANSACTION_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL'
            AND COLUMN_NAME in('ID','CREATE_BY','UPDATE_BY','EQUIPMENT_SN','AGENT_ID','CODE_SCAN_NUM','FACE_SCAN_DAU','EQUIPMENT_DAU','TRANSACTION_NUM','CREATE_TIME','UPDATE_TIME','TRANSACTION_TIME','TRANSACTION_MONEY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'HW_EQUIPMENT_ACTIVITY_SN_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,IS_DEL,BUYER_ID,BUYER_TYPE,ACTIVITY_ID,EQUIPMENT_ID,IS_DAY_STANDARD,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_ACTIVITY_SN_RELATION'
            AND COLUMN_NAME in('ID','INIT_SN','IS_DEL','BUYER_ID','BUYER_TYPE','ACTIVITY_ID','EQUIPMENT_ID','IS_DAY_STANDARD','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_HARVEST_PLAN_AGENT_BLACK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,JOB_NUMBER,OPERATOR_NAME,AGENT_ID,IS_CLOSE,ACTIVITY_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_BLACK'
            AND COLUMN_NAME in('ID','REMARK','JOB_NUMBER','OPERATOR_NAME','AGENT_ID','IS_CLOSE','ACTIVITY_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_GOODS_CATEGORY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,CATEGORY_NAME,PID,SORT,IS_DEL,CATEGORY_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_GOODS_CATEGORY'
            AND COLUMN_NAME in('ID','REMARK','CATEGORY_NAME','PID','SORT','IS_DEL','CATEGORY_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'HW_SHOP_STOCK_COST_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_MODEL,IS_DEL,IS_TEST,EQUIPMENT_ID,STOCK_COST_DAY,END_TERM_NUMBER,FIRST_TERM_NUMBER,OUT_STORAGE_NUMBER,PUT_STORAGE_NUMBER,CREATE_TIME,UPDATE_TIME,COST_ADJUST,END_TERM_PRICE,FIRST_TERM_PRICE,OUT_STORAGE_PRICE,PUT_STORAGE_PRICE,END_TERM_UNIT_PRICE,FIRST_TERM_UNIT_PRICE,OUT_STORAGE_UNIT_PRICE,PUT_STORAGE_UNIT_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_STOCK_COST_ORDER'
            AND COLUMN_NAME in('ID','EQUIPMENT_MODEL','IS_DEL','IS_TEST','EQUIPMENT_ID','STOCK_COST_DAY','END_TERM_NUMBER','FIRST_TERM_NUMBER','OUT_STORAGE_NUMBER','PUT_STORAGE_NUMBER','CREATE_TIME','UPDATE_TIME','COST_ADJUST','END_TERM_PRICE','FIRST_TERM_PRICE','OUT_STORAGE_PRICE','PUT_STORAGE_PRICE','END_TERM_UNIT_PRICE','FIRST_TERM_UNIT_PRICE','OUT_STORAGE_UNIT_PRICE','PUT_STORAGE_UNIT_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 5 as fg,'HW_EQUIPMENT_PERIODS_STATISTICS_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_SN,PERIODS_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_PERIODS_STATISTICS_RECORD'
            AND COLUMN_NAME in('ID','EQUIPMENT_SN','PERIODS_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 7 as fg,'HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,IS_DEL,IS_STANDARD,CREATE_TIME,UPDATE_TIME,REMAIN_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT'
            AND COLUMN_NAME in('ID','INIT_SN','IS_DEL','IS_STANDARD','CREATE_TIME','UPDATE_TIME','REMAIN_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 31 as fg,'HW_EQUIPMENT_STORAGE_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,CREATER,EXAMINER,SUPPLIER,JOB_NUMBER,CONTRACT_PIC,STORAGE_ORDER,EXAMINER_NUMBER,DEPOT,IS_DEL,IS_TEST,AGENT_ID,BIZ_TYPE,ORDER_NUM,ORDER_TYPE,ARRIVAL_NUM,EQUIPMENT_ID,INVOICE_TYPE,OPERATE_TYPE,ORDER_STATUS,PURCHASE_TYPE,INVOICE_CROSS_CHECK_STATUS,CREATE_TIME,UPDATE_TIME,EXECUTE_TIME,UNIT_PRICE,ORDER_PRICE,PURCHASE_RATE,NO_TAX_UNIT_PRICE,NO_TAX_ORDER_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_STORAGE_ORDER'
            AND COLUMN_NAME in('ID','REMARK','CREATER','EXAMINER','SUPPLIER','JOB_NUMBER','CONTRACT_PIC','STORAGE_ORDER','EXAMINER_NUMBER','DEPOT','IS_DEL','IS_TEST','AGENT_ID','BIZ_TYPE','ORDER_NUM','ORDER_TYPE','ARRIVAL_NUM','EQUIPMENT_ID','INVOICE_TYPE','OPERATE_TYPE','ORDER_STATUS','PURCHASE_TYPE','INVOICE_CROSS_CHECK_STATUS','CREATE_TIME','UPDATE_TIME','EXECUTE_TIME','UNIT_PRICE','ORDER_PRICE','PURCHASE_RATE','NO_TAX_UNIT_PRICE','NO_TAX_ORDER_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'HW_EQUIPMENT_WORK_ORDER_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,REMARK,OPERATOR,OPERATOR_ID,WORK_ORDER_SN,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_WORK_ORDER_LOG'
            AND COLUMN_NAME in('ID','INIT_SN','REMARK','OPERATOR','OPERATOR_ID','WORK_ORDER_SN','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'HW_SHOP_REFUND_ORDER_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_NAME,GOODS_SKU_ID,GOODS_SPU_ID,HW_REFUND_ORDER_SN,IS_DEL,IS_TEST,GOODS_TYPE,EQUIPMENT_ID,GOODS_NUMBER,GOODS_REFUND_SCORE,GOODS_REFUND_NUMBER,CREATE_TIME,UPDATE_TIME,GOODS_PRICE,GOODS_SUMPRICE,GOODS_REFUND_SUMPRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_REFUND_ORDER_DETAIL'
            AND COLUMN_NAME in('ID','GOODS_NAME','GOODS_SKU_ID','GOODS_SPU_ID','HW_REFUND_ORDER_SN','IS_DEL','IS_TEST','GOODS_TYPE','EQUIPMENT_ID','GOODS_NUMBER','GOODS_REFUND_SCORE','GOODS_REFUND_NUMBER','CREATE_TIME','UPDATE_TIME','GOODS_PRICE','GOODS_SUMPRICE','GOODS_REFUND_SUMPRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,OPERATOR,OPERATOR_NAME,IS_DEL,AFTER_USER_ID,BEFORE_USER_ID,EFFECTIVE_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST'
            AND COLUMN_NAME in('ID','INIT_SN','OPERATOR','OPERATOR_NAME','IS_DEL','AFTER_USER_ID','BEFORE_USER_ID','EFFECTIVE_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'HW_ALIPAY_MERCHANT_SERVICE_BIND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BIND_TIME,PID,MSID,TOKEN,DEVICE_NO,IDENTITY_NO,UID,STORE_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_ALIPAY_MERCHANT_SERVICE_BIND'
            AND COLUMN_NAME in('ID','BIND_TIME','PID','MSID','TOKEN','DEVICE_NO','IDENTITY_NO','UID','STORE_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'HW_HARVEST_PLAN_SALESMAN_DAY_REWARD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,SALESMAN,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT,DAY_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_SALESMAN_DAY_REWARD'
            AND COLUMN_NAME in('ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','SALESMAN','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT','DAY_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'TP_FACE_SCAN_EQUIPMENT_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_SN,UID,AGENT_ID,STORE_ID,CODE_SCAN_NUM,FACE_SCAN_DAU,LIGHT_STATUS,EQUIPMENT_DAU,ACTIVITY_STATUS,TRANSACTION_NUM,ALIPAY_LIGHT_TIME,LIGHT_TIME,CREATE_TIME,UPDATE_TIME,ACTIVITY_TIME,TRANSACTION_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_FACE_SCAN_EQUIPMENT_RECORD'
            AND COLUMN_NAME in('ID','EQUIPMENT_SN','UID','AGENT_ID','STORE_ID','CODE_SCAN_NUM','FACE_SCAN_DAU','LIGHT_STATUS','EQUIPMENT_DAU','ACTIVITY_STATUS','TRANSACTION_NUM','ALIPAY_LIGHT_TIME','LIGHT_TIME','CREATE_TIME','UPDATE_TIME','ACTIVITY_TIME','TRANSACTION_MONEY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 16 as fg,'TP_EQUIPMENT_PURCHASE_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NOTE,ORDER_SN,AGENT_ID,CREATER,EXAMINER,ORDER_NUM,ARRIVAL_NUM,CREATE_TIME,EXAMIN_TIME,UPDATE_TIME,EQUIPMENT_ID,ORDER_STATUS,PURCHASE_TYPE,DEPOT_LOCATION,ORDER_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_PURCHASE_ORDER'
            AND COLUMN_NAME in('ID','NOTE','ORDER_SN','AGENT_ID','CREATER','EXAMINER','ORDER_NUM','ARRIVAL_NUM','CREATE_TIME','EXAMIN_TIME','UPDATE_TIME','EQUIPMENT_ID','ORDER_STATUS','PURCHASE_TYPE','DEPOT_LOCATION','ORDER_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 30 as fg,'HW_MERCHANT_BUY_ORDER_CHILDREN' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,ORDER_SN,FB_ORDER_SN,NEW_INIT_SN,ACTIVITY_ID,INIT_SN_HISTORY,CHILDREN_ORDER_SN,ALIPAY_AUDIT_REMARK,ALIPAY_ACTIVITY_INFO_URL,ALIPA_SALES_ENTRY_ORDER_ID,UID,AGENT_ID,PAY_TIME,PAY_TYPE,STORE_ID,IS_REFUND,WORKER_ID,SALES_TYPE,ORDER_STATUS,CONFIRM_ORDER,SESAME_GO_TYPE,RECYCLE_STATUS,PERIOD_GENERATE,SESAME_GO_STATUS,WORKER_PARENT_ID,ALIPAY_AUDIT_STATUS,CREATE_TIME,UPDATE_TIME,ORDER_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_MERCHANT_BUY_ORDER_CHILDREN'
            AND COLUMN_NAME in('ID','INIT_SN','ORDER_SN','FB_ORDER_SN','NEW_INIT_SN','ACTIVITY_ID','INIT_SN_HISTORY','CHILDREN_ORDER_SN','ALIPAY_AUDIT_REMARK','ALIPAY_ACTIVITY_INFO_URL','ALIPA_SALES_ENTRY_ORDER_ID','UID','AGENT_ID','PAY_TIME','PAY_TYPE','STORE_ID','IS_REFUND','WORKER_ID','SALES_TYPE','ORDER_STATUS','CONFIRM_ORDER','SESAME_GO_TYPE','RECYCLE_STATUS','PERIOD_GENERATE','SESAME_GO_STATUS','WORKER_PARENT_ID','ALIPAY_AUDIT_STATUS','CREATE_TIME','UPDATE_TIME','ORDER_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'HW_SHOP_GOODS_SKU' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_SKU_ID,GOODS_SPU_ID,IS_DEL,IS_TEST,END_NUMBER,START_NUMBER,CREATE_TIME,UPDATE_TIME,SKU_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_SKU'
            AND COLUMN_NAME in('ID','GOODS_SKU_ID','GOODS_SPU_ID','IS_DEL','IS_TEST','END_NUMBER','START_NUMBER','CREATE_TIME','UPDATE_TIME','SKU_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'HW_HARVEST_PLAN_AGENT_DAY_INTEGRAL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_DAY_INTEGRAL'
            AND COLUMN_NAME in('ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'HW_EQUIPMENT_MODEL_COMMISSION_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,IS_DEL,RULE_TYPE,ACTIVITY_ID,EQUIPMENT_ID,PRODUCT_TYPE,COMMISSION_TYPE,IS_NEED_PARSE_URL,CREATE_TIME,UPDATE_TIME,COMMISSION,COMMISSION_UNIT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_MODEL_COMMISSION_RELATION'
            AND COLUMN_NAME in('ID','IS_DEL','RULE_TYPE','ACTIVITY_ID','EQUIPMENT_ID','PRODUCT_TYPE','COMMISSION_TYPE','IS_NEED_PARSE_URL','CREATE_TIME','UPDATE_TIME','COMMISSION','COMMISSION_UNIT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'HW_EQUIPMENT_DOWNLOAD_CENTER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FILE_URL,REMARKS,CREATE_BY,FILE_NAME,UPDATE_BY,DOWNLOAD_ID,STATUS,MODULE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_DOWNLOAD_CENTER'
            AND COLUMN_NAME in('ID','FILE_URL','REMARKS','CREATE_BY','FILE_NAME','UPDATE_BY','DOWNLOAD_ID','STATUS','MODULE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'HW_GOODS_ACTIVITY_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_ID,BIND_MONTH,HW_ORDER_SN,UID,IS_DEL,SPU_ID,STORE_ID,ACTIVITY_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_GOODS_ACTIVITY_RELATION'
            AND COLUMN_NAME in('ID','GOODS_ID','BIND_MONTH','HW_ORDER_SN','UID','IS_DEL','SPU_ID','STORE_ID','ACTIVITY_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 24 as fg,'TP_EQUIPMENT_SN' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,SYSTEM_SN,UID,DEPOT,IS_DEL,AGENT_ID,GRANT_ID,STORE_ID,LOST_TIME,SN_STATUS,CASHIER_ID,CREATE_TIME,REJECT_TIME,UPDATE_TIME,CASHIER_MODE,EQUIPMENT_ID,RECEIVE_TIME,RECOVER_TIME,DAMAGE_STATUS,RECEIVE_STATUS,BUSINESS_STATUS,DISTRIBUTE_TIME,PRINTER_SETTING' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_SN'
            AND COLUMN_NAME in('ID','INIT_SN','SYSTEM_SN','UID','DEPOT','IS_DEL','AGENT_ID','GRANT_ID','STORE_ID','LOST_TIME','SN_STATUS','CASHIER_ID','CREATE_TIME','REJECT_TIME','UPDATE_TIME','CASHIER_MODE','EQUIPMENT_ID','RECEIVE_TIME','RECOVER_TIME','DAMAGE_STATUS','RECEIVE_STATUS','BUSINESS_STATUS','DISTRIBUTE_TIME','PRINTER_SETTING')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_SHOP_ORDER_SHIPMENTS_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BATCH_NO,EXPRESS_NO,HW_ORDER_SN,IS_DEL,IS_TEST,EXPRESS_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_ORDER_SHIPMENTS_RECORD'
            AND COLUMN_NAME in('ID','BATCH_NO','EXPRESS_NO','HW_ORDER_SN','IS_DEL','IS_TEST','EXPRESS_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 19 as fg,'HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVITY_ID,INIT_SN,PT_MONTH,UID,IS_DEL,BELONG,MARKET_ID,SALESMAN,AUDIT_STATUS,EQUIPMENT_ID,PRODUCT_TYPE,COMMISSION_TYPE,PROCUREMENT_TIME,STATISTICS_MONTH,CREATE_TIME,UPDATE_TIME,MONTH_AMOUNT,MONTH_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW'
            AND COLUMN_NAME in('ID','ACTIVITY_ID','INIT_SN','PT_MONTH','UID','IS_DEL','BELONG','MARKET_ID','SALESMAN','AUDIT_STATUS','EQUIPMENT_ID','PRODUCT_TYPE','COMMISSION_TYPE','PROCUREMENT_TIME','STATISTICS_MONTH','CREATE_TIME','UPDATE_TIME','MONTH_AMOUNT','MONTH_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 38 as fg,'HW_EQUIPMENT_SN' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,OEM_NAME,PASSWORD,SYSTEM_SN,EQUIPMENT_ALIAS_NAME,UID,DEPOT,IS_DEL,OEM_ID,TP_KEY,AGENT_ID,GRANT_ID,PAY_TYPE,STORE_ID,CODE_TYPE,MARKET_ID,SN_STATUS,CASHIER_ID,TRADE_MODE,CASHIER_MODE,CHANNEL_TYPE,EQUIPMENT_ID,OPERATE_TYPE,TRANSFER_TYPE,RECYCLE_STATUS,NEW_CASHIER_MODE,PRINTER_SETTING,IS_OPEN_SELF_SHOPPING,PRINTER_MASTER_SETTING,BACK_TIME,BIND_TIME,CREATE_TIME,UNBIND_TIME,UPDATE_TIME,RECEIVE_TIME,DISTRIBUTE_TIME,TRUE_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_SN'
            AND COLUMN_NAME in('ID','INIT_SN','OEM_NAME','PASSWORD','SYSTEM_SN','EQUIPMENT_ALIAS_NAME','UID','DEPOT','IS_DEL','OEM_ID','TP_KEY','AGENT_ID','GRANT_ID','PAY_TYPE','STORE_ID','CODE_TYPE','MARKET_ID','SN_STATUS','CASHIER_ID','TRADE_MODE','CASHIER_MODE','CHANNEL_TYPE','EQUIPMENT_ID','OPERATE_TYPE','TRANSFER_TYPE','RECYCLE_STATUS','NEW_CASHIER_MODE','PRINTER_SETTING','IS_OPEN_SELF_SHOPPING','PRINTER_MASTER_SETTING','BACK_TIME','BIND_TIME','CREATE_TIME','UNBIND_TIME','UPDATE_TIME','RECEIVE_TIME','DISTRIBUTE_TIME','TRUE_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 14 as fg,'HW_SHOP_ACTIVITY_SCORE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OPERATOR,ACCOUNT_NAME,OPERATE_LOG_ID,OPERATOR_NAME,RELATION_ORDER_NO,IS_DEL,SCORE,ACCOUNT_ID,ACTIVITY_ID,CHANGE_SCORE,OPERATE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_ACTIVITY_SCORE_LOG'
            AND COLUMN_NAME in('ID','OPERATOR','ACCOUNT_NAME','OPERATE_LOG_ID','OPERATOR_NAME','RELATION_ORDER_NO','IS_DEL','SCORE','ACCOUNT_ID','ACTIVITY_ID','CHANGE_SCORE','OPERATE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'TP_EQUIPMENT_SET' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_PIC,EQUIPMENT_MODEL,EQUIPMENT_INTRODUCE,IS_DEL,APP_SHOW,CATEGORY,OPERATOR,IS_INITIAL,START_TIME,CREATE_TIME,UPDATE_TIME,EQUIPMENT_ID,LEASE_PRICE,MIN_SALE_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_SET'
            AND COLUMN_NAME in('ID','EQUIPMENT_PIC','EQUIPMENT_MODEL','EQUIPMENT_INTRODUCE','IS_DEL','APP_SHOW','CATEGORY','OPERATOR','IS_INITIAL','START_TIME','CREATE_TIME','UPDATE_TIME','EQUIPMENT_ID','LEASE_PRICE','MIN_SALE_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 85 as fg,'TP_USER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QQ,TEL,AREA,BANK,CITY,LOGO,EMAIL,LEVEL,PHONE,UNAME,PEOPLE,REMARK,UTOKEN,WECHAT,ADDRESS,CARD_NUM,CARD_WEB,BUSINESS,CARDNAME,CONTACTS,FULL_PATH,NICKNAME,PASSWORD,PROVINCE,TURNOVER,USERNAME,PRIVILEGES,PROFESSION,CARD_ADDRESS,COMPANYNAME,LAST_LOGIN_IP,BUSINESS_AREA,BUSINESS_CITY,CUSTOMER_NOTE,LAST_LOCATION,VERIFYIMAGES,ALIPAYACCOUNT,OPEN_API_CALLBACK,BUSINESS_PROVINCE,UID,CASH,ROLE,TYPE,BELONG,IS_OPEN,IS_PASS,OWN_RUN,STATUS,IS_JDPAY,IS_WHITE,VIPTIME,CHILD_NUM,GROUP_NUM,IS_ALIPAY,PARENT_ID,PLATFORM,IS_SELF_FEE,CONFIG_TYPE,CREATETIME,IS_OPEN_MINA,IS_SALESMAN,IS_SHOW_TIPS,LOAN_STATUS,ACCOUNT_TYPE,IS_QUICK_CASH,SALESMAN_TAG,SUB_CONFIG_ID,USE_GROUP_NUM,CURRENT_LEVEL,SALES_STAFF_ID,IS_SCAN_SERVICE,LAST_LOGIN_TIME,CUSTOMER_SERVICE,IS_OPENAPI_ACCESS,IS_SUPER_SALESMAN,SUPER_SALESMAN_ID,ADVERTISEMENT_NUM,OPERATION_SERVICE,NEW_VERSION_USERS_NUM,SALESMAN_TAG_START_DAY,CREATE_TIME,UPDATE_TIME,FINANCE,ADVERTISEMENT_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_USER'
            AND COLUMN_NAME in('ID','QQ','TEL','AREA','BANK','CITY','LOGO','EMAIL','LEVEL','PHONE','UNAME','PEOPLE','REMARK','UTOKEN','WECHAT','ADDRESS','CARD_NUM','CARD_WEB','BUSINESS','CARDNAME','CONTACTS','FULL_PATH','NICKNAME','PASSWORD','PROVINCE','TURNOVER','USERNAME','PRIVILEGES','PROFESSION','CARD_ADDRESS','COMPANYNAME','LAST_LOGIN_IP','BUSINESS_AREA','BUSINESS_CITY','CUSTOMER_NOTE','LAST_LOCATION','VERIFYIMAGES','ALIPAYACCOUNT','OPEN_API_CALLBACK','BUSINESS_PROVINCE','UID','CASH','ROLE','TYPE','BELONG','IS_OPEN','IS_PASS','OWN_RUN','STATUS','IS_JDPAY','IS_WHITE','VIPTIME','CHILD_NUM','GROUP_NUM','IS_ALIPAY','PARENT_ID','PLATFORM','IS_SELF_FEE','CONFIG_TYPE','CREATETIME','IS_OPEN_MINA','IS_SALESMAN','IS_SHOW_TIPS','LOAN_STATUS','ACCOUNT_TYPE','IS_QUICK_CASH','SALESMAN_TAG','SUB_CONFIG_ID','USE_GROUP_NUM','CURRENT_LEVEL','SALES_STAFF_ID','IS_SCAN_SERVICE','LAST_LOGIN_TIME','CUSTOMER_SERVICE','IS_OPENAPI_ACCESS','IS_SUPER_SALESMAN','SUPER_SALESMAN_ID','ADVERTISEMENT_NUM','OPERATION_SERVICE','NEW_VERSION_USERS_NUM','SALESMAN_TAG_START_DAY','CREATE_TIME','UPDATE_TIME','FINANCE','ADVERTISEMENT_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 14 as fg,'HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVITY_ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,SALESMAN,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT,DAY_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_DAY_REWARD_NEW'
            AND COLUMN_NAME in('ID','ACTIVITY_ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','SALESMAN','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT','DAY_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 42 as fg,'HW_SHOP_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NOTE,PHONE,QR_CODE,ADDRESS,NOTE_IMG,ORDER_SN,AREA_NAME,CITY_NAME,USERNAME,HW_ORDER_SN,RECIPIENT,OPERATE_NOTE,PROVINCE_NAME,TRANSFER_PICTURES,FINANCE_AUDIT_PICTURES,UID,IS_DEL,IS_TEST,USER_ID,STORE_ID,IS_REFUND,USER_TYPE,ADDRESS_ID,HW_PAY_TIME,HW_PAY_TYPE,FINAL_SCORE,HW_PAY_STATUS,HW_ORDER_STATUS,IS_ALL_MATERIAL,IS_INSTALLMENT,HW_CLIENT_ORDER_STATUS,CREATE_TIME,UPDATE_TIME,BALANCE_PRICE,PAYMENT_PRICE,BASE_EXPRESS_FEE,FINAL_EXPRESS_FEE,BASE_GOODS_SUMPRICE,BASE_ORDER_SUMPRICE,FINAL_GOODS_SUMPRICE,FINAL_ORDER_SUMPRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_ORDER'
            AND COLUMN_NAME in('ID','NOTE','PHONE','QR_CODE','ADDRESS','NOTE_IMG','ORDER_SN','AREA_NAME','CITY_NAME','USERNAME','HW_ORDER_SN','RECIPIENT','OPERATE_NOTE','PROVINCE_NAME','TRANSFER_PICTURES','FINANCE_AUDIT_PICTURES','UID','IS_DEL','IS_TEST','USER_ID','STORE_ID','IS_REFUND','USER_TYPE','ADDRESS_ID','HW_PAY_TIME','HW_PAY_TYPE','FINAL_SCORE','HW_PAY_STATUS','HW_ORDER_STATUS','IS_ALL_MATERIAL','IS_INSTALLMENT','HW_CLIENT_ORDER_STATUS','CREATE_TIME','UPDATE_TIME','BALANCE_PRICE','PAYMENT_PRICE','BASE_EXPRESS_FEE','FINAL_EXPRESS_FEE','BASE_GOODS_SUMPRICE','BASE_ORDER_SUMPRICE','FINAL_GOODS_SUMPRICE','FINAL_ORDER_SUMPRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 31 as fg,'HW_EQUIPMENT_WORK_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,AUDIT_REMARK,LOGISTICS_NO,ORDER_REMARK,WORK_BATCH_NO,WORK_ORDER_SN,CANCEL_REASON,ABNORMAL_REMARK,MAINTENANCE_REMARK,PEND,IS_DEL,IS_STOP,SOURCE,AGENT_ID,BACK_WAY,GRANT_ID,STORE_ID,FB_UNBIND,WORK_TYPE,WORKER_ID,WORK_SCENE,ZFB_UNBIND,ASSIGN_TIME,ORDER_STATUS,ABNORMAL_TYPE,CONTINUE_WORK,BACK_WAREHOUSE,ZFB_UNBIND_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_WORK_ORDER'
            AND COLUMN_NAME in('ID','INIT_SN','AUDIT_REMARK','LOGISTICS_NO','ORDER_REMARK','WORK_BATCH_NO','WORK_ORDER_SN','CANCEL_REASON','ABNORMAL_REMARK','MAINTENANCE_REMARK','PEND','IS_DEL','IS_STOP','SOURCE','AGENT_ID','BACK_WAY','GRANT_ID','STORE_ID','FB_UNBIND','WORK_TYPE','WORKER_ID','WORK_SCENE','ZFB_UNBIND','ASSIGN_TIME','ORDER_STATUS','ABNORMAL_TYPE','CONTINUE_WORK','BACK_WAREHOUSE','ZFB_UNBIND_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'HW_SHOP_ORDER_STORAGE_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,HW_ORDER_SN,STORAGE_ORDER,IS_DEL,IS_TEST,DIRECTION,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_ORDER_STORAGE_RECORD'
            AND COLUMN_NAME in('ID','HW_ORDER_SN','STORAGE_ORDER','IS_DEL','IS_TEST','DIRECTION','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 30 as fg,'HW_SHOP_SALES_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,DEPOT,USERNAME,GOODS_NAME,EXTEND_INFO,GOODS_SPU_ID,SALES_ORDER,STORAGE_ORDER,RELATION_ORDER_NO,IS_DEL,SCORE,IS_TEST,USER_ID,USER_TYPE,SALES_TYPE,BIZ_PAY_TIME,EQUIPMENT_ID,GOODS_SN_NUMBER,BIZ_TIME,CREATE_TIME,UPDATE_TIME,COST_PRICE,SALES_RATE,EXPRESS_FEE,SALES_PRICE,COST_UNIT_PRICE,ONLINE_PAY_PRICE,SALES_UNIT_PRICE,BALANCE_PAY_PRICE,OFFLINE_PAY_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_SALES_ORDER'
            AND COLUMN_NAME in('ID','DEPOT','USERNAME','GOODS_NAME','EXTEND_INFO','GOODS_SPU_ID','SALES_ORDER','STORAGE_ORDER','RELATION_ORDER_NO','IS_DEL','SCORE','IS_TEST','USER_ID','USER_TYPE','SALES_TYPE','BIZ_PAY_TIME','EQUIPMENT_ID','GOODS_SN_NUMBER','BIZ_TIME','CREATE_TIME','UPDATE_TIME','COST_PRICE','SALES_RATE','EXPRESS_FEE','SALES_PRICE','COST_UNIT_PRICE','ONLINE_PAY_PRICE','SALES_UNIT_PRICE','BALANCE_PAY_PRICE','OFFLINE_PAY_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'HW_EQUIPMENT_WORK_ORDER_IMAGE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,IMAGE_URL,WORK_ORDER_SN,IS_DEL,IMAGE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_WORK_ORDER_IMAGE'
            AND COLUMN_NAME in('ID','INIT_SN','IMAGE_URL','WORK_ORDER_SN','IS_DEL','IMAGE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 26 as fg,'HW_SHOP_GOODS_SPU' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PICTURE,GOODS_DESC,GOODS_NAME,GOODS_SPU_ID,CATEGORY_IDS,INSTALLMENT_AMOUNTS,SORT,IS_DEL,IS_TEST,IS_GROUP,GOODS_TYPE,PRICE_TYPE,SPU_STATUS,IS_COMBINED,EQUIPMENT_ID,FREE_SEND_NUM,GROUP_NUMBER,IS_INSTALLMENT,INSTALLMENT_TYPE,INSTALLMENT_NUMBER,IS_CONTAINS_EXPRESS,CREATE_TIME,UPDATE_TIME,SPU_PRICE,SPU_WEIGHT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_SPU'
            AND COLUMN_NAME in('ID','PICTURE','GOODS_DESC','GOODS_NAME','GOODS_SPU_ID','CATEGORY_IDS','INSTALLMENT_AMOUNTS','SORT','IS_DEL','IS_TEST','IS_GROUP','GOODS_TYPE','PRICE_TYPE','SPU_STATUS','IS_COMBINED','EQUIPMENT_ID','FREE_SEND_NUM','GROUP_NUMBER','IS_INSTALLMENT','INSTALLMENT_TYPE','INSTALLMENT_NUMBER','IS_CONTAINS_EXPRESS','CREATE_TIME','UPDATE_TIME','SPU_PRICE','SPU_WEIGHT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'HW_EQUIPMENT_OPERATION_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,CREATER,JOB_NUMBER,OPERATE_CONTENT,OPERATE_MODULE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_OPERATION_RECORD'
            AND COLUMN_NAME in('ID','REMARK','CREATER','JOB_NUMBER','OPERATE_CONTENT','OPERATE_MODULE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_SHOP_GOODS_COMBINED_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_NAME,GOODS_SPU_ID,IS_DEL,EQUIPMENT_ID,PRODUCT_TYPE,EQUIPMENT_NUMBER,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_COMBINED_RELATION'
            AND COLUMN_NAME in('ID','GOODS_NAME','GOODS_SPU_ID','IS_DEL','EQUIPMENT_ID','PRODUCT_TYPE','EQUIPMENT_NUMBER','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'HW_EQUIPMENT_STOCK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CREATER,JOB_NUMBER,STOCK_ORDER,STORAGE_ORDER,DEPOT,ORDER_NUM,CREATE_TIME,UPDATE_TIME,EXECUTE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_STOCK'
            AND COLUMN_NAME in('ID','CREATER','JOB_NUMBER','STOCK_ORDER','STORAGE_ORDER','DEPOT','ORDER_NUM','CREATE_TIME','UPDATE_TIME','EXECUTE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 14 as fg,'HW_HARVEST_PLAN_SALESMAN_DAY_REWARD_NEW' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVITY_ID,PT_DAY,INIT_SN,UID,IS_DEL,BELONG,SALESMAN,EQUIPMENT_ID,STATISTICS_DAY,CREATE_TIME,UPDATE_TIME,DAY_AMOUNT,DAY_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_SALESMAN_DAY_REWARD_NEW'
            AND COLUMN_NAME in('ID','ACTIVITY_ID','PT_DAY','INIT_SN','UID','IS_DEL','BELONG','SALESMAN','EQUIPMENT_ID','STATISTICS_DAY','CREATE_TIME','UPDATE_TIME','DAY_AMOUNT','DAY_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 6 as fg,'HW_SHOP_GOODS_INSTALLMENT_SN' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EQUIPMENT_SN,INSTALLMENT_ID,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_INSTALLMENT_SN'
            AND COLUMN_NAME in('ID','EQUIPMENT_SN','INSTALLMENT_ID','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'TP_EQUIPMENT_SHIPMENT_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NOTE,ORDER_SN,AGENT_ID,ORDER_NUM,ARRIVAL_NUM,CREATE_TIME,EXAMIN_TIME,UPDATE_TIME,ORDER_STATUS,SHIPMENT_TYPE,DEPOT_LOCATION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_EQUIPMENT_SHIPMENT_ORDER'
            AND COLUMN_NAME in('ID','NOTE','ORDER_SN','AGENT_ID','ORDER_NUM','ARRIVAL_NUM','CREATE_TIME','EXAMIN_TIME','UPDATE_TIME','ORDER_STATUS','SHIPMENT_TYPE','DEPOT_LOCATION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_SHOP_EQUIPMENT_STOCK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,IS_DEL,STOCK,IS_TEST,EQUIPMENT_ID,HW_ORDER_STOCK,PRE_SALE_STOCK,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_EQUIPMENT_STOCK'
            AND COLUMN_NAME in('ID','IS_DEL','STOCK','IS_TEST','EQUIPMENT_ID','HW_ORDER_STOCK','PRE_SALE_STOCK','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'HW_HARVEST_PLAN_AGENT_MONTH_REWARD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INIT_SN,PT_MONTH,UID,IS_DEL,BELONG,EQUIPMENT_ID,STATISTICS_MONTH,CREATE_TIME,UPDATE_TIME,MONTH_AMOUNT,MONTH_REWARD' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_MONTH_REWARD'
            AND COLUMN_NAME in('ID','INIT_SN','PT_MONTH','UID','IS_DEL','BELONG','EQUIPMENT_ID','STATISTICS_MONTH','CREATE_TIME','UPDATE_TIME','MONTH_AMOUNT','MONTH_REWARD')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'HW_HARVEST_PLAN_AGENT_WHITE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,JOB_NUMBER,OPERATOR_NAME,AGENT_ID,IS_CLOSE,ACTIVITY_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_HARVEST_PLAN_AGENT_WHITE'
            AND COLUMN_NAME in('ID','REMARK','JOB_NUMBER','OPERATOR_NAME','AGENT_ID','IS_CLOSE','ACTIVITY_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 24 as fg,'HW_SHOP_GOODS_INSTALLMENT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORDER_NO,AGENT_NAME,GOODS_NAME,GOODS_SPU_ID,CANCEL_REASON,ORDER_ACCOUNT,INSTALLMENT_ID,IS_DEL,AGENT_ID,FINISH_TYPE,ORDER_ACCOUNT_ID,INSTALLMENT_TYPE,REPAYMENT_STATUS,GOODS_INSTALLMENT,GOODS_TOTAL_NUMBER,INSTALLMENT_END_DATE,INSTALLMENT_START_DATE,REMAINING_INSTALLMENT,ORDER_TIME,CREATE_TIME,UPDATE_TIME,REMAINING_AMOUNT,OUTSTANDING_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_INSTALLMENT'
            AND COLUMN_NAME in('ID','ORDER_NO','AGENT_NAME','GOODS_NAME','GOODS_SPU_ID','CANCEL_REASON','ORDER_ACCOUNT','INSTALLMENT_ID','IS_DEL','AGENT_ID','FINISH_TYPE','ORDER_ACCOUNT_ID','INSTALLMENT_TYPE','REPAYMENT_STATUS','GOODS_INSTALLMENT','GOODS_TOTAL_NUMBER','INSTALLMENT_END_DATE','INSTALLMENT_START_DATE','REMAINING_INSTALLMENT','ORDER_TIME','CREATE_TIME','UPDATE_TIME','REMAINING_AMOUNT','OUTSTANDING_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'HW_EQUIPMENT_ACTIVITY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SPU_IDS,END_TIME,START_TIME,ACTIVITY_NAME,EQUIPMENT_IDS,OPERATOR_NAME,IS_DEL,OPERATOR_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_ACTIVITY'
            AND COLUMN_NAME in('ID','SPU_IDS','END_TIME','START_TIME','ACTIVITY_NAME','EQUIPMENT_IDS','OPERATOR_NAME','IS_DEL','OPERATOR_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'HW_SHOP_GOODS_INSTALLMENT_NUMBER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,ATTACHMENT_URL,INSTALLMENT_ID,IS_DEL,REPAYMENT_TYPE,REPAYMENT_STATUS,REAL_REPAYMENT_TIME,CREATE_TIME,UPDATE_TIME,PREDICT_REPAYMENT_DATE,REAL_REPAYMENT_AMOUNT,PREDICT_REPAYMENT_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_SHOP_GOODS_INSTALLMENT_NUMBER'
            AND COLUMN_NAME in('ID','REMARK','ATTACHMENT_URL','INSTALLMENT_ID','IS_DEL','REPAYMENT_TYPE','REPAYMENT_STATUS','REAL_REPAYMENT_TIME','CREATE_TIME','UPDATE_TIME','PREDICT_REPAYMENT_DATE','REAL_REPAYMENT_AMOUNT','PREDICT_REPAYMENT_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'HW_EQUIPMENT_SN_CHANGE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,SN_ID,DEPOT,AGENT_ID,CHANNEL,GRANT_ID,STORE_ID,MARKET_ID,HANDLE_TYPE,CREATE_TIME,UPDATE_TIME,EXECUTE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'HW_EQUIPMENT_SN_CHANGE_LOG'
            AND COLUMN_NAME in('ID','REMARK','SN_ID','DEPOT','AGENT_ID','CHANNEL','GRANT_ID','STORE_ID','MARKET_ID','HANDLE_TYPE','CREATE_TIME','UPDATE_TIME','EXECUTE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
