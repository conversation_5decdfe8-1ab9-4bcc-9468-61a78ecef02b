<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ReqSpRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ReqSpRelationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REQ_ID" property="reqId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REQ_NAME" property="reqName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REQ_TYPE" property="reqType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ROLE_TYPE" property="roleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SP_ID`,`REQ_ID`,`REQ_NAME`,`DEL_FLAG`,`REQ_TYPE`,`ROLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_REQ_SP_RELATION-->
            <insert id="insert" >
            INSERT INTO ACC_REQ_SP_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="reqId != null">`REQ_ID`,</if>
        <if test="reqName != null">`REQ_NAME`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="reqType != null">`REQ_TYPE`,</if>
        <if test="roleType != null">`ROLE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="reqId != null">#{reqId,jdbcType=VARCHAR},</if>
        <if test="reqName != null">#{reqName,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="reqType != null">#{reqType,jdbcType=TINYINT},</if>
        <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据平台商查询-->
            <select id="getBySpId" resultMap="BaseResultMap">
                    select /*MS-ACC-REQ-SP-RELATION-GETBYSPID*/ <include refid="Base_Column_List" /> from acc_req_sp_relation
        where sp_id=#{spId,jdbcType=VARCHAR}
        AND del_flag = 1
        limit 1
            </select>
    </mapper>
