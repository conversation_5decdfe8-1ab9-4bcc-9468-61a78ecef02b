<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleRefundDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleRefundDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT7" property="ext7" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT8" property="ext8" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT9" property="ext9" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT10" property="ext10" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_CODE" property="refundCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_INFO" property="refundInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FACE_DEVICE_SN" property="faceDeviceSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_SN" property="platformOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_REFUND_SN" property="merchantRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_REFUND_SN" property="platformRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATOR_ORDER_SN" property="liquidatorOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_REFUND_ORDER_SN" property="goodsRefundOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATOR_REFUND_SN" property="liquidatorRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HANDLER" property="handler" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_MORE_DAY" property="isMoreDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_TYPE" property="consumeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LASTEST_TIME" property="lastestTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PART_REFUND" property="isPartRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_AGENT_ID" property="refundAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SPECIAL_SETTLE" property="specialSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_CHANNEL" property="consumeChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POSITION_REFUND" property="isPositionRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORGANIZATION_TYPE" property="organizationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_FAILED_DEAL" property="refundFailedDeal" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_SHARE_STATUS" property="refundShareStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_UPDATE_TIME" property="refundUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REFUND" property="refund" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CASH_FEE" property="cashFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LIFE_FEE" property="lifeFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_FEE" property="agentFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="POUNDAGE" property="poundage" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AGENT_RATE_FEE" property="agentRateFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RECHARGEACT_AMOUNT" property="rechargeactAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`EXT7`,`EXT8`,`EXT9`,`EXT10`,`TOKEN`,`REMARK`,`ORDER_SN`,`DEVICE_NO`,`REFUND_SN`,`NOTIFY_URL`,`ACTIVITY_ID`,`REFUND_CODE`,`REFUND_INFO`,`FACE_DEVICE_SN`,`MERCHANT_ORDER_SN`,`PLATFORM_ORDER_SN`,`MERCHANT_REFUND_SN`,`PLATFORM_REFUND_SN`,`LIQUIDATOR_ORDER_SN`,`GOODS_REFUND_ORDER_SN`,`LIQUIDATOR_REFUND_SN`,`USER_ID`,`CHANNEL`,`GRANT_ID`,`HANDLER`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`MARKET_ID`,`CASHIER_ID`,`IS_MORE_DAY`,`CREATE_TIME`,`SETTLE_MODE`,`CONSUME_TYPE`,`LASTEST_TIME`,`SUB_CONFIG_ID`,`IS_PART_REFUND`,`REFUND_STATUS`,`REFUND_AGENT_ID`,`SPECIAL_SETTLE`,`CONSUME_CHANNEL`,`LIQUIDATION_TYPE`,`IS_POSITION_REFUND`,`ORGANIZATION_TYPE`,`REFUND_FAILED_DEAL`,`REFUND_SHARE_STATUS`,`REFUND_UPDATE_TIME`,`REFUND`,`CASH_FEE`,`LIFE_FEE`,`AGENT_FEE`,`POUNDAGE`,`REFUND_MONEY`,`AGENT_RATE_FEE`,`RECHARGEACT_AMOUNT`
    </sql>


            <!--findOrderRefundType-->
            <select id="findOrderRefundType" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.OrderRefundTypeDTO">
                    select
        order_sn as orderSn,
        is_part_refund as isPartRefund,
        sum( `refund_money`) as refundMoney,
        sum(poundage) as refundFee,
         count(*)  as refundedCount
        from tp_lifecircle_refund
        where refund_status in (1,2)
        and order_sn in
        <foreach collection="list" item="orderSn" open="(" separator="," close=")">
            #{orderSn,jdbcType=INTEGER}
        </foreach>
        GROUP BY order_sn
            </select>
    </mapper>
