<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.ReceiptRefundDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.ReceiptRefundDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_ID" property="receiptId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_FORM" property="receiptForm" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="ERROR_MESSAGE" property="errorMessage" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_TITLE" property="receiptTitle" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_BATCH_NO" property="refundBatchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_REFUND_SN" property="shortRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_REFUND_SN" property="receiptRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_PAY_ORDER_NO" property="shortPayOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HANDLER" property="handler" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PART_REFUND" property="isPartRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_SOURCE" property="refundSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="POUNDAGE" property="poundage" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`ORDER_NO`,`REFUND_SN`,`RECEIPT_ID`,`PAY_ORDER_NO`,`RECEIPT_FORM`,`ERROR_MESSAGE`,`RECEIPT_TITLE`,`REFUND_BATCH_NO`,`SHORT_REFUND_SN`,`MERCHANT_ORDER_SN`,`RECEIPT_REFUND_SN`,`SHORT_PAY_ORDER_NO`,`IS_DEL`,`HANDLER`,`PAY_TYPE`,`STORE_ID`,`CASHIER_ID`,`IS_PART_REFUND`,`REFUND_SOURCE`,`REFUND_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`POUNDAGE`,`ORDER_PRICE`,`REFUND_MONEY`
    </sql>


            <!--insert:TP_RECEIPT_REFUND-->
            <insert id="insert" >
            INSERT INTO TP_RECEIPT_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderNo != null">`ORDER_NO`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="receiptId != null">`RECEIPT_ID`,</if>
        <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
        <if test="receiptForm != null">`RECEIPT_FORM`,</if>
        <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
        <if test="receiptTitle != null">`RECEIPT_TITLE`,</if>
        <if test="refundBatchNo != null">`REFUND_BATCH_NO`,</if>
        <if test="shortRefundSn != null">`SHORT_REFUND_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="receiptRefundSn != null">`RECEIPT_REFUND_SN`,</if>
        <if test="shortPayOrderNo != null">`SHORT_PAY_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="handler != null">`HANDLER`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="isPartRefund != null">`IS_PART_REFUND`,</if>
        <if test="refundSource != null">`REFUND_SOURCE`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="poundage != null">`POUNDAGE`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="refundMoney != null">`REFUND_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
        <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
        <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
        <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
        <if test="receiptTitle != null">#{receiptTitle,jdbcType=VARCHAR},</if>
        <if test="refundBatchNo != null">#{refundBatchNo,jdbcType=VARCHAR},</if>
        <if test="shortRefundSn != null">#{shortRefundSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="receiptRefundSn != null">#{receiptRefundSn,jdbcType=VARCHAR},</if>
        <if test="shortPayOrderNo != null">#{shortPayOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="handler != null">#{handler,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="isPartRefund != null">#{isPartRefund,jdbcType=TINYINT},</if>
        <if test="refundSource != null">#{refundSource,jdbcType=TINYINT},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="poundage != null">#{poundage,jdbcType=DECIMAL},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--getRefundSum-->
            <select id="getRefundSum" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.OrderRefundTypeDTO">
                    select /*MS-TP-RECEIPT-REFUND-GETREFUNDSUM*/ pay_order_no orderSn,is_part_refund isPartRefund,sum(refund_money) refundMoney,sum(poundage) refundFee,  count(*)  refundedCount from tp_receipt_refund
        WHERE `pay_order_no` IN
        <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
            #{payOrderNo,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
