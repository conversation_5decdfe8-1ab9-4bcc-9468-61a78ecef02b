<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.MerchantCommissionMonthDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.MerchantCommissionMonthDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_MANAGER" property="marketManager" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPER_SALESMAN_USERNAME" property="superSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CPM_NUM" property="cpmNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_T1_SETTLE" property="isT1Settle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_HFIVE" property="exposureNumHfive" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NORMAL_TRADE_COUNT" property="normalTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_TRADE_COUNT" property="refundTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPOSURE_NUM_APPLET" property="exposureNumApplet" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="GRANT_FEE" property="grantFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MARKET_FEE" property="marketFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PAYABLE_COMMISSION" property="payableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DEDUCTION_COMMISSION" property="deductionCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="CommissionPayableDetailMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.resultmap.CommissionPayableDetailMap">

                <result column="account_name" property="accountName" javaType="java.lang.String"/>

                <result column="salesman_name" property="salesmanName" javaType="java.lang.String"/>

                <result column="market_manager" property="marketManager" javaType="java.lang.String"/>

                <result column="cpm_num" property="cpmNum" javaType="java.lang.Integer"/>

                <result column="agent_id" property="agentId" javaType="java.lang.Integer"/>

                <result column="market_id" property="marketId" javaType="java.lang.Integer"/>

                <result column="account_id" property="accountId" javaType="java.lang.Integer"/>

                <result column="salesman_id" property="salesmanId" javaType="java.lang.Integer"/>

                <result column="salesman_id" property="salesmanId" javaType="java.lang.Integer"/>

                <result column="business_date" property="businessDate" javaType="java.lang.Integer"/>

                <result column="super_salesman_id" property="superSalesmanId" javaType="java.lang.Integer"/>

                <result column="exposure_num_hfive" property="exposureNumHfive" javaType="java.lang.Integer"/>

                <result column="exposure_total_num" property="exposureTotalNum" javaType="java.lang.Integer"/>

                <result column="normal_trade_count" property="normalTradeCount" javaType="java.lang.Integer"/>

                <result column="exposure_num_applet" property="exposureNumApplet" javaType="java.lang.Integer"/>

                <result column="agent_fee" property="agentFee" javaType="java.math.BigDecimal"/>

                <result column="grant_fee" property="grantFee" javaType="java.math.BigDecimal"/>

                <result column="market_fee" property="marketFee" javaType="java.math.BigDecimal"/>

                <result column="normal_trade_amount" property="normalTradeAmount" javaType="java.math.BigDecimal"/>

                <result column="payable_commission" property="payableCommission" javaType="java.math.BigDecimal"/>

                <result column="deduction_commission" property="deductionCommission" javaType="java.math.BigDecimal"/>

                <result column="actual_payable_commission" property="actualPayableCommission" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`SOURCE_TYPE`,`AGENT_USERNAME`,`MARKET_MANAGER`,`AGENT_COMPANYNAME`,`MERCHANT_USERNAME`,`SALESMAN_USERNAME`,`SUPER_SALESMAN_USERNAME`,`UID`,`CPM_NUM`,`AGENT_ID`,`PT_MONTH`,`MARKET_ID`,`IS_T1_SETTLE`,`SALESMAN_ID`,`BUSINESS_DATE`,`SUPER_SALESMAN_ID`,`EXPOSURE_NUM_HFIVE`,`NORMAL_TRADE_COUNT`,`REFUND_TRADE_COUNT`,`EXPOSURE_NUM_APPLET`,`CREATE_TIME`,`UPDATE_TIME`,`GRANT_FEE`,`MARKET_FEE`,`NORMAL_TRADE_AMOUNT`,`PAYABLE_COMMISSION`,`REFUND_TRADE_AMOUNT`,`DEDUCTION_COMMISSION`,`ACTUAL_PAYABLE_COMMISSION`
    </sql>


            <!--insert:FP_MERCHANT_COMMISSION_MONTH-->
            <insert id="insert" >
                    INSERT INTO FP_MERCHANT_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="marketManager != null">`MARKET_MANAGER`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="merchantUsername != null">`MERCHANT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="superSalesmanUsername != null">`SUPER_SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="cpmNum != null">`CPM_NUM`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="exposureNumHfive != null">`EXPOSURE_NUM_HFIVE`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="exposureNumApplet != null">`EXPOSURE_NUM_APPLET`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="marketManager != null">#{marketManager,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="merchantUsername != null">#{merchantUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="superSalesmanUsername != null">#{superSalesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="cpmNum != null">#{cpmNum,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="exposureNumHfive != null">#{exposureNumHfive,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="exposureNumApplet != null">#{exposureNumApplet,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据代理商id查询商户月佣金列表-->
            <select id="getMerchantCommissionList" resultMap="BaseResultMap">
                    select /*MS-FP-MERCHANT-COMMISSION-MONTH-GETMERCHANTCOMMISSIONLIST*/ <include refid="Base_Column_List" /> from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type = #{sourceType,jdbcType=VARCHAR}
        order by create_time desc
            </select>

            <!--根据代理商id查询代理商月佣金列表-->
            <select id="getAgentCommissionList" resultMap="CommissionPayableDetailMap">
                    select
        agent_id account_id,
        agent_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
            </select>

            <!--根据代理商id查询代理商月佣金列表-->
            <select id="getAgentCommissionV2List" resultMap="CommissionPayableDetailMap">
                    select
        agent_id account_id,
        agent_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
            </select>

            <!--根据代理商id查询超级授理商月佣金列表-->
            <select id="getSuperSalesmanCommissionList" resultMap="CommissionPayableDetailMap">
                    select
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
            </select>

            <!--根据代理商id查询超级授理商月佣金列表-->
            <select id="getSuperSalesmanCommissionV2List" resultMap="CommissionPayableDetailMap">
                    select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        super_salesman_username salesman_name,
        market_id market_id,
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        business_date,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
            </select>

            <!--根据代理商id查询授理商月佣金列表-->
            <select id="getSalesmanCommissionList" resultMap="CommissionPayableDetailMap">
                    select
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
            </select>

            <!--根据代理商id查询授理商月佣金列表-->
            <select id="getSalesmanCommissionV2List" resultMap="CommissionPayableDetailMap">
                    select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
            </select>

            <!--根据授理商ID获取佣金列表-->
            <select id="getMarketCommissionBySalesmanList" resultMap="CommissionPayableDetailMap">
                    select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        sum(market_fee) AS market_fee,
        sum(grant_fee) AS grant_fee,
        market_manager market_manager,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) - sum(market_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = #{salesmanId,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by market_id
        order by create_time desc
            </select>

            <!--根据超级授理商ID获取佣金列表-->
            <select id="getMarketCommissionBySuperSalesmanList" resultMap="CommissionPayableDetailMap">
                    select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        sum(market_fee) AS market_fee,
        sum(grant_fee) AS grant_fee,
        market_manager market_manager,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) - sum(market_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id = #{superSalesmanId,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by market_id
        order by create_time desc
            </select>
    </mapper>
