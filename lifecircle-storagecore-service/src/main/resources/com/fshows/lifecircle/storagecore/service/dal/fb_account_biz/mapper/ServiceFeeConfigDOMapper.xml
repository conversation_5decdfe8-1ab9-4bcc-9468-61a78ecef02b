<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ServiceFeeConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ServiceFeeConfigDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="SPC_ID" property="spcId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONFIG_ID" property="configId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FUBEI_ACCOUNT_ID" property="fubeiAccountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VIRTUAL_WALLET_ID" property="virtualWalletId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_VIRTUAL_WALLET_ID" property="platformVirtualWalletId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PLATFORM_SETTLE_MODE" property="platformSettleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PLATFORM_BILL_TIME_SPAN" property="platformBillTimeSpan" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SERVICE_FEE_RATIO" property="serviceFeeRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SPC_ID`,`CONFIG_ID`,`PLATFORM_CODE`,`FUBEI_ACCOUNT_ID`,`VIRTUAL_WALLET_ID`,`PLATFORM_VIRTUAL_WALLET_ID`,`DEL_FLAG`,`PLATFORM_SETTLE_MODE`,`PLATFORM_BILL_TIME_SPAN`,`CREATE_TIME`,`UPDATE_TIME`,`SERVICE_FEE_RATIO`
    </sql>


            <!--insert:ACC_SERVICE_FEE_CONFIG-->
            <insert id="insert" >
            INSERT INTO ACC_SERVICE_FEE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="configId != null">`CONFIG_ID`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="fubeiAccountId != null">`FUBEI_ACCOUNT_ID`,</if>
        <if test="virtualWalletId != null">`VIRTUAL_WALLET_ID`,</if>
        <if test="platformVirtualWalletId != null">`PLATFORM_VIRTUAL_WALLET_ID`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="platformSettleMode != null">`PLATFORM_SETTLE_MODE`,</if>
        <if test="platformBillTimeSpan != null">`PLATFORM_BILL_TIME_SPAN`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="serviceFeeRatio != null">`SERVICE_FEE_RATIO`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="configId != null">#{configId,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="fubeiAccountId != null">#{fubeiAccountId,jdbcType=VARCHAR},</if>
        <if test="virtualWalletId != null">#{virtualWalletId,jdbcType=VARCHAR},</if>
        <if test="platformVirtualWalletId != null">#{platformVirtualWalletId,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="platformSettleMode != null">#{platformSettleMode,jdbcType=TINYINT},</if>
        <if test="platformBillTimeSpan != null">#{platformBillTimeSpan,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="serviceFeeRatio != null">#{serviceFeeRatio,jdbcType=DECIMAL},</if>
    </trim>
            </insert>
    </mapper>
