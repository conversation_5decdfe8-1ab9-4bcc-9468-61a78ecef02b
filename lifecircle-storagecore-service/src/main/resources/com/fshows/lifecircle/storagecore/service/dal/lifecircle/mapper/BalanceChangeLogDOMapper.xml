<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BalanceChangeLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BalanceChangeLogDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANGE_REMARK" property="changeRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANGE_TYPE" property="changeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="AFTER_TODAY_BALANCE" property="afterTodayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_TOTAL_BALANCE" property="afterTotalBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_TODAY_BALANCE" property="changeTodayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_TOTAL_BALANCE" property="changeTotalBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_SUBSIDY_BALANCE" property="afterSubsidyBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_SUBSIDY_BALANCE" property="changeSubsidyBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_VIP_FREEZE_BALANCE" property="afterVipFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_VIP_FREEZE_BALANCE" property="changeVipFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_SHARE_FREEZE_BALANCE" property="afterShareFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_WITHDRAWABLE_BALANCE" property="afterWithdrawableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_SHARE_FREEZE_BALANCE" property="changeShareFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_DEPOSIT_FREEZE_BALANCE" property="afterDepositFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_ILLEGAL_FREEZE_BALANCE" property="afterIllegalFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_WITHDRAWABLE_BALANCE" property="changeWithdrawableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_DEPOSIT_FREEZE_BALANCE" property="changeDepositFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_ILLEGAL_FREEZE_BALANCE" property="changeIllegalFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="AFTER_SETTLEMENT_FREEZE_BALANCE" property="afterSettlementFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_SETTLEMENT_FREEZE_BALANCE" property="changeSettlementFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`ORDER_SN`,`CHANGE_REMARK`,`UID`,`CHANGE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`AFTER_TODAY_BALANCE`,`AFTER_TOTAL_BALANCE`,`CHANGE_TODAY_BALANCE`,`CHANGE_TOTAL_BALANCE`,`AFTER_SUBSIDY_BALANCE`,`CHANGE_SUBSIDY_BALANCE`,`AFTER_VIP_FREEZE_BALANCE`,`CHANGE_VIP_FREEZE_BALANCE`,`AFTER_SHARE_FREEZE_BALANCE`,`AFTER_WITHDRAWABLE_BALANCE`,`CHANGE_SHARE_FREEZE_BALANCE`,`AFTER_DEPOSIT_FREEZE_BALANCE`,`AFTER_ILLEGAL_FREEZE_BALANCE`,`CHANGE_WITHDRAWABLE_BALANCE`,`CHANGE_DEPOSIT_FREEZE_BALANCE`,`CHANGE_ILLEGAL_FREEZE_BALANCE`,`AFTER_SETTLEMENT_FREEZE_BALANCE`,`CHANGE_SETTLEMENT_FREEZE_BALANCE`
    </sql>


            <!--insert:TP_BALANCE_CHANGE_LOG-->
            <insert id="insert" >
            INSERT INTO TP_BALANCE_CHANGE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="changeRemark != null">`CHANGE_REMARK`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="changeType != null">`CHANGE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="afterTodayBalance != null">`AFTER_TODAY_BALANCE`,</if>
        <if test="afterTotalBalance != null">`AFTER_TOTAL_BALANCE`,</if>
        <if test="changeTodayBalance != null">`CHANGE_TODAY_BALANCE`,</if>
        <if test="changeTotalBalance != null">`CHANGE_TOTAL_BALANCE`,</if>
        <if test="afterVipFreezeBalance != null">`AFTER_VIP_FREEZE_BALANCE`,</if>
        <if test="changeVipFreezeBalance != null">`CHANGE_VIP_FREEZE_BALANCE`,</if>
        <if test="afterShareFreezeBalance != null">`AFTER_SHARE_FREEZE_BALANCE`,</if>
        <if test="afterWithdrawableBalance != null">`AFTER_WITHDRAWABLE_BALANCE`,</if>
        <if test="changeShareFreezeBalance != null">`CHANGE_SHARE_FREEZE_BALANCE`,</if>
        <if test="afterIllegalFreezeBalance != null">`AFTER_ILLEGAL_FREEZE_BALANCE`,</if>
        <if test="changeWithdrawableBalance != null">`CHANGE_WITHDRAWABLE_BALANCE`,</if>
        <if test="changeIllegalFreezeBalance != null">`CHANGE_ILLEGAL_FREEZE_BALANCE`,</if>
        <if test="afterSettlementFreezeBalance != null">`AFTER_SETTLEMENT_FREEZE_BALANCE`,</if>
        <if test="changeSettlementFreezeBalance != null">`CHANGE_SETTLEMENT_FREEZE_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="afterTodayBalance != null">#{afterTodayBalance,jdbcType=DECIMAL},</if>
        <if test="afterTotalBalance != null">#{afterTotalBalance,jdbcType=DECIMAL},</if>
        <if test="changeTodayBalance != null">#{changeTodayBalance,jdbcType=DECIMAL},</if>
        <if test="changeTotalBalance != null">#{changeTotalBalance,jdbcType=DECIMAL},</if>
        <if test="afterVipFreezeBalance != null">#{afterVipFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeVipFreezeBalance != null">#{changeVipFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterShareFreezeBalance != null">#{afterShareFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterWithdrawableBalance != null">#{afterWithdrawableBalance,jdbcType=DECIMAL},</if>
        <if test="changeShareFreezeBalance != null">#{changeShareFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterIllegalFreezeBalance != null">#{afterIllegalFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeWithdrawableBalance != null">#{changeWithdrawableBalance,jdbcType=DECIMAL},</if>
        <if test="changeIllegalFreezeBalance != null">#{changeIllegalFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterSettlementFreezeBalance != null">#{afterSettlementFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeSettlementFreezeBalance != null">#{changeSettlementFreezeBalance,jdbcType=DECIMAL},</if>
    </trim>
            </insert>
    </mapper>
