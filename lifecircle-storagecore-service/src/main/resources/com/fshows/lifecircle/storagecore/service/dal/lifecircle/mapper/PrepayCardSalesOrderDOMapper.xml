<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardSalesOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardSalesOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INVOICE_URL" property="invoiceUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_URL" property="receiptUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTRACT_URL" property="contractUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HANDLER_NAME" property="handlerName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_EMAIL" property="contactEmail" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_PHONE" property="contactPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECEIPT_ORDER" property="receiptOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALES_ORDER_NO" property="salesOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_ADDRESS" property="contactAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_SOURCE" property="orderSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DELIVERY_TIME" property="deliveryTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TOTAL_CARD_NUMBER" property="totalCardNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TOTAL_CARD_REAL" property="totalCardReal" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_CARD_PRICE" property="totalCardPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_CARD_AMOUNT" property="totalCardAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`REMARKS`,`OPERATE_ID`,`CUSTOMER_ID`,`INVOICE_URL`,`RECEIPT_URL`,`CONTRACT_URL`,`HANDLER_NAME`,`OPERATE_NAME`,`CONTACT_EMAIL`,`CONTACT_PHONE`,`CUSTOMER_NAME`,`RECEIPT_ORDER`,`SALES_ORDER_NO`,`CONTACT_ADDRESS`,`IS_DEL`,`ORDER_SOURCE`,`ORDER_STATUS`,`DELIVERY_TIME`,`TOTAL_CARD_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`TOTAL_CARD_REAL`,`TOTAL_CARD_PRICE`,`TOTAL_CARD_AMOUNT`
    </sql>


            <!--insert:TP_PREPAY_CARD_SALES_ORDER-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_SALES_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="invoiceUrl != null">`INVOICE_URL`,</if>
            <if test="contractUrl != null">`CONTRACT_URL`,</if>
            <if test="handlerName != null">`HANDLER_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="contactEmail != null">`CONTACT_EMAIL`,</if>
            <if test="contactPhone != null">`CONTACT_PHONE`,</if>
            <if test="customerName != null">`CUSTOMER_NAME`,</if>
            <if test="receiptOrder != null">`RECEIPT_ORDER`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="contactAddress != null">`CONTACT_ADDRESS`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="orderSource != null">`ORDER_SOURCE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="deliveryTime != null">`DELIVERY_TIME`,</if>
            <if test="totalCardNumber != null">`TOTAL_CARD_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="totalCardReal != null">`TOTAL_CARD_REAL`,</if>
            <if test="totalCardPrice != null">`TOTAL_CARD_PRICE`,</if>
            <if test="totalCardAmount != null">`TOTAL_CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="invoiceUrl != null">#{invoiceUrl,jdbcType=VARCHAR},</if>
            <if test="contractUrl != null">#{contractUrl,jdbcType=VARCHAR},</if>
            <if test="handlerName != null">#{handlerName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="contactEmail != null">#{contactEmail,jdbcType=VARCHAR},</if>
            <if test="contactPhone != null">#{contactPhone,jdbcType=VARCHAR},</if>
            <if test="customerName != null">#{customerName,jdbcType=VARCHAR},</if>
            <if test="receiptOrder != null">#{receiptOrder,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="contactAddress != null">#{contactAddress,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="orderSource != null">#{orderSource,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="deliveryTime != null">#{deliveryTime,jdbcType=INTEGER},</if>
            <if test="totalCardNumber != null">#{totalCardNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="totalCardReal != null">#{totalCardReal,jdbcType=DECIMAL},</if>
            <if test="totalCardPrice != null">#{totalCardPrice,jdbcType=DECIMAL},</if>
            <if test="totalCardAmount != null">#{totalCardAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据单号修改订单状态-->
            <update id="updateOrderStatusBySalesOrderNo" >
                    UPDATE /*MS-TP-PREPAY-CARD-SALES-ORDER-UPDATEORDERSTATUSBYSALESORDERNO*/ TP_PREPAY_CARD_SALES_ORDER
        SET
        order_status = #{orderStatus,jdbcType=TINYINT}
        WHERE
        is_del = 0
        AND sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
            </update>

            <!--根据销售单号修改销售单状态-->
            <update id="updateOrderBySalesOrderNo" >
                    UPDATE /*MS-TP-PREPAY-CARD-SALES-ORDER-UPDATEORDERBYSALESORDERNO*/ tp_prepay_card_sales_order
        SET order_status = #{orderStatus,jdbcType=TINYINT}
        <if test="deliveryTime != null and deliveryTime != 0">
            ,delivery_time = #{deliveryTime,jdbcType=INTEGER}
        </if>
        WHERE is_del = 0
        AND sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
            </update>
    </mapper>
