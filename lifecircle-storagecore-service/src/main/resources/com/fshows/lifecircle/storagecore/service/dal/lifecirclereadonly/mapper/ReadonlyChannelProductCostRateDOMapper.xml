<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyChannelProductCostRateDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyChannelProductCostRateDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXIST_MAX_FEE" property="existMaxFee" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_CODE" property="productCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_CHANNEL" property="paymentChannel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXIST_WITHDRAWAL_FEE" property="existWithdrawalFee" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAYMENT_CHANNEL_NAME" property="paymentChannelName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_TYPE" property="payType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FB_RATE" property="fbRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_FEE" property="maxFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_RATE" property="maxRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_FB_FEE" property="maxFbFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANNEL_RATE" property="channelRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MAX_CHANNEL_FEE" property="maxChannelFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FB_WITHDRAWAL_FEE" property="fbWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANNEL_WITHDRAWAL_FEE" property="channelWithdrawalFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXIST_MAX_FEE`,`PRODUCT_CODE`,`PAYMENT_CHANNEL`,`EXIST_WITHDRAWAL_FEE`,`PAYMENT_CHANNEL_NAME`,`PAY_TYPE`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`FB_RATE`,`MAX_FEE`,`MAX_RATE`,`MAX_FB_FEE`,`CHANNEL_RATE`,`MAX_CHANNEL_FEE`,`FB_WITHDRAWAL_FEE`,`CHANNEL_WITHDRAWAL_FEE`
    </sql>


            <!--insert:TP_CHANNEL_PRODUCT_COST_RATE-->
            <insert id="insert" >
            INSERT INTO TP_CHANNEL_PRODUCT_COST_RATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="existMaxFee != null">`EXIST_MAX_FEE`,</if>
        <if test="productCode != null">`PRODUCT_CODE`,</if>
        <if test="paymentChannel != null">`PAYMENT_CHANNEL`,</if>
        <if test="existWithdrawalFee != null">`EXIST_WITHDRAWAL_FEE`,</if>
        <if test="paymentChannelName != null">`PAYMENT_CHANNEL_NAME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="fbRate != null">`FB_RATE`,</if>
        <if test="maxFee != null">`MAX_FEE`,</if>
        <if test="maxRate != null">`MAX_RATE`,</if>
        <if test="maxFbFee != null">`MAX_FB_FEE`,</if>
        <if test="channelRate != null">`CHANNEL_RATE`,</if>
        <if test="maxChannelFee != null">`MAX_CHANNEL_FEE`,</if>
        <if test="fbWithdrawalFee != null">`FB_WITHDRAWAL_FEE`,</if>
        <if test="channelWithdrawalFee != null">`CHANNEL_WITHDRAWAL_FEE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="existMaxFee != null">#{existMaxFee,jdbcType=VARCHAR},</if>
        <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
        <if test="paymentChannel != null">#{paymentChannel,jdbcType=VARCHAR},</if>
        <if test="existWithdrawalFee != null">#{existWithdrawalFee,jdbcType=VARCHAR},</if>
        <if test="paymentChannelName != null">#{paymentChannelName,jdbcType=VARCHAR},</if>
        <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="fbRate != null">#{fbRate,jdbcType=DECIMAL},</if>
        <if test="maxFee != null">#{maxFee,jdbcType=DECIMAL},</if>
        <if test="maxRate != null">#{maxRate,jdbcType=DECIMAL},</if>
        <if test="maxFbFee != null">#{maxFbFee,jdbcType=DECIMAL},</if>
        <if test="channelRate != null">#{channelRate,jdbcType=DECIMAL},</if>
        <if test="maxChannelFee != null">#{maxChannelFee,jdbcType=DECIMAL},</if>
        <if test="fbWithdrawalFee != null">#{fbWithdrawalFee,jdbcType=DECIMAL},</if>
        <if test="channelWithdrawalFee != null">#{channelWithdrawalFee,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据支付通道查询-->
            <select id="findByPaymentChannel" resultMap="BaseResultMap">
                    select /*MS-TP-CHANNEL-PRODUCT-COST-RATE-FINDBYPAYMENTCHANNEL*/ <include refid="Base_Column_List" /> from tp_channel_product_cost_rate
        where payment_channel = #{paymentChannel,jdbcType=VARCHAR}
            </select>
    </mapper>
