<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.SinanActivityGoldMerchantDayDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityGoldMerchantDayDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="TRADE_DAY" property="tradeDay" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="TRADE_NUM" property="tradeNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="REFUND_NUM" property="refundNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="INCOME_TRADE_NUM" property="incomeTradeNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="TRADE_MONEY" property="tradeMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="INCOME_MONEY" property="incomeMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`MERCHANT_NO`,`MERCHANT_USERNAME`,`UID`,`AGENT_ID`,`PAY_TYPE`,`STORE_ID`,`TRADE_DAY`,`TRADE_NUM`,`REFUND_NUM`,`INCOME_TRADE_NUM`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`TRADE_MONEY`,`INCOME_MONEY`,`REFUND_MONEY`
    </sql>


    <!--根据商编号列表查询交易数据-->
    <select id="findListByMerchantNoList" resultMap="BaseResultMap">
        SELECT
        merchant_no merchantNo,
        SUM(income_trade_num) incomeTradeNum,
        SUM(income_money) incomeMoney
        FROM lm_sinan_activity_gold_merchant_day
        WHERE
        merchant_no IN
        <foreach close=")" collection="list" index="index" item="merchantNo" open="(" separator=",">
            #{merchantNo,jdbcType=VARCHAR}
        </foreach>
        AND trade_day <![CDATA[ >= ]]> #{startDay,jdbcType=INTEGER}
        AND trade_day <![CDATA[ <= ]]> #{endDay,jdbcType=INTEGER}
        AND pay_type IN (1, 2, 11, 12, 7, 21, 22, 5, 9, 91, 13, 23)
        GROUP BY merchant_no
    </select>
</mapper>
