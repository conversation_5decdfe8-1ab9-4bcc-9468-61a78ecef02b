<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.AlipayFaceScanActivityDOMapper">


    <select id="exportActivityMerchant"
            resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AlipayFaceScanActivityListResultDO"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.param.AlipayFaceScanActivityParamDO">
        select

        esn.init_sn equipmentSn,
        e.equipment_model equipmentModel,
        ifnull(fser.face_scan_dau, 0) faceScanDau,
        esnagent.username agentUsername,
        ifnull(esngrant.username, '') grantUsername,
        ifnull(fser.activity_status, 1) activityStatus,
        bindusers.id bindMerchantId,
        ifnull(bindusers.username, '') bindMerchantUsername,
        store.store_id bindStoreId,
        store.store_name bindStoreName,
        activityusers.id activityMerchantId,
        ifnull(activityusers.username, '') activityMerchantUsername,
        fser.activity_time activityTime

        from hw_equipment_sn esn
        left join tp_face_scan_equipment_record fser on esn.init_sn = fser.equipment_sn
        left join hw_equipment e on esn.equipment_id = e.id
        left join tp_users bindusers on esn.uid = bindusers.id
        left join tp_lifecircle_store store on esn.store_id = store.store_id
        left join tp_users activityusers on fser.uid = activityusers.id
        left join tp_user esnagent on esn.agent_id = esnagent.id
        left join tp_user esngrant on esn.grant_id = esngrant.id
        where e.equipment_type = 4
        and e.platform in(1,3)
        and esnagent.own_run = 1
        and esn.oem_id = 0
        and esn.sn_status = 5
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="merchantUsername != null and merchantUsername !=''">
            and activityusers.username like CONCAT(#{merchantUsername,jdbcType=VARCHAR},'%')
        </if>
        <if test="activityStatus != null and activityStatus != -1">
            and fser.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activityTimeStart != null">
            and fser.activity_time <![CDATA[ >= ]]> #{activityTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="activityTimeEnd != null">
            and fser.activity_time <![CDATA[ <= ]]> #{activityTimeEnd,jdbcType=TIMESTAMP}
        </if>
    </select>

</mapper>
