<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.mapper.PgPlatformShareOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.dataobject.PgPlatformShareOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TRADE_NO" property="tradeNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_TIME" property="shareTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TRADE_TYPE" property="tradeType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SETTLE_DATE" property="settleDate" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_REQ_NO" property="shareReqNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_STATE" property="shareState" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_DETAIL" property="shareDetail" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_REQ_NO" property="platformReqNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FEE_ATTRIBUTION" property="feeAttribution" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_REFUND_SN" property="platformRefundSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SHARE_STATE_REMARK" property="shareStateRemark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="FEE" property="fee" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="SHARE_PRICE" property="sharePrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="OTHER_SHARE_PRICE" property="otherSharePrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`ORDER_SN`,`TRADE_NO`,`REFUND_SN`,`SHARE_TIME`,`TRADE_TYPE`,`MERCHANT_NO`,`SETTLE_DATE`,`SHARE_REQ_NO`,`SHARE_STATE`,`SHARE_DETAIL`,`PLATFORM_REQ_NO`,`FEE_ATTRIBUTION`,`PLATFORM_REFUND_SN`,`SHARE_STATE_REMARK`,`CHANNEL`,`CREATE_TIME`,`UPDATE_TIME`,`FEE`,`SHARE_PRICE`,`OTHER_SHARE_PRICE`
        </sql>


        <!--insert:PG_PLATFORM_SHARE_ORDER-->
        <insert id="insert">
            INSERT INTO PG_PLATFORM_SHARE_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="tradeNo != null">`TRADE_NO`,</if>
                <if test="refundSn != null">`REFUND_SN`,</if>
                <if test="shareTime != null">`SHARE_TIME`,</if>
                <if test="tradeType != null">`TRADE_TYPE`,</if>
                <if test="merchantNo != null">`MERCHANT_NO`,</if>
                <if test="settleDate != null">`SETTLE_DATE`,</if>
                <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
                <if test="shareState != null">`SHARE_STATE`,</if>
            <if test="shareDetail != null">`SHARE_DETAIL`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="feeAttribution != null">`FEE_ATTRIBUTION`,</if>
            <if test="platformRefundSn != null">`PLATFORM_REFUND_SN`,</if>
            <if test="shareStateRemark != null">`SHARE_STATE_REMARK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="otherSharePrice != null">`OTHER_SHARE_PRICE`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
            <if test="shareTime != null">#{shareTime,jdbcType=VARCHAR},</if>
            <if test="tradeType != null">#{tradeType,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="shareState != null">#{shareState,jdbcType=VARCHAR},</if>
            <if test="shareDetail != null">#{shareDetail,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="feeAttribution != null">#{feeAttribution,jdbcType=VARCHAR},</if>
            <if test="platformRefundSn != null">#{platformRefundSn,jdbcType=VARCHAR},</if>
            <if test="shareStateRemark != null">#{shareStateRemark,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="otherSharePrice != null">#{otherSharePrice,jdbcType=DECIMAL},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        </trim>
        </insert>

        <!--批量数据插入-->
        <select id="batchInsert" resultMap="BaseResultMap">
            INSERT INTO PG_PLATFORM_SHARE_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                `TRADE_NO`,
                `REFUND_SN`,
                `SHARE_TIME`,
                `TRADE_TYPE`,
                `MERCHANT_NO`,
                `SETTLE_DATE`,
                `SHARE_REQ_NO`,
                `SHARE_STATE`,
                `SHARE_DETAIL`,
                `PLATFORM_REQ_NO`,
                `FEE_ATTRIBUTION`,
                `PLATFORM_REFUND_SN`,
                `SHARE_STATE_REMARK`,
                `FEE`,
                `SHARE_PRICE`,
                `OTHER_SHARE_PRICE`,
                `ORDER_SN`,
            </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.tradeNo,jdbcType=VARCHAR},
                #{item.refundSn,jdbcType=VARCHAR},
                #{item.shareTime,jdbcType=VARCHAR},
                #{item.tradeType,jdbcType=VARCHAR},
                #{item.merchantNo,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.shareReqNo,jdbcType=VARCHAR},
                #{item.shareState,jdbcType=VARCHAR},
                #{item.shareDetail,jdbcType=VARCHAR},
                #{item.platformReqNo,jdbcType=VARCHAR},
                #{item.feeAttribution,jdbcType=VARCHAR},
                #{item.platformRefundSn,jdbcType=VARCHAR},
                #{item.shareStateRemark,jdbcType=VARCHAR},
                #{item.fee,jdbcType=DECIMAL},
                #{item.sharePrice,jdbcType=DECIMAL},
                #{item.otherSharePrice,jdbcType=DECIMAL},
                #{item.orderSn,jdbcType=VARCHAR},
            </trim>
        </foreach>
        </select>
    </mapper>
