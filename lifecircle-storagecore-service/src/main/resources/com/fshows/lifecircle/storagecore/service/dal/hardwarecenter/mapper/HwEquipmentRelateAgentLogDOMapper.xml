<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentRelateAgentLogDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentRelateAgentLogDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="REMARK" property="remark" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CREATER" property="creater" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OEM_AFTER" property="oemAfter" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OEM_BEFORE" property="oemBefore" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BIZ_TYPE" property="bizType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="NEW_AGENT_ID" property="newAgentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="OLD_AGENT_ID" property="oldAgentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="RELATE_TYPE" property="relateType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>

        <resultMap id="SearchMapDo" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.RelateLogSearchMap">

                <result column="create_time" property="createTime" javaType="java.util.Date"/>

            <result column="remark" property="remark" javaType="java.lang.String"/>

            <result column="creater" property="creater" javaType="java.lang.String"/>

            <result column="oem_after" property="oemAfter" javaType="java.lang.String"/>

            <result column="oem_before" property="oemBefore" javaType="java.lang.String"/>

            <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

                <result column="new_agent_username" property="newAgentUsername" javaType="java.lang.String"/>

                <result column="old_agent_username" property="oldAgentUsername" javaType="java.lang.String"/>

                <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="relate_type" property="relateType" javaType="java.lang.Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
        `ID`,`REMARK`,`CREATER`,`OEM_AFTER`,`JOB_NUMBER`,`OEM_BEFORE`,`EQUIPMENT_SN`,`BIZ_TYPE`,`NEW_AGENT_ID`,`OLD_AGENT_ID`,`RELATE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_RELATE_AGENT_LOG-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_RELATE_AGENT_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
            <if test="remark != null">`remark`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="newAgentId != null">`NEW_AGENT_ID`,</if>
            <if test="oldAgentId != null">`OLD_AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="relateType != null">`RELATE_TYPE`,</if>
            <if test="oemBefore != null">`OEM_BEFORE`,</if>
            <if test="oemAfter != null">`OEM_AFTER`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=TINYINT},</if>
            <if test="newAgentId != null">#{newAgentId,jdbcType=INTEGER},</if>
            <if test="oldAgentId != null">#{oldAgentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="relateType != null">#{relateType,jdbcType=TINYINT},</if>
            <if test="oemBefore != null">#{oemBefore,jdbcType=VARCHAR},</if>
            <if test="oemAfter != null">#{oemAfter,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--分页查询-->
            <select id="getListBySearch" resultMap="SearchMapDo">
                    SELECT /*MS-HW-EQUIPMENT-RELATE-AGENT-LOG-GETLISTBYSEARCH*/  relog.equipment_sn,ouser.username as old_agent_username,nuser.username
                as
                new_agent_username,relog.biz_type,relog.creater,relog.create_time,relog.remark,relog.oem_before,relog.oem_after,relog.relate_type
                from hw_equipment_relate_agent_log relog LEFT JOIN tp_user ouser on ouser.id = relog.old_agent_id
        LEFT JOIN tp_user nuser on nuser.id = relog.new_agent_id
        where 1=1
        <if test="equipmentSn!=null and equipmentSn!=''">
            and relog.equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="bizType!=null and bizType!=-1">
            and relog.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="startTime!=null and endTime!=null">
            and relog.create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by relog.create_time desc
            </select>
    </mapper>
