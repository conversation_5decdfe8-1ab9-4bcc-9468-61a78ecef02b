<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopStockCostOrderDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopStockCostOrderDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="EQUIPMENT_MODEL" property="equipmentModel" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="STOCK_COST_DAY" property="stockCostDay" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="END_TERM_NUMBER" property="endTermNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="FIRST_TERM_NUMBER" property="firstTermNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="OUT_STORAGE_NUMBER" property="outStorageNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="PUT_STORAGE_NUMBER" property="putStorageNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="COST_ADJUST" property="costAdjust" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="END_TERM_PRICE" property="endTermPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="FIRST_TERM_PRICE" property="firstTermPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="OUT_STORAGE_PRICE" property="outStoragePrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="PUT_STORAGE_PRICE" property="putStoragePrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="END_TERM_UNIT_PRICE" property="endTermUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="FIRST_TERM_UNIT_PRICE" property="firstTermUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="OUT_STORAGE_UNIT_PRICE" property="outStorageUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="PUT_STORAGE_UNIT_PRICE" property="putStorageUnitPrice" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


        <sql id="Base_Column_List">
    `ID`,`EQUIPMENT_MODEL`,`IS_DEL`,`IS_TEST`,`EQUIPMENT_ID`,`STOCK_COST_DAY`,`END_TERM_NUMBER`,`FIRST_TERM_NUMBER`,`OUT_STORAGE_NUMBER`,`PUT_STORAGE_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`COST_ADJUST`,`END_TERM_PRICE`,`FIRST_TERM_PRICE`,`OUT_STORAGE_PRICE`,`PUT_STORAGE_PRICE`,`END_TERM_UNIT_PRICE`,`FIRST_TERM_UNIT_PRICE`,`OUT_STORAGE_UNIT_PRICE`,`PUT_STORAGE_UNIT_PRICE`
    </sql>


        <!--insert:HW_SHOP_STOCK_COST_ORDER-->
        <insert id="insert">
            INSERT INTO HW_SHOP_STOCK_COST_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="equipmentModel != null">`EQUIPMENT_MODEL`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="isTest != null">`IS_TEST`,</if>
                <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
                <if test="stockCostDay != null">`STOCK_COST_DAY`,</if>
                <if test="endTermNumber != null">`END_TERM_NUMBER`,</if>
                <if test="firstTermNumber != null">`FIRST_TERM_NUMBER`,</if>
                <if test="outStorageNumber != null">`OUT_STORAGE_NUMBER`,</if>
                <if test="putStorageNumber != null">`PUT_STORAGE_NUMBER`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="costAdjust != null">`COST_ADJUST`,</if>
                <if test="endTermPrice != null">`END_TERM_PRICE`,</if>
                <if test="firstTermPrice != null">`FIRST_TERM_PRICE`,</if>
                <if test="outStoragePrice != null">`OUT_STORAGE_PRICE`,</if>
                <if test="putStoragePrice != null">`PUT_STORAGE_PRICE`,</if>
                <if test="endTermUnitPrice != null">`END_TERM_UNIT_PRICE`,</if>
                <if test="firstTermUnitPrice != null">`FIRST_TERM_UNIT_PRICE`,</if>
                <if test="outStorageUnitPrice != null">`OUT_STORAGE_UNIT_PRICE`,</if>
                <if test="putStorageUnitPrice != null">`PUT_STORAGE_UNIT_PRICE`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="equipmentModel != null">#{equipmentModel,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="isTest != null">#{isTest,jdbcType=TINYINT},</if>
                <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="stockCostDay != null">#{stockCostDay,jdbcType=INTEGER},</if>
            <if test="endTermNumber != null">#{endTermNumber,jdbcType=INTEGER},</if>
            <if test="firstTermNumber != null">#{firstTermNumber,jdbcType=INTEGER},</if>
            <if test="outStorageNumber != null">#{outStorageNumber,jdbcType=INTEGER},</if>
            <if test="putStorageNumber != null">#{putStorageNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="costAdjust != null">#{costAdjust,jdbcType=DECIMAL},</if>
                <if test="endTermPrice != null">#{endTermPrice,jdbcType=DECIMAL},</if>
                <if test="firstTermPrice != null">#{firstTermPrice,jdbcType=DECIMAL},</if>
                <if test="outStoragePrice != null">#{outStoragePrice,jdbcType=DECIMAL},</if>
                <if test="putStoragePrice != null">#{putStoragePrice,jdbcType=DECIMAL},</if>
                <if test="endTermUnitPrice != null">#{endTermUnitPrice,jdbcType=DECIMAL},</if>
                <if test="firstTermUnitPrice != null">#{firstTermUnitPrice,jdbcType=DECIMAL},</if>
                <if test="outStorageUnitPrice != null">#{outStorageUnitPrice,jdbcType=DECIMAL},</if>
                <if test="putStorageUnitPrice != null">#{putStorageUnitPrice,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--根据条件查询库存成本单列表 pageCount-->
        <select id="findStockCostOrderListCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            (
            select
            equipment_id,
            equipment_model,
            SUM( `cost_adjust`) AS cost_adjust,
            SUM( `put_storage_number`) AS put_storage_number,
            SUM( `put_storage_price`) AS put_storage_price,
            SUM( `out_storage_number`) AS out_storage_number,
            SUM( `out_storage_price`) AS out_storage_price
            FROM `hw_shop_stock_cost_order`
            <where>
                is_del = 0
                <if test="list != null and list.size() &gt; 0">
                    AND equipment_id in
                    <foreach collection="list" index="index" item="equipmentId" open="(" close=")" separator=",">
                        #{equipmentId, jdbcType=INTEGER}
                    </foreach>
                </if>
                <if test="isTest != null">
                    AND is_test= #{isTest, jdbcType=INTEGER}
                </if>
                AND stock_cost_day <![CDATA[ >= ]]> #{startDay,jdbcType=INTEGER}
                AND stock_cost_day <![CDATA[ <= ]]> #{endDay,jdbcType=INTEGER}
            </where>
            GROUP BY `equipment_id`
            ) temp

        </select>
        <!--根据条件查询库存成本单列表 pageResult-->
        <select id="findStockCostOrderListResult" resultMap="BaseResultMap">
            select
            temp.equipment_id AS equipment_id,
            temp.equipment_model AS equipment_model,
            temp.cost_adjust AS cost_adjust,
            temp.put_storage_number AS put_storage_number,
            temp.put_storage_price AS put_storage_price,
            temp.out_storage_number AS out_storage_number,
            temp.out_storage_price AS out_storage_price
            from (
            select
            equipment_id,
            equipment_model,
            SUM( `cost_adjust`) AS cost_adjust,
            SUM( `put_storage_number`) AS put_storage_number,
            SUM( `put_storage_price`) AS put_storage_price,
            SUM( `out_storage_number`) AS out_storage_number,
            SUM( `out_storage_price`) AS out_storage_price
            FROM `hw_shop_stock_cost_order`
            <where>
                is_del = 0
                <if test="list != null and list.size() &gt; 0">
                    AND equipment_id in
                    <foreach collection="list" index="index" item="equipmentId" open="(" close=")" separator=",">
                        #{equipmentId, jdbcType=INTEGER}
                    </foreach>
                </if>
                <if test="isTest != null">
                    AND is_test= #{isTest, jdbcType=INTEGER}
                </if>
                AND stock_cost_day <![CDATA[ >= ]]> #{startDay,jdbcType=INTEGER}
                AND stock_cost_day <![CDATA[ <= ]]> #{endDay,jdbcType=INTEGER}
            </where>
            GROUP BY `equipment_id`
            ) temp
            ORDER BY out_storage_number desc , equipment_id desc
            limit #{startRow},#{limit}
        </select>

        <!--根据设备id和成本单生成日期查询指定日期的设备成本数据-->
        <select id="getOrderByStockCostDayAndEquipmentId" resultMap="BaseResultMap">
            select /*MS-HW-SHOP-STOCK-COST-ORDER-GETORDERBYSTOCKCOSTDAYANDEQUIPMENTID*/
            <include refid="Base_Column_List"/>
            from hw_shop_stock_cost_order
            where stock_cost_day = #{stockCostDay, jdbcType=INTEGER}
            and equipment_id = #{equipmentId, jdbcType=INTEGER}
            and is_del = 0
        </select>

        <!--查询指定日期的设备成本单数据列表-->
        <select id="findListByStockCostDay" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            HW_SHOP_STOCK_COST_ORDER
            WHERE
            `STOCK_COST_DAY` = #{stockCostDay,jdbcType=INTEGER}
            AND `IS_DEL` = 0
            AND `IS_TEST` IN (0,1)
            <if test="isTest != null">
                AND `IS_TEST` = #{isTest,jdbcType=TINYINT}
            </if>
        </select>
    </mapper>
