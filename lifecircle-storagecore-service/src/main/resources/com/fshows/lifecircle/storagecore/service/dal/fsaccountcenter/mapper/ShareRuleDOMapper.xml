<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.ShareRuleDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.ShareRuleDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="SHARE_MEMBER_ID" property="shareMemberId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ENABLE" property="enable" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SHARE_PORTION" property="sharePortion" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LAST_SHARE_PORTION" property="lastSharePortion" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`SHARE_MEMBER_ID`,`IS_DEL`,`ENABLE`,`STORE_ID`,`BEGIN_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`SHARE_PORTION`,`LAST_SHARE_PORTION`
    </sql>


            <!--insert:TP_SHARE_RULE-->
            <insert id="insert" >
                    INSERT INTO TP_SHARE_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="sharePortion != null">`SHARE_PORTION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="sharePortion != null">#{sharePortion,jdbcType=DECIMAL},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--批量插入-->
            <insert id="insertBatch" >
                    INSERT INTO tp_share_rule
        (
        share_member_id,store_id,share_portion
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shareMemberId,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=INTEGER},
            #{item.sharePortion,jdbcType=DECIMAL}
            )
        </foreach>
            </insert>

            <!--根据storeId查询生效的分账比例总和-->
            <select id="sumByStoreId" resultType="java.math.BigDecimal">
                    SELECT /*MS-TP-SHARE-RULE-SUMBYSTOREID*/  ifnull(sum(share_portion), 0)
        FROM
        tp_share_rule AS sr
        LEFT JOIN
        tp_share_member AS sm ON sr.share_member_id=sm.share_member_id
        WHERE sr.store_id = #{storeId,jdbcType=INTEGER}
        AND (sm.active = 'YES' OR sm.active = 'STOP')
        AND sm.group_id = #{groupId,jdbcType=VARCHAR}
        and sr.is_del = 0
            </select>

            <!--查询分账规则-->
            <select id="getShareRule" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        tp_share_rule
        WHERE store_id = #{storeId,jdbcType=INTEGER}
        and share_member_id = #{shareMemberId,jdbcType=VARCHAR}
        and is_del = 0
            </select>

            <!--删除分账规则数量-->
            <delete id="deleteRule" >
                    UPDATE /*MS-TP-SHARE-RULE-DELETERULE*/ tp_share_rule
        SET
        is_del = 1
        WHERE
        id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
            </delete>
    </mapper>
