<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BalanceAccountPermissionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BalanceAccountPermissionDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FAKE_CLOSE_STATE" property="fakeCloseState" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_SETTLE_TYPE" property="lastSettleType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_CYCLE_END" property="settleCycleEnd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_REJECT_MSG" property="shareRejectMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_CYCLE_START" property="settleCycleStart" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_SETTLE_CYCLE_END" property="lastSettleCycleEnd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_SETTLE_CYCLE_START" property="lastSettleCycleStart" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_PAY" property="sharePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WITHDRAW" property="withdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SHARE_MODE" property="shareMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_TYPE" property="shareType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_PERIOD" property="sharePeriod" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_STATUS" property="shareStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTO_WITHDRAW" property="autoWithdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HAS_WINDOWPOP" property="hasWindowpop" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_SHARE_PAY" property="lastSharePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_SHARE_TYPE" property="lastShareType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QUICK_WITHDRAW" property="quickWithdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPECIAL_SETTLE" property="specialSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="POSITION_REFUND" property="positionRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REALTIME_SETTLE" property="realtimeSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="D0_RATE_SET_STATUS" property="d0RateSetStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_CUSTOMER_NUM" property="shareCustomerNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BALANCE_SHARE_TODAY" property="balanceShareToday" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CUSTOMIZE_SHARE_MARK" property="customizeShareMark" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BUSINESS_VOLUME_DAILY" property="businessVolumeDaily" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HISTORY_QUICK_WITHDRAW" property="historyQuickWithdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TODAY_BALANCE_WITHDRAW" property="todayBalanceWithdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BALANCE_SHARE_IMMEDIATE_ACCOUNT" property="balanceShareImmediateAccount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="LAST_UPDATE_TIME" property="lastUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="LAST_SETTLE_CYCLE_END_TIME" property="lastSettleCycleEndTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PETTY_CASH" property="pettyCash" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SHARE_RATIO" property="shareRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="QUOT_MAX_ONCE" property="quotMaxOnce" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="QUOT_MIN_ONCE" property="quotMinOnce" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`SETTLE_TYPE`,`FAKE_CLOSE_STATE`,`LAST_SETTLE_TYPE`,`SETTLE_CYCLE_END`,`SHARE_REJECT_MSG`,`SETTLE_CYCLE_START`,`LAST_SETTLE_CYCLE_END`,`LAST_SETTLE_CYCLE_START`,`UID`,`BANK_TYPE`,`SHARE_PAY`,`WITHDRAW`,`BEGIN_TIME`,`SHARE_MODE`,`SHARE_TYPE`,`SETTLE_MODE`,`SHARE_PERIOD`,`SHARE_STATUS`,`AUTO_WITHDRAW`,`HAS_WINDOWPOP`,`LAST_SHARE_PAY`,`LAST_SHARE_TYPE`,`QUICK_WITHDRAW`,`SPECIAL_SETTLE`,`POSITION_REFUND`,`REALTIME_SETTLE`,`D0_RATE_SET_STATUS`,`SHARE_CUSTOMER_NUM`,`BALANCE_SHARE_TODAY`,`CUSTOMIZE_SHARE_MARK`,`BUSINESS_VOLUME_DAILY`,`HISTORY_QUICK_WITHDRAW`,`TODAY_BALANCE_WITHDRAW`,`BALANCE_SHARE_IMMEDIATE_ACCOUNT`,`CREATE_TIME`,`UPDATE_TIME`,`LAST_UPDATE_TIME`,`LAST_SETTLE_CYCLE_END_TIME`,`PETTY_CASH`,`SHARE_RATIO`,`QUOT_MAX_ONCE`,`QUOT_MIN_ONCE`
    </sql>


            <!--insert:TP_BALANCE_ACCOUNT_PERMISSION-->
            <insert id="insert" >
                    INSERT INTO TP_BALANCE_ACCOUNT_PERMISSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="withdraw != null">`WITHDRAW`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="quickWithdraw != null">`QUICK_WITHDRAW`,</if>
            <if test="autoWithdraw != null">`AUTOWITHDRAW`,</if>
            <if test="hasWindowpop != null">`HASWINDOWPOP`,</if>
            <if test="positionRefund != null">`POSITION_REFUND`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="withdraw != null">#{withdraw,jdbcType=TINYINT},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="quickWithdraw != null">#{quickWithdraw,jdbcType=TINYINT},</if>
            <if test="autoWithdraw != null">#{autoWithdraw,jdbcType=TINYINT},</if>
            <if test="hasWindowpop != null">#{hasWindowpop,jdbcType=TINYINT},</if>
            <if test="positionRefund != null">#{positionRefund,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据uid查询商户余额账户权限-->
            <select id="getByUid" resultMap="BaseResultMap">
                    select /*MS-TP-BALANCE-ACCOUNT-PERMISSION-GETBYUID*/ <include refid="Base_Column_List" /> from TP_BALANCE_ACCOUNT_PERMISSION where
        uid=#{uid,jdbcType=INTEGER}
        LIMIT 1
            </select>

            <!--根据token查询商户余额账户权限-->
            <select id="getByToken" resultMap="BaseResultMap">
                    select /*MS-TP-BALANCE-ACCOUNT-PERMISSION-GETBYTOKEN*/ <include refid="Base_Column_List" /> from TP_BALANCE_ACCOUNT_PERMISSION where
        token=#{token,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
