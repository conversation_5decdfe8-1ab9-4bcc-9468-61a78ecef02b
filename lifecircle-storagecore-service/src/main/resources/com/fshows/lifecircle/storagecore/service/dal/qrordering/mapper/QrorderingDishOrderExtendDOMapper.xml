<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingDishOrderExtendDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingDishOrderExtendDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT7" property="ext7" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT8" property="ext8" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT9" property="ext9" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT10" property="ext10" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DISH_LIST" property="dishList" jdbcType="LONGVARCHAR"
                    javaType="String"/>

            <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DISH_ORDER_NO" property="dishOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UPLOAD_DISH_LIST" property="uploadDishList" jdbcType="LONGVARCHAR"
                    javaType="String"/>

            <result column="ACTIVITY_DISH_LIST" property="activityDishList" jdbcType="LONGVARCHAR"
                    javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`EXT7`,`EXT8`,`EXT9`,`EXT10`,`DISH_LIST`,`PAY_ORDER_NO`,`DISH_ORDER_NO`,`UPLOAD_DISH_LIST`,`ACTIVITY_DISH_LIST`,`DEL_FLAG`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--insert:TP_QRORDERING_DISH_ORDER_EXTEND-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_DISH_ORDER_EXTEND
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="ext1 != null">`EXT1`,</if>
                <if test="ext2 != null">`EXT2`,</if>
                <if test="ext3 != null">`EXT3`,</if>
                <if test="ext4 != null">`EXT4`,</if>
                <if test="ext5 != null">`EXT5`,</if>
                <if test="ext6 != null">`EXT6`,</if>
                <if test="ext7 != null">`EXT7`,</if>
                <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="dishList != null">`DISH_LIST`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="uploadDishList != null">`UPLOAD_DISH_LIST`,</if>
            <if test="activityDishList != null">`ACTIVITY_DISH_LIST`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="dishList != null">#{dishList,jdbcType=LONGVARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="uploadDishList != null">#{uploadDishList,jdbcType=LONGVARCHAR},</if>
            <if test="activityDishList != null">#{activityDishList,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--根据订单号列表批量查询订单详情-->
        <select id="findByDishOrderNos" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            tp_qrordering_dish_order_extend
            WHERE
            dish_order_no in
            <foreach collection="list" item="dishOrderNo" separator="," open="(" close=")">
                #{dishOrderNo,jdbcType=VARCHAR}
            </foreach>
            AND del_flag = 0
        </select>

        <!--根据订单号列表批量查询订单详情-->
        <select id="findAllByDishOrderNos" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            tp_qrordering_dish_order_extend
            WHERE
            dish_order_no in
            <foreach collection="list" item="dishOrderNo" separator="," open="(" close=")">
                #{dishOrderNo,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
