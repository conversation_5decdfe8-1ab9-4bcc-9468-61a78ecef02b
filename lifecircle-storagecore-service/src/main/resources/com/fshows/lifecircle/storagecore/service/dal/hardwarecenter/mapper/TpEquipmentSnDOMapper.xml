<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentSnDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentSnDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SYSTEM_SN" property="systemSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LOST_TIME" property="lostTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SN_STATUS" property="snStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REJECT_TIME" property="rejectTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_MODE" property="cashierMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECEIVE_TIME" property="receiveTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RECOVER_TIME" property="recoverTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DAMAGE_STATUS" property="damageStatus" jdbcType="TINYINT"
        javaType="Integer"/>

    <result column="RECEIVE_STATUS" property="receiveStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="BUSINESS_STATUS" property="businessStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="DISTRIBUTE_TIME" property="distributeTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="PRINTER_SETTING" property="printerSetting" jdbcType="TINYINT"
            javaType="Integer"/>
</resultMap>

        <resultMap id="equipmentCountSumMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentCountSumMap">

                <result column="code_scan_num" property="codeScanNum" javaType="Integer"/>

                <result column="equipment_dau" property="equipmentDau" javaType="Integer"/>

                <result column="equipment_num" property="equipmentNum" javaType="Integer"/>

                <result column="transaction_num" property="transactionNum" javaType="Integer"/>

                <result column="face_transaction_num" property="faceTransactionNum" javaType="Integer"/>

                <result column="transaction_amount" property="transactionAmount" javaType="java.math.BigDecimal"/>

                <result column="face_transaction_rate" property="faceTransactionRate" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="equipmentListPageMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentListPageMap">

                <result column="init_sn" property="initSn" javaType="String"/>

                <result column="grant_name" property="grantName" javaType="String"/>

                <result column="store_name" property="storeName" javaType="String"/>

                <result column="merchant_name" property="merchantName" javaType="String"/>

                <result column="sn_id" property="snId" javaType="Integer"/>

                <result column="sn_status" property="snStatus" javaType="Integer"/>

                <result column="code_scan_num" property="codeScanNum" javaType="Integer"/>

                <result column="equipment_id" property="equipmentId" javaType="Integer"/>

                <result column="light_status" property="lightStatus" javaType="Integer"/>

                <result column="equipment_dau" property="equipmentDau" javaType="Integer"/>

                <result column="transaction_num" property="transactionNum" javaType="Integer"/>

                <result column="face_transaction_num" property="faceTransactionNum" javaType="Integer"/>

                <result column="light_time" property="lightTime" javaType="java.util.Date"/>

                <result column="transaction_money" property="transactionMoney" javaType="java.math.BigDecimal"/>

                <result column="face_transaction_rate" property="faceTransactionRate" javaType="java.math.BigDecimal"/>
        </resultMap>
    <resultMap id="AgentEquipmentListMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AgentEquipmentListMap">

        <result column="init_sn" property="initSn" javaType="String"/>

        <result column="grant_name" property="grantName" javaType="String"/>

        <result column="store_name" property="storeName" javaType="String"/>

        <result column="merchant_name" property="merchantName" javaType="String"/>

        <result column="bind_time" property="bindTime" javaType="Integer"/>

        <result column="sn_status" property="snStatus" javaType="Integer"/>

        <result column="equipment_id" property="equipmentId" javaType="Integer"/>
    </resultMap>

    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`SYSTEM_SN`,`UID`,`DEPOT`,`IS_DEL`,`AGENT_ID`,`GRANT_ID`,`STORE_ID`,`LOST_TIME`,`SN_STATUS`,`CASHIER_ID`,`CREATE_TIME`,`REJECT_TIME`,`UPDATE_TIME`,`CASHIER_MODE`,`EQUIPMENT_ID`,`RECEIVE_TIME`,`RECOVER_TIME`,`DAMAGE_STATUS`,`RECEIVE_STATUS`,`BUSINESS_STATUS`,`DISTRIBUTE_TIME`,`PRINTER_SETTING`
    </sql>


            <!--insert:TP_EQUIPMENT_SN-->
            <insert id="insert" >
                    INSERT INTO TP_EQUIPMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="systemSn != null">`SYSTEM_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="depot != null">`DEPOT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="lostTime != null">`LOST_TIME`,</if>
            <if test="snStatus != null">`SN_STATUS`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="rejectTime != null">`REJECT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cashierMode != null">`CASHIER_MODE`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="receiveTime != null">`RECEIVE_TIME`,</if>
            <if test="recoverTime != null">`RECOVER_TIME`,</if>
            <if test="damageStatus != null">`DAMAGE_STATUS`,</if>
            <if test="receiveStatus != null">`RECEIVE_STATUS`,</if>
            <if test="businessStatus != null">`BUSINESS_STATUS`,</if>
            <if test="distributeTime != null">`DISTRIBUTE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="systemSn != null">#{systemSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="depot != null">#{depot,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="lostTime != null">#{lostTime,jdbcType=INTEGER},</if>
            <if test="snStatus != null">#{snStatus,jdbcType=TINYINT},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="rejectTime != null">#{rejectTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="cashierMode != null">#{cashierMode,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="receiveTime != null">#{receiveTime,jdbcType=INTEGER},</if>
            <if test="recoverTime != null">#{recoverTime,jdbcType=INTEGER},</if>
            <if test="damageStatus != null">#{damageStatus,jdbcType=TINYINT},</if>
            <if test="receiveStatus != null">#{receiveStatus,jdbcType=TINYINT},</if>
            <if test="businessStatus != null">#{businessStatus,jdbcType=TINYINT},</if>
            <if test="distributeTime != null">#{distributeTime,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--根据设备sn码查询代理商id-->
            <select id="getAgentByInitSn" resultType="Integer">
                    SELECT /*MS-TP-EQUIPMENT-SN-GETAGENTBYINITSN*/  agent_id from tp_equipment_sn where is_del = 0 and init_sn = #{equipmentSn,jdbcType=VARCHAR}
            </select>

            <!--刷脸设备查看汇总统计-->
            <select id="getEquipmentCountSum" resultMap="equipmentCountSumMap">
                    select
        result.equipment_num,
        result.transaction_amount,
        result.equipment_dau,
        result.transaction_num,
        result.code_scan_num,
        round((result.transaction_num - result.code_scan_num) / result.transaction_num,4) face_transaction_rate,
        (result.transaction_num - result.code_scan_num) face_transaction_num
        from
        (
        SELECT
        count(a.init_sn) equipment_num,
        sum(h.transaction_money) transaction_amount,
        sum(h.equipment_dau) equipment_dau,
        sum(h.transaction_num) transaction_num,
        sum(h.code_scan_num) code_scan_num
        FROM
        tp_equipment_sn a
        INNER JOIN tp_equipment b ON b.id = a.equipment_id AND b.equipment_type = 4 AND b.is_del = 0
        <if test="equipmentSetId != null and equipmentSetId != -1">
            INNER JOIN tp_equipment_set c ON c.equipment_id = a.equipment_id
            AND c.id = #{equipmentSetId,jdbcType=INTEGER}
            AND c.start_time &lt; UNIX_TIMESTAMP( NOW( ) )
            AND c.is_del = 0
        </if>
        LEFT JOIN tp_users e ON a.uid = e.id
        LEFT JOIN tp_lifecircle_store f ON a.store_id = f.store_id
        INNER JOIN tp_user g ON a.agent_id = g.id AND g.own_run = 0 AND g.belong = 0 AND g.sub_config_id = 0
        LEFT JOIN tp_face_scan_equipment_record h ON a.init_sn = h.equipment_sn
        WHERE
        a.business_status = 1
        AND a.is_del = 0
        AND a.agent_id != 0
        <if test="initSn != null and initSn !=''">
            AND a.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="snOneStatus != null">
            AND a.sn_status = #{snOneStatus,jdbcType=INTEGER}
        </if>
        <if test="snTwoStatus != null">
            AND a.sn_status != #{snTwoStatus,jdbcType=INTEGER}
        </if>
        <if test="agentName != null and agentName != ''">
            AND g.username LIKE CONCAT(#{agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="lightStartTime != null and lightStartTime != '' and lightEndTime != null and lightEndTime != ''">
            AND h.light_time &gt;= #{lightStartTime,jdbcType=VARCHAR}
            AND h.light_time &lt;= #{lightEndTime,jdbcType=VARCHAR}
            AND h.light_status = 1
        </if>
        ) result
            </select>

            <!--刷脸设备查看分页列表-->
            <select id="getListPageEquipment" resultMap="equipmentListPageMap">
                    select
        result.*,
        round(( result.transaction_num - result.code_scan_num )/result.transaction_num,4) face_transaction_rate,
        ( result.transaction_num - result.code_scan_num ) face_transaction_num
        from
        (
        SELECT
        a.equipment_id,
        a.id sn_id,
        a.init_sn,
        a.sn_status,
        e.username merchant_name,
        f.store_name,
        g.username grant_name,
        h.transaction_num,
        h.code_scan_num,
        h.equipment_dau,
        h.transaction_money,
        h.light_time,
        h.light_status
        FROM
        tp_equipment_sn a
        INNER JOIN tp_equipment b ON b.id = a.equipment_id AND b.equipment_type = 4 AND b.is_del = 0
        <if test="equipmentSetId != null and equipmentSetId != -1">
            INNER JOIN tp_equipment_set c ON c.equipment_id = a.equipment_id
            AND c.id = #{equipmentSetId,jdbcType=INTEGER}
            AND c.start_time &lt; UNIX_TIMESTAMP( NOW( ) )
            AND c.is_del = 0
        </if>
        LEFT JOIN tp_users e ON a.uid = e.id
        LEFT JOIN tp_lifecircle_store f ON a.store_id = f.store_id
        INNER JOIN tp_user g ON a.agent_id = g.id AND g.own_run = 0 AND g.belong = 0 AND g.sub_config_id = 0
        LEFT JOIN tp_face_scan_equipment_record h ON a.init_sn = h.equipment_sn
        WHERE
        a.business_status = 1
        AND a.is_del = 0
        AND a.agent_id != 0
        <if test="initSn != null and initSn !=''">
            AND a.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="snOneStatus != null">
            AND a.sn_status = #{snOneStatus,jdbcType=INTEGER}
        </if>
        <if test="snTwoStatus != null">
            AND a.sn_status != #{snTwoStatus,jdbcType=INTEGER}
        </if>
        <if test="agentName != null and agentName != ''">
            AND g.username LIKE CONCAT(#{agentName,jdbcType=VARCHAR},'%')
        </if>
                <if test="lightStartTime != null and lightStartTime != '' and lightEndTime != null and lightEndTime != ''">
                    AND h.light_time &gt;= #{lightStartTime,jdbcType=VARCHAR}
                    AND h.light_time &lt;= #{lightEndTime,jdbcType=VARCHAR}
                    AND h.light_status = 1
                </if>
                ) result
            </select>

    <!--代理商查询设备列表-->
    <select id="agentEquipmentListQuery" resultMap="AgentEquipmentListMap">
        SELECT /*MS-TP-EQUIPMENT-SN-AGENTEQUIPMENTLISTQUERY*/ esn.equipment_id,esn.init_sn,esn.sn_status,tusers.username
        merchant_name,tuser.username
        grant_name,store.store_name,bind.bind_time
        from tp_equipment_sn esn
        LEFT JOIN tp_user tuser on esn.grant_id = tuser.id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        LEFT JOIN tp_users tusers on tusers.id = esn.uid
        LEFT JOIN tp_equipment_bind bind on esn.id = bind.sn_id and bind.bind_status = 1 and bind.is_close = 0
        where
        esn.agent_id = #{agentId,jdbcType=INTEGER}
        <if test="bindStartTime != null and bindEndTime != null">
            and bind.bind_time BETWEEN #{bindStartTime,jdbcType=INTEGER} AND #{bindEndTime,jdbcType=INTEGER}
        </if>
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="snStatus != null and snStatus == 5">
            and esn.sn_status = #{snStatus,jdbcType=INTEGER}
        </if>
        <if test="snStatus != null and snStatus == 0">
            and esn.sn_status not in (1,3,5)
        </if>
        <if test="snStatus != null and snStatus == -1">
            and esn.sn_status not in (1,3)
        </if>
        <if test="grantId != null and grantId != -1">
            and esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        and esn.is_del = 0
        order by esn.id desc
    </select>

    <!--代理商根据设备型号查询设备列表-->
    <select id="agentEquipmentListQueryWithModel" resultMap="AgentEquipmentListMap">
        SELECT /*MS-TP-EQUIPMENT-SN-AGENTEQUIPMENTLISTQUERYWITHMODEL*/
        esn.equipment_id,esn.init_sn,esn.sn_status,tusers.username merchant_name,
        tuser.username grant_name,store.store_name,bind.bind_time
        from tp_equipment_sn esn
        LEFT JOIN tp_user tuser on esn.grant_id = tuser.id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        LEFT JOIN tp_users tusers on tusers.id = esn.uid
        LEFT JOIN tp_equipment_set eset on eset.equipment_id = esn.equipment_id
        LEFT JOIN tp_equipment_bind bind on esn.id = bind.sn_id and bind.bind_status = 1 and bind.is_close = 0
        where
        esn.agent_id = #{agentId,jdbcType=INTEGER}
        <if test="bindStartTime != null and bindEndTime != null">
            and bind.bind_time BETWEEN #{bindStartTime,jdbcType=INTEGER} AND #{bindEndTime,jdbcType=INTEGER}
        </if>
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="snStatus != null and snStatus == 5">
            and esn.sn_status = #{snStatus,jdbcType=INTEGER}
        </if>
        <if test="snStatus != null and snStatus == 0">
            and esn.sn_status not in (1,3,5)
        </if>
        <if test="snStatus != null and snStatus == -1">
            and esn.sn_status not in (1,3)
        </if>
        <if test="grantId != null and grantId != -1">
            and esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        and eset.id in
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
        and esn.is_del = 0
        order by esn.id desc
    </select>

            <!--根据initSn列表查询-->
            <select id="getByInitSnList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-EQUIPMENT-SN-GETBYINITSNLIST*/  <include refid="Base_Column_List" /> FROM tp_equipment_sn
        WHERE init_sn in
        <foreach collection="list" open="(" close=")" item="sn" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        and is_del = 0
            </select>
    </mapper>
