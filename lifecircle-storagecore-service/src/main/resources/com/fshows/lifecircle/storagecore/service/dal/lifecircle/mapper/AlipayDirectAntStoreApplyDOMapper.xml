<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AlipayDirectAntStoreApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AlipayDirectAntStoreApplyDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="TEL" property="tel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHOP_ID" property="shopId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_ID" property="orderId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IP_ROLE_ID" property="ipRoleId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHOP_TYPE" property="shopType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_NUMBER" property="applyNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHOP_CATEGORY" property="shopCategory" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHOP_STATUS" property="shopStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UNITY_CATEGORY_ID" property="unityCategoryId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TEL`,`SHOP_ID`,`ADDRESS`,`ORDER_ID`,`AREA_CODE`,`AREA_NAME`,`CITY_CODE`,`CITY_NAME`,`IP_ROLE_ID`,`SHOP_TYPE`,`STORE_NAME`,`APPLY_NUMBER`,`CATEGORY_NAME`,`PROVINCE_CODE`,`PROVINCE_NAME`,`REJECT_REASON`,`SHOP_CATEGORY`,`UID`,`IS_DEL`,`SHOP_STATUS`,`UNITY_CATEGORY_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--根据商户ID获取蚂蚁门店进件申请单-->
            <select id="getOneByUid" resultMap="BaseResultMap">
                    SELECT /*MS-TP-ALIPAY-DIRECT-ANT-STORE-APPLY-GETONEBYUID*/  <include refid="Base_Column_List" /> FROM TP_ALIPAY_DIRECT_ANT_STORE_APPLY WHERE uid = #{uid,jdbcType=INTEGER} AND is_del = 0 ORDER BY ID
        DESC LIMIT 1
            </select>

            <!--根据申请单ID和Uid以及申请单ID获取蚂蚁门店进件申请单-->
            <select id="getOneByIdAndUidAndApplyNumber" resultMap="BaseResultMap">
                    SELECT /*MS-TP-ALIPAY-DIRECT-ANT-STORE-APPLY-GETONEBYIDANDUIDANDAPPLYNUMBER*/  <include refid="Base_Column_List" /> FROM TP_ALIPAY_DIRECT_ANT_STORE_APPLY WHERE id = #{id,jdbcType=INTEGER} AND uid =
        #{uid,jdbcType=INTEGER} AND apply_number = #{applyNumber,jdbcType=VARCHAR} AND is_del = 0 LIMIT 1
            </select>
    </mapper>
