<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmAlipayTaskEmunDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmAlipayTaskEmunDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="REASON_DESC" property="reasonDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON_TYPE" property="reasonType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON_VALUE" property="reasonValue" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON_TYPE_DESC" property="reasonTypeDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_TYPE" property="businessType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REASON_DESC`,`REASON_TYPE`,`REASON_VALUE`,`REASON_TYPE_DESC`,`BUSINESS_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_ALIPAY_TASK_EMUN-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_ALIPAY_TASK_EMUN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reasonDesc != null">`REASON_DESC`,</if>
            <if test="reasonType != null">`REASON_TYPE`,</if>
            <if test="reasonValue != null">`REASON_VALUE`,</if>
            <if test="reasonTypeDesc != null">`REASON_TYPE_DESC`,</if>
            <if test="businessType != null">`BUSINESS_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="reasonDesc != null">#{reasonDesc,jdbcType=VARCHAR},</if>
            <if test="reasonType != null">#{reasonType,jdbcType=VARCHAR},</if>
            <if test="reasonValue != null">#{reasonValue,jdbcType=VARCHAR},</if>
            <if test="reasonTypeDesc != null">#{reasonTypeDesc,jdbcType=VARCHAR},</if>
            <if test="businessType != null">#{businessType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--findByBusinessType-->
            <select id="findByBusinessType" resultMap="BaseResultMap">
                    SELECT /*MS-LM-CRM-ALIPAY-TASK-EMUN-FINDBYBUSINESSTYPE*/  <include refid="Base_Column_List" />
        FROM LM_CRM_ALIPAY_TASK_EMUN
        WHERE BUSINESS_TYPE = #{businessType,jdbcType=TINYINT}
            </select>
    </mapper>
