<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ProfitShareDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ProfitShareDetailDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="FEE" property="fee" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="AMOUNT" property="amount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NAME" property="cardName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WALLET_ID" property="walletId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_TASK_ID" property="outTaskId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRANSFER_NO" property="transferNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_NO" property="withdrawNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AUTO_WITHDRAW" property="autoWithdraw" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RULE_RECORD_ID" property="ruleRecordId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACC_MERCHANT_ID" property="accMerchantId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_TRANSFER_NO" property="outTransferNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_WITHDRAW_NO" property="outWithdrawNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_CODE" property="settleBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NO" property="settleAccountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NAME" property="settleAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_BATCH_ID" property="profitShareBatchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_DETAIL_ID" property="profitShareDetailId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_DETAIL_STATUS" property="profitShareDetailStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_MERCHANT_TYPE" property="profitShareMerchantType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_WITHDRAW_STATUS" property="profitShareWithdrawStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PROFIT_SHARE_CREATE_TIME" property="profitShareCreateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PROFIT_SHARE_FINISH_TIME" property="profitShareFinishTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PROFIT_SHARE_WITHDRAW_TIME" property="profitShareWithdrawTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SERVICE_FEE_RATIO" property="serviceFeeRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="PROFIT_SHARE_RATIO" property="profitShareRatio" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FEE`,`AMOUNT`,`SP_ID`,`CARD_NO`,`REMARK`,`BANK_NAME`,`CARD_NAME`,`WALLET_ID`,`ACCOUNT_ID`,`OUT_TASK_ID`,`OPERATOR_ID`,`TRANSFER_NO`,`WITHDRAW_NO`,`AUTO_WITHDRAW`,`RULE_RECORD_ID`,`ACC_MERCHANT_ID`,`OUT_TRANSFER_NO`,`OUT_WITHDRAW_NO`,`SETTLE_BANK_CODE`,`SETTLE_ACCOUNT_NO`,`SETTLE_ACCOUNT_NAME`,`PROFIT_SHARE_BATCH_ID`,`PROFIT_SHARE_DETAIL_ID`,`PROFIT_SHARE_DETAIL_STATUS`,`PROFIT_SHARE_MERCHANT_TYPE`,`PROFIT_SHARE_WITHDRAW_STATUS`,`DEL_FLAG`,`SETTLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`PROFIT_SHARE_CREATE_TIME`,`PROFIT_SHARE_FINISH_TIME`,`PROFIT_SHARE_WITHDRAW_TIME`,`SERVICE_FEE_RATIO`,`PROFIT_SHARE_RATIO`
    </sql>


            <!--insert:ACC_PROFIT_SHARE_DETAIL-->
            <insert id="insert" >
                INSERT INTO ACC_PROFIT_SHARE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="amount != null">`AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="cardName != null">`CARD_NAME`,</if>
        <if test="walletId != null">`WALLET_ID`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="outTaskId != null">`OUT_TASK_ID`,</if>
        <if test="operatorId != null">`OPERATOR_ID`,</if>
        <if test="transferNo != null">`TRANSFER_NO`,</if>
        <if test="withdrawNo != null">`WITHDRAW_NO`,</if>
        <if test="autoWithdraw != null">`AUTO_WITHDRAW`,</if>
        <if test="accMerchantId != null">`ACC_MERCHANT_ID`,</if>
        <if test="outTransferNo != null">`OUT_TRANSFER_NO`,</if>
        <if test="outWithdrawNo != null">`OUT_WITHDRAW_NO`,</if>
        <if test="settleBankCode != null">`SETTLE_BANK_CODE`,</if>
        <if test="settleAccountNo != null">`SETTLE_ACCOUNT_NO`,</if>
        <if test="settleAccountName != null">`SETTLE_ACCOUNT_NAME`,</if>
        <if test="profitShareBatchId != null">`PROFIT_SHARE_BATCH_ID`,</if>
        <if test="profitShareDetailId != null">`PROFIT_SHARE_DETAIL_ID`,</if>
        <if test="profitShareDetailStatus != null">`PROFIT_SHARE_DETAIL_STATUS`,</if>
        <if test="profitShareMerchantType != null">`PROFIT_SHARE_MERCHANT_TYPE`,</if>
        <if test="profitShareWithdrawStatus != null">`PROFIT_SHARE_WITHDRAW_STATUS`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="profitShareCreateTime != null">`PROFIT_SHARE_CREATE_TIME`,</if>
        <if test="profitShareFinishTime != null">`PROFIT_SHARE_FINISH_TIME`,</if>
        <if test="profitShareWithdrawTime != null">`PROFIT_SHARE_WITHDRAW_TIME`,</if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fee != null">#{fee,jdbcType=BIGINT},</if>
        <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
        <if test="walletId != null">#{walletId,jdbcType=VARCHAR},</if>
        <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
        <if test="outTaskId != null">#{outTaskId,jdbcType=VARCHAR},</if>
        <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
        <if test="transferNo != null">#{transferNo,jdbcType=VARCHAR},</if>
        <if test="withdrawNo != null">#{withdrawNo,jdbcType=VARCHAR},</if>
        <if test="autoWithdraw != null">#{autoWithdraw,jdbcType=VARCHAR},</if>
        <if test="accMerchantId != null">#{accMerchantId,jdbcType=VARCHAR},</if>
        <if test="outTransferNo != null">#{outTransferNo,jdbcType=VARCHAR},</if>
        <if test="outWithdrawNo != null">#{outWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="settleBankCode != null">#{settleBankCode,jdbcType=VARCHAR},</if>
        <if test="settleAccountNo != null">#{settleAccountNo,jdbcType=VARCHAR},</if>
        <if test="settleAccountName != null">#{settleAccountName,jdbcType=VARCHAR},</if>
        <if test="profitShareBatchId != null">#{profitShareBatchId,jdbcType=VARCHAR},</if>
        <if test="profitShareDetailId != null">#{profitShareDetailId,jdbcType=VARCHAR},</if>
        <if test="profitShareDetailStatus != null">#{profitShareDetailStatus,jdbcType=VARCHAR},</if>
        <if test="profitShareMerchantType != null">#{profitShareMerchantType,jdbcType=VARCHAR},</if>
        <if test="profitShareWithdrawStatus != null">#{profitShareWithdrawStatus,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareCreateTime != null">#{profitShareCreateTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareFinishTime != null">#{profitShareFinishTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareWithdrawTime != null">#{profitShareWithdrawTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--列表分页查询 pageCount-->
            <select id="pageProfitShareOrderDetailCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM

        `acc_profit_share_detail` a
        LEFT JOIN `acc_merchant` b ON a.`acc_merchant_id` = b.`acc_merchant_id`
        WHERE
        a.`out_task_id` = #{outTaskId,jdbcType=VARCHAR}
        and a.del_flag = 1
            </select>
            <!--列表分页查询 pageResult-->
            <select id="pageProfitShareOrderDetailResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.acctbiz.ProfitShareDetailDTO">
                    SELECT
        a.`profit_share_detail_id` AS profitShareDetailId,
        a.`amount` AS amount,
        a.`create_time` AS createTime,
        a.`account_id` AS accountId,
        b.`merchant_name` AS merchantName,
        a.`profit_share_merchant_type` AS profitShareMerchantType,
        a.actual_amount AS actualAmount
        FROM
        `acc_profit_share_detail` a
        LEFT JOIN `acc_merchant` b ON a.`acc_merchant_id` = b.`acc_merchant_id`
        WHERE
        a.`out_task_id` = #{outTaskId,jdbcType=VARCHAR}
        and a.del_flag = 1
            limit #{startRow},#{limit}
            </select>

            <!--根据外部补单单号获取分账详情-->
            <select id="getLimitOneByOutTaskId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        ACC_PROFIT_SHARE_DETAIL
        WHERE
        out_task_id = #{outTaskId,jdbcType=VARCHAR}
        AND DEL_FLAG = 1
        limit 1
            </select>
    </mapper>
