<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.ReceiptOrderExtendDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.ReceiptOrderExtendDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT2" property="ext2" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT3" property="ext3" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="NICK_NAME" property="nickName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AVATAR_URL" property="avatarUrl" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FORM_VALUE" property="formValue" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RECEIPT_FORM" property="receiptForm" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`EXT1`,`EXT2`,`EXT3`,`ORDER_NO`,`NICK_NAME`,`AVATAR_URL`,`FORM_VALUE`,`PAY_ORDER_NO`,`RECEIPT_FORM`,`DEL_FLAG`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_RECEIPT_ORDER_EXTEND-->
    <insert id="insert">
        INSERT INTO TP_RECEIPT_ORDER_EXTEND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="nickName != null">`NICK_NAME`,</if>
            <if test="avatarUrl != null">`AVATAR_URL`,</if>
            <if test="formValue != null">`FORM_VALUE`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="receiptForm != null">`RECEIPT_FORM`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="nickName != null">#{nickName,jdbcType=VARCHAR},</if>
            <if test="avatarUrl != null">#{avatarUrl,jdbcType=VARCHAR},</if>
            <if test="formValue != null">#{formValue,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--收款单订单扩展列表-->
    <select id="findReceiptOrderExtendLists" resultMap="BaseResultMap">
        select /*MS-TP-RECEIPT-ORDER-EXTEND-FINDRECEIPTORDEREXTENDLISTS*/ ORDER_NO,RECEIPT_FORM from
        TP_RECEIPT_ORDER_EXTEND
        where order_no in
        <foreach collection="list" item="orderNo" open="(" close=")" separator=",">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
