<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.MerchantActivityApplyExtDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.MerchantActivityApplyExtDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="APPLY_NO" property="applyNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="APPLY_PIC_ATTRIBUTE" property="applyPicAttribute" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="MERCHANT_ATTRIBUTE" property="merchantAttribute" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="APPLY_INFO_ATTRIBUTE" property="applyInfoAttribute" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`EXT1`,`APPLY_NO`,`APPLY_PIC_ATTRIBUTE`,`MERCHANT_ATTRIBUTE`,`APPLY_INFO_ATTRIBUTE`,`UID`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_MERCHANT_ACTIVITY_APPLY_EXT-->
    <insert id="insert">
        INSERT INTO LM_MERCHANT_ACTIVITY_APPLY_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="applyNo != null">`APPLY_NO`,</if>
            <if test="applyPicAttribute != null">`APPLY_PIC_ATTRIBUTE`,</if>
            <if test="merchantAttribute != null">`MERCHANT_ATTRIBUTE`,</if>
            <if test="applyInfoAttribute != null">`APPLY_INFO_ATTRIBUTE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="applyNo != null">#{applyNo,jdbcType=VARCHAR},</if>
            <if test="applyPicAttribute != null">#{applyPicAttribute,jdbcType=LONGVARCHAR},</if>
            <if test="merchantAttribute != null">#{merchantAttribute,jdbcType=VARCHAR},</if>
            <if test="applyInfoAttribute != null">#{applyInfoAttribute,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--getMerchantActivityApplyExtByApplyNo-->
    <select id="getMerchantActivityApplyExtByApplyNo" resultMap="BaseResultMap">
        SELECT /*MS-LM-MERCHANT-ACTIVITY-APPLY-EXT-GETMERCHANTACTIVITYAPPLYEXTBYAPPLYNO*/
        <include refid="Base_Column_List"/>
        FROM lm_merchant_activity_apply_ext
        WHERE apply_no = #{applyNo,jdbcType=VARCHAR}
        LIMIT 1;
    </select>
</mapper>
