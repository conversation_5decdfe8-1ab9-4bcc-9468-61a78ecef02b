<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpFaceScanEquipmentRecordDetailDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpFaceScanEquipmentRecordDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CODE_SCAN_NUM" property="codeScanNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="FACE_SCAN_DAU" property="faceScanDau" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EQUIPMENT_DAU" property="equipmentDau" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="TRANSACTION_NUM" property="transactionNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TRANSACTION_TIME" property="transactionTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TRANSACTION_MONEY" property="transactionMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CREATE_BY`,`UPDATE_BY`,`EQUIPMENT_SN`,`AGENT_ID`,`CODE_SCAN_NUM`,`FACE_SCAN_DAU`,`EQUIPMENT_DAU`,`TRANSACTION_NUM`,`CREATE_TIME`,`UPDATE_TIME`,`TRANSACTION_TIME`,`TRANSACTION_MONEY`
    </sql>


            <!--insert:TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL-->
            <insert id="insert" >
            INSERT INTO TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="createBy != null">`CREATE_BY`,</if>
        <if test="updateBy != null">`UPDATE_BY`,</if>
        <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="codeScanNum != null">`CODE_SCAN_NUM`,</if>
        <if test="equipmentDau != null">`EQUIPMENT_DAU`,</if>
        <if test="faceScanDau != null">`FACE_SCAN_DAU`,</if>
        <if test="transactionNum != null">`TRANSACTION_NUM`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="transactionTime != null">`TRANSACTION_TIME`,</if>
        <if test="transactionMoney != null">`TRANSACTION_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
        <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
        <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="codeScanNum != null">#{codeScanNum,jdbcType=INTEGER},</if>
        <if test="equipmentDau != null">#{equipmentDau,jdbcType=INTEGER},</if>
        <if test="faceScanDau != null">#{faceScanDau,jdbcType=INTEGER},</if>
        <if test="transactionNum != null">#{transactionNum,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionTime != null">#{transactionTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionMoney != null">#{transactionMoney,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

        <!--根据日期，设备查询记录-->
        <select id="getByEquipmentTime" resultType="Integer">
            select /*MS-TP-FACE-SCAN-EQUIPMENT-RECORD-DETAIL-GETBYEQUIPMENTTIME*/ count(*) from
            tp_face_scan_equipment_record_detail
            where equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
            and transaction_time = #{transactionTime,jdbcType=TIMESTAMP}
        </select>
    </mapper>
