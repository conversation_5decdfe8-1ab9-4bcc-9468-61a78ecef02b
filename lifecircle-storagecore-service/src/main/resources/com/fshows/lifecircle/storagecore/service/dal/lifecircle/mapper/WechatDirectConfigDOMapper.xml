<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.WechatDirectConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.WechatDirectConfigDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="HANDLER" property="handler" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_NAME" property="subName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_APPID" property="subAppid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPSECRET" property="appsecret" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_AUTO_CONFIG" property="isAutoConfig" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FIRST_OPEN_TIME" property="firstOpenTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`HANDLER`,`SUB_NAME`,`SUB_APPID`,`SUB_MCH_ID`,`APPSECRET`,`UID`,`TYPE`,`STATUS`,`BEGIN_TIME`,`CHANNEL_ID`,`IS_AUTO_CONFIG`,`FIRST_OPEN_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_WECHAT_DIRECT_CONFIG-->
            <insert id="insert" >
                    INSERT INTO TP_WECHAT_DIRECT_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="handler != null">`HANDLER`,</if>
            <if test="subName != null">`SUB_NAME`,</if>
            <if test="subAppid != null">`SUB_APPID`,</if>
            <if test="subMchId != null">`SUB_MCH_ID`,</if>
            <if test="appsecret != null">`APPSECRET`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="handler != null">#{handler,jdbcType=VARCHAR},</if>
            <if test="subName != null">#{subName,jdbcType=VARCHAR},</if>
            <if test="subAppid != null">#{subAppid,jdbcType=VARCHAR},</if>
            <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
            <if test="appsecret != null">#{appsecret,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据子商户id查询商户-->
            <select id="getWechatByMchId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-WECHAT-DIRECT-CONFIG-GETWECHATBYMCHID*/  <include refid="Base_Column_List" />
        FROM TP_WECHAT_DIRECT_CONFIG
        WHERE
        sub_mch_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and type = 2
            </select>

            <!--insert:TP_WECHAT_DIRECT_CONFIG 批量插入-->
            <insert id="insertBatch" >
                    INSERT INTO TP_WECHAT_DIRECT_CONFIG(
        HANDLER
        ,SUB_MCH_ID
        ,UID
        ,TYPE
        ,STATUS
        ,CHANNEL_ID
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.handler,jdbcType=VARCHAR}
            , #{item.subMchId,jdbcType=VARCHAR}
            , #{item.uid,jdbcType=INTEGER}
            , #{item.type,jdbcType=TINYINT}
            , #{item.status,jdbcType=TINYINT}
            , #{item.channelId,jdbcType=INTEGER}
            )
        </foreach>
            </insert>
    </mapper>
