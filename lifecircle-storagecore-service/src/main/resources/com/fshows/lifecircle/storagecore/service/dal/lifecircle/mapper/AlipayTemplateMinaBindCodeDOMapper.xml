<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AlipayTemplateMinaBindCodeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AlipayTemplateMinaBindCodeDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CODE_URL" property="codeUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_ROUTE_GROUP" property="alipayRouteGroup" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_TEMPLATE_URL" property="alipayTemplateUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CODE_TYPE" property="codeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_STATUS" property="bindStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REASON`,`CODE_URL`,`ALIPAY_ROUTE_GROUP`,`ALIPAY_TEMPLATE_URL`,`DEL_FLAG`,`STORE_ID`,`CODE_TYPE`,`BIND_STATUS`,`MERCHANT_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_ALIPAY_TEMPLATE_MINA_BIND_CODE-->
            <insert id="insert" >
                    INSERT INTO TP_ALIPAY_TEMPLATE_MINA_BIND_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="codeUrl != null">`CODE_URL`,</if>
            <if test="alipayRouteGroup != null">`ALIPAY_ROUTE_GROUP`,</if>
            <if test="alipayTemplateUrl != null">`ALIPAY_TEMPLATE_URL`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="codeUrl != null">#{codeUrl,jdbcType=VARCHAR},</if>
            <if test="alipayRouteGroup != null">#{alipayRouteGroup,jdbcType=VARCHAR},</if>
            <if test="alipayTemplateUrl != null">#{alipayTemplateUrl,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=TINYINT},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--解绑二维码绑定状态-->
            <select id="unbindById" resultMap="BaseResultMap">
                    UPDATE tp_alipay_template_mina_bind_code
        SET del_flag = 1
        WHERE alipay_route_group = #{alipayRouteGroup,jdbcType=VARCHAR}
            </select>
    </mapper>
