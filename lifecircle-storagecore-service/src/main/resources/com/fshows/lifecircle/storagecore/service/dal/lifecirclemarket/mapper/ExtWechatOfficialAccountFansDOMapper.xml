<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtWechatOfficialAccountFansDOMapper">

    <!--公众号TAB-分页列表-->
    <select id="getWechatAccountPageList"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.ExtWechatOfficialAccountFansResultDO"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ExtWechatOfficialAccountFansParamDO">
        SELECT
        count_date countDate,
        sub_name subName,
        incr_fans_amount incrFansAmount,
        unfollow_fans_amount unfollowFansAmount,
        net_incr_fans_amount netIncrFansAmount,
        total_fans_amount totalFansAmount
        FROM
        tp_wechat_official_account_fans
        WHERE
        count_date &gt;= #{countStartDate,jdbcType=VARCHAR}
        AND count_date &lt;= #{countEndDate,jdbcType=VARCHAR}
        <if test="subNameLike != null and subNameLike != ''">
            AND sub_name LIKE CONCAT(#{subNameLike,jdbcType=VARCHAR},'%')
        </if>
        GROUP BY
        count_date,
        sub_appid
        ORDER BY
        count_date DESC,total_fans_amount DESC
    </select>
</mapper>
