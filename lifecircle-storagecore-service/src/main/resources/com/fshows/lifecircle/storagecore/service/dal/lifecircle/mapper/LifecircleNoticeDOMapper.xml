<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleNoticeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleNoticeDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TITLE" property="title" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTENT" property="content" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="CONSUM_AMOUNT" property="consumAmount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONSUM_RETURN" property="consumReturn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POSTER_ZIP_URL" property="posterZipUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECHARGE_AMOUNT" property="rechargeAmount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECHARGE_RETURN" property="rechargeReturn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DRAGONFLY_BANNER_URL" property="dragonflyBannerUrl" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PUSH" property="isPush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SEND" property="isSend" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_LIMIT" property="isLimit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SEND_NUM" property="sendNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TIME_LIMIT" property="timeLimit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RETURN_CASH" property="returnCash" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUM_RETURN_MODE" property="consumReturnMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUMPTION_RETURN_NUM" property="consumptionReturnNum" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="RECOMMEND" property="recommend" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTIVATION" property="activation" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RECHARGE_LIMIT" property="rechargeLimit" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TITLE`,`TOKEN`,`CONTENT`,`CONSUM_AMOUNT`,`CONSUM_RETURN`,`POSTER_ZIP_URL`,`RECHARGE_AMOUNT`,`RECHARGE_RETURN`,`DRAGONFLY_BANNER_URL`,`TYPE`,`IS_PUSH`,`IS_SEND`,`STATUS`,`END_TIME`,`IS_LIMIT`,`SEND_NUM`,`START_TIME`,`TIME_LIMIT`,`CREATE_TIME`,`RETURN_CASH`,`ACTIVITY_TYPE`,`CONSUM_RETURN_MODE`,`CONSUMPTION_RETURN_NUM`,`RECOMMEND`,`ACTIVATION`,`RECHARGE_LIMIT`
    </sql>


            <!--insert:TP_LIFECIRCLE_NOTICE-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_NOTICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="recommend != null">`RECOMMEND`,</if>
            <if test="activation != null">`ACTIVATION`,</if>
            <if test="rechargeLimit != null">`RECHARGE_LIMIT`,</if>
            <if test="title != null">`TITLE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="consumAmount != null">`CONSUM_AMOUNT`,</if>
            <if test="consumReturn != null">`CONSUM_RETURN`,</if>
            <if test="posterZipUrl != null">`POSTER_ZIP_URL`,</if>
            <if test="rechargeAmount != null">`RECHARGE_AMOUNT`,</if>
            <if test="rechargeReturn != null">`RECHARGE_RETURN`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="isSend != null">`IS_SEND`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="isLimit != null">`IS_LIMIT`,</if>
            <if test="sendNum != null">`SEND_NUM`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="timeLimit != null">`TIME_LIMIT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="returnCash != null">`RETURN_CASH`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
            <if test="consumReturnMode != null">`CONSUM_RETURN_MODE`,</if>
            <if test="consumptionReturnNum != null">`CONSUMPTION_RETURN_NUM`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="recommend != null">#{recommend,jdbcType=DECIMAL},</if>
            <if test="activation != null">#{activation,jdbcType=DECIMAL},</if>
            <if test="rechargeLimit != null">#{rechargeLimit,jdbcType=DECIMAL},</if>
            <if test="title != null">#{title,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=LONGVARCHAR},</if>
            <if test="consumAmount != null">#{consumAmount,jdbcType=VARCHAR},</if>
            <if test="consumReturn != null">#{consumReturn,jdbcType=VARCHAR},</if>
            <if test="posterZipUrl != null">#{posterZipUrl,jdbcType=VARCHAR},</if>
            <if test="rechargeAmount != null">#{rechargeAmount,jdbcType=VARCHAR},</if>
            <if test="rechargeReturn != null">#{rechargeReturn,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="isSend != null">#{isSend,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="isLimit != null">#{isLimit,jdbcType=TINYINT},</if>
            <if test="sendNum != null">#{sendNum,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="timeLimit != null">#{timeLimit,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="returnCash != null">#{returnCash,jdbcType=TINYINT},</if>
            <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
            <if test="consumReturnMode != null">#{consumReturnMode,jdbcType=TINYINT},</if>
            <if test="consumptionReturnNum != null">#{consumptionReturnNum,jdbcType=SMALLINT},</if>
        </trim>
            </insert>

            <!--修改海报压缩包地址-->
            <update id="updatePosterZipUrlByActivityId" >
                    UPDATE
        TP_LIFECIRCLE_NOTICE
        SET
        poster_zip_url = #{posterZipUrl,jdbcType=VARCHAR}
        WHERE
        id = #{id,jdbcType=INTEGER}
            </update>
    </mapper>
