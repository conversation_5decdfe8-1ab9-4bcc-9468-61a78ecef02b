<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsSpuDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsSpuDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="PICTURE" property="picture" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_DESC" property="goodsDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_IDS" property="categoryIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_GROUP" property="isGroup" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GOODS_TYPE" property="goodsType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRICE_TYPE" property="priceType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPU_STATUS" property="spuStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_COMBINED" property="isCombined" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FREE_SEND_NUM" property="freeSendNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GROUP_NUMBER" property="groupNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_INSTALLMENT" property="isInstallment" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INSTALLMENT_NUMBER" property="installmentNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CONTAINS_EXPRESS" property="isContainsExpress" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SPU_PRICE" property="spuPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SPU_WEIGHT" property="spuWeight" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`PICTURE`,`GOODS_DESC`,`GOODS_NAME`,`GOODS_SPU_ID`,`CATEGORY_IDS`,`SORT`,`IS_DEL`,`IS_TEST`,`IS_GROUP`,`GOODS_TYPE`,`PRICE_TYPE`,`SPU_STATUS`,`IS_COMBINED`,`EQUIPMENT_ID`,`FREE_SEND_NUM`,`GROUP_NUMBER`,`IS_INSTALLMENT`,`INSTALLMENT_NUMBER`,`IS_CONTAINS_EXPRESS`,`CREATE_TIME`,`UPDATE_TIME`,`SPU_PRICE`,`SPU_WEIGHT`
    </sql>


            <!--insert:HW_SHOP_GOODS_SPU-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_GOODS_SPU
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="picture != null">`PICTURE`,</if>
            <if test="goodsDesc != null">`GOODS_DESC`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isGroup != null">`IS_GROUP`,</if>
            <if test="priceType != null">`PRICE_TYPE`,</if>
            <if test="spuStatus != null">`SPU_STATUS`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="groupNumber != null">`GROUP_NUMBER`,</if>
            <if test="isInstallment != null">`IS_INSTALLMENT`,</if>
            <if test="isContainsExpress != null">`IS_CONTAINS_EXPRESS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="spuPrice != null">`SPU_PRICE`,</if>
            <if test="spuWeight != null">`SPU_WEIGHT`,</if>
            <if test="categoryIds != null">`CATEGORY_IDS`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="goodsDesc != null">#{goodsDesc,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isGroup != null">#{isGroup,jdbcType=TINYINT},</if>
            <if test="priceType != null">#{priceType,jdbcType=TINYINT},</if>
            <if test="spuStatus != null">#{spuStatus,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="groupNumber != null">#{groupNumber,jdbcType=INTEGER},</if>
            <if test="isInstallment != null">#{isInstallment,jdbcType=TINYINT},</if>
            <if test="isContainsExpress != null">#{isContainsExpress,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="spuPrice != null">#{spuPrice,jdbcType=DECIMAL},</if>
            <if test="spuWeight != null">#{spuWeight,jdbcType=DECIMAL},</if>
            <if test="categoryIds != null">#{categoryIds,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据条件查询ka账号列表 pageCount-->
            <select id="findGoodsListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 HW_SHOP_GOODS_SPU
        where is_del = 0
        <if test="goodsName != null">
            and goods_name LIKE CONCAT ('%', #{goodsName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="categoryIds != null and categoryIds != ''">
            AND
            (
            category_ids = #{categoryIds,jdbcType=VARCHAR} or
            <foreach collection="categoryIds.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                FIND_IN_SET(#{item,jdbcType=VARCHAR},category_ids)
            </foreach>
            )
        </if>
        <if test="isInstallment != null">
            and is_installment = #{isInstallment,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            and equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="spuStatus != null">
            and spu_status = #{spuStatus,jdbcType=INTEGER}
        </if>
        <if test="isContainsExpress != null">
            and is_contains_express = #{isContainsExpress,jdbcType=INTEGER}
        </if>
        <if test="isTest != null">
            and is_test = #{isTest, jdbcType=INTEGER}
        </if>
        
            </select>
            <!--根据条件查询ka账号列表 pageResult-->
            <select id="findGoodsListResult"  resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-GOODS-SPU-FINDGOODSLIST*/ <include refid="Base_Column_List" /> from HW_SHOP_GOODS_SPU
        where is_del = 0
        <if test="goodsName != null">
            and goods_name LIKE CONCAT ('%', #{goodsName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="categoryIds != null and categoryIds != ''">
            AND
            (
            category_ids = #{categoryIds,jdbcType=VARCHAR} or
            <foreach collection="categoryIds.split(',')" index="index" item="item" open="(" separator="OR" close=")">
                FIND_IN_SET(#{item,jdbcType=VARCHAR},category_ids)
            </foreach>
            )
        </if>
        <if test="isInstallment != null">
            and is_installment = #{isInstallment,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            and equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="spuStatus != null">
            and spu_status = #{spuStatus,jdbcType=INTEGER}
        </if>
        <if test="isContainsExpress != null">
            and is_contains_express = #{isContainsExpress,jdbcType=INTEGER}
        </if>
        <if test="isTest != null">
            and is_test = #{isTest, jdbcType=INTEGER}
        </if>
        ORDER BY sort , create_time ASC
            limit #{startRow},#{limit}
            </select>

            <!--根据商品名称模糊查询商品-->
            <select id="getAllGoodsByGoodsName" resultType="java.lang.String">
                    select /*MS-HW-SHOP-GOODS-SPU-GETALLGOODSBYGOODSNAME*/ goods_spu_id from HW_SHOP_GOODS_SPU
        where goods_name like concat('%', #{goodsName,jdbcType=VARCHAR} ,'%')
            </select>

            <!--根据商品名称查询商品-->
            <select id="getGoodsByGoodsName" resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-GOODS-SPU-GETGOODSBYGOODSNAME*/ <include refid="Base_Column_List" /> from HW_SHOP_GOODS_SPU
        where goods_name = #{goodsName,jdbcType=VARCHAR}
        limit 1
            </select>

            <!--根据商品id查询商品-->
            <select id="getGoodsById" resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-GOODS-SPU-GETGOODSBYID*/ <include refid="Base_Column_List" /> from HW_SHOP_GOODS_SPU
        where id = #{id,jdbcType=INTEGER}
            </select>

            <!--根据商品id查询商品-->
            <select id="getGoodsByGoodsSpuId" resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-GOODS-SPU-GETGOODSBYGOODSSPUID*/ <include refid="Base_Column_List" /> from HW_SHOP_GOODS_SPU
        where goods_spu_id = #{goodsSpuId,jdbcType=VARCHAR}
        and is_del = 0
            </select>

            <!--获取套餐商品中关联物料商品的spu信息-->
            <select id="getMaterialPartOfCombineSpu" resultMap="BaseResultMap">
                    select /*MS-HW-SHOP-GOODS-SPU-GETMATERIALPARTOFCOMBINESPU*/ a.*
        from hw_shop_goods_spu a
        where a.id in (select /*MS-HW-SHOP-GOODS-SPU-GETMATERIALPARTOFCOMBINESPU*/ b.equipment_id
        from hw_shop_goods_combined_relation b
        where b.goods_spu_id = #{goodsSpuId, jdbcType=VARCHAR}
        and b.product_type = 2
        and b.is_del = 0)
        and a.is_del = 0
            </select>
    </mapper>
