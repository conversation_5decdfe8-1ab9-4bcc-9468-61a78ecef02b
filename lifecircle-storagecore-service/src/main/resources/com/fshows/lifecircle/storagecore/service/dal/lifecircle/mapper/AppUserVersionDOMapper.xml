<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AppUserVersionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AppUserVersionDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APP_VERSION" property="appVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE_BRAND" property="phoneBrand" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE_MODEL" property="phoneModel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SYS_VERSION" property="sysVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="VERSION_TYPE" property="versionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PUB_VERSION_ID" property="pubVersionId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>

        <resultMap id="UserVersionResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.UserVersionResult">

                <result column="username" property="username" javaType="String"/>

                <result column="phone_model" property="phoneModel" javaType="String"/>

                <result column="sys_version" property="sysVersion" javaType="String"/>

                <result column="uid" property="uid" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`APP_VERSION`,`PHONE_BRAND`,`PHONE_MODEL`,`SYS_VERSION`,`UID`,`PLATFORM`,`USER_TYPE`,`SUB_CONFIG_ID`,`VERSION_TYPE`,`PUB_VERSION_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_APP_USER_VERSION-->
            <insert id="insert" >
                    INSERT INTO TP_APP_USER_VERSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="phoneBrand != null">`PHONE_BRAND`,</if>
            <if test="phoneModel != null">`PHONE_MODEL`,</if>
            <if test="sysVersion != null">`SYS_VERSION`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="versionType != null">`VERSION_TYPE`,</if>
            <if test="pubVersionId != null">`PUB_VERSION_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="phoneBrand != null">#{phoneBrand,jdbcType=VARCHAR},</if>
            <if test="phoneModel != null">#{phoneModel,jdbcType=VARCHAR},</if>
            <if test="sysVersion != null">#{sysVersion,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="versionType != null">#{versionType,jdbcType=TINYINT},</if>
            <if test="pubVersionId != null">#{pubVersionId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据version_id 查询列表-->
            <select id="findByVersionId" resultMap="UserVersionResultMap">
                    SELECT
        a.uid,
        a.phone_model,
        a.sys_version,
        u.username
        FROM
        tp_app_user_version AS a
        LEFT JOIN tp_users as u ON a.uid = u.id
        WHERE a.pub_version_id = #{versionId,jdbcType=INTEGER}
        AND a.sub_config_id = 0
            </select>
    </mapper>
