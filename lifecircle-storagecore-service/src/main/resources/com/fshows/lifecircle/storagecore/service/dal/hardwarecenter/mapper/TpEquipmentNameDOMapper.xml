<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentNameDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentNameDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EQUIPMENT_NAME" property="equipmentName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
                    javaType="Integer"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`EQUIPMENT_NAME`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:TP_EQUIPMENT_NAME-->
        <insert id="insert">
            INSERT INTO TP_EQUIPMENT_NAME
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="equipmentName != null">`EQUIPMENT_NAME`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="equipmentName != null">#{equipmentName,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            </trim>
        </insert>
    </mapper>
