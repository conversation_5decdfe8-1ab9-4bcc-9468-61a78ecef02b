<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CommunicationsStatementAccountDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CommunicationsStatementAccountDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CUSID" property="cusid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUTT_NO" property="buttNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEAL_NO" property="dealNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BATCHID" property="batchid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEAL_TIME" property="dealTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEAL_TYPE" property="dealType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHNLTRXID" property="chnltrxid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VOUCHER_NO" property="voucherNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARDHOLDER" property="cardholder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TERMINAL_NO" property="terminalNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ISSUING" property="bankIssuing" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_TYPE" property="productType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARDHOLDER_CARD" property="cardholderCard" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INSTALLMENT_NUM" property="installmentNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_REMARK" property="merchantRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PROFIT_TYPE" property="profitType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DEAL_DATE" property="dealDate" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="DEAL_MONEY" property="dealMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLE_MONEY" property="settleMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SHARE_PROFIT" property="shareProfit" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORIGINAL_MONEY" property="originalMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SERVICE_CHARGE" property="serviceCharge" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="INSTALLMENT_FEE" property="installmentFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CUSID`,`BUTT_NO`,`DEAL_NO`,`BATCHID`,`ORDER_NO`,`BANK_CODE`,`BANK_NAME`,`BANK_TYPE`,`DEAL_TIME`,`DEAL_TYPE`,`CHNLTRXID`,`STORE_NAME`,`VOUCHER_NO`,`CARDHOLDER`,`FINISH_TIME`,`TERMINAL_NO`,`BANK_ISSUING`,`PRODUCT_TYPE`,`CARDHOLDER_CARD`,`INSTALLMENT_NUM`,`MERCHANT_REMARK`,`AGENT_ID`,`MERCHANT_ID`,`PROFIT_TYPE`,`DEAL_DATE`,`CREATE_TIME`,`DEAL_MONEY`,`SETTLE_MONEY`,`SHARE_PROFIT`,`ORIGINAL_MONEY`,`SERVICE_CHARGE`,`INSTALLMENT_FEE`
    </sql>


            <!--insert:TP_COMMUNICATIONS_STATEMENT_ACCOUNT-->
            <insert id="insert" >
                    INSERT INTO TP_COMMUNICATIONS_STATEMENT_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cusid != null">`CUSID`,</if>
            <if test="buttNo != null">`BUTT_NO`,</if>
            <if test="dealNo != null">`DEAL_NO`,</if>
            <if test="batchid != null">`BATCHID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="dealType != null">`DEAL_TYPE`,</if>
            <if test="chnltrxid != null">`CHNLTRXID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="voucherNo != null">`VOUCHER_NO`,</if>
            <if test="cardholder != null">`CARDHOLDER`,</if>
            <if test="terminalNo != null">`TERMINAL_NO`,</if>
            <if test="bankIssuing != null">`BANK_ISSUING`,</if>
            <if test="productType != null">`PRODUCT_TYPE`,</if>
            <if test="originalMoney != null">`ORIGINAL_MONEY`,</if>
            <if test="cardholderCard != null">`CARDHOLDER_CARD`,</if>
            <if test="installmentNum != null">`INSTALLMENT_NUM`,</if>
            <if test="merchantRemark != null">`MERCHANT_REMARK`,</if>
            <if test="dealDate != null">`DEAL_DATE`,</if>
            <if test="dealTime != null">`DEAL_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="dealMoney != null">`DEAL_MONEY`,</if>
            <if test="settleMoney != null">`SETTLE_MONEY`,</if>
            <if test="serviceCharge != null">`SERVICE_CHARGE`,</if>
            <if test="installmentFee != null">`INSTALLMENT_FEE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cusid != null">#{cusid,jdbcType=VARCHAR},</if>
            <if test="buttNo != null">#{buttNo,jdbcType=VARCHAR},</if>
            <if test="dealNo != null">#{dealNo,jdbcType=VARCHAR},</if>
            <if test="batchid != null">#{batchid,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=VARCHAR},</if>
            <if test="dealType != null">#{dealType,jdbcType=VARCHAR},</if>
            <if test="chnltrxid != null">#{chnltrxid,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="voucherNo != null">#{voucherNo,jdbcType=VARCHAR},</if>
            <if test="cardholder != null">#{cardholder,jdbcType=VARCHAR},</if>
            <if test="terminalNo != null">#{terminalNo,jdbcType=VARCHAR},</if>
            <if test="bankIssuing != null">#{bankIssuing,jdbcType=VARCHAR},</if>
            <if test="productType != null">#{productType,jdbcType=VARCHAR},</if>
            <if test="originalMoney != null">#{originalMoney,jdbcType=VARCHAR},</if>
            <if test="cardholderCard != null">#{cardholderCard,jdbcType=VARCHAR},</if>
            <if test="installmentNum != null">#{installmentNum,jdbcType=VARCHAR},</if>
            <if test="merchantRemark != null">#{merchantRemark,jdbcType=VARCHAR},</if>
            <if test="dealDate != null">#{dealDate,jdbcType=TIMESTAMP},</if>
            <if test="dealTime != null">#{dealTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="dealMoney != null">#{dealMoney,jdbcType=DECIMAL},</if>
            <if test="settleMoney != null">#{settleMoney,jdbcType=DECIMAL},</if>
            <if test="serviceCharge != null">#{serviceCharge,jdbcType=DECIMAL},</if>
            <if test="installmentFee != null">#{installmentFee,jdbcType=DECIMAL},</if>
        </trim>
            </insert>
    </mapper>
