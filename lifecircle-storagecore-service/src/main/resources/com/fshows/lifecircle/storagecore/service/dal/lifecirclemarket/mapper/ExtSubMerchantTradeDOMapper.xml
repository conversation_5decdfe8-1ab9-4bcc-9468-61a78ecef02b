<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtSubMerchantTradeDOMapper">

    <!--商户TAB-分页列表-->
    <select id="getMerchatFansPageList"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.ExtSubMerchantTradeResultDO"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ExtSubMerchantTradeParamDO">
        SELECT
        smt.count_date countDate,
        smt.uid,
        users.username,
        smt.sub_mch_id subMchId,
        user.username agentName,
        smt.trade_num tradeNum,
        smt.incr_fans_amount incrFansAmount
        FROM
        tp_sub_merchant_trade smt
        LEFT JOIN tp_user user ON smt.agent_id = user.id
        LEFT JOIN tp_users users ON smt.uid = users.id
        WHERE
        smt.count_date &gt;= #{countStartDate,jdbcType=VARCHAR}
        AND smt.count_date &lt;= #{countEndDate,jdbcType=VARCHAR}
        <if test="uid != null and uid != ''">
            AND smt.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="subMchId != null and subMchId != ''">
            AND smt.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="agentName != null and agentName != ''">
            AND user.username LIKE CONCAT(#{agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="sort != null and sort != ''">
            ORDER BY ${sort}
        </if>
        <if test="sort == null">
            ORDER BY
            smt.count_date DESC,
            smt.trade_num DESC
        </if>
    </select>
</mapper>
