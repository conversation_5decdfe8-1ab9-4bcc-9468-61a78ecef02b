<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpFaceScanEquipmentRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpFaceScanEquipmentRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CODE_SCAN_NUM" property="codeScanNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FACE_SCAN_DAU" property="faceScanDau" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIGHT_STATUS" property="lightStatus" jdbcType="TINYINT"
        javaType="Integer"/>

    <result column="EQUIPMENT_DAU" property="equipmentDau" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="TRANSACTION_NUM" property="transactionNum" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="ALIPAY_LIGHT_TIME" property="alipayLightTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="LIGHT_TIME" property="lightTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="ACTIVITY_TIME" property="activityTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TRANSACTION_MONEY" property="transactionMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="ScanFaceRecordDo" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceRecordDO">

                <result column="light_time" property="lightTime" javaType="java.util.Date"/>

                <result column="create_time" property="createTime" javaType="java.util.Date"/>

                <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

                <result column="agent_id" property="agentId" javaType="java.lang.Integer"/>

                <result column="code_scan_num" property="codeScanNum" javaType="java.lang.Integer"/>

                <result column="face_scan_dau" property="faceScanDau" javaType="java.lang.Integer"/>

                <result column="light_status" property="lightStatus" javaType="java.lang.Integer"/>

                <result column="equipment_dau" property="equipmentDau" javaType="java.lang.Integer"/>

                <result column="transaction_num" property="transactionNum" javaType="java.lang.Integer"/>

                <result column="transaction_money" property="transactionMoney" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="ScanFaceEquipmentMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceEquipmentMap">

                <result column="real_name" property="realName" javaType="String"/>

                <result column="agent_name" property="agentName" javaType="String"/>

                <result column="own_run" property="ownRun" javaType="Integer"/>

                <result column="agent_id" property="agentId" javaType="Integer"/>

                <result column="light_num" property="lightNum" javaType="Integer"/>

                <result column="code_scan_num" property="codeScanNum" javaType="Integer"/>

                <result column="equipment_dau" property="equipmentDau" javaType="Integer"/>

                <result column="equipment_num" property="equipmentNum" javaType="Integer"/>

                <result column="transaction_num" property="transactionNum" javaType="Integer"/>

                <result column="transaction_money" property="transactionMoney" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="ScanFaceEquipmentDetailMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceEquipmentDetailMap">

                <result column="system_sn" property="systemSn" javaType="String"/>

                <result column="store_name" property="storeName" javaType="String"/>

                <result column="id" property="id" javaType="Integer"/>

                <result column="grant_name" property="grantName" javaType="String"/>

                <result column="merchant_name" property="merchantName" javaType="String"/>

                <result column="sn_status" property="snStatus" javaType="Integer"/>

                <result column="sn_store_id" property="snStoreId" javaType="Integer"/>

                <result column="code_scan_num" property="codeScanNum" javaType="Integer"/>

                <result column="equipment_id" property="equipmentId" javaType="Integer"/>

                <result column="face_store_id" property="faceStoreId" javaType="Integer"/>

                <result column="light_status" property="lightStatus" javaType="Integer"/>

                <result column="equipment_dau" property="equipmentDau" javaType="Integer"/>

                <result column="activity_status" property="activityStatus" javaType="Integer"/>

                <result column="transaction_num" property="transactionNum" javaType="Integer"/>

                <result column="bind_time" property="bindTime" javaType="java.util.Date"/>

                <result column="light_time" property="lightTime" javaType="java.util.Date"/>

                <result column="activity_time" property="activityTime" javaType="java.util.Date"/>

                <result column="transaction_money" property="transactionMoney" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
        `ID`,`EQUIPMENT_SN`,`UID`,`AGENT_ID`,`STORE_ID`,`CODE_SCAN_NUM`,`FACE_SCAN_DAU`,`LIGHT_STATUS`,`EQUIPMENT_DAU`,`ACTIVITY_STATUS`,`TRANSACTION_NUM`,`ALIPAY_LIGHT_TIME`,`LIGHT_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`ACTIVITY_TIME`,`TRANSACTION_MONEY`
    </sql>


            <!--insert:TP_FACE_SCAN_EQUIPMENT_RECORD-->
            <insert id="insert" >
            INSERT INTO TP_FACE_SCAN_EQUIPMENT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="codeScanNum != null">`CODE_SCAN_NUM`,</if>
        <if test="lightStatus != null">`LIGHT_STATUS`,</if>
        <if test="equipmentDau != null">`EQUIPMENT_DAU`,</if>
        <if test="faceScanDau != null">`FACE_SCAN_DAU`,</if>
        <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
        <if test="transactionNum != null">`TRANSACTION_NUM`,</if>
        <if test="lightTime != null">`LIGHT_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="activityTime != null">`ACTIVITY_TIME`,</if>
        <if test="transactionMoney != null">`TRANSACTION_MONEY`,</if>
        <if test="alipayLightTime != null">`ALIPAY_LIGHT_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="codeScanNum != null">#{codeScanNum,jdbcType=INTEGER},</if>
        <if test="lightStatus != null">#{lightStatus,jdbcType=TINYINT},</if>
        <if test="equipmentDau != null">#{equipmentDau,jdbcType=INTEGER},</if>
        <if test="faceScanDau != null">#{faceScanDau,jdbcType=INTEGER},</if>
        <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
        <if test="transactionNum != null">#{transactionNum,jdbcType=INTEGER},</if>
        <if test="lightTime != null">#{lightTime,jdbcType=TIMESTAMP},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="activityTime != null">#{activityTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionMoney != null">#{transactionMoney,jdbcType=DECIMAL},</if>
        <if test="alipayLightTime != null">#{alipayLightTime,jdbcType=INTEGER},</if>
    </trim>
            </insert>

            <!--根据设备sn码查询-->
            <select id="getByEquipmentSn" resultMap="ScanFaceRecordDo">
                    select
        equipment_sn,agent_id,equipment_dau,face_scan_dau,transaction_num,transaction_money,code_scan_num,light_status,light_time,create_time
        from tp_face_scan_equipment_record where equipment_sn = #{equipmentSn,jdbcType=VARCHAR} limit 1
            </select>

            <!--更新数据-->
            <update id="update" >
                    update /*MS-TP-FACE-SCAN-EQUIPMENT-RECORD-UPDATE*/ TP_FACE_SCAN_EQUIPMENT_RECORD
        SET
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId,jdbcType=INTEGER},</if>
            <if test="codeScanNum != null">code_scan_num = #{codeScanNum,jdbcType=INTEGER},</if>
            <if test="lightStatus != null">light_status = #{lightStatus,jdbcType=TINYINT},</if>
            <if test="equipmentDau != null">equipment_dau = #{equipmentDau,jdbcType=INTEGER},</if>
            <if test="faceScanDau != null">face_scan_dau = #{faceScanDau,jdbcType=INTEGER},</if>
            <if test="transactionNum != null">transaction_num = #{transactionNum,jdbcType=INTEGER},</if>
            <if test="lightTime != null">light_time = #{lightTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="transactionMoney != null">transaction_money = #{transactionMoney,jdbcType=DECIMAL},</if>
            <if test="alipayLightTime != null">alipay_light_time = #{alipayLightTime,jdbcType=INTEGER},</if>
        </trim>
        WHERE
        equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
            </update>

            <!--导出代理商扫脸设备信息-->
            <select id="getScanFaceListExport" resultMap="ScanFaceEquipmentMap">
                    SELECT /*MS-TP-FACE-SCAN-EQUIPMENT-RECORD-GETSCANFACELISTEXPORT*/  ue.username as agent_name,ue.contacts as
        real_name,sn.agent_id,
         count(*)  as equipment_num,sum(light_status)as light_num,
        sum(equipment_dau) as equipment_dau,sum(transaction_num) as transaction_num,
        sum(transaction_money) as transaction_money,sum(code_scan_num) as code_scan_num
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.agent_id
        LEFT JOIN tp_face_scan_equipment_record face on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        where eq.equipment_type = 4
        and sn.agent_id !=0
        and ue.is_salesman = 0
        and ue.sub_config_id = 0
        and sn.is_del = 0
        <if test="ownRun !=null">
            and ue.own_run = #{ownRun,jdbcType = INTEGER}
        </if>
        <if test="agentId !=null">
            and ue.id = #{agentId,jdbcType = INTEGER}
        </if>
        GROUP BY sn.agent_id
        ORDER BY ue.create_time DESC
            </select>

            <!--导出代理商扫脸设备详情信息-->
            <select id="getScanFaceDetailListExport" resultMap="ScanFaceEquipmentDetailMap">
                    SELECT /*MS-TP-FACE-SCAN-EQUIPMENT-RECORD-GETSCANFACEDETAILLISTEXPORT*/  face.activity_status,
        face.activity_time,
        sn.id,sn.bind_time,sn.unbind_time,
        ue.username as grant_name,
        sn.equipment_id,
        sn.init_sn as
        system_sn,
        sn.store_id as sn_store_id,
        sn.sn_status,
        us.username as
        merchant_name,
        store.store_name,
        face.equipment_dau,
        face.transaction_num,
        face.transaction_money,
        face.code_scan_num,
        face.light_status,
        face.light_time,
        face.store_id as face_store_id
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face
        on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        LEFT JOIN tp_users us on us.id = sn.uid
        LEFT JOIN tp_lifecircle_store store on store.store_id = sn.store_id
        where eq.equipment_type = 4
        and sn.is_del = 0
        and sn.agent_id = #{agentId,jdbcType = INTEGER}
        group by sn.init_sn
        ORDER BY sn.create_time DESC
            </select>

            <!--导出直营代理商扫脸设备详情信息-->
            <select id="getScanFaceDetailByDirectListExport" resultMap="ScanFaceEquipmentDetailMap">
                    SELECT /*MS-TP-FACE-SCAN-EQUIPMENT-RECORD-GETSCANFACEDETAILBYDIRECTLISTEXPORT*/  face.activity_status,
        face.activity_time,
        sn.id,sn.bind_time,sn.unbind_time,
        ue.username as grant_name,
        sn.equipment_id,
        sn.init_sn as
        system_sn,
        sn.store_id as sn_store_id,
        sn.sn_status,
        us.username as
        merchant_name,
        store.store_name,
        face.equipment_dau,
        face.transaction_num,
        face.transaction_money,
        face.code_scan_num,
        face.light_status,
        face.light_time,
        face.store_id as face_store_id
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face
        on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        LEFT JOIN tp_users us on us.id = face.uid
        LEFT JOIN tp_lifecircle_store store on store.store_id = face.store_id
        where eq.equipment_type = 4
        and sn.is_del = 0
        and sn.agent_id = #{agentId,jdbcType = INTEGER}
        group by sn.init_sn
        ORDER BY sn.create_time DESC
            </select>

            <!--changeActivityMerchant-->
            <select id="changeActivityMerchant" resultMap="BaseResultMap">
                    UPDATE tp_face_scan_equipment_record SET
        uid = #{merchantId,jdbcType=INTEGER},
        store_id = 0,
        activity_status = 2,
        activity_time = #{activityTime,jdbcType=TIMESTAMP}
        WHERE equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
            </select>

            <!--resetActivityMerchant-->
            <select id="resetActivityMerchant" resultMap="BaseResultMap">
                    UPDATE tp_face_scan_equipment_record SET
        uid = 0,
        store_id = 0,
        activity_status = 1
        WHERE equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
            </select>

            <!--findByInitSn-->
            <select id="findByInitSn" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        tp_face_scan_equipment_record
        WHERE
        equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
