<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleBankPackageDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleBankPackageDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCT_ID" property="acctId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BACK_IMG" property="backImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_PIC" property="cardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_LOGO" property="bankLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_NUMBER" property="idNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_ACTIVE" property="isActive" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMOVE_STATUS" property="removeStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNIONPAY_CODE" property="unionpayCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECTED_REASON" property="rejectedReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LETTER_OF_AUTH_PIC" property="letterOfAuthPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_OPENING_PERMIT_PIC" property="accountOpeningPermitPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ADD_FROM" property="addFrom" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_WAY" property="bindWay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_FROM" property="bindFrom" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_TYPE" property="bindType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUDIT_TIME" property="auditTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_BANK_ID" property="bindBankId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WITHDRAW_FLAG" property="withdrawFlag" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REVIEW_BANKCARD_REJECTION_STATUS" property="reviewBankcardRejectionStatus" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`ACCT_ID`,`BANK_NO`,`MOBILE`,`BACK_IMG`,`CARD_PIC`,`AREA_CODE`,`AREA_NAME`,`BANK_LOGO`,`BANK_NAME`,`CITY_CODE`,`CITY_NAME`,`ID_NUMBER`,`IS_ACTIVE`,`ACCOUNT_NAME`,`PROVINCE_CODE`,`PROVINCE_NAME`,`REMOVE_STATUS`,`UNIONPAY_CODE`,`REJECTED_REASON`,`LETTER_OF_AUTH_PIC`,`ACCOUNT_OPENING_PERMIT_PIC`,`UID`,`IS_DEL`,`ADD_FROM`,`BIND_WAY`,`BIND_FROM`,`BIND_TYPE`,`AUDIT_TIME`,`BIND_BANK_ID`,`CREATE_TIME`,`UPDATE_TIME`,`CHECK_STATUS`,`WITHDRAW_FLAG`,`REVIEW_BANKCARD_REJECTION_STATUS`
    </sql>


            <!--insert:TP_LIFECIRCLE_BANK_PACKAGE-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_BANK_PACKAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="acctId != null">`ACCT_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="backImg != null">`BACK_IMG`,</if>
            <if test="cardPic != null">`CARD_PIC`,</if>
            <if test="areaCode != null">`AREA_CODE`,</if>
            <if test="areaName != null">`AREA_NAME`,</if>
            <if test="bankLogo != null">`BANK_LOGO`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="idNumber != null">`ID_NUMBER`,</if>
            <if test="isActive != null">`IS_ACTIVE`,</if>
            <if test="accountName != null">`ACCOUNT_NAME`,</if>
            <if test="provinceCode != null">`PROVINCE_CODE`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="removeStatus != null">`REMOVE_STATUS`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="rejectedReason != null">`REJECTED_REASON`,</if>
            <if test="letterOfAuthPic != null">`LETTER_OF_AUTH_PIC`,</if>
            <if test="accountOpeningPermitPic != null">`ACCOUNT_OPENING_PERMIT_PIC`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="addFrom != null">`ADD_FROM`,</if>
            <if test="bindWay != null">`BIND_WAY`,</if>
            <if test="bindFrom != null">`BIND_FROM`,</if>
            <if test="bindType != null">`BIND_TYPE`,</if>
            <if test="auditTime != null">`AUDIT_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="withdrawFlag != null">`WITHDRAW_FLAG`,</if>
            <if test="reviewBankcardRejectionStatus != null">`REVIEW_BANKCARD_REJECTION_STATUS`,</if>
            <if test="bindBankId != null">`BIND_BANK_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="acctId != null">#{acctId,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="backImg != null">#{backImg,jdbcType=VARCHAR},</if>
            <if test="cardPic != null">#{cardPic,jdbcType=VARCHAR},</if>
            <if test="areaCode != null">#{areaCode,jdbcType=VARCHAR},</if>
            <if test="areaName != null">#{areaName,jdbcType=VARCHAR},</if>
            <if test="bankLogo != null">#{bankLogo,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="idNumber != null">#{idNumber,jdbcType=VARCHAR},</if>
            <if test="isActive != null">#{isActive,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="removeStatus != null">#{removeStatus,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="rejectedReason != null">#{rejectedReason,jdbcType=VARCHAR},</if>
            <if test="letterOfAuthPic != null">#{letterOfAuthPic,jdbcType=VARCHAR},</if>
            <if test="accountOpeningPermitPic != null">#{accountOpeningPermitPic,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="addFrom != null">#{addFrom,jdbcType=TINYINT},</if>
            <if test="bindWay != null">#{bindWay,jdbcType=INTEGER},</if>
            <if test="bindFrom != null">#{bindFrom,jdbcType=TINYINT},</if>
            <if test="bindType != null">#{bindType,jdbcType=INTEGER},</if>
            <if test="auditTime != null">#{auditTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
            <if test="withdrawFlag != null">#{withdrawFlag,jdbcType=INTEGER},</if>
            <if test="reviewBankcardRejectionStatus != null">#{reviewBankcardRejectionStatus,jdbcType=TINYINT},</if>
            <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
        </trim>
            </insert>
    </mapper>
