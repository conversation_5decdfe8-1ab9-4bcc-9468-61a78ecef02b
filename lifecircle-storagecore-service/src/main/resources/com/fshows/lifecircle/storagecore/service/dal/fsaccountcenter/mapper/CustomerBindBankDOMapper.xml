<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.CustomerBindBankDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.CustomerBindBankDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ACTIVE" property="active" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CARD_ID" property="cardId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CERT_ID" property="certId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TOKEN_NO" property="tokenNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CARD_NAME" property="cardName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CERT_TYPE" property="certType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CARD_BACK_PIC" property="cardBackPic" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BANK_ACCT_TYPE" property="bankAcctType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CARD_FRONT_PIC" property="cardFrontPic" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_BIND_BANK_ID" property="customerBindBankId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`ACTIVE`,`CARD_ID`,`CERT_ID`,`TOKEN_NO`,`BANK_NAME`,`CARD_NAME`,`CERT_TYPE`,`UNION_CODE`,`BRANCH_NAME`,`CUSTOMER_ID`,`CARD_BACK_PIC`,`CHECK_STATUS`,`BANK_ACCT_TYPE`,`CARD_FRONT_PIC`,`CUSTOMER_BIND_BANK_ID`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:TP_CUSTOMER_BIND_BANK-->
        <insert id="insert">
            INSERT INTO TP_CUSTOMER_BIND_BANK
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="active != null">`ACTIVE`,</if>
                <if test="cardId != null">`CARD_ID`,</if>
                <if test="certId != null">`CERT_ID`,</if>
                <if test="tokenNo != null">`TOKEN_NO`,</if>
                <if test="bankName != null">`BANK_NAME`,</if>
                <if test="cardName != null">`CARD_NAME`,</if>
                <if test="certType != null">`CERT_TYPE`,</if>
                <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="bankAcctType != null">`BANK_ACCT_TYPE`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="cardFrontPic != null">`CARD_FRONT_PIC`,</if>
            <if test="cardBackPic != null">`CARD_BACK_PIC`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
            <if test="certId != null">#{certId,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
            <if test="certType != null">#{certType,jdbcType=VARCHAR},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=VARCHAR},</if>
            <if test="bankAcctType != null">#{bankAcctType,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="cardFrontPic != null">#{cardFrontPic,jdbcType=VARCHAR},</if>
            <if test="cardBackPic != null">#{cardBackPic,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--根据持卡人证件号码+银行卡号+生效状态查询记录-->
        <select id="getByIdcardAndCard" resultMap="BaseResultMap">
            SELECT `id`
            FROM tp_customer_bind_bank
            WHERE `cert_id` = #{certId,jdbcType=VARCHAR}
              AND `card_id` = #{cardId,jdbcType=VARCHAR}
              AND (`active` = "YES" OR (`active` = "NO" AND `check_status` = "TOBEAUDIT"))
            limit 1
        </select>

        <!--根据卡号查询持卡信息-->
        <select id="getBindBankListByCardId" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from
            tp_customer_bind_bank
            where
            check_status = 'PASS'
            and active = 'YES'
            and card_id in
            <foreach close=")" collection="list" index="index" item="cardId" open="(" separator=",">
                #{cardId,jdbcType=VARCHAR}
            </foreach>
        </select>

        <!--根据客户id查询客户绑卡信息-->
        <select id="getActiveBindBankByCustomerId" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            TP_CUSTOMER_BIND_BANK
            WHERE
            customer_id = #{customerId,jdbcType=VARCHAR}
            AND
            active = 'YES'
            AND
            check_status = "PASS"
            LIMIT 1
        </select>

        <!--根据银行卡号查询客户绑卡信息不需要判断生效-->
        <select id="getByCardIdWithOutActive" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            TP_CUSTOMER_BIND_BANK
            WHERE
            card_id = #{cardId,jdbcType=VARCHAR}
            AND
            customer_id = #{customerId,jdbcType=VARCHAR}
            ORDER BY ID DESC
            LIMIT 1
        </select>
    </mapper>
