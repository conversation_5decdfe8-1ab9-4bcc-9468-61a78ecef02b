<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantCredentialsExpireRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantCredentialsExpireRecordDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RECORD_ID" property="recordId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_END_DATE" property="licenseEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_BEGIN_DATE" property="licenseBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_BACK_PIC" property="legalIdCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_END_DATE" property="legalIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_FRONT_PIC" property="legalIdCardFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_BEGIN_DATE" property="legalIdCardBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_BACK_PIC" property="settlerIdCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_END_DATE" property="settlerIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_FRONT_PIC" property="settlerIdCardFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGINAL_LICENSE_END_DATE" property="originalLicenseEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_BEGIN_DATE" property="settlerIdCardBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGINAL_LEGAL_ID_CARD_END_DATE" property="originalLegalIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGINAL_SETTLER_ID_CARD_END_DATE" property="originalSettlerIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_NOTICE" property="isNotice" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALEMAN_ID" property="salemanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LICENSE_IS_LONG" property="licenseIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PROCESS_STATUS" property="processStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QUALIFICATION" property="qualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_30_TRADE_NUM" property="last30TradeNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREDENTIALS_STATUS" property="credentialsStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LEGAL_ID_CARD_IS_LONG" property="legalIdCardIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LICENSE_EXPIRED_FLAG" property="licenseExpiredFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLER_ID_CARD_IS_LONG" property="settlerIdCardIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LEGAL_ID_CARD_EXPIRED_FLAG" property="legalIdCardExpiredFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLER_ID_CARD_EXPIRED_FLAG" property="settlerIdCardExpiredFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="LAST_30_TRADE_MONEY" property="last30TradeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`COMPANY`,`RECORD_ID`,`USERNAME`,`LICENSE_PIC`,`MERCHANT_NO`,`REJECT_REASON`,`LICENSE_END_DATE`,`LICENSE_BEGIN_DATE`,`LEGAL_ID_CARD_BACK_PIC`,`LEGAL_ID_CARD_END_DATE`,`LEGAL_ID_CARD_FRONT_PIC`,`LEGAL_ID_CARD_BEGIN_DATE`,`SETTLER_ID_CARD_BACK_PIC`,`SETTLER_ID_CARD_END_DATE`,`SETTLER_ID_CARD_FRONT_PIC`,`ORIGINAL_LICENSE_END_DATE`,`SETTLER_ID_CARD_BEGIN_DATE`,`ORIGINAL_LEGAL_ID_CARD_END_DATE`,`ORIGINAL_SETTLER_ID_CARD_END_DATE`,`UID`,`IS_DEL`,`AGENT_ID`,`IS_NOTICE`,`MARKET_ID`,`CHANNEL_ID`,`ORDER_TYPE`,`SALEMAN_ID`,`LICENSE_IS_LONG`,`PROCESS_STATUS`,`QUALIFICATION`,`LAST_30_TRADE_NUM`,`CREDENTIALS_STATUS`,`LEGAL_ID_CARD_IS_LONG`,`LICENSE_EXPIRED_FLAG`,`SETTLER_ID_CARD_IS_LONG`,`LEGAL_ID_CARD_EXPIRED_FLAG`,`SETTLER_ID_CARD_EXPIRED_FLAG`,`CREATE_TIME`,`UPDATE_TIME`,`LAST_30_TRADE_MONEY`
    </sql>


            <!--insert:TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD-->
            <insert id="insert" >
                    INSERT INTO TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="recordId != null">`RECORD_ID`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="licenseEndDate != null">`LICENSE_END_DATE`,</if>
            <if test="licenseBeginDate != null">`LICENSE_BEGIN_DATE`,</if>
            <if test="legalIdCardBackPic != null">`LEGAL_ID_CARD_BACK_PIC`,</if>
            <if test="legalIdCardEndDate != null">`LEGAL_ID_CARD_END_DATE`,</if>
            <if test="legalIdCardFrontPic != null">`LEGAL_ID_CARD_FRONT_PIC`,</if>
            <if test="legalIdCardBeginDate != null">`LEGAL_ID_CARD_BEGIN_DATE`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="originalLicenseEndDate != null">`ORIGINAL_LICENSE_END_DATE`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="originalLegalIdCardEndDate != null">`ORIGINAL_LEGAL_ID_CARD_END_DATE`,</if>
            <if test="originalSettlerIdCardEndDate != null">`ORIGINAL_SETTLER_ID_CARD_END_DATE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="salemanId != null">`SALEMAN_ID`,</if>
            <if test="licenseIsLong != null">`LICENSE_IS_LONG`,</if>
            <if test="processStatus != null">`PROCESS_STATUS`,</if>
            <if test="legalIdCardIsLong != null">`LEGAL_ID_CARD_IS_LONG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="qualification != null">`QUALIFICATION`,</if>
            <if test="credentialsStatus != null">`CREDENTIALS_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="merchantNo != null"> `MERCHANT_NO`,</if>
            <if test="isNotice != null">`IS_NOTICE`,</if>
            <if test="legalIdCardExpiredFlag != null">`legal_id_card_expired_flag`,</if>
            <if test="licenseExpiredFlag != null">`license_expired_flag`,</if>
            <if test="settlerIdCardExpiredFlag != null">`settler_id_card_expired_flag`,</if>
            <if test="last30TradeNum != null">`last_30_trade_num`,</if>
            <if test="last30TradeMoney != null">`last_30_trade_money`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="licenseEndDate != null">#{licenseEndDate,jdbcType=VARCHAR},</if>
            <if test="licenseBeginDate != null">#{licenseBeginDate,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBackPic != null">#{legalIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardEndDate != null">#{legalIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="legalIdCardFrontPic != null">#{legalIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBeginDate != null">#{legalIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="originalLicenseEndDate != null">#{originalLicenseEndDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="originalLegalIdCardEndDate != null">#{originalLegalIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="originalSettlerIdCardEndDate != null">#{originalSettlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=TINYINT},</if>
            <if test="salemanId != null">#{salemanId,jdbcType=INTEGER},</if>
            <if test="licenseIsLong != null">#{licenseIsLong,jdbcType=TINYINT},</if>
            <if test="processStatus != null">#{processStatus,jdbcType=TINYINT},</if>
            <if test="legalIdCardIsLong != null">#{legalIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="qualification != null">#{qualification,jdbcType=TINYINT},</if>
            <if test="credentialsStatus != null">#{credentialsStatus,jdbcType=TINYINT},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="isNotice != null">#{isNotice,jdbcType=TINYINT},</if>
            <if test="legalIdCardExpiredFlag != null">#{legalIdCardExpiredFlag,jdbcType=TINYINT},</if>
            <if test="licenseExpiredFlag != null">#{licenseExpiredFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardExpiredFlag != null">#{settlerIdCardExpiredFlag,jdbcType=TINYINT},</if>
            <if test="last30TradeNum != null">#{last30TradeNum,jdbcType=INTEGER},</if>
            <if test="last30TradeMoney != null">#{last30TradeMoney,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据商户id查询临期记录-->
            <select id="getByUid" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        `TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD`
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        and process_status in (0, 1, 3)
        AND `IS_DEL` = 0
        limit 1;
            </select>
    </mapper>
