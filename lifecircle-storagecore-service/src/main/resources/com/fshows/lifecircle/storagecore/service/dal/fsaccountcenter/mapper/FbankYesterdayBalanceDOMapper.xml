<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.FbankYesterdayBalanceDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.FbankYesterdayBalanceDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_DAY" property="tradeDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BALANCE" property="balance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ACCOUNT_NO`,`TRADE_DAY`,`CREATE_TIME`,`UPDATE_TIME`,`BALANCE`
    </sql>


            <!--insert:TP_FBANK_YESTERDAY_BALANCE-->
            <insert id="insert" >
                    INSERT INTO TP_FBANK_YESTERDAY_BALANCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="accountNo != null">`ACCOUNT_NO`,</if>
            <if test="tradeDay != null">`TRADE_DAY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balance != null">`BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="tradeDay != null">#{tradeDay,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--批量插入-->
            <insert id="insertBatch" >
                    INSERT INTO TP_FBANK_YESTERDAY_BALANCE(
        ACCOUNT_NO,
        TRADE_DAY,
        BALANCE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountNo,jdbcType=VARCHAR},
            #{item.tradeDay,jdbcType=INTEGER},
            #{item.balance,jdbcType=DECIMAL}
            )
        </foreach>
            </insert>
    </mapper>
