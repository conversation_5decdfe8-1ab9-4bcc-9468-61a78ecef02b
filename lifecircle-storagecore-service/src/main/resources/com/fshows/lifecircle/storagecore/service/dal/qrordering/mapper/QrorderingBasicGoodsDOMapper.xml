<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingBasicGoodsDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingBasicGoodsDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="NAME" property="name" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IMAGE" property="image" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SOURCE" property="source" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`NAME`,`IMAGE`,`SOURCE`,`GOODS_ID`,`DEL_FLAG`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_QRORDERING_BASIC_GOODS-->
    <insert id="insert">
        INSERT INTO TP_QRORDERING_BASIC_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="image != null">`IMAGE`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="goodsId != null">`GOODS_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="image != null">#{image,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--findByNames-->
    <select id="findByNames" resultMap="BaseResultMap">
        select /*MS-TP-QRORDERING-BASIC-GOODS-FINDBYNAMES*/<include refid="Base_Column_List"/>from
        TP_QRORDERING_BASIC_GOODS
        where `name` in
        <foreach collection="list" item="name" separator="," open="(" close=")">
            #{name,jdbcType=VARCHAR}
        </foreach>
        and DEL_FLAG=0
    </select>

    <!--findByNamesAndGroupByName-->
    <select id="findByNamesAndGroupByName" resultMap="BaseResultMap">
        select /*MS-TP-QRORDERING-BASIC-GOODS-FINDBYNAMESANDGROUPBYNAME*/<include refid="Base_Column_List"/>from
        TP_QRORDERING_BASIC_GOODS
        where `name` in
        <foreach collection="list" item="name" separator="," open="(" close=")">
            #{name,jdbcType=VARCHAR}
        </foreach>
        and DEL_FLAG=0
        GROUP BY `name`
    </select>
</mapper>
