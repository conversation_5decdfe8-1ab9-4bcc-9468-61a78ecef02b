<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.MarketCrmStoreDismissReasonRecordDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmStoreDismissReasonRecordDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DISMISS_REASON" property="dismissReason" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_AUDIT_RECORD_ID" property="storeAuditRecordId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARKS`,`CREATE_BY`,`UPDATE_BY`,`DISMISS_REASON`,`STORE_AUDIT_RECORD_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_CRM_STORE_DISMISS_REASON_RECORD-->
    <insert id="insert">
        INSERT INTO TP_CRM_STORE_DISMISS_REASON_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="dismissReason != null">`DISMISS_REASON`,</if>
            <if test="storeAuditRecordId != null">`STORE_AUDIT_RECORD_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="dismissReason != null">#{dismissReason,jdbcType=VARCHAR},</if>
            <if test="storeAuditRecordId != null">#{storeAuditRecordId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--根据门店审核id查询信息-->
    <select id="getByStoreAuditRecordId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-CRM-STORE-DISMISS-REASON-RECORD-GETBYSTOREAUDITRECORDID*/  dismiss_reason as dismissReason FROM tp_crm_store_dismiss_reason_record WHERE store_audit_record_id
        =#{storeAuditRecordId,jdbcType=VARCHAR}
            </select>

    <!--根据门店ID查询拒绝原因-->
    <select id="getRecoedByStoreId" resultMap="BaseResultMap">
        select /*MS-TP-CRM-STORE-DISMISS-REASON-RECORD-GETRECOEDBYSTOREID*/
        <include refid="Base_Column_List"/>
        from tp_crm_store_dismiss_reason_record where
        store_audit_record_id=
        (select /*MS-TP-CRM-STORE-DISMISS-REASON-RECORD-GETRECOEDBYSTOREID*/ audit_record_id from
        tp_crm_store_audit_record where store_id=#{store_id,jdbcType=INTEGER}
        AND (audit_result = 3 OR audit_result = 4 )
        AND (audit_type = 2 OR audit_type = 3 ) order by
        update_time desc limit 1)
    </select>

    <!--获取门店审核原因列表-->
    <select id="getStoreDismissReasonList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-CRM-STORE-DISMISS-REASON-RECORD-GETSTOREDISMISSREASONLIST*/  store_audit_record_id, dismiss_reason
        FROM tp_crm_store_dismiss_reason_record
            </select>

    <!--根据门店ID列表查询拒绝原因-->
    <select id="getRecoedByStoreIdList"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.StoreDismissReasonDO">
        SELECT
        a.store_id storeId,
        a.audit_result auditResult,
        a.audit_time auditTime,
        c.store_audit_record_id recordId,
        c.dismiss_reason reason
        FROM
        tp_crm_store_dismiss_reason_record c
        RIGHT JOIN (
        SELECT
        update_time,
        audit_record_id,
        store_id,
        audit_result,
        audit_time
        FROM
        (
        SELECT
        update_time,
        audit_record_id,
        store_id,
        audit_result,
        audit_time
        FROM
        tp_crm_store_audit_record
        WHERE
        store_id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="list">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND ( audit_result = 3 OR audit_result = 4 )
        AND ( audit_type = 2 OR audit_type = 3 )
        ORDER BY
        update_time DESC
        ) b
        GROUP BY
        b.store_id) a
        ON c.store_audit_record_id = a.audit_record_id
    </select>

    <!--根据门店ID查询最近的一条审核记录和原因-->
    <select id="getAuditRecoedByStoreIds"
            resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.StoreDismissReasonDO">
        SELECT
        a.store_id storeId,
        a.audit_result auditResult,
        a.audit_time auditTime,
        c.store_audit_record_id recordId,
        c.dismiss_reason reason
        FROM
        tp_crm_store_dismiss_reason_record c
        RIGHT JOIN (
        SELECT
        update_time,
        audit_record_id,
        store_id,
        audit_result,
        audit_time
        FROM
        (
        SELECT
        update_time,
        audit_record_id,
        store_id,
        audit_result,
        audit_time
        FROM
        tp_crm_store_audit_record
        WHERE
        store_id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="list">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND ( audit_type = 2 OR audit_type = 3 )
        ORDER BY
        update_time DESC
        ) b
        GROUP BY
        b.store_id) a
        ON c.store_audit_record_id = a.audit_record_id
    </select>
</mapper>
