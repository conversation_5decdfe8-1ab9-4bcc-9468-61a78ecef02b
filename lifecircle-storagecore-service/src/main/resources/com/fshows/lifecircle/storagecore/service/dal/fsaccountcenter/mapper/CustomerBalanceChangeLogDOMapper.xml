<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.CustomerBalanceChangeLogDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.CustomerBalanceChangeLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHANGE_TYPE" property="changeType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHANGE_REMARK" property="changeRemark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ACCOUNT_ID" property="customerAccountId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="AFTER_PETTY_CASH" property="afterPettyCash" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="FRONT_PETTY_CASH" property="frontPettyCash" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="CHANGE_PETTY_CASH" property="changePettyCash" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="AFTER_TOTAL_BALANCE" property="afterTotalBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="FRONT_TOTAL_BALANCE" property="frontTotalBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="CHANGE_TOTAL_BALANCE" property="changeTotalBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="AFTER_WITHDRAWABLE_BALANCE" property="afterWithdrawableBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="FRONT_WITHDRAWABLE_BALANCE" property="frontWithdrawableBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="CHANGE_WITHDRAWABLE_BALANCE" property="changeWithdrawableBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`ORDER_SN`,`CHANGE_TYPE`,`CUSTOMER_ID`,`CHANGE_REMARK`,`CUSTOMER_ACCOUNT_ID`,`CREATE_TIME`,`UPDATE_TIME`,`AFTER_PETTY_CASH`,`FRONT_PETTY_CASH`,`CHANGE_PETTY_CASH`,`AFTER_TOTAL_BALANCE`,`FRONT_TOTAL_BALANCE`,`CHANGE_TOTAL_BALANCE`,`AFTER_WITHDRAWABLE_BALANCE`,`FRONT_WITHDRAWABLE_BALANCE`,`CHANGE_WITHDRAWABLE_BALANCE`
        </sql>


        <!--insert:TP_CUSTOMER_BALANCE_CHANGE_LOG-->
        <insert id="insert">
            INSERT INTO TP_CUSTOMER_BALANCE_CHANGE_LOG
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="changeType != null">`CHANGE_TYPE`,</if>
                <if test="customerId != null">`CUSTOMER_ID`,</if>
                <if test="changeRemark != null">`CHANGE_REMARK`,</if>
                <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="afterPettyCash != null">`AFTER_PETTY_CASH`,</if>
            <if test="frontPettyCash != null">`FRONT_PETTY_CASH`,</if>
            <if test="changePettyCash != null">`CHANGE_PETTY_CASH`,</if>
            <if test="afterTotalBalance != null">`AFTER_TOTAL_BALANCE`,</if>
            <if test="frontTotalBalance != null">`FRONT_TOTAL_BALANCE`,</if>
            <if test="changeTotalBalance != null">`CHANGE_TOTAL_BALANCE`,</if>
            <if test="afterWithdrawableBalance != null">`AFTER_WITHDRAWABLE_BALANCE`,</if>
            <if test="frontWithdrawableBalance != null">`FRONT_WITHDRAWABLE_BALANCE`,</if>
            <if test="changeWithdrawableBalance != null">`CHANGE_WITHDRAWABLE_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="changeType != null">#{changeType,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="afterPettyCash != null">#{afterPettyCash,jdbcType=DECIMAL},</if>
            <if test="frontPettyCash != null">#{frontPettyCash,jdbcType=DECIMAL},</if>
            <if test="changePettyCash != null">#{changePettyCash,jdbcType=DECIMAL},</if>
            <if test="afterTotalBalance != null">#{afterTotalBalance,jdbcType=DECIMAL},</if>
            <if test="frontTotalBalance != null">#{frontTotalBalance,jdbcType=DECIMAL},</if>
            <if test="changeTotalBalance != null">#{changeTotalBalance,jdbcType=DECIMAL},</if>
            <if test="afterWithdrawableBalance != null">#{afterWithdrawableBalance,jdbcType=DECIMAL},</if>
            <if test="frontWithdrawableBalance != null">#{frontWithdrawableBalance,jdbcType=DECIMAL},</if>
            <if test="changeWithdrawableBalance != null">#{changeWithdrawableBalance,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据分账订单号和分账类型类型查询变更记录-->
        <select id="getListByOrderSnAndChangeType" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from
            tp_customer_balance_change_log
            where
            change_type = 'SHARE'
            and
            order_sn in
            <foreach close=")" collection="list" index="index" item="orderSn" open="(" separator=",">
                #{orderSn,jdbcType=VARCHAR}
            </foreach>
        </select>
    </mapper>
