<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentOrderRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentOrderRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STOCK_ORDER" property="stockOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SN_ID" property="snId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>

        <resultMap id="ShipmentEquipmentSnDo" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ShipmentEquipmentSnDO">

                <result column="id" property="id" javaType="java.lang.Integer"/>

                <result column="system_sn" property="systemSn" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`ORDER_NO`,`STOCK_ORDER`,`SN_ID`,`DEPOT`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_ORDER_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_ORDER_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="stockOrder != null">`STOCK_ORDER`,</if>
            <if test="snId != null">`SN_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="stockOrder != null">#{stockOrder,jdbcType=VARCHAR},</if>
            <if test="snId != null">#{snId,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--getByEquipmentSn-->
            <select id="getByEquipmentSn" resultMap="ShipmentEquipmentSnDo">
                    SELECT /*MS-HW-EQUIPMENT-ORDER-RELATION-GETBYEQUIPMENTSN*/  esn.id,esn.system_sn,equipment.equipment_name,equipment.equipment_model
        from hw_equipment_order_relation relation
        LEFT JOIN hw_equipment_sn esn on esn.id = relation.sn_id
        LEFT JOIN hw_equipment equipment on equipment.id = esn.equipment_id
        WHERE relation.order_no = #{orderNo,jdbcType=VARCHAR}
        <if test="equipmentSn!=null and equipmentSn!=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
            </select>

            <!--根据订单号获取snId-->
            <select id="getSnIdList" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-ORDER-RELATION-GETSNIDLIST*/  <include refid="Base_Column_List" />
        FROM HW_EQUIPMENT_ORDER_RELATION
        WHERE is_del = 0
        <if test="orderNo !=null and orderNo !=''">
            AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="stockOrder != null and stockOrder !=''">
            AND stock_order = #{stockOrder,jdbcType=VARCHAR}
        </if>
            </select>

            <!--新代理商后台出入库订单详情导出-->
            <select id="getSnListByOrderNo" resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AgentEquipmentSnListMap">
                    SELECT /*MS-HW-EQUIPMENT-ORDER-RELATION-GETSNLISTBYORDERNO*/  s.init_sn AS initSn,
        eq.equipment_name AS equipmentName,
        eq.equipment_model AS equipmentModel
        FROM hw_equipment_order_relation AS r
        LEFT JOIN hw_equipment_sn AS s ON r.sn_id = s.id
        LEFT JOIN hw_equipment AS eq ON s.equipment_id = eq.id
        WHERE r.order_no = #{orderNo,jdbcType=VARCHAR}
        AND r.is_del = 0
        AND s.is_del = 0
            </select>

            <!--根据snId获取最近一次出库仓库位置-->
            <select id="getLastTimeOutStorageInfoBySnId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        hw_equipment_order_relation
        WHERE sn_id = #{snId, jdbcType=INTEGER}
        AND order_no like 'SDB%'
        ORDER BY create_time DESC
        LIMIT 1;
            </select>
    </mapper>
