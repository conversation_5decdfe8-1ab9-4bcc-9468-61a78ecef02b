<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleDirectRefundDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleDirectRefundDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_CODE" property="refundCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_INFO" property="refundInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FACE_DEVICE_SN" property="faceDeviceSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_SN" property="platformOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_REFUND_SN" property="merchantRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_REFUND_SN" property="platformRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_REFUND_ORDER_SN" property="goodsRefundOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HANDLER" property="handler" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_MORE_DAY" property="isMoreDay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_TYPE" property="consumeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PART_REFUND" property="isPartRefund" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_STATUS" property="refundStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_AGENT_ID" property="refundAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_CHANNEL" property="refundChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONSUME_CHANNEL" property="consumeChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_FAILED_DEAL" property="refundFailedDeal" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REFUND" property="refund" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="POUNDAGE" property="poundage" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_MONEY" property="orderMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_MONEY" property="refundMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="NO_CASH_COUPON_FEE" property="noCashCouponFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`TOKEN`,`REMARK`,`ORDER_SN`,`DEVICE_NO`,`REFUND_SN`,`NOTIFY_URL`,`REFUND_CODE`,`REFUND_INFO`,`FACE_DEVICE_SN`,`MERCHANT_ORDER_SN`,`PLATFORM_ORDER_SN`,`MERCHANT_REFUND_SN`,`PLATFORM_REFUND_SN`,`GOODS_REFUND_ORDER_SN`,`USER_ID`,`GRANT_ID`,`HANDLER`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`MARKET_ID`,`CASHIER_ID`,`IS_MORE_DAY`,`CONSUME_TYPE`,`SUB_CONFIG_ID`,`IS_PART_REFUND`,`REFUND_STATUS`,`REFUND_AGENT_ID`,`REFUND_CHANNEL`,`CONSUME_CHANNEL`,`REFUND_FAILED_DEAL`,`CREATE_TIME`,`UPDATE_TIME`,`REFUND`,`POUNDAGE`,`ORDER_MONEY`,`REFUND_MONEY`,`NO_CASH_COUPON_FEE`
    </sql>


            <!--findOrderRefundType-->
            <select id="findOrderRefundType" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.OrderRefundTypeDTO">
                    select
        order_sn as orderSn,
        is_part_refund as isPartRefund,
        sum( `refund_money`) as refundMoney,
        sum(poundage) as refundFee,
         count(*)  as refundedCount
        from TP_LIFECIRCLE_DIRECT_REFUND
        where refund_status in (1,2)
        and order_sn in
        <foreach collection="list" item="orderSn" open="(" separator="," close=")">
            #{orderSn,jdbcType=INTEGER}
        </foreach>
        GROUP BY order_sn
            </select>
    </mapper>
