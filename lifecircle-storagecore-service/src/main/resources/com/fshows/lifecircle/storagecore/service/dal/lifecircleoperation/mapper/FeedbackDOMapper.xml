<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.mapper.FeedbackDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.dataobject.FeedbackDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="UUID" property="uuid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMAGES" property="images" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MOBILE" property="mobile" jdbcType="CHAR"
        javaType="String"/>

            <result column="CONTENT" property="content" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APP_VERSION" property="appVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FEEDBACK_ID" property="feedbackId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE_MODEL" property="phoneModel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE_VERSION" property="phoneVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APP_TYPE" property="appType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ROLE_TYPE" property="roleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UUID`,`TOKEN`,`IMAGES`,`MOBILE`,`CONTENT`,`USERNAME`,`APP_VERSION`,`FEEDBACK_ID`,`PHONE_MODEL`,`PHONE_VERSION`,`UID`,`APP_TYPE`,`ROLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_FEEDBACK-->
            <insert id="insert" >
                    INSERT INTO TP_FEEDBACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uuid != null">`UUID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="images != null">`IMAGES`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="feedbackId != null">`FEEDBACK_ID`,</if>
            <if test="phoneModel != null">`PHONE_MODEL`,</if>
            <if test="phoneVersion != null">`PHONE_VERSION`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="appType != null">`APP_TYPE`,</if>
            <if test="roleType != null">`ROLE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uuid != null">#{uuid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="images != null">#{images,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=CHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="feedbackId != null">#{feedbackId,jdbcType=VARCHAR},</if>
            <if test="phoneModel != null">#{phoneModel,jdbcType=VARCHAR},</if>
            <if test="phoneVersion != null">#{phoneVersion,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="appType != null">#{appType,jdbcType=TINYINT},</if>
            <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取问题反馈列表-->
            <select id="getFeedBackList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-FEEDBACK-GETFEEDBACKLIST*/  <include refid="Base_Column_List" /> FROM TP_FEEDBACK
        <where>
            <trim prefix="AND" prefixOverrides="AND|OR">
                <if test="startDate != null and endDate != null">
                    AND create_time BETWEEN #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
                </if>
                <if test="username != null">
                    AND username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
                </if>
                <if test="appType != null">
                    AND app_type = #{appType,jdbcType=TINYINT}
                </if>
            </trim>
        </where>
        ORDER BY create_time DESC
            </select>
    </mapper>
