<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.mapper.FsbankplatformguardianDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
        SELECT CONCAT('表结构不一致 tbName:', tb_name, ' 期望字段:', exp_columns, ' 数据库中字段:', db_columns) as msg
        FROM (
                 SELECT COUNT(*) = 23                   as                                                                                                                                                                                                                                                   fg,
                        'PG_SHARE_ORDER_RECONCILIATION' as                                                                                                                                                                                                                                                   tb_name,
                        group_concat(COLUMN_NAME)                                                                                                                                                                                                                                                            db_columns,
                        'ID,IS_DEL,TOKEN,REASON,ORDER_SN,FAIL_TYPE,PLATFORM,DEAL_STATE,MERCHANT_NO,SHARE_REQ_NO,FB_FINISH_TIME,FB_SHARE_STATE,PLATFORM_REQ_NO,PLATFORM_FINISH_TIME,PLATFORM_SHARE_STATE,UID,CHANNEL,HANDLE_DATE,CREATE_TIME,UPDATE_TIME,ABNORMAL_FUND,FB_TOTAL_AMOUNT,PLATFORM_TOTAL_AMOUNT' exp_columns
                 FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_NAME = 'PG_SHARE_ORDER_RECONCILIATION'
                   AND COLUMN_NAME in
                       ('ID', 'IS_DEL', 'TOKEN', 'REASON', 'ORDER_SN', 'FAIL_TYPE', 'PLATFORM', 'DEAL_STATE',
                        'MERCHANT_NO', 'SHARE_REQ_NO', 'FB_FINISH_TIME', 'FB_SHARE_STATE', 'PLATFORM_REQ_NO',
                        'PLATFORM_FINISH_TIME', 'PLATFORM_SHARE_STATE', 'UID', 'CHANNEL', 'HANDLE_DATE', 'CREATE_TIME',
                        'UPDATE_TIME', 'ABNORMAL_FUND', 'FB_TOTAL_AMOUNT', 'PLATFORM_TOTAL_AMOUNT')
                 GROUP BY TABLE_NAME
                 UNION ALL
                 SELECT COUNT(*) = 19             as                                                                                                                                                                                                                fg,
                        'PG_PLATFORM_SHARE_ORDER' as                                                                                                                                                                                                                tb_name,
                        group_concat(COLUMN_NAME)                                                                                                                                                                                                                   db_columns,
                        'ID,TRADE_NO,REFUND_SN,SHARE_TIME,TRADE_TYPE,MERCHANT_NO,SETTLE_DATE,SHARE_REQ_NO,SHARE_STATE,SHARE_DETAIL,PLATFORM_REQ_NO,FEE_ATTRIBUTION,PLATFORM_REFUND_SN,SHARE_STATE_REMARK,CREATE_TIME,UPDATE_TIME,FEE,SHARE_PRICE,OTHER_SHARE_PRICE' exp_columns
                 FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_NAME = 'PG_PLATFORM_SHARE_ORDER'
                   AND COLUMN_NAME in
                       ('ID', 'TRADE_NO', 'REFUND_SN', 'SHARE_TIME', 'TRADE_TYPE', 'MERCHANT_NO', 'SETTLE_DATE',
                        'SHARE_REQ_NO', 'SHARE_STATE', 'SHARE_DETAIL', 'PLATFORM_REQ_NO', 'FEE_ATTRIBUTION',
                        'PLATFORM_REFUND_SN', 'SHARE_STATE_REMARK', 'CREATE_TIME', 'UPDATE_TIME', 'FEE', 'SHARE_PRICE',
                        'OTHER_SHARE_PRICE')
                 GROUP BY TABLE_NAME
                 UNION ALL
                 SELECT COUNT(*) = 9         as                                                                   fg,
                        'PG_SHARE_BILL_FILE' as                                                                   tb_name,
                        group_concat(COLUMN_NAME)                                                                 db_columns,
                        'ID,STATE,FILE_URL,CHANNEL,HANDLE_DATE,CREATE_TIME,UPDATE_TIME,SHARE_AMOUNT,SHARE_INCOME' exp_columns
                 FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_NAME = 'PG_SHARE_BILL_FILE'
                   AND COLUMN_NAME in
                       ('ID', 'STATE', 'FILE_URL', 'CHANNEL', 'HANDLE_DATE', 'CREATE_TIME', 'UPDATE_TIME',
                        'SHARE_AMOUNT', 'SHARE_INCOME')
                 GROUP BY TABLE_NAME
                 UNION ALL
                 SELECT COUNT(*) = 19                          as                                                                                                                                                                                      fg,
                        'PG_SHARE_ORDER_DETAIL_RECONCILIATION' as                                                                                                                                                                                      tb_name,
                        group_concat(COLUMN_NAME)                                                                                                                                                                                                      db_columns,
                        'ID,IS_DEL,TOKEN,ORDER_SN,FAIL_TYPE,DEAL_STATE,CUSTOMER_ID,MERCHANT_ID,SHARE_REQ_NO,FB_FINISH_TIME,PLATFORM_REQ_NO,PLATFORM_FINISH_TIME,UID,CHANNEL,HANDLE_DATE,CREATE_TIME,UPDATE_TIME,FB_TOTAL_AMOUNT,PLATFORM_TOTAL_AMOUNT' exp_columns
                 FROM INFORMATION_SCHEMA.COLUMNS
                 WHERE TABLE_NAME = 'PG_SHARE_ORDER_DETAIL_RECONCILIATION'
                   AND COLUMN_NAME in
                       ('ID', 'IS_DEL', 'TOKEN', 'ORDER_SN', 'FAIL_TYPE', 'DEAL_STATE', 'CUSTOMER_ID', 'MERCHANT_ID',
                        'SHARE_REQ_NO', 'FB_FINISH_TIME', 'PLATFORM_REQ_NO', 'PLATFORM_FINISH_TIME', 'UID', 'CHANNEL',
                        'HANDLE_DATE', 'CREATE_TIME', 'UPDATE_TIME', 'FB_TOTAL_AMOUNT', 'PLATFORM_TOTAL_AMOUNT')
                 GROUP BY TABLE_NAME
             ) a
        WHERE fg = 0
    </select>
</mapper>
