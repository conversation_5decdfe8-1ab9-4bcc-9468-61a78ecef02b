<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.PlatformSettleBillDataDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.PlatformSettleBillDataDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="DATA_ID" property="dataId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OSS_URL" property="ossUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONFIG_ID" property="configId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DOWNLOAD_ID" property="downloadId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="END_DATE" property="endDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RUN_DATE" property="runDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_DATE" property="startDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`DATA_ID`,`OSS_URL`,`CONFIG_ID`,`DATA_SOURCE`,`DOWNLOAD_ID`,`PLATFORM_CODE`,`END_DATE`,`RUN_DATE`,`START_DATE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_PLATFORM_SETTLE_BILL_DATA-->
            <insert id="insert" >
            INSERT INTO ACC_PLATFORM_SETTLE_BILL_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="dataId != null">`DATA_ID`,</if>
        <if test="ossUrl != null">`OSS_URL`,</if>
        <if test="configId != null">`CONFIG_ID`,</if>
        <if test="dataSource != null">`DATA_SOURCE`,</if>
        <if test="downloadId != null">`DOWNLOAD_ID`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="endDate != null">`END_DATE`,</if>
        <if test="runDate != null">`RUN_DATE`,</if>
        <if test="startDate != null">`START_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="goodsSettleAmount != null">`goods_settle_amount`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="dataId != null">#{dataId,jdbcType=VARCHAR},</if>
        <if test="ossUrl != null">#{ossUrl,jdbcType=VARCHAR},</if>
        <if test="configId != null">#{configId,jdbcType=VARCHAR},</if>
        <if test="dataSource != null">#{dataSource,jdbcType=VARCHAR},</if>
        <if test="downloadId != null">#{downloadId,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="endDate != null">#{endDate,jdbcType=INTEGER},</if>
        <if test="runDate != null">#{runDate,jdbcType=INTEGER},</if>
        <if test="startDate != null">#{startDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="goodsSettleAmount != null">#{item.goodsSettleAmount,jdbcType=BIGINT},</if>
    </trim>
            </insert>

            <!--批量新增-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO ACC_PLATFORM_SETTLE_BILL_DATA (
        data_id,
        config_id,
        platform_code,
        data_source,
        download_id,
        run_date,
        start_date,
        end_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dataId,jdbcType=VARCHAR},
            #{item.configId,jdbcType=VARCHAR},
            #{item.platformCode,jdbcType=VARCHAR},
            #{item.dataSource,jdbcType=VARCHAR},
            #{item.downloadId,jdbcType=VARCHAR},
            #{item.runDate,jdbcType=INTEGER},
            #{item.startDate,jdbcType=INTEGER},
            #{item.endDate,jdbcType=INTEGER}
            )
        </foreach>
            </select>

            <!--查询待处理的数据列表-->
            <select id="selectByDataIdList" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        ACC_PLATFORM_SETTLE_BILL_DATA
        where
        data_id in
        <foreach collection="list" item="dataId" open="(" separator="," close=")">
            #{dataId,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--更新oss地址-->
            <update id="updateOssUrlByDataId" >
                    update /*MS-ACC-PLATFORM-SETTLE-BILL-DATA-UPDATEOSSURLBYDATAID*/ ACC_PLATFORM_SETTLE_BILL_DATA set oss_url = #{ossUrl,jdbcType=VARCHAR} where data_id = #{dataId,jdbcType=VARCHAR}
            </update>
    </mapper>
