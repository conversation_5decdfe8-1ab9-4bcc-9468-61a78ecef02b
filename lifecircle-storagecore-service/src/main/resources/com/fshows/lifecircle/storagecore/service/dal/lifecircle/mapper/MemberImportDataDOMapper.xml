<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MemberImportDataDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MemberImportDataDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EMAIL" property="email" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="CHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PASSWORD" property="password" jdbcType="CHAR"
        javaType="String"/>

            <result column="REAL_NAME" property="realName" jdbcType="CHAR"
        javaType="String"/>

            <result column="ALIPAY_USERID" property="alipayUserid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_CARD_NO" property="importCardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SEX" property="sex" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIRTH_DAY" property="birthDay" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="INTEGRAL" property="integral" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIRTH_YEAR" property="birthYear" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="BIRTH_MONTH" property="birthMonth" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="SOURCE_FILE_ID" property="sourceFileId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="COLLAR_CARD_TIME" property="collarCardTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LAST_CONSUME_TIME" property="lastConsumeTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CARD_RECHARGE_AMOUNT" property="cardRechargeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="GIFT_RECHARGE_AMOUNT" property="giftRechargeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_RECHARGE_AMOUNT" property="actualRechargeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EMAIL`,`PHONE`,`TOKEN`,`CARD_NO`,`PASSWORD`,`REAL_NAME`,`ALIPAY_USERID`,`IMPORT_CARD_NO`,`SEX`,`BIRTH_DAY`,`INTEGRAL`,`BIRTH_YEAR`,`BIRTH_MONTH`,`SOURCE_FILE_ID`,`COLLAR_CARD_TIME`,`LAST_CONSUME_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`CARD_RECHARGE_AMOUNT`,`GIFT_RECHARGE_AMOUNT`,`ACTUAL_RECHARGE_AMOUNT`
    </sql>


            <!--insert:TP_MEMBER_IMPORT_DATA-->
            <insert id="insert" >
            INSERT INTO TP_MEMBER_IMPORT_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="email != null">`EMAIL`,</if>
        <if test="phone != null">`PHONE`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="realName != null">`REAL_NAME`,</if>
        <if test="alipayUserid != null">`ALIPAY_USERID`,</if>
        <if test="importCardNo != null">`IMPORT_CARD_NO`,</if>
        <if test="sex != null">`SEX`,</if>
        <if test="birthDay != null">`BIRTH_DAY`,</if>
        <if test="integral != null">`INTEGRAL`,</if>
        <if test="birthYear != null">`BIRTH_YEAR`,</if>
        <if test="birthMonth != null">`BIRTH_MONTH`,</if>
        <if test="sourceFileId != null">`SOURCE_FILE_ID`,</if>
        <if test="collarCardTime != null">`COLLAR_CARD_TIME`,</if>
        <if test="lastConsumeTime != null">`LAST_CONSUME_TIME`,</if>
        <if test="password != null">`PASSWORD`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="cardRechargeAmount != null">`CARD_RECHARGE_AMOUNT`,</if>
        <if test="giftRechargeAmount != null">`GIFT_RECHARGE_AMOUNT`,</if>
        <if test="actualRechargeAmount != null">`ACTUAL_RECHARGE_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="email != null">#{email,jdbcType=VARCHAR},</if>
        <if test="phone != null">#{phone,jdbcType=CHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="realName != null">#{realName,jdbcType=CHAR},</if>
        <if test="alipayUserid != null">#{alipayUserid,jdbcType=VARCHAR},</if>
        <if test="importCardNo != null">#{importCardNo,jdbcType=VARCHAR},</if>
        <if test="sex != null">#{sex,jdbcType=TINYINT},</if>
        <if test="birthDay != null">#{birthDay,jdbcType=SMALLINT},</if>
        <if test="integral != null">#{integral,jdbcType=INTEGER},</if>
        <if test="birthYear != null">#{birthYear,jdbcType=SMALLINT},</if>
        <if test="birthMonth != null">#{birthMonth,jdbcType=SMALLINT},</if>
        <if test="sourceFileId != null">#{sourceFileId,jdbcType=INTEGER},</if>
        <if test="collarCardTime != null">#{collarCardTime,jdbcType=INTEGER},</if>
        <if test="lastConsumeTime != null">#{lastConsumeTime,jdbcType=INTEGER},</if>
        <if test="password != null">#{password,jdbcType=CHAR},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="cardRechargeAmount != null">#{cardRechargeAmount,jdbcType=DECIMAL},</if>
        <if test="giftRechargeAmount != null">#{giftRechargeAmount,jdbcType=DECIMAL},</if>
        <if test="actualRechargeAmount != null">#{actualRechargeAmount,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据条件查询是否已存在导入记录-->
            <select id="countRecord" resultType="Integer">
                    SELECT /*MS-TP-MEMBER-IMPORT-DATA-COUNTRECORD*/   count(*) 
        FROM TP_MEMBER_IMPORT_DATA
        WHERE token=#{token,jdbcType=VARCHAR} AND phone=#{phone,jdbcType=CHAR}
            </select>
    </mapper>
