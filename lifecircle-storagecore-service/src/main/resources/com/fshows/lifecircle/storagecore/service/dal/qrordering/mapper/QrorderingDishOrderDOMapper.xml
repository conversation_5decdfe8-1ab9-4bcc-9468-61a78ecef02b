<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingDishOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingDishOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT4" property="ext4" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT5" property="ext5" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT6" property="ext6" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT7" property="ext7" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT8" property="ext8" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT9" property="ext9" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXT10" property="ext10" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SUB_APPID" property="subAppid" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SUB_MCHID" property="subMchid" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ADDRESS_ID" property="addressId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TABLE_CODE" property="tableCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_ENTRY" property="orderEntry" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_STORE_ID" property="outStoreId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PAY_ORDER_NO" property="payOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACCESS_TOKEN" property="accessToken" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DISH_ORDER_NO" property="dishOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GROUP_FLOW_NO" property="groupFlowNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_POSTER" property="orderPoster" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_NAME" property="receiptName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TABLE_CODE_ID" property="tableCodeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_PHONE" property="receiptPhone" jdbcType="CHAR"
                    javaType="String"/>

            <result column="SUB_APPSECRET" property="subAppsecret" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UPLOAD_ORDER_NO" property="uploadOrderNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_ADDRESS" property="receiptAddress" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="APPLICATION_CODE" property="applicationCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="APPOINTMENT_TIME" property="appointmentTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GROUP_ACTIVITY_ID" property="groupActivityId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="APPOINTMENT_PHONE" property="appointmentPhone" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECEIPT_NUMBER_PLATE" property="receiptNumberPlate" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TAKE_ORDER_VALIDATE_CODE" property="takeOrderValidateCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MEAL_NO" property="mealNo" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PUSH_FLAG" property="pushFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_MODE" property="orderMode" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PRINT_FLAG" property="printFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="TAKEOUT_NO" property="takeoutNo" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="MEAL_METHOD" property="mealMethod" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ACTIVITY_FLAG" property="activityFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="TAKE_ORDER_FLAG" property="takeOrderFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DELIVERY_STATUS" property="deliveryStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_GOODS_COUNT" property="orderGoodsCount" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="DISTRIBUTION_MODE" property="distributionMode" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="LAST_REPORT_STATUS" property="lastReportStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="FIRST_REPORT_STATUS" property="firstReportStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DELIVERY_CHANGE_TIME" property="deliveryChangeTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="DELIVERY_FEE" property="deliveryFee" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORDER_PACKAGE_FEE" property="orderPackageFee" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="RECEIPT_LATITUDE" property="receiptLatitude" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="RECEIPT_LONGITUDE" property="receiptLongitude" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`EXT4`,`EXT5`,`EXT6`,`EXT7`,`EXT8`,`EXT9`,`EXT10`,`REMARK`,`STORE_ID`,`SUB_APPID`,`SUB_MCHID`,`ADDRESS_ID`,`CHANNEL_ID`,`TABLE_CODE`,`ACTIVITY_ID`,`CUSTOMER_ID`,`ORDER_ENTRY`,`OUT_STORE_ID`,`PAY_ORDER_NO`,`ACCESS_TOKEN`,`DISH_ORDER_NO`,`GROUP_FLOW_NO`,`ORDER_POSTER`,`RECEIPT_NAME`,`TABLE_CODE_ID`,`RECEIPT_PHONE`,`SUB_APPSECRET`,`UPLOAD_ORDER_NO`,`RECEIPT_ADDRESS`,`APPLICATION_CODE`,`APPOINTMENT_TIME`,`GROUP_ACTIVITY_ID`,`APPOINTMENT_PHONE`,`RECEIPT_NUMBER_PLATE`,`TAKE_ORDER_VALIDATE_CODE`,`MEAL_NO`,`DEL_FLAG`,`PUSH_FLAG`,`ORDER_MODE`,`ORDER_TYPE`,`PRINT_FLAG`,`TAKEOUT_NO`,`MEAL_METHOD`,`SOURCE_TYPE`,`ORDER_STATUS`,`ACTIVITY_FLAG`,`TAKE_ORDER_FLAG`,`DELIVERY_STATUS`,`ORDER_GOODS_COUNT`,`DISTRIBUTION_MODE`,`LAST_REPORT_STATUS`,`FIRST_REPORT_STATUS`,`DELIVERY_CHANGE_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`ORDER_PRICE`,`DELIVERY_FEE`,`ORDER_PACKAGE_FEE`,`RECEIPT_LATITUDE`,`RECEIPT_LONGITUDE`
    </sql>


        <!--insert:TP_QRORDERING_DISH_ORDER-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_DISH_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="ext4 != null">`EXT4`,</if>
            <if test="ext5 != null">`EXT5`,</if>
            <if test="ext6 != null">`EXT6`,</if>
            <if test="ext7 != null">`EXT7`,</if>
            <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="subAppid != null">`SUB_APPID`,</if>
            <if test="subMchid != null">`SUB_MCHID`,</if>
            <if test="addressId != null">`ADDRESS_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="tableCode != null">`TABLE_CODE`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="orderEntry != null">`ORDER_ENTRY`,</if>
            <if test="outStoreId != null">`OUT_STORE_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="accessToken != null">`ACCESS_TOKEN`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="orderPoster != null">`ORDER_POSTER`,</if>
            <if test="tableCodeId != null">`TABLE_CODE_ID`,</if>
            <if test="subAppsecret != null">`SUB_APPSECRET`,</if>
            <if test="uploadOrderNo != null">`UPLOAD_ORDER_NO`,</if>
            <if test="applicationCode != null">`APPLICATION_CODE`,</if>
            <if test="appointmentTime != null">`APPOINTMENT_TIME`,</if>
            <if test="appointmentPhone != null">`APPOINTMENT_PHONE`,</if>
            <if test="takeOrderValidateCode != null">`TAKE_ORDER_VALIDATE_CODE`,</if>
            <if test="mealNo != null">`MEAL_NO`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="pushFlag != null">`PUSH_FLAG`,</if>
            <if test="orderMode != null">`ORDER_MODE`,</if>
            <if test="printFlag != null">`PRINT_FLAG`,</if>
            <if test="takeoutNo != null">`TAKEOUT_NO`,</if>
            <if test="mealMethod != null">`MEAL_METHOD`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="activityFlag != null">`ACTIVITY_FLAG`,</if>
            <if test="takeOrderFlag != null">`TAKE_ORDER_FLAG`,</if>
            <if test="orderGoodsCount != null">`ORDER_GOODS_COUNT`,</if>
            <if test="distributionMode != null">`DISTRIBUTION_MODE`,</if>
            <if test="lastReportStatus != null">`LAST_REPORT_STATUS`,</if>
            <if test="firstReportStatus != null">`FIRST_REPORT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="deliveryFee != null">`DELIVERY_FEE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="subAppid != null">#{subAppid,jdbcType=VARCHAR},</if>
            <if test="subMchid != null">#{subMchid,jdbcType=VARCHAR},</if>
            <if test="addressId != null">#{addressId,jdbcType=VARCHAR},</if>
            <if test="channelId != null">#{channelId,jdbcType=VARCHAR},</if>
            <if test="tableCode != null">#{tableCode,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="orderEntry != null">#{orderEntry,jdbcType=VARCHAR},</if>
            <if test="outStoreId != null">#{outStoreId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="accessToken != null">#{accessToken,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="orderPoster != null">#{orderPoster,jdbcType=VARCHAR},</if>
            <if test="tableCodeId != null">#{tableCodeId,jdbcType=VARCHAR},</if>
            <if test="subAppsecret != null">#{subAppsecret,jdbcType=VARCHAR},</if>
            <if test="uploadOrderNo != null">#{uploadOrderNo,jdbcType=VARCHAR},</if>
            <if test="applicationCode != null">#{applicationCode,jdbcType=VARCHAR},</if>
            <if test="appointmentTime != null">#{appointmentTime,jdbcType=VARCHAR},</if>
            <if test="appointmentPhone != null">#{appointmentPhone,jdbcType=VARCHAR},</if>
            <if test="takeOrderValidateCode != null">#{takeOrderValidateCode,jdbcType=VARCHAR},</if>
            <if test="mealNo != null">#{mealNo,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="pushFlag != null">#{pushFlag,jdbcType=TINYINT},</if>
            <if test="orderMode != null">#{orderMode,jdbcType=TINYINT},</if>
            <if test="printFlag != null">#{printFlag,jdbcType=TINYINT},</if>
            <if test="takeoutNo != null">#{takeoutNo,jdbcType=INTEGER},</if>
            <if test="mealMethod != null">#{mealMethod,jdbcType=TINYINT},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="activityFlag != null">#{activityFlag,jdbcType=TINYINT},</if>
            <if test="takeOrderFlag != null">#{takeOrderFlag,jdbcType=TINYINT},</if>
            <if test="orderGoodsCount != null">#{orderGoodsCount,jdbcType=INTEGER},</if>
            <if test="distributionMode != null">#{distributionMode,jdbcType=TINYINT},</if>
            <if test="lastReportStatus != null">#{lastReportStatus,jdbcType=TINYINT},</if>
            <if test="firstReportStatus != null">#{firstReportStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="deliveryFee != null">#{deliveryFee,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--商户后台订单列表-->
        <select id="findMerchantAdminOrderListByStoreId"
                resultType="com.fshows.lifecircle.storagecore.service.domain.model.MerchantAdminOrderListModel">
            SELECT
        d.store_id as storeId,
        d.dish_order_no as dishOrderNo,
        d.pay_order_no as payOrderNo,
        d.create_time as createTime,
        d.activity_flag as activityFlag,
        d.remark as remark,
        d.distribution_mode as distributionMode,
        d.address_id as addressId,
        d.takeout_no as takeoutNo,
        d.appointment_time as appointmentTime,
        p.order_sumprice as orderPrice
        FROM
        tp_qrordering_dish_order d
        INNER JOIN tp_qrordering_pay_order p ON d.pay_order_no = p.pay_order_no
        AND d.store_id = p.store_id
        WHERE d.store_id=#{storeId,jdbcType=VARCHAR}
        AND p.pay_status=2
        <if test=" distributionMode != null">
            AND d.distribution_mode=#{distributionMode,jdbcType=INTEGER}
        </if>
        <if test=" activityFlag != null">
            AND d.activity_flag=#{activityFlag,jdbcType=INTEGER}
        </if>
        AND d.create_time <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
        AND d.create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        order by d.create_time
        </select>

        <!--商户后台订单商品列表-->
        <select id="findMerchantAdminOrderGoodsListByStoreId"
                resultType="com.fshows.lifecircle.storagecore.service.domain.model.MerchantAdminOrderGoodsListModel">
            SELECT
        d.activity_flag as activityFlag,
        g.goods_id as goodsId,
        g.activity_goods_id as activityGoodsId,
        sum(g.sale_number) as saleNumber
        FROM
        tp_qrordering_dish_order d
        INNER JOIN tp_qrordering_customer_goods_log g ON d.pay_order_no = g.pay_order_no
        AND d.store_id = g.store_id
        WHERE d.store_id=#{storeId,jdbcType=VARCHAR}
        <if test="distributionMode != null">
            AND d.distribution_mode=#{distributionMode,jdbcType=INTEGER}
        </if>
        <if test="activityFlag != null">
            AND g.activity_flag=#{activityFlag,jdbcType=INTEGER}
        </if>
        AND d.create_time <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
        AND d.create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        GROUP BY g.goods_id,g.activity_goods_id
        ORDER BY saleNumber desc,d.id desc
        </select>

        <!--listAdminGroupActivityOrder-->
        <select id="listAdminGroupActivityOrder" resultMap="BaseResultMap">
                    SELECT
        a.*
        FROM tp_qrordering_dish_order a
        LEFT JOIN TP_QRORDERING_PAY_ORDER b ON a.dish_order_no = b.dish_order_no
        WHERE
        b.pay_status = 2
        AND a.store_id = #{storeId,jdbcType=VARCHAR}
        AND a.order_type = 2
        AND a.group_activity_id = #{groupActivityId,jdbcType=VARCHAR}
        AND a.del_flag = 0
        ORDER BY create_time DESC
            </select>

        <!--收银机后台团购活动订单按商品查询列表-->
        <select id="listAdminGroupActivityOrderGoods"
                resultType="com.fshows.lifecircle.storagecore.service.domain.model.AdminGroupActivityOrderGoodsModel">
                    SELECT
        store_id as storeId,
        out_store_id as outStoreId,
        activity_id as groupActivityId,
        activity_goods_id as activityGoodsId,
        sale_number as saleNumber
        FROM
        (
        SELECT
        dish.store_id,
        log.out_store_id,
        log.activity_id,
        log.activity_goods_id,
        sum(log.sale_number) as sale_number
        FROM
        tp_qrordering_dish_order dish
        INNER JOIN tp_qrordering_customer_goods_log log ON dish.pay_order_no = log.pay_order_no
        AND dish.store_id = log.store_id
        WHERE dish.store_id = #{storeId,jdbcType=VARCHAR}
        AND dish.group_activity_id = #{groupActivityId,jdbcType=VARCHAR}
        AND dish.order_type = 2
        GROUP BY dish.store_id, log.out_store_id, log.activity_id, log.activity_goods_id
        ) temp_table
        ORDER BY saleNumber DESC
            </select>

        <!--根据支付订单号查询订单信息-->
        <select id="findByPayOrderNos" resultMap="BaseResultMap">
            select
        `id`,
        `customer_id`,
        `store_id`,
        `out_store_id`,
        `take_order_validate_code`,
        `take_order_flag`,
        `dish_order_no`,
        `upload_order_no`,
        `pay_order_no`,
        `activity_id`,
        `activity_flag`,
        `meal_no`,
        `channel_id`,
        `source_type`,
        `sub_appid`,
        `sub_appsecret`,
        `sub_mchid`,
        `order_goods_count`,
        `table_code_id`,
        `table_code`,
        `remark`,
        `application_code`,
        `order_entry`,
        `first_report_status`,
        `last_report_status`,
        `order_status`,
        `push_flag`,
        `print_flag`,
        `access_token`,
        `meal_method`,
        `order_mode`,
        `appointment_time`,
        `appointment_phone`,
        `distribution_mode`,
        `takeout_no`,
        `address_id`,
        `delivery_fee`,
        `order_poster`,
        `ext1`,
        `ext2`,
        `ext3`,
        `ext4`,
        `ext5`,
        `ext6`,
        `ext7`,
        `ext8`,
        `ext9`,
        `ext10`,
        `del_flag`,
        `create_time`,
        `update_time`,
        `delivery_status`,
        `delivery_change_time`,
        `order_package_fee`,
        `order_price`,
        `order_type`,
        `group_flow_no`,
        `group_activity_id`,
        `receipt_name`,
        `receipt_phone`,
        `receipt_address`,
        `receipt_number_plate`,
        `receipt_longitude`,
        `receipt_latitude`
        from tp_qrordering_dish_order
        where pay_order_no IN
        <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
            #{payOrderNo,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
        ORDER BY id desc
        </select>
    </mapper>
