<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentWorkOrderLogDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentWorkOrderLogDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REMARK" property="remark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="WORK_ORDER_SN" property="workOrderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`INIT_SN`,`REMARK`,`OPERATOR`,`OPERATOR_ID`,`WORK_ORDER_SN`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:HW_EQUIPMENT_WORK_ORDER_LOG-->
    <insert id="insert">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--insertBatch-->
    <insert id="insertBatch">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_LOG( `INIT_SN`, `REMARK`, `OPERATOR`, `WORK_ORDER_SN`, `OPERATOR_ID`)
        values
        <foreach collection="list" item="log" separator=",">
            (
            #{log.initSn,jdbcType=VARCHAR},
            #{log.remark,jdbcType=VARCHAR},
            #{log.operator,jdbcType=VARCHAR},
            #{log.workOrderSn,jdbcType=VARCHAR},
            #{log.operatorId,jdbcType=VARCHAR}
            )
        </foreach>
    </insert>
</mapper>
