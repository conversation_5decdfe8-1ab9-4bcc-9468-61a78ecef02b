<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ExtraReadOnlyAlipayNewBlueSeaApplyDOMapper">

    <resultMap id="WebResultMap"  type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.resultmap.AlipayNewBlueSeaApplyWebListDO">
        <result column="BIZ_NO" property="bizNo" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UID" property="uid" jdbcType="INTEGER" javaType="Integer"/>
        <result column="USERNAME" property="username" jdbcType="VARCHAR" javaType="String"/>
        <result column="MERCHANT_ALIAS_NAME" property="merchantAliasName" jdbcType="VARCHAR" javaType="String"/>
        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR" javaType="String"/>
        <result column="BELONG_USERNAME" property="belongUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="BIZ_SCENE" property="bizScene" jdbcType="VARCHAR" javaType="String"/>
        <result column="BIZ_STATUS" property="bizStatus" jdbcType="TINYINT" javaType="Integer"/>
        <result column="APPLY_STATUS" property="applyStatus" jdbcType="VARCHAR" javaType="String"/>
        <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR" javaType="String"/>
        <result column="LESHUA_MERCHANT_ID" property="leshuaMerchantId" jdbcType="VARCHAR" javaType="String"/>
        <result column="SMID" property="smid" jdbcType="VARCHAR" javaType="String"/>
    </resultMap>


    <select id="getWebList" resultMap="WebResultMap">
        SELECT * FROM
        (
        SELECT
        apply.biz_no,
        apply.create_time,
        apply.uid,
        apply.username,
        apply.merchant_alias_name,
        apply.store_name,
        belonguser.username belong_username,
        apply.biz_scene,
        apply.biz_status,
        apply.apply_status,
        apply.reject_reason,
        apply.leshua_merchant_id,
        apply.smid
        FROM tp_new_bluesea_activity_apply apply
        LEFT JOIN tp_users users ON apply.uid = users.id
        LEFT JOIN tp_user belonguser ON users.belong = belonguser.id
        <where>
            <if test="createStartTime != null">
                AND apply.create_time <![CDATA[ >= ]]> #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND apply.create_time <![CDATA[ < ]]> #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="uid != null">
                AND apply.uid = #{uid,jdbcType=INTEGER}
            </if>
            <if test="bizScene != null  and bizScene != ''">
                AND apply.biz_scene = #{bizScene,jdbcType=VARCHAR}
            </if>
            <if test="bizStatus != null">
                AND apply.biz_status = #{bizStatus,jdbcType=INTEGER}
            </if>
            <if test="applyStatus != null  and applyStatus != ''">
                AND apply.apply_status = #{applyStatus,jdbcType=VARCHAR}
            </if>
            <if test="leshuaRateSync != null">
                AND apply.leshua_rate_sync = #{leshuaRateSync,jdbcType=VARCHAR}
            </if>
            <if test="username != null and username != ''">
                AND apply.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="belongUsername != null and belongUsername != ''">
                AND belonguser.username LIKE CONCAT (#{belongUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="storeName != null and storeName != ''">
                AND apply.store_name LIKE CONCAT ('%', #{storeName,jdbcType=VARCHAR},'%')
            </if>
            AND apply.del_flag = 0
        </where>
        UNION ALL
        SELECT
        apply.biz_no,
        apply.create_time,
        apply.uid,
        apply.username,
        apply.merchant_alias_name,
        apply.store_name,
        belonguser.username belong_username,
        apply.biz_scene,
        apply.biz_status,
        apply.apply_status,
        apply.reject_reason,
        apply.leshua_merchant_id,
        apply.smid
        FROM tp_new_bluesea_activity_college_apply apply
        LEFT JOIN tp_users users ON apply.uid = users.id
        LEFT JOIN tp_user belonguser ON users.belong = belonguser.id
        <where>
            <if test="createStartTime != null">
                AND apply.create_time <![CDATA[ >= ]]> #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND apply.create_time <![CDATA[ < ]]> #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="uid != null">
                AND apply.uid = #{uid,jdbcType=INTEGER}
            </if>
            <if test="bizScene != null  and bizScene != ''">
                AND apply.biz_scene = #{bizScene,jdbcType=VARCHAR}
            </if>
            <if test="bizStatus != null">
                AND apply.biz_status = #{bizStatus,jdbcType=INTEGER}
            </if>
            <if test="applyStatus != null  and applyStatus != ''">
                AND apply.apply_status = #{applyStatus,jdbcType=VARCHAR}
            </if>
            <if test="leshuaRateSync != null">
                AND apply.leshua_rate_sync = #{leshuaRateSync,jdbcType=VARCHAR}
            </if>
            <if test="username != null and username != ''">
                AND apply.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="belongUsername != null and belongUsername != ''">
                AND belonguser.username LIKE CONCAT (#{belongUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="storeName != null and storeName != ''">
                AND apply.store_name LIKE CONCAT ('%', #{storeName,jdbcType=VARCHAR},'%')
            </if>
            AND apply.del_flag = 0
        </where>
        ) temp_table
        ORDER BY create_time DESC
    </select>
</mapper>
