<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardOperateLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardOperateLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_IP" property="operateIp" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REQUEST_ID" property="requestId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AFTER_PARAM" property="afterParam" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BEFORE_PARAM" property="beforeParam" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTION" property="action" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MODULAR" property="modular" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OPERATE_ID`,`OPERATE_IP`,`REQUEST_ID`,`AFTER_PARAM`,`BUSINESS_ID`,`BEFORE_PARAM`,`OPERATE_NAME`,`IS_DEL`,`ACTION`,`MODULAR`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_OPERATE_LOG-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="afterParam != null">`AFTER_PARAM`,</if>
            <if test="beforeParam != null">`BEFORE_PARAM`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="action != null">`ACTION`,</if>
            <if test="modular != null">`MODULAR`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="afterParam != null">#{afterParam,jdbcType=VARCHAR},</if>
            <if test="beforeParam != null">#{beforeParam,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="action != null">#{action,jdbcType=TINYINT},</if>
            <if test="modular != null">#{modular,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>
    </mapper>
