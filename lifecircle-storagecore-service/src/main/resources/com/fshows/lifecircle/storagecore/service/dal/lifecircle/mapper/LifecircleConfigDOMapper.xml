<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleConfigDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APPID" property="appid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCHID" property="mchid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SSL_PATH" property="sslPath" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPSECRET" property="appsecret" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PARTNERKEY" property="partnerkey" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NAME" property="merchantName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LEVEL" property="level" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_LINK" property="isLink" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT" property="merchant" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GLOB_NUMBER" property="globNumber" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_AUTHORIZE" property="isAuthorize" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTH_SYNC_TIME" property="authSyncTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REAL_CHANNEL_ID" property="realChannelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUI_CONFIG_STATUS" property="liquiConfigStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WECHANT_AUTH_STATUS" property="wechantAuthStatus" jdbcType="TINYINT"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPID`,`MCHID`,`SSL_PATH`,`APPSECRET`,`PARTNERKEY`,`MERCHANT_NAME`,`UID`,`TYPE`,`LEVEL`,`IS_LINK`,`STATUS`,`MERCHANT`,`CHANNEL_ID`,`CREATE_TIME`,`GLOB_NUMBER`,`UPDATE_TIME`,`IS_AUTHORIZE`,`AUTH_SYNC_TIME`,`REAL_CHANNEL_ID`,`LIQUI_CONFIG_STATUS`,`WECHANT_AUTH_STATUS`
    </sql>


            <!--insert:TP_LIFECIRCLE_CONFIG-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="appid != null">`APPID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="sslPath != null">`SSL_PATH`,</if>
        <if test="appsecret != null">`APPSECRET`,</if>
        <if test="partnerkey != null">`PARTNERKEY`,</if>
        <if test="merchantName != null">`MERCHANT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="level != null">`LEVEL`,</if>
        <if test="isLink != null">`IS_LINK`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="merchant != null">`MERCHANT`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="globNumber != null">`GLOB_NUMBER`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="isAuthorize != null">`IS_AUTHORIZE`,</if>
        <if test="liquiConfigStatus != null">`LIQUI_CONFIG_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="sslPath != null">#{sslPath,jdbcType=VARCHAR},</if>
        <if test="appsecret != null">#{appsecret,jdbcType=VARCHAR},</if>
        <if test="partnerkey != null">#{partnerkey,jdbcType=VARCHAR},</if>
        <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="level != null">#{level,jdbcType=TINYINT},</if>
        <if test="isLink != null">#{isLink,jdbcType=TINYINT},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="merchant != null">#{merchant,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="globNumber != null">#{globNumber,jdbcType=TINYINT},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="isAuthorize != null">#{isAuthorize,jdbcType=TINYINT},</if>
        <if test="liquiConfigStatus != null">#{liquiConfigStatus,jdbcType=TINYINT},</if>
    </trim>
            </insert>

            <!--根据子商户号查询商户id-->
            <select id="getBySubMchId" resultType="java.lang.Integer">
                    SELECT
        uid
        FROM
        tp_lifecircle_config
        WHERE
        mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
