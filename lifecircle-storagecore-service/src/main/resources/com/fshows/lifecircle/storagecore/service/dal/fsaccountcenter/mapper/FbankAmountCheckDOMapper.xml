<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.FbankAmountCheckDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.FbankAmountCheckDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_DAY" property="checkDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_TYPE" property="checkType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FBANK_BALANCE" property="fbankBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FUBEI_BALANCE" property="fubeiBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="INCOME_TOTAL_AMOUNT" property="incomeTotalAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLE_TOTAL_AMOUNT" property="settleTotalAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UID`,`CHECK_DAY`,`CHECK_TYPE`,`CHECK_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`FBANK_BALANCE`,`FUBEI_BALANCE`,`INCOME_TOTAL_AMOUNT`,`SETTLE_TOTAL_AMOUNT`
    </sql>


            <!--insert:TP_FBANK_AMOUNT_CHECK-->
            <insert id="insert" >
                    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_FBANK_AMOUNT_CHECK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="checkDay != null">`CHECK_DAY`,</if>
            <if test="checkType != null">`CHECK_TYPE`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbankBalance != null">`FBANK_BALANCE`,</if>
            <if test="fubeiBalance != null">`FUBEI_BALANCE`,</if>
            <if test="incomeTotalAmount != null">`INCOME_TOTAL_AMOUNT`,</if>
            <if test="settleTotalAmount != null">`SETTLE_TOTAL_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="checkDay != null">#{checkDay,jdbcType=INTEGER},</if>
            <if test="checkType != null">#{checkType,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbankBalance != null">#{fbankBalance,jdbcType=DECIMAL},</if>
            <if test="fubeiBalance != null">#{fubeiBalance,jdbcType=DECIMAL},</if>
            <if test="incomeTotalAmount != null">#{incomeTotalAmount,jdbcType=DECIMAL},</if>
            <if test="settleTotalAmount != null">#{settleTotalAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--update table:TP_FBANK_AMOUNT_CHECK-->
            <update id="update" >
                    UPDATE /*MS-TP-FBANK-AMOUNT-CHECK-UPDATE*/ TP_FBANK_AMOUNT_CHECK
        SET
        <trim prefix="" suffix="" suffixOverrides="," prefixOverrides=",">
            <if test="uid != null">UID = #{uid,jdbcType=INTEGER}</if>
            <if test="checkDay != null">,CHECK_DAY = #{checkDay,jdbcType=INTEGER}</if>
            <if test="checkType != null">,CHECK_TYPE = #{checkType,jdbcType=INTEGER}</if>
            <if test="checkStatus != null">,CHECK_STATUS = #{checkStatus,jdbcType=INTEGER}</if>
            <if test="createTime != null">,CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}</if>
            <if test="updateTime != null">,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}</if>
            <if test="fbankBalance != null">,FBANK_BALANCE = #{fbankBalance,jdbcType=DECIMAL}</if>
            <if test="fubeiBalance != null">,FUBEI_BALANCE = #{fubeiBalance,jdbcType=DECIMAL}</if>
            <if test="incomeTotalAmount != null">,INCOME_TOTAL_AMOUNT = #{incomeTotalAmount,jdbcType=DECIMAL}</if>
            <if test="settleTotalAmount != null">,SETTLE_TOTAL_AMOUNT = #{settleTotalAmount,jdbcType=DECIMAL}</if>
        </trim>
        WHERE
        ID
        = #{id,jdbcType=BIGINT}
            </update>

            <!--根据 uid 、check_type、check_day获得信息-->
            <select id="getByUidAndCheckTypeAndCheckDay" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_FBANK_AMOUNT_CHECK
        WHERE
        UID = #{uid,jdbcType=INTEGER}
        AND
        CHECK_DAY = #{checkDay,jdbcType=INTEGER}
        AND
        CHECK_TYPE = #{checkType,jdbcType=INTEGER}
            </select>
    </mapper>
