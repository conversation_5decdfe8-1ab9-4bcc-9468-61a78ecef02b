<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPEN_ID" property="openId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_CODE" property="cardCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIND_PHONE" property="bindPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FUBEI_UNION_ID" property="fubeiUnionId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCOPE_CITY_CODE" property="scopeCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVATION_CODE" property="activationCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCOPE_PROVINCE_CODE" property="scopeProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_TIME" property="bindTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_TYPE" property="cardType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_SOURCE" property="bindSource" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_STATUS" property="bindStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_EXPIRY" property="cardExpiry" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_STATUS" property="cardStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_MINA_SALES" property="isMinaSales" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STOCK_STATUS" property="stockStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_SHAPE_TYPE" property="cardShapeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVATION_TIME" property="activationTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OUT_STORAGE_TIME" property="outStorageTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_LONG_EFFECTIVE" property="isLongEffective" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVATION_STATUS" property="activationStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CARD_PRICE" property="cardPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CARD_AMOUNT" property="cardAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_NO`,`OPEN_ID`,`CARD_CODE`,`BIND_PHONE`,`CARD_SKU_ID`,`CARD_SPU_ID`,`CARD_SPU_NAME`,`FUBEI_UNION_ID`,`PUBLISH_ORG_ID`,`SCOPE_CITY_CODE`,`ACTIVATION_CODE`,`SCOPE_PROVINCE_CODE`,`IS_DEL`,`BIND_TIME`,`CARD_TYPE`,`BIND_SOURCE`,`BIND_STATUS`,`CARD_EXPIRY`,`CARD_STATUS`,`IS_MINA_SALES`,`STOCK_STATUS`,`CARD_SHAPE_TYPE`,`ACTIVATION_TIME`,`OUT_STORAGE_TIME`,`IS_LONG_EFFECTIVE`,`ACTIVATION_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`CARD_PRICE`,`CARD_AMOUNT`
    </sql>


            <!--insert:TP_PREPAY_CARD-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="cardCode != null">`CARD_CODE`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="fubeiUnionId != null">`FUBEI_UNION_ID`,</if>
            <if test="activationCode != null">`ACTIVATION_CODE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="cardType != null">`CARD_TYPE`,</if>
            <if test="bindSource != null">`BIND_SOURCE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="cardExpiry != null">`CARD_EXPIRY`,</if>
            <if test="cardStatus != null">`CARD_STATUS`,</if>
            <if test="isMinaSales != null">`IS_MINA_SALES`,</if>
            <if test="stockStatus != null">`STOCK_STATUS`,</if>
            <if test="activationTime != null">`ACTIVATION_TIME`,</if>
            <if test="outStorageTime != null">`OUT_STORAGE_TIME`,</if>
            <if test="isLongEffective != null">`IS_LONG_EFFECTIVE`,</if>
            <if test="activationStatus != null">`ACTIVATION_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="cardCode != null">#{cardCode,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="fubeiUnionId != null">#{fubeiUnionId,jdbcType=VARCHAR},</if>
            <if test="activationCode != null">#{activationCode,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=INTEGER},</if>
            <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
            <if test="bindSource != null">#{bindSource,jdbcType=TINYINT},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
            <if test="cardExpiry != null">#{cardExpiry,jdbcType=INTEGER},</if>
            <if test="cardStatus != null">#{cardStatus,jdbcType=TINYINT},</if>
            <if test="isMinaSales != null">#{isMinaSales,jdbcType=TINYINT},</if>
            <if test="stockStatus != null">#{stockStatus,jdbcType=TINYINT},</if>
            <if test="activationTime != null">#{activationTime,jdbcType=INTEGER},</if>
            <if test="outStorageTime != null">#{outStorageTime,jdbcType=INTEGER},</if>
            <if test="isLongEffective != null">#{isLongEffective,jdbcType=TINYINT},</if>
            <if test="activationStatus != null">#{activationStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据预付卡查询卡列表-->
            <select id="findListPrepayCardByCardNos" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        `tp_prepay_card`
        WHERE
        `is_del`= 0
        AND `card_no` IN
        <foreach close=")" collection="list" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--查询作废卡数量-->
            <select id="countPrepayCardByCardNos" resultType="java.lang.Integer">
                    SELECT
         count(*) 
        FROM
        `tp_prepay_card`
        WHERE
        `is_del`= 0
        AND card_status = 4
        AND `card_no` IN
        <foreach close=")" collection="list" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--根据卡号查询卡信息-->
            <select id="findCardByCardNoList" resultMap="BaseResultMap">
                select
                <include refid="Base_Column_List"/>
                from tp_prepay_card
                where card_no in
                <foreach collection="list" item="cardNo" open="(" close=")" separator=",">
                    #{cardNo, jdbcType=VARCHAR}
                </foreach>
                and is_del = 0
            </select>

    <!--根据卡号列表查询实体卡信息-->
    <select id="findEntityCardDOList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_prepay_card
        where card_no in
        <foreach collection="list" item="cardNo" open="(" close=")" separator=",">
            #{cardNo, jdbcType=VARCHAR}
        </foreach>
        and card_shape_type = 2
        and is_del = 0
    </select>

    <!--查询满足出库条件的卡信息 pageCount-->
    <select id="findSatisfyOutStorageCardListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 `tp_prepay_card`
        WHERE `is_del`= 0
        AND `stock_status` = 1
        and `card_status` = 1
        and `card_expiry` <![CDATA[ > ]]>  #{cardExpiry, jdbcType=INTEGER}
        and `card_spu_id` = #{cardSpuId, jdbcType=VARCHAR}
        and `card_sku_id` = #{cardSkuId, jdbcType=VARCHAR}
        and `publish_org_id` = #{publishOrgId, jdbcType=VARCHAR}
        
            </select>
    <!--查询满足出库条件的卡信息 pageResult-->
    <select id="findSatisfyOutStorageCardListResult" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM `tp_prepay_card`
        WHERE `is_del`= 0
        AND `stock_status` = 1
        and `card_status` = 1
        and `card_expiry` <![CDATA[ > ]]>  #{cardExpiry, jdbcType=INTEGER}
        and `card_spu_id` = #{cardSpuId, jdbcType=VARCHAR}
        and `card_sku_id` = #{cardSkuId, jdbcType=VARCHAR}
        and `publish_org_id` = #{publishOrgId, jdbcType=VARCHAR}
        ORDER BY `id` ASC
        limit #{startRow},#{limit}
    </select>

    <!--根据卡号批量查询指定sku的预付卡信息-->
    <select id="findCardByCardNoListAndSkuId" resultMap="BaseResultMap">
        SELECT /*MS-TP-PREPAY-CARD-FINDCARDBYCARDNOLISTANDSKUID*/
        <include refid="Base_Column_List"/>
        FROM tp_prepay_card
        WHERE card_no in
        <foreach collection="list" item="cardNo" open="(" separator="," close=")">
            #{cardNo, jdbcType=VARCHAR}
        </foreach>
        and card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        and is_del = 0
    </select>

    <!--根据卡号查询预付卡信息-->
    <select id="getCardByCardNo" resultMap="BaseResultMap">
        SELECT /*MS-TP-PREPAY-CARD-GETCARDBYCARDNO*/
        <include refid="Base_Column_List"/>
        FROM tp_prepay_card WHERE card_no = #{cardNo, jdbcType=VARCHAR} and is_del = 0 limit 1
    </select>
</mapper>
