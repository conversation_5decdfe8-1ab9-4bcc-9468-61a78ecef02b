<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BalanceAccountCheckDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BalanceAccountCheckDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_DATE" property="tradeDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MYBANK_CHECK_RESULT" property="mybankCheckResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BALANCE_CHECK_RESULT" property="balanceCheckResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COUNTEROFFER_CHECK_RESULT" property="counterofferCheckResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WITHDRAWABLE_CHECK_RESULT" property="withdrawableCheckResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SETTLE_MONEY" property="settleMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TODAY_BALANCE" property="todayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_BALANCE" property="totalBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MYBANK_BALANCE" property="mybankBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="WITHDRAW_MONEY" property="withdrawMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SUPPLEMENT_MONEY" property="supplementMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="YESTEDAY_BALANCE" property="yestedayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TODAY_SHARE_AMOUNT" property="todayShareAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="VIP_FREEZE_BALANCE" property="vipFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COUNTEROFFER_MONEY" property="counterofferMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MYBANK_INCOME_MONEY" property="mybankIncomeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SHARE_FREEZE_BALANCE" property="shareFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TODAY_WITHDRAW_MONEY" property="todayWithdrawMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="YESTEDAY_TRADE_MONEY" property="yestedayTradeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TODAY_TRANSFER_AMOUNT" property="todayTransferAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="WITHDRAWABLE_BALANCE" property="withdrawableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ILLEGAL_FREEZE_BALANCE" property="illegalFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="YESTERDAY_SHARE_AMOUNT" property="yesterdayShareAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LIFECIRCLE_TODAY_BALANCE" property="lifecircleTodayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LIFECIRCLE_TOTAL_BALANCE" property="lifecircleTotalBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLEMENT_FREEZE_BALANCE" property="settlementFreezeBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="YESTERDAY_TRANSFER_AMOUNT" property="yesterdayTransferAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="YESTERDAY_SHARE_TRANSFER_AMOUNT" property="yesterdayShareTransferAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LIFECIRCLE_WITHDRAWABLE_BALANCE" property="lifecircleWithdrawableBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`UID`,`TRADE_DATE`,`MYBANK_CHECK_RESULT`,`BALANCE_CHECK_RESULT`,`COUNTEROFFER_CHECK_RESULT`,`WITHDRAWABLE_CHECK_RESULT`,`CREATE_TIME`,`UPDATE_TIME`,`SETTLE_MONEY`,`TODAY_BALANCE`,`TOTAL_BALANCE`,`MYBANK_BALANCE`,`WITHDRAW_MONEY`,`SUPPLEMENT_MONEY`,`YESTEDAY_BALANCE`,`TODAY_SHARE_AMOUNT`,`VIP_FREEZE_BALANCE`,`COUNTEROFFER_MONEY`,`MYBANK_INCOME_MONEY`,`SHARE_FREEZE_BALANCE`,`TODAY_WITHDRAW_MONEY`,`YESTEDAY_TRADE_MONEY`,`TODAY_TRANSFER_AMOUNT`,`WITHDRAWABLE_BALANCE`,`ILLEGAL_FREEZE_BALANCE`,`YESTERDAY_SHARE_AMOUNT`,`LIFECIRCLE_TODAY_BALANCE`,`LIFECIRCLE_TOTAL_BALANCE`,`SETTLEMENT_FREEZE_BALANCE`,`YESTERDAY_TRANSFER_AMOUNT`,`YESTERDAY_SHARE_TRANSFER_AMOUNT`,`LIFECIRCLE_WITHDRAWABLE_BALANCE`
    </sql>


            <!--insert:TP_BALANCE_ACCOUNT_CHECK-->
            <insert id="insert" >
            INSERT INTO TP_BALANCE_ACCOUNT_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="counterofferCheckResult != null">`COUNTEROFFER_CHECK_RESULT`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="counterofferMoney != null">`COUNTEROFFER_MONEY`,</if>
        <if test="yestedayTradeMoney != null">`YESTEDAY_TRADE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="counterofferCheckResult != null">#{counterofferCheckResult,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="counterofferMoney != null">#{counterofferMoney,jdbcType=DECIMAL},</if>
        <if test="yestedayTradeMoney != null">#{yestedayTradeMoney,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--根据商户token和交易日期-->
            <select id="getByTokenAndTradeDate" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_BALANCE_ACCOUNT_CHECK
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
        LIMIT 1
            </select>

            <!--根据商户token和交易日期进行更新-->
            <update id="updateByTokenAndTradeDate" >
                    UPDATE
        TP_BALANCE_ACCOUNT_CHECK
        SET
        yesteday_trade_money = #{yestedayTradeMoney,jdbcType=DECIMAL},
        counteroffer_money = #{counterofferMoney,jdbcType=DECIMAL},
        counteroffer_check_result = #{counterofferCheckResult,jdbcType=TINYINT}
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
            </update>
    </mapper>
