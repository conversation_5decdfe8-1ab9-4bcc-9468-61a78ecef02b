<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.SinanActivityRegistrationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityRegistrationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ACT_NM" property="actNm" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCT_ID" property="acctId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INDUSTRY" property="industry" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_IDS" property="storeIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_PHONE" property="cardPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POSTAR_EXT" property="postarExt" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_TYPE" property="policyType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_INFO" property="rejectInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SIGN_REGION" property="signRegion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SIGN_PROVINCE" property="signProvince" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_HEAD_IMG" property="storeHeadImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POSTAR_AGE_NAME" property="postarAgeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_MONEY_IMG" property="storeMoneyImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_NUMBER" property="activityNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRANCH_BANK_CODE" property="branchBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRANCH_BANK_NAME" property="branchBankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_INSIDE_IMG" property="storeInsideImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DETAILED_ADDRESS" property="detailedAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ADDRESS" property="merchantAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPEN_BANK_CARD_DATE" property="openBankCardDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POSTAR_FALL_AGET_ID" property="postarFallAgetId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LS_GOLD_ACTIVITY_CODE" property="lsGoldActivityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_RESPONSE_ID" property="platformResponseId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_NAME" property="businessLicenseName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_MANAGER_CODE" property="customerManagerCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_MANAGER_NAME" property="customerManagerName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_TYPE_INDUSTRY_POLICY" property="activityTypeIndustryPolicy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CARD_OPEN" property="isCardOpen" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHARE_PAY" property="isSharePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_CUT_CHANNEL" property="isCutChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_SETTLE_TYPE" property="cardSettleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_CHANGE_BANK_NO" property="isChangeBankNo" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BANK_APPLY_STATUS" property="bankApplyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HAS_NOTIFY_MERCHANT" property="hasNotifyMerchant" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_REGISTRATION_TYPE" property="activityRegistrationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SHARE_PAY_TIME" property="sharePayTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CUT_CHANNEL_TIME" property="cutChannelTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="EVERYDAY_TRADE" property="everydayTrade" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="siNanActivityMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.SiNanActivityListDO">

                <result column="company" property="company" javaType="String"/>

                <result column="username" property="username" javaType="String"/>

                <result column="uid" property="uid" javaType="Integer"/>

                <result column="activity_status" property="activityStatus" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`ACT_NM`,`ACCT_ID`,`COMPANY`,`CITY_NAME`,`INDUSTRY`,`STORE_IDS`,`USERNAME`,`CARD_PHONE`,`POSTAR_EXT`,`UNION_CODE`,`MERCHANT_NO`,`POLICY_TYPE`,`REJECT_INFO`,`SIGN_REGION`,`CONTACT_NAME`,`PROVINCE_NAME`,`SIGN_PROVINCE`,`STORE_HEAD_IMG`,`PLATFORM_ORG_ID`,`POSTAR_AGE_NAME`,`STORE_MONEY_IMG`,`ACTIVITY_NUMBER`,`BRANCH_BANK_CODE`,`BRANCH_BANK_NAME`,`STORE_INSIDE_IMG`,`DETAILED_ADDRESS`,`MERCHANT_ADDRESS`,`OPEN_BANK_CARD_DATE`,`POSTAR_FALL_AGET_ID`,`LS_GOLD_ACTIVITY_CODE`,`PLATFORM_RESPONSE_ID`,`BUSINESS_LICENSE_NAME`,`CUSTOMER_MANAGER_CODE`,`CUSTOMER_MANAGER_NAME`,`ACTIVITY_TYPE_INDUSTRY_POLICY`,`UID`,`IS_DEL`,`BELONG`,`END_TIME`,`STORE_ID`,`START_TIME`,`IS_CARD_OPEN`,`IS_SHARE_PAY`,`IS_CUT_CHANNEL`,`ACTIVITY_STATUS`,`CARD_SETTLE_TYPE`,`IS_CHANGE_BANK_NO`,`BANK_APPLY_STATUS`,`LIQUIDATION_TYPE`,`HAS_NOTIFY_MERCHANT`,`ACTIVITY_REGISTRATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`SHARE_PAY_TIME`,`CUT_CHANNEL_TIME`,`EVERYDAY_TRADE`
    </sql>


            <!--getManagerByUserId-->
            <select id="getManagerByUserId" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETMANAGERBYUSERID*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration where uid=#{userId,jdbcType=INTEGER}
            </select>

            <!--getSinanActivityCount-->
            <select id="getSinanActivityCount" resultType="Integer">
                    SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANACTIVITYCOUNT*/  COUNT(*) FROM
        (
        SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANACTIVITYCOUNT*/ 
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="activityRegistrationType != null">
                AND activityRegistrationType = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
            </select>

            <!--getSinanSuperActivityCount-->
            <select id="getSinanSuperActivityCount" resultType="Integer">
                    SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANSUPERACTIVITYCOUNT*/  COUNT(*) FROM
        (
        SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANSUPERACTIVITYCOUNT*/ 
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND users.createtime &gt; 1617206400
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="activityRegistrationType != null">
                AND activityRegistrationType = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
            </select>

            <!--getSinanSuperActivityList-->
            <select id="getSinanSuperActivityList" resultMap="siNanActivityMap">
                    SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANSUPERACTIVITYLIST*/  <include refid="Base_Column_List" /> FROM
        (
        SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANSUPERACTIVITYLIST*/ 
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND users.createtime &gt; 1617206400
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="activityRegistrationType != null">
                AND activity_registration_type = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{startRow,jdbcType=INTEGER},#{offset,jdbcType=INTEGER}
            </select>

            <!--insertCardInfo-->
            <insert id="insertCardInfo" >
                    insert into lm_sinan_activity_registration
        (uid,username,company,industry,policy_type,is_card_open,merchant_address,detailed_address,card_phone,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},#{policyType,jdbcType=VARCHAR},
        #{isCardOpen,jdbcType=INTEGER},#{merchantAddress,jdbcType=VARCHAR},
        #{detailedAddress,jdbcType=VARCHAR},#{cardPhone,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER}
        #{activityNumber,jdbcType=VARCHAR},#{activityRegistrationType,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP},
        #{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
            </insert>

            <!--insertSuperStoreInfo-->
            <insert id="insertSuperStoreInfo" >
                    insert into lm_sinan_activity_registration
        (uid,username,company,industry,store_ids,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},#{storeIds,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER},#{activityNumber,jdbcType=VARCHAR},#{activityRegistrationType,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},#{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
            </insert>

            <!--insertIndustryInfo-->
            <insert id="insertIndustryInfo" >
                    insert into lm_sinan_activity_registration
        (uid,username,company,industry,activity_type_industry_policy,everyday_trade,store_head_img,store_inside_img,store_money_img
        ,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},
        #{activityTypeIndustryPolicy,jdbcType=VARCHAR},#{everydayTrade,jdbcType=DECIMAL},#{storeHeadImg,jdbcType=VARCHAR},
        #{storeInsideImg,jdbcType=VARCHAR},#{storeMoneyImg,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER},#{activityNumber,jdbcType=VARCHAR}
        ,#{activityRegistrationType,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP}
        ,#{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
            </insert>

            <!--getCardActivityDetail-->
            <select id="getCardActivityDetail" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETCARDACTIVITYDETAIL*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration where uid=#{uid,jdbcType=INTEGER}
            </select>

            <!--updateSinanActivityInfo-->
            <update id="updateSinanActivityInfo" >
                    update /*MS-LM-SINAN-ACTIVITY-REGISTRATION-UPDATESINANACTIVITYINFO*/ lm_sinan_activity_registration
        <set>
            <if test="policyType!=null">
                policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="isCardOpen!=null">
                is_card_open=#{isCardOpen,jdbcType=INTEGER}
            </if>
            <if test="merchantAddress!=null">
                merchant_address=#{merchantAddress,jdbcType=VARCHAR}
            </if>
            <if test="detailedAddress!=null">
                detailed_address=#{detailedAddress,jdbcType=VARCHAR}
            </if>
            <if test="cardPhone!=null">
                card_phone=#{cardPhone,jdbcType=INTEGER}
            </if>
            <if test="activityTypeIndustryPolicy!=null">
                activity_type_industry_policy=#{activityTypeIndustryPolicy,jdbcType=VARCHAR}
            </if>
            <if test="activityTypeIndustryPolicy!=null">
                activity_type_industry_policy=#{activityTypeIndustryPolicy,jdbcType=VARCHAR}
            </if>
            <if test="everydayTrade!=null">
                everyday_trade=#{everydayTrade,jdbcType=DECIMAL}
            </if>
            <if test="storeHeadImg!=null">
                store_head_img=#{storeHeadImg,jdbcType=VARCHAR}
            </if>
            <if test="storeInsideImg!=null">
                store_inside_img=#{storeInsideImg,jdbcType=VARCHAR}
            </if>
            <if test="storeMoneyImg!=null">
                store_money_img=#{storeMoneyImg,jdbcType=VARCHAR}
            </if>
            <if test="storeIds!=null">
                store_ids=#{storeIds,jdbcType=VARCHAR}
            </if>
            <if test="updateTime!=null">
                update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            where activity_number=#{activityNumber,jdbcType=VARCHAR}
        </set>
            </update>

            <!--getSinanJoinActivityInfoByActivityNumber-->
            <select id="getSinanJoinActivityInfoByActivityNumber" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANJOINACTIVITYINFOBYACTIVITYNUMBER*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration where activity_number=#{activityNumber,jdbcType=VARCHAR}
            </select>

            <!--getSinanCrmActivityInfoCountByUsername-->
            <select id="getSinanCrmActivityInfoCountByUsername" resultType="Integer">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOCOUNTBYUSERNAME*/  count(*)  from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and username=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
            </select>

            <!--getSinanCrmActivityInfoCountByuid-->
            <select id="getSinanCrmActivityInfoCountByuid" resultType="Integer">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOCOUNTBYUID*/  count(*)  from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
            </select>

            <!--getSinanCrmActivityInfoCountByMerchantNo-->
            <select id="getSinanCrmActivityInfoCountByMerchantNo" resultType="Integer">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOCOUNTBYMERCHANTNO*/  count(*)  from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
            </select>

            <!--getSinanCrmActivityInfoCountBybelong-->
            <select id="getSinanCrmActivityInfoCountBybelong" resultType="Integer">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOCOUNTBYBELONG*/  count(*)  from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
            </select>

            <!--updateActivityStatusByActivityNumber-->
            <update id="updateActivityStatusByActivityNumber" >
                    update /*MS-LM-SINAN-ACTIVITY-REGISTRATION-UPDATEACTIVITYSTATUSBYACTIVITYNUMBER*/ lm_sinan_activity_registration set activity_status=#{activityStatus,jdbcType=INTEGER},
        reject_info=#{rejectInfo,jdbcType=VARCHAR},
        update_time=#{updateTime,jdbcType=TIMESTAMP}
        where activity_number=#{activityNumber,jdbcType=VARCHAR}
            </update>

            <!--getActivityStatusByActivityNumber-->
            <select id="getActivityStatusByActivityNumber" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETACTIVITYSTATUSBYACTIVITYNUMBER*/ <include refid="Base_Column_List" />from lm_sinan_activity_registration where activity_number=#{activityNumber,jdbcType=VARCHAR}
            </select>

            <!--getSinanCrmActivityInfoByUsername pageCount-->
            <select id="getSinanCrmActivityInfoByUsernameCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 lm_sinan_activity_registration
        <where>
            <if test="searchValue!=null and searchValue!='' ">
                and username LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        
            </select>
            <!--getSinanCrmActivityInfoByUsername pageResult-->
            <select id="getSinanCrmActivityInfoByUsernameResult"  resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYUSERNAME*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration
        <where>
            <if test="searchValue!=null and searchValue!='' ">
                and username LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--getSinanCrmActivityInfoByMerchantNo pageCount-->
            <select id="getSinanCrmActivityInfoByMerchantNoCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        
            </select>
            <!--getSinanCrmActivityInfoByMerchantNo pageResult-->
            <select id="getSinanCrmActivityInfoByMerchantNoResult"  resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYMERCHANTNO*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--getSinanCrmActivityInfoByBelong pageCount-->
            <select id="getSinanCrmActivityInfoByBelongCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        
            </select>
            <!--getSinanCrmActivityInfoByBelong pageResult-->
            <select id="getSinanCrmActivityInfoByBelongResult"  resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYBELONG*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--getSinanCrmActivityInfoByUid pageCount-->
            <select id="getSinanCrmActivityInfoByUidCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        
            </select>
            <!--getSinanCrmActivityInfoByUid pageResult-->
            <select id="getSinanCrmActivityInfoByUidResult"  resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYUID*/ <include refid="Base_Column_List" /> from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--findActivityRegistrationPage pageCount-->
            <select id="findActivityRegistrationPageCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM

        lm_sinan_activity_registration
        <where>
            <if test="username !=null and username!='' ">
                AND username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uid !=null and uid!=0 ">
                AND uid=#{uid,jdbcType=INTEGER}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="(" separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="activityName!=null and activityName!=-1 ">
                AND activity_registration_type=#{activityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                AND activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="isCutChannel!=null and isCutChannel!=0 ">
                AND is_cut_channel = #{isCutChannel,jdbcType=INTEGER}
            </if>
            <if test="isSharePay!=null and isSharePay!=0 ">
                AND is_share_pay = #{isSharePay,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null and liquidationType!=0 ">
                AND liquidation_type = #{liquidationType,jdbcType=INTEGER}
            </if>
            <if test="signProvince !=null and signProvince!='' ">
                AND SIGN_PROVINCE LIKE CONCAT(#{signProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="signRegion !=null and signRegion!='' ">
                AND SIGN_REGION LIKE CONCAT(#{signRegion,jdbcType=VARCHAR},'%')
            </if>
            <if test="bankApplyStatus !=null ">
                AND BANK_APPLY_STATUS = #{bankApplyStatus,jdbcType=INTEGER}
            </if>
            <if test="isCardOpen !=null ">
                AND is_card_open = #{isCardOpen,jdbcType=INTEGER}
            </if>
        </where>
        
            </select>
            <!--findActivityRegistrationPage pageResult-->
            <select id="findActivityRegistrationPageResult"  resultMap="BaseResultMap">
                    SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-FINDACTIVITYREGISTRATIONPAGE*/  <include refid="Base_Column_List" /> FROM
        lm_sinan_activity_registration
        <where>
            <if test="username !=null and username!='' ">
                AND username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uid !=null and uid!=0 ">
                AND uid=#{uid,jdbcType=INTEGER}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="(" separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="activityName!=null and activityName!=-1 ">
                AND activity_registration_type=#{activityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                AND activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="isCutChannel!=null and isCutChannel!=0 ">
                AND is_cut_channel = #{isCutChannel,jdbcType=INTEGER}
            </if>
            <if test="isSharePay!=null and isSharePay!=0 ">
                AND is_share_pay = #{isSharePay,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null and liquidationType!=0 ">
                AND liquidation_type = #{liquidationType,jdbcType=INTEGER}
            </if>
            <if test="signProvince !=null and signProvince!='' ">
                AND SIGN_PROVINCE LIKE CONCAT(#{signProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="signRegion !=null and signRegion!='' ">
                AND SIGN_REGION LIKE CONCAT(#{signRegion,jdbcType=VARCHAR},'%')
            </if>
            <if test="bankApplyStatus !=null ">
                AND BANK_APPLY_STATUS = #{bankApplyStatus,jdbcType=INTEGER}
            </if>
            <if test="isCardOpen !=null ">
                AND is_card_open = #{isCardOpen,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--findRegistrationPageGoldDashboard pageCount-->
            <select id="findRegistrationPageGoldDashboardCount" resultType="int">
                    
        SELECT
          COUNT(*) AS total 
        FROM

        lm_sinan_activity_registration lsar
        <if test="isV2Agent != null and isV2Agent == true">
            LEFT JOIN tp_users us on us.id = lsar.uid
            LEFT JOIN tp_grant_right_control salesmanTgrc on salesmanTgrc.key_id = us.salesman
            LEFT JOIN tp_grant_right_control marketTgrc on marketTgrc.key_id = us.market_id
        </if>
        <where>
            lsar.is_del = 0
            AND lsar.`activity_registration_type` = 1
            AND lsar.belong = #{belong,jdbcType=INTEGER}
            <if test="isV2Agent != null and isV2Agent == true">
                AND (salesmanTgrc.individual_settle = 0 OR salesmanTgrc.individual_settle is null)
                AND (marketTgrc.individual_settle = 0 OR marketTgrc.individual_settle is null)
            </if>
            <if test="username !=null and username!='' ">
                AND lsar.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uidList != null and uidList.size &gt;0">
                AND lsar.uid IN
                <foreach close=")" collection="uidList" index="index" item="uid" open="(" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="company!=null and company!=''">
                AND lsar.company=#{company,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND lsar.merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND lsar.policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="cardSettleType!=null">
                AND lsar.card_settle_type=#{cardSettleType,jdbcType=INTEGER}
            </if>
            <if test="isChangeBankNo!=null and isChangeBankNo!=0">
                AND lsar.is_change_bank_no=#{isChangeBankNo,jdbcType=INTEGER}
            </if>
            <if test="activityStatus!=null and activityStatus!=0">
                AND lsar.activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityStartTime!=null">
                AND lsar.create_time <![CDATA[ >= ]]> #{searchActivityStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND lsar.create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null">
                AND lsar.liquidation_type = #{liquidationType,jdbcType=TINYINT}
            </if>
        </where>
        
            </select>
            <!--findRegistrationPageGoldDashboard pageResult-->
            <select id="findRegistrationPageGoldDashboardResult"  resultMap="BaseResultMap">
                    
        SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-FINDREGISTRATIONPAGEGOLDDASHBOARD*/  lsar.* FROM
        lm_sinan_activity_registration lsar
        <if test="isV2Agent != null and isV2Agent == true">
            LEFT JOIN tp_users us on us.id = lsar.uid
            LEFT JOIN tp_grant_right_control salesmanTgrc on salesmanTgrc.key_id = us.salesman
            LEFT JOIN tp_grant_right_control marketTgrc on marketTgrc.key_id = us.market_id
        </if>
        <where>
            lsar.is_del = 0
            AND lsar.`activity_registration_type` = 1
            AND lsar.belong = #{belong,jdbcType=INTEGER}
            <if test="isV2Agent != null and isV2Agent == true">
                AND (salesmanTgrc.individual_settle = 0 OR salesmanTgrc.individual_settle is null)
                AND (marketTgrc.individual_settle = 0 OR marketTgrc.individual_settle is null)
            </if>
            <if test="username !=null and username!='' ">
                AND lsar.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uidList != null and uidList.size &gt;0">
                AND lsar.uid IN
                <foreach close=")" collection="uidList" index="index" item="uid" open="(" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="company!=null and company!=''">
                AND lsar.company=#{company,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND lsar.merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND lsar.policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="cardSettleType!=null">
                AND lsar.card_settle_type=#{cardSettleType,jdbcType=INTEGER}
            </if>
            <if test="isChangeBankNo!=null and isChangeBankNo!=0">
                AND lsar.is_change_bank_no=#{isChangeBankNo,jdbcType=INTEGER}
            </if>
            <if test="activityStatus!=null and activityStatus!=0">
                AND lsar.activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityStartTime!=null">
                AND lsar.create_time <![CDATA[ >= ]]> #{searchActivityStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND lsar.create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null">
                AND lsar.liquidation_type = #{liquidationType,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY lsar.create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--查询出来所有的审核中的金卡活动根据对应得通道类型-->
            <select id="getByLiquidationTypeGoldCardRecently" resultMap="BaseResultMap">
                    SELECT /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETBYLIQUIDATIONTYPEGOLDCARDRECENTLY*/  <include refid="Base_Column_List" /> FROM lm_sinan_activity_registration
        WHERE liquidation_type = #{liquidationType,jdbcType=TINYINT}
        AND activity_registration_type = 1
        AND activity_status = 2
        AND is_del = 0
        <if test="list != null and list.size &gt; 0">
            AND uid IN
            <foreach close=")" collection="list" index="index" item="uid" open="(" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        ORDER BY create_time DESC
            </select>

            <!--根据ID进行设置审核状态-->
            <update id="updateStatusById" >
                    UPDATE /*MS-LM-SINAN-ACTIVITY-REGISTRATION-UPDATESTATUSBYID*/ lm_sinan_activity_registration
        SET activity_status = #{activityStatus,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=INTEGER}
            </update>
    </mapper>
