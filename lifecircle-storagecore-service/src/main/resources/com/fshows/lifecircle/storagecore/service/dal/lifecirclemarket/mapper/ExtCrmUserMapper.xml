<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtCrmUserMapper">

    <resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ExtCrmUserDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="SYS_USER_ID" property="sysUserId" jdbcType="BIGINT"
                javaType="Long"/>

        <result column="SALT" property="salt" jdbcType="CHAR"
                javaType="String"/>

        <result column="EMAIL" property="email" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AVATAR" property="avatar" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="USER_ID" property="userId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LAST_LOGIN_IP" property="lastLoginIp" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LOGIN_PASSWORD" property="loginPassword" jdbcType="CHAR"
                javaType="String"/>

        <result column="DINGTALK_USER_ID" property="dingtalkUserId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LAST_LOGIN_DEVICE" property="lastLoginDevice" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RELEVANT_TRIAL_ACCOUNT" property="relevantTrialAccount" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SEX" property="sex" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ADD_SOURCE" property="addSource" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="USER_STATUS" property="userStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_DIRECT_TYPE" property="isDirectType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_FIRST_START" property="isFirstStart" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_SET_CLOSE_TIME" property="isSetCloseTime" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_FORMAL_ACCOUNT" property="isFormalAccount" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_SET_FREEZE_TIME" property="isSetFreezeTime" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_VIRTUAL_ACCOUNT" property="isVirtualAccount" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_DEFAULT_PWD_MODIFIED" property="isDefaultPwdModified" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CLOSE_TIME" property="closeTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="HIRED_DATE" property="hiredDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="FREEZE_TIME" property="freezeTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="REGULAR_DATE" property="regularDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="DIMISSION_DATE" property="dimissionDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`SYS_USER_ID`,`SALT`,`EMAIL`,`AVATAR`,`MOBILE`,`USER_ID`,`CREATE_BY`,`REAL_NAME`,`UPDATE_BY`,`JOB_NUMBER`,`LAST_LOGIN_IP`,`LOGIN_PASSWORD`,`DINGTALK_USER_ID`,`LAST_LOGIN_DEVICE`,`RELEVANT_TRIAL_ACCOUNT`,`SEX`,`USER_TYPE`,`ADD_SOURCE`,`USER_STATUS`,`IS_DIRECT_TYPE`,`IS_FIRST_START`,`ACCOUNT_STATUS`,`IS_SET_CLOSE_TIME`,`IS_FORMAL_ACCOUNT`,`IS_SET_FREEZE_TIME`,`IS_VIRTUAL_ACCOUNT`,`IS_DEFAULT_PWD_MODIFIED`,`CLOSE_TIME`,`HIRED_DATE`,`CREATE_TIME`,`FREEZE_TIME`,`UPDATE_TIME`,`REGULAR_DATE`,`DIMISSION_DATE`,`LAST_LOGIN_TIME`
    </sql>


    <!--根据业务id查询-->
    <select id="getByUserId" resultMap="BaseResultMap">
        SELECT /*MS-LM-CRM-USER-GETBYUSERID*/  <include refid="Base_Column_List" /> FROM lm_crm_user
        WHERE user_id = #{userId,jdbcType=VARCHAR}
        LIMIT 1
    </select>
</mapper>
