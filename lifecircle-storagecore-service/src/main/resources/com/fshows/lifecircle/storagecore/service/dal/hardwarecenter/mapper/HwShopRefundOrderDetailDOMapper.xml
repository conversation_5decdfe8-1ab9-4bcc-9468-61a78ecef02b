<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopRefundOrderDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopRefundOrderDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SKU_ID" property="goodsSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_REFUND_ORDER_SN" property="hwRefundOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GOODS_TYPE" property="goodsType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GOODS_NUMBER" property="goodsNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GOODS_REFUND_SCORE" property="goodsRefundScore" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GOODS_REFUND_NUMBER" property="goodsRefundNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="GOODS_PRICE" property="goodsPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="GOODS_SUMPRICE" property="goodsSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="GOODS_REFUND_SUMPRICE" property="goodsRefundSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_NAME`,`GOODS_SKU_ID`,`GOODS_SPU_ID`,`HW_REFUND_ORDER_SN`,`IS_DEL`,`IS_TEST`,`GOODS_TYPE`,`EQUIPMENT_ID`,`GOODS_NUMBER`,`GOODS_REFUND_SCORE`,`GOODS_REFUND_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`GOODS_PRICE`,`GOODS_SUMPRICE`,`GOODS_REFUND_SUMPRICE`
    </sql>


            <!--insert:HW_SHOP_REFUND_ORDER_DETAIL-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_REFUND_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="hwRefundOrderSn != null">`HW_REFUND_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="goodsNumber != null">`GOODS_NUMBER`,</if>
            <if test="goodsRefundNumber != null">`GOODS_REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="goodsPrice != null">`GOODS_PRICE`,</if>
            <if test="goodsSumprice != null">`GOODS_SUMPRICE`,</if>
            <if test="goodsRefundSumprice != null">`GOODS_REFUND_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="hwRefundOrderSn != null">#{hwRefundOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="goodsNumber != null">#{goodsNumber,jdbcType=INTEGER},</if>
            <if test="goodsRefundNumber != null">#{goodsRefundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsPrice != null">#{goodsPrice,jdbcType=DECIMAL},</if>
            <if test="goodsSumprice != null">#{goodsSumprice,jdbcType=DECIMAL},</if>
            <if test="goodsRefundSumprice != null">#{goodsRefundSumprice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--查询退款订单详细信息-->
            <select id="findRefundOrderDetailListByOrderSnList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        hw_shop_refund_order_detail
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND hw_refund_order_sn IN
                <foreach collection="list" open="(" close=")" item="hwRefundOrderSn" separator=",">
                    #{hwRefundOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND `IS_DEL` = 0
        </where>
            </select>
    </mapper>
