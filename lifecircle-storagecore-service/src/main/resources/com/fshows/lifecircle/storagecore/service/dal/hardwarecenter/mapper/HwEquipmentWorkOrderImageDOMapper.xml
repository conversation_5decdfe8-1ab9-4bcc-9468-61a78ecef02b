<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentWorkOrderImageDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentWorkOrderImageDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMAGE_URL" property="imageUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WORK_ORDER_SN" property="workOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IMAGE_TYPE" property="imageType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`IMAGE_URL`,`WORK_ORDER_SN`,`IS_DEL`,`IMAGE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_WORK_ORDER_IMAGE-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_WORK_ORDER_IMAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="imageUrl != null">`IMAGE_URL`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="imageType != null">`IMAGE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="imageUrl != null">#{imageUrl,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="imageType != null">#{imageType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--batchInsert:HW_EQUIPMENT_WORK_ORDER_IMAGE-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO HW_EQUIPMENT_WORK_ORDER_IMAGE(`INIT_SN`,`IMAGE_URL`,`WORK_ORDER_SN`,`IMAGE_TYPE`)
        VALUES
        <foreach collection="list" item="img" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{img.initSn,jdbcType=VARCHAR},
                #{img.imageUrl,jdbcType=VARCHAR},
                #{img.workOrderSn,jdbcType=VARCHAR},
                #{img.imageType,jdbcType=TINYINT},
            </trim>
        </foreach>
            </select>
    </mapper>
