<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantCredentialsBatchImportRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantCredentialsBatchImportRecordDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BATCH_ID" property="batchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FAIL_URL" property="failUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FILE_NAME" property="fileName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TASK_STATUS" property="taskStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REASON`,`BATCH_ID`,`FAIL_URL`,`FILE_NAME`,`TASK_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD-->
            <insert id="insert" >
                    INSERT INTO TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="batchId != null">`BATCH_ID`,</if>
            <if test="failUrl != null">`FAIL_URL`,</if>
            <if test="taskStatus != null">`TASK_STATUS`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="batchId != null">#{batchId,jdbcType=VARCHAR},</if>
            <if test="failUrl != null">#{failUrl,jdbcType=VARCHAR},</if>
            <if test="taskStatus != null">#{taskStatus,jdbcType=TINYINT},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--updateByBatchId-->
            <update id="updateByBatchId" >
                    UPDATE /*MS-TP-MERCHANT-CREDENTIALS-BATCH-IMPORT-RECORD-UPDATEBYBATCHID*/ TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        <set>
            <if test="reason != null">`REASON` = #{reason,jdbcType=VARCHAR},</if>
            <if test="failUrl != null">`FAIL_URL` = #{failUrl,jdbcType=VARCHAR},</if>
            <if test="taskStatus != null">`TASK_STATUS` = #{taskStatus,jdbcType=TINYINT},</if>
            <if test="fileName != null">`FILE_NAME` = #{fileName,jdbcType=VARCHAR},</if>
        </set>
        WHERE `BATCH_ID` = #{batchId,jdbcType=VARCHAR}
            </update>

            <!--根据批次号查询导入记录-->
            <select id="getByBatchId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        WHERE `BATCH_ID` = #{batchId,jdbcType=VARCHAR}
        limit 1;
            </select>

            <!--findImportRecordList pageCount-->
            <select id="findImportRecordListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        WHERE 1=1
        
            </select>
            <!--findImportRecordList pageResult-->
            <select id="findImportRecordListResult"  resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        WHERE 1=1
        ORDER BY ID DESC
            limit #{startRow},#{limit}
            </select>
    </mapper>
