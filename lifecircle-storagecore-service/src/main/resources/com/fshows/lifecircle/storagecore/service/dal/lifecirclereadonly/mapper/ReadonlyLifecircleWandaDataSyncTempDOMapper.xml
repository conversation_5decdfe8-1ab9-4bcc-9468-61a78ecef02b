<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyLifecircleWandaDataSyncTempDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyLifecircleWandaDataSyncTempDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="UID" property="uid" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="AD_ID" property="adId" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="CITY_ID" property="cityId" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="STORE_ID" property="storeId" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="PROVINCE_ID" property="provinceId" jdbcType="BIGINT"
            javaType="Long"/>

    <result column="NAME" property="name" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="FLOOR" property="floor" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OWNER" property="owner" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="AD_NAME" property="adName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="REGION" property="region" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ADDRESS" property="address" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE" property="license" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLAZA_ID" property="plazaId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LATITUDE" property="latitude" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_PIC" property="storePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="WX_OPENID" property="wxOpenid" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ERROR_CODE" property="errorCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LONGITUDE" property="longitude" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLAZA_NAME" property="plazaName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_ID" property="businessId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="TRADER_NAME" property="traderName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BANK_CARD_PIC" property="bankCardPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CITY_COMPANY" property="cityCompany" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_NAME" property="businessName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ERROR_MESSAGE" property="errorMessage" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ID_CARD_NUMBER" property="idCardNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OWNER_BACK_URL" property="ownerBackUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SHOW_ERROR_MSG" property="showErrorMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_HEAD_PIC" property="storeHeadPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UNIONPAY_CODE" property="unionpayCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ACCOUNT_MOBILE" property="accountMobile" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OWNER_FRONT_URL" property="ownerFrontUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="INCOME_ERROR_MSG" property="incomeErrorMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="INSIDE_SCENE_PIC" property="insideScenePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_MANAGE_PIC" property="storeManagePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="WD_CATEGORYNAME" property="wdCategoryname" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_NO" property="settlerIdCardNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_FINANCE_PIC" property="storeFinancePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_PLACE_PIC" property="businessPlacePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_ID_DOC_COPY" property="contactIdDocCopy" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_PERIOD_END" property="contactPeriodEnd" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE_ELECTRONIC" property="licenseElectronic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_NAME" property="settlerIdCardName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_PERIOD_BEGIN" property="contactPeriodBegin" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OWNER_CERTIFICATE_NO" property="ownerCertificateNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="FINAL_INCOME_SYNC_TIME" property="finalIncomeSyncTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_ID_DOC_COPY_BACK" property="contactIdDocCopyBack" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_BACK_PIC" property="settlerIdCardBackPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_END_DATE" property="settlerIdCardEndDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="AUTHORIZE_SAVE_ERROR_MSG" property="authorizeSaveErrorMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_FRONT_PIC" property="settlerIdCardFrontPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_ID_CARD_BEGIN_DATE" property="settlerIdCardBeginDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OWNER_CERTIFICATE_ADDRESS" property="ownerCertificateAddress" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLER_NOT_LEGAL_PROVE_PIC" property="settlerNotLegalProvePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ALIPAY_AUTHORIZE_SAVE_ERROR_MSG" property="alipayAuthorizeSaveErrorMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_AUTHORIZATION_LETTER" property="businessAuthorizationLetter" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="WECHAT_AUTHORIZE_SAVE_ERROR_MSG" property="wechatAuthorizeSaveErrorMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SYNC_FLAG" property="syncFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CHANGE_FLAG" property="changeFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="TRADER_TYPE" property="traderType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="UNITY_CAT_ID" property="unityCatId" jdbcType="SMALLINT"
            javaType="Integer"/>

    <result column="CHECK_STATUS" property="checkStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_TYPE" property="contactType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SETTLER_TYPE" property="settlerType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="BUSINESS_TYPE" property="businessType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="KA_INCOME_FLAG" property="kaIncomeFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="NEED_NEW_SUB_NO" property="needNewSubNo" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CALL_INCOME_STATUS" property="callIncomeStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_ID_DOC_TYPE" property="contactIdDocType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="AUTHORIZE_SAVE_FLAG" property="authorizeSaveFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="HAS_OFFLINE_CONFIRM" property="hasOfflineConfirm" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SETTLER_ID_CARD_TYPE" property="settlerIdCardType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_PERIOD_IS_LONG" property="contactPeriodIsLong" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SETTLER_ID_CARD_IS_LONG" property="settlerIdCardIsLong" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="STORE_PIC_CHECK_STATUS" property="storePicCheckStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="OWNER_CERTIFICATE_TYPE" property="ownerCertificateType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ALIPAY_AUTHORIZE_SAVE_FLAG" property="alipayAuthorizeSaveFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="WECHAT_AUTHORIZE_SAVE_FLAG" property="wechatAuthorizeSaveFlag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="EFFECTIVE_END_DATE" property="effectiveEndDate" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="CERTIFICATE_END_DATE" property="certificateEndDate" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="EFFECTIVE_START_DATE" property="effectiveStartDate" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="CERTIFICATE_START_DATE" property="certificateStartDate" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`UID`,`AD_ID`,`CITY_ID`,`STORE_ID`,`PROVINCE_ID`,`NAME`,`FLOOR`,`OWNER`,`AD_NAME`,`BANK_NO`,`MOBILE`,`REGION`,`ADDRESS`,`LICENSE`,`PLAZA_ID`,`BANK_NAME`,`CITY_NAME`,`LATITUDE`,`STORE_PIC`,`WX_OPENID`,`ERROR_CODE`,`LONGITUDE`,`PLAZA_NAME`,`STORE_NAME`,`BUSINESS_ID`,`TRADER_NAME`,`BANK_CARD_PIC`,`CITY_COMPANY`,`BUSINESS_NAME`,`ERROR_MESSAGE`,`ID_CARD_NUMBER`,`OWNER_BACK_URL`,`PROVINCE_NAME`,`SHOW_ERROR_MSG`,`STORE_HEAD_PIC`,`UNIONPAY_CODE`,`ACCOUNT_MOBILE`,`OWNER_FRONT_URL`,`INCOME_ERROR_MSG`,`INSIDE_SCENE_PIC`,`STORE_MANAGE_PIC`,`WD_CATEGORYNAME`,`SETTLER_ID_CARD_NO`,`STORE_FINANCE_PIC`,`BUSINESS_PLACE_PIC`,`CONTACT_ID_DOC_COPY`,`CONTACT_PERIOD_END`,`LICENSE_ELECTRONIC`,`SETTLER_ID_CARD_NAME`,`CONTACT_PERIOD_BEGIN`,`OWNER_CERTIFICATE_NO`,`FINAL_INCOME_SYNC_TIME`,`CONTACT_ID_DOC_COPY_BACK`,`SETTLER_ID_CARD_BACK_PIC`,`SETTLER_ID_CARD_END_DATE`,`AUTHORIZE_SAVE_ERROR_MSG`,`SETTLER_ID_CARD_FRONT_PIC`,`SETTLER_ID_CARD_BEGIN_DATE`,`OWNER_CERTIFICATE_ADDRESS`,`SETTLER_NOT_LEGAL_PROVE_PIC`,`ALIPAY_AUTHORIZE_SAVE_ERROR_MSG`,`BUSINESS_AUTHORIZATION_LETTER`,`WECHAT_AUTHORIZE_SAVE_ERROR_MSG`,`BANK_TYPE`,`SYNC_FLAG`,`CHANGE_FLAG`,`TRADER_TYPE`,`UNITY_CAT_ID`,`CHECK_STATUS`,`CONTACT_TYPE`,`SETTLER_TYPE`,`BUSINESS_TYPE`,`KA_INCOME_FLAG`,`NEED_NEW_SUB_NO`,`CALL_INCOME_STATUS`,`CONTACT_ID_DOC_TYPE`,`AUTHORIZE_SAVE_FLAG`,`HAS_OFFLINE_CONFIRM`,`SETTLER_ID_CARD_TYPE`,`CONTACT_PERIOD_IS_LONG`,`SETTLER_ID_CARD_IS_LONG`,`STORE_PIC_CHECK_STATUS`,`OWNER_CERTIFICATE_TYPE`,`ALIPAY_AUTHORIZE_SAVE_FLAG`,`WECHAT_AUTHORIZE_SAVE_FLAG`,`CREATE_TIME`,`UPDATE_TIME`,`EFFECTIVE_END_DATE`,`CERTIFICATE_END_DATE`,`EFFECTIVE_START_DATE`,`CERTIFICATE_START_DATE`
        </sql>


        <!--insert:TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP-->
        <insert id="insert">
            INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="uid != null">`UID`,</if>
                <if test="adId != null">`AD_ID`,</if>
                <if test="cityId != null">`CITY_ID`,</if>
                <if test="storeId != null">`STORE_ID`,</if>
                <if test="provinceId != null">`PROVINCE_ID`,</if>
                <if test="name != null">`NAME`,</if>
                <if test="owner != null">`OWNER`,</if>
                <if test="adName != null">`AD_NAME`,</if>
                <if test="bankNo != null">`BANK_NO`,</if>
                <if test="mobile != null">`MOBILE`,</if>
                <if test="region != null">`REGION`,</if>
                <if test="address != null">`ADDRESS`,</if>
                <if test="license != null">`LICENSE`,</if>
                <if test="plazaId != null">`PLAZA_ID`,</if>
                <if test="bankName != null">`BANK_NAME`,</if>
                <if test="cityName != null">`CITY_NAME`,</if>
                <if test="latitude != null">`LATITUDE`,</if>
                <if test="storePic != null">`STORE_PIC`,</if>
            <if test="errorCode != null">`ERROR_CODE`,</if>
            <if test="longitude != null">`LONGITUDE`,</if>
            <if test="plazaName != null">`PLAZA_NAME`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="businessId != null">`BUSINESS_ID`,</if>
            <if test="traderName != null">`TRADER_NAME`,</if>
            <if test="bankCardPic != null">`BANK_CARD_PIC`,</if>
            <if test="cityCompany != null">`CITY_COMPANY`,</if>
            <if test="businessName != null">`BUSINESS_NAME`,</if>
            <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
            <if test="idCardNumber != null">`ID_CARD_NUMBER`,</if>
            <if test="ownerBackUrl != null">`OWNER_BACK_URL`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="accountMobile != null">`ACCOUNT_MOBILE`,</if>
            <if test="ownerFrontUrl != null">`OWNER_FRONT_URL`,</if>
            <if test="incomeErrorMsg != null">`INCOME_ERROR_MSG`,</if>
            <if test="insideScenePic != null">`INSIDE_SCENE_PIC`,</if>
            <if test="settlerIdCardNo != null">`SETTLER_ID_CARD_NO`,</if>
            <if test="businessPlacePic != null">`BUSINESS_PLACE_PIC`,</if>
            <if test="contactIdDocCopy != null">`CONTACT_ID_DOC_COPY`,</if>
            <if test="contactPeriodEnd != null">`CONTACT_PERIOD_END`,</if>
            <if test="licenseElectronic != null">`LICENSE_ELECTRONIC`,</if>
            <if test="settlerIdCardName != null">`SETTLER_ID_CARD_NAME`,</if>
            <if test="contactPeriodBegin != null">`CONTACT_PERIOD_BEGIN`,</if>
            <if test="ownerCertificateNo != null">`OWNER_CERTIFICATE_NO`,</if>
            <if test="contactIdDocCopyBack != null">`CONTACT_ID_DOC_COPY_BACK`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="authorizeSaveErrorMsg != null">`AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="settlerNotLegalProvePic != null">`SETTLER_NOT_LEGAL_PROVE_PIC`,</if>
            <if test="alipayAuthorizeSaveErrorMsg != null">`ALIPAY_AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="businessAuthorizationLetter != null">`BUSINESS_AUTHORIZATION_LETTER`,</if>
            <if test="wechatAuthorizeSaveErrorMsg != null">`WECHAT_AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="syncFlag != null">`SYNC_FLAG`,</if>
            <if test="traderType != null">`TRADER_TYPE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="contactType != null">`CONTACT_TYPE`,</if>
            <if test="settlerType != null">`SETTLER_TYPE`,</if>
            <if test="callIncomeStatus != null">`CALL_INCOME_STATUS`,</if>
            <if test="contactIdDocType != null">`CONTACT_ID_DOC_TYPE`,</if>
            <if test="authorizeSaveFlag != null">`AUTHORIZE_SAVE_FLAG`,</if>
            <if test="settlerIdCardType != null">`SETTLER_ID_CARD_TYPE`,</if>
            <if test="contactPeriodIsLong != null">`CONTACT_PERIOD_IS_LONG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="storePicCheckStatus != null">`STORE_PIC_CHECK_STATUS`,</if>
            <if test="ownerCertificateType != null">`OWNER_CERTIFICATE_TYPE`,</if>
            <if test="alipayAuthorizeSaveFlag != null">`ALIPAY_AUTHORIZE_SAVE_FLAG`,</if>
            <if test="wechatAuthorizeSaveFlag != null">`WECHAT_AUTHORIZE_SAVE_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="effectiveEndDate != null">`EFFECTIVE_END_DATE`,</if>
            <if test="certificateEndDate != null">`CERTIFICATE_END_DATE`,</if>
            <if test="effectiveStartDate != null">`EFFECTIVE_START_DATE`,</if>
            <if test="certificateStartDate != null">`CERTIFICATE_START_DATE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=BIGINT},</if>
            <if test="adId != null">#{adId,jdbcType=BIGINT},</if>
            <if test="cityId != null">#{cityId,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="provinceId != null">#{provinceId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="owner != null">#{owner,jdbcType=VARCHAR},</if>
            <if test="adName != null">#{adName,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="region != null">#{region,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="license != null">#{license,jdbcType=VARCHAR},</if>
            <if test="plazaId != null">#{plazaId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="latitude != null">#{latitude,jdbcType=VARCHAR},</if>
            <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
            <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
            <if test="longitude != null">#{longitude,jdbcType=VARCHAR},</if>
            <if test="plazaName != null">#{plazaName,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="businessId != null">#{businessId,jdbcType=VARCHAR},</if>
            <if test="traderName != null">#{traderName,jdbcType=VARCHAR},</if>
            <if test="bankCardPic != null">#{bankCardPic,jdbcType=VARCHAR},</if>
            <if test="cityCompany != null">#{cityCompany,jdbcType=VARCHAR},</if>
            <if test="businessName != null">#{businessName,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
            <if test="idCardNumber != null">#{idCardNumber,jdbcType=VARCHAR},</if>
            <if test="ownerBackUrl != null">#{ownerBackUrl,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="accountMobile != null">#{accountMobile,jdbcType=VARCHAR},</if>
            <if test="ownerFrontUrl != null">#{ownerFrontUrl,jdbcType=VARCHAR},</if>
            <if test="incomeErrorMsg != null">#{incomeErrorMsg,jdbcType=VARCHAR},</if>
            <if test="insideScenePic != null">#{insideScenePic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardNo != null">#{settlerIdCardNo,jdbcType=VARCHAR},</if>
            <if test="businessPlacePic != null">#{businessPlacePic,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopy != null">#{contactIdDocCopy,jdbcType=VARCHAR},</if>
            <if test="contactPeriodEnd != null">#{contactPeriodEnd,jdbcType=VARCHAR},</if>
            <if test="licenseElectronic != null">#{licenseElectronic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardName != null">#{settlerIdCardName,jdbcType=VARCHAR},</if>
            <if test="contactPeriodBegin != null">#{contactPeriodBegin,jdbcType=VARCHAR},</if>
            <if test="ownerCertificateNo != null">#{ownerCertificateNo,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopyBack != null">#{contactIdDocCopyBack,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="authorizeSaveErrorMsg != null">#{authorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settlerNotLegalProvePic != null">#{settlerNotLegalProvePic,jdbcType=VARCHAR},</if>
            <if test="alipayAuthorizeSaveErrorMsg != null">#{alipayAuthorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="businessAuthorizationLetter != null">#{businessAuthorizationLetter,jdbcType=VARCHAR},</if>
            <if test="wechatAuthorizeSaveErrorMsg != null">#{wechatAuthorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="syncFlag != null">#{syncFlag,jdbcType=TINYINT},</if>
            <if test="traderType != null">#{traderType,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
            <if test="contactType != null">#{contactType,jdbcType=TINYINT},</if>
            <if test="settlerType != null">#{settlerType,jdbcType=TINYINT},</if>
            <if test="callIncomeStatus != null">#{callIncomeStatus,jdbcType=TINYINT},</if>
            <if test="contactIdDocType != null">#{contactIdDocType,jdbcType=TINYINT},</if>
            <if test="authorizeSaveFlag != null">#{authorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardType != null">#{settlerIdCardType,jdbcType=TINYINT},</if>
            <if test="contactPeriodIsLong != null">#{contactPeriodIsLong,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="storePicCheckStatus != null">#{storePicCheckStatus,jdbcType=TINYINT},</if>
            <if test="ownerCertificateType != null">#{ownerCertificateType,jdbcType=TINYINT},</if>
            <if test="alipayAuthorizeSaveFlag != null">#{alipayAuthorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="wechatAuthorizeSaveFlag != null">#{wechatAuthorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="effectiveEndDate != null">#{effectiveEndDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateEndDate != null">#{certificateEndDate,jdbcType=TIMESTAMP},</if>
            <if test="effectiveStartDate != null">#{effectiveStartDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateStartDate != null">#{certificateStartDate,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--根据门店ID获取商户资料-->
        <select id="getByStoreId" resultMap="BaseResultMap">
            SELECT /*MS-TP-LIFECIRCLE-WANDA-DATA-SYNC-TEMP-GETBYSTOREID*/
            <include refid="Base_Column_List"/>
            FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
            WHERE store_id = #{storeId,jdbcType=BIGINT} limit 1
        </select>

        <!--根据门店ID获取商户资料-->
        <select id="findByStoreIdList" resultMap="BaseResultMap">
            SELECT /*MS-TP-LIFECIRCLE-WANDA-DATA-SYNC-TEMP-FINDBYSTOREIDLIST*/
            <include refid="Base_Column_List"/>
            FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
            WHERE store_id in
            <foreach item="storeId" index="index" collection="list" open="(" separator="," close=")">
                #{storeId,jdbcType=BIGINT}
            </foreach>
        </select>
    </mapper>
