<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.AgentCommissionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.AgentCommissionDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="AGENT_ID" property="agentId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="ERP_SLAVE_SYNC_TIMESTAMP" property="erpSlaveSyncTimestamp" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="ERP_MASTER_SYNC_TIMESTAMP" property="erpMasterSyncTimestamp" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_EXT_NUM" property="businessExtNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROJECT_FEE_CODE" property="projectFeeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMMISSION_SLAVE_ID" property="commissionSlaveId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SYNC_BILL_FLAG" property="syncBillFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TAX_PROCESS_FLAG" property="taxProcessFlag" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_MERGE_TIMES" property="agentMergeTimes" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ERP_SLAVE_SYNC_STATUS" property="erpSlaveSyncStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ERP_MASTER_SYNC_STATUS" property="erpMasterSyncStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ERP_SLAVE_SYNC_FAIL_TIMES" property="erpSlaveSyncFailTimes" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ERP_MASTER_SYNC_FAIL_TIMES" property="erpMasterSyncFailTimes" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BUSINESS_TIME" property="businessTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="AMOUNT" property="amount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TAX_AMOUNT" property="taxAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`AGENT_ID`,`ERP_SLAVE_SYNC_TIMESTAMP`,`ERP_MASTER_SYNC_TIMESTAMP`,`PT_MONTH`,`SOURCE_TYPE`,`BUSINESS_EXT_NUM`,`PROJECT_FEE_CODE`,`COMMISSION_SLAVE_ID`,`SYNC_BILL_FLAG`,`TAX_PROCESS_FLAG`,`AGENT_MERGE_TIMES`,`ERP_SLAVE_SYNC_STATUS`,`ERP_MASTER_SYNC_STATUS`,`ERP_SLAVE_SYNC_FAIL_TIMES`,`ERP_MASTER_SYNC_FAIL_TIMES`,`CREATE_TIME`,`UPDATE_TIME`,`BUSINESS_TIME`,`AMOUNT`,`TAX_AMOUNT`
    </sql>


            <!--根据佣金ID查询-->
            <select id="getByCommissionId" resultMap="BaseResultMap">
                    SELECT /*MS-FP-AGENT-COMMISSION-GETBYCOMMISSIONID*/  <include refid="Base_Column_List" />
        FROM fp_agent_commission
        WHERE
        ID = #{id,jdbcType=BIGINT}
            </select>
    </mapper>
