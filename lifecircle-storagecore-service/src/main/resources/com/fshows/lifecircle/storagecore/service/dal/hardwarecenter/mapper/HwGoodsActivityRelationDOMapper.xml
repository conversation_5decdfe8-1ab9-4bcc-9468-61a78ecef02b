<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwGoodsActivityRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwGoodsActivityRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIND_MONTH" property="bindMonth" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SPU_ID" property="spuId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_ID`,`BIND_MONTH`,`HW_ORDER_SN`,`UID`,`IS_DEL`,`SPU_ID`,`STORE_ID`,`ACTIVITY_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_GOODS_ACTIVITY_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_GOODS_ACTIVITY_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="uid != null">`uid`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="spuId != null">`SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="goodsId != null">`GOODS_ID`,</if>
            <if test="bindMonth != null">`BIND_MONTH`</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="spuId != null">#{spuId,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="bindMonth != null">#{bindMonth,jdbcType=VARCHAR}</if>
        </trim>
            </insert>

            <!--获取物料信息-->
            <select id="getGoodsActivityRelation" resultMap="BaseResultMap">
                    SELECT /*MS-HW-GOODS-ACTIVITY-RELATION-GETGOODSACTIVITYRELATION*/  <include refid="Base_Column_List" />
        FROM hw_goods_activity_relation
        WHERE  store_id = #{storeId,jdbcType=INTEGER}
        AND activity_id = #{activityId,jdbcType=INTEGER}
        AND goods_id = #{goodsId,jdbcType=VARCHAR}
        and is_del = 0
        limit 1
            </select>

            <!--分页查询物料返佣金额关联关系列表 pageCount-->
            <select id="findGoodsCommissionListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM

        `hw_goods_activity_relation` t1
        LEFT JOIN hw_equipment_activity t2 ON t1.activity_id = t2.id
        LEFT JOIN hw_shop_goods_spu t3 ON t1.spu_id = t3.id
        WHERE
        t1.is_del = 0
        <if test="startCreateTime != null">
            AND t1.create_time &gt; #{startCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateTime != null">
            AND t1.create_time &lt; #{endCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND t1.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="activityId != null">
            AND t1.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="goodsId != null and goodsId != '' ">
            AND t3.goods_spu_id = #{goodsId,jdbcType=VARCHAR}
        </if>
        <if test="hwOrderSn != null and hwOrderSn != ''">
            AND t1.hw_order_sn = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="bindMonth != null and bindMonth != ''">
            AND t1.bind_month = #{bindMonth,jdbcType=VARCHAR}
        </if>
        
            </select>
            <!--分页查询物料返佣金额关联关系列表 pageResult-->
            <select id="findGoodsCommissionListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.GoodsCommissionDTO">
                    SELECT
        t1.id,
        t1.uid,
        t1.store_id AS storeId,
        t1.hw_order_sn AS hwOrderSn,
        t1.activity_id AS activityId,
        t1.create_time AS createTime,
        t1.bind_month,
        t2.activity_name AS activityName,
        t3.goods_name AS goodsName
        FROM
        `hw_goods_activity_relation` t1
        LEFT JOIN hw_equipment_activity t2 ON t1.activity_id = t2.id
        LEFT JOIN hw_shop_goods_spu t3 ON t1.spu_id = t3.id
        WHERE
        t1.is_del = 0
        <if test="startCreateTime != null">
            AND t1.create_time &gt; #{startCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateTime != null">
            AND t1.create_time &lt; #{endCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND t1.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="activityId != null">
            AND t1.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="goodsId != null and goodsId != '' ">
            AND t3.goods_spu_id = #{goodsId,jdbcType=VARCHAR}
        </if>
        <if test="hwOrderSn != null and hwOrderSn != ''">
            AND t1.hw_order_sn = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="bindMonth != null and bindMonth != ''">
            AND t1.bind_month = #{bindMonth,jdbcType=VARCHAR}
        </if>
        order by t1.create_time desc
            limit #{startRow},#{limit}
            </select>
    </mapper>
