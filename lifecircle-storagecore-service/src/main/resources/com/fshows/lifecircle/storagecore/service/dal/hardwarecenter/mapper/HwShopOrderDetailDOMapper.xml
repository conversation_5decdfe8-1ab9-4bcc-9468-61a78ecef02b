<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopOrderDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopOrderDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SKU_ID" property="goodsSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SCORE" property="score" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_GROUP" property="isGroup" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GOODS_TYPE" property="goodsType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GOODS_NUMBER" property="goodsNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GROUP_NUMBER" property="groupNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CAN_JOIN_ACTIVITY_NUMBER" property="canJoinActivityNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HAD_JOIN_ACTIVITY_NUMBER" property="hadJoinActivityNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BASE_PRICE" property="basePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FINAL_PRICE" property="finalPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BASE_SUMPRICE" property="baseSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FINAL_SUMPRICE" property="finalSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_NAME`,`HW_ORDER_SN`,`GOODS_SKU_ID`,`GOODS_SPU_ID`,`IS_DEL`,`SCORE`,`IS_TEST`,`IS_GROUP`,`GOODS_TYPE`,`EQUIPMENT_ID`,`GOODS_NUMBER`,`GROUP_NUMBER`,`CAN_JOIN_ACTIVITY_NUMBER`,`HAD_JOIN_ACTIVITY_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`BASE_PRICE`,`FINAL_PRICE`,`BASE_SUMPRICE`,`FINAL_SUMPRICE`
    </sql>


            <!--insert:HW_SHOP_ORDER_DETAIL-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isGroup != null">`IS_GROUP`,</if>
            <if test="goodsNumber != null">`GOODS_NUMBER`,</if>
            <if test="groupNumber != null">`GROUP_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="basePrice != null">`BASE_PRICE`,</if>
            <if test="finalPrice != null">`FINAL_PRICE`,</if>
            <if test="baseSumprice != null">`BASE_SUMPRICE`,</if>
            <if test="finalSumprice != null">`FINAL_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isGroup != null">#{isGroup,jdbcType=TINYINT},</if>
            <if test="goodsNumber != null">#{goodsNumber,jdbcType=INTEGER},</if>
            <if test="groupNumber != null">#{groupNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="basePrice != null">#{basePrice,jdbcType=DECIMAL},</if>
            <if test="finalPrice != null">#{finalPrice,jdbcType=DECIMAL},</if>
            <if test="baseSumprice != null">#{baseSumprice,jdbcType=DECIMAL},</if>
            <if test="finalSumprice != null">#{finalSumprice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--批量查询订单详情列表-->
            <select id="findOrderDetailList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_DETAIL
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
            </select>

            <!--批量查询订单详情列表-->
            <select id="findOrderDetailByOrderSn" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_DETAIL
        where HW_ORDER_SN = #{hwOrderSn,jdbcType=VARCHAR}
        AND GOODS_SPU_ID = #{goodsSpuId,jdbcType=VARCHAR}
        AND IS_DEL = 0
        limit 1
            </select>

            <!--根据orderSn和GoodsId扣减关联数量信息-->
            <update id="updateActivityNumberByOrderSnAndGoodsId" >
                    update
        HW_SHOP_ORDER_DETAIL
        set
            had_join_activity_number = had_join_activity_number + 1
        WHERE
        HW_ORDER_SN = #{hwOrderSn,jdbcType=VARCHAR}
        AND GOODS_SPU_ID = #{goodsSpuId,jdbcType=VARCHAR}
        AND IS_DEL = 0
        limit 1
            </update>
    </mapper>
