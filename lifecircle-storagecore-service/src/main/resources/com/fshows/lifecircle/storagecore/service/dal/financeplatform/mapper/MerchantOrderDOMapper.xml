<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.MerchantOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.MerchantOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT3" property="ext3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_MANAGER" property="marketManager" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPER_SALESMAN_USERNAME" property="superSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_RISK_TYPE" property="orderRiskType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TIME" property="payTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="EXT4" property="ext4" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="RATE_NUM" property="rateNum" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ORDER_SUMPRICE" property="orderSumprice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT1`,`EXT2`,`EXT3`,`FEE_CODE`,`ORDER_SN`,`ORDER_TYPE`,`AGENT_USERNAME`,`MARKET_MANAGER`,`AGENT_COMPANYNAME`,`MERCHANT_USERNAME`,`SALESMAN_USERNAME`,`SUPER_SALESMAN_USERNAME`,`UID`,`AGENT_ID`,`PAY_TYPE`,`MARKET_ID`,`SALESMAN_ID`,`BUSINESS_DATE`,`ORDER_RISK_TYPE`,`SUPER_SALESMAN_ID`,`PAY_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`EXT4`,`RATE_NUM`,`ORDER_PRICE`,`ORDER_SUMPRICE`,`ACTUAL_PAYABLE_COMMISSION`
    </sql>


            <!--超级商户佣金详情查询-->
            <select id="getSuperMerchantCommissionDetails" resultMap="BaseResultMap">
                    select /*MS-FP-MERCHANT-ORDER-GETSUPERMERCHANTCOMMISSIONDETAILS*/ <include refid="Base_Column_List" /> from fp_merchant_order
        where 1 = 1
        <if test="merchantId != null and merchantId != ''">
            and uid = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="businessDate != null and businessDate != ''">
            and business_date = #{businessDate,jdbcType=INTEGER}
        </if>
        <if test="orderSn != null and orderSn != ''">
            and order_sn = #{orderSn,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            and pay_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null ">
            and pay_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by pay_time desc
            </select>
    </mapper>
