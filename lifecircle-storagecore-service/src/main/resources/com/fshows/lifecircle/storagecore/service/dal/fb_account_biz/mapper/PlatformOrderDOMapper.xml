<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.PlatformOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.PlatformOrderDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="FROZEN_AMOUNT" property="frozenAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_ID" property="dataId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_DATE" property="settleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_TYPE" property="productType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BILL_NO" property="storeBillNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_GOODS_ID" property="platformGoodsId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORDER_NO" property="platformOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_STORE_ID" property="platformStoreId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRODUCT_SCENE_CODE" property="productSceneCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_SCENE_CODE" property="businessSceneCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_GOODS_NAME" property="platformGoodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_STORE_NAME" property="platformStoreName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_SETTLE_DATE" property="platformSettleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_REAL_TYPE" property="orderRealType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PLATFORM_TRADE_TIME" property="platformTradeTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORDER_PRICE`,`FROZEN_AMOUNT`,`SP_ID`,`BILL_NO`,`BLOC_ID`,`DATA_ID`,`ORDER_TYPE`,`SETTLE_DATE`,`PRODUCT_TYPE`,`STORE_BILL_NO`,`PLATFORM_CODE`,`PLATFORM_GOODS_ID`,`PLATFORM_ORDER_NO`,`PLATFORM_STORE_ID`,`PRODUCT_SCENE_CODE`,`BUSINESS_SCENE_CODE`,`PLATFORM_GOODS_NAME`,`PLATFORM_STORE_NAME`,`PLATFORM_SETTLE_DATE`,`ORDER_REAL_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`PLATFORM_TRADE_TIME`
    </sql>


            <!--insert:ACC_PLATFORM_ORDER-->
            <insert id="insert" >
            INSERT INTO ACC_PLATFORM_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="dataId != null">`DATA_ID`,</if>
        <if test="orderType != null">`ORDER_TYPE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="platformGoodsId != null">`PLATFORM_GOODS_ID`,</if>
        <if test="platformOrderNo != null">`PLATFORM_ORDER_NO`,</if>
        <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
        <if test="platformGoodsName != null">`PLATFORM_GOODS_NAME`,</if>
        <if test="platformStoreName != null">`PLATFORM_STORE_NAME`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="orderRealType != null">`ORDER_REAL_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="platformTradeTime != null">`PLATFORM_TRADE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="productType != null">`PRODUCT_TYPE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="dataId != null">#{dataId,jdbcType=VARCHAR},</if>
        <if test="orderType != null">#{orderType,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="platformGoodsId != null">#{platformGoodsId,jdbcType=VARCHAR},</if>
        <if test="platformOrderNo != null">#{platformOrderNo,jdbcType=VARCHAR},</if>
        <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
        <if test="platformGoodsName != null">#{platformGoodsName,jdbcType=VARCHAR},</if>
        <if test="platformStoreName != null">#{platformStoreName,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="orderRealType != null">#{orderRealType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="platformTradeTime != null">#{platformTradeTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="productType != null">#{productType,jdbcType=VARCHAR},</if>
    </trim>
            </insert>

            <!--批量新增-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO ACC_PLATFORM_ORDER (
            `ORDER_PRICE`,
            `SP_ID`,
            `BILL_NO`,
            `BLOC_ID`,
            `DATA_ID`,
            `ORDER_TYPE`,
            `SETTLE_DATE`,
            `STORE_BILL_NO`,
            `PLATFORM_CODE`,
            `PLATFORM_GOODS_ID`,
            `PLATFORM_ORDER_NO`,
            `PLATFORM_STORE_ID`,
            `PLATFORM_GOODS_NAME`,
            `PLATFORM_STORE_NAME`,
            `PLATFORM_SETTLE_DATE`,
            `PLATFORM_TRADE_TIME`,
            `FROZEN_AMOUNT`,
            `BUSINESS_SCENE_CODE`,
            `PRODUCT_SCENE_CODE`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderPrice,jdbcType=BIGINT},
                #{item.spId,jdbcType=VARCHAR},
                #{item.billNo,jdbcType=VARCHAR},
                #{item.blocId,jdbcType=VARCHAR},
                #{item.dataId,jdbcType=VARCHAR},
                #{item.orderType,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.storeBillNo,jdbcType=VARCHAR},
                #{item.platformCode,jdbcType=VARCHAR},
                #{item.platformGoodsId,jdbcType=VARCHAR},
                #{item.platformOrderNo,jdbcType=VARCHAR},
                #{item.platformStoreId,jdbcType=VARCHAR},
                #{item.platformGoodsName,jdbcType=VARCHAR},
                #{item.platformStoreName,jdbcType=VARCHAR},
                #{item.platformSettleDate,jdbcType=VARCHAR},
                #{item.platformTradeTime,jdbcType=TIMESTAMP},
                #{item.frozenAmount,jdbcType=BIGINT},
                #{item.businessSceneCode,jdbcType=VARCHAR},
                #{item.productSceneCode,jdbcType=VARCHAR}
            )
        </foreach>
            </select>

            <!--查询待处理的数据列表-->
            <delete id="deleteByBillNo" >
                    delete
        from
        ACC_PLATFORM_ORDER
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
            </delete>
    </mapper>
