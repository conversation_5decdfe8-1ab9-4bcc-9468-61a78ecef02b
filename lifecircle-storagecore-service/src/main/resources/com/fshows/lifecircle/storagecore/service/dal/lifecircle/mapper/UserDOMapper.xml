<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.UserDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.UserDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QQ" property="qq" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TEL" property="tel" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA" property="area" jdbcType="CHAR"
        javaType="String"/>

            <result column="BANK" property="bank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY" property="city" jdbcType="CHAR"
        javaType="String"/>

            <result column="LOGO" property="logo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EMAIL" property="email" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEVEL" property="level" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNAME" property="uname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PEOPLE" property="people" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UTOKEN" property="utoken" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT" property="wechat" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NUM" property="cardNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_WEB" property="cardWeb" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS" property="business" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARDNAME" property="cardname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACTS" property="contacts" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="FULL_PATH" property="fullPath" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="NICKNAME" property="nickname" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PASSWORD" property="password" jdbcType="CHAR"
        javaType="String"/>

            <result column="PROVINCE" property="province" jdbcType="CHAR"
        javaType="String"/>

            <result column="TURNOVER" property="turnover" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRIVILEGES" property="privileges" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFESSION" property="profession" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_ADDRESS" property="cardAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANYNAME" property="companyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_LOGIN_IP" property="lastLoginIp" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="BUSINESS_AREA" property="businessArea" jdbcType="CHAR"
            javaType="String"/>

    <result column="BUSINESS_CITY" property="businessCity" jdbcType="CHAR"
            javaType="String"/>

            <result column="CUSTOMER_NOTE" property="customerNote" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LAST_LOCATION" property="lastLocation" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VERIFYIMAGES" property="verifyimages" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAYACCOUNT" property="alipayaccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPEN_API_CALLBACK" property="openApiCallback" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_PROVINCE" property="businessProvince" jdbcType="CHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASH" property="cash" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ROLE" property="role" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPEN" property="isOpen" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PASS" property="isPass" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OWN_RUN" property="ownRun" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_JDPAY" property="isJdpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_WHITE" property="isWhite" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VIPTIME" property="viptime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHILD_NUM" property="childNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GROUP_NUM" property="groupNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ALIPAY" property="isAlipay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SELF_FEE" property="isSelfFee" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIG_TYPE" property="configType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATETIME" property="createtime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPEN_MINA" property="isOpenMina" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SALESMAN" property="isSalesman" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW_TIPS" property="isShowTips" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LOAN_STATUS" property="loanStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACCOUNT_TYPE" property="accountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_QUICK_CASH" property="isQuickCash" jdbcType="TINYINT"
        javaType="Integer"/>

    <result column="SALESMAN_TAG" property="salesmanTag" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USE_GROUP_NUM" property="useGroupNum" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="CURRENT_LEVEL" property="currentLevel" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="SALES_STAFF_ID" property="salesStaffId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SCAN_SERVICE" property="isScanService" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_LOGIN_TIME" property="lastLoginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CUSTOMER_SERVICE" property="customerService" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_OPENAPI_ACCESS" property="isOpenapiAccess" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SUPER_SALESMAN" property="isSuperSalesman" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ADVERTISEMENT_NUM" property="advertisementNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OPERATION_SERVICE" property="operationService" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NEW_VERSION_USERS_NUM" property="newVersionUsersNum" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="SALESMAN_TAG_START_DAY" property="salesmanTagStartDay" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FINANCE" property="finance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ADVERTISEMENT_BALANCE" property="advertisementBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`QQ`,`TEL`,`AREA`,`BANK`,`CITY`,`LOGO`,`EMAIL`,`LEVEL`,`PHONE`,`UNAME`,`PEOPLE`,`REMARK`,`UTOKEN`,`WECHAT`,`ADDRESS`,`CARD_NUM`,`CARD_WEB`,`BUSINESS`,`CARDNAME`,`CONTACTS`,`FULL_PATH`,`NICKNAME`,`PASSWORD`,`PROVINCE`,`TURNOVER`,`USERNAME`,`PRIVILEGES`,`PROFESSION`,`CARD_ADDRESS`,`COMPANYNAME`,`LAST_LOGIN_IP`,`BUSINESS_AREA`,`BUSINESS_CITY`,`CUSTOMER_NOTE`,`LAST_LOCATION`,`VERIFYIMAGES`,`ALIPAYACCOUNT`,`OPEN_API_CALLBACK`,`BUSINESS_PROVINCE`,`UID`,`CASH`,`ROLE`,`TYPE`,`BELONG`,`IS_OPEN`,`IS_PASS`,`OWN_RUN`,`STATUS`,`IS_JDPAY`,`IS_WHITE`,`VIPTIME`,`CHILD_NUM`,`GROUP_NUM`,`IS_ALIPAY`,`PARENT_ID`,`PLATFORM`,`IS_SELF_FEE`,`CONFIG_TYPE`,`CREATETIME`,`IS_OPEN_MINA`,`IS_SALESMAN`,`IS_SHOW_TIPS`,`LOAN_STATUS`,`ACCOUNT_TYPE`,`IS_QUICK_CASH`,`SALESMAN_TAG`,`SUB_CONFIG_ID`,`USE_GROUP_NUM`,`CURRENT_LEVEL`,`SALES_STAFF_ID`,`IS_SCAN_SERVICE`,`LAST_LOGIN_TIME`,`CUSTOMER_SERVICE`,`IS_OPENAPI_ACCESS`,`IS_SUPER_SALESMAN`,`SUPER_SALESMAN_ID`,`ADVERTISEMENT_NUM`,`OPERATION_SERVICE`,`NEW_VERSION_USERS_NUM`,`SALESMAN_TAG_START_DAY`,`CREATE_TIME`,`UPDATE_TIME`,`FINANCE`,`ADVERTISEMENT_BALANCE`
    </sql>


            <!--根据ID批量获取名称-->
            <select id="findUserNameByIds" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-FINDUSERNAMEBYIDS*/  id,username
        FROM tp_user
        WHERE id in
        <foreach collection="list" item="merchantId" open="(" close=")" separator=",">
            #{merchantId,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--根据ID批量获取受理商名称-->
            <select id="findSalesmanUserNameByIds" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-FINDSALESMANUSERNAMEBYIDS*/  id,username
        FROM tp_user
        WHERE is_salesman = 1 AND id in
        <foreach collection="list" item="merchantId" open="(" close=")" separator=",">
            #{merchantId,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--查询所属代理商-->
            <select id="getBelongByIdAndSianaActiv" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-GETBELONGBYIDANDSIANAACTIV*/  <include refid="Base_Column_List" /> FROM `tp_user` WHERE `id` =#{id,jdbcType=INTEGER}
            </select>

            <!--getBelongById-->
            <select id="getBelongById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-GETBELONGBYID*/  <include refid="Base_Column_List" /> FROM `tp_user` WHERE `id` =#{id,jdbcType=INTEGER}
            </select>

            <!--getByUsername-->
            <select id="getByUsername" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-GETBYUSERNAME*/  id,password,is_salesman as isSalesman,is_pass as isPass,status,viptime,platform,belong FROM tp_user WHERE
        username=#{username,jdbcType=VARCHAR} AND role = 10 AND status != 4
            </select>

    <!--根据用户名批量获取代理商信息-->
    <select id="findListByUserNameList" resultMap="BaseResultMap">
        SELECT /*MS-TP-USER-FINDLISTBYUSERNAMELIST*/
        <include refid="Base_Column_List"/>
        FROM tp_user WHERE username in
        <foreach collection="list" item="username" open="(" close=")" separator=",">
            #{username,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--get:TP_USER-->
    <select id="getById" resultMap="BaseResultMap">
        SELECT /*MS-TP-USER-GETBYID*/
        <include refid="Base_Column_List"/>
        FROM TP_USER
        WHERE
        ID
        = #{id,jdbcType=INTEGER}
    </select>
    </mapper>
