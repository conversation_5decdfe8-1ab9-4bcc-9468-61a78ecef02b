<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwMerchantBuyOrderChildrenDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwMerchantBuyOrderChildrenDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FB_ORDER_SN" property="fbOrderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="NEW_INIT_SN" property="newInitSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="INIT_SN_HISTORY" property="initSnHistory" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CHILDREN_ORDER_SN" property="childrenOrderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ALIPAY_AUDIT_REMARK" property="alipayAuditRemark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ALIPAY_ACTIVITY_INFO_URL" property="alipayActivityInfoUrl" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ALIPA_SALES_ENTRY_ORDER_ID" property="alipaSalesEntryOrderId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PAY_TIME" property="payTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_REFUND" property="isRefund" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="WORKER_ID" property="workerId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="SALES_TYPE" property="salesType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CONFIRM_ORDER" property="confirmOrder" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SESAME_GO_TYPE" property="sesameGoType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="RECYCLE_STATUS" property="recycleStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PERIOD_GENERATE" property="periodGenerate" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SESAME_GO_STATUS" property="sesameGoStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="WORKER_PARENT_ID" property="workerParentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ALIPAY_AUDIT_STATUS" property="alipayAuditStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`INIT_SN`,`ORDER_SN`,`FB_ORDER_SN`,`NEW_INIT_SN`,`ACTIVITY_ID`,`INIT_SN_HISTORY`,`CHILDREN_ORDER_SN`,`ALIPAY_AUDIT_REMARK`,`ALIPAY_ACTIVITY_INFO_URL`,`ALIPA_SALES_ENTRY_ORDER_ID`,`UID`,`AGENT_ID`,`PAY_TIME`,`PAY_TYPE`,`STORE_ID`,`IS_REFUND`,`WORKER_ID`,`SALES_TYPE`,`ORDER_STATUS`,`CONFIRM_ORDER`,`SESAME_GO_TYPE`,`RECYCLE_STATUS`,`PERIOD_GENERATE`,`SESAME_GO_STATUS`,`WORKER_PARENT_ID`,`ALIPAY_AUDIT_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`ORDER_PRICE`
    </sql>


    <!--insert:HW_MERCHANT_BUY_ORDER_CHILDREN-->
    <insert id="insert">
        INSERT INTO HW_MERCHANT_BUY_ORDER_CHILDREN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="fbOrderSn != null">`FB_ORDER_SN`,</if>
            <if test="newInitSn != null">`NEW_INIT_SN`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="initSnHistory != null">`INIT_SN_HISTORY`,</if>
            <if test="childrenOrderSn != null">`CHILDREN_ORDER_SN`,</if>
            <if test="alipayAuditRemark != null">`ALIPAY_AUDIT_REMARK`,</if>
            <if test="alipayActivityInfoUrl != null">`ALIPAY_ACTIVITY_INFO_URL`,</if>
            <if test="alipaSalesEntryOrderId != null">`ALIPA_SALES_ENTRY_ORDER_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="isRefund != null">`IS_REFUND`,</if>
            <if test="workerId != null">`WORKER_ID`,</if>
            <if test="salesType != null">`SALES_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="confirmOrder != null">`CONFIRM_ORDER`,</if>
            <if test="sesameGoType != null">`SESAME_GO_TYPE`,</if>
            <if test="recycleStatus != null">`RECYCLE_STATUS`,</if>
            <if test="periodGenerate != null">`PERIOD_GENERATE`,</if>
            <if test="sesameGoStatus != null">`SESAME_GO_STATUS`,</if>
            <if test="workerParentId != null">`WORKER_PARENT_ID`,</if>
            <if test="alipayAuditStatus != null">`ALIPAY_AUDIT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="fbOrderSn != null">#{fbOrderSn,jdbcType=VARCHAR},</if>
            <if test="newInitSn != null">#{newInitSn,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="initSnHistory != null">#{initSnHistory,jdbcType=VARCHAR},</if>
            <if test="childrenOrderSn != null">#{childrenOrderSn,jdbcType=VARCHAR},</if>
            <if test="alipayAuditRemark != null">#{alipayAuditRemark,jdbcType=VARCHAR},</if>
            <if test="alipayActivityInfoUrl != null">#{alipayActivityInfoUrl,jdbcType=VARCHAR},</if>
            <if test="alipaSalesEntryOrderId != null">#{alipaSalesEntryOrderId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="isRefund != null">#{isRefund,jdbcType=TINYINT},</if>
            <if test="workerId != null">#{workerId,jdbcType=INTEGER},</if>
            <if test="salesType != null">#{salesType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="confirmOrder != null">#{confirmOrder,jdbcType=TINYINT},</if>
            <if test="sesameGoType != null">#{sesameGoType,jdbcType=TINYINT},</if>
            <if test="recycleStatus != null">#{recycleStatus,jdbcType=TINYINT},</if>
            <if test="periodGenerate != null">#{periodGenerate,jdbcType=TINYINT},</if>
            <if test="sesameGoStatus != null">#{sesameGoStatus,jdbcType=TINYINT},</if>
            <if test="workerParentId != null">#{workerParentId,jdbcType=INTEGER},</if>
            <if test="alipayAuditStatus != null">#{alipayAuditStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--selectLatestNotCloseOrderByNewSn-->
    <select id="selectLatestNotCloseOrderByNewSn" resultMap="BaseResultMap">
        select /*MS-HW-MERCHANT-BUY-ORDER-CHILDREN-SELECTLATESTNOTCLOSEORDERBYNEWSN*/
        <include refid="Base_Column_List"/>
        from hw_merchant_buy_order_children
        where new_init_sn = #{sn,jdbcType=VARCHAR}
        and order_status != 2
        and is_refund = 0
        and recycle_status = 0
        order by id desc limit 1
    </select>
</mapper>
