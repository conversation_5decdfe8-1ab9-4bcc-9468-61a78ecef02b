<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.RiskMerchantDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.RiskMerchantDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE_ID" property="typeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_PUNISH" property="merchantPunish" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RISKINFO_INDIRECT_MER_DESC" property="riskinfoIndirectMerDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEAL_TIME" property="dealTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RISK_LEVEL" property="riskLevel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SINGLE_OUT_DAYS" property="singleOutDays" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RISK_DEAL_STATUS" property="riskDealStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CURRENT_ANT_RISK_RANK" property="currentAntRiskRank" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RISKINFO_INDIRECT_MER_SCORE" property="riskinfoIndirectMerScore" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="RISK_RANK_UPDATE_TIME" property="riskRankUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`TYPE_ID`,`MERCHANT_PUNISH`,`RISKINFO_INDIRECT_MER_DESC`,`DEAL_TIME`,`RISK_LEVEL`,`MERCHANT_ID`,`SINGLE_OUT_DAYS`,`RISK_DEAL_STATUS`,`CURRENT_ANT_RISK_RANK`,`RISKINFO_INDIRECT_MER_SCORE`,`CREATE_TIME`,`UPDATE_TIME`,`RISK_RANK_UPDATE_TIME`
    </sql>


            <!--insert:FK_RISK_MERCHANT-->
            <insert id="insert" >
            INSERT INTO FK_RISK_MERCHANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="typeId != null">`TYPE_ID`,</if>
        <if test="dealTime != null">`DEAL_TIME`,</if>
        <if test="riskLevel != null">`RISK_LEVEL`,</if>
        <if test="merchantId != null">`MERCHANT_ID`,</if>
        <if test="singleOutDays != null">`SINGLE_OUT_DAYS`,</if>
        <if test="riskDealStatus != null">`RISK_DEAL_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="typeId != null">#{typeId,jdbcType=VARCHAR},</if>
        <if test="dealTime != null">#{dealTime,jdbcType=INTEGER},</if>
        <if test="riskLevel != null">#{riskLevel,jdbcType=TINYINT},</if>
        <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
        <if test="singleOutDays != null">#{singleOutDays,jdbcType=TINYINT},</if>
        <if test="riskDealStatus != null">#{riskDealStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据唯一约束UniqMerchantId获取数据:fk_risk_merchant-->
            <select id="getByUniqMerchantId" resultMap="BaseResultMap">
                    SELECT /*MS-FK-RISK-MERCHANT-GETBYUNIQMERCHANTID*/  <include refid="Base_Column_List" />
        FROM fk_risk_merchant
        WHERE
        <![CDATA[
            MERCHANT_ID     = #{merchantId,jdbcType=INTEGER}
        ]]>
        LIMIT 1
            </select>
    </mapper>
