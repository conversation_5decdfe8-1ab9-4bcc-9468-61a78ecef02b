<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BlocBindMerchantDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BlocBindMerchantDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="TYPE" property="type" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`ORG_ID`,`TOKEN`,`BLOC_ID`,`UID`,`TYPE`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_AGENT_INFO_CHANGE_LOG-->
    <insert id="insert">
        INSERT INTO TP_BLOC_BIND_MERCHANT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="blocId != null">BLOC_ID,</if>
            <if test="type != null">TYPE,</if>
            <if test="token != null">TOKEN,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="uid != null">UID,</if>
            <if test="orgId != null">ORG_ID,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
</mapper>
