<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.ExtraCommunicationsStatementAccountDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CommunicationsStatementAccountDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CUSID" property="cusid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BUTT_NO" property="buttNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEAL_NO" property="dealNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BATCHID" property="batchid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_NO" property="orderNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BANK_TYPE" property="bankType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEAL_TYPE" property="dealType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CHNLTRXID" property="chnltrxid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="VOUCHER_NO" property="voucherNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CARDHOLDER" property="cardholder" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TERMINAL_NO" property="terminalNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BANK_ISSUING" property="bankIssuing" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_TYPE" property="productType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CARDHOLDER_CARD" property="cardholderCard" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="INSTALLMENT_NUM" property="installmentNum" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_REMARK" property="merchantRemark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEAL_DATE" property="dealDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="DEAL_TIME" property="dealTime" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="FINISH_TIME" property="finishTime" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEAL_MONEY" property="dealMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="SETTLE_MONEY" property="settleMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="ORIGINAL_MONEY" property="originalMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="SERVICE_CHARGE" property="serviceCharge" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="INSTALLMENT_FEE" property="installmentFee" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,
        `CUSID`,
        `BUTT_NO`,
        `DEAL_NO`,
        `BATCHID`,
        `ORDER_NO`,
        `BANK_CODE`,
        `BANK_NAME`,
        `BANK_TYPE`,
        `DEAL_TYPE`,
        `CHNLTRXID`,
        `STORE_NAME`,
        `VOUCHER_NO`,
        `CARDHOLDER`,
        `TERMINAL_NO`,
        `BANK_ISSUING`,
        `PRODUCT_TYPE`,
        `CARDHOLDER_CARD`,
        `INSTALLMENT_NUM`,
        `MERCHANT_REMARK`,
        `DEAL_DATE`,
        `DEAL_TIME`,
        `CREATE_TIME`,
        `FINISH_TIME`,
        `DEAL_MONEY`,
        `SETTLE_MONEY`,
        `ORIGINAL_MONEY`,
        `SERVICE_CHARGE`,
        `INSTALLMENT_FEE`
    </sql>

    <insert id="insertBatch">
        INSERT INTO TP_COMMUNICATIONS_STATEMENT_ACCOUNT(
        `CUSID`,
        `BUTT_NO`,
        `DEAL_NO`,
        `BATCHID`,
        `ORDER_NO`,
        `BANK_CODE`,
        `BANK_NAME`,
        `BANK_TYPE`,
        `DEAL_TYPE`,
        `CHNLTRXID`,
        `STORE_NAME`,
        `VOUCHER_NO`,
        `CARDHOLDER`,
        `TERMINAL_NO`,
        `BANK_ISSUING`,
        `PRODUCT_TYPE`,
        `CARDHOLDER_CARD`,
        `INSTALLMENT_NUM`,
        `MERCHANT_REMARK`,
        `DEAL_DATE`,
        `DEAL_TIME`,
        `FINISH_TIME`,
        `DEAL_MONEY`,
        `SETTLE_MONEY`,
        `ORIGINAL_MONEY`,
        `SERVICE_CHARGE`,
        `INSTALLMENT_FEE`,
        `MERCHANT_ID`,
        `AGENT_ID`,
        `SHARE_PROFIT`,
        `PROFIT_TYPE`
        )VALUES
        <foreach collection="list" item="co" index="index" separator=",">
            (
            #{co.cusid,          jdbcType=VARCHAR},
            #{co.buttNo,        jdbcType=VARCHAR},
            #{co.dealNo,        jdbcType=VARCHAR},
            #{co.batchid,        jdbcType=VARCHAR},
            #{co.orderNo,       jdbcType=VARCHAR},
            #{co.bankCode,      jdbcType=VARCHAR},
            #{co.bankName,      jdbcType=VARCHAR},
            #{co.bankType,      jdbcType=VARCHAR},
            #{co.dealType,      jdbcType=VARCHAR},
            #{co.chnltrxid,      jdbcType=VARCHAR},
            #{co.storeName,     jdbcType=VARCHAR},
            #{co.voucherNo,     jdbcType=VARCHAR},
            #{co.cardholder,     jdbcType=VARCHAR},
            #{co.terminalNo,    jdbcType=VARCHAR},
            #{co.bankIssuing,   jdbcType=VARCHAR},
            #{co.productType,   jdbcType=VARCHAR},
            #{co.cardholderCard,jdbcType=VARCHAR},
            #{co.installmentNum,jdbcType=VARCHAR},
            #{co.merchantRemark,jdbcType=VARCHAR},
            #{co.dealDate,      jdbcType=TIMESTAMP},
            #{co.dealTime,      jdbcType=VARCHAR},
            #{co.finishTime,    jdbcType=VARCHAR},
            #{co.dealMoney,     jdbcType=DECIMAL},
            #{co.settleMoney,   jdbcType=DECIMAL},
            #{co.originalMoney, jdbcType=DECIMAL},
            #{co.serviceCharge, jdbcType=DECIMAL},
            #{co.installmentFee, jdbcType=DECIMAL},
            #{co.merchantId, jdbcType=INTEGER},
            #{co.agentId, jdbcType=INTEGER},
            #{co.shareProfit, jdbcType=DECIMAL},
            #{co.profitType, jdbcType=TINYINT}
            )
        </foreach>
    </insert>
</mapper>
