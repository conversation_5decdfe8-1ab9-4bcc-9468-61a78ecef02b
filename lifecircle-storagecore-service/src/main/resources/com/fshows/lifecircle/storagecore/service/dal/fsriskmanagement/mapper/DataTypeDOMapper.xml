<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.DataTypeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.DataTypeDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="NAME" property="name" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_TYPE_ID" property="dataTypeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_TYPE_CODE" property="dataTypeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_TYPE" property="platformType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RISK_SOURCE_ID" property="riskSourceId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`NAME`,`DATA_TYPE_ID`,`DATA_TYPE_CODE`,`PLATFORM_TYPE`,`RISK_SOURCE_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:FK_DATA_TYPE-->
            <insert id="insert" >
            INSERT INTO FK_DATA_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="name != null">`NAME`,</if>
        <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
        <if test="dataTypeCode != null">`DATA_TYPE_CODE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="name != null">#{name,jdbcType=VARCHAR},</if>
        <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
        <if test="dataTypeCode != null">#{dataTypeCode,jdbcType=VARCHAR},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据唯一约束UniqDataTypeId获取数据:fk_data_type-->
            <select id="getByUniqDataTypeId" resultMap="BaseResultMap">
                    SELECT /*MS-FK-DATA-TYPE-GETBYUNIQDATATYPEID*/  <include refid="Base_Column_List" />
        FROM fk_data_type
        WHERE
        <![CDATA[
            DATA_TYPE_ID    = #{dataTypeId,jdbcType=VARCHAR}
        ]]>
        LIMIT 1
            </select>
    </mapper>
