<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardStockDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardStockDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STOCK" property="stock" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LOCK_STOCK" property="lockStock" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_SKU_ID`,`CARD_SPU_ID`,`PUBLISH_ORG_ID`,`IS_DEL`,`STOCK`,`LOCK_STOCK`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_STOCK-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="publishOrgId != null">`PUBLISH_ORG_ID`,</if>
            <if test="lockStock != null">`LOCK_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="publishOrgId != null">#{publishOrgId,jdbcType=VARCHAR},</if>
            <if test="lockStock != null">#{lockStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据stock和skuId增加库存信息-->
            <update id="updateStockAddByStockAndCardSku" >
                    update
        tp_prepay_card_stock
        <set>
            <if test="stock != null">
                stock = stock + #{stock,jdbcType=INTEGER},
            </if>
            <if test="lockStock != null">
                lock_stock = lock_stock + #{lockStock,jdbcType=INTEGER},
            </if>
        </set>
        WHERE
        is_del =0
        AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
            </update>

            <!--根据stock和skuId扣减库存信息-->
            <update id="updateStockSubtractByStockAndCardSku" >
                    update
        tp_prepay_card_stock
        <set>
            <if test="stock != null">
                stock = stock - #{stock,jdbcType=INTEGER},
            </if>
            <if test="lockStock != null">
                lock_stock = lock_stock - #{lockStock,jdbcType=INTEGER},
            </if>
        </set>
        WHERE
        is_del =0
        AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
            </update>
    </mapper>
