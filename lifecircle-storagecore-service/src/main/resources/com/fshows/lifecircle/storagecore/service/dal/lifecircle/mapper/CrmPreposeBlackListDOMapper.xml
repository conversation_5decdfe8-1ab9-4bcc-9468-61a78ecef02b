<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CrmPreposeBlackListDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CrmPreposeBlackListDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLACK_LIST_ID" property="blackListId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SYS_USER_ID" property="sysUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>

        <resultMap id="preAuditBlackList" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.PreAuditBlackListDO">

                <result column="create_time" property="createTime" javaType="java.util.Date"/>

                <result column="username" property="username" javaType="java.lang.String"/>

                <result column="black_list_id" property="blackListId" javaType="java.lang.String"/>

                <result column="sys_user_id" property="sysUserId" javaType="java.lang.Integer"/>

                <result column="is_salesman" property="isSalesman" javaType="java.lang.Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`REMARKS`,`CREATE_BY`,`UPDATE_BY`,`BLACK_LIST_ID`,`STATUS`,`SYS_USER_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_CRM_PREPOSE_BLACK_LIST-->
            <insert id="insert" >
                    INSERT INTO TP_CRM_PREPOSE_BLACK_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="sysUserId != null">`SYS_USER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="blackListId != null">`BLACK_LIST_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="sysUserId != null">#{sysUserId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="blackListId != null">#{blackListId,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据用户id查询-->
            <select id="getBySysUserId" resultMap="BaseResultMap">
                    select
        id,
        black_list_id,
        sys_user_id,
        status,
        create_by,
        update_by,
        remarks,
        create_time,
        update_time
        from tp_crm_prepose_black_list
        where sys_user_id = #{sysUserId,jdbcType=INTEGER} and status = 1
            </select>

            <!--查询所有生效的黑名单信息-->
            <select id="getBlankList" resultMap="BaseResultMap">
                    select
        id,
        black_list_id,
        sys_user_id,
        status,
        create_by,
        update_by,
        remarks,
        create_time,
        update_time
        from tp_crm_prepose_black_list
        where status = 1
            </select>

            <!--查询前置黑名单列表 pageCount-->
            <select id="getPreAuditBlackListListBySearchCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 tp_crm_prepose_black_list a
        LEFT JOIN tp_user b ON a.sys_user_id = b.id
        WHERE a.status = 1
        <if test="isSalesman != null and isSalesman != -1">
            AND b.is_salesman = #{isSalesman,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND b.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null">
            AND a.create_time BETWEEN #{beginDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        </if>
        
            </select>
            <!--查询前置黑名单列表 pageResult-->
            <select id="getPreAuditBlackListListBySearchResult"  resultMap="preAuditBlackList">
                    SELECT
        a.black_list_id, a.sys_user_id, a.create_time, b.username, b.is_salesman
        FROM tp_crm_prepose_black_list a
        LEFT JOIN tp_user b ON a.sys_user_id = b.id
        WHERE a.status = 1
        <if test="isSalesman != null and isSalesman != -1">
            AND b.is_salesman = #{isSalesman,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND b.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null">
            AND a.create_time BETWEEN #{beginDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        </if>
        ORDER BY a.create_time DESC
            limit #{startRow},#{limit}
            </select>

            <!--导出前置黑名单列表-->
            <select id="exportPreAuditBlackListListBySearch" resultMap="preAuditBlackList">
                    SELECT
        a.black_list_id, a.sys_user_id, a.create_time, b.username, b.is_salesman
        FROM tp_crm_prepose_black_list a
        LEFT JOIN tp_user b ON a.sys_user_id = b.id
        WHERE a.status = 1
        <if test="isSalesman != null and isSalesman != -1">
            AND b.is_salesman = #{isSalesman,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND b.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null">
            AND a.create_time BETWEEN #{beginDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        </if>
        ORDER BY a.create_time DESC
            </select>
    </mapper>
