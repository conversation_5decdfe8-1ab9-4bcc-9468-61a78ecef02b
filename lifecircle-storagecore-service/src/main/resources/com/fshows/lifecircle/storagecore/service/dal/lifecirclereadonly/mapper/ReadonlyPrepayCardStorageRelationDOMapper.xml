<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardStorageRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardStorageRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`STORAGE_ORDER`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_STORAGE_RELATION-->
            <insert id="insert" >
            INSERT INTO TP_PREPAY_CARD_STORAGE_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
        <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
        <if test="storageOrder != null">`STORAGE_ORDER`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
        <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
        <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据出入库订单号查询预付卡出入库关联列表-->
    <select id="findListStorageRecordByStorageOrder" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        TP_PREPAY_CARD_STORAGE_RELATION
        WHERE
        is_del = 0
        AND storage_order = #{storageOrder, jdbcType=VARCHAR}
    </select>

    <!--根据出入库单号和卡号查询出入库记录明细-->
    <select id="findListCardStorageRelationSegmentByCardNos"
            resultType="com.fshows.lifecircle.storagecore.service.domain.model.prepaycard.PrepayCardRefundCardInfoModel">
        SELECT
        t1.card_sku_id cardSkuId,
        t1.card_no cardNo,
        t2.card_price cardPrice
        FROM
        TP_PREPAY_CARD_STORAGE_RELATION t1
        left join tp_prepay_card t2 on t1.card_no = t2.card_no
        WHERE
        t1.is_del = 0 and t2.is_del = 0
        AND t1.storage_order = #{storageOrder, jdbcType=VARCHAR}
        AND t1.`card_no` IN
        <foreach close=")" collection="cardNos" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--根据出库订单查询出库关联订单信息-->
    <select id="findStorageRelationByStorageOrderNoList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from tp_prepay_card_storage_relation
        where storage_order in
        <foreach collection="list" item="orderNo" open="(" close=")" separator=",">
            #{orderNo, jdbcType=VARCHAR}
        </foreach>
    </select>

    <!--根据关联订单查询卡信息列表-->
    <select id="findStorageRelationByStorageOrderNoListAndOperateType" resultMap="BaseResultMap">
        select
        relation.*
        from TP_PREPAY_CARD_STORAGE_RECORD record
        left join tp_prepay_card_storage_relation relation on record.storage_order = relation.storage_order
        where record.storage_order in
        <foreach collection="list" item="storageOrderNo" open="(" close=")" separator=",">
            #{storageOrderNo, jdbcType=VARCHAR}
        </foreach>
        and record.operate_type = #{operateType,jdbcType=INTEGER}
    </select>
</mapper>
