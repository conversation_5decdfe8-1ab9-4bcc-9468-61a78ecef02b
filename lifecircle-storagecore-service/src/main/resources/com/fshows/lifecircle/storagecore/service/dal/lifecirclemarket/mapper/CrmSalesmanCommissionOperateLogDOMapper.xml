<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmSalesmanCommissionOperateLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmSalesmanCommissionOperateLogDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="UNIONID" property="unionid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRE_SALESMAN_COST_RATE_EXT" property="preSalesmanCostRateExt" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AFTER_SALESMAN_COST_RATE_EXT" property="afterSalesmanCostRateExt" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRE_COMMISSION_TYPE" property="preCommissionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRE_FIXED_COMMISSION" property="preFixedCommission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AFTER_COMMISSION_TYPE" property="afterCommissionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRE_NORMAL_COMMISSION" property="preNormalCommission" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AFTER_FIXED_COMMISSION" property="afterFixedCommission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AFTER_NORMAL_COMMISSION" property="afterNormalCommission" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRE_FIXED_COMMISSION_EFFECTIVE_TIME" property="preFixedCommissionEffectiveTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AFTER_FIXED_COMMISSION_EFFECTIVE_TIME" property="afterFixedCommissionEffectiveTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UNIONID`,`REAL_NAME`,`JOB_NUMBER`,`PRE_SALESMAN_COST_RATE_EXT`,`AFTER_SALESMAN_COST_RATE_EXT`,`IS_DEL`,`USER_ID`,`PRE_COMMISSION_TYPE`,`PRE_FIXED_COMMISSION`,`AFTER_COMMISSION_TYPE`,`PRE_NORMAL_COMMISSION`,`AFTER_FIXED_COMMISSION`,`AFTER_NORMAL_COMMISSION`,`PRE_FIXED_COMMISSION_EFFECTIVE_TIME`,`AFTER_FIXED_COMMISSION_EFFECTIVE_TIME`,`BEGIN_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG-->
            <insert id="insert" >
                    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="unionid != null">`UNIONID`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="preSalesmanCostRateExt != null">`PRE_SALESMAN_COST_RATE_EXT`,</if>
            <if test="afterSalesmanCostRateExt != null">`AFTER_SALESMAN_COST_RATE_EXT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="preCommissionType != null">`PRE_COMMISSION_TYPE`,</if>
            <if test="afterCommissionType != null">`AFTER_COMMISSION_TYPE`,</if>
            <if test="preNormalCommission != null">`PRE_NORMAL_COMMISSION`,</if>
            <if test="afterNormalCommission != null">`AFTER_NORMAL_COMMISSION`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="preFixedCommission != null">`pre_fixed_commission`,</if>
            <if test="afterFixedCommission != null">`after_fixed_commission`,</if>
            <if test="preFixedCommissionEffectiveTime != null">`pre_fixed_commission_effective_time`,</if>
            <if test="afterFixedCommissionEffectiveTime != null">`after_fixed_commission_effective_time`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="unionid != null">#{unionid,jdbcType=VARCHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="preSalesmanCostRateExt != null">#{preSalesmanCostRateExt,jdbcType=VARCHAR},</if>
            <if test="afterSalesmanCostRateExt != null">#{afterSalesmanCostRateExt,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="preCommissionType != null">#{preCommissionType,jdbcType=TINYINT},</if>
            <if test="afterCommissionType != null">#{afterCommissionType,jdbcType=TINYINT},</if>
            <if test="preNormalCommission != null">#{preNormalCommission,jdbcType=INTEGER},</if>
            <if test="afterNormalCommission != null">#{afterNormalCommission,jdbcType=INTEGER},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="preFixedCommission != null">#{preFixedCommission,jdbcType=TINYINT},</if>
            <if test="afterFixedCommission != null">#{afterFixedCommission,jdbcType=TINYINT},</if>
            <if test="preFixedCommissionEffectiveTime != null">#{preFixedCommissionEffectiveTime,jdbcType=INTEGER},</if>
            <if test="afterFixedCommissionEffectiveTime != null">
                #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER},
            </if>
        </trim>
            </insert>

            <!--获取授理商最后一次分拥方式为分拥比例的变更记录-->
            <select id="getGrantLastFromCommissionChangeLog" resultMap="BaseResultMap">
                    SELECT /*MS-LM-CRM-SALESMAN-COMMISSION-OPERATE-LOG-GETGRANTLASTFROMCOMMISSIONCHANGELOG*/  <include refid="Base_Column_List" />
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        WHERE
        user_id = #{userId, jdbcType=INTEGER}
        AND pre_commission_type = 2
        AND is_del = 0
        ORDER BY id DESC
        LIMIT 1
            </select>

            <!--获取授理商交易方式从比例到成本的列表-->
            <select id="findGrantChangeTypeLogList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <where>
            is_del = 0
            AND pre_commission_type = 2
            AND after_commission_type IN (1,3)
            <if test="list !=null and list.size()&gt;0">
                AND user_id IN
                <foreach collection="list" item="userId" open="(" separator="," close=")">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
            </select>

            <!--修改操作记录表-->
            <update id="updateOperateLog" >
                    UPDATE
        LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <set>
            <if test="afterCommissionType !=null">
                AFTER_COMMISSION_TYPE=#{afterCommissionType,jdbcType=VARCHAR},
            </if>
            <if test="afterNormalCommission !=null">
                after_normal_commission=#{afterNormalCommission,jdbcType=VARCHAR},
            </if>
            <if test="afterSalesmanCostRateExt !=null">
                AFTER_SALESMAN_COST_RATE_EXT=#{afterSalesmanCostRateExt,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
            </update>

            <!--查询用户固定比例操作日志-->
            <select id="findUserFixCommissionLog" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND after_fixed_commission = #{afterFixedCommission, jdbcType=INTEGER}
        AND user_id = #{userId,jdbcType=INTEGER}
            </select>

            <!--查询最近一次指定月份的操作日志-->
            <select id="getNearAppointTimeMaxLogId" resultType="java.lang.Long">
                    SELECT
        MAX( `id`)
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND user_id = #{userId, jdbcType=INTEGER}
        AND after_fixed_commission_effective_time  <![CDATA[ <= ]]>
        #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER}
            </select>

            <!--查询指定月份最大操作日志-->
            <select id="getAppointMothMaxLogId" resultType="java.lang.Long">
                    SELECT
        MAX( `id`)
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND user_id = #{userId, jdbcType=INTEGER}
        AND after_fixed_commission_effective_time = #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER}
            </select>

            <!--根据id查询日志记录-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        and id = #{id,jdbcType=BIGINT}
        limit 1
            </select>

            <!--查询用户固定比例操作日志-->
            <select id="findFixCommissionLogByUserIdList" resultType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.FixedCommissionLogStaticInfo">
                    SELECT
        user_id as userId,
         count(*)  as num
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND after_fixed_commission = #{afterFixedCommission, jdbcType=INTEGER}
        AND user_id in
        <foreach collection="list" index="index" item="userId" open="(" separator="," close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
        group by user_id
            </select>
    </mapper>
