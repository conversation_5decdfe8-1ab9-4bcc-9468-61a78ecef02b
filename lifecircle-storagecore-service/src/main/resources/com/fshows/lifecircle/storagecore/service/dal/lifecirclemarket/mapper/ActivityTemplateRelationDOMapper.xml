<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ActivityTemplateRelationDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ActivityTemplateRelationDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SINAN_ACTIVITY_CODE" property="sinanActivityCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RELATION_TEMPLATE_CODE" property="relationTemplateCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RELATION_TEMPLATE_NAME" property="relationTemplateName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RELATION_TEMPLATE_TYPE" property="relationTemplateType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="FIT_RANGE" property="fitRange" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SORT_SCORE" property="sortScore" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="EFFECTIVE_STATUS" property="effectiveStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`ACTIVITY_ID`,`SINAN_ACTIVITY_CODE`,`RELATION_TEMPLATE_CODE`,`RELATION_TEMPLATE_NAME`,`RELATION_TEMPLATE_TYPE`,`IS_DEL`,`FIT_RANGE`,`SORT_SCORE`,`EFFECTIVE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_ACTIVITY_TEMPLATE_RELATION-->
    <insert id="insert">
        INSERT INTO LM_ACTIVITY_TEMPLATE_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="sinanActivityCode != null">`SINAN_ACTIVITY_CODE`,</if>
            <if test="relationTemplateCode != null">`RELATION_TEMPLATE_CODE`,</if>
            <if test="relationTemplateName != null">`RELATION_TEMPLATE_NAME`,</if>
            <if test="relationTemplateType != null">`RELATION_TEMPLATE_TYPE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="fitRange != null">`FIT_RANGE`,</if>
            <if test="sortScore != null">`SORT_SCORE`,</if>
            <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="sinanActivityCode != null">#{sinanActivityCode,jdbcType=VARCHAR},</if>
            <if test="relationTemplateCode != null">#{relationTemplateCode,jdbcType=VARCHAR},</if>
            <if test="relationTemplateName != null">#{relationTemplateName,jdbcType=VARCHAR},</if>
            <if test="relationTemplateType != null">#{relationTemplateType,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="fitRange != null">#{fitRange,jdbcType=TINYINT},</if>
            <if test="sortScore != null">#{sortScore,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--findActivityIdListByCode-->
    <select id="findActivityIdListByCode" resultMap="BaseResultMap">
        SELECT /*MS-LM-ACTIVITY-TEMPLATE-RELATION-FINDACTIVITYIDLISTBYCODE*/
        <include refid="Base_Column_List"/>
        FROM lm_activity_template_relation
        WHERE is_del = 0
        AND effective_status = 1
        AND sinan_activity_code = #{sinanActivityCode, jdbcType=VARCHAR}
        GROUP BY activity_id
    </select>
</mapper>
