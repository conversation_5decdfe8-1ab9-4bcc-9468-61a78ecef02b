<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.mapper.StockDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.dataobject.StockDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="GS_UID" property="gsUid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GS_STORE_ID" property="gsStoreId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GS_STORE_NAME" property="gsStoreName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_UNLIMITED_STOCK" property="isUnlimitedStock" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="STOCK_NUM" property="stockNum" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GS_UID`,`GOODS_ID`,`CREATE_BY`,`UPDATE_BY`,`GOODS_NAME`,`GS_STORE_ID`,`GS_STORE_NAME`,`DEL_FLAG`,`IS_UNLIMITED_STOCK`,`CREATE_TIME`,`UPDATE_TIME`,`STOCK_NUM`
    </sql>


    <!--getGroupGoodsStock-->
    <select id="getGroupGoodsStock" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        from gs_stock
        where goods_id = #{goodsId,jdbcType=VARCHAR}
        AND gs_store_id = #{gsStoreId,jdbcType=VARCHAR}
        AND del_flag = 1
    </select>
</mapper>
