<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwFlowCardDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwFlowCardDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ICCID" property="iccid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SIM_CARD" property="simCard" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_TYPE" property="cardType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TOTAL_FLOW" property="totalFlow" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BEYOND_FLOW" property="beyondFlow" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_STATUS" property="cardStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_QUERY_DAY" property="lastQueryDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="RESIDUAL_FLOW" property="residualFlow" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EFFECTIVE_TIME" property="effectiveTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EXPIRATION_TIME" property="expirationTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ICCID`,`INIT_SN`,`SIM_CARD`,`OPERATOR`,`IS_DEL`,`CARD_TYPE`,`TOTAL_FLOW`,`BEYOND_FLOW`,`CARD_STATUS`,`LAST_QUERY_DAY`,`RESIDUAL_FLOW`,`EFFECTIVE_TIME`,`EXPIRATION_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_FLOW_CARD-->
            <insert id="insert" >
                    INSERT INTO HW_FLOW_CARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="iccid != null">`ICCID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="simCard != null">`SIM_CARD`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="cardType != null">`CARD_TYPE`,</if>
            <if test="totalFlow != null">`TOTAL_FLOW`,</if>
            <if test="beyondFlow != null">`BEYOND_FLOW`,</if>
            <if test="cardStatus != null">`CARD_STATUS`,</if>
            <if test="lastQueryDay != null">`LAST_QUERY_DAY`,</if>
            <if test="residualFlow != null">`RESIDUAL_FLOW`,</if>
            <if test="effectiveTime != null">`EFFECTIVE_TIME`,</if>
            <if test="expirationTime != null">`EXPIRATION_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="iccid != null">#{iccid,jdbcType=VARCHAR},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="simCard != null">#{simCard,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
            <if test="totalFlow != null">#{totalFlow,jdbcType=INTEGER},</if>
            <if test="beyondFlow != null">#{beyondFlow,jdbcType=INTEGER},</if>
            <if test="cardStatus != null">#{cardStatus,jdbcType=TINYINT},</if>
            <if test="lastQueryDay != null">#{lastQueryDay,jdbcType=INTEGER},</if>
            <if test="residualFlow != null">#{residualFlow,jdbcType=INTEGER},</if>
            <if test="effectiveTime != null">#{effectiveTime,jdbcType=INTEGER},</if>
            <if test="expirationTime != null">#{expirationTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--queryCrmFlowCardInfoList pageCount-->
            <select id="queryCrmFlowCardInfoListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 HW_FLOW_CARD a
        LEFT JOIN `hw_equipment_sn` b on a.init_sn= b.`init_sn`
        where a.IS_DEL=0
        <if test="cardType!=null">
            and card_type = #{cardType,jdbcType=TINYINT}
        </if>
        <if test="agentId!=null">
            and `agent_id` = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="merchantId!=null">
            and `uid` = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="expireStartTime!=null and expireEndTime!=null">
            and expiration_time between #{expireStartTime,jdbcType=INTEGER} and #{expireEndTime,jdbcType=INTEGER}
        </if>
        <if test="initSn!=null and initSn!=''">
            and a.`init_sn` = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="iccid!=null and iccid!=''">
            and `iccid` = #{iccid,jdbcType=VARCHAR}
        </if>
        <if test="overFlow!=null">
            and #{overFlow,jdbcType=INTEGER} = #{overFlow,jdbcType=INTEGER}
        </if>
        <if test="overFlow==1">
            and BEYOND_FLOW &gt; 0
        </if>
        <if test="overFlow==2">
            and BEYOND_FLOW =0
        </if>
        
            </select>
            <!--queryCrmFlowCardInfoList pageResult-->
            <select id="queryCrmFlowCardInfoListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.model.harvest.FlowCardListExportModel">
                    SELECT /*MS-HW-FLOW-CARD-QUERYCRMFLOWCARDINFOLIST*/  a.init_sn as initSn,agent_id as agentId,uid as merchantId, `store_id` as storeId ,total_flow as
        totalFlow, residual_flow as residualFlow,beyond_flow as beyondFlow,operator,
        expiration_time as expirationTime,effective_time as effectiveTime,CARD_STATUS as cardStatus,card_type as
        cardType
        FROM HW_FLOW_CARD a
        LEFT JOIN `hw_equipment_sn` b on a.init_sn= b.`init_sn`
        where a.IS_DEL=0
        <if test="cardType!=null">
            and card_type = #{cardType,jdbcType=TINYINT}
        </if>
        <if test="agentId!=null">
            and `agent_id` = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="merchantId!=null">
            and `uid` = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="expireStartTime!=null and expireEndTime!=null">
            and expiration_time between #{expireStartTime,jdbcType=INTEGER} and #{expireEndTime,jdbcType=INTEGER}
        </if>
        <if test="initSn!=null and initSn!=''">
            and a.`init_sn` = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="iccid!=null and iccid!=''">
            and `iccid` = #{iccid,jdbcType=VARCHAR}
        </if>
        <if test="overFlow!=null">
            and #{overFlow,jdbcType=INTEGER} = #{overFlow,jdbcType=INTEGER}
        </if>
        <if test="overFlow==1">
            and BEYOND_FLOW &gt; 0
        </if>
        <if test="overFlow==2">
            and BEYOND_FLOW =0
        </if>
        order by a.id
            limit #{startRow},#{limit}
            </select>
    </mapper>
