<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentStorageOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentStorageOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CREATER" property="creater" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXAMINER" property="examiner" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SUPPLIER" property="supplier" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXAMINER_NUMBER" property="examinerNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="BIZ_TYPE" property="bizType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_NUM" property="orderNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PURCHASE_TYPE" property="purchaseType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="EXECUTE_TIME" property="executeTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UNIT_PRICE" property="unitPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>

        <resultMap id="InStoreroomDirectOrderInfoMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.InStoreroomDirectOrderInfoMap">

                <result column="create_time" property="createTime" javaType="java.util.Date"/>

                <result column="username" property="username" javaType="java.lang.String"/>

                <result column="storage_order" property="storageOrder" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

                <result column="order_num" property="orderNum" javaType="java.lang.Integer"/>

                <result column="operate_type" property="operateType" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="EquipmentSnExportMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentSnExportMap">

            <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

            <result column="storage_order" property="storageOrder" javaType="java.lang.String"/>

            <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

            <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="depot_org" property="depotOrg" javaType="java.lang.Integer"/>

            <result column="order_type" property="orderType" javaType="java.lang.Integer"/>

            <result column="unit_price" property="unitPrice" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="PurchaseOrderEquipmentInfoMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.PurchaseOrderEquipmentInfoMap">

            <result column="create_time" property="createTime" javaType="java.util.Date"/>

            <result column="system_sn" property="systemSn" javaType="java.lang.String"/>

            <result column="stock_order" property="stockOrder" javaType="java.lang.String"/>

            <result column="storage_order" property="storageOrder" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="ByEquipmentIdMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ByEquipmentIdMap">

            <result column="num" property="num" javaType="java.lang.Integer"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="ByEquipmentInMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ByEquipmentInMap">

            <result column="num" property="num" javaType="java.lang.Integer"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>

            <result column="operate_type" property="operateType" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="ByBizTypeMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ByBizTypeMap">

            <result column="num" property="num" javaType="java.lang.Integer"/>

            <result column="operate_type" property="operateType" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="InEuipmentSnMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.InEuipmentSnMap">

            <result column="execute_time" property="executeTime" javaType="java.util.Date"/>

            <result column="init_sn" property="initSn" javaType="java.lang.String"/>

            <result column="order_no" property="orderNo" javaType="java.lang.String"/>

            <result column="stock_order" property="stockOrder" javaType="java.lang.String"/>

            <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

            <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="unit_price" property="unitPrice" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="OrderDepotSnListMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.OrderDepotSnListMap">

            <result column="execute_time" property="executeTime" javaType="java.util.Date"/>

            <result column="init_sn" property="initSn" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>

        <sql id="Base_Column_List">
    `ID`,`REMARK`,`CREATER`,`EXAMINER`,`SUPPLIER`,`JOB_NUMBER`,`STORAGE_ORDER`,`EXAMINER_NUMBER`,`DEPOT`,`IS_DEL`,`AGENT_ID`,`BIZ_TYPE`,`ORDER_NUM`,`ORDER_TYPE`,`ARRIVAL_NUM`,`EQUIPMENT_ID`,`OPERATE_TYPE`,`ORDER_STATUS`,`PURCHASE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`EXECUTE_TIME`,`UNIT_PRICE`,`ORDER_PRICE`
    </sql>


            <!--insert:HW_EQUIPMENT_STORAGE_ORDER-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_STORAGE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="examiner != null">`EXAMINER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="examinerNumber != null">`EXAMINER_NUMBER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="orderNum != null">`ORDER_NUM`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="examiner != null">#{examiner,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="examinerNumber != null">#{examinerNumber,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="bizType != null">#{bizType,jdbcType=TINYINT},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

        <!--根据订单号查询直营入库订单信息-->
        <select id="getInStoreroomDirectByStorageOrder" resultMap="InStoreroomDirectOrderInfoMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINSTOREROOMDIRECTBYSTORAGEORDER*/
            a.storage_order,a.create_time,b.username,a.order_num,a.operate_type,a.depot
            from hw_equipment_storage_order a
            LEFT JOIN tp_user b on a.agent_id = b.id
            where a.order_type = 1 and a.biz_type = 2
            and a.storage_order = #{storageOrder,jdbcType=VARCHAR}
            limit 1
        </select>

        <!--根据订单号查询非直营回库订单信息-->
        <select id="getUnDirectBackByStorageOrder" resultMap="BaseResultMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETUNDIRECTBACKBYSTORAGEORDER*/
            <include refid="Base_Column_List"/>
            from hw_equipment_storage_order
            where order_type = 1 and biz_type = 4
            and storage_order = #{storageOrder,jdbcType=VARCHAR}
            limit 1
        </select>

        <!--根据订单号查询直营入库订单信息-->
        <select id="getByStorageOrder" resultMap="InStoreroomDirectOrderInfoMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETBYSTORAGEORDER*/
            a.storage_order,a.create_time,b.username,a.order_num,a.operate_type,a.biz_type
            from hw_equipment_storage_order a
            LEFT JOIN tp_user b on a.agent_id = b.id
        where a.order_type = #{orderType,jdbcType=TINYINT}
        <if test="bizType!=null">
            and a.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="storageOrder!=null and storageOrder!=''">
            and a.storage_order = #{storageOrder,jdbcType=VARCHAR}
        </if>
            limit 1
        </select>

        <!--根据采购单号获得采购信息-->
        <select id="getStorageByOrder" resultMap="BaseResultMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETSTORAGEBYORDER*/
            <include refid="Base_Column_List"/>
            FROM hw_equipment_storage_order
            WHERE storage_order = #{storageOrder,jdbcType=VARCHAR}
        </select>

        <!--订单直营入库及出库查询-->
        <select id="getBackAndShipmentOrderList" resultMap="BaseResultMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETBACKANDSHIPMENTORDERLIST*/
            <include refid="Base_Column_List"/>
            from hw_equipment_storage_order
            where
            execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
            and order_type = #{orderType,jdbcType=INTEGER}
            <if test="orderType != null and orderType == 1">
                and order_status = 3 and biz_type in (2,4)
                and depot in
                <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                    #{typeId,jdbcType=TINYINT}
                </foreach>
            </if>
            <if test="orderType != null and orderType == 2">
                and biz_type in
                <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                    #{typeId,jdbcType=TINYINT}
                </foreach>
            </if>
            and is_del = 0
        </select>

        <!--设备直营入库及出库查询-->
        <select id="getDirectAndShipmentEquipmentList" resultMap="EquipmentSnExportMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETDIRECTANDSHIPMENTEQUIPMENTLIST*/ c.system_sn
            equipment_sn,d.equipment_name,d.equipment_model,a.order_type,a.storage_order,a.depot,a.unit_price,c.depot
            depot_org,a.biz_type
            from hw_equipment_storage_order a
            LEFT JOIN hw_equipment_order_relation b on a.storage_order = b.order_no and b.is_del = 0
            LEFT JOIN hw_equipment_sn c on b.sn_id = c.id and c.is_del = 0
            LEFT JOIN hw_equipment d on c.equipment_id = d.id
            where a.execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
            and a.order_type = #{orderType,jdbcType=INTEGER}
            <if test="orderType != null and orderType == 1">
                and a.order_status = 3 and a.biz_type in (2,4)
            and a.depot in
            <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                #{typeId,jdbcType=TINYINT}
            </foreach>
            </if>
            <if test="orderType != null and orderType == 2">
                and a.biz_type in
                <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                    #{typeId,jdbcType=TINYINT}
                </foreach>
            </if>
            and a.is_del = 0
        </select>

        <!--根据采购订单查询入库设备信息-->
        <select id="getPurchaseOrderEquipmentInfo" resultMap="PurchaseOrderEquipmentInfoMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETPURCHASEORDEREQUIPMENTINFO*/
            a.storage_order,a.equipment_id,a.create_time,b.stock_order,b.depot,d.system_sn
            FROM hw_equipment_storage_order a
            LEFT JOIN hw_equipment_stock b on b.storage_order = a.storage_order
            LEFT JOIN hw_equipment_order_relation c on c.stock_order = b.stock_order
            LEFT JOIN hw_equipment_sn d on d.id = c.sn_id
            where a.storage_order = #{storageOrder,jdbcType=VARCHAR}
            and a.is_del = 0 and c.is_del = 0 and d.is_del = 0
        </select>

        <!--getOutByExecuteTime-->
        <select id="getOutByExecuteTime" resultType="String">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETOUTBYEXECUTETIME*/ esn.init_sn
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment_sn_change_log log on log.sn_id = esn.id and log.execute_time = rage.execute_time
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 2
            and log.handle_type = 11
            and log.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and log.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--getOutByExecuteTimeForObject-->
        <select id="getOutByExecuteTimeForObject" resultMap="InEuipmentSnMap">
            SELECT
            esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,log.depot,log.execute_time,rage.biz_type
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment_sn_change_log log on log.sn_id = esn.id and log.execute_time = rage.execute_time
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 2
            and log.handle_type = 11
            and log.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and log.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--getInByExecuteTime-->
        <select id="getInByExecuteTime" resultType="String">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINBYEXECUTETIME*/ esn.init_sn from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            where rage.order_type = 1
            and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
            and rage.order_status !=1
            and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and esn.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--查询出库设备信息-->
        <select id="getOutByEquipmentId" resultMap="ByEquipmentIdMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETOUTBYEQUIPMENTID*/ rage.biz_type,esn.equipment_id,lation.depot
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
            where
            rage.order_type = 2
            and lation.is_del = 0
            and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            and lation.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--查询入库设备信息-->
        <select id="getInByEquipmentId" resultMap="ByEquipmentIdMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINBYEQUIPMENTID*/ count(*) num,
            rage.biz_type,esn.equipment_id,rage.depot
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
            where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            and rage.order_status !=1
            and rage.order_type = 1
            and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
            GROUP BY rage.depot,esn.equipment_id,rage.biz_type
        </select>

        <!--根据biz_type查询入库设备信息-->
        <select id="getInByBizType" resultMap="ByBizTypeMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINBYBIZTYPE*/ count(*) num,rage.operate_type
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
            where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            and rage.order_type = 1
            and rage.order_status !=1
            and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
            and rage.biz_type = #{bizType,jdbcType=TINYINT}
            and esn.depot = #{depot,jdbcType=TINYINT}
            and esn.equipment_id = #{equipmentId,jdbcType=INTEGER}
            GROUP BY rage.operate_type
        </select>

        <!--获取入库时仓库位置-->
        <select id="getInDepot" resultType="Integer">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINDEPOT*/ rage.depot
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
            where
            esn.init_sn = #{initSn,jdbcType = VARCHAR}
            and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
            and rage.order_type = 1
            ORDER BY rage.execute_time DESC LIMIT 1
        </select>

        <!--根据snList，订单仓库位置查询-->
        <select id="getByOrderDepotSnList" resultMap="ByEquipmentIdMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETBYORDERDEPOTSNLIST*/ count(*) as num ,depot,equipment_id from
            (SELECT init_sn,depot,equipment_id
            from (SELECT esn.init_sn,rage.depot,esn.equipment_id from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
            where rage.order_type = 1
            and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
            and esn.init_sn in
            <if test="snList!=null and snList.size()&gt;0">
                <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                    #{sn,jdbcType=VARCHAR}
                </foreach>
            </if>
            ORDER BY rage.execute_time desc limit 10000) a
            GROUP BY a.init_sn)b group by b.depot,b.equipment_id;
        </select>

        <!--getInByExecuteTimeForStock-->
        <select id="getInByExecuteTimeForStock" resultMap="InEuipmentSnMap">
            SELECT
            esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,stock.depot,stock.execute_time,rage.biz_type
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment_stock stock on stock.stock_order = re.stock_order
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 1
            and rage.biz_type = 1
            and stock.id is not null
            and stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and stock.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--getInByExecuteTimeForBack-->
        <select id="getInByExecuteTimeForBack" resultMap="InEuipmentSnMap">
            SELECT
            esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,rage.depot,rage.execute_time,rage.biz_type
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 1
            and rage.biz_type != 1
            and re.stock_order = ''
            and re.id is not null
            and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and rage.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--getOutSnByExecuteTimeAndLogDepot-->
        <select id="getOutSnByExecuteTimeAndLogDepot" resultMap="InEuipmentSnMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETOUTSNBYEXECUTETIMEANDLOGDEPOT*/ esn.init_sn,la.depot from
            hw_equipment_sn esn
            left join hw_equipment_order_relation la on la.sn_id = esn.id
            left join hw_equipment_storage_order ra on ra.storage_order = la.order_no
            where ra.order_type = 2
            and ra.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and la.is_del = 0
            and la.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--getOutByExecuteTimeAndSn-->
        <select id="getOutByExecuteTimeAndSn" resultMap="InEuipmentSnMap">
            SELECT
            esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,rage.execute_time,rage.biz_type
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 2
            and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and esn.init_sn in
            <foreach collection="list" open="(" close=")" item="sn" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </select>

        <!--getOutByExecuteTimeAndDepot-->
        <select id="getOutByExecuteTimeAndDepot" resultMap="InEuipmentSnMap">
            SELECT
            esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,re.depot,rage.execute_time,rage.biz_type
            from hw_equipment_sn esn
            left join hw_equipment_order_relation re on re.sn_id = esn.id
            left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
            left join hw_equipment e on e.id = esn.equipment_id
            where rage.order_type = 2
            and re.is_del = 0
            and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
            and re.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--查询入库设备信息-->
        <select id="getInByEquipmentIdForBack" resultMap="ByEquipmentInMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINBYEQUIPMENTIDFORBACK*/
            rage.biz_type,esn.equipment_id,rage.depot,rage.operate_type
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
            where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            and rage.order_type = 1
            and rage.biz_type != 1
            and rage.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--查询入库设备信息-->
        <select id="getInByEquipmentIdForStock" resultMap="ByEquipmentInMap">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETINBYEQUIPMENTIDFORSTOCK*/
            rage.biz_type,esn.equipment_id,stock.depot,rage.operate_type
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
            LEFT JOIN hw_equipment_stock stock on stock.storage_order = rage.storage_order and stock.stock_order =
            re.stock_order
            where stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
            and rage.order_type = 1
            and rage.biz_type = 1
            and stock.depot in
            <foreach collection="list" open="(" close=")" item="depot" separator=",">
                #{depot,jdbcType=TINYINT}
            </foreach>
        </select>

        <!--根据snList，订单仓库位置查询回库信息-->
        <select id="getByOrderDepotSnListForBack" resultMap="OrderDepotSnListMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETBYORDERDEPOTSNLISTFORBACK*/
            init_sn,depot,equipment_id,execute_time
            from(SELECT esn.init_sn,rage.depot,esn.equipment_id,rage.execute_time
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
            where rage.order_type = 1
            and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
            and rage.biz_type !=1
            and esn.init_sn in
            <if test="snList!=null and snList.size()&gt;0">
                <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                    #{sn,jdbcType=VARCHAR}
                </foreach>
            </if>
            ORDER BY rage.execute_time desc limit 100000 )a group by init_sn
        </select>

        <!--根据snList，订单仓库位置查询采购信息-->
        <select id="getByOrderDepotSnListForStock" resultMap="OrderDepotSnListMap">
            select /*MS-HW-EQUIPMENT-STORAGE-ORDER-GETBYORDERDEPOTSNLISTFORSTOCK*/
            init_sn,depot,equipment_id,execute_time
            from(SELECT esn.init_sn,rage.depot,esn.equipment_id,rage.execute_time
            from hw_equipment_sn esn
            LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
            LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
            LEFT JOIN hw_equipment_stock stock on stock.storage_order = rage.storage_order and stock.stock_order =
            retion.stock_order
            where rage.order_type = 1
            and rage.biz_type = 1
            and stock.id is not null
            and stock.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
            and esn.init_sn in
            <if test="snList!=null and snList.size()&gt;0">
                <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                    #{sn,jdbcType=VARCHAR}
                </foreach>
            </if>
            ORDER BY stock.execute_time desc limit 100000 )a group by init_sn
        </select>

        <!--查询采购信息-->
        <select id="queryPurchaseInfo"
                resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentStoreRoomModel">
            SELECT /*MS-HW-EQUIPMENT-STORAGE-ORDER-QUERYPURCHASEINFO*/ o.storage_order as storageOrder,e.equipment_name
            as equipmentName,e.equipment_model as equipmentModel,
            o.order_num as orderNum,o.arrival_num as arrivalNum,e.equipment_pic as
            equipmentPic,o.depot,is_union_authentication as isUnionAuthentication
            FROM hw_equipment_storage_order o
            LEFT JOIN hw_equipment e on o.equipment_id = e.id
            WHERE o.order_type = 1
            AND o.biz_type = 1
            AND o.is_del = 0
            AND o.storage_order = #{storageOrder,jdbcType=VARCHAR}
        </select>
    </mapper>
