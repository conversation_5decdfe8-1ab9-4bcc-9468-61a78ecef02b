<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingGoodsCategoryDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingGoodsCategoryDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PRINTER_ID" property="printerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CATEGORY_ID" property="categoryId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GS_CATEGORY_ID" property="gsCategoryId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LABEL_PRINTER_ID" property="labelPrinterId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PARENT_CATEGORY_ID" property="parentCategoryId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="OUT_MERCHANT_ID" property="outMerchantId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="SYSTEM_CATEGORY" property="systemCategory" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`STORE_ID`,`PRINTER_ID`,`CATEGORY_ID`,`CATEGORY_NAME`,`GS_CATEGORY_ID`,`LABEL_PRINTER_ID`,`PARENT_CATEGORY_ID`,`SORT`,`DEL_FLAG`,`OUT_MERCHANT_ID`,`SYSTEM_CATEGORY`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--insert:TP_QRORDERING_GOODS_CATEGORY-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_GOODS_CATEGORY
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="storeId != null">`STORE_ID`,</if>
                <if test="printerId != null">`PRINTER_ID`,</if>
                <if test="categoryId != null">`CATEGORY_ID`,</if>
                <if test="categoryName != null">`CATEGORY_NAME`,</if>
                <if test="parentCategoryId != null">`PARENT_CATEGORY_ID`,</if>
                <if test="sort != null">`SORT`,</if>
                <if test="delFlag != null">`DEL_FLAG`,</if>
                <if test="outMerchantId != null">`OUT_MERCHANT_ID`,</if>
            <if test="systemCategory != null">`SYSTEM_CATEGORY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="printerId != null">#{printerId,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="parentCategoryId != null">#{parentCategoryId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="outMerchantId != null">#{outMerchantId,jdbcType=INTEGER},</if>
            <if test="systemCategory != null">#{systemCategory,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--批量获取非默认分组-->
        <select id="findByCategoryIds" resultMap="BaseResultMap">
            select /*MS-TP-QRORDERING-GOODS-CATEGORY-FINDBYCATEGORYIDS*/ CATEGORY_ID,CATEGORY_NAME from
            TP_QRORDERING_GOODS_CATEGORY
            where
            `CATEGORY_ID` in
            <foreach collection="list" item="categoryId" open="(" close=")" separator=",">
                #{categoryId,jdbcType=VARCHAR}
            </foreach>
            and system_category = 2
            and DEL_FLAG = 0
        </select>
    </mapper>
