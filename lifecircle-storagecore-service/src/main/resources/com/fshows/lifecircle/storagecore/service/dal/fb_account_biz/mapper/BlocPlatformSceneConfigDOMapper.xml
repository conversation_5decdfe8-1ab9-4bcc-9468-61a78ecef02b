<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.BlocPlatformSceneConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.BlocPlatformSceneConfigDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCENE_CODE" property="sceneCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCENE_NAME" property="sceneName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCENE_TYPE" property="sceneType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_SOURCE" property="dataSource" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PARENT_SCENE_CODE" property="parentSceneCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELATED_BILL_CODE" property="relatedBillCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RULE_SWITCH" property="ruleSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="FUND_FLOW_TYPE" property="fundFlowType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PRESET_SCENE" property="isPresetScene" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SERVICE_FEE_SWITCH" property="serviceFeeSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MATCH_BILL_PRODUCT_TYPE" property="matchBillProductType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECONCILIATION_SWITCH" property="reconciliationSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CUSTOM_SERVICE_RATE" property="customServiceRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BLOC_ID`,`SCENE_CODE`,`SCENE_NAME`,`SCENE_TYPE`,`DATA_SOURCE`,`PLATFORM_CODE`,`PARENT_SCENE_CODE`,`RELATED_BILL_CODE`,`RULE_SWITCH`,`FUND_FLOW_TYPE`,`IS_PRESET_SCENE`,`SERVICE_FEE_SWITCH`,`MATCH_BILL_PRODUCT_TYPE`,`RECONCILIATION_SWITCH`,`CREATE_TIME`,`UPDATE_TIME`,`CUSTOM_SERVICE_RATE`
    </sql>


            <!--insert:ACC_BLOC_PLATFORM_SCENE_CONFIG-->
            <insert id="insert" >
            INSERT INTO ACC_BLOC_PLATFORM_SCENE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="customServiceRate != null">`CUSTOM_SERVICE_RATE`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="sceneCode != null">`SCENE_CODE`,</if>
        <if test="sceneName != null">`SCENE_NAME`,</if>
        <if test="sceneType != null">`SCENE_TYPE`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="parentSceneCode != null">`PARENT_SCENE_CODE`,</if>
        <if test="relatedBillCode != null">`RELATED_BILL_CODE`,</if>
        <if test="ruleSwitch != null">`RULE_SWITCH`,</if>
        <if test="fundFlowType != null">`FUND_FLOW_TYPE`,</if>
        <if test="isPresetScene != null">`IS_PRESET_SCENE`,</if>
        <if test="serviceFeeSwitch != null">`SERVICE_FEE_SWITCH`,</if>
        <if test="matchBillProductType != null">`MATCH_BILL_PRODUCT_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="dataSource != null">`DATA_SOURCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="customServiceRate != null">#{customServiceRate,jdbcType=DECIMAL},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="sceneCode != null">#{sceneCode,jdbcType=VARCHAR},</if>
        <if test="sceneName != null">#{sceneName,jdbcType=VARCHAR},</if>
        <if test="sceneType != null">#{sceneType,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="parentSceneCode != null">#{parentSceneCode,jdbcType=VARCHAR},</if>
        <if test="relatedBillCode != null">#{relatedBillCode,jdbcType=VARCHAR},</if>
        <if test="ruleSwitch != null">#{ruleSwitch,jdbcType=TINYINT},</if>
        <if test="fundFlowType != null">#{fundFlowType,jdbcType=TINYINT},</if>
        <if test="isPresetScene != null">#{isPresetScene,jdbcType=TINYINT},</if>
        <if test="serviceFeeSwitch != null">#{serviceFeeSwitch,jdbcType=TINYINT},</if>
        <if test="matchBillProductType != null">#{matchBillProductType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="dataSource != null">#{dataSource,jdbcType=VARCHAR},</if>
    </trim>
            </insert>

            <!--getByBlocIdAndPlatformCode-->
            <select id="getByBlocIdAndPlatformCode" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
            </select>

            <!--getByBlocIdAndPlatformCodeAndSceneType-->
            <select id="getByBlocIdAndPlatformCodeAndSceneType" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
            </select>

            <!--getByParentSceneCode-->
            <select id="getByParentSceneCode" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE PARENT_SCENE_CODE = #{parentSceneCode,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
        AND BLOC_ID = #{blocId,jdbcType=VARCHAR}
            </select>

            <!--getByBlocIdAndPlatformCodeAndSceneTypeAndDataSource-->
            <select id="getByBlocIdAndPlatformCodeAndSceneTypeAndDataSource" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
        AND DATA_SOURCE = #{dataSource,jdbcType=VARCHAR}
            </select>
    </mapper>
