<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.BalanceAccountFreezeRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.BalanceAccountFreezeRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLEMENT_ORDER_SN" property="settlementOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TRADE_DATE" property="tradeDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FREEZE_MONEY" property="freezeMoney" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`REASON`,`ORDER_SN`,`SETTLEMENT_ORDER_SN`,`TYPE`,`IS_SHOW`,`STATUS`,`CHANNEL`,`TRADE_DATE`,`CREATE_TIME`,`UPDATE_TIME`,`FREEZE_MONEY`
    </sql>


            <!--insert:TP_BALANCE_ACCOUNT_FREEZE_RECORD-->
            <insert id="insert" >
            INSERT INTO TP_BALANCE_ACCOUNT_FREEZE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="reason != null">`REASON`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="settlementOrderSn != null">`SETTLEMENT_ORDER_SN`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="isShow != null">`IS_SHOW`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="freezeMoney != null">`FREEZE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="settlementOrderSn != null">#{settlementOrderSn,jdbcType=VARCHAR},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="freezeMoney != null">#{freezeMoney,jdbcType=DECIMAL},</if>
    </trim>
            </insert>
    </mapper>
