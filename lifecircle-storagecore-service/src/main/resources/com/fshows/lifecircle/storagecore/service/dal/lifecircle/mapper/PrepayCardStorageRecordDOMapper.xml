<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardStorageRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardStorageRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELATION_ORDER" property="relationOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECORD_NUMBER" property="recordNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`OPERATE_ID`,`OPERATE_NAME`,`STORAGE_ORDER`,`RELATION_ORDER`,`IS_DEL`,`OPERATE_TYPE`,`RECORD_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_STORAGE_RECORD-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_STORAGE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="relationOrder != null">`RELATION_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="recordNumber != null">`RECORD_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="relationOrder != null">#{relationOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="recordNumber != null">#{recordNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>
    </mapper>
