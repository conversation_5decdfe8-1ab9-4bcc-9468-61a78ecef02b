<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MemberImportFileDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MemberImportFileDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FILE_URL" property="fileUrl" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FILE_NAME" property="fileName" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FILE_TYPE" property="fileType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SOURCE_ID" property="sourceId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="POINT_POWER" property="pointPower" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IMPORT_STATUS" property="importStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTO_ACTIVATION" property="autoActivation" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`FILE_URL`,`ERROR_MSG`,`FILE_NAME`,`STORE_ID`,`FILE_TYPE`,`SOURCE_ID`,`POINT_POWER`,`IMPORT_STATUS`,`AUTO_ACTIVATION`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MEMBER_IMPORT_FILE-->
            <insert id="insert" >
                <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
        SELECT
        LAST_INSERT_ID()
    </selectKey>
INSERT INTO TP_MEMBER_IMPORT_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="fileUrl != null">`FILE_URL`,</if>
        <if test="fileName != null">`FILE_NAME`,</if>
        <if test="fileType != null">`FILE_TYPE`,</if>
        <if test="sourceId != null">`SOURCE_ID`,</if>
        <if test="importStatus != null">`IMPORT_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="autoActivation != null">`AUTO_ACTIVATION`,</if>
        <if test="pointPower != null">`POINT_POWER`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="fileUrl != null">#{fileUrl,jdbcType=LONGVARCHAR},</if>
        <if test="fileName != null">#{fileName,jdbcType=LONGVARCHAR},</if>
        <if test="fileType != null">#{fileType,jdbcType=TINYINT},</if>
        <if test="sourceId != null">#{sourceId,jdbcType=INTEGER},</if>
        <if test="importStatus != null">#{importStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="autoActivation != null">#{autoActivation,jdbcType=TINYINT},</if>
        <if test="pointPower != null">#{pointPower,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
    </trim>
            </insert>

            <!--根据id更新导入记录状态及失败原因-->
            <update id="updateDataById" >
                    UPDATE /*MS-TP-MEMBER-IMPORT-FILE-UPDATEDATABYID*/ TP_MEMBER_IMPORT_FILE
        SET
        import_status = #{importStatus,jdbcType=INTEGER},
        error_msg = #{errorMsg,jdbcType=VARCHAR}
        WHERE
        ID = #{id,jdbcType=INTEGER}
            </update>
    </mapper>
