<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.MarketLifecircleStoreDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.LifecircleStoreDO">

        <result column="STORE_LAT" property="storeLat" jdbcType="REAL"
                javaType="Float"/>

        <result column="STORE_LNG" property="storeLng" jdbcType="REAL"
                javaType="Float"/>

        <result column="KEY" property="key" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TEL" property="tel" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CITY" property="city" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="NOTE" property="note" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="COUNTY" property="county" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ADDRESS" property="address" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="END_TIME" property="endTime" jdbcType="CHAR"
                javaType="String"/>

        <result column="OPERATE" property="operate" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="SERVICE" property="service" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PROVINCE" property="province" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OBJECTION" property="objection" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="RECOMMEND" property="recommend" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="START_TIME" property="startTime" jdbcType="CHAR"
                javaType="String"/>

        <result column="STORE_AREA" property="storeArea" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_LOGO" property="storeLogo" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DENIAL_TYPE" property="denialType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_BRIEF" property="storeBrief" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_IMAGE" property="storeImage" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="EXAMINE_JSON" property="examineJson" jdbcType="LONGVARCHAR"
                javaType="String"/>

        <result column="SERVICE_MORE" property="serviceMore" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LICENSE_NUMBER" property="licenseNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MAIN_STORE_INFO" property="mainStoreInfo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REC_ID" property="recId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_HORN" property="isHorn" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="AVG_PRICE" property="avgPrice" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_ONLINE" property="isOnline" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="JOIN_TIME" property="joinTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PRINT_NUM" property="printNum" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AUTOPRINT" property="autoprint" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="BIND_LOG_ID" property="bindLogId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PRINT_CODE" property="printCode" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CATEGORYID" property="categoryid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_PROMOTED" property="isPromoted" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="UNITY_CAT_ID" property="unityCatId" jdbcType="SMALLINT"
                javaType="Integer"/>

        <result column="EXAMINE_TIME" property="examineTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="JOIN_CHANNEL" property="joinChannel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PRINTER_TYPE" property="printerType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_STATUS" property="storeStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_BIND_KOUBEI" property="isBindKoubei" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_TAKEPARTIN" property="isTakepartin" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="TAKING_ORDERS" property="takingOrders" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="EXAMINE_STATUS" property="examineStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STORE_BELONG_ID" property="storeBelongId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="DISPLAY_COMMENT" property="displayComment" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="INDEX_RECOMMEND" property="indexRecommend" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PREFERENTIAL_ID" property="preferentialId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_HORN_CANCEL_PAY" property="isHornCancelPay" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="TAKING_ORDERS_TYPE" property="takingOrdersType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>

    <resultMap id="storeAuditResult"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.StoreAuditResult">

        <result column="belong" property="belong" javaType="java.lang.String"/>

        <result column="sales_man" property="salesMan" javaType="java.lang.String"/>

        <result column="username" property="username" javaType="java.lang.String"/>

        <result column="objection" property="objection" javaType="java.lang.String"/>

        <result column="store_name" property="storeName" javaType="java.lang.String"/>

        <result column="examine_json" property="examineJson" javaType="java.lang.String"/>

        <result column="store_objection" property="storeObjection" javaType="java.lang.String"/>

        <result column="completion_error_msg" property="completionErrorMsg" javaType="java.lang.String"/>

        <result column="store_id" property="storeId" javaType="java.lang.Integer"/>

        <result column="belong_id" property="belongId" javaType="java.lang.Integer"/>

        <result column="is_online" property="isOnline" javaType="java.lang.Integer"/>

        <result column="audit_time" property="auditTime" javaType="java.lang.Integer"/>

        <result column="sales_man_id" property="salesManId" javaType="java.lang.Integer"/>

        <result column="update_time" property="updateTime" javaType="java.lang.Integer"/>

        <result column="join_channel" property="joinChannel" javaType="java.lang.Integer"/>

        <result column="store_status" property="storeStatus" javaType="java.lang.Integer"/>

        <result column="examine_status" property="examineStatus" javaType="java.lang.Integer"/>

        <result column="store_examine_time" property="storeExamineTime" javaType="java.lang.Integer"/>
    </resultMap>

    <sql id="Base_Column_List">
    `STORE_LAT`,`STORE_LNG`,`KEY`,`TEL`,`CITY`,`NOTE`,`TOKEN`,`COUNTY`,`ADDRESS`,`END_TIME`,`OPERATE`,`SERVICE`,`DEVICE_NO`,`PROVINCE`,`BRAND_NAME`,`OBJECTION`,`RECOMMEND`,`START_TIME`,`STORE_AREA`,`STORE_LOGO`,`STORE_NAME`,`BRANCH_NAME`,`DENIAL_TYPE`,`STORE_BRIEF`,`STORE_IMAGE`,`EXAMINE_JSON`,`SERVICE_MORE`,`LICENSE_NUMBER`,`MAIN_STORE_INFO`,`REC_ID`,`IS_HORN`,`IS_SHOW`,`AGENT_ID`,`STORE_ID`,`AVG_PRICE`,`IS_ONLINE`,`JOIN_TIME`,`PRINT_NUM`,`AUTOPRINT`,`BIND_LOG_ID`,`PRINT_CODE`,`CATEGORYID`,`CREATE_TIME`,`IS_PROMOTED`,`UNITY_CAT_ID`,`EXAMINE_TIME`,`JOIN_CHANNEL`,`PRINTER_TYPE`,`STORE_STATUS`,`IS_BIND_KOUBEI`,`IS_TAKEPARTIN`,`TAKING_ORDERS`,`EXAMINE_STATUS`,`STORE_BELONG_ID`,`DISPLAY_COMMENT`,`INDEX_RECOMMEND`,`PREFERENTIAL_ID`,`IS_HORN_CANCEL_PAY`,`TAKING_ORDERS_TYPE`,`UPDATE_TIME`
    </sql>


    <!--门店前置审核列表 pageCount-->
    <select id="getFrontListCount" resultType="int">
        SELECT
        COUNT(*) AS total
        FROM
        tp_lifecircle_store AS a
        <if test="startDate!=null and endDate!=null">
            force index(IDX_CREATE_TIME)
        </if>
        LEFT JOIN tp_wxuser AS b ON a.token = b.token
        LEFT JOIN tp_users AS c ON b.uid = c.id
        LEFT JOIN tp_user AS d ON d.id = c.belong
        LEFT JOIN tp_user AS e ON e.id = c.salesman
        LEFT JOIN tp_store_completion AS completion ON a.store_id=completion.store_id
        LEFT JOIN tp_fastpush_store fp on fp.store_id=a.store_id
        left join tp_lifecircle_account account on account.uid = c.id
        left join tp_lifecircle_bind_bank bank on bank.uid = c.id
        WHERE
        fp.store_id is null
        and account.auth_status = 2
        and bank.bind_status = 1
        and bank.is_del = 0
        AND d.own_run=#{ownRun,jdbcType=INTEGER}
        AND c.sub_config_id=0
        <if test="merchantName !=null and merchantName != ''">
            AND c.username LIKE CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="storeName !=null and storeName != ''">
            AND a.store_name LIKE CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="salesmanName !=null and salesmanName != ''">
            AND e.username LIKE CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName !=null and belongName != ''">
            AND d.username LIKE CONCAT(#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="isOpen !=null">
            AND a.is_online = #{isOpen,jdbcType=INTEGER}
        </if>
        <if test="liquidatorStatusList != null and liquidatorStatusList.size()&gt;0">
            and a.examine_status in
            <foreach close=")" collection="liquidatorStatusList" index="index" item="liquidatorStatus" open="("
                     separator=",">
                #{liquidatorStatus,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storeStatus!=null and storeStatus==0">
            AND (
            a.store_status = 0
            or (a.store_status=5 and a.examine_status=2)
            or (a.store_status=5 and a.examine_status=0)
            )
        </if>
        <if test="storeStatus!=null and storeStatus==5">
            AND a.store_status = 5
            AND a.examine_status = 1
        </if>
        <if test="storeStatus!=null and storeStatus!=0 and storeStatus!=5">
            AND a.store_status = #{storeStatus,jdbcType=INTEGER}
        </if>
        <if test="joinChannelList != null and joinChannelList.size()&gt;0">
            and a.join_channel in
            <foreach close=")" collection="joinChannelList" index="index" item="joinChannel" open="(" separator=",">
                #{joinChannel,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="startDate!=null and endDate!=null">
            AND a.create_time BETWEEN #{startDate,jdbcType=INTEGER} AND #{endDate,jdbcType=INTEGER}
        </if>
        <if test="audtiStartDate!=null and audtiEndDate!=null">
            AND a.examine_time BETWEEN #{audtiStartDate,jdbcType=INTEGER} AND #{audtiEndDate,jdbcType=INTEGER}
        </if>
        <if test="isBlackList!=null and isBlackList==1 and blackList!=null and blackList.size()&gt;0">
            AND (1=#{isBlackList,jdbcType=TINYINT} and c.belong not IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            OR c.salesman not IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            )
        </if>
        <if test="isBlackList!=null and isBlackList==2 and blackList!=null and blackList.size()&gt;0">
            AND (2=#{isBlackList,jdbcType=TINYINT} and c.belong IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            OR c.salesman IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            )
        </if>
        <if test="isSpecial != null and isSpecial == 1">
            and not exists (
            select #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token and status = 1
            )
        </if>
        <if test="isSpecial != null and isSpecial == 2 and auditUserId != null">
            and exists (
            select #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token
            and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            and status = 1
            )
        </if>

    </select>
    <!--门店前置审核列表 pageResult-->
    <select id="getFrontListResult" resultMap="storeAuditResult">
        SELECT
        a.create_time AS update_time,
        c.username,
        a.store_name,
        e.username AS sales_man,
        d.username AS belong,
        a.join_channel,
        a.examine_status,
        a.store_status,
        a.examine_json,
        a.is_online,
        a.store_id,
        c.salesman AS sales_man_id,
        c.belong AS belong_id,
        completion.error_msg as completion_error_msg,
        a.objection,
        a.examine_time as audit_time
        FROM
        tp_lifecircle_store AS a
        <if test="startDate!=null and endDate!=null">
            force index(IDX_CREATE_TIME)
        </if>
        LEFT JOIN tp_wxuser AS b ON a.token = b.token
        LEFT JOIN tp_users AS c ON b.uid = c.id
        LEFT JOIN tp_user AS d ON d.id = c.belong
        LEFT JOIN tp_user AS e ON e.id = c.salesman
        LEFT JOIN tp_store_completion AS completion ON a.store_id=completion.store_id
        LEFT JOIN tp_fastpush_store fp on fp.store_id=a.store_id
        left join tp_lifecircle_account account on account.uid = c.id
        left join tp_lifecircle_bind_bank bank on bank.uid = c.id
        WHERE
        fp.store_id is null
        and account.auth_status = 2
        and bank.bind_status = 1
        and bank.is_del = 0
        AND d.own_run=#{ownRun,jdbcType=INTEGER}
        AND c.sub_config_id=0
        <if test="merchantName !=null and merchantName != ''">
            AND c.username LIKE CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="storeName !=null and storeName != ''">
            AND a.store_name LIKE CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="salesmanName !=null and salesmanName != ''">
            AND e.username LIKE CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName !=null and belongName != ''">
            AND d.username LIKE CONCAT(#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="isOpen !=null">
            AND a.is_online = #{isOpen,jdbcType=INTEGER}
        </if>
        <if test="liquidatorStatusList != null and liquidatorStatusList.size()&gt;0">
            and a.examine_status in
            <foreach close=")" collection="liquidatorStatusList" index="index" item="liquidatorStatus" open="("
                     separator=",">
                #{liquidatorStatus,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storeStatus!=null and storeStatus==0">
            AND (
            a.store_status = 0
            or (a.store_status=5 and a.examine_status=2)
            or (a.store_status=5 and a.examine_status=0)
            )
        </if>
        <if test="storeStatus!=null and storeStatus==5">
            AND a.store_status = 5
            AND a.examine_status = 1
        </if>
        <if test="storeStatus!=null and storeStatus!=0 and storeStatus!=5">
            AND a.store_status = #{storeStatus,jdbcType=INTEGER}
        </if>
        <if test="joinChannelList != null and joinChannelList.size()&gt;0">
            and a.join_channel in
            <foreach close=")" collection="joinChannelList" index="index" item="joinChannel" open="(" separator=",">
                #{joinChannel,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="startDate!=null and endDate!=null">
            AND a.create_time BETWEEN #{startDate,jdbcType=INTEGER} AND #{endDate,jdbcType=INTEGER}
        </if>
        <if test="audtiStartDate!=null and audtiEndDate!=null">
            AND a.examine_time BETWEEN #{audtiStartDate,jdbcType=INTEGER} AND #{audtiEndDate,jdbcType=INTEGER}
        </if>
        <if test="isBlackList!=null and isBlackList==1 and blackList!=null and blackList.size()&gt;0">
            AND (1=#{isBlackList,jdbcType=TINYINT} and c.belong not IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            OR c.salesman not IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            )
        </if>
        <if test="isBlackList!=null and isBlackList==2 and blackList!=null and blackList.size()&gt;0">
            AND (2=#{isBlackList,jdbcType=TINYINT} and c.belong IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            OR c.salesman IN
            <foreach close=")" collection="blackList" index="index" item="black" open="(" separator=",">
                #{black,jdbcType=TINYINT}
            </foreach>
            )
        </if>
        <if test="isSpecial != null and isSpecial == 1">
            and not exists (
            select /*MS-TP-LIFECIRCLE-STORE-GETFRONTLIST*/ #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token and status = 1
            )
        </if>
        <if test="isSpecial != null and isSpecial == 2 and auditUserId != null">
            and exists (
            select /*MS-TP-LIFECIRCLE-STORE-GETFRONTLIST*/ #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token
            and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            and status = 1
            )
        </if>
        order by a.create_time desc,a.store_id desc
        limit #{startRow},#{limit}
    </select>

    <!--门店后置审核列表 pageCount-->
    <select id="getBehindListCount" resultType="int">
        SELECT
        COUNT(*) AS total
        FROM
        tp_fastpush_store fp
        <if test="startDate!=null and endDate!=null">
            force index(idx_authtime)
        </if>
        LEFT JOIN tp_users AS c ON c.id = fp.uid
        LEFT JOIN tp_lifecircle_store a ON a.store_id = fp.store_id
        LEFT JOIN tp_user AS d ON c.belong = d.id
        LEFT JOIN tp_user AS e ON c.salesman = e.id
        LEFT JOIN tp_store_completion AS completion ON a.store_id=completion.store_id
        left join tp_lifecircle_account account on account.uid = c.id
        left join tp_lifecircle_bind_bank bank on bank.uid = c.id
        WHERE
        d.own_run=#{ownRun,jdbcType=INTEGER}
        and account.auth_status = 2
        and bank.bind_status = 1
        and bank.is_del = 0
        and a.store_id is not null
        <if test="merchantName !=null and merchantName != ''">
            AND c.username LIKE CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="storeName !=null and storeName != ''">
            AND a.store_name LIKE CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="salesmanName !=null and salesmanName != ''">
            AND e.username LIKE CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName !=null and belongName != ''">
            AND d.username LIKE CONCAT(#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="isOpen !=null">
            AND a.is_online = #{isOpen,jdbcType=INTEGER}
        </if>
        <if test="liquidatorStatusList != null and liquidatorStatusList.size()&gt;0">
            and a.examine_status in
            <foreach close=")" collection="liquidatorStatusList" index="index" item="liquidatorStatus" open="("
                     separator=",">
                #{liquidatorStatus,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storeStatus!=null">
            AND a.store_status = #{storeStatus,jdbcType=INTEGER}
        </if>
        <if test="joinChannelList != null and joinChannelList.size()&gt;0">
            and a.join_channel in
            <foreach close=")" collection="joinChannelList" index="index" item="joinChannel" open="(" separator=",">
                #{joinChannel,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="startDate!=null and endDate!=null">
            AND fp.audit_time BETWEEN #{startDate,jdbcType=INTEGER} AND #{endDate,jdbcType=INTEGER}
        </if>
        <if test="audtiStartDate!=null and audtiEndDate!=null">
            AND fp.verify_time BETWEEN #{audtiStartDate,jdbcType=INTEGER} AND #{audtiEndDate,jdbcType=INTEGER}
        </if>
        <if test="isSpecial != null and isSpecial == 1">
            and not exists (
            select #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token and status = 1
            )
        </if>
        <if test="isSpecial != null and isSpecial == 2 and auditUserId != null">
            and exists (
            select #{isSpecial,jdbcType=INTEGER} from lm_crm_store_special_audit
            where token = a.token
            and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            and status = 1
            )
        </if>

    </select>
    <!--门店后置审核列表 pageResult-->
    <select id="getBehindListResult" resultMap="storeAuditResult">
        SELECT
        fp.audit_time AS update_time,
        c.username,
        a.store_name AS store_name,
        e.username AS sales_man,
        d.username AS belong,
        a.join_channel,
        a.examine_status,
        a.store_status,
        a.examine_json,
        a.is_online,
        fp.store_id,
        c.salesman AS sales_man_id,
        c.belong AS belong_id,
        completion.error_msg as completion_error_msg,
        fp.reason AS objection,
        fp.verify_time as audit_time,
        a.objection as store_objection,
        a.examine_time as store_examine_time
        FROM
        tp_fastpush_store fp
        <if test="startDate!=null and endDate!=null">
            force index(idx_authtime)
        </if>
        LEFT JOIN tp_users AS c ON c.id = fp.uid
        LEFT JOIN tp_lifecircle_store a ON a.store_id = fp.store_id
        LEFT JOIN tp_user AS d ON c.belong = d.id
        LEFT JOIN tp_user AS e ON c.salesman = e.id
        LEFT JOIN tp_store_completion AS completion ON a.store_id=completion.store_id
        left join tp_lifecircle_account account on account.uid = c.id
        left join tp_lifecircle_bind_bank bank on bank.uid = c.id
        WHERE
        d.own_run=#{ownRun,jdbcType=INTEGER}
        and account.auth_status = 2
        and bank.bind_status = 1
        and bank.is_del = 0
        and a.store_id is not null
        <if test="merchantName !=null and merchantName != ''">
            AND c.username LIKE CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="storeName !=null and storeName != ''">
            AND a.store_name LIKE CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="salesmanName !=null and salesmanName != ''">
            AND e.username LIKE CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName !=null and belongName != ''">
            AND d.username LIKE CONCAT(#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="isOpen !=null">
            AND a.is_online = #{isOpen,jdbcType=INTEGER}
        </if>
        <if test="liquidatorStatusList != null and liquidatorStatusList.size()&gt;0">
            and a.examine_status in
            <foreach close=")" collection="liquidatorStatusList" index="index" item="liquidatorStatus" open="("
                     separator=",">
                #{liquidatorStatus,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storeStatus!=null">
            AND a.store_status = #{storeStatus,jdbcType=INTEGER}
        </if>
        <if test="joinChannelList != null and joinChannelList.size()&gt;0">
            and a.join_channel in
            <foreach close=")" collection="joinChannelList" index="index" item="joinChannel" open="(" separator=",">
                #{joinChannel,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="startDate!=null and endDate!=null">
            AND fp.audit_time BETWEEN #{startDate,jdbcType=INTEGER} AND #{endDate,jdbcType=INTEGER}
        </if>
        <if test="audtiStartDate!=null and audtiEndDate!=null">
            AND fp.verify_time BETWEEN #{audtiStartDate,jdbcType=INTEGER} AND #{audtiEndDate,jdbcType=INTEGER}
        </if>
        <if test="isSpecial != null and isSpecial == 1">
            and not exists (
            select /*MS-TP-LIFECIRCLE-STORE-GETBEHINDLIST*/ #{isSpecial,jdbcType=INTEGER} from
            lm_crm_store_special_audit
            where token = a.token and status = 1
            )
        </if>
        <if test="isSpecial != null and isSpecial == 2 and auditUserId != null">
            and exists (
            select /*MS-TP-LIFECIRCLE-STORE-GETBEHINDLIST*/ #{isSpecial,jdbcType=INTEGER} from
            lm_crm_store_special_audit
            where token = a.token
            and audit_user_id = #{auditUserId,jdbcType=VARCHAR}
            and status = 1
            )
        </if>
        order by fp.audit_time desc,fp.id desc
        limit #{startRow},#{limit}
    </select>
</mapper>
