<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingGoodsDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingGoodsDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="NAME" property="name" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UNIT" property="unit" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GS_SPU_ID" property="gsSpuId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LABEL_ID" property="labelId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PICTURE" property="picture" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CATEGORY_ID" property="categoryId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DESCRIPTION" property="description" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PICTURE_EXTEND" property="pictureExtend" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="GOODS_TEMPLATE_ID" property="goodsTemplateId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="HOT_SALE" property="hotSale" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="STOCK_NUM" property="stockNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="RESET_STOCK_NUM" property="resetStockNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="RESET_STOCK_FLAG" property="resetStockFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="PRICE" property="price" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="ORIGINAL_PRICE" property="originalPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`NAME`,`UNIT`,`GOODS_ID`,`GS_SPU_ID`,`LABEL_ID`,`PICTURE`,`STORE_ID`,`CATEGORY_ID`,`DESCRIPTION`,`PICTURE_EXTEND`,`GOODS_TEMPLATE_ID`,`SORT`,`STATUS`,`DEL_FLAG`,`HOT_SALE`,`STOCK_NUM`,`RESET_STOCK_NUM`,`RESET_STOCK_FLAG`,`CREATE_TIME`,`UPDATE_TIME`,`PRICE`,`ORIGINAL_PRICE`
    </sql>


        <!--insert:TP_QRORDERING_GOODS-->
        <insert id="insert">
            INSERT INTO TP_QRORDERING_GOODS
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="name != null">`NAME`,</if>
                <if test="unit != null">`UNIT`,</if>
                <if test="goodsId != null">`GOODS_ID`,</if>
                <if test="labelId != null">`LABEL_ID`,</if>
                <if test="picture != null">`PICTURE`,</if>
                <if test="storeId != null">`STORE_ID`,</if>
                <if test="categoryId != null">`CATEGORY_ID`,</if>
                <if test="description != null">`DESCRIPTION`,</if>
            <if test="goodsTemplateId != null">`GOODS_TEMPLATE_ID`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="hotSale != null">`HOT_SALE`,</if>
            <if test="stockNum != null">`STOCK_NUM`,</if>
            <if test="resetStockNum != null">`RESET_STOCK_NUM`,</if>
            <if test="resetStockFlag != null">`RESET_STOCK_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="price != null">`PRICE`,</if>
            <if test="originalPrice != null">`ORIGINAL_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="unit != null">#{unit,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="labelId != null">#{labelId,jdbcType=VARCHAR},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="goodsTemplateId != null">#{goodsTemplateId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="hotSale != null">#{hotSale,jdbcType=TINYINT},</if>
            <if test="stockNum != null">#{stockNum,jdbcType=INTEGER},</if>
            <if test="resetStockNum != null">#{resetStockNum,jdbcType=INTEGER},</if>
            <if test="resetStockFlag != null">#{resetStockFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="price != null">#{price,jdbcType=DECIMAL},</if>
            <if test="originalPrice != null">#{originalPrice,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--通过商品id列表批量获取商品信息-->
        <select id="findByGoodsIds" resultMap="BaseResultMap">
            SELECT
            goods_id,
            name,
            price,
            unit,
            category_id
            FROM tp_qrordering_goods
            WHERE goods_id IN
            <foreach collection="list" item="goodsId" separator="," open="(" close=")">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
            AND del_flag = 0
        </select>
    </mapper>
