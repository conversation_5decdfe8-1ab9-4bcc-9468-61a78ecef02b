<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.RefundTaskDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.RefundTaskDetailDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_NO" property="tradeNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ERROR_MSG" property="errorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_AMOUNT" property="refundAmount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_TASK_ID" property="refundTaskId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRE_CHECK_STATUS" property="preCheckStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_TASK_DETAIL_ID" property="refundTaskDetailId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TASK_DATE" property="taskDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORDER_SN`,`TRADE_NO`,`ERROR_MSG`,`REFUND_SN`,`APPLY_STATUS`,`REFUND_AMOUNT`,`REFUND_TASK_ID`,`PRE_CHECK_STATUS`,`REFUND_TASK_DETAIL_ID`,`UID`,`TASK_DATE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_REFUND_TASK_DETAIL-->
            <insert id="insert" >
                    INSERT INTO TP_REFUND_TASK_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="refundSn != null">`REFUND_SN`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="refundAmount != null">`REFUND_AMOUNT`,</if>
            <if test="refundTaskId != null">`REFUND_TASK_ID`,</if>
            <if test="preCheckStatus != null">`PRE_CHECK_STATUS`,</if>
            <if test="refundTaskDetailId != null">`REFUND_TASK_DETAIL_ID`,</if>
            <if test="taskDate != null">`TASK_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=VARCHAR},</if>
            <if test="refundAmount != null">#{refundAmount,jdbcType=VARCHAR},</if>
            <if test="refundTaskId != null">#{refundTaskId,jdbcType=VARCHAR},</if>
            <if test="preCheckStatus != null">#{preCheckStatus,jdbcType=VARCHAR},</if>
            <if test="refundTaskDetailId != null">#{refundTaskDetailId,jdbcType=VARCHAR},</if>
            <if test="taskDate != null">#{taskDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--批量退款明细查询-->
            <select id="findByTaskId" resultMap="BaseResultMap">
                    select /*MS-TP-REFUND-TASK-DETAIL-FINDBYTASKID*/ <include refid="Base_Column_List" /> from TP_REFUND_TASK_DETAIL
        where `REFUND_TASK_ID` = #{refundTaskId,jdbcType=VARCHAR}
        <if test="preCheckStatusList != null">and `PRE_CHECK_STATUS` in
            <foreach collection="preCheckStatusList" separator="," item="item"   open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="applyStatusList != null">and `APPLY_STATUS` in
            <foreach collection="applyStatusList" separator="," item="item1"   open="(" close=")">
                #{item1}
            </foreach>
        </if>
            </select>
    </mapper>
