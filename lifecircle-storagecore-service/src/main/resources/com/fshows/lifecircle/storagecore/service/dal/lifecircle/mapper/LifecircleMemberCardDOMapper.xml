<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleMemberCardDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleMemberCardDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PASSWORD" property="password" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TPL_ID" property="tplId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_TYPE" property="cardType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SHOW_AMOUNT" property="isShowAmount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECOMMEND_UID" property="recommendUid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ACTIVATION_TIME" property="activationTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MEMBER_FROM_TYPE" property="memberFromType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LAST_CONSUME_TIME" property="lastConsumeTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MEMBER_ACTIVE_CHANNEL" property="memberActiveChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECHARGE" property="recharge" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="TOTAL_AMOUNT" property="totalAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`CARD_NO`,`PASSWORD`,`UID`,`TPL_ID`,`STATUS`,`USER_ID`,`STORE_ID`,`CARD_TYPE`,`CREATE_TIME`,`IS_SHOW_AMOUNT`,`RECOMMEND_UID`,`ACTIVATION_TIME`,`MEMBER_FROM_TYPE`,`LAST_CONSUME_TIME`,`MEMBER_ACTIVE_CHANNEL`,`RECHARGE`,`TOTAL_AMOUNT`
    </sql>


            <!--insert:TP_LIFECIRCLE_MEMBER_CARD-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_MEMBER_CARD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="tplId != null">`TPL_ID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="isShowAmount != null">`IS_SHOW_AMOUNT`,</if>
        <if test="recommendUid != null">`RECOMMEND_UID`,</if>
        <if test="activationTime != null">`ACTIVATION_TIME`,</if>
        <if test="lastConsumeTime != null">`LAST_CONSUME_TIME`,</if>
        <if test="recharge != null">`RECHARGE`,</if>
        <if test="totalAmount != null">`TOTAL_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="tplId != null">#{tplId,jdbcType=INTEGER},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="isShowAmount != null">#{isShowAmount,jdbcType=TINYINT},</if>
        <if test="recommendUid != null">#{recommendUid,jdbcType=INTEGER},</if>
        <if test="activationTime != null">#{activationTime,jdbcType=INTEGER},</if>
        <if test="lastConsumeTime != null">#{lastConsumeTime,jdbcType=INTEGER},</if>
        <if test="recharge != null">#{recharge,jdbcType=DECIMAL},</if>
        <if test="totalAmount != null">#{totalAmount,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--查询用户在某个商家已激活的会员卡-->
            <select id="countByUserIdsAndToken" resultType="Integer">
                    SELECT
        count(1)
        FROM
        TP_LIFECIRCLE_MEMBER_CARD
        WHERE
        token = #{token,jdbcType=VARCHAR} and
        card_type = 1 and
        status = 1 and
        user_id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId, jdbcType=INTEGER}
        </foreach>
        )
            </select>
    </mapper>
