<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentTransferBlackListDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentTransferBlackListDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`AGENT_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_TRANSFER_BLACK_LIST-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_TRANSFER_BLACK_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据id更新归属代理商-->
            <update id="updateAgentById" >
                    update /*MS-HW-EQUIPMENT-TRANSFER-BLACK-LIST-UPDATEAGENTBYID*/ HW_EQUIPMENT_TRANSFER_BLACK_LIST set agent_id = #{agentId,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
            </update>

            <!--根据设备sn查询-->
            <select id="getByInitSn" resultMap="BaseResultMap">
                    select /*MS-HW-EQUIPMENT-TRANSFER-BLACK-LIST-GETBYINITSN*/ <include refid="Base_Column_List" /> from HW_EQUIPMENT_TRANSFER_BLACK_LIST where init_sn = #{nitSn,jdbcType=VARCHAR} limit 1
            </select>
    </mapper>
