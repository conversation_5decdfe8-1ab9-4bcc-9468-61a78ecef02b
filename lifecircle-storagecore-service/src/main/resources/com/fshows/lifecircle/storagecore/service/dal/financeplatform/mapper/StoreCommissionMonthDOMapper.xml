<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.financeplatform.mapper.StoreCommissionMonthDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.dataobject.StoreCommissionMonthDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="FEE_CODE" property="feeCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_TYPE" property="merchantType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_MANAGER" property="marketManager" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_USERNAME" property="storeUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_COMPANYNAME" property="agentCompanyname" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPER_SALESMAN_USERNAME" property="superSalesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUSINESS_DATE" property="businessDate" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_ID" property="superSalesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NORMAL_TRADE_COUNT" property="normalTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_TRADE_COUNT" property="refundTradeCount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="NORMAL_TRADE_AMOUNT" property="normalTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_TRADE_AMOUNT" property="refundTradeAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SETTLEMENT_COEFFICIENT" property="settlementCoefficient" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ACTUAL_PAYABLE_COMMISSION" property="actualPayableCommission" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="OpenAccountCommissionPayableDetailMap" type="com.fshows.lifecircle.storagecore.service.dal.financeplatform.resultmap.OpenAccountCommissionPayableDetailMap">

                <result column="salesman_name" property="salesmanName" javaType="java.lang.String"/>

                <result column="salesman_id" property="salesmanId" javaType="java.lang.Integer"/>

                <result column="normal_trade_count" property="normalTradeCount" javaType="java.lang.Integer"/>

                <result column="normal_trade_amount" property="normalTradeAmount" javaType="java.math.BigDecimal"/>

                <result column="settlement_coefficient" property="settlementCoefficient" javaType="java.math.BigDecimal"/>

                <result column="actual_payable_commission" property="actualPayableCommission" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`FEE_CODE`,`SOURCE_TYPE`,`MERCHANT_TYPE`,`AGENT_USERNAME`,`MARKET_MANAGER`,`STORE_USERNAME`,`AGENT_COMPANYNAME`,`MERCHANT_USERNAME`,`SALESMAN_USERNAME`,`SUPER_SALESMAN_USERNAME`,`UID`,`AGENT_ID`,`PT_MONTH`,`STORE_ID`,`MARKET_ID`,`SALESMAN_ID`,`BUSINESS_DATE`,`SUPER_SALESMAN_ID`,`NORMAL_TRADE_COUNT`,`REFUND_TRADE_COUNT`,`CREATE_TIME`,`UPDATE_TIME`,`NORMAL_TRADE_AMOUNT`,`REFUND_TRADE_AMOUNT`,`SETTLEMENT_COEFFICIENT`,`ACTUAL_PAYABLE_COMMISSION`
    </sql>


            <!--insert:FP_STORE_COMMISSION_MONTH-->
            <insert id="insert" >
                    INSERT INTO FP_STORE_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="marketManager != null">`MARKET_MANAGER`,</if>
            <if test="storeUsername != null">`STORE_USERNAME`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="merchantUsername != null">`MERCHANT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="superSalesmanUsername != null">`SUPER_SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="settlementCoefficient != null">`SETTLEMENT_COEFFICIENT`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="marketManager != null">#{marketManager,jdbcType=VARCHAR},</if>
            <if test="storeUsername != null">#{storeUsername,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="merchantUsername != null">#{merchantUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="superSalesmanUsername != null">#{superSalesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="settlementCoefficient != null">#{settlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据代理商id查询商户月佣金列表-->
            <select id="getStoreCommissionListPage" resultMap="BaseResultMap">
                    select /*MS-FP-STORE-COMMISSION-MONTH-GETSTORECOMMISSIONLISTPAGE*/ <include refid="Base_Column_List" /> from FP_STORE_COMMISSION_MONTH
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
            </select>

            <!--根据代理商id查询代理商月佣金列表-->
            <select id="getAgentCommissionList" resultMap="OpenAccountCommissionPayableDetailMap">
                    select
        agent_id salesman_id,
        agent_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
            </select>

            <!--根据代理商id查询超级授理商月佣金列表-->
            <select id="getSuperSalesmanCommissionList" resultMap="OpenAccountCommissionPayableDetailMap">
                    select
        super_salesman_id salesman_id,
        super_salesman_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
            </select>

            <!--根据代理商id查询授理商月佣金列表-->
            <select id="getSalesmanCommissionList" resultMap="OpenAccountCommissionPayableDetailMap">
                    select
        salesman_id salesman_id,
        salesman_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
            </select>
    </mapper>
