<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.mapper.LifecircleoperationDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 8 as fg,'TP_MERCHANT_MARKET_STORE_QRCODE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TOKEN,QRCODE_URL,TEMPLATE_QRCODE_URL,UID,STORE_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MERCHANT_MARKET_STORE_QRCODE'
            AND COLUMN_NAME in('ID','TOKEN','QRCODE_URL','TEMPLATE_QRCODE_URL','UID','STORE_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
