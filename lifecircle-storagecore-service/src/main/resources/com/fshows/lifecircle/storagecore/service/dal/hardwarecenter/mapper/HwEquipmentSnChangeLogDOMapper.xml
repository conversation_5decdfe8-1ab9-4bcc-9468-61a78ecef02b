<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentSnChangeLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentSnChangeLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SN_ID" property="snId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL" property="channel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HANDLE_TYPE" property="handleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="EXECUTE_TIME" property="executeTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`SN_ID`,`DEPOT`,`AGENT_ID`,`CHANNEL`,`GRANT_ID`,`STORE_ID`,`HANDLE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`EXECUTE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_SN_CHANGE_LOG-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_SN_CHANGE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="snId != null">`SN_ID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="handleType != null">`HANDLE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="snId != null">#{snId,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="handleType != null">#{handleType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--查询设备最后的绑定记录-->
            <select id="getLastLogBySnId" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETLASTLOGBYSNID*/  <include refid="Base_Column_List" />
        from hw_equipment_sn_change_log
        where handle_type = 3
        and sn_id = #{snId,jdbcType = INTEGER}
        ORDER BY create_time DESC limit 1
            </select>

            <!--获取首次绑定记录-->
            <select id="getFirstBind" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETFIRSTBIND*/  <include refid="Base_Column_List" /> from hw_equipment_sn_change_log
        where sn_id = #{snId,jdbcType = INTEGER}
        and handle_type = 3
        ORDER BY create_time ASC limit 1
            </select>

            <!--获取入库时仓库位置-->
            <select id="getInDepot" resultType="Integer">
                    select /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETINDEPOT*/ depot from hw_equipment_sn_change_log where sn_id = (select /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETINDEPOT*/ id from hw_equipment_sn where init_sn =
        #{initSn,jdbcType = VARCHAR} LIMIT 1)
        AND execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        AND handle_type IN (8,10,14)
        ORDER BY execute_time DESC LIMIT 1
            </select>

            <!--获取出库时仓库位置-->
            <select id="getOutDepot" resultType="Integer">
                    select /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETOUTDEPOT*/ depot from hw_equipment_sn_change_log where sn_id = (select /*MS-HW-EQUIPMENT-SN-CHANGE-LOG-GETOUTDEPOT*/ id from hw_equipment_sn where init_sn =
        #{initSn,jdbcType = VARCHAR} LIMIT 1)
        AND execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        AND handle_type = 11
        ORDER BY execute_time DESC LIMIT 1
            </select>
    </mapper>
