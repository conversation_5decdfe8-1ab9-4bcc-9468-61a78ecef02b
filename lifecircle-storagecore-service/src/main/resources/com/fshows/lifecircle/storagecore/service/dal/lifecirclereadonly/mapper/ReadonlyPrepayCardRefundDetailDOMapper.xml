<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardRefundDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardRefundDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_NO" property="refundNo" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SALES_ORDER_NO" property="salesOrderNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="REFUND_NUMBER" property="refundNumber" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CARD_PRICE" property="cardPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CARD_AMOUNT" property="cardAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`REFUND_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`CARD_SPU_NAME`,`PUBLISH_ORG_ID`,`SALES_ORDER_NO`,`IS_DEL`,`REFUND_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`CARD_PRICE`,`CARD_AMOUNT`
    </sql>


            <!--insert:TP_PREPAY_CARD_REFUND_DETAIL-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="refundNo != null">`REFUND_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

    <!--根据销售单no查询退款详情列表-->
    <select id="findListBySalesOrderNo" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tp_prepay_card_refund_detail
        WHERE
        sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
    </select>

    <!--查询预付卡退货订单-->
    <select id="findRefundOrderDetailByAppointTime" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM tp_prepay_card_refund_detail
        WHERE is_del = 0
        and `create_time` <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and `create_time` <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        and publish_org_id in
        <foreach close=")" collection="list" index="index" item="publishOrgId" open="(" separator=",">
            #{publishOrgId, jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
