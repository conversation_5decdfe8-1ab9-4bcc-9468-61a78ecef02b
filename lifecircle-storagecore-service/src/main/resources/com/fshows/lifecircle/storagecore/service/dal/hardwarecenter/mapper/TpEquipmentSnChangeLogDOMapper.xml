<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentSnChangeLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentSnChangeLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="SN_ID" property="snId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OP_TYPE" property="opType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STATE_DESC" property="stateDesc" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HANDLE_TYPE" property="handleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>
    </resultMap>

        <resultMap id="bingTimeMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.BingTimeMap">

                <result column="sn_id" property="snId" javaType="Integer"/>

                <result column="create_time" property="createTime" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`SN_ID`,`OP_TYPE`,`AGENT_ID`,`STORE_ID`,`STATE_DESC`,`CREATE_TIME`,`HANDLE_TYPE`,`UPDATE_TIME`
    </sql>


            <!--批量查询绑定时间-->
            <select id="getBingTimeBatch" resultMap="bingTimeMap">
                    SELECT /*MS-TP-EQUIPMENT-SN-CHANGE-LOG-GETBINGTIMEBATCH*/ 
        log.sn_id,
        log.create_time
        FROM
        ( SELECT /*MS-TP-EQUIPMENT-SN-CHANGE-LOG-GETBINGTIMEBATCH*/  <include refid="Base_Column_List" /> FROM tp_equipment_sn_change_log WHERE handle_type = 3 ORDER BY create_time ASC ) log
        WHERE log.sn_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        log.sn_id
            </select>
    </mapper>
