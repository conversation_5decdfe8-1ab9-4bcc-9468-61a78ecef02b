<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.RiskRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.RiskRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="MCH_ID" property="mchId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="REASON" property="reason" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="RECORD_ID" property="recordId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="RISK_TYPE" property="riskType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="TRADE_TIME" property="tradeTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="DATA_TYPE_ID" property="dataTypeId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="DEAL_METHOD" property="dealMethod" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ORG_TRADE_NO" property="orgTradeNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PAYER_PHONE" property="payerPhone" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PUNISH_PLAN" property="punishPlan" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PUNISH_TIME" property="punishTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="FUBEI_TRADE_NO" property="fubeiTradeNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="RISK_TYPE_DESC" property="riskTypeDesc" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COLLECT_DATA_ID" property="collectDataId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMPLAINT_TIME" property="complaintTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SINGLE_OUT_DAYS" property="singleOutDays" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLATFORM_STATUS" property="platformStatus" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMPLAINT_DETAIL" property="complaintDetail" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLATFORM_TRADE_NO" property="platformTradeNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SERVICE_PROVIDER" property="serviceProvider" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLATFORM_DEAL_TIME" property="platformDealTime" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PLATFORM_RISK_TYPE" property="platformRiskType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="PUNISH_DESCRIPTION" property="punishDescription" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MSID_TYPE" property="msidType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="USER_COMPLAINT_TIMES" property="userComplaintTimes" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="AMOUNT" property="amount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`MCH_ID`,`REASON`,`ORDER_SN`,`RECORD_ID`,`RISK_TYPE`,`TRADE_TIME`,`DATA_TYPE_ID`,`DEAL_METHOD`,`ORG_TRADE_NO`,`PAYER_PHONE`,`PUNISH_PLAN`,`PUNISH_TIME`,`FUBEI_TRADE_NO`,`RISK_TYPE_DESC`,`COLLECT_DATA_ID`,`COMPLAINT_TIME`,`SINGLE_OUT_DAYS`,`PLATFORM_STATUS`,`COMPLAINT_DETAIL`,`PLATFORM_TRADE_NO`,`SERVICE_PROVIDER`,`PLATFORM_DEAL_TIME`,`PLATFORM_RISK_TYPE`,`PUNISH_DESCRIPTION`,`MSID_TYPE`,`MERCHANT_ID`,`USER_COMPLAINT_TIMES`,`CREATE_TIME`,`UPDATE_TIME`,`AMOUNT`
    </sql>


            <!--insert:FK_RISK_RECORD-->
    <insert id="insert">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO FK_RISK_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mchId != null">`MCH_ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="recordId != null">`RECORD_ID`,</if>
            <if test="riskType != null">`RISK_TYPE`,</if>
            <if test="tradeTime != null">`TRADE_TIME`,</if>
            <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
            <if test="dealMethod != null">`DEAL_METHOD`,</if>
            <if test="orgTradeNo != null">`ORG_TRADE_NO`,</if>
            <if test="payerPhone != null">`PAYER_PHONE`,</if>
            <if test="punishPlan != null">`PUNISH_PLAN`,</if>
            <if test="punishTime != null">`PUNISH_TIME`,</if>
            <if test="fubeiTradeNo != null">`FUBEI_TRADE_NO`,</if>
            <if test="riskTypeDesc != null">`RISK_TYPE_DESC`,</if>
            <if test="collectDataId != null">`COLLECT_DATA_ID`,</if>
            <if test="complaintTime != null">`COMPLAINT_TIME`,</if>
            <if test="singleOutDays != null">`SINGLE_OUT_DAYS`,</if>
            <if test="platformStatus != null">`PLATFORM_STATUS`,</if>
            <if test="complaintDetail != null">`COMPLAINT_DETAIL`,</if>
            <if test="platformTradeNo != null">`PLATFORM_TRADE_NO`,</if>
            <if test="serviceProvider != null">`SERVICE_PROVIDER`,</if>
            <if test="platformDealTime != null">`PLATFORM_DEAL_TIME`,</if>
            <if test="platformRiskType != null">`PLATFORM_RISK_TYPE`,</if>
            <if test="punishDescription != null">`PUNISH_DESCRIPTION`,</if>
            <if test="msidType != null">`MSID_TYPE`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="userComplaintTimes != null">`USER_COMPLAINT_TIMES`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="amount != null">`AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="mchId != null">#{mchId,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
            <if test="riskType != null">#{riskType,jdbcType=VARCHAR},</if>
            <if test="tradeTime != null">#{tradeTime,jdbcType=VARCHAR},</if>
            <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
            <if test="dealMethod != null">#{dealMethod,jdbcType=VARCHAR},</if>
            <if test="orgTradeNo != null">#{orgTradeNo,jdbcType=VARCHAR},</if>
            <if test="payerPhone != null">#{payerPhone,jdbcType=VARCHAR},</if>
            <if test="punishPlan != null">#{punishPlan,jdbcType=VARCHAR},</if>
            <if test="punishTime != null">#{punishTime,jdbcType=VARCHAR},</if>
            <if test="fubeiTradeNo != null">#{fubeiTradeNo,jdbcType=VARCHAR},</if>
            <if test="riskTypeDesc != null">#{riskTypeDesc,jdbcType=VARCHAR},</if>
            <if test="collectDataId != null">#{collectDataId,jdbcType=VARCHAR},</if>
            <if test="complaintTime != null">#{complaintTime,jdbcType=VARCHAR},</if>
            <if test="singleOutDays != null">#{singleOutDays,jdbcType=VARCHAR},</if>
            <if test="platformStatus != null">#{platformStatus,jdbcType=VARCHAR},</if>
            <if test="complaintDetail != null">#{complaintDetail,jdbcType=VARCHAR},</if>
            <if test="platformTradeNo != null">#{platformTradeNo,jdbcType=VARCHAR},</if>
            <if test="serviceProvider != null">#{serviceProvider,jdbcType=VARCHAR},</if>
            <if test="platformDealTime != null">#{platformDealTime,jdbcType=VARCHAR},</if>
            <if test="platformRiskType != null">#{platformRiskType,jdbcType=VARCHAR},</if>
            <if test="punishDescription != null">#{punishDescription,jdbcType=VARCHAR},</if>
            <if test="msidType != null">#{msidType,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="userComplaintTimes != null">#{userComplaintTimes,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

            <!--根据OrderSn 和 来源 获取数据:fk_risk_record-->
            <select id="queryByOrderSn" resultMap="BaseResultMap">
                    SELECT /*MS-FK-RISK-RECORD-QUERYBYORDERSN*/  <include refid="Base_Column_List" />
        FROM fk_risk_record
        WHERE
        order_sn = #{orderSn,jdbcType=VARCHAR}
        AND
        data_type_id = #{dataTypeId,jdbcType=VARCHAR}
            </select>

            <!--根据merchantId:fk_risk_record-->
            <select id="queryByMerchantId" resultMap="BaseResultMap">
                    SELECT /*MS-FK-RISK-RECORD-QUERYBYMERCHANTID*/  <include refid="Base_Column_List" />
        FROM fk_risk_record
        WHERE
        MERCHANT_ID = #{merchantId,jdbcType=INTEGER}
        ORDER BY CREATE_TIME DESC
        LIMIT 1
            </select>
    </mapper>
