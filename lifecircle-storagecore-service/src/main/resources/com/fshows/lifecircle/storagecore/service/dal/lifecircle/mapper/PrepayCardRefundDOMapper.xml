<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardRefundDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardRefundDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_NO" property="refundNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_NAME" property="customerName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALES_ORDER_NO" property="salesOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_RE_PUT_CARD" property="isRePutCard" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_NUMBER" property="refundNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUND_PRICE" property="refundPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`REFUND_NO`,`OPERATE_ID`,`CUSTOMER_ID`,`OPERATE_NAME`,`CUSTOMER_NAME`,`SALES_ORDER_NO`,`IS_DEL`,`IS_RE_PUT_CARD`,`REFUND_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`ORDER_PRICE`,`REFUND_PRICE`
    </sql>


            <!--insert:TP_PREPAY_CARD_REFUND-->
            <insert id="insert" >
            INSERT INTO TP_PREPAY_CARD_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="orgId != null">`ORG_ID`,</if>
        <if test="refundNo != null">`REFUND_NO`,</if>
        <if test="operateId != null">`OPERATE_ID`,</if>
        <if test="customerId != null">`CUSTOMER_ID`,</if>
        <if test="operateName != null">`OPERATE_NAME`,</if>
        <if test="customerName != null">`CUSTOMER_NAME`,</if>
        <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="refundNumber != null">`REFUND_NUMBER`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="refundPrice != null">`REFUND_PRICE`,</if>
        <if test="isRePutCard != null">`IS_RE_PUT_CARD`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
        <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
        <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
        <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
        <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
        <if test="customerName != null">#{customerName,jdbcType=VARCHAR},</if>
        <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="refundPrice != null">#{refundPrice,jdbcType=DECIMAL},</if>
        <if test="isRePutCard != null">#{isRePutCard,jdbcType=TINYINT},</if>
    </trim>
            </insert>
    </mapper>
