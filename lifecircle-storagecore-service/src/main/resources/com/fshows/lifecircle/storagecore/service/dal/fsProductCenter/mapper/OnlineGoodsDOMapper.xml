<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.mapper.OnlineGoodsDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.fsProductCenter.dataobject.OnlineGoodsDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="PLU" property="plu" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SKU" property="sku" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SPU" property="spu" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CODE" property="code" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT1" property="ext1" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT2" property="ext2" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXT3" property="ext3" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="NAME" property="name" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GS_UID" property="gsUid" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SPU_ID" property="spuId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REMARK" property="remark" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_ID" property="goodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GS_STORE_ID" property="gsStoreId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SIMPLE_SPELL" property="simpleSpell" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ONLINE_GOODS_ID" property="onlineGoodsId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ONLINE_SUB_SPU_ID" property="onlineSubSpuId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ONLINE_CATEGORY_ID" property="onlineCategoryId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="SALE_COUNT" property="saleCount" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CATEGORY_ID" property="categoryId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="SALE_WEIGHT" property="saleWeight" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="SELL_STATUS" property="sellStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="IS_UNLIMITED_STOCK" property="isUnlimitedStock" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="REVISE_PRICE_TIME" property="revisePriceTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_PRICE_TIME" property="updatePriceTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="PRICE" property="price" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="STOCK_NUM" property="stockNum" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="SALE_MONEY" property="saleMoney" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="STOCK_PRICE" property="stockPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="REVISE_PRICE" property="revisePrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`PLU`,`SKU`,`SPU`,`CODE`,`EXT1`,`EXT2`,`EXT3`,`NAME`,`GS_UID`,`SPU_ID`,`REMARK`,`GOODS_ID`,`CREATE_BY`,`GS_STORE_ID`,`SIMPLE_SPELL`,`ONLINE_GOODS_ID`,`ONLINE_SUB_SPU_ID`,`ONLINE_CATEGORY_ID`,`IS_DEL`,`SALE_COUNT`,`CATEGORY_ID`,`SALE_WEIGHT`,`SELL_STATUS`,`IS_UNLIMITED_STOCK`,`CREATE_TIME`,`UPDATE_TIME`,`REVISE_PRICE_TIME`,`UPDATE_PRICE_TIME`,`PRICE`,`STOCK_NUM`,`SALE_MONEY`,`STOCK_PRICE`,`REVISE_PRICE`
    </sql>


    <!--get:GS_ONLINE_GOODS-->
    <select id="listOnlineGoodsByOnlineGoodsId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from gs_online_goods
        where online_goods_id in
        <foreach collection="list" item="onlineGoodsId" open="(" separator="," close=")">
            #{onlineGoodsId,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
