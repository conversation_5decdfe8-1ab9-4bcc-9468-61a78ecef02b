<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.ChannelPaymentDiffDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.ChannelPaymentDiffDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BILL_AMOUNT" property="billAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PAYMENT_AMOUNT" property="paymentAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHECK_REMARK" property="checkRemark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_OPERATOR_ID" property="applyOperatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHECK_OPERATOR_ID" property="checkOperatorId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHECK_TIME" property="checkTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PAYMENT_TIME" property="paymentTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BILL_AMOUNT`,`PAYMENT_AMOUNT`,`BILL_NO`,`BLOC_ID`,`CHECK_REMARK`,`CHECK_STATUS`,`PLATFORM_CODE`,`APPLY_OPERATOR_ID`,`CHECK_OPERATOR_ID`,`DEL_FLAG`,`CHECK_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`PAYMENT_TIME`
    </sql>


            <!--insert:ACC_CHANNEL_PAYMENT_DIFF-->
            <insert id="insert" >
                    INSERT INTO ACC_CHANNEL_PAYMENT_DIFF
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="billAmount != null">`BILL_AMOUNT`,</if>
            <if test="paymentAmount != null">`PAYMENT_AMOUNT`,</if>
            <if test="billNo != null">`BILL_NO`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="checkRemark != null">`CHECK_REMARK`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="platformCode != null">`PLATFORM_CODE`,</if>
            <if test="applyOperatorId != null">`APPLY_OPERATOR_ID`,</if>
            <if test="checkOperatorId != null">`CHECK_OPERATOR_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="checkTime != null">`CHECK_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="paymentTime != null">`PAYMENT_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="billAmount != null">#{billAmount,jdbcType=BIGINT},</if>
            <if test="paymentAmount != null">#{paymentAmount,jdbcType=BIGINT},</if>
            <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="checkRemark != null">#{checkRemark,jdbcType=VARCHAR},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=VARCHAR},</if>
            <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
            <if test="applyOperatorId != null">#{applyOperatorId,jdbcType=VARCHAR},</if>
            <if test="checkOperatorId != null">#{checkOperatorId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="checkTime != null">#{checkTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="paymentTime != null">#{paymentTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--删除分账待处理差异-->
            <delete id="deleteDiffByBillNo" >
                    delete
        from
        ACC_CHANNEL_PAYMENT_DIFF
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
        AND CHECK_STATUS = 'INIT'
            </delete>
    </mapper>
