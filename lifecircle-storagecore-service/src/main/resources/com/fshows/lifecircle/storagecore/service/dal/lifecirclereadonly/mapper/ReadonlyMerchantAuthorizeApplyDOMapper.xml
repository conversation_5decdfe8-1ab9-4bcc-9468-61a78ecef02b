<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyMerchantAuthorizeApplyDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyMerchantAuthorizeApplyDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="NAME" property="name" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CERT_PIC" property="certPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CERT_TYPE" property="certType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SUB_MCHID" property="subMchid" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CERT_NUMBER" property="certNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="QRCODE_DATA" property="qrcodeData" jdbcType="LONGVARCHAR"
            javaType="String"/>

    <result column="APPLYMENT_ID" property="applymentId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LEGAL_PERSON" property="legalPerson" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="REJECT_PARAM" property="rejectParam" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SUBJECT_TYPE" property="subjectType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_CODE" property="businessCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CERT_TYPE_NAME" property="certTypeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ID_CARD_NUMBER" property="idCardNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MICRO_BIZ_TYPE" property="microBizType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_ADDRESS" property="storeAddress" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE_NUMBER" property="licenseNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_AREA_CODE" property="storeAreaCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_AREA_NAME" property="storeAreaName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_CITY_CODE" property="storeCityCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_CITY_NAME" property="storeCityName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="APPLYMENT_STATE" property="applymentState" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="AUTHORIZE_STATE" property="authorizeState" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMPANY_ADDRESS" property="companyAddress" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE_END_DATE" property="licenseEndDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_HEADER_PIC" property="storeHeaderPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_INDOOR_PIC" property="storeIndoorPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COMPANY_PROVE_PIC" property="companyProvePic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SUBJECT_TYPE_NAME" property="subjectTypeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONFIRM_MCHID_LIST" property="confirmMchidList" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_ID_DOC_COPY" property="contactIdDocCopy" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_PERIOD_END" property="contactPeriodEnd" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="LICENSE_BEGIN_DATE" property="licenseBeginDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MICRO_BIZ_TYPE_NAME" property="microBizTypeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_ADDRESS_CODE" property="storeAddressCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="APPLYMENT_STATE_MSG" property="applymentStateMsg" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_PROVINCE_CODE" property="storeProvinceCode" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="STORE_PROVINCE_NAME" property="storeProvinceName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_PERIOD_BEGIN" property="contactPeriodBegin" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_NAME" property="identificationName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_TYPE" property="identificationType" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CONTACT_ID_DOC_COPY_BACK" property="contactIdDocCopyBack" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_NUMBER" property="identificationNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_ADDRESS" property="identificationAddress" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_BACK_PIC" property="identificationBackPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_END_DATE" property="identificationEndDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_FRONT_PIC" property="identificationFrontPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_TYPE_NAME" property="identificationTypeName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IDENTIFICATION_BEGIN_DATE" property="identificationBeginDate" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="BUSINESS_AUTHORIZATION_LETTER" property="businessAuthorizationLetter" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UID" property="uid" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="AUDIT_STATUS" property="auditStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_TYPE" property="contactType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="APPLY_CHANNEL" property="applyChannel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="AUTHORIZE_TYPE" property="authorizeType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CERT_TYPE_VALUE" property="certTypeValue" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="LICENSE_IS_LONG" property="licenseIsLong" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="APPLYMENT_METHOD" property="applymentMethod" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="APPLYMENT_STATUS" property="applymentStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="AUTHORIZE_STATUS" property="authorizeStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_ID_DOC_TYPE" property="contactIdDocType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SUBJECT_TYPE_VALUE" property="subjectTypeValue" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="MICRO_BIZ_TYPE_VALUE" property="microBizTypeValue" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CONTACT_PERIOD_IS_LONG" property="contactPeriodIsLong" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IDENTIFICATION_IS_LONG" property="identificationIsLong" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IDENTIFICATION_TYPE_VALUE" property="identificationTypeValue" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


    <sql id="Base_Column_List">
    `ID`,`NAME`,`MOBILE`,`CERT_PIC`,`CERT_TYPE`,`SUB_MCHID`,`STORE_NAME`,`CERT_NUMBER`,`LICENSE_PIC`,`QRCODE_DATA`,`APPLYMENT_ID`,`COMPANY_NAME`,`LEGAL_PERSON`,`REJECT_PARAM`,`SUBJECT_TYPE`,`BUSINESS_CODE`,`CERT_TYPE_NAME`,`ID_CARD_NUMBER`,`MICRO_BIZ_TYPE`,`REJECT_REASON`,`STORE_ADDRESS`,`LICENSE_NUMBER`,`STORE_AREA_CODE`,`STORE_AREA_NAME`,`STORE_CITY_CODE`,`STORE_CITY_NAME`,`APPLYMENT_STATE`,`AUTHORIZE_STATE`,`COMPANY_ADDRESS`,`LICENSE_END_DATE`,`STORE_HEADER_PIC`,`STORE_INDOOR_PIC`,`COMPANY_PROVE_PIC`,`SUBJECT_TYPE_NAME`,`CONFIRM_MCHID_LIST`,`CONTACT_ID_DOC_COPY`,`CONTACT_PERIOD_END`,`LICENSE_BEGIN_DATE`,`MICRO_BIZ_TYPE_NAME`,`STORE_ADDRESS_CODE`,`APPLYMENT_STATE_MSG`,`STORE_PROVINCE_CODE`,`STORE_PROVINCE_NAME`,`CONTACT_PERIOD_BEGIN`,`IDENTIFICATION_NAME`,`IDENTIFICATION_TYPE`,`CONTACT_ID_DOC_COPY_BACK`,`IDENTIFICATION_NUMBER`,`IDENTIFICATION_ADDRESS`,`IDENTIFICATION_BACK_PIC`,`IDENTIFICATION_END_DATE`,`IDENTIFICATION_FRONT_PIC`,`IDENTIFICATION_TYPE_NAME`,`IDENTIFICATION_BEGIN_DATE`,`BUSINESS_AUTHORIZATION_LETTER`,`UID`,`IS_DEL`,`STORE_ID`,`CHANNEL_ID`,`AUDIT_STATUS`,`CONTACT_TYPE`,`APPLY_CHANNEL`,`AUTHORIZE_TYPE`,`CERT_TYPE_VALUE`,`LICENSE_IS_LONG`,`APPLYMENT_METHOD`,`APPLYMENT_STATUS`,`AUTHORIZE_STATUS`,`LIQUIDATION_TYPE`,`CONTACT_ID_DOC_TYPE`,`SUBJECT_TYPE_VALUE`,`MICRO_BIZ_TYPE_VALUE`,`CONTACT_PERIOD_IS_LONG`,`IDENTIFICATION_IS_LONG`,`IDENTIFICATION_TYPE_VALUE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_MERCHANT_AUTHORIZE_APPLY-->
    <insert id="insert">
        INSERT INTO TP_MERCHANT_AUTHORIZE_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="certPic != null">`CERT_PIC`,</if>
            <if test="certType != null">`CERT_TYPE`,</if>
            <if test="subMchid != null">`SUB_MCHID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="certNumber != null">`CERT_NUMBER`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="qrcodeData != null">`QRCODE_DATA`,</if>
            <if test="applymentId != null">`APPLYMENT_ID`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="legalPerson != null">`LEGAL_PERSON`,</if>
            <if test="rejectParam != null">`REJECT_PARAM`,</if>
            <if test="subjectType != null">`SUBJECT_TYPE`,</if>
            <if test="businessCode != null">`BUSINESS_CODE`,</if>
            <if test="certTypeName != null">`CERT_TYPE_NAME`,</if>
            <if test="idCardNumber != null">`ID_CARD_NUMBER`,</if>
            <if test="microBizType != null">`MICRO_BIZ_TYPE`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="storeAddress != null">`STORE_ADDRESS`,</if>
            <if test="licenseNumber != null">`LICENSE_NUMBER`,</if>
            <if test="storeAreaCode != null">`STORE_AREA_CODE`,</if>
            <if test="storeAreaName != null">`STORE_AREA_NAME`,</if>
            <if test="storeCityCode != null">`STORE_CITY_CODE`,</if>
            <if test="storeCityName != null">`STORE_CITY_NAME`,</if>
            <if test="applymentState != null">`APPLYMENT_STATE`,</if>
            <if test="authorizeState != null">`AUTHORIZE_STATE`,</if>
            <if test="companyAddress != null">`COMPANY_ADDRESS`,</if>
            <if test="licenseEndDate != null">`LICENSE_END_DATE`,</if>
            <if test="storeHeaderPic != null">`STORE_HEADER_PIC`,</if>
            <if test="storeIndoorPic != null">`STORE_INDOOR_PIC`,</if>
            <if test="companyProvePic != null">`COMPANY_PROVE_PIC`,</if>
            <if test="subjectTypeName != null">`SUBJECT_TYPE_NAME`,</if>
            <if test="confirmMchidList != null">`CONFIRM_MCHID_LIST`,</if>
            <if test="contactIdDocCopy != null">`CONTACT_ID_DOC_COPY`,</if>
            <if test="contactPeriodEnd != null">`CONTACT_PERIOD_END`,</if>
            <if test="licenseBeginDate != null">`LICENSE_BEGIN_DATE`,</if>
            <if test="microBizTypeName != null">`MICRO_BIZ_TYPE_NAME`,</if>
            <if test="storeAddressCode != null">`STORE_ADDRESS_CODE`,</if>
            <if test="applymentStateMsg != null">`APPLYMENT_STATE_MSG`,</if>
            <if test="storeProvinceCode != null">`STORE_PROVINCE_CODE`,</if>
            <if test="storeProvinceName != null">`STORE_PROVINCE_NAME`,</if>
            <if test="contactPeriodBegin != null">`CONTACT_PERIOD_BEGIN`,</if>
            <if test="identificationName != null">`IDENTIFICATION_NAME`,</if>
            <if test="identificationType != null">`IDENTIFICATION_TYPE`,</if>
            <if test="contactIdDocCopyBack != null">`CONTACT_ID_DOC_COPY_BACK`,</if>
            <if test="identificationNumber != null">`IDENTIFICATION_NUMBER`,</if>
            <if test="identificationAddress != null">`IDENTIFICATION_ADDRESS`,</if>
            <if test="identificationBackPic != null">`IDENTIFICATION_BACK_PIC`,</if>
            <if test="identificationEndDate != null">`IDENTIFICATION_END_DATE`,</if>
            <if test="identificationFrontPic != null">`IDENTIFICATION_FRONT_PIC`,</if>
            <if test="identificationTypeName != null">`IDENTIFICATION_TYPE_NAME`,</if>
            <if test="identificationBeginDate != null">`IDENTIFICATION_BEGIN_DATE`,</if>
            <if test="businessAuthorizationLetter != null">`BUSINESS_AUTHORIZATION_LETTER`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="contactType != null">`CONTACT_TYPE`,</if>
            <if test="applyChannel != null">`APPLY_CHANNEL`,</if>
            <if test="authorizeType != null">`AUTHORIZE_TYPE`,</if>
            <if test="certTypeValue != null">`CERT_TYPE_VALUE`,</if>
            <if test="licenseIsLong != null">`LICENSE_IS_LONG`,</if>
            <if test="applymentMethod != null">`APPLYMENT_METHOD`,</if>
            <if test="applymentStatus != null">`APPLYMENT_STATUS`,</if>
            <if test="authorizeStatus != null">`AUTHORIZE_STATUS`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="contactIdDocType != null">`CONTACT_ID_DOC_TYPE`,</if>
            <if test="subjectTypeValue != null">`SUBJECT_TYPE_VALUE`,</if>
            <if test="microBizTypeValue != null">`MICRO_BIZ_TYPE_VALUE`,</if>
            <if test="contactPeriodIsLong != null">`CONTACT_PERIOD_IS_LONG`,</if>
            <if test="identificationIsLong != null">`IDENTIFICATION_IS_LONG`,</if>
            <if test="identificationTypeValue != null">`IDENTIFICATION_TYPE_VALUE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="certPic != null">#{certPic,jdbcType=VARCHAR},</if>
            <if test="certType != null">#{certType,jdbcType=VARCHAR},</if>
            <if test="subMchid != null">#{subMchid,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="certNumber != null">#{certNumber,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="qrcodeData != null">#{qrcodeData,jdbcType=LONGVARCHAR},</if>
            <if test="applymentId != null">#{applymentId,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="legalPerson != null">#{legalPerson,jdbcType=VARCHAR},</if>
            <if test="rejectParam != null">#{rejectParam,jdbcType=VARCHAR},</if>
            <if test="subjectType != null">#{subjectType,jdbcType=VARCHAR},</if>
            <if test="businessCode != null">#{businessCode,jdbcType=VARCHAR},</if>
            <if test="certTypeName != null">#{certTypeName,jdbcType=VARCHAR},</if>
            <if test="idCardNumber != null">#{idCardNumber,jdbcType=VARCHAR},</if>
            <if test="microBizType != null">#{microBizType,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="storeAddress != null">#{storeAddress,jdbcType=VARCHAR},</if>
            <if test="licenseNumber != null">#{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="storeAreaCode != null">#{storeAreaCode,jdbcType=VARCHAR},</if>
            <if test="storeAreaName != null">#{storeAreaName,jdbcType=VARCHAR},</if>
            <if test="storeCityCode != null">#{storeCityCode,jdbcType=VARCHAR},</if>
            <if test="storeCityName != null">#{storeCityName,jdbcType=VARCHAR},</if>
            <if test="applymentState != null">#{applymentState,jdbcType=VARCHAR},</if>
            <if test="authorizeState != null">#{authorizeState,jdbcType=VARCHAR},</if>
            <if test="companyAddress != null">#{companyAddress,jdbcType=VARCHAR},</if>
            <if test="licenseEndDate != null">#{licenseEndDate,jdbcType=VARCHAR},</if>
            <if test="storeHeaderPic != null">#{storeHeaderPic,jdbcType=VARCHAR},</if>
            <if test="storeIndoorPic != null">#{storeIndoorPic,jdbcType=VARCHAR},</if>
            <if test="companyProvePic != null">#{companyProvePic,jdbcType=VARCHAR},</if>
            <if test="subjectTypeName != null">#{subjectTypeName,jdbcType=VARCHAR},</if>
            <if test="confirmMchidList != null">#{confirmMchidList,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopy != null">#{contactIdDocCopy,jdbcType=VARCHAR},</if>
            <if test="contactPeriodEnd != null">#{contactPeriodEnd,jdbcType=VARCHAR},</if>
            <if test="licenseBeginDate != null">#{licenseBeginDate,jdbcType=VARCHAR},</if>
            <if test="microBizTypeName != null">#{microBizTypeName,jdbcType=VARCHAR},</if>
            <if test="storeAddressCode != null">#{storeAddressCode,jdbcType=VARCHAR},</if>
            <if test="applymentStateMsg != null">#{applymentStateMsg,jdbcType=VARCHAR},</if>
            <if test="storeProvinceCode != null">#{storeProvinceCode,jdbcType=VARCHAR},</if>
            <if test="storeProvinceName != null">#{storeProvinceName,jdbcType=VARCHAR},</if>
            <if test="contactPeriodBegin != null">#{contactPeriodBegin,jdbcType=VARCHAR},</if>
            <if test="identificationName != null">#{identificationName,jdbcType=VARCHAR},</if>
            <if test="identificationType != null">#{identificationType,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopyBack != null">#{contactIdDocCopyBack,jdbcType=VARCHAR},</if>
            <if test="identificationNumber != null">#{identificationNumber,jdbcType=VARCHAR},</if>
            <if test="identificationAddress != null">#{identificationAddress,jdbcType=VARCHAR},</if>
            <if test="identificationBackPic != null">#{identificationBackPic,jdbcType=VARCHAR},</if>
            <if test="identificationEndDate != null">#{identificationEndDate,jdbcType=VARCHAR},</if>
            <if test="identificationFrontPic != null">#{identificationFrontPic,jdbcType=VARCHAR},</if>
            <if test="identificationTypeName != null">#{identificationTypeName,jdbcType=VARCHAR},</if>
            <if test="identificationBeginDate != null">#{identificationBeginDate,jdbcType=VARCHAR},</if>
            <if test="businessAuthorizationLetter != null">#{businessAuthorizationLetter,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="contactType != null">#{contactType,jdbcType=TINYINT},</if>
            <if test="applyChannel != null">#{applyChannel,jdbcType=TINYINT},</if>
            <if test="authorizeType != null">#{authorizeType,jdbcType=TINYINT},</if>
            <if test="certTypeValue != null">#{certTypeValue,jdbcType=TINYINT},</if>
            <if test="licenseIsLong != null">#{licenseIsLong,jdbcType=TINYINT},</if>
            <if test="applymentMethod != null">#{applymentMethod,jdbcType=TINYINT},</if>
            <if test="applymentStatus != null">#{applymentStatus,jdbcType=TINYINT},</if>
            <if test="authorizeStatus != null">#{authorizeStatus,jdbcType=TINYINT},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="contactIdDocType != null">#{contactIdDocType,jdbcType=TINYINT},</if>
            <if test="subjectTypeValue != null">#{subjectTypeValue,jdbcType=TINYINT},</if>
            <if test="microBizTypeValue != null">#{microBizTypeValue,jdbcType=TINYINT},</if>
            <if test="contactPeriodIsLong != null">#{contactPeriodIsLong,jdbcType=TINYINT},</if>
            <if test="identificationIsLong != null">#{identificationIsLong,jdbcType=TINYINT},</if>
            <if test="identificationTypeValue != null">#{identificationTypeValue,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--分页查询商户实名认证列表 pageCount-->
    <select id="findMerchantAuthorizeCrmListByPageCount" resultType="int">
        SELECT
        COUNT(*) AS total
        FROM
        tp_merchant_authorize_apply apply
        LEFT JOIN tp_users users
        ON apply.uid = users.id
        LEFT JOIN tp_user u
        ON users.belong = u.id
        WHERE apply.is_del = 0
        AND users.parent_id = 0
        AND apply.audit_status in (1, 2, 3, 4, 5, 6)
        <if test="authorizeType != null">
            AND apply.authorize_type = #{authorizeType, jdbcType=INTEGER}
        </if>
        <if test="startDate != null">
            AND apply.create_time <![CDATA[ >= ]]> #{startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            AND apply.create_time <![CDATA[ <= ]]>  #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="subMchid != null and subMchid != ''">
            AND apply.sub_mchid = #{subMchid,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and company != ''">
            AND users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName != null and belongName != '' ">
            AND u.username LIKE CONCAT (#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="auditStatus != null and auditStatus != -1  and auditStatus != 0">
            AND apply.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="applyChannel!=null and applyChannel !=0">
            AND apply.apply_channel=#{applyChannel,jdbcType=INTEGER}
        </if>

    </select>
    <!--分页查询商户实名认证列表 pageResult-->
    <select id="findMerchantAuthorizeCrmListByPageResult"
            resultType="com.fshows.lifecircle.storagecore.service.domain.dto.CrmMerchantAuthSimpleInfoDTO">
        SELECT
        apply.liquidation_type as liquidationType,
        apply.authorize_type as authorizeType,
        apply.applyment_method as applymentMethod,
        apply.create_time as applyTime,
        apply.uid as uid,
        users.username as username,
        users.company as company,
        apply.sub_mchid as subMchid,
        u.username as belongName,
        apply.audit_status as auditStatus,
        apply.reject_reason as rejectReason,
        apply.apply_channel as applyChannel
        FROM
        tp_merchant_authorize_apply apply
        LEFT JOIN tp_users users
        ON apply.uid = users.id
        LEFT JOIN tp_user u
        ON users.belong = u.id
        WHERE apply.is_del = 0
        AND users.parent_id = 0
        AND apply.audit_status in (1, 2, 3, 4, 5, 6)
        <if test="authorizeType != null">
            AND apply.authorize_type = #{authorizeType, jdbcType=INTEGER}
        </if>
        <if test="startDate != null">
            AND apply.create_time <![CDATA[ >= ]]> #{startDate, jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            AND apply.create_time <![CDATA[ <= ]]>  #{endDate, jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="subMchid != null and subMchid != ''">
            AND apply.sub_mchid = #{subMchid,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and company != ''">
            AND users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName != null and belongName != '' ">
            AND u.username LIKE CONCAT (#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="auditStatus != null and auditStatus != -1  and auditStatus != 0">
            AND apply.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="applyChannel!=null and applyChannel !=0">
            AND apply.apply_channel=#{applyChannel,jdbcType=INTEGER}
        </if>
        ORDER BY apply.create_time DESC
        limit #{startRow},#{limit}
    </select>
    </mapper>
