<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopOrderShipmentsRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopOrderShipmentsRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BATCH_NO" property="batchNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXPRESS_NO" property="expressNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HW_ORDER_SN" property="hwOrderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BATCH_NO`,`EXPRESS_NO`,`HW_ORDER_SN`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_SHOP_ORDER_SHIPMENTS_RECORD-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_ORDER_SHIPMENTS_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="expressNo != null">`EXPRESS_NO`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="expressNo != null">#{expressNo,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据物流单号查询订单号列表-->
            <select id="findOrderSnListByExpressNo" resultType="java.lang.String">
                    SELECT
        `HW_ORDER_SN`
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        WHERE
        `EXPRESS_NO` = #{expressNo,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
            </select>

            <!--批量查询订单发货单列表-->
            <select id="findExpressNoList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
            </select>

            <!--批量查询订单发货单列表-->
            <select id="findListByExpressNo" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        WHERE
        `EXPRESS_NO` = #{expressNo,jdbcType=VARCHAR}
        AND IS_DEL = 0
            </select>
    </mapper>
