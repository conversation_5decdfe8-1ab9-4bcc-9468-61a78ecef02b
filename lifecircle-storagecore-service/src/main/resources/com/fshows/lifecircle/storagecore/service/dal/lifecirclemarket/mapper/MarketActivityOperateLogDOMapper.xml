<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.MarketActivityOperateLogDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.MarketActivityOperateLogDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APPLY_NO" property="applyNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_DESC" property="operateDesc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_CODE" property="activityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_NAME" property="activityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATE_ATTRIBUTE" property="operateAttribute" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APPLY_NO`,`OPERATE_ID`,`OPERATE_DESC`,`OPERATE_NAME`,`ACTIVITY_CODE`,`ACTIVITY_NAME`,`OPERATE_ATTRIBUTE`,`IS_DEL`,`APPLY_STATUS`,`OPERATE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:LM_MARKET_ACTIVITY_OPERATE_LOG-->
            <insert id="insert" >
                    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_MARKET_ACTIVITY_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="operateDesc != null">`OPERATE_DESC`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="operateAttribute != null">`OPERATE_ATTRIBUTE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="activityCode != null">`ACTIVITY_CODE`,</if>
            <if test="activityName != null">`ACTIVITY_NAME`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="applyNo != null">`APPLY_NO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="operateDesc != null">#{operateDesc,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="operateAttribute != null">#{operateAttribute,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="activityCode != null">#{activityCode,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=TINYINT},</if>
            <if test="applyNo != null">#{applyNo,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--根据ID更新属性-->
            <update id="updateAttributeById" >
                    UPDATE /*MS-LM-MARKET-ACTIVITY-OPERATE-LOG-UPDATEATTRIBUTEBYID*/ lm_market_activity_operate_log
        SET
        operate_desc = #{operateDesc,jdbcType=VARCHAR}
        ,operate_attribute = #{operateAttribute,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
        LIMIT 1
            </update>

            <!--获取报名记录-->
            <select id="getByApplyNo" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        lm_market_activity_operate_log
        where
        is_del = 0
        and apply_no = #{applyNo,jdbcType=VARCHAR}
        order by id desc
            </select>

            <!--删除相关日志-->
            <delete id="deleteByApplyNoAndStatus" >
                    delete /*MS-LM-MARKET-ACTIVITY-OPERATE-LOG-DELETEBYAPPLYNOANDSTATUS*/ from lm_market_activity_operate_log
        where
        apply_no = #{applyNo,jdbcType=VARCHAR}
        and apply_status = #{applyStatus,jdbcType=TINYINT}
            </delete>
    </mapper>
