<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentPeriodsStatisticsRecordDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentPeriodsStatisticsRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EQUIPMENT_SN" property="equipmentSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PERIODS_TIME" property="periodsTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>

        <resultMap id="ByEquipmentIdMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ByEquipmentIdMap">

            <result column="num" property="num" javaType="java.lang.Integer"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>

            <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>

        <sql id="Base_Column_List">
            `ID`,`EQUIPMENT_SN`,`PERIODS_TIME`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:HW_EQUIPMENT_PERIODS_STATISTICS_RECORD-->
        <insert id="insert">
            INSERT INTO HW_EQUIPMENT_PERIODS_STATISTICS_RECORD
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
                <if test="periodsTime != null">`PERIODS_TIME`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
                <if test="periodsTime != null">#{periodsTime,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            </trim>
        </insert>

        <!--获取指定日期可用期数-->
        <select id="getMaxPeriodsTime" resultType="Integer">
            select /*MS-HW-EQUIPMENT-PERIODS-STATISTICS-RECORD-GETMAXPERIODSTIME*/ MAX(periods_time) FROM
            HW_EQUIPMENT_PERIODS_STATISTICS_RECORD
            where periods_time &lt;=#{periodsTime,jdbcType=INTEGER}
        </select>

        <!--查询指定时间的设备-->
        <select id="getByPeriodsTime" resultType="String">
            SELECT /*MS-HW-EQUIPMENT-PERIODS-STATISTICS-RECORD-GETBYPERIODSTIME*/ equipment_sn from
            hw_equipment_periods_statistics_record
            where periods_time = #{periodsTime,jdbcType=INTEGER}
        </select>

        <!--查询期初设备信息-->
        <select id="getByPeriods" resultMap="ByEquipmentIdMap">
            SELECT /*MS-HW-EQUIPMENT-PERIODS-STATISTICS-RECORD-GETBYPERIODS*/ count(*) num,esn.equipment_id,esn.depot
            from hw_equipment_periods_statistics_record periods
            LEFT JOIN hw_equipment_sn esn on esn.init_sn = periods.equipment_sn
            where periods.periods_time BETWEEN #{startTime,jdbcType=INTEGER} and #{endTime,jdbcType=INTEGER}
            GROUP BY esn.depot,esn.equipment_id
        </select>
    </mapper>
