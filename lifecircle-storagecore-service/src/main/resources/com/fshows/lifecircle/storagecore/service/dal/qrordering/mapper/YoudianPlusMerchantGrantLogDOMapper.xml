<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.YoudianPlusMerchantGrantLogDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.YoudianPlusMerchantGrantLogDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="BELONG_NAME" property="belongName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_NAME" property="merchantName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OWN_RUN" property="ownRun" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="BELONG_ID" property="belongId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="OPEN_MONTH" property="openMonth" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ACTION_TYPE" property="actionType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="BELONG_TYPE" property="belongType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="OPERATOR_ID" property="operatorId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="OPEN_TIME" property="openTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BELONG_NAME`,`MERCHANT_NAME`,`OPERATOR_NAME`,`OWN_RUN`,`DEL_FLAG`,`BELONG_ID`,`OPEN_MONTH`,`ACTION_TYPE`,`BELONG_TYPE`,`MERCHANT_ID`,`OPERATOR_ID`,`OPEN_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:FS_YOUDIAN_PLUS_MERCHANT_GRANT_LOG-->
    <insert id="insert">
        INSERT INTO FS_YOUDIAN_PLUS_MERCHANT_GRANT_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="belongName != null">`BELONG_NAME`,</if>
            <if test="merchantName != null">`MERCHANT_NAME`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="ownRun != null">`OWN_RUN`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="belongId != null">`BELONG_ID`,</if>
            <if test="openMonth != null">`OPEN_MONTH`,</if>
            <if test="actionType != null">`ACTION_TYPE`,</if>
            <if test="belongType != null">`BELONG_TYPE`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="openTime != null">`OPEN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="belongName != null">#{belongName,jdbcType=VARCHAR},</if>
            <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="ownRun != null">#{ownRun,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="belongId != null">#{belongId,jdbcType=INTEGER},</if>
            <if test="openMonth != null">#{openMonth,jdbcType=INTEGER},</if>
            <if test="actionType != null">#{actionType,jdbcType=TINYINT},</if>
            <if test="belongType != null">#{belongType,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=INTEGER},</if>
            <if test="openTime != null">#{openTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--导出友店权限开通日志信息-->
    <select id="getMerchantGrantLogExport" resultMap="BaseResultMap">
        SELECT
        <include refid="Base_Column_List"/>
        FROM
        fs_youdian_plus_merchant_grant_log
        WHERE
        del_flag = 0
        <if test="merchantId !=null">
            AND merchant_id = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="merchantName !=null">
            AND merchant_name LIKE CONCAT(#{merchantName,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName !=null">
            AND belong_name LIKE CONCAT(#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="operatorName !=null">
            AND operator_name LIKE CONCAT(#{operatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="actionType !=null">
            AND action_type = #{actionType,jdbcType=INTEGER}
        </if>
        <if test="belongType !=null">
            AND belong_type = #{belongType,jdbcType=INTEGER}
        </if>
        <if test="openMonth !=null">
            AND open_month = #{openMonth,jdbcType=INTEGER}
        </if>
        <if test="ownRun !=null">
            AND own_run = #{ownRun,jdbcType=INTEGER}
        </if>
        <if test="startTime !=null and endTime!=null">
            AND open_time BETWEEN #{startTime,jdbcType=VARCHAR}
            AND #{endTime,jdbcType=VARCHAR}
        </if>
        order by update_time desc
    </select>
</mapper>
