<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmLifecircleGroupMealMerchantApplyDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmLifecircleGroupMealMerchantApplyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CONTACT_NAME" property="contactName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LOGIC_GROUP_ID" property="logicGroupId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AGREEMENT_ONE_PIC" property="agreementOnePic" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AGREEMENT_TWO_PIC" property="agreementTwoPic" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CONTACT_POSITION" property="contactPosition" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CONTACT_TELEPHONE" property="contactTelephone" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ALIPAY_LEGAL_PERSON_ACCOUNT" property="alipayLegalPersonAccount" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PV_DAY" property="pvDay" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="APPLY_TIME" property="applyTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="AUDIT_TIME" property="auditTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="PV_DAY_MEAL" property="pvDayMeal" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EXPECT_TIME" property="expectTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EQUIPMENT_NUM" property="equipmentNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="GROUP_MEAL_STATUS" property="groupMealStatus" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`REASON`,`ADDRESS`,`COMPANY`,`USERNAME`,`LICENSE_PIC`,`CONTACT_NAME`,`LOGIC_GROUP_ID`,`AGREEMENT_ONE_PIC`,`AGREEMENT_TWO_PIC`,`CONTACT_POSITION`,`CONTACT_TELEPHONE`,`ALIPAY_LEGAL_PERSON_ACCOUNT`,`IS_DEL`,`PV_DAY`,`APPLY_TIME`,`AUDIT_TIME`,`PV_DAY_MEAL`,`EXPECT_TIME`,`MERCHANT_ID`,`EQUIPMENT_NUM`,`GROUP_MEAL_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--insert:TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY-->
        <insert id="insert">
            INSERT INTO TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="reason != null">`REASON`,</if>
                <if test="address != null">`ADDRESS`,</if>
                <if test="company != null">`COMPANY`,</if>
                <if test="username != null">`USERNAME`,</if>
                <if test="licensePic != null">`LICENSE_PIC`,</if>
                <if test="contactName != null">`CONTACT_NAME`,</if>
                <if test="logicGroupId != null">`LOGIC_GROUP_ID`,</if>
                <if test="agreementOnePic != null">`AGREEMENT_ONE_PIC`,</if>
            <if test="agreementTwoPic != null">`AGREEMENT_TWO_PIC`,</if>
            <if test="contactPosition != null">`CONTACT_POSITION`,</if>
            <if test="contactTelephone != null">`CONTACT_TELEPHONE`,</if>
            <if test="alipayLegalPersonAccount != null">`ALIPAY_LEGAL_PERSON_ACCOUNT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="pvDay != null">`PV_DAY`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="auditTime != null">`AUDIT_TIME`,</if>
            <if test="pvDayMeal != null">`PV_DAY_MEAL`,</if>
            <if test="expectTime != null">`EXPECT_TIME`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="equipmentNum != null">`EQUIPMENT_NUM`,</if>
            <if test="groupMealStatus != null">`GROUP_MEAL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
            <if test="logicGroupId != null">#{logicGroupId,jdbcType=VARCHAR},</if>
            <if test="agreementOnePic != null">#{agreementOnePic,jdbcType=VARCHAR},</if>
            <if test="agreementTwoPic != null">#{agreementTwoPic,jdbcType=VARCHAR},</if>
            <if test="contactPosition != null">#{contactPosition,jdbcType=VARCHAR},</if>
            <if test="contactTelephone != null">#{contactTelephone,jdbcType=VARCHAR},</if>
            <if test="alipayLegalPersonAccount != null">#{alipayLegalPersonAccount,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="pvDay != null">#{pvDay,jdbcType=INTEGER},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="auditTime != null">#{auditTime,jdbcType=INTEGER},</if>
            <if test="pvDayMeal != null">#{pvDayMeal,jdbcType=INTEGER},</if>
            <if test="expectTime != null">#{expectTime,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="equipmentNum != null">#{equipmentNum,jdbcType=INTEGER},</if>
            <if test="groupMealStatus != null">#{groupMealStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--分页查询crm后台商户审核列表 pageCount-->
        <select id="findGroupMealMerchantListCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY gma
            LEFT JOIN TP_USERS users
            ON gma.merchant_id = users.`ID`
        <where>
            gma.is_del = 0
            <if test="username != null and username != '' ">
                AND gma.`username` like CONCAT ( #{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="company != null and company != '' ">
                AND gma.`company` like CONCAT ( #{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantId != null and merchantId != 0 ">
                AND gma.`merchant_id` = #{merchantId,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.apply_time BETWEEN #{applyStartTime,jdbcType=INTEGER} and #{applyEndTime,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.audit_time BETWEEN #{auditStartTime,jdbcType=INTEGER} and #{auditEndTime,jdbcType=INTEGER}
            </if>
            <if test="groupMealStatusList != null and groupMealStatusList.size &gt;0">
                AND gma.GROUP_MEAL_STATUS IN
                <foreach close=")" collection="groupMealStatusList" index="index" item="groupMealStatus" open="("
                         separator=",">
                    #{groupMealStatus,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND
                users.belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="(" separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="salesManUserIdList != null and salesManUserIdList.size &gt;0">
                and
                users.salesman IN
                <foreach close=")" collection="salesManUserIdList" index="index" item="salesManId" open="("
                         separator=",">
                    #{salesManId,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>

        </select>
        <!--分页查询crm后台商户审核列表 pageResult-->
        <select id="findGroupMealMerchantListResult"
                resultType="com.fshows.lifecircle.storagecore.service.domain.dto.GroupMealMerchantDetailDTO">
            SELECT /*MS-TP-LIFECIRCLE-GROUP-MEAL-MERCHANT-APPLY-FINDGROUPMEALMERCHANTLIST*/ users.`ID` as merchantId,
            users.`BELONG` as agentId,
            users.`SALESMAN`as salesmanId,
            gma.username AS username,
            gma.company as company,
            gma.address as address,
            gma.AUDIT_TIME as auditTime ,
            gma.APPLY_TIME as applyTime,
            gma.create_time as firstApplyTime,
            gma.LICENSE_PIC as licensePic,
            gma.AGREEMENT_ONE_PIC as agreementOnePic,
            gma.AGREEMENT_TWO_PIC as agreementTwoPic,
            gma.GROUP_MEAL_STATUS as groupMealStatus,
            gma.CONTACT_POSITION as contactPosition,
            gma.CONTACT_NAME as contactName,
            gma.CONTACT_TELEPHONE as contactTelephone,
            gma.ALIPAY_LEGAL_PERSON_ACCOUNT as alipayLegalPersonAccount,
            gma.PV_DAY as pvDay,
            gma.PV_DAY_MEAL as pvDayMeal,
            gma.EXPECT_TIME as expectTime,
        gma.EQUIPMENT_NUM as equipmentNum,
        gma.reason as reason
        FROM TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY gma
        LEFT JOIN TP_USERS users
        ON gma.merchant_id = users.`ID`
        <where>
            gma.is_del = 0
            <if test="username != null and username != '' ">
                AND gma.`username` like CONCAT ( #{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="company != null and company != '' ">
                AND gma.`company` like CONCAT ( #{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantId != null and merchantId != 0 ">
                AND gma.`merchant_id` = #{merchantId,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.apply_time BETWEEN #{applyStartTime,jdbcType=INTEGER} and #{applyEndTime,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.audit_time BETWEEN #{auditStartTime,jdbcType=INTEGER} and #{auditEndTime,jdbcType=INTEGER}
            </if>
            <if test="groupMealStatusList != null and groupMealStatusList.size &gt;0">
                AND gma.GROUP_MEAL_STATUS IN
                <foreach close=")" collection="groupMealStatusList" index="index" item="groupMealStatus" open="("
                         separator=",">
                    #{groupMealStatus,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND
                users.belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="(" separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="salesManUserIdList != null and salesManUserIdList.size &gt;0">
                and
                users.salesman IN
                <foreach close=")" collection="salesManUserIdList" index="index" item="salesManId" open="("
                         separator=",">
                    #{salesManId,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
            ORDER BY gma.apply_time DESC, gma.CREATE_TIME DESC
            limit #{startRow},#{limit}
        </select>
    </mapper>
