<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingTableCodeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingTableCodeDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QRCODE" property="qrcode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TABLE_CODE" property="tableCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TABLE_CODE_ID" property="tableCodeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_STATUS" property="bindStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TABLE_CODE_DISPLAY" property="tableCodeDisplay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QRCODE`,`STORE_ID`,`TABLE_CODE`,`TABLE_CODE_ID`,`DEL_FLAG`,`BIND_STATUS`,`TABLE_CODE_DISPLAY`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_QRORDERING_TABLE_CODE-->
            <insert id="insert" >
            INSERT INTO TP_QRORDERING_TABLE_CODE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="qrcode != null">`QRCODE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="tableCode != null">`TABLE_CODE`,</if>
        <if test="tableCodeId != null">`TABLE_CODE_ID`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="bindStatus != null">`BIND_STATUS`,</if>
        <if test="tableCodeDisplay != null">`TABLE_CODE_DISPLAY`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
        <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
        <if test="tableCode != null">#{tableCode,jdbcType=VARCHAR},</if>
        <if test="tableCodeId != null">#{tableCodeId,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
        <if test="tableCodeDisplay != null">#{tableCodeDisplay,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

    <!--insertBatch-->
    <insert id="insertBatch">
        INSERT INTO TP_QRORDERING_TABLE_CODE
        (`QRCODE`, `STORE_ID`, `TABLE_CODE_ID`, `BIND_STATUS`, `TABLE_CODE`) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.qrcode,jdbcType=VARCHAR}, #{item.storeId,jdbcType=VARCHAR}, #{item.tableCodeId,jdbcType=VARCHAR},
            #{item.bindStatus,jdbcType=TINYINT}, #{item.tableCode,jdbcType=VARCHAR})
        </foreach>
    </insert>
    </mapper>
