<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.FsaccountcenterDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 14 as fg,'TP_SHARE_MEMBER_WITHDRAW_EXCEPTION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REASON,TOKEN_NO,CASH_STATUS,FRONT_LOG_NO,SERIAL_NUMBER,NEW_SERIAL_NUMBER,CUSTOMER_ACCOUNT_ID,CUSTOMER_BIND_BANK_ID,FINISH_TIME,CREATE_TIME,UPDATE_TIME,FEE_AMOUNT,CASH_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_MEMBER_WITHDRAW_EXCEPTION'
            AND COLUMN_NAME in('ID','REASON','TOKEN_NO','CASH_STATUS','FRONT_LOG_NO','SERIAL_NUMBER','NEW_SERIAL_NUMBER','CUSTOMER_ACCOUNT_ID','CUSTOMER_BIND_BANK_ID','FINISH_TIME','CREATE_TIME','UPDATE_TIME','FEE_AMOUNT','CASH_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 29 as fg,'TP_SHARE_MEMBER_WITHDRAW' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REASON,REMARKS,TOKEN_NO,CASH_TYPE,NOTIFY_URL,CASH_STATUS,CUSTOMER_ID,FRONT_LOG_NO,OPERATOR_ID,OPERATOR_NAME,SERIAL_NUMBER,WITHDRAW_STATUS,OLD_SERIAL_NUMBER,OUT_SERIAL_NUMBER,WITHDRAW_VOUCHER,WITHDRAWALS_MODE,CUSTOMER_ACCOUNT_ID,CUSTOMER_BIND_BANK_ID,UID,AGENT_ID,BANK_TYPE,TRAN_TIME,FINISH_TIME,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME,FEE_AMOUNT,CASH_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_MEMBER_WITHDRAW'
            AND COLUMN_NAME in('ID','REASON','REMARKS','TOKEN_NO','CASH_TYPE','NOTIFY_URL','CASH_STATUS','CUSTOMER_ID','FRONT_LOG_NO','OPERATOR_ID','OPERATOR_NAME','SERIAL_NUMBER','WITHDRAW_STATUS','OLD_SERIAL_NUMBER','OUT_SERIAL_NUMBER','WITHDRAW_VOUCHER','WITHDRAWALS_MODE','CUSTOMER_ACCOUNT_ID','CUSTOMER_BIND_BANK_ID','UID','AGENT_ID','BANK_TYPE','TRAN_TIME','FINISH_TIME','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME','FEE_AMOUNT','CASH_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'TP_SHARE_RULE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SHARE_MEMBER_ID,IS_DEL,ENABLE,STORE_ID,BEGIN_TIME,CREATE_TIME,UPDATE_TIME,SHARE_PORTION,LAST_SHARE_PORTION' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_RULE'
            AND COLUMN_NAME in('ID','SHARE_MEMBER_ID','IS_DEL','ENABLE','STORE_ID','BEGIN_TIME','CREATE_TIME','UPDATE_TIME','SHARE_PORTION','LAST_SHARE_PORTION')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'TP_FBANK_AMOUNT_CHECK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,UID,CHECK_DAY,CHECK_TYPE,CHECK_STATUS,CREATE_TIME,UPDATE_TIME,FBANK_BALANCE,FUBEI_BALANCE,INCOME_TOTAL_AMOUNT,SETTLE_TOTAL_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_FBANK_AMOUNT_CHECK'
            AND COLUMN_NAME in('ID','UID','CHECK_DAY','CHECK_TYPE','CHECK_STATUS','CREATE_TIME','UPDATE_TIME','FBANK_BALANCE','FUBEI_BALANCE','INCOME_TOTAL_AMOUNT','SETTLE_TOTAL_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 59 as fg,'TP_CUSTOMER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,AREA,PHONE,ACTIVE,COUNTY,REASON,ADDRESS,PROVICE,LEGAL_MAIL,LICENSE_NO,MINI_PHONE,REG_STATUS,SHORT_NAME,CPR_REG_NM_CN,CUSTOMER_ID,SOURCE_TYPE,EXCEL_OSS_URL,LEGAL_CERT_ID,LEGAL_PERSON,UNION_PAY_MCC,CUSTOMER_NAME,CUSTOMER_TYPE,IMPORT_REASON,IMPORT_STATUS,LICENSE_PHOTO,SHOP_FRONT_PIC,HAND_IDCARD_PIC,LEGAL_CERT_TYPE,SHOP_INSIDE_PIC,LICENSE_EXPIRES,MERCHANT_ORDER_SN,AUTHORIZATION_PIC,RESIDENCE_ADDRESS,SETTLEMENT_CERT_ID,SETTLEMENT_PERSON,LEGAL_PERSON_LIC_ENT,LEGAL_PERSON_LIC_STT,LICENSE_EXPIRES_END,SHOP_CHECKSTAND_PIC,LEGAL_BACK_PERSON_PIC,LEGAL_FRONT_PERSON_PIC,LEGAL_PERSON_LIC_EFFECT,LICENSE_EXPIRES_BEGAIN,SETTLEMENT_PERSON_LIC_ENT,SETTLEMENT_PERSON_LIC_STT,SETTLEMENT_BACK_PERSON_PIC,SETTLEMENT_FRONT_PERSON_PIC,SETTLEMENT_PERSON_LIC_EFFECT,UID,AGENT_ID,STORE_ID,BANK_TYPE,SETTLE_MODE,UNITY_CAT_ID,CHANNEL_TYPE,CREATE_TIME,UPDATE_TIME,PETTY_CASH,TOTAL_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CUSTOMER'
            AND COLUMN_NAME in('ID','AREA','PHONE','ACTIVE','COUNTY','REASON','ADDRESS','PROVICE','LEGAL_MAIL','LICENSE_NO','MINI_PHONE','REG_STATUS','SHORT_NAME','CPR_REG_NM_CN','CUSTOMER_ID','SOURCE_TYPE','EXCEL_OSS_URL','LEGAL_CERT_ID','LEGAL_PERSON','UNION_PAY_MCC','CUSTOMER_NAME','CUSTOMER_TYPE','IMPORT_REASON','IMPORT_STATUS','LICENSE_PHOTO','SHOP_FRONT_PIC','HAND_IDCARD_PIC','LEGAL_CERT_TYPE','SHOP_INSIDE_PIC','LICENSE_EXPIRES','MERCHANT_ORDER_SN','AUTHORIZATION_PIC','RESIDENCE_ADDRESS','SETTLEMENT_CERT_ID','SETTLEMENT_PERSON','LEGAL_PERSON_LIC_ENT','LEGAL_PERSON_LIC_STT','LICENSE_EXPIRES_END','SHOP_CHECKSTAND_PIC','LEGAL_BACK_PERSON_PIC','LEGAL_FRONT_PERSON_PIC','LEGAL_PERSON_LIC_EFFECT','LICENSE_EXPIRES_BEGAIN','SETTLEMENT_PERSON_LIC_ENT','SETTLEMENT_PERSON_LIC_STT','SETTLEMENT_BACK_PERSON_PIC','SETTLEMENT_FRONT_PERSON_PIC','SETTLEMENT_PERSON_LIC_EFFECT','UID','AGENT_ID','STORE_ID','BANK_TYPE','SETTLE_MODE','UNITY_CAT_ID','CHANNEL_TYPE','CREATE_TIME','UPDATE_TIME','PETTY_CASH','TOTAL_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'TP_CUSTOMER_BALANCE_CHANGE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORDER_SN,CHANGE_TYPE,CUSTOMER_ID,CHANGE_REMARK,CUSTOMER_ACCOUNT_ID,CREATE_TIME,UPDATE_TIME,AFTER_PETTY_CASH,FRONT_PETTY_CASH,CHANGE_PETTY_CASH,AFTER_TOTAL_BALANCE,FRONT_TOTAL_BALANCE,CHANGE_TOTAL_BALANCE,AFTER_WITHDRAWABLE_BALANCE,FRONT_WITHDRAWABLE_BALANCE,CHANGE_WITHDRAWABLE_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CUSTOMER_BALANCE_CHANGE_LOG'
            AND COLUMN_NAME in('ID','ORDER_SN','CHANGE_TYPE','CUSTOMER_ID','CHANGE_REMARK','CUSTOMER_ACCOUNT_ID','CREATE_TIME','UPDATE_TIME','AFTER_PETTY_CASH','FRONT_PETTY_CASH','CHANGE_PETTY_CASH','AFTER_TOTAL_BALANCE','FRONT_TOTAL_BALANCE','CHANGE_TOTAL_BALANCE','AFTER_WITHDRAWABLE_BALANCE','FRONT_WITHDRAWABLE_BALANCE','CHANGE_WITHDRAWABLE_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 18 as fg,'TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REASON,TOKEN_NO,CASH_STATUS,FRONT_LOG_NO,OPERATOR_ID,OPERATOR_NAME,SERIAL_NUMBER,NEW_SERIAL_NUMBER,WITHDRAW_VOUCHER,CUSTOMER_ACCOUNT_ID,CUSTOMER_BIND_BANK_ID,BANK_TYPE,FINISH_TIME,CREATE_TIME,UPDATE_TIME,FEE_AMOUNT,CASH_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT'
            AND COLUMN_NAME in('ID','REASON','TOKEN_NO','CASH_STATUS','FRONT_LOG_NO','OPERATOR_ID','OPERATOR_NAME','SERIAL_NUMBER','NEW_SERIAL_NUMBER','WITHDRAW_VOUCHER','CUSTOMER_ACCOUNT_ID','CUSTOMER_BIND_BANK_ID','BANK_TYPE','FINISH_TIME','CREATE_TIME','UPDATE_TIME','FEE_AMOUNT','CASH_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 26 as fg,'TP_SHARE_GROUP' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TOKEN,ACTIVE,REASON,GROUP_ID,SHARE_MODE,SHARE_TYPE,SETTLE_TYPE,SOURCE_TYPE,GROUP_STATUS,PROTOCOL_PIC,IMPORT_REASON,IMPORT_STATUS,SHARE_GROUP_ID,SETTLE_CYCLE_END,SHARE_GROUP_NAME,MERCHANT_ORDER_SN,REQ_SERIAL_NUMBER,SETTLE_CYCLE_START,UID,PASS_NUM,BANK_TYPE,CHANNEL_TYPE,SHARE_MEMBER_NUM,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_GROUP'
            AND COLUMN_NAME in('ID','TOKEN','ACTIVE','REASON','GROUP_ID','SHARE_MODE','SHARE_TYPE','SETTLE_TYPE','SOURCE_TYPE','GROUP_STATUS','PROTOCOL_PIC','IMPORT_REASON','IMPORT_STATUS','SHARE_GROUP_ID','SETTLE_CYCLE_END','SHARE_GROUP_NAME','MERCHANT_ORDER_SN','REQ_SERIAL_NUMBER','SETTLE_CYCLE_START','UID','PASS_NUM','BANK_TYPE','CHANNEL_TYPE','SHARE_MEMBER_NUM','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 33 as fg,'TP_SHARE_PAY_BILL_EXCEPTION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,MNO,TOKEN,REASON,ORDER_ID,REMARKS,BILL_DATE,SHARE_ROLE,SHARE_TYPE,CUSTOMER_ID,SETTLE_TYPE,SHARE_BILL_NO,SHARE_STATUS,TARGET_IN_MNO,CHANNEL_BILL_NO,SHARE_MEMBER_ID,SHARE_PAY_SUB_NO,BANK_PAY_PURPOSE,WITHDRAW_STATUS,REQ_SERIAL_NUMBER,WITHDRAW_SERIAL_NUMBER,UID,CREATE_TIME,FINISH_TIME,UPDATE_TIME,SETTLE_END_TIME,SETTLE_START_TIME,SET_AMT,FEE_AMOUNT,SET_FEE_AMT,ORDER_PRICE,SHARE_PRICE,REAL_SHARE_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_PAY_BILL_EXCEPTION'
            AND COLUMN_NAME in('ID','MNO','TOKEN','REASON','ORDER_ID','REMARKS','BILL_DATE','SHARE_ROLE','SHARE_TYPE','CUSTOMER_ID','SETTLE_TYPE','SHARE_BILL_NO','SHARE_STATUS','TARGET_IN_MNO','CHANNEL_BILL_NO','SHARE_MEMBER_ID','SHARE_PAY_SUB_NO','BANK_PAY_PURPOSE','WITHDRAW_STATUS','REQ_SERIAL_NUMBER','WITHDRAW_SERIAL_NUMBER','UID','CREATE_TIME','FINISH_TIME','UPDATE_TIME','SETTLE_END_TIME','SETTLE_START_TIME','SET_AMT','FEE_AMOUNT','SET_FEE_AMT','ORDER_PRICE','SHARE_PRICE','REAL_SHARE_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 28 as fg,'TP_MCH_SHARE_ACCOUNT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,MOBILE,COMPANY,MCHNT_CD,ACCOUNT_ID,ACCOUNT_NO,AGENT_NAME,MCHNT_NAME,ISS_INS_NAME,BANK_INTER_NO,ISS_CITY_NAME,SALESMAN_NAME,TRADE_MCHNT_CD,SUB_BRANCH_NAME,BANK_ACCOUNT_NAME,BANK_ACCOUNT_MCHNT_CD,UID,ROLE,IS_DEL,AGENT_ID,BANK_TYPE,ROLE_USER_ID,SALESMAN_ID,BANK_CHANNEL,ACCOUNT_ACTIVE_TIME,COOPERATION_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MCH_SHARE_ACCOUNT'
            AND COLUMN_NAME in('ID','MOBILE','COMPANY','MCHNT_CD','ACCOUNT_ID','ACCOUNT_NO','AGENT_NAME','MCHNT_NAME','ISS_INS_NAME','BANK_INTER_NO','ISS_CITY_NAME','SALESMAN_NAME','TRADE_MCHNT_CD','SUB_BRANCH_NAME','BANK_ACCOUNT_NAME','BANK_ACCOUNT_MCHNT_CD','UID','ROLE','IS_DEL','AGENT_ID','BANK_TYPE','ROLE_USER_ID','SALESMAN_ID','BANK_CHANNEL','ACCOUNT_ACTIVE_TIME','COOPERATION_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 6 as fg,'TP_FBANK_YESTERDAY_BALANCE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACCOUNT_NO,TRADE_DAY,CREATE_TIME,UPDATE_TIME,BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_FBANK_YESTERDAY_BALANCE'
            AND COLUMN_NAME in('ID','ACCOUNT_NO','TRADE_DAY','CREATE_TIME','UPDATE_TIME','BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 41 as fg,'TP_SHARE_PAY_BILL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TOKEN,REASON,SOURCE,ORDER_ID,REMARKS,VOUCHER,BILL_DATE,BIZ_SCENE,NOTIFY_URL,OUT_BIZ_KEY,SHARE_MODE,SHARE_ROLE,SHARE_TYPE,CUSTOMER_ID,OPERATOR_ID,SETTLE_TYPE,SHARE_BILL_NO,SHARE_STATUS,OPERATOR_NAME,CHANNEL_BILL_NO,SHARE_MEMBER_ID,SHARE_PAY_SUB_NO,WITHDRAW_STATUS,OUT_SERIAL_NUMBER,REQ_SERIAL_NUMBER,WITHDRAW_VOUCHER,SHARE_BATCH_BILL_NO,WITHDRAW_SERIAL_NUMBER,UID,BANK_TYPE,CREATE_TIME,FINISH_TIME,UPDATE_TIME,SETTLE_END_TIME,SETTLE_START_TIME,SHARE_FINISH_TIME,FEE_AMOUNT,ORDER_PRICE,SHARE_PRICE,REAL_SHARE_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_PAY_BILL'
            AND COLUMN_NAME in('ID','TOKEN','REASON','SOURCE','ORDER_ID','REMARKS','VOUCHER','BILL_DATE','BIZ_SCENE','NOTIFY_URL','OUT_BIZ_KEY','SHARE_MODE','SHARE_ROLE','SHARE_TYPE','CUSTOMER_ID','OPERATOR_ID','SETTLE_TYPE','SHARE_BILL_NO','SHARE_STATUS','OPERATOR_NAME','CHANNEL_BILL_NO','SHARE_MEMBER_ID','SHARE_PAY_SUB_NO','WITHDRAW_STATUS','OUT_SERIAL_NUMBER','REQ_SERIAL_NUMBER','WITHDRAW_VOUCHER','SHARE_BATCH_BILL_NO','WITHDRAW_SERIAL_NUMBER','UID','BANK_TYPE','CREATE_TIME','FINISH_TIME','UPDATE_TIME','SETTLE_END_TIME','SETTLE_START_TIME','SHARE_FINISH_TIME','FEE_AMOUNT','ORDER_PRICE','SHARE_PRICE','REAL_SHARE_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 18 as fg,'TP_CUSTOMER_ACCOUNT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVE,TOKEN_NO,CUSTOMER_ID,SETTLE_TYPE,SUB_AGENT_ID,OUT_ACCOUNT_ID,OUT_CUSTOMER_ID,OUT_MERCHANT_ID,BALANCE_ACCOUNT_ID,CUSTOMER_ACCOUNT_ID,CUSTOMER_BIND_BANK_ID,UNION_PAY_MERCHANT_ID,BANK_TYPE,CHANNEL_TYPE,CREATE_TIME,UPDATE_TIME,WITHDRAWABLE_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CUSTOMER_ACCOUNT'
            AND COLUMN_NAME in('ID','ACTIVE','TOKEN_NO','CUSTOMER_ID','SETTLE_TYPE','SUB_AGENT_ID','OUT_ACCOUNT_ID','OUT_CUSTOMER_ID','OUT_MERCHANT_ID','BALANCE_ACCOUNT_ID','CUSTOMER_ACCOUNT_ID','CUSTOMER_BIND_BANK_ID','UNION_PAY_MERCHANT_ID','BANK_TYPE','CHANNEL_TYPE','CREATE_TIME','UPDATE_TIME','WITHDRAWABLE_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'TP_SHARE_MEMBER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVE,GROUP_ID,OTHER_PIC,SHARE_DESC,USE_STATUS,CUSTOMER_ID,AUDIT_STATUS,PROTOCOL_PIC,SIGN_END_DATE,IMPORT_REASON,IMPORT_STATUS,REJECT_REASON,SHARE_MEMBER_ID,SIGN_START_DATE,RELATION_AUDIT_STATUS,JOIN_TYPE,AUDIT_TIME,SHARE_RELATION_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_SHARE_MEMBER'
            AND COLUMN_NAME in('ID','ACTIVE','GROUP_ID','OTHER_PIC','SHARE_DESC','USE_STATUS','CUSTOMER_ID','AUDIT_STATUS','PROTOCOL_PIC','SIGN_END_DATE','IMPORT_REASON','IMPORT_STATUS','REJECT_REASON','SHARE_MEMBER_ID','SIGN_START_DATE','RELATION_AUDIT_STATUS','JOIN_TYPE','AUDIT_TIME','SHARE_RELATION_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'TP_CUSTOMER_BIND_BANK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PHONE,ACTIVE,CARD_ID,CERT_ID,TOKEN_NO,BANK_CODE,BANK_NAME,CARD_NAME,CERT_TYPE,UNION_CODE,BRANCH_NAME,CUSTOMER_ID,CARD_BACK_PIC,CHECK_STATUS,BANK_ACCT_TYPE,CARD_FRONT_PIC,REMOVE_STATUS,CUSTOMER_BIND_BANK_ID,ACCOUNT_OPENING_PERMIT_PIC,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CUSTOMER_BIND_BANK'
            AND COLUMN_NAME in('ID','PHONE','ACTIVE','CARD_ID','CERT_ID','TOKEN_NO','BANK_CODE','BANK_NAME','CARD_NAME','CERT_TYPE','UNION_CODE','BRANCH_NAME','CUSTOMER_ID','CARD_BACK_PIC','CHECK_STATUS','BANK_ACCT_TYPE','CARD_FRONT_PIC','REMOVE_STATUS','CUSTOMER_BIND_BANK_ID','ACCOUNT_OPENING_PERMIT_PIC','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
