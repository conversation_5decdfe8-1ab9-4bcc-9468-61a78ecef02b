<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyLiquidationConfigDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyLiquidationConfigDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APP_ID" property="appId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PARTNERS" property="partners" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PHP_PUBLIC_KEY" property="phpPublicKey" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="JAVA_PUBLIC_KEY" property="javaPublicKey" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PHP_PRIVATE_KEY" property="phpPrivateKey" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="JAVA_PRIVATE_KEY" property="javaPrivateKey" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IMAGE_GATEWAY_URL" property="imageGatewayUrl" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="INNER_GATEWAY_URL" property="innerGatewayUrl" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LIQUIDATION_NAME" property="liquidationName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUTER_GATEWAY_URL" property="outerGatewayUrl" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`APP_ID`,`REMARK`,`PARTNERS`,`PHP_PUBLIC_KEY`,`JAVA_PUBLIC_KEY`,`PHP_PRIVATE_KEY`,`JAVA_PRIVATE_KEY`,`IMAGE_GATEWAY_URL`,`INNER_GATEWAY_URL`,`LIQUIDATION_NAME`,`OUTER_GATEWAY_URL`,`STATUS`,`PAY_TYPE`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--insert:TP_LIQUIDATION_CONFIG-->
        <insert id="insert">
            INSERT INTO TP_LIQUIDATION_CONFIG
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="appId != null">`APP_ID`,</if>
                <if test="remark != null">`REMARK`,</if>
                <if test="partners != null">`PARTNERS`,</if>
                <if test="phpPublicKey != null">`PHP_PUBLIC_KEY`,</if>
                <if test="javaPublicKey != null">`JAVA_PUBLIC_KEY`,</if>
                <if test="phpPrivateKey != null">`PHP_PRIVATE_KEY`,</if>
                <if test="javaPrivateKey != null">`JAVA_PRIVATE_KEY`,</if>
                <if test="imageGatewayUrl != null">`IMAGE_GATEWAY_URL`,</if>
            <if test="innerGatewayUrl != null">`INNER_GATEWAY_URL`,</if>
            <if test="liquidationName != null">`LIQUIDATION_NAME`,</if>
            <if test="outerGatewayUrl != null">`OUTER_GATEWAY_URL`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appId != null">#{appId,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="partners != null">#{partners,jdbcType=VARCHAR},</if>
            <if test="phpPublicKey != null">#{phpPublicKey,jdbcType=VARCHAR},</if>
            <if test="javaPublicKey != null">#{javaPublicKey,jdbcType=VARCHAR},</if>
            <if test="phpPrivateKey != null">#{phpPrivateKey,jdbcType=VARCHAR},</if>
            <if test="javaPrivateKey != null">#{javaPrivateKey,jdbcType=VARCHAR},</if>
            <if test="imageGatewayUrl != null">#{imageGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="innerGatewayUrl != null">#{innerGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="liquidationName != null">#{liquidationName,jdbcType=VARCHAR},</if>
            <if test="outerGatewayUrl != null">#{outerGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--获取支付通道配置列表-->
        <select id="findByLiquidationTypeList" resultMap="BaseResultMap">
            select /*MS-TP-LIQUIDATION-CONFIG-FINDBYLIQUIDATIONTYPELIST*/
            <include refid="Base_Column_List"/>
            from TP_LIQUIDATION_CONFIG
            where liquidation_type in
            <foreach close=")" collection="list" index="index" item="liquidationType" open="(" separator=",">
                #{liquidationType,jdbcType=TINYINT}
            </foreach>
        </select>
    </mapper>
