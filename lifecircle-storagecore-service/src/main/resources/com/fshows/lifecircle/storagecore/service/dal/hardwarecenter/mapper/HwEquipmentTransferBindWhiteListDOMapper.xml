<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentTransferBindWhiteListDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentTransferBindWhiteListDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AFTER_USER_ID" property="afterUserId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="BEFORE_USER_ID" property="beforeUserId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EFFECTIVE_STATUS" property="effectiveStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`INIT_SN`,`OPERATOR`,`OPERATOR_NAME`,`IS_DEL`,`AFTER_USER_ID`,`BEFORE_USER_ID`,`EFFECTIVE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST-->
        <insert id="insert">
            INSERT INTO HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="initSn != null">`INIT_SN`,</if>
                <if test="operator != null">`OPERATOR`,</if>
                <if test="operatorName != null">`OPERATOR_NAME`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="afterUserId != null">`AFTER_USER_ID`,</if>
                <if test="beforeUserId != null">`BEFORE_USER_ID`,</if>
                <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="afterUserId != null">#{afterUserId,jdbcType=INTEGER},</if>
            <if test="beforeUserId != null">#{beforeUserId,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--批量保存-->
        <select id="batchInsert" resultMap="BaseResultMap">
            insert into hw_equipment_transfer_bind_white_list
            <trim prefix="(" suffix=")" suffixOverrides=",">
                `INIT_SN`,
                `OPERATOR`,
                `OPERATOR_NAME`,
                `AFTER_USER_ID`,
                `BEFORE_USER_ID`,
                `EFFECTIVE_STATUS`,
            </trim>
            VALUES
            <foreach collection="list" item="item" separator=",">
                <trim prefix="(" suffix=")" suffixOverrides=",">
                    #{item.initSn,jdbcType=VARCHAR},
                    #{item.operator,jdbcType=VARCHAR},
                    #{item.operatorName,jdbcType=VARCHAR},
                    #{item.afterUserId,jdbcType=INTEGER},
                    #{item.beforeUserId,jdbcType=INTEGER},
                    #{item.effectiveStatus,jdbcType=TINYINT},
                </trim>
            </foreach>
        </select>

        <!--根据sn查询名单-->
        <select id="findByInitSn" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from hw_equipment_transfer_bind_white_list
            where is_del = 0
            and init_sn = #{initSn,jdbcType=VARCHAR}
            <if test="effectiveStatus != null">
                and effective_status = #{effectiveStatus, jdbcType=TINYINT}
            </if>
        </select>

        <!--根据sn查询名单-->
        <select id="findByInitSnList" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from hw_equipment_transfer_bind_white_list
            where is_del = 0
            and init_sn in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item,jdbcType=VARCHAR}
            </foreach>
            <if test="effectiveStatus != null">
                and effective_status = #{effectiveStatus, jdbcType=TINYINT}
            </if>
        </select>

        <!--批量修改-->
        <select id="batchUpdateByIdList" resultMap="BaseResultMap">
            update hw_equipment_transfer_bind_white_list
            set update_time = now()
            <if test="effectiveStatus != null">
                effective_status = #{effectiveStatus, jdbcType=TINYINT}
            </if>
            where id in
            <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
        </select>
    </mapper>
