<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtraSinanActivityRegistrationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityRegistrationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INDUSTRY" property="industry" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_IDS" property="storeIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_PHONE" property="cardPhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="POLICY_TYPE" property="policyType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_INFO" property="rejectInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_HEAD_IMG" property="storeHeadImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_MONEY_IMG" property="storeMoneyImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_NUMBER" property="activityNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_INSIDE_IMG" property="storeInsideImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DETAILED_ADDRESS" property="detailedAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_ADDRESS" property="merchantAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_TYPE_INDUSTRY_POLICY" property="activityTypeIndustryPolicy" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="IS_CARD_OPEN" property="isCardOpen" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CARD_SETTLE_TYPE" property="cardSettleType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ACTIVITY_REGISTRATION_TYPE" property="activityRegistrationType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="EVERYDAY_TRADE" property="everydayTrade" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="siNanActivityMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.SiNanActivityListDO">

            <result column="company" property="company" javaType="String"/>

            <result column="username" property="username" javaType="String"/>

            <result column="uid" property="uid" javaType="Integer"/>

            <result column="activity_status" property="activityStatus" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
        `ID`
        ,`COMPANY`,`INDUSTRY`,`STORE_IDS`,`USERNAME`,`CARD_PHONE`,`MERCHANT_NO`,`POLICY_TYPE`,`REJECT_INFO`,`STORE_HEAD_IMG`,`STORE_MONEY_IMG`,`ACTIVITY_NUMBER`,`STORE_INSIDE_IMG`,`DETAILED_ADDRESS`,`MERCHANT_ADDRESS`,`ACTIVITY_TYPE_INDUSTRY_POLICY`,`UID`,`BELONG`,`STORE_ID`,`IS_CARD_OPEN`,`ACTIVITY_STATUS`,`CARD_SETTLE_TYPE`,`ACTIVITY_REGISTRATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`EVERYDAY_TRADE`
    </sql>

    <!--getSinanCrmActivityInfoByUsername pageResult-->
    <select id="getSinanCrmActivityInfoByUsernameResult" resultMap="BaseResultMap">
        select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYUSERNAME*/
        <include refid="Base_Column_List"/>
        from lm_sinan_activity_registration
        <where>
            <if test="searchValue!=null and searchValue!='' ">
                and username LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!--getSinanCrmActivityInfoByMerchantNo pageResult-->
    <select id="getSinanCrmActivityInfoByMerchantNoResult" resultMap="BaseResultMap">
        select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYMERCHANTNO*/
        <include refid="Base_Column_List"/>
        from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!--getSinanCrmActivityInfoByBelong pageResult-->
    <select id="getSinanCrmActivityInfoByBelongResult" resultMap="BaseResultMap">
        select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYBELONG*/
        <include refid="Base_Column_List"/>
        from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </select>

    <!--getSinanCrmActivityInfoByUid pageResult-->
    <select id="getSinanCrmActivityInfoByUidResult" resultMap="BaseResultMap">
        select /*MS-LM-SINAN-ACTIVITY-REGISTRATION-GETSINANCRMACTIVITYINFOBYUID*/
        <include refid="Base_Column_List"/>
        from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
            </select>
    </mapper>
