<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingCustomerDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingCustomerDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CITY" property="city" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CODE" property="code" jdbcType="CHAR"
                javaType="String"/>

        <result column="OPEN_ID" property="openId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="COUNTRY" property="country" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UNION_ID" property="unionId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="NICK_NAME" property="nickName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PROVINCE" property="province" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SUB_APP_ID" property="subAppId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="AVATAR_URL" property="avatarUrl" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PHONE_NUMBER" property="phoneNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TRADE_OPEN_ID" property="tradeOpenId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="HAVE_CERTIFIED" property="haveCertified" jdbcType="CHAR"
                javaType="String"/>

        <result column="GENDER" property="gender" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AUTH_STATUS" property="authStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CODE_INVALID" property="codeInvalid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CUSTOMER_TYPE" property="customerType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="MEMBER_USER_ID" property="memberUserId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CUSTOMER_SOURCE" property="customerSource" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="INDEX_BARCODE_FRAME" property="indexBarcodeFrame" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AUTH_TIME" property="authTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CITY`,`CODE`,`OPEN_ID`,`COUNTRY`,`UNION_ID`,`NICK_NAME`,`PROVINCE`,`SUB_APP_ID`,`AVATAR_URL`,`CUSTOMER_ID`,`PHONE_NUMBER`,`TRADE_OPEN_ID`,`HAVE_CERTIFIED`,`GENDER`,`DEL_FLAG`,`AUTH_STATUS`,`MERCHANT_ID`,`CODE_INVALID`,`CUSTOMER_TYPE`,`MEMBER_USER_ID`,`CUSTOMER_SOURCE`,`INDEX_BARCODE_FRAME`,`AUTH_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_QRORDERING_CUSTOMER-->
    <insert id="insert">
        INSERT INTO TP_QRORDERING_CUSTOMER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="code != null">`CODE`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="country != null">`COUNTRY`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="nickName != null">`NICK_NAME`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="subAppId != null">`SUB_APP_ID`,</if>
            <if test="avatarUrl != null">`AVATAR_URL`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="phoneNumber != null">`PHONE_NUMBER`,</if>
            <if test="tradeOpenId != null">`TRADE_OPEN_ID`,</if>
            <if test="haveCertified != null">`HAVE_CERTIFIED`,</if>
            <if test="gender != null">`GENDER`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="codeInvalid != null">`CODE_INVALID`,</if>
            <if test="customerType != null">`CUSTOMER_TYPE`,</if>
            <if test="memberUserId != null">`MEMBER_USER_ID`,</if>
            <if test="customerSource != null">`CUSTOMER_SOURCE`,</if>
            <if test="indexBarcodeFrame != null">`INDEX_BARCODE_FRAME`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>
            <if test="code != null">#{code,jdbcType=CHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="country != null">#{country,jdbcType=VARCHAR},</if>
            <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
            <if test="nickName != null">#{nickName,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="subAppId != null">#{subAppId,jdbcType=VARCHAR},</if>
            <if test="avatarUrl != null">#{avatarUrl,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="phoneNumber != null">#{phoneNumber,jdbcType=VARCHAR},</if>
            <if test="tradeOpenId != null">#{tradeOpenId,jdbcType=VARCHAR},</if>
            <if test="haveCertified != null">#{haveCertified,jdbcType=CHAR},</if>
            <if test="gender != null">#{gender,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="codeInvalid != null">#{codeInvalid,jdbcType=INTEGER},</if>
            <if test="customerType != null">#{customerType,jdbcType=TINYINT},</if>
            <if test="memberUserId != null">#{memberUserId,jdbcType=INTEGER},</if>
            <if test="customerSource != null">#{customerSource,jdbcType=TINYINT},</if>
            <if test="indexBarcodeFrame != null">#{indexBarcodeFrame,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--查询顾客-->
    <select id="listCustomerByCustomerId" resultMap="BaseResultMap">
        SELECT /*MS-TP-QRORDERING-CUSTOMER-LISTCUSTOMERBYCUSTOMERID*/
        <include refid="Base_Column_List"/>
        FROM tp_qrordering_customer
        WHERE customer_id IN
        <foreach collection="list" item="customerId" open="(" close=")" separator=",">
            #{customerId,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </select>
</mapper>
