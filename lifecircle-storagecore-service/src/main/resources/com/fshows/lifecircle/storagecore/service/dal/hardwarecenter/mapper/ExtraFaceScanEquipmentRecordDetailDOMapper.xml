<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.ExtraFaceScanEquipmentRecordDetailDOMapper">

    <resultMap id="ScanFaceDetailMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ScanFaceDetailDO">
        <id column="equipment_sn" property="equipmentSn" jdbcType="VARCHAR" javaType="String"/>
        <id column="equipment_dau" property="equipmentDau" jdbcType="INTEGER" javaType="Integer"/>
        <id column="transaction_num" property="transactionNum" jdbcType="INTEGER" javaType="Integer"/>
        <id column="transaction_money" property="transactionMoney" jdbcType="DECIMAL" javaType="BigDecimal"/>
        <id column="code_scan_num" property="codeScanNum" jdbcType="INTEGER" javaType="Integer"/>
        <id column="face_scan_dau" property="faceScanDau" jdbcType="INTEGER" javaType="Integer"/>
    </resultMap>

    <select id="getScanFaceDetailBySystemSn" resultMap="ScanFaceDetailMap">
        SELECT
        equipment_sn,
        sum(equipment_dau) as equipment_dau,
        sum(transaction_num) as transaction_num,
        sum(transaction_money) as transaction_money,
        sum(code_scan_num) as code_scan_num,
        sum(face_scan_dau) AS face_scan_dau
        from tp_face_scan_equipment_record_detail
        where equipment_sn in
        <foreach collection="list" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        <if test="transactionStartTime !=null and transactionEndTime!=null">
            and transaction_time BETWEEN #{transactionStartTime,jdbcType=TIMESTAMP} AND
            #{transactionEndTime,jdbcType=TIMESTAMP}
        </if>
        GROUP BY equipment_sn
    </select>

    <select id="getScanFaceDetailBySystemSnForCount" resultMap="ScanFaceDetailMap">
        SELECT
        equipment_sn,
        sum(equipment_dau) as equipment_dau,
        sum(transaction_num) as transaction_num,
        sum(transaction_money) as transaction_money,
        sum(code_scan_num) as code_scan_num,
        sum(face_scan_dau) AS face_scan_dau
        from tp_face_scan_equipment_record_detail
        where equipment_sn in
        <foreach collection="list" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        <if test="transactionStartTime !=null and transactionEndTime!=null">
            and transaction_time BETWEEN #{transactionStartTime,jdbcType=TIMESTAMP} AND
            #{transactionEndTime,jdbcType=TIMESTAMP}
        </if>
    </select>
</mapper>
