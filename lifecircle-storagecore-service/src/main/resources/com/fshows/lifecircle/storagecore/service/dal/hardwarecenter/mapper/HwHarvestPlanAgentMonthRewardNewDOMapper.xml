<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwHarvestPlanAgentMonthRewardNewDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwHarvestPlanAgentMonthRewardNewDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PT_MONTH" property="ptMonth" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN" property="salesman" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUDIT_STATUS" property="auditStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRODUCT_TYPE" property="productType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMISSION_TYPE" property="commissionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PROCUREMENT_TIME" property="procurementTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STATISTICS_MONTH" property="statisticsMonth" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="MONTH_AMOUNT" property="monthAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="MONTH_REWARD" property="monthReward" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ACTIVITY_ID`,`INIT_SN`,`PT_MONTH`,`UID`,`IS_DEL`,`BELONG`,`MARKET_ID`,`SALESMAN`,`AUDIT_STATUS`,`EQUIPMENT_ID`,`PRODUCT_TYPE`,`COMMISSION_TYPE`,`PROCUREMENT_TIME`,`STATISTICS_MONTH`,`CREATE_TIME`,`UPDATE_TIME`,`MONTH_AMOUNT`,`MONTH_REWARD`
    </sql>


            <!--insert:HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW-->
            <insert id="insert" >
                    INSERT INTO HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="statisticsMonth != null">`STATISTICS_MONTH`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="monthAmount != null">`MONTH_AMOUNT`,</if>
            <if test="monthReward != null">`MONTH_REWARD`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="statisticsMonth != null">#{statisticsMonth,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="monthAmount != null">#{monthAmount,jdbcType=DECIMAL},</if>
            <if test="monthReward != null">#{monthReward,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--分页查询代理商月佣金 pageCount-->
            <select id="findMonthRewardListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM

        HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW t1
        LEFT JOIN hw_equipment t2 ON t2.id = t1.equipment_id
        LEFT JOIN hw_goods_activity_relation t3 ON t1.init_sn = t3.id
        LEFT JOIN hw_shop_goods_spu t4 ON t3.goods_id = t4.goods_spu_id
        WHERE t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="productType != null">
            and t1.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test="auditStatus != null">
            and t1.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="goodsIdList != null and goodsIdList.size() &gt; 0 ">
            and t3.goods_id in
            <foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
            and t1.belong in
            <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                #{belong,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="salesmanIdList != null and salesmanIdList.size() &gt; 0">
            and t1.salesman in
            <foreach collection="salesmanIdList" item="salesman" open="(" close=")" separator=",">
                #{salesman,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
            and t1.uid in
            <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="activityIdList != null and activityIdList.size() &gt; 0 ">
            and t1.activity_id IN
            <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                #{activityId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="marketIdList != null and marketIdList.size() &gt; 0 ">
            and t1.market_id in
            <foreach collection="marketIdList" item="market" open="(" close=")" separator=",">
                #{market,jdbcType=INTEGER}
            </foreach>
        </if>
        and t1.is_del = 0
        
            </select>
            <!--分页查询代理商月佣金 pageResult-->
            <select id="findMonthRewardListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.RewardDTO">
                    SELECT
        t1.statistics_month as statistics,
        t1.init_sn as equipmentSn,
        t2.equipment_model as equipmentModel,
        cast(t1.month_amount as char) tradeAmount,
        cast(round(t1.month_reward,2) as char) awardAmount,
        t1.uid as merchantId,
        t1.belong as agentId,
        t1.salesman as salesmanId,
        t1.market_id as marketId,
        t1.commission_type as commissionType,
        t1.audit_status as auditStatus,
        t1.product_type as productType,
        t1.equipment_id,
        t1.procurement_time as procurementTime,
        t3.goods_id as goodsId,
        t4.goods_name as goodsName
        FROM
        HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW t1
        LEFT JOIN hw_equipment t2 ON t2.id = t1.equipment_id
        LEFT JOIN hw_goods_activity_relation t3 ON t1.init_sn = t3.id
        LEFT JOIN hw_shop_goods_spu t4 ON t3.goods_id = t4.goods_spu_id
        WHERE t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="productType != null">
            and t1.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test="auditStatus != null">
            and t1.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        <if test="goodsIdList != null and goodsIdList.size() &gt; 0 ">
            and t3.goods_id in
            <foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
            and t1.belong in
            <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                #{belong,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="salesmanIdList != null and salesmanIdList.size() &gt; 0">
            and t1.salesman in
            <foreach collection="salesmanIdList" item="salesman" open="(" close=")" separator=",">
                #{salesman,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
            and t1.uid in
            <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="activityIdList != null and activityIdList.size() &gt; 0 ">
            and t1.activity_id IN
            <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                #{activityId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="marketIdList != null and marketIdList.size() &gt; 0 ">
            and t1.market_id in
            <foreach collection="marketIdList" item="market" open="(" close=")" separator=",">
                #{market,jdbcType=INTEGER}
            </foreach>
        </if>
        and t1.is_del = 0
        ORDER BY t1.`statistics_month` DESC , t1.`month_amount` DESC
            limit #{startRow},#{limit}
            </select>

            <!--查询单设备，单月，单代理商奖励佣金之和-->
            <select id="getMonthTotalReward" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.TotalRewardDTO">
                    select
        t1.init_sn as equipmentSn,
        t1.belong as agentId,
        t1.salesman as salesmanId,
        round(SUM(t1.month_reward),2) as totalReward
        from
        HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW t1
        LEFT JOIN hw_goods_activity_relation t2 ON t1.init_sn = t2.id
        where
        t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
            and t1.belong in
            <foreach collection="agentIdList" item="agentId" open="(" close=")" separator=",">
                #{agentId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="salesmanIdList != null and salesmanIdList.size() &gt; 0">
            and t1.salesman in
            <foreach collection="salesmanIdList" item="salesman" open="(" close=")" separator=",">
                #{salesman,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="productType != null">
            and t1.product_type = #{productType,jdbcType=INTEGER}
        </if>
        <if test="goodsIdList != null and goodsIdList.size() &gt; 0 ">
            and t2.goods_id in
            <foreach collection="goodsIdList" item="goodsId" open="(" close=")" separator=",">
                #{goodsId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
            and t1.uid in
            <foreach collection="merchantIdList" item="merchantId" open="(" close=")" separator=",">
                #{merchantId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="activityIdList != null and activityIdList.size() &gt; 0 ">
            and t1.activity_id IN
            <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                #{activityId,jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY t1.statistics_month,t1.init_sn,t1.belong,t1.salesman
            </select>

            <!--查询单设备累计交易金额和返佣积分-->
            <select id="getTotalAmountAndTotalReward" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.TotalAmountDTO">
                    SELECT
        cast(round(SUM(t1.month_amount),2) as char) as totalAmount,
        cast(round(SUM(t1.month_reward),2) as char) as totalReward
        FROM
        HW_HARVEST_PLAN_AGENT_MONTH_REWARD_NEW t1
        WHERE
        t1.init_sn = #{initSn,jdbcType=VARCHAR}
        and t1.activity_id = #{activityId,jdbcType=INTEGER}
        and t1.belong = #{belong,jdbcType=INTEGER}
        and t1.salesman = #{salesman,jdbcType=INTEGER}
        and t1.audit_status = 1
        and t1.statistics_month &lt;= #{statisticsMonth,jdbcType=INTEGER}
        and t1.is_del = 0
            </select>
    </mapper>
