<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.RiskWorkOrderDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.RiskWorkOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="LAST_PUSH_TIME" property="lastPushTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PUSH_DEAD_LINE" property="pushDeadLine" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_MSG" property="rejectMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_SMID" property="alipaySmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_TYPE_ID" property="dataTypeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MARKET_NAME" property="marketName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_SMID" property="wechatSmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_REJECT_MSG" property="orgRejectMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REVIEWER_NAME" property="reviewerName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_NAME" property="salesmanName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TICKET_NUMBER" property="ticketNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUBMITTER_NAME" property="submitterName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPEAL_TEMPLATE" property="appealTemplate" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="WECHAT_CHANNEL_NO" property="wechatChannelNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WORK_ORDER_NUMBER" property="workOrderNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RELEVANCE_WORK_NUMBER" property="relevanceWorkNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RISK_CONTROL_DIMENSION" property="riskControlDimension" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PUSH" property="isPush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="KA_DEAL_FLAG" property="kaDealFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PUSH_STATUS" property="pushStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TRADE_STATUS" property="tradeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RISK_RECORD_ID" property="riskRecordId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_STATUS" property="settleStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMITTER_TYPE" property="submitterType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WORK_ORDER_TYPE" property="workOrderType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORG_REVIEW_STATUS" property="orgReviewStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_ORG_STATUS" property="submitOrgStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPEAL_TEMPLATE_TYPE" property="appealTemplateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="REVIEM_TIME" property="reviemTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="ORG_REVIEW_TIME" property="orgReviewTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SUBMITTER_TIME" property="submitterTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FUBEI_SUBMITTER_TIME" property="fubeiSubmitterTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`LAST_PUSH_TIME`,`PUSH_DEAD_LINE`,`REMARK`,`USERNAME`,`AGENT_NAME`,`REJECT_MSG`,`ALIPAY_SMID`,`DATA_TYPE_ID`,`MARKET_NAME`,`MERCHANT_NO`,`WECHAT_SMID`,`COMPANY_NAME`,`ORG_REJECT_MSG`,`REVIEWER_NAME`,`SALESMAN_NAME`,`TICKET_NUMBER`,`SUBMITTER_NAME`,`APPEAL_TEMPLATE`,`WECHAT_CHANNEL_NO`,`WORK_ORDER_NUMBER`,`RELEVANCE_WORK_NUMBER`,`RISK_CONTROL_DIMENSION`,`IS_DEL`,`IS_PUSH`,`AGENT_ID`,`MARKET_ID`,`KA_DEAL_FLAG`,`MERCHANT_ID`,`PUSH_STATUS`,`SALESMAN_ID`,`ORDER_STATUS`,`TRADE_STATUS`,`RISK_RECORD_ID`,`SETTLE_STATUS`,`SUBMITTER_TYPE`,`WORK_ORDER_TYPE`,`ORG_REVIEW_STATUS`,`SUBMIT_ORG_STATUS`,`APPEAL_TEMPLATE_TYPE`,`CREATE_TIME`,`REVIEM_TIME`,`UPDATE_TIME`,`ORG_REVIEW_TIME`,`SUBMITTER_TIME`,`FUBEI_SUBMITTER_TIME`
    </sql>


            <!--insert:FK_RISK_WORK_ORDER-->
            <insert id="insert" >
                    INSERT INTO FK_RISK_WORK_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="rejectMsg != null">`REJECT_MSG`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
            <if test="marketName != null">`MARKET_NAME`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="wechatSmid != null">`WECHAT_SMID`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="orgRejectMsg != null">`ORG_REJECT_MSG`,</if>
            <if test="reviewerName != null">`REVIEWER_NAME`,</if>
            <if test="salesmanName != null">`SALESMAN_NAME`,</if>
            <if test="ticketNumber != null">`TICKET_NUMBER`,</if>
            <if test="submitterName != null">`SUBMITTER_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="riskRecordId != null">`RISK_RECORD_ID`,</if>
            <if test="submitterType != null">`SUBMITTER_TYPE`,</if>
            <if test="orgReviewStatus != null">`ORG_REVIEW_STATUS`,</if>
            <if test="workOrderNumber != null">`WORK_ORDER_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="reviemTime != null">`REVIEM_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orgReviewTime != null">`ORG_REVIEW_TIME`,</if>
            <if test="submitterTime != null">`SUBMITTER_TIME`,</if>
            <if test="fubeiSubmitterTime != null">`FUBEI_SUBMITTER_TIME`,</if>
            <if test="appealTemplate != null">`APPEAL_TEMPLATE`,</if>
            <if test="isPush != null">`is_push`,</if>
            <if test="appealTemplateType != null">`APPEAL_TEMPLATE_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="rejectMsg != null">#{rejectMsg,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
            <if test="marketName != null">#{marketName,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="wechatSmid != null">#{wechatSmid,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="orgRejectMsg != null">#{orgRejectMsg,jdbcType=VARCHAR},</if>
            <if test="reviewerName != null">#{reviewerName,jdbcType=VARCHAR},</if>
            <if test="salesmanName != null">#{salesmanName,jdbcType=VARCHAR},</if>
            <if test="ticketNumber != null">#{ticketNumber,jdbcType=VARCHAR},</if>
            <if test="submitterName != null">#{submitterName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="riskRecordId != null">#{riskRecordId,jdbcType=INTEGER},</if>
            <if test="submitterType != null">#{submitterType,jdbcType=TINYINT},</if>
            <if test="orgReviewStatus != null">#{orgReviewStatus,jdbcType=TINYINT},</if>
            <if test="workOrderNumber != null">#{workOrderNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="reviemTime != null">#{reviemTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orgReviewTime != null">#{orgReviewTime,jdbcType=TIMESTAMP},</if>
            <if test="submitterTime != null">#{submitterTime,jdbcType=TIMESTAMP},</if>
            <if test="fubeiSubmitterTime != null">#{fubeiSubmitterTime,jdbcType=TIMESTAMP},</if>
            <if test="appealTemplate != null">#{appealTemplate,jdbcType=LONGVARCHAR},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="appealTemplateType != null">#{appealTemplateType,jdbcType=TINYINT},</if>
        </trim>
            </insert>

            <!--根据条件查询工单-->
            <select id="getUniqueWorkOrder" resultMap="BaseResultMap">
                    select /*MS-FK-RISK-WORK-ORDER-GETUNIQUEWORKORDER*/ <include refid="Base_Column_List" />
        from fk_risk_work_order
        where
        is_del=0
        <if test="wechatSmid !=null ">
            and wechat_smid=#{wechatSmid,jdbcType=VARCHAR}
        </if>
        <if test="merchantId !=null ">
            and merchant_id=#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="merchantNo !=null ">
            and merchant_no=#{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="dataTypeId !=null ">
            and data_type_id=#{dataTypeId,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus !=null ">
            and order_status !=#{orderStatus,jdbcType=INTEGER}
        </if>
        <if test="isPush !=null ">
            and is_push =#{isPush,jdbcType=INTEGER}
        </if>
        limit 1
            </select>

            <!--根据条件查询未申诉陈工的工单-->
            <select id="getNotAppealWorkOrder" resultMap="BaseResultMap">
                    select /*MS-FK-RISK-WORK-ORDER-GETNOTAPPEALWORKORDER*/ <include refid="Base_Column_List" />
        from fk_risk_work_order
        where
        order_status not in(6,7)
        <if test="merchantId !=null ">
            and merchant_id=#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="isPush !=null ">
            and is_push =#{isPush,jdbcType=INTEGER}
        </if>
        limit 1
            </select>
    </mapper>
