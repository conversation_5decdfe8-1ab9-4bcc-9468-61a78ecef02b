<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.ExtraHwEquipmentStorageOrderDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentStorageOrderDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CREATER" property="creater" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXAMINER" property="examiner" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="EXAMINER_NUMBER" property="examinerNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="BIZ_TYPE" property="bizType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ORDER_NUM" property="orderNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>

    <resultMap id="StorageOrderDo"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.StorageOrderDO">

        <result column="create_time" property="createTime" javaType="java.util.Date"/>

        <result column="creater" property="creater" javaType="java.lang.String"/>

        <result column="examiner" property="examiner" javaType="java.lang.String"/>

        <result column="storage_order" property="storageOrder" javaType="java.lang.String"/>

        <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

        <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

        <result column="agent_id" property="agentId" javaType="java.lang.Integer"/>

        <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

        <result column="order_num" property="orderNum" javaType="java.lang.Integer"/>

        <result column="arrival_num" property="arrivalNum" javaType="java.lang.Integer"/>

        <result column="operate_type" property="operateType" javaType="java.lang.Integer"/>

        <result column="order_status" property="orderStatus" javaType="java.lang.Integer"/>

        <result column="order_price" property="orderPrice" javaType="java.math.BigDecimal"/>

        <result column="depot" property="depot" javaType="java.lang.Integer"/>
        <result column="unit_price" property="unitPrice" javaType="java.math.BigDecimal"/>
        <result column="supplier" property="supplier" javaType="java.lang.String"/>
        <result column="execute_time" property="executeTime" javaType="java.util.Date"/>
        <result column="username" property="username" javaType="java.lang.String"/>
        <result column="purchase_type" property="purchaseType" javaType="java.lang.Integer"/>
        <result column="remark" property="remark" javaType="java.lang.String"/>
        <result column="purchase_rate" property="purchaseRate" javaType="java.math.BigDecimal"/>
        <result column="invoice_cross_check_status" property="invoiceCrossCheckStatus" javaType="java.lang.Integer"/>
        <result column="no_tax_unit_price" property="noTaxUnitPrice" javaType="java.math.BigDecimal"/>
        <result column="no_tax_order_price" property="noTaxOrderPrice" javaType="java.math.BigDecimal"/>
        <result column="invoice_type" property="invoiceType" javaType="java.lang.Integer"/>
    </resultMap>

    <sql id="Base_Column_List">
        `ID`
        ,
        `CREATER`,
        `EXAMINER`,
        `JOB_NUMBER`,
        `STORAGE_ORDER`,
        `EXAMINER_NUMBER`,
        `IS_DEL`,
        `AGENT_ID`,
        `BIZ_TYPE`,
        `ORDER_NUM`,
        `ORDER_TYPE`,
        `ARRIVAL_NUM`,
        `EQUIPMENT_ID`,
        `OPERATE_TYPE`,
        `ORDER_STATUS`,
        `CREATE_TIME`,
        `UPDATE_TIME`,
        `ORDER_PRICE`
    </sql>

    <!--查询仓储订单信息列表带有SN码 pageResult-->
    <select id="getStorageOrderListResult" resultMap="StorageOrderDo">
        SELECT
        estorage.storage_order,estorage.create_time,equipment.equipment_name,equipment.equipment_model,estorage.order_num,estorage.arrival_num,
        estorage.order_price,estorage.order_status,estorage.creater,estorage.examiner,estorage.operate_type,estorage.agent_id,estorage.biz_type,
        estorage.depot,u.username,estorage.unit_price,estorage.supplier,estorage.execute_time,estorage.purchase_type,estorage.remark,
        estorage.purchase_rate,
        estorage.invoice_cross_check_status,
        estorage.no_tax_unit_price,
        estorage.no_tax_order_price,
        estorage.invoice_type
        from hw_equipment_storage_order estorage
        LEFT JOIN hw_equipment equipment on estorage.equipment_id = equipment.id
        LEFT JOIN hw_equipment_order_relation relation on relation.order_no = estorage.storage_order
        LEFT JOIN tp_user u on u.id = estorage.agent_id
        where estorage.is_del = 0
        and relation.is_del = 0
        <if test="orderType!=null and orderType!=-1">
            AND estorage.order_type = #{orderType,jdbcType=TINYINT}
        </if>
        <if test="bizType!=null and bizType!=-1">
            AND estorage.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="bizType != null and bizType == -1 and bizTypeList != null and bizTypeList.size() > 0">
            AND estorage.biz_type IN
            <foreach close=")" collection="bizTypeList" index="index" item="bizTypeId" open="(" separator=",">
                #{bizTypeId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storageOrder!=null and storageOrder!=''">
            AND estorage.storage_order = #{storageOrder,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus!=null and orderStatus!=-1">
            AND estorage.order_status = #{orderStatus,jdbcType=TINYINT}
        </if>
        <if test="purchaseType!=null and purchaseType!=-1">
            AND estorage.purchase_type = #{purchaseType,jdbcType=TINYINT}
        </if>
        <if test="snId!=null">
            AND relation.sn_id = #{snId,jdbcType=INTEGER}
        </if>
        <if test="supplier!=null and supplier!=''">
            AND estorage.supplier like concat(#{supplier,jdbcType=VARCHAR},'%')
        </if>
        <if test="executeStartTime!=null and executeEndTime!=null">
            AND estorage.execute_time BETWEEN #{executeStartTime,jdbcType=TIMESTAMP} AND
            #{executeEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createStartTime !=null and createEndTime !=null">
            AND estorage.create_time BETWEEN #{createStartTime,jdbcType=TIMESTAMP} AND
            #{createEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="depot != null and depot != -1">
            AND estorage.depot = #{depot,jdbcType=TINYINT}
        </if>
        <if test="depot != null and depot == -1 and depotList != null and depotList.size() > 0">
            AND estorage.depot IN
            <foreach close=")" collection="depotList" index="index" item="depotId" open="(" separator=",">
                #{depotId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="isTest != null">
            AND estorage.is_test = #{isTest, jdbcType=INTEGER}
        </if>
        group by estorage.storage_order
        order by estorage.create_time desc
    </select>

    <!--查询仓储订单信息列表 pageResult-->
    <select id="getPurchaseOrderListResult" resultMap="StorageOrderDo">
        SELECT
        estorage.storage_order,estorage.create_time,equipment.equipment_name,equipment.equipment_model,estorage.order_num,estorage.arrival_num,
        estorage.order_price,estorage.order_status,estorage.creater,estorage.examiner,estorage.operate_type,estorage.agent_id,estorage.biz_type,
        estorage.depot,u.username,estorage.unit_price,estorage.supplier,estorage.execute_time,estorage.purchase_type,estorage.remark,
        estorage.purchase_rate,
        estorage.invoice_cross_check_status,
        estorage.no_tax_unit_price,
        estorage.no_tax_order_price,
        estorage.invoice_type
        from hw_equipment_storage_order estorage
        LEFT JOIN hw_equipment equipment on estorage.equipment_id = equipment.id
        LEFT JOIN tp_user u on u.id = estorage.agent_id
        where estorage.is_del = 0
        <if test="orderType!=null and orderType!=-1">
            AND estorage.order_type = #{orderType,jdbcType=TINYINT}
        </if>
        <if test="bizType!=null and bizType!=-1">
            AND estorage.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="bizType != null and bizType == -1 and bizTypeList != null and bizTypeList.size() > 0">
            AND estorage.biz_type IN
            <foreach close=")" collection="bizTypeList" index="index" item="bizTypeId" open="(" separator=",">
                #{bizTypeId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="storageOrder!=null and storageOrder!=''">
            AND estorage.storage_order = #{storageOrder,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus!=null and orderStatus!=-1">
            AND estorage.order_status = #{orderStatus,jdbcType=TINYINT}
        </if>
        <if test="purchaseType!=null and purchaseType!=-1">
            AND estorage.purchase_type = #{purchaseType,jdbcType=TINYINT}
        </if>
        <if test="supplier!=null and supplier!=''">
            AND estorage.supplier like concat(#{supplier,jdbcType=VARCHAR},'%')
        </if>
        <if test="executeStartTime!=null and executeEndTime!=null">
            AND estorage.execute_time BETWEEN #{executeStartTime,jdbcType=TIMESTAMP} AND
            #{executeEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="createStartTime !=null and createEndTime !=null">
            AND estorage.create_time BETWEEN #{createStartTime,jdbcType=TIMESTAMP} AND
            #{createEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="depot != null and depot != -1">
            AND estorage.depot = #{depot,jdbcType=TINYINT}
        </if>
        <if test="depot != null and depot == -1 and depotList != null and depotList.size() > 0">
            AND estorage.depot IN
            <foreach close=")" collection="depotList" index="index" item="depotId" open="(" separator=",">
                #{depotId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="isTest != null">
            AND estorage.is_test = #{isTest, jdbcType=INTEGER}
        </if>
        order by estorage.create_time desc
    </select>

</mapper>
