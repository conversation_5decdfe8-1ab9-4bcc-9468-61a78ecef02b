<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmActivityPolicyTargetDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmActivityPolicyTargetDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="REMARK" property="remark" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="END_TIME" property="endTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="TARGET_ID" property="targetId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="START_TIME" property="startTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="SPECIAL_MARK" property="specialMark" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SETTLE_AGENT_ID" property="settleAgentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="REGISTER_STATUS" property="registerStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ACTIVITY_TARGET_TYPE" property="activityTargetType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`REMARK`,`ACTIVITY_ID`,`IS_DEL`,`END_TIME`,`TARGET_ID`,`START_TIME`,`SPECIAL_MARK`,`SETTLE_AGENT_ID`,`REGISTER_STATUS`,`ACTIVITY_TARGET_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:LM_CRM_ACTIVITY_POLICY_TARGET-->
        <insert id="insert">
            INSERT INTO LM_CRM_ACTIVITY_POLICY_TARGET
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="remark != null">`REMARK`,</if>
                <if test="activityId != null">`ACTIVITY_ID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="endTime != null">`END_TIME`,</if>
                <if test="targetId != null">`TARGET_ID`,</if>
                <if test="startTime != null">`START_TIME`,</if>
                <if test="specialMark != null">`SPECIAL_MARK`,</if>
                <if test="registerStatus != null">`REGISTER_STATUS`,</if>
                <if test="activityTargetType != null">`ACTIVITY_TARGET_TYPE`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="settleAgentId != null">`SETTLE_AGENT_ID`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
                <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
                <if test="targetId != null">#{targetId,jdbcType=INTEGER},</if>
                <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
                <if test="specialMark != null">#{specialMark,jdbcType=TINYINT},</if>
                <if test="registerStatus != null">#{registerStatus,jdbcType=TINYINT},</if>
                <if test="activityTargetType != null">#{activityTargetType,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="settleAgentId != null">#{settleAgentId,jdbcType=INTEGER},</if>
            </trim>
        </insert>

        <!--findListByActivityId-->
        <select id="findListByActivityId" resultMap="BaseResultMap">
            SELECT /*MS-LM-CRM-ACTIVITY-POLICY-TARGET-FINDLISTBYACTIVITYID*/
            <include refid="Base_Column_List"/>
            FROM LM_CRM_ACTIVITY_POLICY_TARGET
            WHERE is_del = 0
            <if test="list != null and list.size() &gt; 0">
                AND activity_id IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </select>

        <!--批量插入活动政策报名-->
        <insert id="insertBatch">
            INSERT INTO LM_CRM_ACTIVITY_POLICY_TARGET (
            `REMARK`,
            `ACTIVITY_ID`,
            `IS_DEL`,
            `END_TIME`,
            `TARGET_ID`,
            `START_TIME`,
            `SPECIAL_MARK`,
            `REGISTER_STATUS`,
            `ACTIVITY_TARGET_TYPE`,
            `SETTLE_AGENT_ID`
            )VALUES
            <foreach collection="list" item="item" index="index" separator=",">
                (
                #{item.remark,jdbcType=VARCHAR},
                #{item.activityId,jdbcType=VARCHAR},
                #{item.isDel,jdbcType=TINYINT},
                #{item.endTime,jdbcType=INTEGER},
                #{item.targetId,jdbcType=INTEGER},
                #{item.startTime,jdbcType=INTEGER},
                #{item.specialMark,jdbcType=TINYINT},
                #{item.registerStatus,jdbcType=TINYINT},
                #{item.activityTargetType,jdbcType=TINYINT},
                #{item.settleAgentId,jdbcType=INTEGER}
                )
            </foreach>
        </insert>

        <!--findPage pageCount-->
        <select id="findPageCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            LM_CRM_ACTIVITY_POLICY_TARGET
            WHERE
            is_del = 0
            AND activity_target_type = #{activityTargetType,jdbcType=INTEGER}
            <if test="activityIdList != null and activityIdList.size() &gt; 0">
                AND activity_id IN
                <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                    #{activityId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="targetIdList != null and targetIdList.size() &gt; 0">
                AND target_id IN
                <foreach collection="targetIdList" item="targetId" open="(" close=")" separator=",">
                    #{targetId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="registerStatus != null and registerStatus != 0">
                AND register_status = #{registerStatus,jdbcType=INTEGER}
            </if>
            <if test="specialMark != null and specialMark != 0">
                AND special_mark = #{specialMark,jdbcType=INTEGER}
            </if>
        </select>
        <!--findPage pageResult-->
        <select id="findPageResult" resultMap="BaseResultMap">
            SELECT /*MS-LM-CRM-ACTIVITY-POLICY-TARGET-FINDPAGE*/
            <include refid="Base_Column_List"/>
            FROM LM_CRM_ACTIVITY_POLICY_TARGET
            WHERE
            is_del = 0
            AND activity_target_type = #{activityTargetType,jdbcType=INTEGER}
            <if test="activityIdList != null and activityIdList.size() &gt; 0">
                AND activity_id IN
                <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                    #{activityId,jdbcType=VARCHAR}
                </foreach>
            </if>
            <if test="targetIdList != null and targetIdList.size() &gt; 0">
                AND target_id IN
                <foreach collection="targetIdList" item="targetId" open="(" close=")" separator=",">
                    #{targetId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="registerStatus != null and registerStatus != 0">
                AND register_status = #{registerStatus,jdbcType=INTEGER}
            </if>
            <if test="specialMark != null and specialMark != 0">
                AND special_mark = #{specialMark,jdbcType=INTEGER}
            </if>
            limit #{startRow},#{limit}
        </select>
    </mapper>
