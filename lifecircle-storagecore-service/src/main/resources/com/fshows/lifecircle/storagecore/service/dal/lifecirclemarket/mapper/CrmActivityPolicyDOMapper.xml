<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmActivityPolicyDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmActivityPolicyDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACTIVITY_NAME" property="activityName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="EXCLUDE_ACTIVITY_ID_LIST" property="excludeActivityIdList" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`ACTIVITY_ID`,`ACTIVITY_NAME`,`EXCLUDE_ACTIVITY_ID_LIST`,`IS_DEL`,`END_TIME`,`START_TIME`,`ACTIVITY_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
        </sql>


        <!--insert:LM_CRM_ACTIVITY_POLICY-->
        <insert id="insert">
            INSERT INTO LM_CRM_ACTIVITY_POLICY
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="activityId != null">`ACTIVITY_ID`,</if>
                <if test="activityName != null">`ACTIVITY_NAME`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="endTime != null">`END_TIME`,</if>
                <if test="startTime != null">`START_TIME`,</if>
                <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--findListByActivityId-->
        <select id="findListByActivityId" resultMap="BaseResultMap">
            SELECT /*MS-LM-CRM-ACTIVITY-POLICY-FINDLISTBYACTIVITYID*/
            <include refid="Base_Column_List"/>
            FROM LM_CRM_ACTIVITY_POLICY
            WHERE is_del = 0
            <if test="list != null and list.size() &gt; 0">
                AND activity_id IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </select>
    </mapper>
