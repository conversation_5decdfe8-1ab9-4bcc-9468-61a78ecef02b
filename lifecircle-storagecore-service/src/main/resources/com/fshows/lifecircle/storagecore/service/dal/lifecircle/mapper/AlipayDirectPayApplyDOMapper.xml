<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AlipayDirectPayApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AlipayDirectPayApplyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="PID" property="pid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCC_CODE" property="mccCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PIC" property="storePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_NUMBER" property="applyNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_MOBILE" property="legalMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_CODE" property="licenseCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_NAME" property="licenseName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_MOBILE" property="storeMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_NAME" property="districtName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_ACCOUNT" property="alipayAccount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_CONTACTS" property="storeContacts" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INSIDE_SCENE_PIC" property="insideScenePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_ADDRESS" property="licenseAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_END_TIME" property="licenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_START_TIME" property="licenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SPECIAL_QUALIFICATION_PIC" property="specialQualificationPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="QUALIFICATION" property="qualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UNITY_CATEGORY_ID" property="unityCategoryId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APPLY_TIME" property="applyTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="AUDIT_TIME" property="auditTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`PID`,`ADDRESS`,`MCC_CODE`,`CITY_CODE`,`CITY_NAME`,`STORE_PIC`,`USERNAME`,`STORE_NAME`,`LICENSE_PIC`,`APPLY_NUMBER`,`LEGAL_MOBILE`,`LICENSE_CODE`,`LICENSE_NAME`,`STORE_MOBILE`,`DISTRICT_CODE`,`DISTRICT_NAME`,`PROVINCE_CODE`,`PROVINCE_NAME`,`REJECT_REASON`,`ALIPAY_ACCOUNT`,`STORE_CONTACTS`,`INSIDE_SCENE_PIC`,`LICENSE_ADDRESS`,`LICENSE_END_TIME`,`LICENSE_START_TIME`,`SPECIAL_QUALIFICATION_PIC`,`IS_DEL`,`MERCHANT_ID`,`CHECK_STATUS`,`QUALIFICATION`,`UNITY_CATEGORY_ID`,`APPLY_TIME`,`AUDIT_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--查询支付宝N7直连申请单信息-->
            <select id="findAlipayDirectPayApplyList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-ALIPAY-DIRECT-PAY-APPLY-FINDALIPAYDIRECTPAYAPPLYLIST*/  <include refid="Base_Column_List" />
        FROM
            TP_ALIPAY_DIRECT_PAY_APPLY
        WHERE is_del = 0
        <if test="merchantId != null and merchantId != 0">
            AND merchant_id = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="username!=null and username!='' ">
            and username=#{username,jdbcType=VARCHAR}
        </if>
        <if test="alipayAccount !=null and alipayAccount!='' ">
            and alipay_account=#{alipayAccount,jdbcType=VARCHAR}
        </if>
        <if test="applyTimeStart!=null">
            AND apply_time <![CDATA[ >= ]]> #{applyTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="applyTimeEnd !=null">
            AND apply_time <![CDATA[ < ]]> #{applyTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="checkStatus !=null and checkStatus != -1 ">
            and check_status=#{checkStatus,jdbcType=INTEGER}
        </if>
        order by apply_time desc
            </select>
    </mapper>
