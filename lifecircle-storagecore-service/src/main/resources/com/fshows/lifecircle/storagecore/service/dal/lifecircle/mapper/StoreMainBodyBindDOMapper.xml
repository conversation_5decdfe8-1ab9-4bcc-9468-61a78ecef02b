<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.StoreMainBodyBindDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.StoreMainBodyBindDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_SMID" property="alipaySmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_SUB_MCH_ID" property="wechatSubMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APPLY_ID" property="applyId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTH_SYNC_TIME" property="authSyncTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WECHANT_AUTH_STATUS" property="wechantAuthStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UNION_CODE`,`ALIPAY_SMID`,`MERCHANT_NO`,`PLATFORM_ORG_ID`,`WECHAT_SUB_MCH_ID`,`UID`,`APPLY_ID`,`STORE_ID`,`START_TIME`,`AUTH_SYNC_TIME`,`WECHANT_AUTH_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_STORE_MAIN_BODY_BIND-->
            <insert id="insert" >
                    INSERT INTO TP_STORE_MAIN_BODY_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
            <if test="wechatSubMchId != null">`WECHAT_SUB_MCH_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
            <if test="wechatSubMchId != null">#{wechatSubMchId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="applyId != null">#{applyId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--通过门店ID查询绑定记录-->
            <select id="getByStoreId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-STORE-MAIN-BODY-BIND-GETBYSTOREID*/  <include refid="Base_Column_List" />
        FROM `tp_store_main_body_bind`
        WHERE `store_id` = #{storeId,jdbcType=INTEGER}
        and `platform_org_id` = #{platformOrgId,jdbcType=VARCHAR}
        and `start_time` &lt; NOW()
        ORDER BY `start_time` desc,id DESC
        limit 1
            </select>
    </mapper>
