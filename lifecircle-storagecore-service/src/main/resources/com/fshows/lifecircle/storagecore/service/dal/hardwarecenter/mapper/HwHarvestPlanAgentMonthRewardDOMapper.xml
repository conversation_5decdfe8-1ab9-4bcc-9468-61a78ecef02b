<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwHarvestPlanAgentMonthRewardDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwHarvestPlanAgentMonthRewardDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UID" property="uid" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="BELONG" property="belong" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="STATISTICS_MONTH" property="statisticsMonth" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="MONTH_AMOUNT" property="monthAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="MONTH_REWARD" property="monthReward" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`INIT_SN`,`UID`,`IS_DEL`,`BELONG`,`EQUIPMENT_ID`,`STATISTICS_MONTH`,`CREATE_TIME`,`UPDATE_TIME`,`MONTH_AMOUNT`,`MONTH_REWARD`
        </sql>


        <!--insert:HW_HARVEST_PLAN_AGENT_MONTH_REWARD-->
        <insert id="insert">
            INSERT INTO HW_HARVEST_PLAN_AGENT_MONTH_REWARD
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="initSn != null">`INIT_SN`,</if>
                <if test="uid != null">`UID`,</if>
                <if test="isDel != null">`IS_DEL`,</if>
                <if test="belong != null">`BELONG`,</if>
                <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
                <if test="statisticsMonth != null">`STATISTICS_MONTH`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="monthAmount != null">`MONTH_AMOUNT`,</if>
                <if test="monthReward != null">`MONTH_REWARD`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
                <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
                <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
                <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
                <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
                <if test="statisticsMonth != null">#{statisticsMonth,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="monthAmount != null">#{monthAmount,jdbcType=DECIMAL},</if>
                <if test="monthReward != null">#{monthReward,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--分页查询代理商月佣金 pageCount-->
        <select id="findMonthRewardListCount" resultType="int">
            SELECT
            COUNT(*) AS total
            FROM
            hw_harvest_plan_agent_month_reward t1
            LEFT JOIN
            hw_equipment t2
            ON
            t2.id = t1.equipment_id
            WHERE
            t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
            <if test="initSn != null and initSn != '' ">
                and t1.init_sn = #{initSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentId != null">
                and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
            </if>
            <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
                and t1.belong in
                <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                    #{belong,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
                and t1.uid in
                <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            and t1.is_del = 0

        </select>
        <!--分页查询代理商月佣金 pageResult-->
        <select id="findMonthRewardListResult"
                resultType="com.fshows.lifecircle.storagecore.service.domain.dto.RewardDTO">
            SELECT
            t1.statistics_month as statistics,
            t1.init_sn as equipmentSn,
            t2.equipment_model as equipmentModel,
            cast(t1.month_amount as char) tradeAmount,
            cast(t1.month_reward as char) awardAmount,
            t1.uid as merchantId,
            t1.belong as agentId
            FROM
            hw_harvest_plan_agent_month_reward t1
            LEFT JOIN
            hw_equipment t2
            ON
            t2.id = t1.equipment_id
            WHERE
            t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
            <if test="initSn != null and initSn != '' ">
                and t1.init_sn = #{initSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentId != null">
                and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
            </if>
            <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
                and t1.belong in
                <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                    #{belong,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
                and t1.uid in
                <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            and t1.is_del = 0
            ORDER BY t1.`statistics_month` DESC , t1.`month_amount` DESC
            limit #{startRow},#{limit}
        </select>

        <!--查询单设备，单月，单代理商奖励佣金之和-->
        <select id="getMonthTotalReward"
                resultType="com.fshows.lifecircle.storagecore.service.domain.dto.TotalRewardDTO">
            select
            init_sn as equipmentSn,
            belong as agentId,
            SUM(month_reward) as totalReward
            from
            hw_harvest_plan_agent_month_reward
            where
            statistics_month = #{statisticsMonth,jdbcType=INTEGER}
            <if test="initSn != null and initSn != '' ">
                and init_sn = #{initSn,jdbcType=VARCHAR}
            </if>
            <if test="equipmentId != null">
                and equipment_id = #{equipmentId,jdbcType=INTEGER}
            </if>
            <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
                and belong in
                <foreach collection="agentIdList" item="agentId" open="(" close=")" separator=",">
                    #{agentId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
                and uid in
                <foreach collection="merchantIdList" item="merchantId" open="(" close=")" separator=",">
                    #{merchantId,jdbcType=INTEGER}
                </foreach>
            </if>
            GROUP BY statistics_month,init_sn,belong
        </select>
    </mapper>
