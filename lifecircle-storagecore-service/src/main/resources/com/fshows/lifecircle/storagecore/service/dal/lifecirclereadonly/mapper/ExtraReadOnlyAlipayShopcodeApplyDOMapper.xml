<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ExtraReadOnlyAlipayShopcodeApplyDOMapper">

    <resultMap id="BaseResultMap"  type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.resultmap.AlipayShopCodeApplyWebListDO">
        <result column="BIZ_NO" property="bizNo" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="UID" property="uid" jdbcType="INTEGER" javaType="Integer"/>
        <result column="STORE_ID" property="storeId" jdbcType="INTEGER" javaType="Integer"/>
        <result column="SHOP_NAME" property="shopName" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPANY" property="company" jdbcType="VARCHAR" javaType="String"/>
        <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="BELONG_USERNAME" property="belongUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="BELONG_TYPE" property="belongType" jdbcType="TINYINT" javaType="Integer"/>
        <result column="SETTLEMENT_TYPE" property="settlementType" jdbcType="TINYINT" javaType="Integer"/>
        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR" javaType="String"/>
        <result column="RESULT_DESC" property="resultDesc" jdbcType="VARCHAR" javaType="String"/>
        <result column="BIZ_STATUS" property="bizStatus" jdbcType="TINYINT" javaType="Integer"/>
    </resultMap>

    <resultMap id="MauBaseResultMap"  type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.resultmap.AlipayShopCodeApplyMauWebListDO">
        <result column="BIZ_NO" property="bizNo" jdbcType="VARCHAR" javaType="String"/>
        <result column="STORE_ID" property="storeId" jdbcType="INTEGER" javaType="Integer"/>
        <result column="SHOP_NAME" property="shopName" jdbcType="VARCHAR" javaType="String"/>
        <result column="COMPANY" property="company" jdbcType="VARCHAR" javaType="String"/>
        <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="BELONG_USERNAME" property="belongUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="APPLY_SUCCESS_TIME" property="applySuccessTime" jdbcType="TINYINT" javaType="Integer"/>
        <result column="MAU" property="mau" jdbcType="INTEGER" javaType="Integer"/>
        <result column="MAU_COMPLETED" property="mauCompleted" jdbcType="TINYINT" javaType="Integer"/>
        <result column="MAU_REWARDED" property="mauRewarded" jdbcType="TINYINT" javaType="Integer"/>
    </resultMap>

    <resultMap id="TransBaseResultMap"  type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.resultmap.AlipayShopCodeTransWebListDO">
        <result column="BIZ_NO" property="bizNo" jdbcType="VARCHAR" javaType="String"/>
        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
        <result column="ALIPAY_ACCOUNT" property="alipayAccount" jdbcType="VARCHAR" javaType="String"/>
        <result column="ALIPAY_USERNAME" property="alipayUsername" jdbcType="VARCHAR" javaType="String"/>
        <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR" javaType="String"/>
        <result column="CHANGE_BALANCE" property="changeBalance" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result column="TRANS_AMOUNT" property="transAmount" jdbcType="DECIMAL" javaType="java.math.BigDecimal"/>
        <result column="TRANS_STATUS" property="transStatus" jdbcType="TINYINT" javaType="Integer"/>
        <result column="FAIL_REASON" property="failReason" jdbcType="VARCHAR" javaType="String"/>
    </resultMap>

    <select id="getWebList" resultMap="BaseResultMap">
        SELECT
        apply.biz_no,
        apply.create_time,
        apply.uid,
        apply.store_id,
        apply.shop_name,
        users.company,
        users.username merchant_username,
        belonguser.username belong_username,
        belonguser.type belong_type,
        apply.settlement_type,
        apply.operator_name,
        apply.biz_status,
        apply.result_desc
        FROM tp_alipay_shopcode_apply apply
        LEFT JOIN tp_lifecircle_store store ON apply.store_id = store.store_id
        LEFT JOIN tp_users users ON store.token = users.users_token
        LEFT JOIN tp_user belonguser ON users.belong = belonguser.id
        <where>
            <if test="shopName != null and shopName != ''">
                AND apply.shop_name LIKE CONCAT ('%', #{shopName,jdbcType=VARCHAR},'%')
            </if>
            <if test="operatorName != null and operatorName != ''">
                AND apply.operator_name LIKE CONCAT ('%', #{operatorName,jdbcType=VARCHAR},'%')
            </if>
            <if test="storeId != null">
                AND apply.store_id = #{storeId,jdbcType=INTEGER}
            </if>
            <if test="merchantUsername != null and merchantUsername != ''">
                AND users.username LIKE CONCAT (#{merchantUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="belongUsername != null and belongUsername != ''">
                AND belonguser.username LIKE CONCAT (#{belongUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="belongType != null">
                AND belonguser.type = #{belongType,jdbcType=INTEGER}
            </if>
            <if test="bizStatus != null">
                AND apply.biz_status = #{bizStatus,jdbcType=INTEGER}
            </if>
            <if test="settlementType != null">
                AND apply.settlement_type = #{settlementType,jdbcType=INTEGER}
            </if>
            <if test="createStartTime != null">
                AND apply.create_time <![CDATA[ >= ]]> #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND apply.create_time <![CDATA[ < ]]> #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            AND is_del = 0
        </where>
        ORDER BY apply.create_time DESC
    </select>

    <select id="getMauWebList" resultMap="MauBaseResultMap">
        SELECT
        apply.biz_no,
        apply.store_id,
        apply.shop_name,
        users.company,
        users.username merchant_username,
        belonguser.username belong_username,
        apply.apply_success_time,
        apply.mau,
        apply.mau_completed,
        apply.mau_rewarded
        FROM tp_alipay_shopcode_apply apply
        LEFT JOIN tp_lifecircle_store store ON apply.store_id = store.store_id
        LEFT JOIN tp_users users ON store.token = users.users_token
        LEFT JOIN tp_user belonguser ON users.belong = belonguser.id
        <where>
            apply.apply_own_type = 2
            AND apply.settlement_type = 2
            AND apply.biz_status = 8
            <if test="shopName != null and shopName != ''">
                AND apply.shop_name LIKE CONCAT ('%', #{shopName,jdbcType=VARCHAR},'%')
            </if>
            <if test="storeId != null">
                AND apply.store_id = #{storeId,jdbcType=INTEGER}
            </if>
            <if test="merchantUsername != null and merchantUsername != ''">
                AND users.username LIKE CONCAT (#{merchantUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="belongUsername != null and belongUsername != ''">
                AND belonguser.username LIKE CONCAT (#{belongUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="mauCompleted != null">
                AND apply.mau_completed = #{mauCompleted,jdbcType=INTEGER}
            </if>
            <if test="mauRewarded != null">
                AND apply.mau_rewarded = #{mauRewarded,jdbcType=INTEGER}
            </if>
            <if test="createStartTime != null">
                AND apply.create_time <![CDATA[ >= ]]> #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND apply.create_time <![CDATA[ < ]]> #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            AND is_del = 0
        </where>
        ORDER BY apply.audit_time DESC
    </select>

    <select id="getTransWebList" resultMap="TransBaseResultMap">
        SELECT
        trans_date AS create_time,
        alipay_account,
        alipay_username,
        change_balance,
        trans_amount,
        operator_name,
        trans_status,
        fail_reason
        FROM tp_user_alipay_shopcode_account_change_log
        <where>
            <if test="alipayAccount != null and alipayAccount != ''">
                AND alipay_account LIKE CONCAT ('%', #{alipayAccount,jdbcType=VARCHAR},'%')
            </if>
            <if test="alipayUsername != null and alipayUsername != ''">
                AND alipay_username LIKE CONCAT ('%', #{alipayUsername,jdbcType=VARCHAR},'%')
            </if>
            <if test="operatorName != null and operatorName != ''">
                AND operator_name LIKE CONCAT ('%', #{operatorName,jdbcType=VARCHAR},'%')
            </if>
            <if test="transStatus != null">
                AND trans_status = #{transStatus,jdbcType=INTEGER}
            </if>
            <if test="createStartTime != null">
                AND create_time <![CDATA[ >= ]]> #{createStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="createEndTime != null">
                AND create_time <![CDATA[ < ]]> #{createEndTime,jdbcType=TIMESTAMP}
            </if>
            AND del_flag = 0
        </where>
        ORDER BY trans_date DESC
    </select>
</mapper>
