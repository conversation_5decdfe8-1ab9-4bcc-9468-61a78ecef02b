<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentSnDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentSnDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OEM_NAME" property="oemName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PASSWORD" property="password" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SYSTEM_SN" property="systemSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EQUIPMENT_ALIAS_NAME" property="equipmentAliasName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OEM_ID" property="oemId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TP_KEY" property="tpKey" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="GRANT_ID" property="grantId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CODE_TYPE" property="codeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SN_STATUS" property="snStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRADE_MODE" property="tradeMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASHIER_MODE" property="cashierMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_TYPE" property="channelType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OPERATE_TYPE" property="operateType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TRANSFER_TYPE" property="transferType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECYCLE_STATUS" property="recycleStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NEW_CASHIER_MODE" property="newCashierMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRINTER_SETTING" property="printerSetting" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_OPEN_SELF_SHOPPING" property="isOpenSelfShopping" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRINTER_MASTER_SETTING" property="printerMasterSetting" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BACK_TIME" property="backTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BIND_TIME" property="bindTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UNBIND_TIME" property="unbindTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="RECEIVE_TIME" property="receiveTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="DISTRIBUTE_TIME" property="distributeTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="TRUE_PRICE" property="truePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="EquipmentSnInfoMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentSnInfoMap">

                <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>
        </resultMap>
        <resultMap id="GetAllBySearchParamMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.GetAllBySearchParamMap">

                <result column="last_bind_time" property="lastBindTime" javaType="java.util.Date"/>

                <result column="agent_name" property="agentName" javaType="java.lang.String"/>

                <result column="grant_name" property="grantName" javaType="java.lang.String"/>

                <result column="store_name" property="storeName" javaType="java.lang.String"/>

            <result column="market_name" property="marketName" javaType="java.lang.String"/>

            <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

                <result column="merchant_name" property="merchantName" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

                <result column="sn_id" property="snId" javaType="java.lang.Integer"/>

                <result column="depot" property="depot" javaType="java.lang.Integer"/>

                <result column="sn_status" property="snStatus" javaType="java.lang.Integer"/>

                <result column="trade_mode" property="tradeMode" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="EquipmentStockExportMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentStockExportMap">

                <result column="back_time" property="backTime" javaType="java.util.Date"/>

                <result column="create_time" property="createTime" javaType="java.util.Date"/>

                <result column="purchase_time" property="purchaseTime" javaType="java.util.Date"/>

                <result column="init_sn" property="initSn" javaType="java.lang.String"/>

                <result column="order_no" property="orderNo" javaType="java.lang.String"/>

                <result column="system_sn" property="systemSn" javaType="java.lang.String"/>

                <result column="stock_order" property="stockOrder" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

                <result column="id" property="id" javaType="java.lang.Integer"/>

                <result column="depot" property="depot" javaType="java.lang.Integer"/>

                <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

                <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>

                <result column="operate_type" property="operateType" javaType="java.lang.Integer"/>

                <result column="unit_price" property="unitPrice" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="InEuipmentSnMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.InEuipmentSnMap">

                <result column="execute_time" property="executeTime" javaType="java.util.Date"/>

                <result column="init_sn" property="initSn" javaType="java.lang.String"/>

                <result column="order_no" property="orderNo" javaType="java.lang.String"/>

                <result column="stock_order" property="stockOrder" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

                <result column="depot" property="depot" javaType="java.lang.Integer"/>

                <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

                <result column="unit_price" property="unitPrice" javaType="java.math.BigDecimal"/>
        </resultMap>
        <resultMap id="ByEquipmentIdMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.ByEquipmentIdMap">

                <result column="num" property="num" javaType="java.lang.Integer"/>

                <result column="depot" property="depot" javaType="java.lang.Integer"/>

                <result column="biz_type" property="bizType" javaType="java.lang.Integer"/>

                <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>
        <resultMap id="GetModelByListMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.GetModelByListMap">

                <result column="init_sn" property="initSn" javaType="java.lang.String"/>

                <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

                <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

                <result column="depot" property="depot" javaType="java.lang.Integer"/>

                <result column="equipment_price" property="equipmentPrice" javaType="java.math.BigDecimal"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`OEM_NAME`,`PASSWORD`,`SYSTEM_SN`,`EQUIPMENT_ALIAS_NAME`,`UID`,`DEPOT`,`IS_DEL`,`OEM_ID`,`TP_KEY`,`AGENT_ID`,`GRANT_ID`,`PAY_TYPE`,`STORE_ID`,`CODE_TYPE`,`MARKET_ID`,`SN_STATUS`,`CASHIER_ID`,`TRADE_MODE`,`CASHIER_MODE`,`CHANNEL_TYPE`,`EQUIPMENT_ID`,`OPERATE_TYPE`,`TRANSFER_TYPE`,`RECYCLE_STATUS`,`NEW_CASHIER_MODE`,`PRINTER_SETTING`,`IS_OPEN_SELF_SHOPPING`,`PRINTER_MASTER_SETTING`,`BACK_TIME`,`BIND_TIME`,`CREATE_TIME`,`UNBIND_TIME`,`UPDATE_TIME`,`RECEIVE_TIME`,`DISTRIBUTE_TIME`,`TRUE_PRICE`
    </sql>


            <!--insert:HW_EQUIPMENT_SN-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="systemSn != null">`SYSTEM_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="snStatus != null">`SN_STATUS`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="systemSn != null">#{systemSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="snStatus != null">#{snStatus,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据设备SN码查询信息-->
            <select id="getByInitSn" resultMap="BaseResultMap">
                    select /*MS-HW-EQUIPMENT-SN-GETBYINITSN*/ <include refid="Base_Column_List" /> from hw_equipment_sn where init_sn = #{initSn,jdbcType=VARCHAR} and is_del = 0
            </select>

            <!--根据 initSn 批量查询 设备SN 信息-->
            <select id="getByInitSnList" resultMap="BaseResultMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-GETBYINITSNLIST*/  <include refid="Base_Column_List" />
        FROM hw_equipment_sn
        WHERE init_sn IN
        <foreach close=")" collection="list" index="index" item="initSn" open="(" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND is_del = 0
            </select>

            <!--根据订单号查询设备sn信息-->
            <select id="getEquipmentSnByStorageOrder" resultMap="EquipmentSnInfoMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-GETEQUIPMENTSNBYSTORAGEORDER*/  a.system_sn equipment_sn,d.equipment_name,d.equipment_model
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on a.id = b.sn_id
        LEFT JOIN hw_equipment_storage_order c on b.order_no = c.storage_order
        LEFT JOIN hw_equipment d on d.id = a.equipment_id
        where c.storage_order = #{storageOrder,jdbcType=VARCHAR}
            </select>

            <!--根据查询条件查询所有设备-->
            <select id="getAllBySearchParamForDownload" resultMap="GetAllBySearchParamMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-GETALLBYSEARCHPARAMFORDOWNLOAD*/  esn.id as sn_id,esn.init_sn as equipment_sn,e.equipment_name,e.equipment_model,esn.sn_status,u1.username
        as agent_name,esn.trade_mode,
                u2.username as grant_name,us.username as merchant_name
                ,store.store_name,bind_time as last_bind_time,esn.depot,u3.username as market_name
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment e on esn.equipment_id = e.id
        LEFT JOIN tp_users us on us.id = esn.uid
        LEFT JOIN tp_user u1 on u1.id = us.belong
        LEFT JOIN tp_user u2 on u2.id = us.salesman
                LEFT JOIN tp_user u3 on u3.id = us.market_id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        where 1=1
        <if test="equipmentSn!=null and equipmentSn!=''">
            AND esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="startBindTime!=null and endBindTime!=null">
            AND esn.bind_time BETWEEN #{startBindTime,jdbcType=TIMESTAMP} AND #{endBindTime,jdbcType=TIMESTAMP}
        </if>
        <if test="startCreateTime!=null and endCreateTime!=null">
            AND esn.create_time BETWEEN #{startCreateTime,jdbcType=TIMESTAMP} AND #{endCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="ownRun!=null and ownRun!=-1">
            AND u1.own_run = #{ownRun,jdbcType=TINYINT}
        </if>
        <if test="depot!=null and depot!=-1">
            AND esn.depot = #{depot,jdbcType=TINYINT}
        </if>
        <if test="depotIdList!=null and depotIdList.size() &gt; 0">
            AND esn.depot in
            <foreach collection="depotIdList" open="(" close=")" item="depotItem" separator=",">
                #{depotItem,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="snStatus!=null and snStatus!=-1">
            AND esn.sn_status = #{snStatus,jdbcType=TINYINT}
        </if>
        <if test="tradeMode!=null and tradeMode!=-1">
            AND esn.trade_mode = #{tradeMode,jdbcType=TINYINT}
        </if>
        <if test="equipmentName!=null and equipmentName!=''">
            AND e.equipment_name = #{equipmentName,jdbcType=VARCHAR}
        </if>
        <if test="equipmentNameList != null and equipmentNameList.size() &gt; 0">
            AND e.equipment_name in
            <foreach collection="equipmentNameList" open="(" close=")" item="equipmentNameItem" separator=",">
                #{equipmentNameItem,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="equipmentModel!=null and equipmentModel!=''">
            AND e.equipment_model = #{equipmentModel,jdbcType=VARCHAR}
        </if>
        <if test="agentId!=null and agentId!=0">
            AND esn.agent_id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="grantId!=null and grantId!=0">
            AND esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
                <if test="marketId!=null and marketId!=0">
                    AND esn.market_id = #{marketId,jdbcType=INTEGER}
        </if>
        <if test="uid!=null and uid!=0">
            AND esn.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="storeId!=null and storeId!=0">
            AND esn.store_id = #{storeId,jdbcType=INTEGER}
        </if>
        <if test="oemId !=null">
            AND esn.oem_id = #{oemId,jdbcType=INTEGER}
        </if>
        order by esn.create_time desc
            </select>

            <!--设备关联代理商-->
            <select id="associationAgent" resultMap="BaseResultMap">
                    UPDATE hw_equipment_sn
        SET
        agent_id = #{agentId,jdbcType=INTEGER},
        oem_id = #{oemId,jdbcType=INTEGER},
        oem_name = #{oemName,jdbcType=VARCHAR},
        grant_id = #{grantId,jdbcType=INTEGER}
        WHERE init_sn = #{initSn,jdbcType=VARCHAR}
        AND is_del = 0
            </select>

            <!--硬件库存一键导出-->
            <select id="equipmentStockExport" resultMap="EquipmentStockExportMap">
                    select /*MS-HW-EQUIPMENT-SN-EQUIPMENTSTOCKEXPORT*/ id,init_sn,equipment_id,depot,equipment_name,equipment_model,order_no,stock_order,create_time,
        biz_type,operate_type,unit_price,back_time,purchase_time
        from (select /*MS-HW-EQUIPMENT-SN-EQUIPMENTSTOCKEXPORT*/ a.id,a.init_sn,a.equipment_id,a.depot,c.equipment_name,c.equipment_model,
        b.order_no,b.stock_order,b.create_time,d.biz_type,d.operate_type,d.unit_price,d.execute_time
        back_time,e.execute_time
        purchase_time
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on b.sn_id = a.id and b.is_del = 0
        LEFT JOIN hw_equipment c on c.id = a.equipment_id
        LEFT JOIN hw_equipment_storage_order d on d.storage_order = b.order_no and d.is_del = 0
        LEFT JOIN hw_equipment_stock e on e.stock_order = b.stock_order
        where a.sn_status = 2 and a.is_del = 0
        and a.depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
        order by b.create_time desc) f group by id
            </select>

            <!--根据snList查询-->
            <select id="getBySnList" resultMap="InEuipmentSnMap">
                    select /*MS-HW-EQUIPMENT-SN-GETBYSNLIST*/ init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= #{orderType,jdbcType=TINYINT}
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc limit 10000) a
        group by a.init_sn
            </select>

            <!--根据snList查询-->
            <select id="getBySnListForMiddle" resultMap="InEuipmentSnMap">
                    SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= #{orderType,jdbcType=TINYINT}
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc
            </select>

            <!--带参数硬件库存一键导出-->
            <select id="equipmentStockExportBySnList" resultMap="EquipmentStockExportMap">
                    select /*MS-HW-EQUIPMENT-SN-EQUIPMENTSTOCKEXPORTBYSNLIST*/ id,init_sn,system_sn,equipment_id,depot,equipment_name,equipment_model,order_no,stock_order,create_time,
        biz_type,operate_type,unit_price,back_time,purchase_time
        from (select /*MS-HW-EQUIPMENT-SN-EQUIPMENTSTOCKEXPORTBYSNLIST*/ a.id,a.init_sn,a.system_sn,a.equipment_id,a.depot,c.equipment_name,c.equipment_model,
        b.order_no,b.stock_order,b.create_time,d.biz_type,d.operate_type,d.unit_price,d.execute_time
        back_time,e.execute_time
        purchase_time
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on b.sn_id = a.id and b.is_del = 0
        LEFT JOIN hw_equipment c on c.id = a.equipment_id
        LEFT JOIN hw_equipment_storage_order d on d.storage_order = b.order_no and d.is_del = 0
        LEFT JOIN hw_equipment_stock e on e.stock_order = b.stock_order
        where
        d.depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
        and a.init_sn in
        <foreach collection="snList" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by b.create_time desc limit 10000) f group by id
            </select>

            <!--getBySnListDepot-->
            <select id="getBySnListDepot" resultMap="ByEquipmentIdMap">
                    SELECT /*MS-HW-EQUIPMENT-SN-GETBYSNLISTDEPOT*/   count(*)  num,esn.equipment_id,esn.depot
        from hw_equipment_sn esn
        where esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        GROUP BY esn.depot,esn.equipment_id
            </select>

            <!--查询设备属性-->
            <select id="getModelByList" resultMap="GetModelByListMap">
                    select /*MS-HW-EQUIPMENT-SN-GETMODELBYLIST*/ esn.depot,esn.init_sn,e.equipment_name,e.equipment_model,e.equipment_price from hw_equipment_sn esn left
        join
        hw_equipment e on e.id = esn.equipment_id
        where esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--时间段内入库查询-->
            <select id="getBySnListForIn" resultMap="InEuipmentSnMap">
                    select /*MS-HW-EQUIPMENT-SN-GETBYSNLISTFORIN*/ init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= 1
        and rage.biz_type !=1
        and lation.id is not null
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc limit 10000) a
        group by a.init_sn
            </select>

            <!--根据stock表executeTime查询-->
            <select id="getBySnListForStock" resultMap="InEuipmentSnMap">
                    select /*MS-HW-EQUIPMENT-SN-GETBYSNLISTFORSTOCK*/ init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,stock.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment_stock stock on stock.stock_order = lation.stock_order
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= 1
        and rage.biz_type = 1
        and stock.id is not null
        and stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by stock.execute_time desc limit 10000) a
        group by a.init_sn
            </select>

            <!--根据设备SN批量查询-->
            <select id="findByInitSnList" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.EquipmentSnDTO">
                    SELECT /*MS-HW-EQUIPMENT-SN-FINDBYINITSNLIST*/  sn.init_sn AS equipmentSn,
        sn.equipment_id AS equipmentId,
        equipment.equipment_model AS equipmentModel
        FROM hw_equipment_sn AS sn
        LEFT JOIN hw_equipment AS equipment ON sn.equipment_id = equipment.`id`
        WHERE sn.init_sn IN
        <foreach collection="list" index="index" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND sn.is_del = 0
            </select>

            <!--查询设备归属代理商信息集合-->
            <select id="findSnAndUserTypeList" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.EquipmentSnBelongDTO">
                    SELECT
        tuser.id userId,
        tuser.username username,
        tuser.type type,
        sn.init_sn initSn
        FROM
        hw_equipment_sn sn
        LEFT JOIN tp_user tuser ON sn.agent_id = tuser.id
        WHERE
        tuser.type != null
        AND sn.init_sn IN
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--修改设备归属-->
            <update id="updateSnBelongByVersion" >
                    update /*MS-HW-EQUIPMENT-SN-UPDATESNBELONGBYVERSION*/ hw_equipment_sn
        set
        agent_id = #{agentId,jdbcType=INTEGER},
        grant_id = 0,
        update_time = now()
        where init_sn = #{initSn,jdbcType=VARCHAR}
        and is_del = 0
        and agent_id = #{beforeAgentId,jdbcType=INTEGER}
            </update>

            <!--修改信息-->
            <update id="updateEquipmentSnById" >
                    UPDATE
        hw_equipment_sn
        <set>
            <if test="tpKey != null">
                tp_key = #{tpKey,jdbcType=INTEGER},
            </if>
            <if test="initSn != null">
                init_sn = #{initSn,jdbcType=VARCHAR},
            </if>
            <if test="systemSn != null">
                system_sn = #{systemSn,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                is_del = #{isDel,jdbcType=TINYINT},
            </if>
            <if test="agentId != null">
                agent_id = #{agentId,jdbcType=INTEGER},
            </if>
            <if test="grantId != null">
                grant_id = #{grantId,jdbcType=INTEGER},
            </if>
            <if test="marketId != null">
                market_id = #{marketId,jdbcType=INTEGER},
            </if>
            <if test="uid != null">
                uid = #{uid,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="cashierId != null">
                cashier_id = #{cashierId,jdbcType=INTEGER},
            </if>
            <if test="cashierMode != null">
                cashier_mode = #{cashierMode,jdbcType=TINYINT},
            </if>
            <if test="depot != null">
                depot = #{depot,jdbcType=TINYINT},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType,jdbcType=TINYINT},
            </if>
            <if test="tradeMode != null">
                trade_mode = #{tradeMode,jdbcType=TINYINT},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=TINYINT},
            </if>
            <if test="truePrice != null">
                true_price = #{truePrice,jdbcType=DECIMAL},
            </if>
            <if test="printerSetting != null">
                printer_setting = #{printerSetting,jdbcType=TINYINT},
            </if>
            <if test="printerMasterSetting != null">
                printer_master_setting = #{printerMasterSetting,jdbcType=TINYINT},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=INTEGER},
            </if>
            <if test="unbindTime != null">
                unbind_time = #{unbindTime,jdbcType=INTEGER},
            </if>
            <if test="snStatus != null">
                sn_status = #{snStatus,jdbcType=TINYINT},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=TINYINT},
            </if>
            <if test="equipmentId != null">
                equipment_id = #{equipmentId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="distributeTime != null">
                distribute_time = #{distributeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="backTime != null">
                back_time = #{backTime,jdbcType=TIMESTAMP},
            </if>
            <if test="equipmentAliasName != null">
                equipment_alias_name = #{equipmentAliasName,jdbcType=VARCHAR},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        id= #{id,jdbcType=INTEGER}
            </update>

            <!--根据SN更新设备信息-->
            <update id="updateRecycleStatusBySnList" >
                    UPDATE /*MS-HW-EQUIPMENT-SN-UPDATERECYCLESTATUSBYSNLIST*/ hw_equipment_sn
        set recycle_status = #{recycleStatus,jdbcType=TINYINT}
        where init_sn in
        <foreach open="(" close=")" collection="snList" item="initSn" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
            </update>
    </mapper>
