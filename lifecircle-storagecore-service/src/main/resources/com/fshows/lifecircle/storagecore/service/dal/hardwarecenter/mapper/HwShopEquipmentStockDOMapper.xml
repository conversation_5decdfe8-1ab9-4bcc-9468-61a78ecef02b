<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopEquipmentStockDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopEquipmentStockDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="STOCK" property="stock" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_TEST" property="isTest" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="HW_ORDER_STOCK" property="hwOrderStock" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="PRE_SALE_STOCK" property="preSaleStock" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`IS_DEL`,`STOCK`,`IS_TEST`,`EQUIPMENT_ID`,`HW_ORDER_STOCK`,`PRE_SALE_STOCK`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:HW_SHOP_EQUIPMENT_STOCK-->
    <insert id="insert">
        INSERT INTO HW_SHOP_EQUIPMENT_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="isTest != null">`IS_TEST`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="hwOrderStock != null">`HW_ORDER_STOCK`,</if>
            <if test="preSaleStock != null">`PRE_SALE_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="isTest != null">#{isTest,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="hwOrderStock != null">#{hwOrderStock,jdbcType=INTEGER},</if>
            <if test="preSaleStock != null">#{preSaleStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--分页查询商城硬件库存-->
    <select id="findStockPageList" resultMap="BaseResultMap">
        SELECT
        stock.*
        FROM hw_shop_equipment_stock stock
        left join hw_equipment eq on stock.equipment_id = eq.id
        where stock.is_del = 0
        and stock.is_test = #{isTest, jdbcType=TINYINT}
        <if test="equipmentModel != null and '' != equipmentModel">
            and eq.equipment_model like concat(#{equipmentModel,jdbcType=VARCHAR},'%')
        </if>
        <if test="equipmentName != null and '' != equipmentName">
            and eq.equipment_name like concat(#{equipmentName,jdbcType=VARCHAR},'%')
        </if>
    </select>
</mapper>
