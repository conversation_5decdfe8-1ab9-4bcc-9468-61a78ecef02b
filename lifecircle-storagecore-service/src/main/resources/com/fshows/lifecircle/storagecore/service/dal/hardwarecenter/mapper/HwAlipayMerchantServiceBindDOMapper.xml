<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwAlipayMerchantServiceBindDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwAlipayMerchantServiceBindDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BIND_TIME" property="bindTime" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PID" property="pid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MSID" property="msid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_NO" property="identityNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BIND_TIME`,`PID`,`MSID`,`TOKEN`,`DEVICE_NO`,`IDENTITY_NO`,`UID`,`STORE_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_ALIPAY_MERCHANT_SERVICE_BIND-->
            <insert id="insert" >
                    INSERT INTO HW_ALIPAY_MERCHANT_SERVICE_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="msid != null">`MSID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="identityNo != null">`IDENTITY_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=BIGINT},</if>
            <if test="msid != null">#{msid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="identityNo != null">#{identityNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--查询所有绑定的商户-->
            <select id="findAllMerchant" resultMap="BaseResultMap">
                    select
        uid, token
        from hw_alipay_merchant_service_bind
            </select>

            <!--统计所有绑定的商户-->
            <select id="countAllMerchant" resultType="Integer">
                    select
         count(*) 
        from hw_alipay_merchant_service_bind
            </select>

            <!--查询所有绑定的商户-->
            <select id="findPageAllMerchant" resultMap="BaseResultMap">
                    select
        id, uid, token
        from hw_alipay_merchant_service_bind
        where id &gt; #{id,jdbcType=BIGINT}
        limit #{size,jdbcType=INTEGER}
            </select>
    </mapper>
