<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.LifecirclereadonlyDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT COUNT(*) = 15                  as                                                                                                                                      fg,
                   'TP_PREPAY_CARD_REFUND_DETAIL' as                                                                                                                                      tb_name,
                   group_concat(COLUMN_NAME)                                                                                                                                              db_columns,
                   'ID,ORG_ID,REFUND_NO,CARD_SKU_ID,CARD_SPU_ID,CARD_SPU_NAME,PUBLISH_ORG_ID,SALES_ORDER_NO,IS_DEL,IS_STATS,REFUND_NUMBER,CREATE_TIME,UPDATE_TIME,CARD_PRICE,CARD_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_PREPAY_CARD_REFUND_DETAIL'
              AND COLUMN_NAME in
                  ('ID', 'ORG_ID', 'REFUND_NO', 'CARD_SKU_ID', 'CARD_SPU_ID', 'CARD_SPU_NAME', 'PUBLISH_ORG_ID',
                   'SALES_ORDER_NO', 'IS_DEL', 'IS_STATS', 'REFUND_NUMBER', 'CREATE_TIME', 'UPDATE_TIME', 'CARD_PRICE',
                   'CARD_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT COUNT(*) = 8                      as                                              fg,
                   'TP_PREPAY_CARD_STORAGE_RELATION' as                                              tb_name,
                   group_concat(COLUMN_NAME)                                                         db_columns,
                   'ID,CARD_NO,CARD_SKU_ID,CARD_SPU_ID,STORAGE_ORDER,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_PREPAY_CARD_STORAGE_RELATION'
              AND COLUMN_NAME in
                  ('ID', 'CARD_NO', 'CARD_SKU_ID', 'CARD_SPU_ID', 'STORAGE_ORDER', 'IS_DEL', 'CREATE_TIME',
                   'UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'TP_CHANNEL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,SERIAL_NO,CHANNEL_NUM,JSAPI_PATH1,JSAPI_PATH2,JSAPI_PATH3,JSAPI_PATH4,JSAPI_PATH5,PRIVATE_KEY,SUBJECT_BODY,WECHAT_API_V2_KEY,WECHAT_API_V3_KEY,STATUS,PAY_TYPE,CHANNEL_ID,APPLET_INCOME,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CHANNEL'
            AND COLUMN_NAME in('ID','REMARK','SERIAL_NO','CHANNEL_NUM','JSAPI_PATH1','JSAPI_PATH2','JSAPI_PATH3','JSAPI_PATH4','JSAPI_PATH5','PRIVATE_KEY','SUBJECT_BODY','WECHAT_API_V2_KEY','WECHAT_API_V3_KEY','STATUS','PAY_TYPE','CHANNEL_ID','APPLET_INCOME','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT COUNT(*) = 31    as                                                                                                                                                                                                                                                                                                                                                                                         fg,
                   'TP_PREPAY_CARD' as                                                                                                                                                                                                                                                                                                                                                                                         tb_name,
                   group_concat(COLUMN_NAME)                                                                                                                                                                                                                                                                                                                                                                                   db_columns,
                   'ID,CARD_NO,OPEN_ID,CARD_CODE,BIND_PHONE,CARD_SKU_ID,CARD_SPU_ID,CARD_SPU_NAME,FUBEI_UNION_ID,PUBLISH_ORG_ID,SCOPE_CITY_CODE,ACTIVATION_CODE,SCOPE_PROVINCE_CODE,IS_DEL,BIND_TIME,CARD_TYPE,BIND_SOURCE,BIND_STATUS,CARD_EXPIRY,CARD_STATUS,IS_MINA_SALES,STOCK_STATUS,CARD_SHAPE_TYPE,ACTIVATION_TIME,OUT_STORAGE_TIME,IS_LONG_EFFECTIVE,ACTIVATION_STATUS,CREATE_TIME,UPDATE_TIME,CARD_PRICE,CARD_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_PREPAY_CARD'
              AND COLUMN_NAME in
                  ('ID', 'CARD_NO', 'OPEN_ID', 'CARD_CODE', 'BIND_PHONE', 'CARD_SKU_ID', 'CARD_SPU_ID', 'CARD_SPU_NAME',
                   'FUBEI_UNION_ID', 'PUBLISH_ORG_ID', 'SCOPE_CITY_CODE', 'ACTIVATION_CODE', 'SCOPE_PROVINCE_CODE',
                   'IS_DEL', 'BIND_TIME', 'CARD_TYPE', 'BIND_SOURCE', 'BIND_STATUS', 'CARD_EXPIRY', 'CARD_STATUS',
                   'IS_MINA_SALES', 'STOCK_STATUS', 'CARD_SHAPE_TYPE', 'ACTIVATION_TIME', 'OUT_STORAGE_TIME',
                   'IS_LONG_EFFECTIVE', 'ACTIVATION_STATUS', 'CREATE_TIME', 'UPDATE_TIME', 'CARD_PRICE', 'CARD_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT COUNT(*) = 9           as                                                                   fg,
                   'TP_PREPAY_CARD_STOCK' as                                                                   tb_name,
                   group_concat(COLUMN_NAME)                                                                   db_columns,
                   'ID,CARD_SKU_ID,CARD_SPU_ID,PUBLISH_ORG_ID,IS_DEL,STOCK,LOCK_STOCK,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_PREPAY_CARD_STOCK'
              AND COLUMN_NAME in
                  ('ID', 'CARD_SKU_ID', 'CARD_SPU_ID', 'PUBLISH_ORG_ID', 'IS_DEL', 'STOCK', 'LOCK_STOCK', 'CREATE_TIME',
                   'UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 30 as fg,'TP_COST_RATE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,IS_SYNC,SYNC_SUB,ROLE_TYPE,EXIST_MAX_FEE,IS_EFFECTIVE,PRODUCT_CODE,PAYMENT_CHANNEL,EXIST_WITHDRAWAL_FEE,UID,PAY_TYPE,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME,PARENT_EFFECTIVE_TIME,FB_RATE,OEM_RATE,MAX_FB_FEE,AGENT_RATE,MAX_OEM_FEE,CHANNEL_RATE,MAX_AGENT_FEE,SALESMAN_RATE,MAX_CHANNEL_FEE,MAX_SALESMAN_FEE,FB_WITHDRAWAL_FEE,OEM_WITHDRAWAL_FEE,AGENT_WITHDRAWAL_FEE,CHANNEL_WITHDRAWAL_FEE,SALESMAN_WITHDRAWAL_FEE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_COST_RATE'
            AND COLUMN_NAME in('ID','IS_SYNC','SYNC_SUB','ROLE_TYPE','EXIST_MAX_FEE','IS_EFFECTIVE','PRODUCT_CODE','PAYMENT_CHANNEL','EXIST_WITHDRAWAL_FEE','UID','PAY_TYPE','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME','PARENT_EFFECTIVE_TIME','FB_RATE','OEM_RATE','MAX_FB_FEE','AGENT_RATE','MAX_OEM_FEE','CHANNEL_RATE','MAX_AGENT_FEE','SALESMAN_RATE','MAX_CHANNEL_FEE','MAX_SALESMAN_FEE','FB_WITHDRAWAL_FEE','OEM_WITHDRAWAL_FEE','AGENT_WITHDRAWAL_FEE','CHANNEL_WITHDRAWAL_FEE','SALESMAN_WITHDRAWAL_FEE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 62 as fg,'TP_LIFECIRCLE_REFUND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10,TOKEN,REMARK,ORDER_SN,DEVICE_NO,REFUND_SN,NOTIFY_URL,ACTIVITY_ID,REFUND_CODE,REFUND_INFO,FACE_DEVICE_SN,MERCHANT_ORDER_SN,PLATFORM_ORDER_SN,MERCHANT_REFUND_SN,PLATFORM_REFUND_SN,LIQUIDATOR_ORDER_SN,GOODS_REFUND_ORDER_SN,LIQUIDATOR_REFUND_SN,USER_ID,CHANNEL,GRANT_ID,HANDLER,PAY_TIME,PAY_TYPE,STORE_ID,MARKET_ID,CASHIER_ID,IS_MORE_DAY,CREATE_TIME,SETTLE_MODE,CONSUME_TYPE,LASTEST_TIME,SUB_CONFIG_ID,IS_PART_REFUND,REFUND_STATUS,REFUND_AGENT_ID,SPECIAL_SETTLE,CONSUME_CHANNEL,LIQUIDATION_TYPE,IS_POSITION_REFUND,ORGANIZATION_TYPE,REFUND_FAILED_DEAL,REFUND_SHARE_STATUS,REFUND_UPDATE_TIME,REFUND,CASH_FEE,LIFE_FEE,AGENT_FEE,POUNDAGE,REFUND_MONEY,AGENT_RATE_FEE,RECHARGEACT_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_REFUND'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','EXT5','EXT6','EXT7','EXT8','EXT9','EXT10','TOKEN','REMARK','ORDER_SN','DEVICE_NO','REFUND_SN','NOTIFY_URL','ACTIVITY_ID','REFUND_CODE','REFUND_INFO','FACE_DEVICE_SN','MERCHANT_ORDER_SN','PLATFORM_ORDER_SN','MERCHANT_REFUND_SN','PLATFORM_REFUND_SN','LIQUIDATOR_ORDER_SN','GOODS_REFUND_ORDER_SN','LIQUIDATOR_REFUND_SN','USER_ID','CHANNEL','GRANT_ID','HANDLER','PAY_TIME','PAY_TYPE','STORE_ID','MARKET_ID','CASHIER_ID','IS_MORE_DAY','CREATE_TIME','SETTLE_MODE','CONSUME_TYPE','LASTEST_TIME','SUB_CONFIG_ID','IS_PART_REFUND','REFUND_STATUS','REFUND_AGENT_ID','SPECIAL_SETTLE','CONSUME_CHANNEL','LIQUIDATION_TYPE','IS_POSITION_REFUND','ORGANIZATION_TYPE','REFUND_FAILED_DEAL','REFUND_SHARE_STATUS','REFUND_UPDATE_TIME','REFUND','CASH_FEE','LIFE_FEE','AGENT_FEE','POUNDAGE','REFUND_MONEY','AGENT_RATE_FEE','RECHARGEACT_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 66 as fg,'TP_LIFECIRCLE_STORE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'STORE_LAT,STORE_LNG,KEY,TEL,CITY,NOTE,TOKEN,COUNTY,ADDRESS,END_TIME,OPERATE,SERVICE,DEVICE_NO,PROVINCE,BRAND_NAME,OBJECTION,RECOMMEND,START_TIME,STORE_AREA,STORE_LOGO,STORE_NAME,BRANCH_NAME,DENIAL_TYPE,STORE_BRIEF,STORE_IMAGE,EXAMINE_JSON,SERVICE_MORE,LICENSE_NUMBER,MAIN_STORE_INFO,REC_ID,IS_HORN,IS_SHOW,AGENT_ID,STORE_ID,AVG_PRICE,IS_ONLINE,JOIN_TIME,PRINT_NUM,SETTLE_TO,AUTOPRINT,BIND_LOG_ID,PRINT_CODE,STORE_TYPE,CATEGORYID,CREATE_TIME,IS_PROMOTED,UNITY_CAT_ID,EXAMINE_TIME,JOIN_CHANNEL,PRINTER_TYPE,STORE_STATUS,IS_BIND_KOUBEI,IS_NEED_INCOME,IS_TAKEPARTIN,TAKING_ORDERS,EXAMINE_STATUS,STORE_BELONG_ID,DISPLAY_COMMENT,INDEX_RECOMMEND,PREFERENTIAL_ID,SETTLEMENT_TYPE,IS_HORN_CANCEL_PAY,SETTLE_START_TIME,TAKING_ORDERS_TYPE,MERCHANT_QUALIFICATION,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_STORE'
            AND COLUMN_NAME in('STORE_LAT','STORE_LNG','KEY','TEL','CITY','NOTE','TOKEN','COUNTY','ADDRESS','END_TIME','OPERATE','SERVICE','DEVICE_NO','PROVINCE','BRAND_NAME','OBJECTION','RECOMMEND','START_TIME','STORE_AREA','STORE_LOGO','STORE_NAME','BRANCH_NAME','DENIAL_TYPE','STORE_BRIEF','STORE_IMAGE','EXAMINE_JSON','SERVICE_MORE','LICENSE_NUMBER','MAIN_STORE_INFO','REC_ID','IS_HORN','IS_SHOW','AGENT_ID','STORE_ID','AVG_PRICE','IS_ONLINE','JOIN_TIME','PRINT_NUM','SETTLE_TO','AUTOPRINT','BIND_LOG_ID','PRINT_CODE','STORE_TYPE','CATEGORYID','CREATE_TIME','IS_PROMOTED','UNITY_CAT_ID','EXAMINE_TIME','JOIN_CHANNEL','PRINTER_TYPE','STORE_STATUS','IS_BIND_KOUBEI','IS_NEED_INCOME','IS_TAKEPARTIN','TAKING_ORDERS','EXAMINE_STATUS','STORE_BELONG_ID','DISPLAY_COMMENT','INDEX_RECOMMEND','PREFERENTIAL_ID','SETTLEMENT_TYPE','IS_HORN_CANCEL_PAY','SETTLE_START_TIME','TAKING_ORDERS_TYPE','MERCHANT_QUALIFICATION','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT COUNT(*) = 40                             as                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      fg,
                   'TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY' as                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                      tb_name,
                   group_concat(COLUMN_NAME)                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                                         db_columns,
                   'ID,SMID,SCENE,CARD_NO,APPLY_ID,COMPANY,AREA_CODE,AREA_NAME,CARD_NAME,CITY_CODE,CITY_NAME,SCHOOL_ID,USERNAME,MERCHANT_NO,REJECT_INFO,ACTIVITY_TYPE,INDUSTRY_CODE,PROVINCE_CODE,PROVINCE_NAME,BANK_BRANCH_NAME,SCHOOL_FULL_NAME,STORE_IN_DOOR_IMG,STORE_ENTRANCE_IMG,BUSINESS_LICENSE_PIC,LEGAL_ID_CARD_BACK_PIC,BANK_ACCOUNT_PROVE_PIC,LEGAL_ID_CARD_FRONT_PIC,FINANCE_STORE_IN_DOOR_PIC,BANK_COOPERATION_AGREEMENT_PIC,UID,BELONG,CHANNEL_ID,HAS_SHOP_ID,SCHOOL_TYPE,SUBJECT_TYPE,ACTIVITY_STATUS,ACTIVITY_OPEN_TIME,ACTIVITY_SIGN_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY'
              AND COLUMN_NAME in
                  ('ID', 'SMID', 'SCENE', 'CARD_NO', 'APPLY_ID', 'COMPANY', 'AREA_CODE', 'AREA_NAME', 'CARD_NAME',
                   'CITY_CODE', 'CITY_NAME', 'SCHOOL_ID', 'USERNAME', 'MERCHANT_NO', 'REJECT_INFO', 'ACTIVITY_TYPE',
                   'INDUSTRY_CODE', 'PROVINCE_CODE', 'PROVINCE_NAME', 'BANK_BRANCH_NAME', 'SCHOOL_FULL_NAME',
                   'STORE_IN_DOOR_IMG', 'STORE_ENTRANCE_IMG', 'BUSINESS_LICENSE_PIC', 'LEGAL_ID_CARD_BACK_PIC',
                   'BANK_ACCOUNT_PROVE_PIC', 'LEGAL_ID_CARD_FRONT_PIC', 'FINANCE_STORE_IN_DOOR_PIC',
                   'BANK_COOPERATION_AGREEMENT_PIC', 'UID', 'BELONG', 'CHANNEL_ID', 'HAS_SHOP_ID', 'SCHOOL_TYPE',
                   'SUBJECT_TYPE', 'ACTIVITY_STATUS', 'ACTIVITY_OPEN_TIME', 'ACTIVITY_SIGN_TIME', 'CREATE_TIME',
                   'UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT COUNT(*) = 10 as                                                                                        fg,
                   'TP_USER_TAG' as                                                                                        tb_name,
                   group_concat(COLUMN_NAME)                                                                               db_columns,
                   'ID,TAG,AGENT_ID,SAIL_TAG,START_TIME,OLD_SAIL_TAG,COMPANY_TYPE,POLICY_END_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE TABLE_NAME = 'TP_USER_TAG'
              AND COLUMN_NAME in
                  ('ID', 'TAG', 'AGENT_ID', 'SAIL_TAG', 'START_TIME', 'OLD_SAIL_TAG', 'COMPANY_TYPE', 'POLICY_END_TIME',
                   'CREATE_TIME', 'UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'TP_LIQUIDATION_CONFIG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APP_ID,REMARK,PARTNERS,PHP_PUBLIC_KEY,JAVA_PUBLIC_KEY,PHP_PRIVATE_KEY,JAVA_PRIVATE_KEY,IMAGE_GATEWAY_URL,INNER_GATEWAY_URL,LIQUIDATION_NAME,OUTER_GATEWAY_URL,STATUS,PAY_TYPE,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIQUIDATION_CONFIG'
            AND COLUMN_NAME in('ID','APP_ID','REMARK','PARTNERS','PHP_PUBLIC_KEY','JAVA_PUBLIC_KEY','PHP_PRIVATE_KEY','JAVA_PRIVATE_KEY','IMAGE_GATEWAY_URL','INNER_GATEWAY_URL','LIQUIDATION_NAME','OUTER_GATEWAY_URL','STATUS','PAY_TYPE','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'TP_LIFECIRCLE_GENERAL_BANK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BANK_FULL_NAME,BANK_SHORT_NAME,BANK_PACKAGE_IMG,BANK_PACKAGE_LOGO,GENERAL_BANK_CODE,IS_DEL,SHOW_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_GENERAL_BANK'
            AND COLUMN_NAME in('ID','BANK_FULL_NAME','BANK_SHORT_NAME','BANK_PACKAGE_IMG','BANK_PACKAGE_LOGO','GENERAL_BANK_CODE','IS_DEL','SHOW_FLAG','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'TP_PREPAY_CARD_SKU' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CARD_SKU_ID,CARD_SPU_ID,IS_DEL,CREATE_TIME,UPDATE_TIME,CARD_PRICE,CARD_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_SKU'
            AND COLUMN_NAME in('ID','CARD_SKU_ID','CARD_SPU_ID','IS_DEL','CREATE_TIME','UPDATE_TIME','CARD_PRICE','CARD_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 26 as fg,'TP_PREPAY_CARD_SALES_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORG_ID,REMARKS,OPERATE_ID,CUSTOMER_ID,INVOICE_URL,RECEIPT_URL,CONTRACT_URL,HANDLER_NAME,OPERATE_NAME,CONTACT_EMAIL,CONTACT_PHONE,CUSTOMER_NAME,RECEIPT_ORDER,SALES_ORDER_NO,CONTACT_ADDRESS,IS_DEL,ORDER_SOURCE,ORDER_STATUS,DELIVERY_TIME,TOTAL_CARD_NUMBER,CREATE_TIME,UPDATE_TIME,TOTAL_CARD_REAL,TOTAL_CARD_PRICE,TOTAL_CARD_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_SALES_ORDER'
            AND COLUMN_NAME in('ID','ORG_ID','REMARKS','OPERATE_ID','CUSTOMER_ID','INVOICE_URL','RECEIPT_URL','CONTRACT_URL','HANDLER_NAME','OPERATE_NAME','CONTACT_EMAIL','CONTACT_PHONE','CUSTOMER_NAME','RECEIPT_ORDER','SALES_ORDER_NO','CONTACT_ADDRESS','IS_DEL','ORDER_SOURCE','ORDER_STATUS','DELIVERY_TIME','TOTAL_CARD_NUMBER','CREATE_TIME','UPDATE_TIME','TOTAL_CARD_REAL','TOTAL_CARD_PRICE','TOTAL_CARD_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 33 as fg,'TP_PREPAY_CARD_CONSUME' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APPID,EXT01,EXT02,EXT03,EXT04,EXT05,EXT06,TOKEN,CARD_NO,OPEN_ID,ORDER_SN,UNION_ID,FUBEI_UNION_ID,LEVEL01_ORG_ID,LEVEL02_ORG_ID,LEVEL03_ORG_ID,LEVEL04_ORG_ID,PUBLISH_ORG_ID,UID,IS_DEL,PAY_TIME,PAY_TYPE,STORE_ID,ORDER_TYPE,PAY_STATUS,REFUND_TIME,REFUND_STATUS,CREATE_TIME,UPDATE_TIME,FEE,ORDER_PRICE,REFUND_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_CONSUME'
            AND COLUMN_NAME in('ID','APPID','EXT01','EXT02','EXT03','EXT04','EXT05','EXT06','TOKEN','CARD_NO','OPEN_ID','ORDER_SN','UNION_ID','FUBEI_UNION_ID','LEVEL01_ORG_ID','LEVEL02_ORG_ID','LEVEL03_ORG_ID','LEVEL04_ORG_ID','PUBLISH_ORG_ID','UID','IS_DEL','PAY_TIME','PAY_TYPE','STORE_ID','ORDER_TYPE','PAY_STATUS','REFUND_TIME','REFUND_STATUS','CREATE_TIME','UPDATE_TIME','FEE','ORDER_PRICE','REFUND_MONEY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'TP_PREPAY_MAKE_CARD_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BATCH_NO,CARD_SKU_ID,CARD_SPU_ID,OPERATE_ID,CARD_SPU_NAME,OPERATE_NAME,PUBLISH_ORG_ID,IS_DEL,FINISH_TIME,MAKE_NUMBER,MAKE_STATUS,SUBMIT_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_MAKE_CARD_LOG'
            AND COLUMN_NAME in('ID','BATCH_NO','CARD_SKU_ID','CARD_SPU_ID','OPERATE_ID','CARD_SPU_NAME','OPERATE_NAME','PUBLISH_ORG_ID','IS_DEL','FINISH_TIME','MAKE_NUMBER','MAKE_STATUS','SUBMIT_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 85 as fg,'TP_USER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QQ,TEL,AREA,BANK,CITY,LOGO,EMAIL,LEVEL,PHONE,UNAME,PEOPLE,REMARK,UTOKEN,WECHAT,ADDRESS,CARD_NUM,CARD_WEB,BUSINESS,CARDNAME,CONTACTS,FULL_PATH,NICKNAME,PASSWORD,PROVINCE,TURNOVER,USERNAME,PRIVILEGES,PROFESSION,CARD_ADDRESS,COMPANYNAME,LAST_LOGIN_IP,BUSINESS_AREA,BUSINESS_CITY,CUSTOMER_NOTE,LAST_LOCATION,VERIFYIMAGES,ALIPAYACCOUNT,OPEN_API_CALLBACK,BUSINESS_PROVINCE,UID,CASH,ROLE,TYPE,BELONG,IS_OPEN,IS_PASS,OWN_RUN,STATUS,IS_JDPAY,IS_WHITE,VIPTIME,CHILD_NUM,GROUP_NUM,IS_ALIPAY,PARENT_ID,PLATFORM,IS_SELF_FEE,CONFIG_TYPE,CREATETIME,IS_OPEN_MINA,IS_SALESMAN,IS_SHOW_TIPS,LOAN_STATUS,ACCOUNT_TYPE,IS_QUICK_CASH,SALESMAN_TAG,SUB_CONFIG_ID,USE_GROUP_NUM,CURRENT_LEVEL,SALES_STAFF_ID,IS_SCAN_SERVICE,LAST_LOGIN_TIME,CUSTOMER_SERVICE,IS_OPENAPI_ACCESS,IS_SUPER_SALESMAN,SUPER_SALESMAN_ID,ADVERTISEMENT_NUM,OPERATION_SERVICE,NEW_VERSION_USERS_NUM,SALESMAN_TAG_START_DAY,CREATE_TIME,UPDATE_TIME,FINANCE,ADVERTISEMENT_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_USER'
            AND COLUMN_NAME in('ID','QQ','TEL','AREA','BANK','CITY','LOGO','EMAIL','LEVEL','PHONE','UNAME','PEOPLE','REMARK','UTOKEN','WECHAT','ADDRESS','CARD_NUM','CARD_WEB','BUSINESS','CARDNAME','CONTACTS','FULL_PATH','NICKNAME','PASSWORD','PROVINCE','TURNOVER','USERNAME','PRIVILEGES','PROFESSION','CARD_ADDRESS','COMPANYNAME','LAST_LOGIN_IP','BUSINESS_AREA','BUSINESS_CITY','CUSTOMER_NOTE','LAST_LOCATION','VERIFYIMAGES','ALIPAYACCOUNT','OPEN_API_CALLBACK','BUSINESS_PROVINCE','UID','CASH','ROLE','TYPE','BELONG','IS_OPEN','IS_PASS','OWN_RUN','STATUS','IS_JDPAY','IS_WHITE','VIPTIME','CHILD_NUM','GROUP_NUM','IS_ALIPAY','PARENT_ID','PLATFORM','IS_SELF_FEE','CONFIG_TYPE','CREATETIME','IS_OPEN_MINA','IS_SALESMAN','IS_SHOW_TIPS','LOAN_STATUS','ACCOUNT_TYPE','IS_QUICK_CASH','SALESMAN_TAG','SUB_CONFIG_ID','USE_GROUP_NUM','CURRENT_LEVEL','SALES_STAFF_ID','IS_SCAN_SERVICE','LAST_LOGIN_TIME','CUSTOMER_SERVICE','IS_OPENAPI_ACCESS','IS_SUPER_SALESMAN','SUPER_SALESMAN_ID','ADVERTISEMENT_NUM','OPERATION_SERVICE','NEW_VERSION_USERS_NUM','SALESMAN_TAG_START_DAY','CREATE_TIME','UPDATE_TIME','FINANCE','ADVERTISEMENT_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'TP_PREPAY_CARD_REFUND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORG_ID,REFUND_NO,OPERATE_ID,CUSTOMER_ID,OPERATE_NAME,CUSTOMER_NAME,SALES_ORDER_NO,IS_DEL,IS_RE_PUT_CARD,REFUND_NUMBER,CREATE_TIME,UPDATE_TIME,ORDER_PRICE,REFUND_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_REFUND'
            AND COLUMN_NAME in('ID','ORG_ID','REFUND_NO','OPERATE_ID','CUSTOMER_ID','OPERATE_NAME','CUSTOMER_NAME','SALES_ORDER_NO','IS_DEL','IS_RE_PUT_CARD','REFUND_NUMBER','CREATE_TIME','UPDATE_TIME','ORDER_PRICE','REFUND_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 103 as fg,'TP_USERS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,INDIRECT,SALESPERCENT,DISTRIBUTOR_SALESPERCENT,MP,QQ,AREA,CITY,EMAIL,PHONE,LASTIP,MOBILE,PEOPLE,QRCODE,STATUS,ADDRESS,COMPANY,CONTACT,STORE_ID,BANK_NAME,BANK_USER,CREATEIP,LASTTIME,PASSWORD,PLUGSAVE,PROVINCE,REAL_NAME,USERNAME,ALIPAYNUM,BANK_ACOUNT,INDUSTRY_ID,USERS_TOKEN,FUND_PASSWORD,COMPANYADDRESS,USERS_HEADERPIC,PROTOCOL_VERSION,FUND_PASSWORD_SALT,STORE_DEFAULT_LOGO,GID,FOCUS,MONEY,AMOUNT,BELONG,DIYNUM,ISPUSH,ROLE_ID,VENDOR,API_USER,CARD_NUM,IS_APPLY,IS_JDPAY,UNION_ID,VIPTIME,IS_ALIPAY,MARKET_ID,PARENT_ID,PAY_LIMIT,PLATFORM,SALESMAN,WX_STATUS,APPLY_TIME,IS_CONFIRM,VERSION_ID,CONFIG_TYPE,CONNECTNUM,CREATETIME,DEALAMOUNT,IS_GROUP_BUY,IS_OPEN_MINA,IS_PROTOCOL,LOAN_STATUS,ONLINETIME,PROTOCOL_ID,SALER_AUDIT,VOICE_ON_OFF,ACTIVITYNUM,CASHOUT_LOCK,CONFIRM_TIME,IS_QUICK_CASH,MCARD_STATUS,SUB_CONFIG_ID,TOTALSMSNUM,INCOME_STATUS,MERCHANT_TYPE,PROTOCOL_TIME,PS_MODIFY_TIME,TOTALSMSUSED,TRANSFER_TIME,IS_SCAN_SERVICE,ISEMSCNPLPUSH,PW_RESET_STATUS,RECHARGE_LIMIT,WECHAT_CARD_NUM,ATTACHMENTSIZE,AUTO_WITHDRAWAL,LASTLOGINMONTH,LIFECIRCLE_TIME,LIQUIDATION_TYPE,CARD_CREATE_STATUS,LATESTONLINETIME,IS_SERVICENO_ACCESS,UPDATE_TIME,FINANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_USERS'
            AND COLUMN_NAME in('ID','INDIRECT','SALESPERCENT','DISTRIBUTOR_SALESPERCENT','MP','QQ','AREA','CITY','EMAIL','PHONE','LASTIP','MOBILE','PEOPLE','QRCODE','STATUS','ADDRESS','COMPANY','CONTACT','STORE_ID','BANK_NAME','BANK_USER','CREATEIP','LASTTIME','PASSWORD','PLUGSAVE','PROVINCE','REAL_NAME','USERNAME','ALIPAYNUM','BANK_ACOUNT','INDUSTRY_ID','USERS_TOKEN','FUND_PASSWORD','COMPANYADDRESS','USERS_HEADERPIC','PROTOCOL_VERSION','FUND_PASSWORD_SALT','STORE_DEFAULT_LOGO','GID','FOCUS','MONEY','AMOUNT','BELONG','DIYNUM','ISPUSH','ROLE_ID','VENDOR','API_USER','CARD_NUM','IS_APPLY','IS_JDPAY','UNION_ID','VIPTIME','IS_ALIPAY','MARKET_ID','PARENT_ID','PAY_LIMIT','PLATFORM','SALESMAN','WX_STATUS','APPLY_TIME','IS_CONFIRM','VERSION_ID','CONFIG_TYPE','CONNECTNUM','CREATETIME','DEALAMOUNT','IS_GROUP_BUY','IS_OPEN_MINA','IS_PROTOCOL','LOAN_STATUS','ONLINETIME','PROTOCOL_ID','SALER_AUDIT','VOICE_ON_OFF','ACTIVITYNUM','CASHOUT_LOCK','CONFIRM_TIME','IS_QUICK_CASH','MCARD_STATUS','SUB_CONFIG_ID','TOTALSMSNUM','INCOME_STATUS','MERCHANT_TYPE','PROTOCOL_TIME','PS_MODIFY_TIME','TOTALSMSUSED','TRANSFER_TIME','IS_SCAN_SERVICE','ISEMSCNPLPUSH','PW_RESET_STATUS','RECHARGE_LIMIT','WECHAT_CARD_NUM','ATTACHMENTSIZE','AUTO_WITHDRAWAL','LASTLOGINMONTH','LIFECIRCLE_TIME','LIQUIDATION_TYPE','CARD_CREATE_STATUS','LATESTONLINETIME','IS_SERVICENO_ACCESS','UPDATE_TIME','FINANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'TP_PREPAY_CARD_BALANCE_CHANGE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CARD_NO,ORDER_SN,CHANGE_REMARK,CHANGE_TYPE,CREATE_TIME,UPDATE_TIME,AFTER_BALANCE,CHANGE_BALANCE,AFTER_TODAY_CONSUME_AMOUNT,CHANGE_TODAY_CONSUME_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_BALANCE_CHANGE_LOG'
            AND COLUMN_NAME in('ID','CARD_NO','ORDER_SN','CHANGE_REMARK','CHANGE_TYPE','CREATE_TIME','UPDATE_TIME','AFTER_BALANCE','CHANGE_BALANCE','AFTER_TODAY_CONSUME_AMOUNT','CHANGE_TODAY_CONSUME_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'TP_PREPAY_CARD_STORAGE_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,OPERATE_ID,OPERATE_NAME,STORAGE_ORDER,RELATION_ORDER,IS_DEL,OPERATE_TYPE,RECORD_NUMBER,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_STORAGE_RECORD'
            AND COLUMN_NAME in('ID','OPERATE_ID','OPERATE_NAME','STORAGE_ORDER','RELATION_ORDER','IS_DEL','OPERATE_TYPE','RECORD_NUMBER','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 18 as fg,'TP_CHANNEL_PRODUCT_COST_RATE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXIST_MAX_FEE,PRODUCT_CODE,PAYMENT_CHANNEL,EXIST_WITHDRAWAL_FEE,PAYMENT_CHANNEL_NAME,PAY_TYPE,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME,FB_RATE,MAX_FEE,MAX_RATE,MAX_FB_FEE,CHANNEL_RATE,MAX_CHANNEL_FEE,FB_WITHDRAWAL_FEE,CHANNEL_WITHDRAWAL_FEE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_CHANNEL_PRODUCT_COST_RATE'
            AND COLUMN_NAME in('ID','EXIST_MAX_FEE','PRODUCT_CODE','PAYMENT_CHANNEL','EXIST_WITHDRAWAL_FEE','PAYMENT_CHANNEL_NAME','PAY_TYPE','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME','FB_RATE','MAX_FEE','MAX_RATE','MAX_FB_FEE','CHANNEL_RATE','MAX_CHANNEL_FEE','FB_WITHDRAWAL_FEE','CHANNEL_WITHDRAWAL_FEE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'TP_PREPAY_CARD_SALES_ORDER_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORG_ID,CARD_SKU_ID,CARD_SPU_ID,CARD_SPU_NAME,PUBLISH_ORG_ID,SALES_ORDER_NO,IS_STATS,CARD_NUMBER,CREATE_TIME,UPDATE_TIME,CARD_PRICE,CARD_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_SALES_ORDER_DETAIL'
            AND COLUMN_NAME in('ID','ORG_ID','CARD_SKU_ID','CARD_SPU_ID','CARD_SPU_NAME','PUBLISH_ORG_ID','SALES_ORDER_NO','IS_STATS','CARD_NUMBER','CREATE_TIME','UPDATE_TIME','CARD_PRICE','CARD_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 39 as fg,'TP_MINA_AUTH' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APPID,OPENID,ICP_MEMOS,ACCOUNT_NAME,MINA_HEAD_IMG,PRE_AUTH_CODE,AUDIT_VERSION,DEPLOY_REMARK,MINA_NICK_NAME,MINA_USER_NAME,PLATFORM_TYPE,AUDIT_DEPLOY_ID,MINA_INTRODUCE,ONLINE_VERSION,PRINCIPAL_NAME,ONLINE_DEPLOY_ID,AUTHORIZER_REFRESH_TOKEN,STEP,DEL_FLAG,AUTH_TYPE,HAS_DEPLOY,AUTH_STATUS,MERCHANT_ID,SYNC_STATUS,ACCOUNT_TYPE,AUDIT_STATUS,QRCODE_STATUS,ICP_AUDIT_STATUS,UPDATE_AUDIT_STATUS,PLATFORM_VERIFY_TYPE,SERVER_DOMAIN_STATUS,BUSINESS_DOMAIN_STATUS,AUTH_TIME,CREATE_TIME,SUBMIT_TIME,UPDATE_TIME,STATUS_UPDATE_TIME,ONLINE_VERSION_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MINA_AUTH'
            AND COLUMN_NAME in('ID','APPID','OPENID','ICP_MEMOS','ACCOUNT_NAME','MINA_HEAD_IMG','PRE_AUTH_CODE','AUDIT_VERSION','DEPLOY_REMARK','MINA_NICK_NAME','MINA_USER_NAME','PLATFORM_TYPE','AUDIT_DEPLOY_ID','MINA_INTRODUCE','ONLINE_VERSION','PRINCIPAL_NAME','ONLINE_DEPLOY_ID','AUTHORIZER_REFRESH_TOKEN','STEP','DEL_FLAG','AUTH_TYPE','HAS_DEPLOY','AUTH_STATUS','MERCHANT_ID','SYNC_STATUS','ACCOUNT_TYPE','AUDIT_STATUS','QRCODE_STATUS','ICP_AUDIT_STATUS','UPDATE_AUDIT_STATUS','PLATFORM_VERIFY_TYPE','SERVER_DOMAIN_STATUS','BUSINESS_DOMAIN_STATUS','AUTH_TIME','CREATE_TIME','SUBMIT_TIME','UPDATE_TIME','STATUS_UPDATE_TIME','ONLINE_VERSION_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 23 as fg,'TP_MERCHANT_OPEN_LOCATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,AREA_CODE,AREA_NAME,CITY_CODE,CITY_NAME,USERNAME,IP_ADDRESS,PROVINCE_CODE,PROVINCE_NAME,PHONE_DEVICE_ID,MERCHANT_AREA_CODE,MERCHANT_AREA_NAME,MERCHANT_CITY_CODE,MERCHANT_CITY_NAME,MERCHANT_USERNAME,MERCHANT_PROVINCE_CODE,MERCHANT_PROVINCE_NAME,UID,USER_ID,CREATE_TIME,UPDATE_TIME,LATITUDE,LONGITUDE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MERCHANT_OPEN_LOCATION'
            AND COLUMN_NAME in('ID','AREA_CODE','AREA_NAME','CITY_CODE','CITY_NAME','USERNAME','IP_ADDRESS','PROVINCE_CODE','PROVINCE_NAME','PHONE_DEVICE_ID','MERCHANT_AREA_CODE','MERCHANT_AREA_NAME','MERCHANT_CITY_CODE','MERCHANT_CITY_NAME','MERCHANT_USERNAME','MERCHANT_PROVINCE_CODE','MERCHANT_PROVINCE_NAME','UID','USER_ID','CREATE_TIME','UPDATE_TIME','LATITUDE','LONGITUDE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 51 as fg,'TP_DEPOSIT_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BODY,EXT1,EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10,TOKEN,CARD_ID,REMARK,BANK_NAME,DEVICE_NO,SUB_MCH_ID,POS_FLOW_ID,CHANNEL_NUM,POS_BATCH_NO,POS_AUTH_CODE,DEPOSIT_ORDER_SN,MERCHANT_ORDER_SN,PLATFORM_ORDER_SN,REFERENCE_NUMBER,MERCHANT_DEPOSIT_ORDER_SN,UID,AGENT_ID,CHANNEL,GRANT_ID,PAY_TYPE,STORE_ID,CARD_TYPE,CASHIER_ID,THAW_STATUS,DEPOSIT_TYPE,ORDER_STATUS,SUB_CONFIG_ID,TRADE_STATUS,IS_PRINT_TICKET,DEPOSIT_TRADE_TYPE,DEPOSIT_REVOKE_TIME,TRADE_TIME,CREATE_TIME,REVOKE_TIME,UPDATE_TIME,DEPOSIT_TIME,THAW_PRICE,CONSUME_PRICE,DEPOSIT_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_DEPOSIT_ORDER'
            AND COLUMN_NAME in('ID','BODY','EXT1','EXT2','EXT3','EXT4','EXT5','EXT6','EXT7','EXT8','EXT9','EXT10','TOKEN','CARD_ID','REMARK','BANK_NAME','DEVICE_NO','SUB_MCH_ID','POS_FLOW_ID','CHANNEL_NUM','POS_BATCH_NO','POS_AUTH_CODE','DEPOSIT_ORDER_SN','MERCHANT_ORDER_SN','PLATFORM_ORDER_SN','REFERENCE_NUMBER','MERCHANT_DEPOSIT_ORDER_SN','UID','AGENT_ID','CHANNEL','GRANT_ID','PAY_TYPE','STORE_ID','CARD_TYPE','CASHIER_ID','THAW_STATUS','DEPOSIT_TYPE','ORDER_STATUS','SUB_CONFIG_ID','TRADE_STATUS','IS_PRINT_TICKET','DEPOSIT_TRADE_TYPE','DEPOSIT_REVOKE_TIME','TRADE_TIME','CREATE_TIME','REVOKE_TIME','UPDATE_TIME','DEPOSIT_TIME','THAW_PRICE','CONSUME_PRICE','DEPOSIT_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 77 as fg,'TP_MERCHANT_AUTHORIZE_APPLY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,MOBILE,CERT_PIC,CERT_TYPE,SUB_MCHID,STORE_NAME,CERT_NUMBER,LICENSE_PIC,QRCODE_DATA,APPLYMENT_ID,COMPANY_NAME,LEGAL_PERSON,REJECT_PARAM,SUBJECT_TYPE,BUSINESS_CODE,CERT_TYPE_NAME,ID_CARD_NUMBER,MICRO_BIZ_TYPE,REJECT_REASON,STORE_ADDRESS,LICENSE_NUMBER,STORE_AREA_CODE,STORE_AREA_NAME,STORE_CITY_CODE,STORE_CITY_NAME,APPLYMENT_STATE,AUTHORIZE_STATE,COMPANY_ADDRESS,LICENSE_END_DATE,STORE_HEADER_PIC,STORE_INDOOR_PIC,COMPANY_PROVE_PIC,SUBJECT_TYPE_NAME,CONFIRM_MCHID_LIST,CONTACT_ID_DOC_COPY,CONTACT_PERIOD_END,LICENSE_BEGIN_DATE,MICRO_BIZ_TYPE_NAME,STORE_ADDRESS_CODE,APPLYMENT_STATE_MSG,STORE_PROVINCE_CODE,STORE_PROVINCE_NAME,CONTACT_PERIOD_BEGIN,IDENTIFICATION_NAME,IDENTIFICATION_TYPE,CONTACT_ID_DOC_COPY_BACK,IDENTIFICATION_NUMBER,IDENTIFICATION_ADDRESS,IDENTIFICATION_BACK_PIC,IDENTIFICATION_END_DATE,IDENTIFICATION_FRONT_PIC,IDENTIFICATION_TYPE_NAME,IDENTIFICATION_BEGIN_DATE,BUSINESS_AUTHORIZATION_LETTER,UID,IS_DEL,STORE_ID,CHANNEL_ID,AUDIT_STATUS,CONTACT_TYPE,APPLY_CHANNEL,AUTHORIZE_TYPE,CERT_TYPE_VALUE,LICENSE_IS_LONG,APPLYMENT_METHOD,APPLYMENT_STATUS,AUTHORIZE_STATUS,LIQUIDATION_TYPE,CONTACT_ID_DOC_TYPE,SUBJECT_TYPE_VALUE,MICRO_BIZ_TYPE_VALUE,CONTACT_PERIOD_IS_LONG,IDENTIFICATION_IS_LONG,IDENTIFICATION_TYPE_VALUE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MERCHANT_AUTHORIZE_APPLY'
            AND COLUMN_NAME in('ID','NAME','MOBILE','CERT_PIC','CERT_TYPE','SUB_MCHID','STORE_NAME','CERT_NUMBER','LICENSE_PIC','QRCODE_DATA','APPLYMENT_ID','COMPANY_NAME','LEGAL_PERSON','REJECT_PARAM','SUBJECT_TYPE','BUSINESS_CODE','CERT_TYPE_NAME','ID_CARD_NUMBER','MICRO_BIZ_TYPE','REJECT_REASON','STORE_ADDRESS','LICENSE_NUMBER','STORE_AREA_CODE','STORE_AREA_NAME','STORE_CITY_CODE','STORE_CITY_NAME','APPLYMENT_STATE','AUTHORIZE_STATE','COMPANY_ADDRESS','LICENSE_END_DATE','STORE_HEADER_PIC','STORE_INDOOR_PIC','COMPANY_PROVE_PIC','SUBJECT_TYPE_NAME','CONFIRM_MCHID_LIST','CONTACT_ID_DOC_COPY','CONTACT_PERIOD_END','LICENSE_BEGIN_DATE','MICRO_BIZ_TYPE_NAME','STORE_ADDRESS_CODE','APPLYMENT_STATE_MSG','STORE_PROVINCE_CODE','STORE_PROVINCE_NAME','CONTACT_PERIOD_BEGIN','IDENTIFICATION_NAME','IDENTIFICATION_TYPE','CONTACT_ID_DOC_COPY_BACK','IDENTIFICATION_NUMBER','IDENTIFICATION_ADDRESS','IDENTIFICATION_BACK_PIC','IDENTIFICATION_END_DATE','IDENTIFICATION_FRONT_PIC','IDENTIFICATION_TYPE_NAME','IDENTIFICATION_BEGIN_DATE','BUSINESS_AUTHORIZATION_LETTER','UID','IS_DEL','STORE_ID','CHANNEL_ID','AUDIT_STATUS','CONTACT_TYPE','APPLY_CHANNEL','AUTHORIZE_TYPE','CERT_TYPE_VALUE','LICENSE_IS_LONG','APPLYMENT_METHOD','APPLYMENT_STATUS','AUTHORIZE_STATUS','LIQUIDATION_TYPE','CONTACT_ID_DOC_TYPE','SUBJECT_TYPE_VALUE','MICRO_BIZ_TYPE_VALUE','CONTACT_PERIOD_IS_LONG','IDENTIFICATION_IS_LONG','IDENTIFICATION_TYPE_VALUE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 52 as fg,'TP_LIFECIRCLE_CONSUME' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,TOKEN,OPENID,REMARK,ORDER_SN,BANK_TYPE,DISCOUNT,PAY_TOKEN,CALL_BACK_URL,MERCHANT_ORDER_SN,UID,MODE,TYPE,MCHID,RED_ID,USER_ID,AGENT_ID,CHANNEL,PAY_TIME,PAY_TYPE,STORE_ID,MARKREAD,PRINT_NUM,RED_PRE_ID,CASHIER_ID,PAY_STATUS,CREATE_TIME,DISCOUNT_ID,REFUND_STATUS,RECHARGEACT_ID,PREFERENTIAL_ID,REPAIR_ORDER_STATUS,UPDATE_TIME_AUTO,FEE,REFUND,CASH_FEE,RATE_FEE,RED_MONEY,COUPON_FEE,ORDER_PRICE,COMMISSION_FEE,DISCOUNT_MONEY,ORDER_SUMPRICE,AUTOWIPINGZERO,ADDITIONAL_PRICE,COMMISSION_RATE_FEE,RECHARGEACT_AMOUNT,PREFERENTIAL_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_CONSUME'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','TOKEN','OPENID','REMARK','ORDER_SN','BANK_TYPE','DISCOUNT','PAY_TOKEN','CALL_BACK_URL','MERCHANT_ORDER_SN','UID','MODE','TYPE','MCHID','RED_ID','USER_ID','AGENT_ID','CHANNEL','PAY_TIME','PAY_TYPE','STORE_ID','MARKREAD','PRINT_NUM','RED_PRE_ID','CASHIER_ID','PAY_STATUS','CREATE_TIME','DISCOUNT_ID','REFUND_STATUS','RECHARGEACT_ID','PREFERENTIAL_ID','REPAIR_ORDER_STATUS','UPDATE_TIME_AUTO','FEE','REFUND','CASH_FEE','RATE_FEE','RED_MONEY','COUPON_FEE','ORDER_PRICE','COMMISSION_FEE','DISCOUNT_MONEY','ORDER_SUMPRICE','AUTOWIPINGZERO','ADDITIONAL_PRICE','COMMISSION_RATE_FEE','RECHARGEACT_AMOUNT','PREFERENTIAL_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 92 as fg,'TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,UID,AD_ID,CITY_ID,STORE_ID,PROVINCE_ID,NAME,FLOOR,OWNER,AD_NAME,BANK_NO,MOBILE,REGION,ADDRESS,LICENSE,PLAZA_ID,BANK_NAME,CITY_NAME,LATITUDE,STORE_PIC,WX_OPENID,ERROR_CODE,LONGITUDE,PLAZA_NAME,STORE_NAME,BUSINESS_ID,TRADER_NAME,BANK_CARD_PIC,CITY_COMPANY,BUSINESS_NAME,ERROR_MESSAGE,ID_CARD_NUMBER,OWNER_BACK_URL,PROVINCE_NAME,SHOW_ERROR_MSG,STORE_HEAD_PIC,UNIONPAY_CODE,ACCOUNT_MOBILE,OWNER_FRONT_URL,INCOME_ERROR_MSG,INSIDE_SCENE_PIC,STORE_MANAGE_PIC,WD_CATEGORYNAME,SETTLER_ID_CARD_NO,STORE_FINANCE_PIC,BUSINESS_PLACE_PIC,CONTACT_ID_DOC_COPY,CONTACT_PERIOD_END,LICENSE_ELECTRONIC,SETTLER_ID_CARD_NAME,CONTACT_PERIOD_BEGIN,OWNER_CERTIFICATE_NO,FINAL_INCOME_SYNC_TIME,CONTACT_ID_DOC_COPY_BACK,SETTLER_ID_CARD_BACK_PIC,SETTLER_ID_CARD_END_DATE,AUTHORIZE_SAVE_ERROR_MSG,SETTLER_ID_CARD_FRONT_PIC,SETTLER_ID_CARD_BEGIN_DATE,OWNER_CERTIFICATE_ADDRESS,SETTLER_NOT_LEGAL_PROVE_PIC,ALIPAY_AUTHORIZE_SAVE_ERROR_MSG,BUSINESS_AUTHORIZATION_LETTER,WECHAT_AUTHORIZE_SAVE_ERROR_MSG,BANK_TYPE,SYNC_FLAG,CHANGE_FLAG,TRADER_TYPE,UNITY_CAT_ID,CHECK_STATUS,CONTACT_TYPE,SETTLER_TYPE,BUSINESS_TYPE,KA_INCOME_FLAG,NEED_NEW_SUB_NO,CALL_INCOME_STATUS,CONTACT_ID_DOC_TYPE,AUTHORIZE_SAVE_FLAG,HAS_OFFLINE_CONFIRM,SETTLER_ID_CARD_TYPE,CONTACT_PERIOD_IS_LONG,SETTLER_ID_CARD_IS_LONG,STORE_PIC_CHECK_STATUS,OWNER_CERTIFICATE_TYPE,ALIPAY_AUTHORIZE_SAVE_FLAG,WECHAT_AUTHORIZE_SAVE_FLAG,CREATE_TIME,UPDATE_TIME,EFFECTIVE_END_DATE,CERTIFICATE_END_DATE,EFFECTIVE_START_DATE,CERTIFICATE_START_DATE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP'
            AND COLUMN_NAME in('ID','UID','AD_ID','CITY_ID','STORE_ID','PROVINCE_ID','NAME','FLOOR','OWNER','AD_NAME','BANK_NO','MOBILE','REGION','ADDRESS','LICENSE','PLAZA_ID','BANK_NAME','CITY_NAME','LATITUDE','STORE_PIC','WX_OPENID','ERROR_CODE','LONGITUDE','PLAZA_NAME','STORE_NAME','BUSINESS_ID','TRADER_NAME','BANK_CARD_PIC','CITY_COMPANY','BUSINESS_NAME','ERROR_MESSAGE','ID_CARD_NUMBER','OWNER_BACK_URL','PROVINCE_NAME','SHOW_ERROR_MSG','STORE_HEAD_PIC','UNIONPAY_CODE','ACCOUNT_MOBILE','OWNER_FRONT_URL','INCOME_ERROR_MSG','INSIDE_SCENE_PIC','STORE_MANAGE_PIC','WD_CATEGORYNAME','SETTLER_ID_CARD_NO','STORE_FINANCE_PIC','BUSINESS_PLACE_PIC','CONTACT_ID_DOC_COPY','CONTACT_PERIOD_END','LICENSE_ELECTRONIC','SETTLER_ID_CARD_NAME','CONTACT_PERIOD_BEGIN','OWNER_CERTIFICATE_NO','FINAL_INCOME_SYNC_TIME','CONTACT_ID_DOC_COPY_BACK','SETTLER_ID_CARD_BACK_PIC','SETTLER_ID_CARD_END_DATE','AUTHORIZE_SAVE_ERROR_MSG','SETTLER_ID_CARD_FRONT_PIC','SETTLER_ID_CARD_BEGIN_DATE','OWNER_CERTIFICATE_ADDRESS','SETTLER_NOT_LEGAL_PROVE_PIC','ALIPAY_AUTHORIZE_SAVE_ERROR_MSG','BUSINESS_AUTHORIZATION_LETTER','WECHAT_AUTHORIZE_SAVE_ERROR_MSG','BANK_TYPE','SYNC_FLAG','CHANGE_FLAG','TRADER_TYPE','UNITY_CAT_ID','CHECK_STATUS','CONTACT_TYPE','SETTLER_TYPE','BUSINESS_TYPE','KA_INCOME_FLAG','NEED_NEW_SUB_NO','CALL_INCOME_STATUS','CONTACT_ID_DOC_TYPE','AUTHORIZE_SAVE_FLAG','HAS_OFFLINE_CONFIRM','SETTLER_ID_CARD_TYPE','CONTACT_PERIOD_IS_LONG','SETTLER_ID_CARD_IS_LONG','STORE_PIC_CHECK_STATUS','OWNER_CERTIFICATE_TYPE','ALIPAY_AUTHORIZE_SAVE_FLAG','WECHAT_AUTHORIZE_SAVE_FLAG','CREATE_TIME','UPDATE_TIME','EFFECTIVE_END_DATE','CERTIFICATE_END_DATE','EFFECTIVE_START_DATE','CERTIFICATE_START_DATE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'TP_PREPAY_CARD_SPU' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,USE_DESC,COVER_URL,CARD_SPU_ID,OPERATE_ID,CARD_SPU_NAME,OPERATE_NAME,IS_DEL,SPU_STATUS,IS_MINA_SALES,CARD_SHAPE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_PREPAY_CARD_SPU'
            AND COLUMN_NAME in('ID','USE_DESC','COVER_URL','CARD_SPU_ID','OPERATE_ID','CARD_SPU_NAME','OPERATE_NAME','IS_DEL','SPU_STATUS','IS_MINA_SALES','CARD_SHAPE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
