<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwRiskWorkOrderWarnDailyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwRiskWorkOrderWarnDailyDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="PUSH_APPLICATION" property="pushApplication" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RISK_RECORD_ID" property="riskRecordId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PUSH_TIME" property="pushTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`PUSH_APPLICATION`,`RISK_RECORD_ID`,`PUSH_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_RISK_WORK_ORDER_WARN_DAILY-->
            <insert id="insert" >
            INSERT INTO HW_RISK_WORK_ORDER_WARN_DAILY
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="pushApplication != null">`PUSH_APPLICATION`,</if>
        <if test="riskRecordId != null">`RISK_RECORD_ID`,</if>
        <if test="pushTime != null">`PUSH_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="pushApplication != null">#{pushApplication,jdbcType=VARCHAR},</if>
        <if test="riskRecordId != null">#{riskRecordId,jdbcType=INTEGER},</if>
        <if test="pushTime != null">#{pushTime,jdbcType=TIMESTAMP},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>
    </mapper>
