<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PaBankUnionpayCodeDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PaBankUnionpayCodeDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNIONPAY_CODE" property="unionpayCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_SHORT_NAME" property="bankShortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ABBREVIATION" property="bankAbbreviation" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BANK_CODE`,`CITY_CODE`,`BRANCH_NAME`,`UNIONPAY_CODE`,`BANK_SHORT_NAME`,`BANK_ABBREVIATION`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PA_BANK_UNIONPAY_CODE-->
            <insert id="insert" >
                    INSERT INTO TP_PA_BANK_UNIONPAY_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据联行号查询开户行-->
            <select id="getByUnionpayCode" resultType="String">
                    SELECT /*MS-TP-PA-BANK-UNIONPAY-CODE-GETBYUNIONPAYCODE*/  branch_name from tp_pa_bank_unionpay_code
        where unionpay_code = #{unionpayCode,jdbcType=VARCHAR}
            </select>

            <!--根据联行号查询信息-->
            <select id="getInfoByUnionPayCode" resultMap="BaseResultMap">
                    SELECT /*MS-TP-PA-BANK-UNIONPAY-CODE-GETINFOBYUNIONPAYCODE*/  <include refid="Base_Column_List" /> from tp_pa_bank_unionpay_code
        where unionpay_code = #{unionpayCode,jdbcType=VARCHAR} limit 1
            </select>
    </mapper>
