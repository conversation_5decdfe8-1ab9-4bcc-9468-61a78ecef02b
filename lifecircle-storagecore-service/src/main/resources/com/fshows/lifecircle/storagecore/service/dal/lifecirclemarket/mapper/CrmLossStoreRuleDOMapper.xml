<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmLossStoreRuleDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmLossStoreRuleDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OFF_PERCENT" property="offPercent" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PUSH_STATUS" property="pushStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PUSH_FREQUENCY" property="pushFrequency" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="LAST_WEEK_AVERAGE_DAILY" property="lastWeekAverageDaily" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`IS_DEL`,`USER_ID`,`OFF_PERCENT`,`PUSH_STATUS`,`PUSH_FREQUENCY`,`CREATE_TIME`,`UPDATE_TIME`,`LAST_WEEK_AVERAGE_DAILY`
    </sql>


            <!--insert:LM_CRM_GROUP-->
            <insert id="insert" >
                    INSERT INTO LM_CRM_LOSS_STORE_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="lastWeekAverageDaily != null">`LAST_WEEK_AVERAGE_DAILY`,</if>
            <if test="offPercent != null">`OFF_PERCENT`,</if>
            <if test="pushStatus != null">`PUSH_STATUS`,</if>
            <if test="pushFrequency != null">`PUSH_FREQUENCY`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="lastWeekAverageDaily != null">#{lastWeekAverageDaily,jdbcType=INTEGER},</if>
            <if test="offPercent != null">#{offPercent,jdbcType=INTEGER},</if>
            <if test="pushStatus != null">#{pushStatus,jdbcType=INTEGER},</if>
            <if test="pushFrequency != null">#{pushFrequency,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--update:LM_CRM_LOSS_STORE_RULE-->
            <update id="update" >
                    update /*MS-LM-CRM-LOSS-STORE-RULE-UPDATE*/ LM_CRM_LOSS_STORE_RULE
        set update_time = now()
        <if test="userId != null">
            ,USER_ID = #{userId,jdbcType=INTEGER}
        </if>
        <if test="lastWeekAverageDaily != null">
            ,LAST_WEEK_AVERAGE_DAILY = #{lastWeekAverageDaily,jdbcType=INTEGER}
        </if>
        <if test="offPercent != null">
            ,OFF_PERCENT = #{offPercent,jdbcType=INTEGER}
        </if>
        <if test="pushStatus != null">
            ,PUSH_STATUS = #{pushStatus,jdbcType=INTEGER}
        </if>
        <if test="pushFrequency != null">
            ,PUSH_FREQUENCY = #{pushFrequency,jdbcType=INTEGER}
        </if>
        <if test="isDel != null">
            ,IS_DEL = #{isDel,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            ,CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        where
        id = #{id,jdbcType=BIGINT}
            </update>

            <!--根据userId获取出来规则-->
            <select id="getOneByUserId" resultMap="BaseResultMap">
                    SELECT /*MS-LM-CRM-LOSS-STORE-RULE-GETONEBYUSERID*/  <include refid="Base_Column_List" /> FROM LM_CRM_LOSS_STORE_RULE WHERE USER_ID = #{userId,jdbcType=INTEGER} and IS_DEL = 0 ORDER BY ID DESC LIMIT 1
            </select>
    </mapper>
