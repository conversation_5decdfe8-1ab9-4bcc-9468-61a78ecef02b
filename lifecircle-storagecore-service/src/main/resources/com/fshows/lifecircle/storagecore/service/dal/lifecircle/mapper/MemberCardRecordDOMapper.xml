<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MemberCardRecordDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MemberCardRecordDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_NO`,`TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MEMBER_CARD_RECORD-->
            <insert id="insert" >
                    INSERT INTO TP_MEMBER_CARD_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--getLastOne-->
            <select id="getLastOne" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MEMBER-CARD-RECORD-GETLASTONE*/  <include refid="Base_Column_List" /> FROM `tp_member_card_record` ORDER BY `id` DESC LIMIT 1
            </select>

            <!--batchInsert-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    insert into tp_member_card_record(card_no,create_time,update_time) values
        <foreach collection="list" item="data" separator=",">
            (#{data.cardNo,jdbcType=VARCHAR},#{data.createTime,jdbcType=INTEGER},#{data.updateTime,jdbcType=INTEGER})
        </foreach>
            </select>
    </mapper>
