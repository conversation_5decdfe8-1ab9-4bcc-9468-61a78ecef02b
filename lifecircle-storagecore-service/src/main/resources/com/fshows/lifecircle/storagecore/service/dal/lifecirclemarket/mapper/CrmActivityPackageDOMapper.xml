<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmActivityPackageDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmActivityPackageDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PERIOD" property="period" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_NUMBER" property="merchantNumber" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="PERIOD_END_DATE" property="periodEndDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="PERIOD_BEGIN_DATE" property="periodBeginDate" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="TOTAL" property="total" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="BALANCE" property="balance" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`TOKEN`,`PERIOD`,`MERCHANT_NUMBER`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`PERIOD_END_DATE`,`PERIOD_BEGIN_DATE`,`TOTAL`,`BALANCE`
    </sql>


    <!--insert:TP_ACTIVITY_PACKAGE-->
    <insert id="insert">
        INSERT INTO TP_ACTIVITY_PACKAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="period != null">`PERIOD`,</if>
            <if test="merchantNumber != null">`MERCHANT_NUMBER`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="periodEndDate != null">`PERIOD_END_DATE`,</if>
            <if test="periodBeginDate != null">`PERIOD_BEGIN_DATE`,</if>
            <if test="total != null">`TOTAL`,</if>
            <if test="balance != null">`BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="period != null">#{period,jdbcType=VARCHAR},</if>
            <if test="merchantNumber != null">#{merchantNumber,jdbcType=VARCHAR},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="periodEndDate != null">#{periodEndDate,jdbcType=TIMESTAMP},</if>
            <if test="periodBeginDate != null">#{periodBeginDate,jdbcType=TIMESTAMP},</if>
            <if test="total != null">#{total,jdbcType=DECIMAL},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--getByLiquidationTypeAndMerchantNumber-->
    <select id="getByLiquidationTypeAndMerchantNumber" resultMap="BaseResultMap">
        select /*MS-TP-ACTIVITY-PACKAGE-GETBYLIQUIDATIONTYPEANDMERCHANTNUMBER*/
        <include refid="Base_Column_List"/>
        from TP_ACTIVITY_PACKAGE
        where merchant_number= #{merchantNumber,jdbcType=VARCHAR}
        and liquidation_type= #{liquidationType,jdbcType=TINYINT}
        order by period_end_date desc limit 1
    </select>
</mapper>
