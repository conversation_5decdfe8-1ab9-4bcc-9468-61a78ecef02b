<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleactivity.mapper.ActivityPermissionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircleactivity.dataobject.ActivityPermissionDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="DESCRIBE" property="describe" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ROLE" property="role" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`DESCRIBE`,`ACTIVITY_ID`,`ROLE`,`STATUS`,`USER_ID`,`END_TIME`,`START_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_ACTIVITY_PERMISSION-->
            <insert id="insert" >
                    <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_ACTIVITY_PERMISSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="role != null">`ROLE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="describe != null">`DESCRIBE`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="role != null">#{role,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="describe != null">#{describe,jdbcType=VARCHAR},</if>
            <if test="startTime != null"> #{startTime,jdbcType=INTEGER},</if>
            <if test="activityId != null"> #{activityId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--根据商户id角色活动id查询活动权限-->
            <select id="getByUidActivityId" resultMap="BaseResultMap">
                    SELECT
        `id`,
        `describe`,
        `activity_id` as activityId,
        `role`,
        `status`,
        `user_id` as userId,
        `end_time` as endTime,
        `start_time` as startTime,
        `create_time` as createTime,
        `update_time` as updateTime
        from tp_activity_permission
        WHERE
        `user_id` = #{uid, jdbcType=INTEGER}
        and `role` = 3
        and `activity_id` = #{activityId, jdbcType=VARCHAR}
        limit 1
            </select>

            <!--update table:TP_ACTIVITY_PERMISSION-->
            <update id="updateById" >
                    UPDATE /*MS-TP-ACTIVITY-PERMISSION-UPDATEBYID*/ TP_ACTIVITY_PERMISSION
        <set>
            <if test="role != null">`ROLE` = #{role,jdbcType=TINYINT},</if>
            <if test="status != null">`STATUS` = #{status,jdbcType=TINYINT},</if>
            <if test="userId != null">`USER_ID` = #{userId,jdbcType=INTEGER},</if>
            <if test="endTime != null">`END_TIME` = #{endTime,jdbcType=INTEGER},</if>
            <if test="describe != null">`DESCRIBE` = #{describe,jdbcType=VARCHAR},</if>
            <if test="startTime != null">`START_TIME` = #{startTime,jdbcType=INTEGER},</if>
            <if test="activityId != null">`ACTIVITY_ID` = #{activityId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">`CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">`UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE
        `ID` = #{id,jdbcType=BIGINT}
            </update>
    </mapper>
