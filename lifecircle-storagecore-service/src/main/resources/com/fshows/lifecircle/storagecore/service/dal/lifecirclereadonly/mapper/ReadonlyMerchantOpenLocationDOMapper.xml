<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyMerchantOpenLocationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyMerchantOpenLocationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IP_ADDRESS" property="ipAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE_DEVICE_ID" property="phoneDeviceId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_AREA_CODE" property="merchantAreaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_AREA_NAME" property="merchantAreaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_CITY_CODE" property="merchantCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_CITY_NAME" property="merchantCityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_USERNAME" property="merchantUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_PROVINCE_CODE" property="merchantProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_PROVINCE_NAME" property="merchantProvinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="LATITUDE" property="latitude" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="LONGITUDE" property="longitude" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>

        <resultMap id="MerchantOpenRiskList" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.resultmap.MerchantOpenRiskList">

                <result column="area_name" property="areaName" javaType="String"/>

                <result column="city_name" property="cityName" javaType="String"/>

                <result column="user_name" property="userName" javaType="String"/>

                <result column="agent_name" property="agentName" javaType="String"/>

                <result column="sales_name" property="salesName" javaType="String"/>

                <result column="operate_name" property="operateName" javaType="String"/>

                <result column="province_name" property="provinceName" javaType="String"/>

                <result column="merchant_area_name" property="merchantAreaName" javaType="String"/>

                <result column="merchant_city_name" property="merchantCityName" javaType="String"/>

                <result column="merchant_province_name" property="merchantProvinceName" javaType="String"/>

                <result column="uid" property="uid" javaType="Integer"/>

                <result column="agent_id" property="agentId" javaType="Integer"/>

                <result column="sales_id" property="salesId" javaType="Integer"/>

                <result column="operate_id" property="operateId" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`AREA_CODE`,`AREA_NAME`,`CITY_CODE`,`CITY_NAME`,`USERNAME`,`IP_ADDRESS`,`PROVINCE_CODE`,`PROVINCE_NAME`,`PHONE_DEVICE_ID`,`MERCHANT_AREA_CODE`,`MERCHANT_AREA_NAME`,`MERCHANT_CITY_CODE`,`MERCHANT_CITY_NAME`,`MERCHANT_USERNAME`,`MERCHANT_PROVINCE_CODE`,`MERCHANT_PROVINCE_NAME`,`UID`,`USER_ID`,`CREATE_TIME`,`UPDATE_TIME`,`LATITUDE`,`LONGITUDE`
    </sql>


            <!--根据商户id查询出异地开户情况信息-->
            <select id="findOpenRiskList" resultMap="MerchantOpenRiskList">
                    SELECT
        location.`user_id` as operateId,
        location.`username` as operateName,
        location.uid as uid,
        location.`merchant_username` as userName,
        b.`id` as agentId,
        b.`username` as agentName,
        c.id as salesId,
        c.`username` as salesName,
        location.`province_name` as provinceName,
        location.`city_name` as cityName,
        location.`area_name` as areaName,
        location.`merchant_province_name` as merchantProvinceName,
        location.`merchant_city_name` as merchantCityName,
        location.`merchant_area_name` as merchantAreaName
        FROM
        `tp_merchant_open_location` location
        LEFT JOIN `tp_users` users on location.`uid` = users.id
        LEFT JOIN tp_user b on users.`belong` = b.id
        LEFT JOIN `tp_user` c on users.`salesman` = c.id
        LEFT JOIN `tp_users_entry_ext` ext on users.`users_token` = ext.`token`
        LEFT JOIN `tp_merchant_number` number on location.`uid` = number.`uid`
        WHERE
        location.`city_code` != location.`merchant_city_code`
        and users.`id` in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
            </select>
    </mapper>
