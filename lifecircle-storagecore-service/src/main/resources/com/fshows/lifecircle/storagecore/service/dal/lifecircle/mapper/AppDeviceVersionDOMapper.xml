<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AppDeviceVersionDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AppDeviceVersionDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="DEVICE_SN" property="deviceSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APP_VERSION" property="appVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PUB_VERSION_ID" property="pubVersionId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>

        <resultMap id="DeviceVersionResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.DeviceVersionResult">

                <result column="device_sn" property="deviceSn" javaType="String"/>

                <result column="username" property="username" javaType="String"/>

                <result column="platform_name" property="platformName" javaType="String"/>

                <result column="uid" property="uid" javaType="Integer"/>

                <result column="platform" property="platform" javaType="Integer"/>
        </resultMap>

    <sql id="Base_Column_List">
    `ID`,`DEVICE_SN`,`APP_VERSION`,`PLATFORM`,`PUB_VERSION_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_APP_DEVICE_VERSION-->
            <insert id="insert" >
                    INSERT INTO TP_APP_DEVICE_VERSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="deviceSn != null">`DEVICE_SN`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="pubVersionId != null">`PUB_VERSION_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="deviceSn != null">#{deviceSn,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="pubVersionId != null">#{pubVersionId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据version_id 分页查询列表-->
            <select id="findByVersionId" resultMap="DeviceVersionResultMap">
                    select /*MS-TP-APP-DEVICE-VERSION-FINDBYVERSIONID*/ a.device_sn deviceSn,u.id uid,u.username username,a.platform platform
        from tp_app_device_version a
        left join tp_equipment_sn e on a.device_sn = e.init_sn
        left join tp_users as u on e.uid = u.id
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
        limit #{pageStartNum,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
            </select>

            <!--根据version_id 分页查询列表-->
            <select id="findByVersionIdNew" resultMap="DeviceVersionResultMap">
                    select
        a.device_sn deviceSn,
        a.platform platform
        from tp_app_device_version a
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
        limit #{pageStartNum,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
            </select>

            <!--根据version_id 统计数量列表-->
            <select id="countByVersionId" resultType="Integer">
                    SELECT
         count(*) 
        from tp_app_device_version a
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
            </select>
    </mapper>
