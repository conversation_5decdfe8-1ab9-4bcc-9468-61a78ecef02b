<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardSalesOrderDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardSalesOrderDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALES_ORDER_NO" property="salesOrderNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_STATS" property="isStats" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_NUMBER" property="cardNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="CARD_PRICE" property="cardPrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CARD_AMOUNT" property="cardAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`CARD_SKU_ID`,`CARD_SPU_ID`,`CARD_SPU_NAME`,`PUBLISH_ORG_ID`,`SALES_ORDER_NO`,`IS_STATS`,`CARD_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`,`CARD_PRICE`,`CARD_AMOUNT`
    </sql>


            <!--insert:TP_PREPAY_CARD_SALES_ORDER_DETAIL-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_SALES_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="publishOrgId != null">`PUBLISH_ORG_ID`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="isStats != null">`IS_STATS`,</if>
            <if test="cardNumber != null">`CARD_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="publishOrgId != null">#{publishOrgId,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="isStats != null">#{isStats,jdbcType=TINYINT},</if>
            <if test="cardNumber != null">#{cardNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>
    </mapper>
