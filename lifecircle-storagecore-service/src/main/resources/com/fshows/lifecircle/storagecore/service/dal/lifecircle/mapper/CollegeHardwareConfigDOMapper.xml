<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.CollegeHardwareConfigDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.CollegeHardwareConfigDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEVICE_NO" property="deviceNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DELETE" property="isDelete" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASHIER_ID" property="cashierId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`DEVICE_NO`,`TYPE`,`USER_ID`,`STORE_ID`,`IS_DELETE`,`CASHIER_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_COLLEGE_HARDWARE_CONFIG-->
            <insert id="insert" >
                    INSERT INTO TP_COLLEGE_HARDWARE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="isDelete != null">`IS_DELETE`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取微信高校硬件-->
            <select id="getCollegeHardwareList" resultMap="BaseResultMap">
                    SELECT
        TOKEN,DEVICE_NO,USER_ID,STORE_ID
        from TP_COLLEGE_HARDWARE_CONFIG WHERE type = #{type,jdbcType=INTEGER}
        AND IS_DELETE = 0
        AND USER_ID &gt; 0;
            </select>
    </mapper>
