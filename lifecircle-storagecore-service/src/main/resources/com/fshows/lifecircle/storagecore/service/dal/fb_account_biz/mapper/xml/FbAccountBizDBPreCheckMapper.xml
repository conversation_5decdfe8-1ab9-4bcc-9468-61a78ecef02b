<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.FbAccountBizDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 36 as fg,'ACC_ACCOUNT_WITHDRAW' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE,AMOUNT,SP_ID,SPC_ID,REASON,REMARK,SOURCE,BANK_NAME,WALLET_ID,ACCOUNT_ID,VOUCHER_NO,OPER<PERSON>OR_ID,VOUCHER_URL,WITHDRAW_NO,ACCOUNT_NAME,CHANNEL_CODE,INNER_REASON,WITHDRAW_DATE,WITHDRAW_MODE,OUT_WITHDRAW_NO,BANK_BRANCH_CODE,SETTLE_BANK_CODE,WITHDRAW_STATUS,SETTLE_ACCOUNT_ID,SETTLE_ACCOUNT_NO,VIRTUAL_WALLET_ID,PLATFORM_WITHDRAW_NO,TRACK_WITHDRAW_STATUS,DEL_FLAG,IS_REFUNDED,SETTLE_TYPE,OPERATOR_TYPE,CREATE_TIME,FINISH_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_ACCOUNT_WITHDRAW'
            AND COLUMN_NAME in('ID','FEE','AMOUNT','SP_ID','SPC_ID','REASON','REMARK','SOURCE','BANK_NAME','WALLET_ID','ACCOUNT_ID','VOUCHER_NO','OPERATOR_ID','VOUCHER_URL','WITHDRAW_NO','ACCOUNT_NAME','CHANNEL_CODE','INNER_REASON','WITHDRAW_DATE','WITHDRAW_MODE','OUT_WITHDRAW_NO','BANK_BRANCH_CODE','SETTLE_BANK_CODE','WITHDRAW_STATUS','SETTLE_ACCOUNT_ID','SETTLE_ACCOUNT_NO','VIRTUAL_WALLET_ID','PLATFORM_WITHDRAW_NO','TRACK_WITHDRAW_STATUS','DEL_FLAG','IS_REFUNDED','SETTLE_TYPE','OPERATOR_TYPE','CREATE_TIME','FINISH_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 24 as fg,'ACC_PLATFORM_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORDER_PRICE,FROZEN_AMOUNT,SP_ID,BILL_NO,BLOC_ID,DATA_ID,ORDER_TYPE,SETTLE_DATE,PRODUCT_TYPE,STORE_BILL_NO,PLATFORM_CODE,PLATFORM_GOODS_ID,PLATFORM_ORDER_NO,PLATFORM_STORE_ID,PRODUCT_SCENE_CODE,BUSINESS_SCENE_CODE,PLATFORM_GOODS_NAME,PLATFORM_STORE_NAME,PLATFORM_SETTLE_DATE,ORDER_REAL_TYPE,CREATE_TIME,UPDATE_TIME,PLATFORM_TRADE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PLATFORM_ORDER'
            AND COLUMN_NAME in('ID','ORDER_PRICE','FROZEN_AMOUNT','SP_ID','BILL_NO','BLOC_ID','DATA_ID','ORDER_TYPE','SETTLE_DATE','PRODUCT_TYPE','STORE_BILL_NO','PLATFORM_CODE','PLATFORM_GOODS_ID','PLATFORM_ORDER_NO','PLATFORM_STORE_ID','PRODUCT_SCENE_CODE','BUSINESS_SCENE_CODE','PLATFORM_GOODS_NAME','PLATFORM_STORE_NAME','PLATFORM_SETTLE_DATE','ORDER_REAL_TYPE','CREATE_TIME','UPDATE_TIME','PLATFORM_TRADE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'ACC_PLATFORM_SETTLE_BILL_DATA' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,DATA_ID,OSS_URL,CONFIG_ID,DATA_SOURCE,DOWNLOAD_ID,PLATFORM_CODE,END_DATE,RUN_DATE,START_DATE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PLATFORM_SETTLE_BILL_DATA'
            AND COLUMN_NAME in('ID','DATA_ID','OSS_URL','CONFIG_ID','DATA_SOURCE','DOWNLOAD_ID','PLATFORM_CODE','END_DATE','RUN_DATE','START_DATE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 18 as fg,'ACC_BLOC_PLATFORM_SCENE_CONFIG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BLOC_ID,SCENE_CODE,SCENE_NAME,SCENE_TYPE,DATA_SOURCE,PLATFORM_CODE,PARENT_SCENE_CODE,RELATED_BILL_CODE,RULE_SWITCH,FUND_FLOW_TYPE,IS_PRESET_SCENE,SERVICE_FEE_SWITCH,MATCH_BILL_PRODUCT_TYPE,RECONCILIATION_SWITCH,CREATE_TIME,UPDATE_TIME,CUSTOM_SERVICE_RATE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_BLOC_PLATFORM_SCENE_CONFIG'
            AND COLUMN_NAME in('ID','BLOC_ID','SCENE_CODE','SCENE_NAME','SCENE_TYPE','DATA_SOURCE','PLATFORM_CODE','PARENT_SCENE_CODE','RELATED_BILL_CODE','RULE_SWITCH','FUND_FLOW_TYPE','IS_PRESET_SCENE','SERVICE_FEE_SWITCH','MATCH_BILL_PRODUCT_TYPE','RECONCILIATION_SWITCH','CREATE_TIME','UPDATE_TIME','CUSTOM_SERVICE_RATE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'ACC_CHANNEL_PAYMENT_DIFF' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BILL_AMOUNT,PAYMENT_AMOUNT,BILL_NO,BLOC_ID,CHECK_REMARK,CHECK_STATUS,PLATFORM_CODE,APPLY_OPERATOR_ID,CHECK_OPERATOR_ID,DEL_FLAG,CHECK_TIME,CREATE_TIME,UPDATE_TIME,PAYMENT_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_CHANNEL_PAYMENT_DIFF'
            AND COLUMN_NAME in('ID','BILL_AMOUNT','PAYMENT_AMOUNT','BILL_NO','BLOC_ID','CHECK_REMARK','CHECK_STATUS','PLATFORM_CODE','APPLY_OPERATOR_ID','CHECK_OPERATOR_ID','DEL_FLAG','CHECK_TIME','CREATE_TIME','UPDATE_TIME','PAYMENT_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 37 as fg,'ACC_PROFIT_SHARE_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FEE,AMOUNT,ACTUAL_AMOUNT,SP_ID,CARD_NO,REMARK,BANK_NAME,CARD_NAME,WALLET_ID,ACCOUNT_ID,OUT_TASK_ID,OPERATOR_ID,TRANSFER_NO,WITHDRAW_NO,AUTO_WITHDRAW,RULE_RECORD_ID,ACC_MERCHANT_ID,OUT_TRANSFER_NO,OUT_WITHDRAW_NO,SETTLE_BANK_CODE,SETTLE_ACCOUNT_NO,SETTLE_ACCOUNT_NAME,PROFIT_SHARE_BATCH_ID,PROFIT_SHARE_DETAIL_ID,PROFIT_SHARE_DETAIL_STATUS,PROFIT_SHARE_MERCHANT_TYPE,PROFIT_SHARE_WITHDRAW_STATUS,DEL_FLAG,SETTLE_TYPE,CREATE_TIME,UPDATE_TIME,PROFIT_SHARE_CREATE_TIME,PROFIT_SHARE_FINISH_TIME,PROFIT_SHARE_WITHDRAW_TIME,SERVICE_FEE_RATIO,PROFIT_SHARE_RATIO' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PROFIT_SHARE_DETAIL'
            AND COLUMN_NAME in('ID','FEE','AMOUNT','ACTUAL_AMOUNT','SP_ID','CARD_NO','REMARK','BANK_NAME','CARD_NAME','WALLET_ID','ACCOUNT_ID','OUT_TASK_ID','OPERATOR_ID','TRANSFER_NO','WITHDRAW_NO','AUTO_WITHDRAW','RULE_RECORD_ID','ACC_MERCHANT_ID','OUT_TRANSFER_NO','OUT_WITHDRAW_NO','SETTLE_BANK_CODE','SETTLE_ACCOUNT_NO','SETTLE_ACCOUNT_NAME','PROFIT_SHARE_BATCH_ID','PROFIT_SHARE_DETAIL_ID','PROFIT_SHARE_DETAIL_STATUS','PROFIT_SHARE_MERCHANT_TYPE','PROFIT_SHARE_WITHDRAW_STATUS','DEL_FLAG','SETTLE_TYPE','CREATE_TIME','UPDATE_TIME','PROFIT_SHARE_CREATE_TIME','PROFIT_SHARE_FINISH_TIME','PROFIT_SHARE_WITHDRAW_TIME','SERVICE_FEE_RATIO','PROFIT_SHARE_RATIO')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'ACC_PLATFORM_SETTLE_BILL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FROZEN_AMOUNT,SETTLE_AMOUNT,GOODS_SETTLE_AMOUNT,REFUND_SETTLE_AMOUNT,PAYMENT_SETTLE_AMOUNT,RECONCILIATION_AMOUNT,SP_ID,BILL_NO,BLOC_ID,REMARK,SOURCE,SETTLE_DATE,CHANNEL_CODE,PLATFORM_CODE,BILL_CHECK_SOURCE,PLATFORM_SETTLE_DATE,BILL_CHECK_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PLATFORM_SETTLE_BILL'
            AND COLUMN_NAME in('ID','FROZEN_AMOUNT','SETTLE_AMOUNT','GOODS_SETTLE_AMOUNT','REFUND_SETTLE_AMOUNT','PAYMENT_SETTLE_AMOUNT','RECONCILIATION_AMOUNT','SP_ID','BILL_NO','BLOC_ID','REMARK','SOURCE','SETTLE_DATE','CHANNEL_CODE','PLATFORM_CODE','BILL_CHECK_SOURCE','PLATFORM_SETTLE_DATE','BILL_CHECK_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'ACC_PLATFORM_CONFIG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SPC_ID,CONFIG_ID,EXT_CONFIG,PLATFORM_CODE,PLATFORM_NAME,PLATFORM_USER_ID,DEL_FLAG,ACTIVE_FLAG,NEXT_END_DAY,NEXT_START_DAY,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PLATFORM_CONFIG'
            AND COLUMN_NAME in('ID','SPC_ID','CONFIG_ID','EXT_CONFIG','PLATFORM_CODE','PLATFORM_NAME','PLATFORM_USER_ID','DEL_FLAG','ACTIVE_FLAG','NEXT_END_DAY','NEXT_START_DAY','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'ACC_REQ_SP_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SP_ID,REQ_ID,REQ_NAME,DEL_FLAG,REQ_TYPE,ROLE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_REQ_SP_RELATION'
            AND COLUMN_NAME in('ID','SP_ID','REQ_ID','REQ_NAME','DEL_FLAG','REQ_TYPE','ROLE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'ACC_ORG_RELATION_ACCOUNT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORG_ID,BLOC_ID,FULL_PATH,ACCOUNT_ID,ACCOUNT_TYPE,DEL_FLAG,MAX_BIND_NUM,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_ORG_RELATION_ACCOUNT'
            AND COLUMN_NAME in('ID','ORG_ID','BLOC_ID','FULL_PATH','ACCOUNT_ID','ACCOUNT_TYPE','DEL_FLAG','MAX_BIND_NUM','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 27 as fg,'ACC_PLATFORM_STORE_SETTLE_BILL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FROZEN_COUNT,FROZEN_AMOUNT,SETTLE_AMOUNT,GOODS_SETTLE_COUNT,GOODS_SETTLE_AMOUNT,REFUND_SETTLE_COUNT,PAYMENT_SETTLE_COUNT,REFUND_SETTLE_AMOUNT,PAYMENT_SETTLE_AMOUNT,RECONCILIATION_AMOUNT,SP_ID,BILL_NO,BLOC_ID,REMARK,SOURCE,SETTLE_DATE,STORE_BILL_NO,PLATFORM_CODE,PLATFORM_STORE_ID,BILL_HANDLE_SOURCE,PLATFORM_STORE_NAME,PLATFORM_SETTLE_DATE,PROFIT_SHARE_BATCH_ID,BILL_HANDLE_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PLATFORM_STORE_SETTLE_BILL'
            AND COLUMN_NAME in('ID','FROZEN_COUNT','FROZEN_AMOUNT','SETTLE_AMOUNT','GOODS_SETTLE_COUNT','GOODS_SETTLE_AMOUNT','REFUND_SETTLE_COUNT','PAYMENT_SETTLE_COUNT','REFUND_SETTLE_AMOUNT','PAYMENT_SETTLE_AMOUNT','RECONCILIATION_AMOUNT','SP_ID','BILL_NO','BLOC_ID','REMARK','SOURCE','SETTLE_DATE','STORE_BILL_NO','PLATFORM_CODE','PLATFORM_STORE_ID','BILL_HANDLE_SOURCE','PLATFORM_STORE_NAME','PLATFORM_SETTLE_DATE','PROFIT_SHARE_BATCH_ID','BILL_HANDLE_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'ACC_SERVICE_FEE_CONFIG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SPC_ID,CONFIG_ID,PLATFORM_CODE,FUBEI_ACCOUNT_ID,VIRTUAL_WALLET_ID,PLATFORM_VIRTUAL_WALLET_ID,DEL_FLAG,UNDERTAKE,PROFIT_SHARE_MODEL,PLATFORM_SETTLE_MODE,PLATFORM_BILL_TIME_SPAN,CREATE_TIME,UPDATE_TIME,SERVICE_FEE_RATIO' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_SERVICE_FEE_CONFIG'
            AND COLUMN_NAME in('ID','SPC_ID','CONFIG_ID','PLATFORM_CODE','FUBEI_ACCOUNT_ID','VIRTUAL_WALLET_ID','PLATFORM_VIRTUAL_WALLET_ID','DEL_FLAG','UNDERTAKE','PROFIT_SHARE_MODEL','PLATFORM_SETTLE_MODE','PLATFORM_BILL_TIME_SPAN','CREATE_TIME','UPDATE_TIME','SERVICE_FEE_RATIO')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 25 as fg,'ACC_PROFIT_SHARE_ORDER_DETAIL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ORDER_PRICE,RELATION_ORDER_ID,PROFIT_SHARE_PRICE,SP_ID,TYPE,BLOC_ID,ACCOUNT_ID,ORDER_TYPE,STORE_BILL_NO,PLATFORM_CODE,RULE_RECORD_ID,PLATFORM_ORDER_NO,PLATFORM_STORE_ID,RECEIVE_MERCHANT_ID,PROFIT_SHARE_DETAIL_ID,PROFIT_SHARE_MERCHANT_ID,PROFIT_SHARE_ORDER_DETAIL_ID,DEL_FLAG,DEAL_STATUS,SETTLE_TYPE,CREATE_TIME,UPDATE_TIME,RATIO,SERVICE_FEE_RATIO' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_PROFIT_SHARE_ORDER_DETAIL'
            AND COLUMN_NAME in('ID','ORDER_PRICE','RELATION_ORDER_ID','PROFIT_SHARE_PRICE','SP_ID','TYPE','BLOC_ID','ACCOUNT_ID','ORDER_TYPE','STORE_BILL_NO','PLATFORM_CODE','RULE_RECORD_ID','PLATFORM_ORDER_NO','PLATFORM_STORE_ID','RECEIVE_MERCHANT_ID','PROFIT_SHARE_DETAIL_ID','PROFIT_SHARE_MERCHANT_ID','PROFIT_SHARE_ORDER_DETAIL_ID','DEL_FLAG','DEAL_STATUS','SETTLE_TYPE','CREATE_TIME','UPDATE_TIME','RATIO','SERVICE_FEE_RATIO')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'ACC_SERVICE_PROVIDER_CHANNEL' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SPC_ID,ACTIVE,USER_ID,CHANNEL_CODE,OUT_ACCOUNT_ID,EXTEND_CONFIGURATION,IS_SYNC,DEL_FLAG,ROLE_TYPE,IS_DEFAULT,CONFIG_STATUS,BALANCE_SWITCH,IS_AFFILIATION,FUND_SETTLE_MODE,WITHDRAWAL_MODE,PROFIT_SHARE_MODE,ENABLE_ACCOUNT_WALLET,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'ACC_SERVICE_PROVIDER_CHANNEL'
            AND COLUMN_NAME in('ID','SPC_ID','ACTIVE','USER_ID','CHANNEL_CODE','OUT_ACCOUNT_ID','EXTEND_CONFIGURATION','IS_SYNC','DEL_FLAG','ROLE_TYPE','IS_DEFAULT','CONFIG_STATUS','BALANCE_SWITCH','IS_AFFILIATION','FUND_SETTLE_MODE','WITHDRAWAL_MODE','PROFIT_SHARE_MODE','ENABLE_ACCOUNT_WALLET','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
