<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.CollectDataDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.dataobject.CollectDataDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="URL" property="url" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FAIL_URL" property="failUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DATA_TYPE_ID" property="dataTypeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COLLECT_DATA_ID" property="collectDataId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`URL`,`FAIL_URL`,`DATA_TYPE_ID`,`COLLECT_DATA_ID`,`STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:FK_COLLECT_DATA-->
            <insert id="insert" >
            INSERT INTO FK_COLLECT_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="url != null">`URL`,</if>
        <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
        <if test="collectDataId != null">`COLLECT_DATA_ID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="url != null">#{url,jdbcType=VARCHAR},</if>
        <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
        <if test="collectDataId != null">#{collectDataId,jdbcType=VARCHAR},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据唯一约束UniqCollectData更新表:fk_collect_data-->
            <update id="updateByCollectDataId" >
                    <![CDATA[
        UPDATE /*MS-FK-COLLECT-DATA-UPDATEBYCOLLECTDATAID*/ fk_collect_data
        SET
            STATUS          = #{status,jdbcType=TINYINT}
        WHERE
            COLLECT_DATA_ID = #{collectDataId,jdbcType=VARCHAR}
        ]]>
            </update>

            <!--根据唯一约束UniqCollectData获取数据:fk_collect_data-->
            <select id="getByUniqCollectData" resultMap="BaseResultMap">
                    SELECT /*MS-FK-COLLECT-DATA-GETBYUNIQCOLLECTDATA*/  <include refid="Base_Column_List" />
        FROM fk_collect_data
        WHERE
        <![CDATA[
            COLLECT_DATA_ID = #{collectDataId,jdbcType=VARCHAR}
        ]]>
        LIMIT 1
            </select>
    </mapper>
