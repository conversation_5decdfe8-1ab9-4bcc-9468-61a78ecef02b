<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentShipmentOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentShipmentOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="NOTE" property="note" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ORDER_NUM" property="orderNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EXAMIN_TIME" property="examinTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="SHIPMENT_TYPE" property="shipmentType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEPOT_LOCATION" property="depotLocation" jdbcType="INTEGER"
                    javaType="Integer"/>
        </resultMap>

        <resultMap id="AgentInStorageOrderDetailMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AgentInStorageOrderDetailMap">

            <result column="note" property="note" javaType="java.lang.String"/>

            <result column="init_sn" property="initSn" javaType="java.lang.String"/>

            <result column="order_sn" property="orderSn" javaType="java.lang.String"/>

            <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

            <result column="equipment_id" property="equipmentId" javaType="java.lang.Integer"/>
        </resultMap>

        <sql id="Base_Column_List">
            `ID`,`NOTE`,`ORDER_SN`,`AGENT_ID`,`ORDER_NUM`,`ARRIVAL_NUM`,`CREATE_TIME`,`EXAMIN_TIME`,`UPDATE_TIME`,`ORDER_STATUS`,`SHIPMENT_TYPE`,`DEPOT_LOCATION`
        </sql>


        <!--insert:TP_EQUIPMENT_SHIPMENT_ORDER-->
        <insert id="insert">
            INSERT INTO TP_EQUIPMENT_SHIPMENT_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="note != null">`NOTE`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="agentId != null">`AGENT_ID`,</if>
                <if test="orderNum != null">`ORDER_NUM`,</if>
                <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="examinTime != null">`EXAMIN_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="shipmentType != null">`SHIPMENT_TYPE`,</if>
            <if test="depotLocation != null">`DEPOT_LOCATION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="examinTime != null">#{examinTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="shipmentType != null">#{shipmentType,jdbcType=TINYINT},</if>
            <if test="depotLocation != null">#{depotLocation,jdbcType=INTEGER},</if>
        </trim>
        </insert>

        <!--代理商查询入库订单详情-->
        <select id="agentInStorageOrderDetailQuery" resultMap="AgentInStorageOrderDetailMap">
            SELECT
            shipment.order_sn,shipment.create_time,shipment.order_num,esn.init_sn,esn.equipment_id,ename.equipment_name
            from tp_equipment_shipment_order shipment
            LEFT JOIN tp_equipment_order_relation relation on shipment.id = relation.order_id
            LEFT JOIN tp_equipment_sn esn on esn.id = relation.sn_id
            LEFT JOIN tp_equipment e on e.id = esn.equipment_id
            LEFT JOIN tp_equipment_name ename on ename.id = e.equipment_name_id
            where
            shipment.agent_id = #{agentId,jdbcType=INTEGER}
            and shipment.order_sn = #{orderSn,jdbcType=VARCHAR}
            and relation.order_type = 3
            and relation.is_del = 0 and esn.is_del = 0
            order by esn.init_sn
        </select>

        <!--代理商查询回库订单详情-->
        <select id="agentBackStorageOrderDetailQuery" resultMap="AgentInStorageOrderDetailMap">
            SELECT
            purchase.order_sn,purchase.create_time,purchase.order_num,purchase.note,esn.init_sn,esn.equipment_id,ename.equipment_name
            from tp_equipment_purchase_order purchase
            LEFT JOIN tp_equipment_order_relation relation on relation.order_id = purchase.id
            LEFT JOIN tp_equipment_sn esn on esn.id = relation.sn_id
            LEFT JOIN tp_equipment e on e.id = esn.equipment_id
            LEFT JOIN tp_equipment_name ename on ename.id = e.equipment_name_id
            where
            purchase.agent_id = #{agentId,jdbcType=INTEGER} and purchase.purchase_type = 2
            and purchase.order_sn = #{orderSn,jdbcType=VARCHAR}
            and relation.order_type = 2
            and relation.is_del = 0 and esn.is_del = 0
            order by esn.init_sn
        </select>
    </mapper>
