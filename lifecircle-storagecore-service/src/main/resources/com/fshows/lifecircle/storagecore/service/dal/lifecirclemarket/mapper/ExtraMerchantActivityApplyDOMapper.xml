<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ExtraMerchantActivityApplyDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.MerchantActivityApplyDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="APPLY_NO" property="applyNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="COMPANY" property="company" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SUB_MCH_ID" property="subMchId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="USERNAME" property="username" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ACTIVITY_ID" property="activityId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="OUT_APPLY_NO" property="outApplyNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="ACTIVITY_OPEN_TIME" property="activityOpenTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="ACTIVITY_SIGN_TIME" property="activitySignTime" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`APPLY_NO`,`COMPANY`,`SUB_MCH_ID`,`USERNAME`,`ACTIVITY_ID`,`MERCHANT_NO`,`OUT_APPLY_NO`,`REJECT_REASON`,`UID`,`IS_DEL`,`CHANNEL_ID`,`ACTIVITY_STATUS`,`ACTIVITY_OPEN_TIME`,`ACTIVITY_SIGN_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <select id="findMarketActivityCrmListPageCount" resultType="java.lang.Integer">
        SELECT
        COUNT(*) AS total
        FROM
        lm_merchant_activity_apply apply
        LEFT JOIN lm_merchant_activity_apply_ext ext ON apply.apply_no = ext.apply_no
        WHERE apply.is_del= 0
        AND apply.activity_id IN
        <foreach close=")" collection="activityIdList" index="index" item="activityId" open="(" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="belong != null">
            AND apply.belong = #{belong,jdbcType=INTEGER}
        </if>
        <if test="username != null">
            AND apply.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="subMchId != null">
            AND apply.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="merchantNo != null">
            AND apply.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            AND apply.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activitySignStartTime != null">
            AND apply.activity_sign_time <![CDATA[ >= ]]> #{activitySignStartTime,jdbcType=INTEGER}
        </if>
        <if test="activitySignEndTime != null">
            AND apply.activity_sign_time <![CDATA[ <= ]]> #{activitySignEndTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenStartTime != null">
            AND apply.activity_open_time <![CDATA[ >= ]]> #{activityOpenStartTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenEndTime != null">
            AND apply.activity_open_time <![CDATA[ <= ]]> #{activityOpenEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStatus != null">
            AND apply.activity_rebate_apply_status = #{rebateApplyStatus,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStartTime != null">
            AND apply.activity_rebate_apply_time <![CDATA[ >= ]]> #{rebateApplyStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyEndTime != null">
            AND apply.activity_rebate_apply_time <![CDATA[ <= ]]> #{rebateApplyEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateBeginMonth != null">
            AND apply.activity_rebate_begin_month = #{rebateBeginMonth,jdbcType=INTEGER}
        </if>
    </select>
    <select id="findMarketActivityCrmListPageResult"
            resultType="com.fshows.lifecircle.storagecore.service.domain.dto.MarketActivityApplyListDTO">
        SELECT
        apply.id,
        apply.activity_sign_time activitySignTime,
        apply.activity_open_time activityOpenTime,
        apply.uid,
        apply.username,
        apply.company,
        apply.sub_mch_id subMchId,
        apply.merchant_no merchantNo,
        apply.activity_status activityStatus,
        apply.activity_id activityId,
        apply.reject_reason rejectReason,
        apply.apply_no applyNo,
        apply.activity_rebate_apply_status activityRebateApplyStatus,
        apply.activity_rebate_apply_time activityRebateApplyTime,
        apply.activity_rebate_begin_month activityRebateBeginMonth,
        apply.activity_rebate_reject_reason activityRebateRejectReason,
        ext.merchant_attribute merchantAttribute,
        ext.apply_info_attribute applyInfoAttribute
        FROM lm_merchant_activity_apply apply
        LEFT JOIN lm_merchant_activity_apply_ext ext ON apply.apply_no = ext.apply_no
        WHERE apply.is_del= 0
        AND apply.activity_id IN
        <foreach close=")" collection="activityIdList" index="index" item="activityId" open="(" separator=",">
            #{activityId,jdbcType=VARCHAR}
        </foreach>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="belong != null">
            AND apply.belong = #{belong,jdbcType=INTEGER}
        </if>
        <if test="username != null">
            AND apply.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="subMchId != null">
            AND apply.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        </if>
        <if test="merchantNo != null">
            AND apply.merchant_no = #{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="activityStatus != null">
            AND apply.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStatus != null">
            AND apply.activity_rebate_apply_status = #{rebateApplyStatus,jdbcType=INTEGER}
        </if>
        <if test="activitySignStartTime != null">
            AND apply.activity_sign_time <![CDATA[ >= ]]> #{activitySignStartTime,jdbcType=INTEGER}
        </if>
        <if test="activitySignEndTime != null">
            AND apply.activity_sign_time <![CDATA[ <= ]]> #{activitySignEndTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenStartTime != null">
            AND apply.activity_open_time <![CDATA[ >= ]]> #{activityOpenStartTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenEndTime != null">
            AND apply.activity_open_time <![CDATA[ <= ]]> #{activityOpenEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStartTime != null">
            AND apply.activity_rebate_apply_time <![CDATA[ >= ]]> #{rebateApplyStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyEndTime != null">
            AND apply.activity_rebate_apply_time <![CDATA[ <= ]]> #{rebateApplyEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateBeginMonth != null">
            AND apply.activity_rebate_begin_month = #{rebateBeginMonth,jdbcType=INTEGER}
        </if>
        <if test="sort == null or sort == 1">
            ORDER BY apply.activity_sign_time DESC
        </if>
        <if test="sort == 2">
            ORDER BY apply.activity_open_time DESC
        </if>
        limit #{startRow},#{limit}
    </select>

</mapper>
