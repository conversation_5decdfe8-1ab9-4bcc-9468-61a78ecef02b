<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmMerchantNationalSubsidyProductsDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmMerchantNationalSubsidyProductsDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CODE_TYPE" property="codeType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PROVINCE" property="province" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_ID" property="productId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_NAME" property="productName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="GOODS_BAR_CODE" property="goodsBarCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_BRAND" property="productBrand" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_IMAGE" property="productImage" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_MODEL" property="productModel" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PRODUCT_CATEGORY" property="productCategory" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ENERGY_EFFICIENCY" property="energyEfficiency" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MANUFACT_CERT_CODE" property="manufactCertCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="PRODUCT_SORT" property="productSort" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="AVAILABLE_STATUS" property="availableStatus" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="BASIS_PRICE" property="basisPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PRODUCT_PRICE" property="productPrice" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`CODE_TYPE`,`PROVINCE`,`PRODUCT_ID`,`PRODUCT_NAME`,`GOODS_BAR_CODE`,`PRODUCT_BRAND`,`PRODUCT_IMAGE`,`PRODUCT_MODEL`,`PRODUCT_CATEGORY`,`ENERGY_EFFICIENCY`,`MANUFACT_CERT_CODE`,`UID`,`IS_DEL`,`PRODUCT_SORT`,`AVAILABLE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`BASIS_PRICE`,`PRODUCT_PRICE`
    </sql>


    <!--insert:TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS-->
    <insert id="insert">
        INSERT INTO TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="productId != null">`PRODUCT_ID`,</if>
            <if test="productName != null">`PRODUCT_NAME`,</if>
            <if test="goodsBarCode != null">`GOODS_BAR_CODE`,</if>
            <if test="productBrand != null">`PRODUCT_BRAND`,</if>
            <if test="productImage != null">`PRODUCT_IMAGE`,</if>
            <if test="productModel != null">`PRODUCT_MODEL`,</if>
            <if test="productCategory != null">`PRODUCT_CATEGORY`,</if>
            <if test="energyEfficiency != null">`ENERGY_EFFICIENCY`,</if>
            <if test="manufactCertCode != null">`MANUFACT_CERT_CODE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="productSort != null">`PRODUCT_SORT`,</if>
            <if test="availableStatus != null">`AVAILABLE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="basisPrice != null">`BASIS_PRICE`,</if>
            <if test="productPrice != null">`PRODUCT_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="productId != null">#{productId,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="goodsBarCode != null">#{goodsBarCode,jdbcType=VARCHAR},</if>
            <if test="productBrand != null">#{productBrand,jdbcType=VARCHAR},</if>
            <if test="productImage != null">#{productImage,jdbcType=VARCHAR},</if>
            <if test="productModel != null">#{productModel,jdbcType=VARCHAR},</if>
            <if test="productCategory != null">#{productCategory,jdbcType=VARCHAR},</if>
            <if test="energyEfficiency != null">#{energyEfficiency,jdbcType=VARCHAR},</if>
            <if test="manufactCertCode != null">#{manufactCertCode,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="productSort != null">#{productSort,jdbcType=INTEGER},</if>
            <if test="availableStatus != null">#{availableStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="basisPrice != null">#{basisPrice,jdbcType=DECIMAL},</if>
            <if test="productPrice != null">#{productPrice,jdbcType=DECIMAL},</if>
        </trim>
    </insert>

    <!--countByGoodsBarCode-->
    <select id="countByGoodsBarCode" resultType="java.lang.Integer">
        SELECT /*MS-TP-MERCHANT-NATIONAL-SUBSIDY-PRODUCTS-COUNTBYGOODSBARCODE*/ count(*) from
        TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS
        WHERE GOODS_BAR_CODE = #{goodsBarCode,jdbcType=VARCHAR}
        and uid = #{uid,jdbcType=INTEGER}
        and is_del = 0
    </select>
</mapper>
