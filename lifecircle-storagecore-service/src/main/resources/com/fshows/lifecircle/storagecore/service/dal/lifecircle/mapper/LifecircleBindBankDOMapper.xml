<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleBindBankDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleBindBankDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ACT_NM" property="actNm" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCT_ID" property="acctId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NO" property="bankNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_PIC" property="cardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ID_NUMBER" property="idNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_ACTIVE" property="isActive" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMOVE_STATUS" property="removeStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNIONPAY_CODE" property="unionpayCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECTED_REASON" property="rejectedReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LETTER_OF_AUTH_PIC" property="letterOfAuthPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_OPENING_PERMIT_PIC" property="accountOpeningPermitPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_READ" property="isRead" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ADD_FROM" property="addFrom" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_WAY" property="bindWay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_FROM" property="bindFrom" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_TYPE" property="bindType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BEGIN_TIME" property="beginTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_STATUS" property="bindStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHECK_STATUS" property="checkStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REVIEW_BANKCARD_REJECTION_STATUS" property="reviewBankcardRejectionStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MODIFY_TIME" property="modifyTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ACT_NM`,`TOKEN`,`ACCT_ID`,`BANK_NO`,`MOBILE`,`CARD_PIC`,`BANK_NAME`,`ID_NUMBER`,`IS_ACTIVE`,`REMOVE_STATUS`,`UNIONPAY_CODE`,`REJECTED_REASON`,`LETTER_OF_AUTH_PIC`,`ACCOUNT_OPENING_PERMIT_PIC`,`UID`,`IS_DEL`,`IS_READ`,`ADD_FROM`,`BIND_WAY`,`BIND_FROM`,`BIND_TYPE`,`BEGIN_TIME`,`BIND_STATUS`,`CREATE_TIME`,`UPDATE_TIME`,`CHECK_STATUS`,`LIQUIDATION_TYPE`,`REVIEW_BANKCARD_REJECTION_STATUS`,`MODIFY_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_BIND_BANK-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_BIND_BANK
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="acctId != null">`ACCT_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="cardPic != null">`CARD_PIC`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="idNumber != null">`ID_NUMBER`,</if>
            <if test="isActive != null">`IS_ACTIVE`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="addFrom != null">`ADD_FROM`,</if>
            <if test="bindWay != null">`BIND_WAY`,</if>
            <if test="bindFrom != null">`BIND_FROM`,</if>
            <if test="bindType != null">`BIND_TYPE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="acctId != null">#{acctId,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="cardPic != null">#{cardPic,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="idNumber != null">#{idNumber,jdbcType=VARCHAR},</if>
            <if test="isActive != null">#{isActive,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="addFrom != null">#{addFrom,jdbcType=TINYINT},</if>
            <if test="bindWay != null">#{bindWay,jdbcType=INTEGER},</if>
            <if test="bindFrom != null">#{bindFrom,jdbcType=TINYINT},</if>
            <if test="bindType != null">#{bindType,jdbcType=INTEGER},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
        </trim>
            </insert>

            <!--根据id批量查询出银行卡信息-->
            <select id="getCardBankAndCardNoById" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        tp_lifecircle_bind_bank
        where
        id in
        <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--根据银行卡得到银行卡信息-->
            <select id="getCardInfoByAcctIdList" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        tp_lifecircle_bind_bank
        where
        is_del = 0
        and
        bind_status = 1
        and
        is_active = 'y'
        and
        acct_id in
        <foreach close=")" collection="list" index="index" item="acctId" open="(" separator=",">
            #{acctId,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--根据token和当前date查询批量登记挂账数据列表-->
            <select id="getByToken" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-BIND-BANK-GETBYTOKEN*/  <include refid="Base_Column_List" />
        FROM
        tp_lifecircle_bind_bank
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND bind_status = 1
        AND check_status in (0,1)
        AND is_del = 0
        AND is_active = 'y'
        ORDER BY update_time DESC
        LIMIT 1
            </select>

            <!--通过token获取商户最新一条绑卡记录-->
            <select id="getLastByToken" resultMap="BaseResultMap">
                    select /*MS-TP-LIFECIRCLE-BIND-BANK-GETLASTBYTOKEN*/ <include refid="Base_Column_List" /> from tp_lifecircle_bind_bank
        where token = #{token,jdbcType=VARCHAR}
        order by id desc limit 1
            </select>

            <!--根据uid查询商户所有的绑卡记录-->
            <select id="getBindBankByUid" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        tp_lifecircle_bind_bank
        WHERE
        uid = #{uid, jdbcType=INTEGER}
        and is_del = 0
        limit 1
            </select>
    </mapper>
