<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleAgentOpenDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleAgentOpenDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="PID" property="pid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPID" property="appid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PAY_URL" property="payUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SECRET" property="secret" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CALLBACK_URL" property="callbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESOURCE_KEY" property="resourceKey" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AUTH_CALLBACK_URL" property="authCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AUDIT_CALLBACK_URL" property="auditCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMIT_CALLBACK_URL" property="remitCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_CALLBACK_URL" property="shareCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_CALLBACK_URL" property="refundCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SECOND_CALLBACK_URL" property="secondCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_CALLBACK_URL" property="withdrawCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_AUDIT_CALLBACK_URL" property="shareAuditCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_WITHDRAW_CALLBACK_URL" property="subWithdrawCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_ZFT_AUDIT_CALLBACK_URL" property="alipayZftAuditCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_REGISTER_CALLBACK_URL" property="accountRegisterCallbackUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="REFUND_FAIL_SWITCH" property="refundFailSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_CONFIG_OWN_CHANNEL" property="isConfigOwnChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BILL_DOWNLOAD_TEMPLATE_ID" property="billDownloadTemplateId" jdbcType="INTEGER"
        javaType="Integer"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`PID`,`APPID`,`PAY_URL`,`SECRET`,`CALLBACK_URL`,`RESOURCE_KEY`,`AUTH_CALLBACK_URL`,`AUDIT_CALLBACK_URL`,`REMIT_CALLBACK_URL`,`SHARE_CALLBACK_URL`,`REFUND_CALLBACK_URL`,`SECOND_CALLBACK_URL`,`WITHDRAW_CALLBACK_URL`,`SHARE_AUDIT_CALLBACK_URL`,`SUB_WITHDRAW_CALLBACK_URL`,`ALIPAY_ZFT_AUDIT_CALLBACK_URL`,`ACCOUNT_REGISTER_CALLBACK_URL`,`AGENT_ID`,`CREATE_TIME`,`UPDATE_TIME`,`REFUND_FAIL_SWITCH`,`IS_CONFIG_OWN_CHANNEL`,`BILL_DOWNLOAD_TEMPLATE_ID`
    </sql>


            <!--insert:TP_LIFECIRCLE_AGENT_OPEN-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_AGENT_OPEN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="secret != null">`SECRET`,</if>
            <if test="callbackUrl != null">`CALLBACK_URL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="pid != null">#{pid,jdbcType=VARCHAR},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="secret != null">#{secret,jdbcType=VARCHAR},</if>
            <if test="callbackUrl != null">#{callbackUrl,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--通过代理商id获取配置-->
            <select id="getInfoByAgentId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-AGENT-OPEN-GETINFOBYAGENTID*/  <include refid="Base_Column_List" />
        FROM TP_LIFECIRCLE_AGENT_OPEN
        WHERE agent_id = #{agentId,jdbcType=INTEGER}
        LIMIT 1
            </select>
    </mapper>
