<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.ShareMemberWithdrawSupplementDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.ShareMemberWithdrawSupplementDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TOKEN_NO" property="tokenNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CASH_STATUS" property="cashStatus" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FRONT_LOG_NO" property="frontLogNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR_ID" property="operatorId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPERATOR_NAME" property="operatorName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="NEW_SERIAL_NUMBER" property="newSerialNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ACCOUNT_ID" property="customerAccountId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_BIND_BANK_ID" property="customerBindBankId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="FEE_AMOUNT" property="feeAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>

            <result column="CASH_AMOUNT" property="cashAmount" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`REASON`,`TOKEN_NO`,`CASH_STATUS`,`FRONT_LOG_NO`,`OPERATOR_ID`,`OPERATOR_NAME`,`SERIAL_NUMBER`,`NEW_SERIAL_NUMBER`,`CUSTOMER_ACCOUNT_ID`,`CUSTOMER_BIND_BANK_ID`,`FINISH_TIME`,`CREATE_TIME`,`UPDATE_TIME`,`FEE_AMOUNT`,`CASH_AMOUNT`
        </sql>


        <!--insert:TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT-->
        <insert id="insert">
            INSERT INTO TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="reason != null">`REASON`,</if>
                <if test="tokenNo != null">`TOKEN_NO`,</if>
                <if test="cashStatus != null">`CASH_STATUS`,</if>
                <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
                <if test="operatorId != null">`OPERATOR_ID`,</if>
                <if test="operatorName != null">`OPERATOR_NAME`,</if>
                <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
                <if test="newSerialNumber != null">`NEW_SERIAL_NUMBER`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="newSerialNumber != null">#{newSerialNumber,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--批量查询状态不为成功的提现数据-->
        <select id="getDiffSupplementWithdrawList" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from
            tp_share_member_withdraw_supplement
            where
            new_serial_number in
            <foreach close=")" collection="list" index="index" item="newSerialNumber" open="(" separator=",">
                #{newSerialNumber,jdbcType=VARCHAR}
            </foreach>
            and
            cash_status in ('NOSUMBIT', 'PROCESSING', 'FAIL')
        </select>

        <!--根据订单号更新打款状态-->
        <update id="updateSupplementWithdrawBySerialNumber">
            update
                tp_share_member_withdraw_supplement
            set cash_status = #{cashStatus,jdbcType=VARCHAR}
            where new_serial_number = #{newSerialNumber,jdbcType=VARCHAR}
        </update>
    </mapper>
