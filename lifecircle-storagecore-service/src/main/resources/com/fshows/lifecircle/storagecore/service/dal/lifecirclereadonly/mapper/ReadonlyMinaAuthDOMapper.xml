<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyMinaAuthDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyMinaAuthDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APPID" property="appid" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OPENID" property="openid" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MINA_HEAD_IMG" property="minaHeadImg" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PRE_AUTH_CODE" property="preAuthCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AUDIT_VERSION" property="auditVersion" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DEPLOY_REMARK" property="deployRemark" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MINA_NICK_NAME" property="minaNickName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MINA_USER_NAME" property="minaUserName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PLATFORM_TYPE" property="platformType" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AUDIT_DEPLOY_ID" property="auditDeployId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MINA_INTRODUCE" property="minaIntroduce" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ONLINE_VERSION" property="onlineVersion" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PRINCIPAL_NAME" property="principalName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ONLINE_DEPLOY_ID" property="onlineDeployId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AUTHORIZER_REFRESH_TOKEN" property="authorizerRefreshToken" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STEP" property="step" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AUTH_TYPE" property="authType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="HAS_DEPLOY" property="hasDeploy" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AUTH_STATUS" property="authStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="MERCHANT_ID" property="merchantId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="SYNC_STATUS" property="syncStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ACCOUNT_TYPE" property="accountType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AUDIT_STATUS" property="auditStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="QRCODE_STATUS" property="qrcodeStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="UPDATE_AUDIT_STATUS" property="updateAuditStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PLATFORM_VERIFY_TYPE" property="platformVerifyType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="SERVER_DOMAIN_STATUS" property="serverDomainStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="BUSINESS_DOMAIN_STATUS" property="businessDomainStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AUTH_TIME" property="authTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="SUBMIT_TIME" property="submitTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="STATUS_UPDATE_TIME" property="statusUpdateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="ONLINE_VERSION_TIME" property="onlineVersionTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`
            ,`APPID`,`OPENID`,`ACCOUNT_NAME`,`MINA_HEAD_IMG`,`PRE_AUTH_CODE`,`AUDIT_VERSION`,`DEPLOY_REMARK`,`MINA_NICK_NAME`,`MINA_USER_NAME`,`PLATFORM_TYPE`,`AUDIT_DEPLOY_ID`,`MINA_INTRODUCE`,`ONLINE_VERSION`,`PRINCIPAL_NAME`,`ONLINE_DEPLOY_ID`,`AUTHORIZER_REFRESH_TOKEN`,`STEP`,`DEL_FLAG`,`AUTH_TYPE`,`HAS_DEPLOY`,`AUTH_STATUS`,`MERCHANT_ID`,`SYNC_STATUS`,`ACCOUNT_TYPE`,`AUDIT_STATUS`,`QRCODE_STATUS`,`UPDATE_AUDIT_STATUS`,`PLATFORM_VERIFY_TYPE`,`SERVER_DOMAIN_STATUS`,`BUSINESS_DOMAIN_STATUS`,`AUTH_TIME`,`CREATE_TIME`,`SUBMIT_TIME`,`UPDATE_TIME`,`STATUS_UPDATE_TIME`,`ONLINE_VERSION_TIME`
        </sql>


        <!--insert:TP_MINA_AUTH-->
        <insert id="insert">
            INSERT INTO TP_MINA_AUTH
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="appid != null">`APPID`,</if>
                <if test="openid != null">`OPENID`,</if>
                <if test="accountName != null">`ACCOUNT_NAME`,</if>
                <if test="minaHeadImg != null">`MINA_HEAD_IMG`,</if>
                <if test="preAuthCode != null">`PRE_AUTH_CODE`,</if>
                <if test="auditVersion != null">`AUDIT_VERSION`,</if>
                <if test="deployRemark != null">`DEPLOY_REMARK`,</if>
                <if test="minaNickName != null">`MINA_NICK_NAME`,</if>
            <if test="minaUserName != null">`MINA_USER_NAME`,</if>
            <if test="platformType != null">`PLATFORM_TYPE`,</if>
            <if test="auditDeployId != null">`AUDIT_DEPLOY_ID`,</if>
            <if test="minaIntroduce != null">`MINA_INTRODUCE`,</if>
            <if test="onlineVersion != null">`ONLINE_VERSION`,</if>
            <if test="principalName != null">`PRINCIPAL_NAME`,</if>
            <if test="onlineDeployId != null">`ONLINE_DEPLOY_ID`,</if>
            <if test="authorizerRefreshToken != null">`AUTHORIZER_REFRESH_TOKEN`,</if>
            <if test="step != null">`STEP`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="authType != null">`AUTH_TYPE`,</if>
            <if test="hasDeploy != null">`HAS_DEPLOY`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="syncStatus != null">`SYNC_STATUS`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="qrcodeStatus != null">`QRCODE_STATUS`,</if>
            <if test="updateAuditStatus != null">`UPDATE_AUDIT_STATUS`,</if>
            <if test="platformVerifyType != null">`PLATFORM_VERIFY_TYPE`,</if>
            <if test="serverDomainStatus != null">`SERVER_DOMAIN_STATUS`,</if>
            <if test="businessDomainStatus != null">`BUSINESS_DOMAIN_STATUS`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="statusUpdateTime != null">`STATUS_UPDATE_TIME`,</if>
            <if test="onlineVersionTime != null">`ONLINE_VERSION_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="openid != null">#{openid,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="minaHeadImg != null">#{minaHeadImg,jdbcType=VARCHAR},</if>
            <if test="preAuthCode != null">#{preAuthCode,jdbcType=VARCHAR},</if>
            <if test="auditVersion != null">#{auditVersion,jdbcType=VARCHAR},</if>
            <if test="deployRemark != null">#{deployRemark,jdbcType=VARCHAR},</if>
            <if test="minaNickName != null">#{minaNickName,jdbcType=VARCHAR},</if>
            <if test="minaUserName != null">#{minaUserName,jdbcType=VARCHAR},</if>
            <if test="platformType != null">#{platformType,jdbcType=VARCHAR},</if>
            <if test="auditDeployId != null">#{auditDeployId,jdbcType=VARCHAR},</if>
            <if test="minaIntroduce != null">#{minaIntroduce,jdbcType=VARCHAR},</if>
            <if test="onlineVersion != null">#{onlineVersion,jdbcType=VARCHAR},</if>
            <if test="principalName != null">#{principalName,jdbcType=VARCHAR},</if>
            <if test="onlineDeployId != null">#{onlineDeployId,jdbcType=VARCHAR},</if>
            <if test="authorizerRefreshToken != null">#{authorizerRefreshToken,jdbcType=VARCHAR},</if>
            <if test="step != null">#{step,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="authType != null">#{authType,jdbcType=TINYINT},</if>
            <if test="hasDeploy != null">#{hasDeploy,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="syncStatus != null">#{syncStatus,jdbcType=TINYINT},</if>
            <if test="accountType != null">#{accountType,jdbcType=TINYINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="qrcodeStatus != null">#{qrcodeStatus,jdbcType=TINYINT},</if>
            <if test="updateAuditStatus != null">#{updateAuditStatus,jdbcType=TINYINT},</if>
            <if test="platformVerifyType != null">#{platformVerifyType,jdbcType=TINYINT},</if>
            <if test="serverDomainStatus != null">#{serverDomainStatus,jdbcType=TINYINT},</if>
            <if test="businessDomainStatus != null">#{businessDomainStatus,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="statusUpdateTime != null">#{statusUpdateTime,jdbcType=TIMESTAMP},</if>
            <if test="onlineVersionTime != null">#{onlineVersionTime,jdbcType=TIMESTAMP},</if>
        </trim>
        </insert>

        <!--查询小程序信息-->
        <select id="getMinaAuthInfo" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM `tp_mina_auth`
            WHERE `merchant_id` = #{merchantId,jdbcType=INTEGER}
            <if test="platformType != null">
                and `platform_type` = #{platformType,jdbcType=VARCHAR}
            </if>
            <if test="hasDeploy != null">
                and `has_deploy` = #{hasDeploy,jdbcType=TINYINT}
            </if>
            and `del_flag` = 0
            order by `id` desc
            limit 1
        </select>
    </mapper>
