<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MybankRightControlDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MybankRightControlDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="MYBANK_ISV_ORG_ID" property="mybankIsvOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MYBANK_ACCOUNT_NO" property="mybankAccountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_TYPE" property="userType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPLICATION_ENV" property="applicationEnv" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_AUTO_BATCH" property="mybankAutoBatch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_FUNDS_UNFREEZE" property="mybankFundsUnfreeze" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_AUTO_REPLENISHMENT" property="mybankAutoReplenishment" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MYBANK_BALANCE_PERMISSION" property="mybankBalancePermission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`MYBANK_ISV_ORG_ID`,`MYBANK_ACCOUNT_NO`,`UID`,`IS_DEL`,`USER_ID`,`USER_TYPE`,`APPLICATION_ENV`,`MYBANK_AUTO_BATCH`,`MYBANK_FUNDS_UNFREEZE`,`MYBANK_AUTO_REPLENISHMENT`,`MYBANK_BALANCE_PERMISSION`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MYBANK_RIGHT_CONTROL-->
            <insert id="insert" >
                    INSERT INTO TP_MYBANK_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mybankIsvOrgId != null">`MYBANK_ISV_ORG_ID`,</if>
            <if test="mybankAccountNo != null">`MYBANK_ACCOUNT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="mybankAutoBatch != null">`MYBANK_AUTO_BATCH`,</if>
            <if test="mybankFundsUnfreeze != null">`MYBANK_FUNDS_UNFREEZE`,</if>
            <if test="mybankAutoReplenishment != null">`MYBANK_AUTO_REPLENISHMENT`,</if>
            <if test="mybankBalancePermission != null">`MYBANK_BALANCE_PERMISSION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="mybankIsvOrgId != null">#{mybankIsvOrgId,jdbcType=VARCHAR},</if>
            <if test="mybankAccountNo != null">#{mybankAccountNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="mybankAutoBatch != null">#{mybankAutoBatch,jdbcType=TINYINT},</if>
            <if test="mybankFundsUnfreeze != null">#{mybankFundsUnfreeze,jdbcType=TINYINT},</if>
            <if test="mybankAutoReplenishment != null">#{mybankAutoReplenishment,jdbcType=TINYINT},</if>
            <if test="mybankBalancePermission != null">#{mybankBalancePermission,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据代理商id查询代理商网商权限记录-->
            <select id="getByAgentId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MYBANK-RIGHT-CONTROL-GETBYAGENTID*/  <include refid="Base_Column_List" /> FROM `tp_mybank_right_control` where `user_type` = 2 and `user_id` = #{userId,jdbcType=INTEGER} limit 1
            </select>
    </mapper>
