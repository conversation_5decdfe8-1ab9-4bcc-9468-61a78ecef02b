<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyChannelDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyChannelDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_NUM" property="channelNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JSAPI_PATH1" property="jsapiPath1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JSAPI_PATH2" property="jsapiPath2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JSAPI_PATH3" property="jsapiPath3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JSAPI_PATH4" property="jsapiPath4" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JSAPI_PATH5" property="jsapiPath5" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRIVATE_KEY" property="privateKey" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUBJECT_BODY" property="subjectBody" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_API_V2_KEY" property="wechatApiV2Key" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_API_V3_KEY" property="wechatApiV3Key" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STATUS" property="status" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PAY_TYPE" property="payType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="APPLET_INCOME" property="appletIncome" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`SERIAL_NO`,`CHANNEL_NUM`,`JSAPI_PATH1`,`JSAPI_PATH2`,`JSAPI_PATH3`,`JSAPI_PATH4`,`JSAPI_PATH5`,`PRIVATE_KEY`,`SUBJECT_BODY`,`WECHAT_API_V2_KEY`,`WECHAT_API_V3_KEY`,`STATUS`,`PAY_TYPE`,`CHANNEL_ID`,`APPLET_INCOME`,`LIQUIDATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_CHANNEL-->
            <insert id="insert" >
            INSERT INTO TP_CHANNEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="serialNo != null">`SERIAL_NO`,</if>
        <if test="channelNum != null">`CHANNEL_NUM`,</if>
        <if test="jsapiPath1 != null">`JSAPI_PATH1`,</if>
        <if test="jsapiPath2 != null">`JSAPI_PATH2`,</if>
        <if test="jsapiPath3 != null">`JSAPI_PATH3`,</if>
        <if test="jsapiPath4 != null">`JSAPI_PATH4`,</if>
        <if test="jsapiPath5 != null">`JSAPI_PATH5`,</if>
        <if test="privateKey != null">`PRIVATE_KEY`,</if>
        <if test="subjectBody != null">`SUBJECT_BODY`,</if>
        <if test="wechatApiV2Key != null">`WECHAT_API_V2_KEY`,</if>
        <if test="wechatApiV3Key != null">`WECHAT_API_V3_KEY`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="appletIncome != null">`APPLET_INCOME`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
        <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
        <if test="jsapiPath1 != null">#{jsapiPath1,jdbcType=VARCHAR},</if>
        <if test="jsapiPath2 != null">#{jsapiPath2,jdbcType=VARCHAR},</if>
        <if test="jsapiPath3 != null">#{jsapiPath3,jdbcType=VARCHAR},</if>
        <if test="jsapiPath4 != null">#{jsapiPath4,jdbcType=VARCHAR},</if>
        <if test="jsapiPath5 != null">#{jsapiPath5,jdbcType=VARCHAR},</if>
        <if test="privateKey != null">#{privateKey,jdbcType=VARCHAR},</if>
        <if test="subjectBody != null">#{subjectBody,jdbcType=VARCHAR},</if>
        <if test="wechatApiV2Key != null">#{wechatApiV2Key,jdbcType=VARCHAR},</if>
        <if test="wechatApiV3Key != null">#{wechatApiV3Key,jdbcType=VARCHAR},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="appletIncome != null">#{appletIncome,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据渠道号ID查询-->
            <select id="getByChannelId" resultMap="BaseResultMap">
                    select /*MS-TP-CHANNEL-GETBYCHANNELID*/ <include refid="Base_Column_List" /> from TP_CHANNEL
        where channel_id = #{channelId,jdbcType=INTEGER}
            </select>
    </mapper>
