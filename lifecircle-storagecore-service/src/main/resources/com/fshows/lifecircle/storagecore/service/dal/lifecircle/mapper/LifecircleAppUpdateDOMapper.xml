<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleAppUpdateDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleAppUpdateDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="APK_URL" property="apkUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APP_NAME" property="appName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTENT" property="content" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OEM_CODE" property="oemCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VERSION_NAME" property="versionName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PUSH" property="isPush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PUSH_COUNT" property="pushCount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GREY_STATUS" property="greyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_GREY_FLAG" property="isGreyFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VERSION_CODE" property="versionCode" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="VERSION_TYPE" property="versionType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WINDOWS_TYPE" property="windowsType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_FORCE_UPDATE" property="isForceUpdate" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_FORCE_UPDATE_NEW" property="isForceUpdateNew" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="RELEASE_TIME" property="releaseTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`APK_URL`,`APP_NAME`,`CONTENT`,`OEM_CODE`,`VERSION_NAME`,`TYPE`,`IS_PUSH`,`PLATFORM`,`PUSH_COUNT`,`GREY_STATUS`,`IS_GREY_FLAG`,`VERSION_CODE`,`VERSION_TYPE`,`WINDOWS_TYPE`,`IS_FORCE_UPDATE`,`IS_FORCE_UPDATE_NEW`,`CREATE_TIME`,`UPDATE_TIME`,`RELEASE_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_APP_UPDATE-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_APP_UPDATE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="apkUrl != null">`APK_URL`,</if>
            <if test="appName != null">`APP_NAME`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="oemCode != null">`OEM_CODE`,</if>
            <if test="versionName != null">`VERSION_NAME`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="pushCount != null">`PUSH_COUNT`,</if>
            <if test="greyStatus != null">`GREY_STATUS`,</if>
            <if test="isGreyFlag != null">`IS_GREY_FLAG`,</if>
            <if test="versionCode != null">`VERSION_CODE`,</if>
            <if test="versionType != null">`VERSION_TYPE`,</if>
            <if test="windowsType != null">`WINDOWS_TYPE`,</if>
            <if test="isForceUpdate != null">`IS_FORCE_UPDATE`,</if>
            <if test="isForceUpdateNew != null">`IS_FORCE_UPDATE_NEW`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="apkUrl != null">#{apkUrl,jdbcType=VARCHAR},</if>
            <if test="appName != null">#{appName,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="oemCode != null">#{oemCode,jdbcType=VARCHAR},</if>
            <if test="versionName != null">#{versionName,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="pushCount != null">#{pushCount,jdbcType=TINYINT},</if>
            <if test="greyStatus != null">#{greyStatus,jdbcType=TINYINT},</if>
            <if test="isGreyFlag != null">#{isGreyFlag,jdbcType=TINYINT},</if>
            <if test="versionCode != null">#{versionCode,jdbcType=INTEGER},</if>
            <if test="versionType != null">#{versionType,jdbcType=TINYINT},</if>
            <if test="windowsType != null">#{windowsType,jdbcType=TINYINT},</if>
            <if test="isForceUpdate != null">#{isForceUpdate,jdbcType=TINYINT},</if>
            <if test="isForceUpdateNew != null">#{isForceUpdateNew,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据 ID 获得版本信息-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-APP-UPDATE-GETBYID*/  <include refid="Base_Column_List" />
        FROM
        TP_LIFECIRCLE_APP_UPDATE
        WHERE
        id = #{id,jdbcType=INTEGER}
        LIMIT 1
            </select>
    </mapper>
