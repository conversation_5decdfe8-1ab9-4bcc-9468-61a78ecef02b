<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.MchShareAccountDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.MchShareAccountDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCHNT_CD" property="mchntCd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NO" property="accountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_NAME" property="agentName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCHNT_NAME" property="mchntName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ISS_INS_NAME" property="issInsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_INTER_NO" property="bankInterNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ISS_CITY_NAME" property="issCityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_NAME" property="salesmanName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_MCHNT_CD" property="tradeMchntCd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_BRANCH_NAME" property="subBranchName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACCOUNT_NAME" property="bankAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACCOUNT_MCHNT_CD" property="bankAccountMchntCd" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ROLE" property="role" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ROLE_USER_ID" property="roleUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_CHANNEL" property="bankChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACCOUNT_ACTIVE_TIME" property="accountActiveTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`MOBILE`,`COMPANY`,`MCHNT_CD`,`ACCOUNT_ID`,`ACCOUNT_NO`,`AGENT_NAME`,`MCHNT_NAME`,`ISS_INS_NAME`,`BANK_INTER_NO`,`ISS_CITY_NAME`,`SALESMAN_NAME`,`TRADE_MCHNT_CD`,`SUB_BRANCH_NAME`,`BANK_ACCOUNT_NAME`,`BANK_ACCOUNT_MCHNT_CD`,`UID`,`ROLE`,`IS_DEL`,`AGENT_ID`,`BANK_TYPE`,`ROLE_USER_ID`,`SALESMAN_ID`,`BANK_CHANNEL`,`ACCOUNT_ACTIVE_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--根据查询条件查询商户信息-->
            <select id="queryListByParam" resultType="com.fshows.lifecircle.storagecore.service.domain.model.mchshare.MchShareAccountListItemModel">
                    SELECT
        sa.uid as uid,
        sa.agent_id as agentId,
        sa.agent_name as agentName,
        sa.mchnt_name as userName,
        sa.mchnt_cd as mchntCd,
        sa.company as company,
        cr.sign_status as signStatus,
        cr.update_time as signUpdateTimeDate,
        ai.income_status as incomeStatus,
        ai.update_time as incomeUpdateTimeDate,
        sa.mobile as mobile,
        sa.account_no as accountNo,
        sa.salesman_id as salesmanId,
        sa.salesman_name as salesmanName,
        cr.sign_rate as decimalRate,
        sa.create_time as createTimeDate,
        sa.update_time as updateTimeDate,
        sa.account_active_time as accountActiveTime
        FROM
        `tp_mch_share_account` sa
        LEFT JOIN `tp_mch_share_concentrate_relation` cr on sa.uid = cr.uid
        LEFT JOIN `tp_mch_users_account_info` ai on sa.uid = ai.uid
        WHERE sa.is_del = 0 and sa.role = #{role,jdbcType=TINYINT}
        <if test="agentId != null ">
            AND sa.agent_id  =  #{agentId,jdbcType=INTEGER}
        </if>
        <if test="salesmanId != null ">
            AND sa.salesman_id  =  #{salesmanId,jdbcType=INTEGER}
        </if>
        <if test="uid != null ">
            AND sa.uid =  #{uid,jdbcType=INTEGER}
        </if>
        <if test="mchntCd != null ">
            AND sa.mchnt_cd =  #{mchntCd,jdbcType=INTEGER}
        </if>
        <if test="mchntName != null ">
            AND sa.mchnt_name  LIKE concat('%',#{mchntName, jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null ">
            AND sa.company  LIKE concat('%',#{company, jdbcType=VARCHAR},'%')
        </if>
        <if test="agentName != null ">
            AND sa.agent_name  LIKE concat('%',#{agentName, jdbcType=VARCHAR},'%')
        </if>
        <if test="signStatus != null and signStatus != -1">
            <choose>
                <when test="signStatus == 1">
                    AND cr.sign_status is null
                </when>
                <otherwise>
                    AND cr.sign_status = #{signStatus,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="incomeStatus != null and incomeStatus != -1">
            <choose>
                <when test="incomeStatus == 5">
                    AND (ai.income_status is null or ai.income_status = #{incomeStatus,jdbcType=INTEGER})
                </when>
                <otherwise>
                    AND ai.income_status = #{incomeStatus,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="beginTime != null and endTime !=null">
            AND sa.create_time between #{beginTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY sa.create_time desc
            </select>
    </mapper>
