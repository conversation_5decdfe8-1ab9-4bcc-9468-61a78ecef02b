<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmUserRoleDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmUserRoleDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="ROLE_ID" property="roleId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="USER_ID" property="userId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_BY" property="createBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UPDATE_BY" property="updateBy" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ROLE_ID`,`USER_ID`,`CREATE_BY`,`UPDATE_BY`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:LM_CRM_USER_ROLE-->
    <insert id="insert">
        INSERT INTO LM_CRM_USER_ROLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="roleId != null">`ROLE_ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="roleId != null">#{roleId,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--根据角色id用户id查询角色-->
    <select id="findByRoleIdAndUserId" resultMap="BaseResultMap">
        SELECT /*MS-LM-CRM-USER-ROLE-FINDBYROLEIDANDUSERID*/
        <include refid="Base_Column_List"/>
        FROM lm_crm_user_role
        WHERE user_id = #{userId, jdbcType=VARCHAR} and role_id =#{roleId,jdbcType=VARCHAR} limit 1
    </select>
</mapper>
