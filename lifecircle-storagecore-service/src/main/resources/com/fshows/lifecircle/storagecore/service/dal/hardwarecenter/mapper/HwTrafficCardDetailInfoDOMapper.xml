<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwTrafficCardDetailInfoDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwTrafficCardDetailInfoDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ICCID" property="iccid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MSISDN" property="msisdn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SP_CODE" property="spCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARRIER" property="carrier" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SIM_TYPE" property="simType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIND_INIT_SN" property="bindInitSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXPIRY_DATE" property="expiryDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_STATUS" property="accountStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVE" property="active" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_UID" property="bindUid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DATA_WARN" property="dataWarn" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DELETE_WARN" property="deleteWarn" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EXPIRY_WARN" property="expiryWarn" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BIND_AGENT_ID" property="bindAgentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="DATA_PLAN" property="dataPlan" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DATA_USAGE" property="dataUsage" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="DATA_BALANCE" property="dataBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ICCID`,`MSISDN`,`REMARK`,`SP_CODE`,`CARRIER`,`SIM_TYPE`,`BIND_INIT_SN`,`EXPIRY_DATE`,`ACCOUNT_STATUS`,`ACTIVE`,`AGENT_ID`,`BIND_UID`,`DATA_WARN`,`DELETE_WARN`,`EXPIRY_WARN`,`BIND_AGENT_ID`,`CREATE_TIME`,`UPDATE_TIME`,`DATA_PLAN`,`DATA_USAGE`,`DATA_BALANCE`
    </sql>


            <!--insert:HW_TRAFFIC_CARD_DETAIL_INFO-->
            <insert id="insert" >
            INSERT INTO HW_TRAFFIC_CARD_DETAIL_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="iccid != null">`ICCID`,</if>
        <if test="msisdn != null">`MSISDN`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="spCode != null">`SP_CODE`,</if>
        <if test="carrier != null">`CARRIER`,</if>
        <if test="simType != null">`SIM_TYPE`,</if>
        <if test="bindInitSn != null">`BIND_INIT_SN`,</if>
        <if test="expiryDate != null">`EXPIRY_DATE`,</if>
        <if test="accountStatus != null">`ACCOUNT_STATUS`,</if>
        <if test="active != null">`ACTIVE`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="bindUid != null">`BIND_UID`,</if>
        <if test="bindAgentId != null">`BIND_AGENT_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="dataPlan != null">`DATA_PLAN`,</if>
        <if test="dataUsage != null">`DATA_USAGE`,</if>
        <if test="dataBalance != null">`DATA_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="iccid != null">#{iccid,jdbcType=VARCHAR},</if>
        <if test="msisdn != null">#{msisdn,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="spCode != null">#{spCode,jdbcType=VARCHAR},</if>
        <if test="carrier != null">#{carrier,jdbcType=VARCHAR},</if>
        <if test="simType != null">#{simType,jdbcType=VARCHAR},</if>
        <if test="bindInitSn != null">#{bindInitSn,jdbcType=VARCHAR},</if>
        <if test="expiryDate != null">#{expiryDate,jdbcType=VARCHAR},</if>
        <if test="accountStatus != null">#{accountStatus,jdbcType=VARCHAR},</if>
        <if test="active != null">#{active,jdbcType=TINYINT},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="bindUid != null">#{bindUid,jdbcType=INTEGER},</if>
        <if test="bindAgentId != null">#{bindAgentId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="dataPlan != null">#{dataPlan,jdbcType=DECIMAL},</if>
        <if test="dataUsage != null">#{dataUsage,jdbcType=DECIMAL},</if>
        <if test="dataBalance != null">#{dataBalance,jdbcType=DECIMAL},</if>
    </trim>
            </insert>

            <!--查询代理商流量卡列表 pageCount-->
            <select id="findTrafficCardListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 HW_TRAFFIC_CARD_DETAIL_INFO
        <where>
            AGENT_ID = #{agentId,jdbcType=INTEGER}
            <if test="msisdn != '' or initSn != '' or uid != null">
                AND (MSISDN like concat('%',#{msisdn,jdbcType=VARCHAR},'%')
                OR BIND_INIT_SN like concat('%',#{initSn,jdbcType=VARCHAR},'%')
                <if test="uid != null">
                    OR BIND_UID like concat('%',#{uid,jdbcType=INTEGER},'%')
                </if>
                )
            </if>
            <if test="msisdn == '' and initSn == '' and uid == null">
                <if test="accountStatusList != null and accountStatusList.size &gt; 0">
                    AND ACCOUNT_STATUS in
                    <foreach collection="accountStatusList" item="accountStatus" open="(" separator="," close=")">
                        #{accountStatus,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="active != null">
                    AND ACTIVE = #{active,jdbcType=TINYINT}
                </if>
                <if test="dataWarn != null">
                    AND DATA_WARN = #{dataWarn,jdbcType=TINYINT}
                </if>
                <if test="deleteWarn != null">
                    AND DELETE_WARN = #{deleteWarn,jdbcType=TINYINT}
                </if>
                <if test="expiryWarn != null">
                    AND EXPIRY_WARN = #{expiryWarn,jdbcType=TINYINT}
                </if>
            </if>
        </where>
            </select>
            <!--查询代理商流量卡列表 pageResult-->
            <select id="findTrafficCardListResult"  resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM HW_TRAFFIC_CARD_DETAIL_INFO
        <where>
            AGENT_ID = #{agentId,jdbcType=INTEGER}
            <if test="msisdn != '' or initSn != '' or uid != null">
                AND (MSISDN like concat('%',#{msisdn,jdbcType=VARCHAR},'%')
                OR BIND_INIT_SN like concat('%',#{initSn,jdbcType=VARCHAR},'%')
                <if test="uid != null">
                    OR BIND_UID like concat('%',#{uid,jdbcType=INTEGER},'%')
                </if>
                )
            </if>
            <if test="msisdn == '' and initSn == '' and uid == null">
                <if test="accountStatusList != null and accountStatusList.size &gt; 0">
                    AND ACCOUNT_STATUS in
                    <foreach collection="accountStatusList" item="accountStatus" open="(" separator="," close=")">
                        #{accountStatus,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="active != null">
                    AND ACTIVE = #{active,jdbcType=TINYINT}
                </if>
                <if test="dataWarn != null">
                    AND DATA_WARN = #{dataWarn,jdbcType=TINYINT}
                </if>
                <if test="deleteWarn != null">
                    AND DELETE_WARN = #{deleteWarn,jdbcType=TINYINT}
                </if>
                <if test="expiryWarn != null">
                    AND EXPIRY_WARN = #{expiryWarn,jdbcType=TINYINT}
                </if>
            </if>
        </where>
            limit #{startRow},#{limit}
            </select>
    </mapper>
