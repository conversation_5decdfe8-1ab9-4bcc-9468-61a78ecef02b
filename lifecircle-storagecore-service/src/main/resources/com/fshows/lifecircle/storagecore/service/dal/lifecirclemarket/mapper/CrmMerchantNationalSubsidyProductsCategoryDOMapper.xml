<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.CrmMerchantNationalSubsidyProductsCategoryDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.CrmMerchantNationalSubsidyProductsCategoryDO">
        <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

        <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CATEGORY_CODE" property="categoryCode" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="CATEGORY_SORT" property="categorySort" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`,`CITY_CODE`,`CATEGORY_CODE`,`CATEGORY_NAME`,`IS_DEL`,`CATEGORY_SORT`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY-->
    <insert id="insert">
        INSERT INTO TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="categoryCode != null">`CATEGORY_CODE`,</if>
            <if test="categoryName != null">`CATEGORY_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="categorySort != null">`CATEGORY_SORT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="categoryCode != null">#{categoryCode,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="categorySort != null">#{categorySort,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--findAll-->
    <select id="findAll" resultMap="BaseResultMap">
        SELECT /*MS-TP-MERCHANT-NATIONAL-SUBSIDY-PRODUCTS-CATEGORY-FINDALL*/
        <include refid="Base_Column_List"/>
        from TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY
        WHERE IS_DEL=0
    </select>
</mapper>
