<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentActivitySnRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentActivitySnRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BUYER_ID" property="buyerId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BUYER_TYPE" property="buyerType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_ID" property="activityId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DAY_STANDARD" property="isDayStandard" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`IS_DEL`,`BUYER_ID`,`BUYER_TYPE`,`ACTIVITY_ID`,`EQUIPMENT_ID`,`IS_DAY_STANDARD`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_EQUIPMENT_ACTIVITY_SN_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_EQUIPMENT_ACTIVITY_SN_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="isDayStandard != null">`IS_DAY_STANDARD`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="buyerId != null">`BUYER_ID`,</if>
            <if test="buyerType != null">`BUYER_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="isDayStandard != null">#{isDayStandard,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="buyerId != null">#{buyerId,jdbcType=INTEGER},</if>
            <if test="buyerType != null">#{buyerType,jdbcType=INTEGER},</if>
        </trim>
            </insert>

            <!--根据设备SN批量查询活动设备参与活动信息 pageCount-->
            <select id="findEquipmentListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
        hw_equipment_activity_sn_relation relation
        LEFT JOIN hw_equipment_activity_sn_first_trade trade ON relation.init_sn = trade.init_sn
        LEFT JOIN hw_equipment equipment ON relation.equipment_id = equipment.id
        LEFT JOIN hw_equipment_activity activity ON relation.activity_id = activity.id
        WHERE
        relation.create_time &gt; #{startJoinTime,jdbcType=TIMESTAMP}
        AND relation.create_time &lt; #{endJoinTime,jdbcType=TIMESTAMP}
        <if test="initSn != null and initSn != '' ">
            AND relation.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="activityId != null">
            AND relation.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            AND relation.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="startTradeTime != null">
            AND trade.first_trade_time &gt; #{startTradeTime,jdbcType=INTEGER}
        </if>
        <if test="endTradeTime != null">
            AND trade.first_trade_time &lt; #{endTradeTime,jdbcType=INTEGER}
        </if>
        AND relation.is_del = 0
        
            </select>
            <!--根据设备SN批量查询活动设备参与活动信息 pageResult-->
            <select id="findEquipmentListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.HarvestPlanEquipmentDTO">
                    SELECT
        relation.init_sn as equipmentSn,
        equipment.equipment_model as equipmentModel,
        activity.activity_name as activityName,
        relation.create_time as createTime,
        trade.first_trade_time as firstTradeTime
        FROM
        hw_equipment_activity_sn_relation relation
        LEFT JOIN hw_equipment_activity_sn_first_trade trade ON relation.init_sn = trade.init_sn
        LEFT JOIN hw_equipment equipment ON relation.equipment_id = equipment.id
        LEFT JOIN hw_equipment_activity activity ON relation.activity_id = activity.id
        WHERE
        relation.create_time &gt; #{startJoinTime,jdbcType=TIMESTAMP}
        AND relation.create_time &lt; #{endJoinTime,jdbcType=TIMESTAMP}
        <if test="initSn != null and initSn != '' ">
            AND relation.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="activityId != null">
            AND relation.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            AND relation.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="startTradeTime != null">
            AND trade.first_trade_time &gt; #{startTradeTime,jdbcType=INTEGER}
        </if>
        <if test="endTradeTime != null">
            AND trade.first_trade_time &lt; #{endTradeTime,jdbcType=INTEGER}
        </if>
        AND relation.is_del = 0
        order by relation.`id` desc
            limit #{startRow},#{limit}
            </select>

            <!--批量查询参与活动的设备SN-->
            <select id="findInitSnListBySnList" resultType="java.lang.String">
                    SELECT
        INIT_SN
        FROM
        HW_EQUIPMENT_ACTIVITY_SN_RELATION
        WHERE INIT_SN IN
        <foreach collection="list" index="index" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND IS_DEL = 0
            </select>

            <!--批量插入设备活动关联信息-->
            <insert id="insertBatch" >
                    INSERT INTO
        HW_EQUIPMENT_ACTIVITY_SN_RELATION
        (`INIT_SN`,`ACTIVITY_ID`,`EQUIPMENT_ID`,`BUYER_ID`,`BUYER_TYPE`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.initSn,jdbcType=VARCHAR},
            #{item.activityId,jdbcType=INTEGER},
            #{item.equipmentId,jdbcType=INTEGER},
            #{item.buyerId,jdbcType=INTEGER},
            #{item.buyerType,jdbcType=INTEGER}
            )
        </foreach>
            </insert>
    </mapper>
