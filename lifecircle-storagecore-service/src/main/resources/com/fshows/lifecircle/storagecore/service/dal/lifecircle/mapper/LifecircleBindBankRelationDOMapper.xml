<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleBindBankRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleBindBankRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN_NO" property="tokenNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIND_BANK_ID" property="bindBankId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN_NO`,`BIND_BANK_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_BIND_BANK_RELATION-->
            <insert id="insert" >
                    INSERT INTO TP_LIFECIRCLE_BIND_BANK_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="bindBankId != null">`BIND_BANK_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据tokenNo批量查出bindBankRelation记录-->
            <select id="getBankRelationByTokenNo" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        tp_lifecircle_bind_bank_relation
        where
        token_no in
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
