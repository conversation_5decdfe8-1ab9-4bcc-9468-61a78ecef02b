<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MemberBindRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MemberBindRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="EXT0" property="ext0" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT1" property="ext1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="EXT2" property="ext2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="CHAR"
        javaType="String"/>

            <result column="USER_IDS" property="userIds" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_TYPE" property="cardType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WX_USER_ID" property="wxUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PHONE_USER_ID" property="phoneUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WXPAY_USER_ID" property="wxpayUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ALIPAY_USER_ID" property="alipayUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ENTITY_USER_ID" property="entityUserId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`EXT0`,`EXT1`,`EXT2`,`PHONE`,`USER_IDS`,`UID`,`CARD_TYPE`,`WX_USER_ID`,`PHONE_USER_ID`,`WXPAY_USER_ID`,`ALIPAY_USER_ID`,`ENTITY_USER_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MEMBER_BIND_RELATION-->
            <insert id="insert" >
            INSERT INTO TP_MEMBER_BIND_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext0 != null">`EXT0`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="phone != null">`PHONE`,</if>
        <if test="userIds != null">`USER_IDS`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="wxUserId != null">`WX_USER_ID`,</if>
        <if test="alipayUserId != null">`ALIPAY_USER_ID`,</if>
        <if test="entityUserId != null">`ENTITY_USER_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="ext0 != null">#{ext0,jdbcType=VARCHAR},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="phone != null">#{phone,jdbcType=CHAR},</if>
        <if test="userIds != null">#{userIds,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="wxUserId != null">#{wxUserId,jdbcType=INTEGER},</if>
        <if test="alipayUserId != null">#{alipayUserId,jdbcType=INTEGER},</if>
        <if test="entityUserId != null">#{entityUserId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据手机号码获取用户关联信息-->
            <select id="selectByPhone" resultMap="BaseResultMap">
                    select /*MS-TP-MEMBER-BIND-RELATION-SELECTBYPHONE*/ <include refid="Base_Column_List" /> from TP_MEMBER_BIND_RELATION where phone = #{phone,jdbcType=VARCHAR} and card_type = 1 limit 1
            </select>
    </mapper>
