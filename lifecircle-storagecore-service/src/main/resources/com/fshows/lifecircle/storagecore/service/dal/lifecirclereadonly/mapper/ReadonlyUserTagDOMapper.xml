<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyUserTagDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyUserTagDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TAG" property="tag" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SAIL_TAG" property="sailTag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OLD_SAIL_TAG" property="oldSailTag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMPANY_TYPE" property="companyType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="POLICY_END_TIME" property="policyEndTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TAG`,`AGENT_ID`,`SAIL_TAG`,`START_TIME`,`OLD_SAIL_TAG`,`COMPANY_TYPE`,`POLICY_END_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_USER_TAG-->
            <insert id="insert" >
                    INSERT INTO TP_USER_TAG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="tag != null">`TAG`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="sailTag != null">`SAIL_TAG`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="oldSailTag != null">`OLD_SAIL_TAG`,</if>
            <if test="policyEndTime != null">`POLICY_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="tag != null">#{tag,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="sailTag != null">#{sailTag,jdbcType=TINYINT},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="oldSailTag != null">#{oldSailTag,jdbcType=TINYINT},</if>
            <if test="policyEndTime != null">#{policyEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据代理商id获取代理商标签-->
            <select id="getByAgentId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USER-TAG-GETBYAGENTID*/  <include refid="Base_Column_List" />
        FROM TP_USER_TAG
        WHERE AGENT_ID = #{agentId,jdbcType=INTEGER}
            </select>
    </mapper>
