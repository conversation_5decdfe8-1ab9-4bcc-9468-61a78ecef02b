<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.ChannelAgentExtDOMapper">

    <resultMap id="ResultListPage"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.ChannelAgentListResultMap">
        <result column="id" property="agentId" javaType="Integer"/>
        <result column="username" property="username" javaType="String"/>
        <result column="companyname" property="companyname" javaType="String"/>
        <result column="level" property="level" javaType="String"/>
        <result column="province" property="province" javaType="String"/>
        <result column="city" property="city" javaType="String"/>
        <result column="business_province" property="businessProvince" javaType="String"/>
        <result column="business_city" property="businessCity" javaType="String"/>
        <result column="business_area" property="businessArea" javaType="String"/>
        <result column="createtime" property="createTime" jdbcType="INTEGER" javaType="Integer"/>
        <result column="viptime" property="vipTime" jdbcType="INTEGER" javaType="Integer"/>
        <result column="status" property="valid" jdbcType="INTEGER" javaType="Integer"/>
        <result column="visit_time" property="visitTime" jdbcType="TIMESTAMP" javaType="java.util.Date"/>
    </resultMap>

    <!-- 代理商分页列表（后台列表导出，非必填条件比较多，需根据查询条件选择是否多关联某张表) -->
    <select id="listByCondition"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ChannelAgentQueryExtDO"
            resultMap="ResultListPage">
        SELECT
        a.id,
        a.username,
        a.companyname,
        a.`level`,
        a.province,
        a.city,
        a.business_province,
        a.business_city,
        a.business_area,
        a.createtime,
        a.viptime,
        a.`status`,
        c.visit_time
        FROM tp_user a
        LEFT JOIN dp_agent_extend b ON a.id = b.agent_id
        <if test=" operationTypeFirst != null and operationTypeFirst != -1 ">
            INNER JOIN dp_agent_operation d ON a.id = d.agent_id
            AND d.valid = 1
            AND d.operation_type=#{operationTypeFirst,jdbcType=TINYINT}
            <if test=" unionIdFirst != null and unionIdFirst != '' and unionIdFirst != '-1' ">
                AND d.union_id = #{unionIdFirst,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="oenTypeTagIdlList != null and oenTypeTagIdlList.size()>0">
            INNER JOIN
            (
            SELECT agent_id FROM dp_tag_agent
            WHERE valid = 1
            AND tag_id IN
            <foreach item="item" index="index" collection="oenTypeTagIdlList" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            GROUP By agent_id
            ) e
            ON a.id = e.agent_id
        </if>
        <if test="manyTypeTagIdlList != null and manyTypeTagIdlList.size()>0">
            INNER JOIN
            (
            SELECT
            f.agent_id
            FROM
            (
            SELECT
            tag.agent_id
            FROM
            dp_tag_agent tag
            WHERE
            tag.valid = 1
            AND tag.tag_id IN
            <foreach item="item" index="index" collection="manyTypeTagIdlList" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            GROUP BY tag.agent_id,tag.tag_parent_id
            ) f
            GROUP BY f.agent_id
            HAVING COUNT(1) = #{tagTypeSize,jdbcType=INTEGER}
            ) g on a.id = g.agent_id
        </if>
        LEFT JOIN dp_agent_visit c ON a.id = c.agent_id AND c.valid = 1 AND c.is_last = 1
        WHERE
        ( b.valid IS NULL OR b.valid=1 )
        AND a.belong = 0
        AND a.own_run = 0
        AND a.sub_config_id = 0
        <if test=" vipStartTime != null ">
            AND a.viptime <![CDATA[ > ]]> #{vipStartTime,jdbcType=BIGINT}
        </if>
        <if test=" vipEndTime != null">
            AND a.viptime <![CDATA[ <= ]]> #{vipEndTime,jdbcType=BIGINT}
        </if>
        <if test=" agentId != null ">
            AND a.id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test=" username != null and username != '' ">
            AND a.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test=" agentContact != null and agentContact != '' ">
            AND a.contacts LIKE CONCAT(#{agentContact,jdbcType=VARCHAR},'%')
        </if>
        <if test=" companyname != null and companyname != '' ">
            AND a.companyname LIKE CONCAT(#{companyname,jdbcType=VARCHAR},'%')
        </if>
        <if test=" level != null and level != '' ">
            AND a.level = #{level,jdbcType=VARCHAR}
        </if>
        <if test=" province != null and province != '' ">
            AND a.province = #{province,jdbcType=VARCHAR}
        </if>
        <if test=" city != null and city != '' ">
            AND a.city = #{city,jdbcType=VARCHAR}
        </if>
        <if test=" businessProvince != null and businessProvince != '' ">
            AND a.business_province = #{businessProvince,jdbcType=VARCHAR}
        </if>
        <if test=" businessCity != null and businessCity != '' ">
            AND a.business_city = #{businessCity,jdbcType=VARCHAR}
        </if>
        <if test=" businessArea != null and businessArea != '' ">
            AND a.business_area = #{businessArea,jdbcType=VARCHAR}
        </if>
        <if test=" status != null and status != -1 ">
            AND a.status = #{status,jdbcType=TINYINT}
        </if>
        <if test=" createStartTime != null and createStartTime != 0 ">
            AND a.createtime <![CDATA[ >= ]]> #{createStartTime,jdbcType=BIGINT}
        </if>
        <if test=" createEndTime != null and createEndTime != 0 ">
            AND a.createtime <![CDATA[ < ]]> #{createEndTime,jdbcType=BIGINT}
        </if>
        <if test=" operationTypeSecond != null and operationTypeSecond != -1 ">
            AND a.id IN
            (
            SELECT
            h.agent_id
            FROM
            dp_agent_operation h
            WHERE
            h.valid = 1
            AND h.operation_type = #{operationTypeSecond,jdbcType=TINYINT}
            <if test=" unionIdSecond != null and unionIdSecond != '' and unionIdSecond != '-1' ">
                AND h.union_id = #{unionIdSecond,jdbcType=VARCHAR}
            </if>
            )
        </if>
        <if test=" operationUnMatchType != null and operationUnMatchType != -1 ">
            AND a.id NOT IN
            (
            SELECT
            j.agent_id
            FROM
            dp_agent_operation j
            WHERE
            j.valid = 1
            AND j.operation_type = #{operationUnMatchType,jdbcType=TINYINT}
            )
        </if>
        <if test=" sortBy != null and sortBy != '' ">
            ORDER BY ${sortBy}
        </if>
        LIMIT #{startRow,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </select>

    <select id="countByCondition"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.ChannelAgentQueryExtDO"
            resultType="java.lang.Integer">
        SELECT
        count(*)
        FROM tp_user a
        LEFT JOIN dp_agent_extend b ON a.id = b.agent_id
        <if test=" operationTypeFirst != null and operationTypeFirst != -1 ">
            INNER JOIN dp_agent_operation d ON a.id = d.agent_id
            AND d.valid = 1
            AND d.operation_type=#{operationTypeFirst,jdbcType=TINYINT}
            <if test=" unionIdFirst != null and unionIdFirst != '' and unionIdFirst != '-1' ">
                AND d.union_id = #{unionIdFirst,jdbcType=VARCHAR}
            </if>
        </if>
        <if test="oenTypeTagIdlList != null and oenTypeTagIdlList.size()>0">
            INNER JOIN
            (
            SELECT agent_id FROM dp_tag_agent
            WHERE valid = 1
            AND tag_id IN
            <foreach item="item" index="index" collection="oenTypeTagIdlList" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            GROUP By agent_id
            ) e
            ON a.id = e.agent_id
        </if>
        <if test="manyTypeTagIdlList != null and manyTypeTagIdlList.size()>0">
            INNER JOIN
            (
            SELECT
            f.agent_id
            FROM
            (
            SELECT
            tag.agent_id
            FROM
            dp_tag_agent tag
            WHERE
            tag.valid = 1
            AND tag.tag_id IN
            <foreach item="item" index="index" collection="manyTypeTagIdlList" open="(" separator="," close=")">
                #{item,jdbcType=INTEGER}
            </foreach>
            GROUP BY tag.agent_id,tag.tag_parent_id
            ) f
            GROUP BY f.agent_id
            HAVING COUNT(1) = #{tagTypeSize,jdbcType=INTEGER}
            ) g on a.id = g.agent_id
        </if>
        LEFT JOIN dp_agent_visit c ON a.id = c.agent_id AND c.valid = 1 AND c.is_last = 1
        WHERE
        ( b.valid IS NULL OR b.valid=1 )
        AND a.belong = 0
        AND a.own_run = 0
        AND a.sub_config_id = 0
        <if test=" vipStartTime != null ">
            AND a.viptime <![CDATA[ > ]]> #{vipStartTime,jdbcType=BIGINT}
        </if>
        <if test=" vipEndTime != null">
            AND a.viptime <![CDATA[ <= ]]> #{vipEndTime,jdbcType=BIGINT}
        </if>
        <if test=" agentId != null ">
            AND a.id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test=" username != null and username != '' ">
            AND a.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test=" agentContact != null and agentContact != '' ">
            AND a.contacts LIKE CONCAT(#{agentContact,jdbcType=VARCHAR},'%')
        </if>
        <if test=" companyname != null and companyname != '' ">
            AND a.companyname LIKE CONCAT(#{companyname,jdbcType=VARCHAR},'%')
        </if>
        <if test=" level != null and level != '' ">
            AND a.level = #{level,jdbcType=VARCHAR}
        </if>
        <if test=" province != null and province != '' ">
            AND a.province = #{province,jdbcType=VARCHAR}
        </if>
        <if test=" city != null and city != '' ">
            AND a.city = #{city,jdbcType=VARCHAR}
        </if>
        <if test=" businessProvince != null and businessProvince != '' ">
            AND a.business_province = #{businessProvince,jdbcType=VARCHAR}
        </if>
        <if test=" businessCity != null and businessCity != '' ">
            AND a.business_city = #{businessCity,jdbcType=VARCHAR}
        </if>
        <if test=" businessArea != null and businessArea != '' ">
            AND a.business_area = #{businessArea,jdbcType=VARCHAR}
        </if>
        <if test=" status != null and status != -1 ">
            AND a.status = #{status,jdbcType=TINYINT}
        </if>
        <if test=" createStartTime != null and createStartTime != 0 ">
            AND a.createtime <![CDATA[ >= ]]> #{createStartTime,jdbcType=BIGINT}
        </if>
        <if test=" createEndTime != null and createEndTime != 0 ">
            AND a.createtime <![CDATA[ < ]]> #{createEndTime,jdbcType=BIGINT}
        </if>
        <if test=" operationTypeSecond != null and operationTypeSecond != -1 ">
            AND a.id IN
            (
            SELECT
            h.agent_id
            FROM
            dp_agent_operation h
            WHERE
            h.valid = 1
            AND h.operation_type = #{operationTypeSecond,jdbcType=TINYINT}
            <if test=" unionIdSecond != null and unionIdSecond != '' and unionIdSecond != '-1' ">
                AND h.union_id = #{unionIdSecond,jdbcType=VARCHAR}
            </if>
            )
        </if>
        <if test=" operationUnMatchType != null and operationUnMatchType != -1 ">
            AND a.id NOT IN
            (
            SELECT
            j.agent_id
            FROM
            dp_agent_operation j
            WHERE
            j.valid = 1
            AND j.operation_type = #{operationUnMatchType,jdbcType=TINYINT}
            )
        </if>
    </select>

</mapper>
