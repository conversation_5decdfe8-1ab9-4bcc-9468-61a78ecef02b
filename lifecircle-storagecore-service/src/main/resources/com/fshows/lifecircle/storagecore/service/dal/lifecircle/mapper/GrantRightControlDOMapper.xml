<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.GrantRightControlDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.GrantRightControlDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="KEY_ID" property="keyId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTO_INCOME" property="autoIncome" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMM_SETTLE" property="commSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_STEP" property="submitStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_OFFLINE_SALE" property="nfcOfflineSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ZERO_BUY_SALE" property="nfcZeroBuySale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="T1_SETTLE_SWITCH" property="t1SettleSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MAX_MARKET_NUMBER" property="maxMarketNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NFC_SESAME_GO_SALE" property="nfcSesameGoSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QUICK_CASH_SWITCH" property="quickCashSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_SUB_ACCOUNT" property="createSubAccount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDIVIDUAL_SETTLE" property="individualSettle" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="T1_SETTLE_BEGIN_DAY" property="t1SettleBeginDay" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TAX_UNDERTAKE_TYPE" property="taxUndertakeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_TOUCH_SWITCH" property="alipayTouchSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MAX_ACCOUNTS_NUMBER" property="maxAccountsNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NFC_ONLINE_CLUE_SALE" property="nfcOnlineClueSale" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_AUDIT_SWITCH" property="settleAuditSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_CONFIG_OWN_CHANNEL" property="isConfigOwnChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="T1_SETTLE_SHOW_SWITCH" property="t1SettleShowSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_BUYOUT_SALE" property="nfcAlipayBuyoutSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OLD_TAX_UNDERTAKE_TYPE" property="oldTaxUndertakeType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_PAY_RETURN_LATER_SALE" property="nfcPayReturnLaterSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="TAX_UNDERTAKE_START_TIME" property="taxUndertakeStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANNEL_VALUE_ADDED_AGENT" property="channelValueAddedAgent" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDIVIDUAL_SETTLE_START_TIME" property="individualSettleStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PHP_AGENT_BACKEND_PERMISSIONS" property="phpAgentBackendPermissions" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_PAY_RETURN_LATER_SALE" property="nfcAlipayPayReturnLaterSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_ZERO_ENJOY_FIRST_SALE" property="nfcAlipayZeroEnjoyFirstSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMMISSION_APPLY_AMOUNT" property="commissionApplyAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="FREE_HANDLING_FEE_APPLY_AMOUNT" property="freeHandlingFeeApplyAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`KEY_ID`,`AUTO_INCOME`,`COMM_SETTLE`,`SUBMIT_STEP`,`NFC_OFFLINE_SALE`,`NFC_ZERO_BUY_SALE`,`T1_SETTLE_SWITCH`,`MAX_MARKET_NUMBER`,`NFC_SESAME_GO_SALE`,`QUICK_CASH_SWITCH`,`CREATE_SUB_ACCOUNT`,`INDIVIDUAL_SETTLE`,`T1_SETTLE_BEGIN_DAY`,`TAX_UNDERTAKE_TYPE`,`ALIPAY_TOUCH_SWITCH`,`MAX_ACCOUNTS_NUMBER`,`NFC_ONLINE_CLUE_SALE`,`SETTLE_AUDIT_SWITCH`,`IS_CONFIG_OWN_CHANNEL`,`T1_SETTLE_SHOW_SWITCH`,`NFC_ALIPAY_BUYOUT_SALE`,`OLD_TAX_UNDERTAKE_TYPE`,`NFC_PAY_RETURN_LATER_SALE`,`TAX_UNDERTAKE_START_TIME`,`CHANNEL_VALUE_ADDED_AGENT`,`INDIVIDUAL_SETTLE_START_TIME`,`PHP_AGENT_BACKEND_PERMISSIONS`,`NFC_ALIPAY_PAY_RETURN_LATER_SALE`,`NFC_ALIPAY_ZERO_ENJOY_FIRST_SALE`,`COMMISSION_APPLY_AMOUNT`,`FREE_HANDLING_FEE_APPLY_AMOUNT`
    </sql>


            <!--insert:TP_GRANT_RIGHT_CONTROL-->
            <insert id="insert" >
                    INSERT INTO TP_GRANT_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="autoIncome != null">`AUTO_INCOME`,</if>
            <if test="commSettle != null">`COMM_SETTLE`,</if>
            <if test="t1SettleSwitch != null">`T1_SETTLE_SWITCH`,</if>
            <if test="maxMarketNumber != null">`MAX_MARKET_NUMBER`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="createSubAccount != null">`CREATE_SUB_ACCOUNT`,</if>
            <if test="t1SettleBeginDay != null">`T1_SETTLE_BEGIN_DAY`,</if>
            <if test="taxUndertakeType != null">`TAX_UNDERTAKE_TYPE`,</if>
            <if test="alipayTouchSwitch != null">`ALIPAY_TOUCH_SWITCH`,</if>
            <if test="maxAccountsNumber != null">`MAX_ACCOUNTS_NUMBER`,</if>
            <if test="isConfigOwnChannel != null">`IS_CONFIG_OWN_CHANNEL`,</if>
            <if test="t1SettleShowSwitch != null">`T1_SETTLE_SHOW_SWITCH`,</if>
            <if test="oldTaxUndertakeType != null">`OLD_TAX_UNDERTAKE_TYPE`,</if>
            <if test="taxUndertakeStartTime != null">`TAX_UNDERTAKE_START_TIME`,</if>
            <if test="commissionApplyAmount != null">`COMMISSION_APPLY_AMOUNT`,</if>
            <if test="freeHandlingFeeApplyAmount != null">`FREE_HANDLING_FEE_APPLY_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="autoIncome != null">#{autoIncome,jdbcType=TINYINT},</if>
            <if test="commSettle != null">#{commSettle,jdbcType=TINYINT},</if>
            <if test="t1SettleSwitch != null">#{t1SettleSwitch,jdbcType=TINYINT},</if>
            <if test="maxMarketNumber != null">#{maxMarketNumber,jdbcType=INTEGER},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="createSubAccount != null">#{createSubAccount,jdbcType=TINYINT},</if>
            <if test="t1SettleBeginDay != null">#{t1SettleBeginDay,jdbcType=INTEGER},</if>
            <if test="taxUndertakeType != null">#{taxUndertakeType,jdbcType=TINYINT},</if>
            <if test="alipayTouchSwitch != null">#{alipayTouchSwitch,jdbcType=TINYINT},</if>
            <if test="maxAccountsNumber != null">#{maxAccountsNumber,jdbcType=INTEGER},</if>
            <if test="isConfigOwnChannel != null">#{isConfigOwnChannel,jdbcType=TINYINT},</if>
            <if test="t1SettleShowSwitch != null">#{t1SettleShowSwitch,jdbcType=TINYINT},</if>
            <if test="oldTaxUndertakeType != null">#{oldTaxUndertakeType,jdbcType=TINYINT},</if>
            <if test="taxUndertakeStartTime != null">#{taxUndertakeStartTime,jdbcType=INTEGER},</if>
            <if test="commissionApplyAmount != null">#{commissionApplyAmount,jdbcType=DECIMAL},</if>
            <if test="freeHandlingFeeApplyAmount != null">#{freeHandlingFeeApplyAmount,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--getByKeyId:TP_GRANT_RIGHT_CONTROL-->
            <select id="getByKeyId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM TP_GRANT_RIGHT_CONTROL
        WHERE `KEY_ID` = #{keyId,jdbcType=INTEGER}
        limit 1
            </select>

            <!--getByKeyIdList:TP_GRANT_RIGHT_CONTROL-->
            <select id="getByKeyIdList" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM TP_GRANT_RIGHT_CONTROL
        WHERE `KEY_ID` in
        <foreach collection="keyIdList" open="(" close=")" item="keyId" index="index" separator=",">
            #{keyId,jdbcType=INTEGER}
        </foreach>
            </select>
    </mapper>
