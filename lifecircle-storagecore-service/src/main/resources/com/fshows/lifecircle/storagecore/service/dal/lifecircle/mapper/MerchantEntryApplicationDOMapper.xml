<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantEntryApplicationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantEntryApplicationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="QRA" property="qra" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QRC" property="qrc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUPS" property="cups" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TERM_NO" property="termNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MCC_CODE" property="mccCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_CODE" property="orgCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DCEP_SMID" property="dcepSmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RATE_JSON" property="rateJson" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PIC" property="storePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TAX_REG_NO" property="taxRegNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_PID" property="alipayPid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PARENT_MNO" property="parentMno" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_SMID" property="alipaySmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NUMBER" property="bankNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HANDLE_TYPE" property="handleType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_NO" property="identityNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORG_CODE_PIC" property="orgCodePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_AGENT_ID" property="subAgentId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSI_CONTENT" property="busiContent" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_TYPE" property="channelType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DISTRICT_CODE" property="districtCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IDENTITY_NAME" property="identityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ISS_AUTHORITY" property="issAuthority" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_MCC_CODE" property="unionMccCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLICATION_ID" property="applicationId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DCEP_SMID_STATE" property="dcepSmidState" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HAND_IDCARD_PIC" property="handIdcardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BRANCH_BANK_NAME" property="branchBankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INSIDE_SCENE_PIC" property="insideScenePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_MOBILE" property="merchantMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_RATE_ID" property="merchantRateId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC1" property="storeOtherPic1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC2" property="storeOtherPic2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC3" property="storeOtherPic3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_ALIAS_NAME" property="unionAliasName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_SUB_MCH_ID" property="wechatSubMchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LETTER_OF_AUTH_PIC" property="letterOfAuthPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESERVE_BANK_CODE" property="reserveBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESERVE_BANK_NAME" property="reserveBankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_LICENSE_NO" property="settleLicenseNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_MERCHANT_NO" property="tradeMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_PLACE_PIC" property="businessPlacePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DCEP_SMID_ERROR_MSG" property="dcepSmidErrorMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESERVE_ACCOUNT_NO" property="reserveAccountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANKCARD_NO" property="settleBankcardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_CIRCLE_FLAG" property="settleCircleFlag" jdbcType="CHAR"
        javaType="String"/>

            <result column="WECHAT_CHANNEL_NUM" property="wechatChannelNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_TELEPHONE" property="customerTelephone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_REG_ADDRESS" property="licenseRegAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_MODIFY_PIC" property="merchantModifyPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_SHORT_NAME" property="merchantShortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NAME" property="settleAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPPORT_TRADE_TYPES" property="supportTradeTypes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_MERCHANT_NO" property="platformMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RESERVE_ACCOUNT_NAME" property="reserveAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BIZ_LICENSE_PIC" property="storeBizLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUPPORT_PAY_CHANNELS" property="supportPayChannels" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANKCARD_OPPOSITE_PIC" property="bankcardOppositePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANKCARD_POSITIVE_PIC" property="bankcardPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_CODE" property="businessLicenseCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_NAME" property="businessLicenseName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OLD_SETTLE_BANKCARD_NO" property="oldSettleBankcardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_VIDEOS_URL" property="storeOtherVideosUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TAX_REGIST_LICENSE_PIC" property="taxRegistLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_LICENSE_END_TIME" property="settleLicenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_INFO_MODIFY_PIC" property="merchantInfoModifyPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_END_TIME" property="businessLicenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_OPPOSITE_PIC" property="legalPersonOppositePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_POSITIVE_PIC" property="legalPersonPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_LICENSE_START_TIME" property="settleLicenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_START_TIME" property="businessLicenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_IDCARD_ADDRESS" property="legalPersonIdcardAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPENING_ACCOUNT_LICENSE_PIC" property="openingAccountLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_LICENSE_END_TIME" property="legalPersonLicenseEndTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_LICENSE_START_TIME" property="legalPersonLicenseStartTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_OPPOSITE_PIC" property="settlePersonIdcardOppositePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_PERSON_IDCARD_POSITIVE_PIC" property="settlePersonIdcardPositivePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_FINISH" property="isFinish" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SUBMIT" property="isSubmit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USER_FEE_ID" property="userFeeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CHANGE_CARD" property="changeCard" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIG_STEP" property="configStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPLY_STATUS" property="applyStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMPANY_TYPE" property="companyType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IDENTITY_TYPE" property="identityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LICENSE_MATCH" property="licenseMatch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_TYPE" property="merchantType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_MAIN_CHANNEL" property="isMainChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_REJECTED_STORE" property="isRejectedStore" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPERATIONAL_TYPE" property="operationalType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMPANY_CHILD_TYPE" property="companyChildType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDEPENDENT_MODEL" property="independentModel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WECHANT_CHANNEL_ID" property="wechantChannelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_REJECTED_ACCOUNT" property="isRejectedAccount" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_ACCOUNT_TYPE" property="settleAccountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_MCH_ID_ROUTE_FLAG" property="subMchIdRouteFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_REJECTED_BANKCARD" property="isRejectedBankcard" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_IDENTITY_TYPE" property="settleIdentityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WX_DR_CR_SEPARATE_FLAG" property="wxDrCrSeparateFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_MIGRATE_LIQUIDATION" property="isMigrateLiquidation" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_QUALIFICATION" property="merchantQualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UNION_RATE" property="unionRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="ALIPAY_RATE" property="alipayRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="WECHAT_RATE" property="wechatRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QRA`,`QRC`,`CUPS`,`TOKEN`,`TERM_NO`,`ADDRESS`,`APPLY_ID`,`MCC_CODE`,`ORG_CODE`,`CITY_CODE`,`DCEP_SMID`,`RATE_JSON`,`STORE_PIC`,`TAX_REG_NO`,`ALIPAY_PID`,`PARENT_MNO`,`UNION_CODE`,`ALIPAY_SMID`,`BANK_NUMBER`,`HANDLE_TYPE`,`IDENTITY_NO`,`LICENSE_PIC`,`MERCHANT_NO`,`ORG_CODE_PIC`,`SUB_AGENT_ID`,`BUSI_CONTENT`,`CHANNEL_TYPE`,`DISTRICT_CODE`,`IDENTITY_NAME`,`ISS_AUTHORITY`,`PROVINCE_CODE`,`REJECT_REASON`,`UNION_MCC_CODE`,`APPLICATION_ID`,`DCEP_SMID_STATE`,`HAND_IDCARD_PIC`,`PLATFORM_ORG_ID`,`BRANCH_BANK_NAME`,`INSIDE_SCENE_PIC`,`MERCHANT_MOBILE`,`MERCHANT_RATE_ID`,`STORE_OTHER_PIC1`,`STORE_OTHER_PIC2`,`STORE_OTHER_PIC3`,`UNION_ALIAS_NAME`,`WECHAT_SUB_MCH_ID`,`LETTER_OF_AUTH_PIC`,`RESERVE_BANK_CODE`,`RESERVE_BANK_NAME`,`SETTLE_LICENSE_NO`,`TRADE_MERCHANT_NO`,`BUSINESS_PLACE_PIC`,`DCEP_SMID_ERROR_MSG`,`RESERVE_ACCOUNT_NO`,`SETTLE_BANKCARD_NO`,`SETTLE_CIRCLE_FLAG`,`WECHAT_CHANNEL_NUM`,`CUSTOMER_TELEPHONE`,`LICENSE_REG_ADDRESS`,`MERCHANT_MODIFY_PIC`,`MERCHANT_SHORT_NAME`,`SETTLE_ACCOUNT_NAME`,`SUPPORT_TRADE_TYPES`,`PLATFORM_MERCHANT_NO`,`RESERVE_ACCOUNT_NAME`,`STORE_BIZ_LICENSE_PIC`,`SUPPORT_PAY_CHANNELS`,`BANKCARD_OPPOSITE_PIC`,`BANKCARD_POSITIVE_PIC`,`BUSINESS_LICENSE_CODE`,`BUSINESS_LICENSE_NAME`,`OLD_SETTLE_BANKCARD_NO`,`STORE_OTHER_VIDEOS_URL`,`TAX_REGIST_LICENSE_PIC`,`SETTLE_LICENSE_END_TIME`,`MERCHANT_INFO_MODIFY_PIC`,`BUSINESS_LICENSE_END_TIME`,`LEGAL_PERSON_OPPOSITE_PIC`,`LEGAL_PERSON_POSITIVE_PIC`,`SETTLE_LICENSE_START_TIME`,`BUSINESS_LICENSE_START_TIME`,`LEGAL_PERSON_IDCARD_ADDRESS`,`OPENING_ACCOUNT_LICENSE_PIC`,`LEGAL_PERSON_LICENSE_END_TIME`,`LEGAL_PERSON_LICENSE_START_TIME`,`SETTLE_PERSON_IDCARD_OPPOSITE_PIC`,`SETTLE_PERSON_IDCARD_POSITIVE_PIC`,`UID`,`STORE_ID`,`IS_FINISH`,`IS_SUBMIT`,`USER_FEE_ID`,`CHANGE_CARD`,`CONFIG_STEP`,`SETTLE_MODE`,`SETTLE_TYPE`,`APPLY_STATUS`,`COMPANY_TYPE`,`ACTIVITY_TYPE`,`IDENTITY_TYPE`,`LICENSE_MATCH`,`MERCHANT_TYPE`,`IS_MAIN_CHANNEL`,`IS_REJECTED_STORE`,`OPERATIONAL_TYPE`,`COMPANY_CHILD_TYPE`,`INDEPENDENT_MODEL`,`WECHANT_CHANNEL_ID`,`IS_REJECTED_ACCOUNT`,`SETTLE_ACCOUNT_TYPE`,`SUB_MCH_ID_ROUTE_FLAG`,`IS_REJECTED_BANKCARD`,`SETTLE_IDENTITY_TYPE`,`WX_DR_CR_SEPARATE_FLAG`,`IS_MIGRATE_LIQUIDATION`,`MERCHANT_QUALIFICATION`,`CREATE_TIME`,`UPDATE_TIME`,`UNION_RATE`,`ALIPAY_RATE`,`WECHAT_RATE`
    </sql>


            <!--insert:TP_MERCHANT_ENTRY_APPLICATION-->
            <insert id="insert" >
                            INSERT INTO TP_MERCHANT_ENTRY_APPLICATION
                <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="id != null">`ID`,</if>
                        <if test="address != null">`ADDRESS`,</if>
                        <if test="applyId != null">`APPLY_ID`,</if>
                        <if test="mccCode != null">`MCC_CODE`,</if>
                        <if test="orgCode != null">`ORG_CODE`,</if>
                        <if test="cityCode != null">`CITY_CODE`,</if>
                        <if test="storePic != null">`STORE_PIC`,</if>
                        <if test="taxRegNo != null">`TAX_REG_NO`,</if>
                        <if test="alipayPid != null">`ALIPAY_PID`,</if>
                        <if test="parentMno != null">`PARENT_MNO`,</if>
                        <if test="unionCode != null">`UNION_CODE`,</if>
                        <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
                        <if test="bankNumber != null">`BANK_NUMBER`,</if>
                        <if test="handleType != null">`HANDLE_TYPE`,</if>
                        <if test="identityNo != null">`IDENTITY_NO`,</if>
                        <if test="licensePic != null">`LICENSE_PIC`,</if>
                        <if test="merchantNo != null">`MERCHANT_NO`,</if>
                        <if test="orgCodePic != null">`ORG_CODE_PIC`,</if>
                        <if test="districtCode != null">`DISTRICT_CODE`,</if>
                        <if test="identityName != null">`IDENTITY_NAME`,</if>
                        <if test="provinceCode != null">`PROVINCE_CODE`,</if>
                        <if test="rejectReason != null">`REJECT_REASON`,</if>
                        <if test="applicationId != null">`APPLICATION_ID`,</if>
                        <if test="handIdcardPic != null">`HAND_IDCARD_PIC`,</if>
                        <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
                        <if test="branchBankName != null">`BRANCH_BANK_NAME`,</if>
                        <if test="insideScenePic != null">`INSIDE_SCENE_PIC`,</if>
                        <if test="merchantMobile != null">`MERCHANT_MOBILE`,</if>
                        <if test="wechatSubMchId != null">`WECHAT_SUB_MCH_ID`,</if>
                        <if test="letterOfAuthPic != null">`LETTER_OF_AUTH_PIC`,</if>
                        <if test="settleLicenseNo != null">`SETTLE_LICENSE_NO`,</if>
                        <if test="businessPlacePic != null">`BUSINESS_PLACE_PIC`,</if>
                        <if test="settleBankcardNo != null">`SETTLE_BANKCARD_NO`,</if>
                        <if test="wechatChannelNum != null">`WECHAT_CHANNEL_NUM`,</if>
                        <if test="customerTelephone != null">`CUSTOMER_TELEPHONE`,</if>
                        <if test="merchantShortName != null">`MERCHANT_SHORT_NAME`,</if>
                        <if test="settleAccountName != null">`SETTLE_ACCOUNT_NAME`,</if>
                        <if test="supportTradeTypes != null">`SUPPORT_TRADE_TYPES`,</if>
                        <if test="supportPayChannels != null">`SUPPORT_PAY_CHANNELS`,</if>
                        <if test="bankcardOppositePic != null">`BANKCARD_OPPOSITE_PIC`,</if>
                        <if test="bankcardPositivePic != null">`BANKCARD_POSITIVE_PIC`,</if>
                        <if test="businessLicenseCode != null">`BUSINESS_LICENSE_CODE`,</if>
                        <if test="businessLicenseName != null">`BUSINESS_LICENSE_NAME`,</if>
                        <if test="taxRegistLicensePic != null">`TAX_REGIST_LICENSE_PIC`,</if>
                        <if test="settleLicenseEndTime != null">`SETTLE_LICENSE_END_TIME`,</if>
                        <if test="businessLicenseEndTime != null">`BUSINESS_LICENSE_END_TIME`,</if>
                        <if test="legalPersonOppositePic != null">`LEGAL_PERSON_OPPOSITE_PIC`,</if>
                        <if test="legalPersonPositivePic != null">`LEGAL_PERSON_POSITIVE_PIC`,</if>
                        <if test="settleLicenseStartTime != null">`SETTLE_LICENSE_START_TIME`,</if>
                        <if test="businessLicenseStartTime != null">`BUSINESS_LICENSE_START_TIME`,</if>
                        <if test="openingAccountLicensePic != null">`OPENING_ACCOUNT_LICENSE_PIC`,</if>
                        <if test="legalPersonLicenseEndTime != null">`LEGAL_PERSON_LICENSE_END_TIME`,</if>
                        <if test="legalPersonLicenseStartTime != null">`LEGAL_PERSON_LICENSE_START_TIME`,</if>
                        <if test="settlePersonIdcardOppositePic != null">`SETTLE_PERSON_IDCARD_OPPOSITE_PIC`,</if>
                        <if test="settlePersonIdcardPositivePic != null">`SETTLE_PERSON_IDCARD_POSITIVE_PIC`,</if>
                        <if test="uid != null">`UID`,</if>
                        <if test="storeId != null">`STORE_ID`,</if>
                        <if test="isFinish != null">`IS_FINISH`,</if>
                        <if test="isSubmit != null">`IS_SUBMIT`,</if>
                        <if test="userFeeId != null">`USER_FEE_ID`,</if>
                        <if test="configStep != null">`CONFIG_STEP`,</if>
                        <if test="settleType != null">`SETTLE_TYPE`,</if>
                        <if test="applyStatus != null">`APPLY_STATUS`,</if>
                        <if test="activityType != null">`ACTIVITY_TYPE`,</if>
                        <if test="identityType != null">`IDENTITY_TYPE`,</if>
                        <if test="licenseMatch != null">`LICENSE_MATCH`,</if>
                        <if test="merchantType != null">`MERCHANT_TYPE`,</if>
                        <if test="isRejectedStore != null">`IS_REJECTED_STORE`,</if>
                        <if test="operationalType != null">`OPERATIONAL_TYPE`,</if>
                        <if test="independentModel != null">`INDEPENDENT_MODEL`,</if>
                        <if test="wechantChannelId != null">`WECHANT_CHANNEL_ID`,</if>
                        <if test="isRejectedAccount != null">`IS_REJECTED_ACCOUNT`,</if>
                        <if test="settleAccountType != null">`SETTLE_ACCOUNT_TYPE`,</if>
                        <if test="isRejectedBankcard != null">`IS_REJECTED_BANKCARD`,</if>
                        <if test="isMigrateLiquidation != null">`IS_MIGRATE_LIQUIDATION`,</if>
                        <if test="merchantQualification != null">`MERCHANT_QUALIFICATION`,</if>
                        <if test="createTime != null">`CREATE_TIME`,</if>
                        <if test="updateTime != null">`UPDATE_TIME`,</if>
                        <if test="unionRate != null">`UNION_RATE`,</if>
                        <if test="alipayRate != null">`ALIPAY_RATE`,</if>
                        <if test="wechatRate != null">`WECHAT_RATE`,</if>
                </trim>
                VALUES
                <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="id != null">#{id,jdbcType=BIGINT},</if>
                        <if test="address != null">#{address,jdbcType=VARCHAR},</if>
                        <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
                        <if test="mccCode != null">#{mccCode,jdbcType=VARCHAR},</if>
                        <if test="orgCode != null">#{orgCode,jdbcType=VARCHAR},</if>
                        <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
                        <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
                        <if test="taxRegNo != null">#{taxRegNo,jdbcType=VARCHAR},</if>
                        <if test="alipayPid != null">#{alipayPid,jdbcType=VARCHAR},</if>
                        <if test="parentMno != null">#{parentMno,jdbcType=VARCHAR},</if>
                        <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
                        <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
                        <if test="bankNumber != null">#{bankNumber,jdbcType=VARCHAR},</if>
                        <if test="handleType != null">#{handleType,jdbcType=VARCHAR},</if>
                        <if test="identityNo != null">#{identityNo,jdbcType=VARCHAR},</if>
                        <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
                        <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
                        <if test="orgCodePic != null">#{orgCodePic,jdbcType=VARCHAR},</if>
                        <if test="districtCode != null">#{districtCode,jdbcType=VARCHAR},</if>
                        <if test="identityName != null">#{identityName,jdbcType=VARCHAR},</if>
                        <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
                        <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
                        <if test="applicationId != null">#{applicationId,jdbcType=VARCHAR},</if>
                        <if test="handIdcardPic != null">#{handIdcardPic,jdbcType=VARCHAR},</if>
                        <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
                        <if test="branchBankName != null">#{branchBankName,jdbcType=VARCHAR},</if>
                        <if test="insideScenePic != null">#{insideScenePic,jdbcType=VARCHAR},</if>
                        <if test="merchantMobile != null">#{merchantMobile,jdbcType=VARCHAR},</if>
                        <if test="wechatSubMchId != null">#{wechatSubMchId,jdbcType=VARCHAR},</if>
                        <if test="letterOfAuthPic != null">#{letterOfAuthPic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseNo != null">#{settleLicenseNo,jdbcType=VARCHAR},</if>
                        <if test="businessPlacePic != null">#{businessPlacePic,jdbcType=VARCHAR},</if>
                        <if test="settleBankcardNo != null">#{settleBankcardNo,jdbcType=VARCHAR},</if>
                        <if test="wechatChannelNum != null">#{wechatChannelNum,jdbcType=VARCHAR},</if>
                        <if test="customerTelephone != null">#{customerTelephone,jdbcType=VARCHAR},</if>
                        <if test="merchantShortName != null">#{merchantShortName,jdbcType=VARCHAR},</if>
                        <if test="settleAccountName != null">#{settleAccountName,jdbcType=VARCHAR},</if>
                        <if test="supportTradeTypes != null">#{supportTradeTypes,jdbcType=VARCHAR},</if>
                        <if test="supportPayChannels != null">#{supportPayChannels,jdbcType=VARCHAR},</if>
                        <if test="bankcardOppositePic != null">#{bankcardOppositePic,jdbcType=VARCHAR},</if>
                        <if test="bankcardPositivePic != null">#{bankcardPositivePic,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseCode != null">#{businessLicenseCode,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseName != null">#{businessLicenseName,jdbcType=VARCHAR},</if>
                        <if test="taxRegistLicensePic != null">#{taxRegistLicensePic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseEndTime != null">#{settleLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseEndTime != null">#{businessLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="legalPersonOppositePic != null">#{legalPersonOppositePic,jdbcType=VARCHAR},</if>
                        <if test="legalPersonPositivePic != null">#{legalPersonPositivePic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseStartTime != null">#{settleLicenseStartTime,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseStartTime != null">#{businessLicenseStartTime,jdbcType=VARCHAR},</if>
                        <if test="openingAccountLicensePic != null">#{openingAccountLicensePic,jdbcType=VARCHAR},</if>
                        <if test="legalPersonLicenseEndTime != null">#{legalPersonLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="legalPersonLicenseStartTime != null">
                                #{legalPersonLicenseStartTime,jdbcType=VARCHAR},
                        </if>
                        <if test="settlePersonIdcardOppositePic != null">
                                #{settlePersonIdcardOppositePic,jdbcType=VARCHAR},
                        </if>
                        <if test="settlePersonIdcardPositivePic != null">
                                #{settlePersonIdcardPositivePic,jdbcType=VARCHAR},
                        </if>
                        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
                        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
                        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
                        <if test="isSubmit != null">#{isSubmit,jdbcType=TINYINT},</if>
                        <if test="userFeeId != null">#{userFeeId,jdbcType=INTEGER},</if>
                        <if test="configStep != null">#{configStep,jdbcType=TINYINT},</if>
                        <if test="settleType != null">#{settleType,jdbcType=TINYINT},</if>
                        <if test="applyStatus != null">#{applyStatus,jdbcType=TINYINT},</if>
                        <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
                        <if test="identityType != null">#{identityType,jdbcType=TINYINT},</if>
                        <if test="licenseMatch != null">#{licenseMatch,jdbcType=TINYINT},</if>
                        <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
                        <if test="isRejectedStore != null">#{isRejectedStore,jdbcType=TINYINT},</if>
                        <if test="operationalType != null">#{operationalType,jdbcType=TINYINT},</if>
                        <if test="independentModel != null">#{independentModel,jdbcType=TINYINT},</if>
                        <if test="wechantChannelId != null">#{wechantChannelId,jdbcType=INTEGER},</if>
                        <if test="isRejectedAccount != null">#{isRejectedAccount,jdbcType=TINYINT},</if>
                        <if test="settleAccountType != null">#{settleAccountType,jdbcType=TINYINT},</if>
                        <if test="isRejectedBankcard != null">#{isRejectedBankcard,jdbcType=TINYINT},</if>
                        <if test="isMigrateLiquidation != null">#{isMigrateLiquidation,jdbcType=TINYINT},</if>
                        <if test="merchantQualification != null">#{merchantQualification,jdbcType=TINYINT},</if>
                        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                        <if test="unionRate != null">#{unionRate,jdbcType=DECIMAL},</if>
                        <if test="alipayRate != null">#{alipayRate,jdbcType=DECIMAL},</if>
                        <if test="wechatRate != null">#{wechatRate,jdbcType=DECIMAL},</if>
                </trim>
            </insert>

            <!--根据商户商编查询进件申请单信息-->
            <select id="getByMerchantNo" resultMap="BaseResultMap">
                            SELECT
                <include refid="Base_Column_List" />
                FROM
                tp_merchant_entry_application
                WHERE
                merchant_no = #{merchantNo,jdbcType=INTEGER}
                AND handle_type = 'STORE_INCOME'
                ORDER BY
                id desc
                LIMIT 1
            </select>

            <!--根据商户商编查询申请单信息-->
            <select id="getOneByMerchantNo" resultMap="BaseResultMap">
                            SELECT
                <include refid="Base_Column_List" />
                FROM
                tp_merchant_entry_application
                WHERE
                merchant_no = #{merchantNo,jdbcType=INTEGER}
                ORDER BY
                id desc
                LIMIT 1
            </select>

            <!--根据uid,机构号查询-->
            <select id="getByUid" resultMap="BaseResultMap">
                            SELECT
                <include refid="Base_Column_List" />
                FROM
                tp_merchant_entry_application
                WHERE
                uid = #{uid,jdbcType=INTEGER}
                and platform_org_id = #{platformOrgId,jdbcType=VARCHAR}
                ORDER BY
                id asc
                LIMIT 1
            </select>
    </mapper>
