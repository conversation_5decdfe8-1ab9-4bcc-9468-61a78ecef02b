<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.SinanActivityExtendDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.SinanActivityExtendDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_BY" property="createBy" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_STATUS" property="userStatus" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CARD_ACTIVITY_STATUS" property="cardActivityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="INDUSTRY_ACTIVITY_STATUS" property="industryActivityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_ACTIVITY_STATUS" property="merchantActivityStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UID`,`CREATE_BY`,`USER_STATUS`,`CARD_ACTIVITY_STATUS`,`INDUSTRY_ACTIVITY_STATUS`,`MERCHANT_ACTIVITY_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--getManagerExtendByUserId-->
            <select id="getManagerExtendByUserId" resultMap="BaseResultMap">
                    select /*MS-LM-SINAN-ACTIVITY-EXTEND-GETMANAGEREXTENDBYUSERID*/ <include refid="Base_Column_List" /> from lm_sinan_activity_extend where uid=#{userId,jdbcType=INTEGER}
            </select>

            <!--insertSiNanActivityExtend-->
            <insert id="insertSiNanActivityExtend" >
                    insert into LM_SINAN_ACTIVITY_EXTEND
        <if test="uid !=null ">`uid`</if>
        <if test="industryActivityStatus !=null ">`industry_activity_status`</if>
        <if test="merchantActivityStatus !=null ">`merchant_activity_status`</if>
        <if test="cardActivityStatus !=null ">`card_activity_status`</if>
        <if test="userStatus !=null ">`user_status`</if>
        <if test="createTime !=null ">`create_time`</if>
        <if test="updateTime !=null ">`update_time`</if>
        <if test="createBy !=null ">`creqte_by`</if>
        values
        (#{uid,jdbcType=INTEGER},#{industryActivityStatus,jdbcType=INTEGER},
        #{merchantActivityStatus,jdbcType=INTEGER},
        #{cardActivityStatus,jdbcType=INTEGER},#{userStatus,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=INTEGER})
            </insert>

            <!--updateActivityStatus-->
            <update id="updateActivityStatus" >
                    update /*MS-LM-SINAN-ACTIVITY-EXTEND-UPDATEACTIVITYSTATUS*/ LM_SINAN_ACTIVITY_EXTEND
        <set>
            <if test="userStatus != null">USER_STATUS = #{userStatus,jdbcType=INTEGER},</if>
            <if test="createBy != null">create_by = #{createBy,jdbcType=INTEGER},</if>
            <if test="industryActivityStatus != null">
                industry_activity_status=#{industryActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="merchantActivityStatus != null">
                merchant_activity_status=#{merchantActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="cardActivityStatus != null">
                card_activity_status=#{cardActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="updateTime ! = null">
                update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
        </set>
        where user_id = #{userId,jdbcType=INTEGER}
            </update>
    </mapper>
