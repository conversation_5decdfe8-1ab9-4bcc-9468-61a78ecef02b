<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.PlatformSettleBillDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.PlatformSettleBillDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="FROZEN_AMOUNT" property="frozenAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SETTLE_AMOUNT" property="settleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="GOODS_SETTLE_AMOUNT" property="goodsSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="REFUND_SETTLE_AMOUNT" property="refundSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PAYMENT_SETTLE_AMOUNT" property="paymentSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="RECONCILIATION_AMOUNT" property="reconciliationAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE" property="source" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_DATE" property="settleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_CODE" property="channelCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_CHECK_SOURCE" property="billCheckSource" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_SETTLE_DATE" property="platformSettleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_CHECK_STATUS" property="billCheckStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FROZEN_AMOUNT`,`SETTLE_AMOUNT`,`GOODS_SETTLE_AMOUNT`,`REFUND_SETTLE_AMOUNT`,`PAYMENT_SETTLE_AMOUNT`,`RECONCILIATION_AMOUNT`,`SP_ID`,`BILL_NO`,`BLOC_ID`,`REMARK`,`SOURCE`,`SETTLE_DATE`,`CHANNEL_CODE`,`PLATFORM_CODE`,`BILL_CHECK_SOURCE`,`PLATFORM_SETTLE_DATE`,`BILL_CHECK_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_PLATFORM_SETTLE_BILL-->
            <insert id="insert" >
            INSERT INTO ACC_PLATFORM_SETTLE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="settleAmount != null">`SETTLE_AMOUNT`,</if>
        <if test="refundSettleAmount != null">`REFUND_SETTLE_AMOUNT`,</if>
        <if test="paymentSettleAmount != null">`PAYMENT_SETTLE_AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="billCheckSource != null">`BILL_CHECK_SOURCE`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="billCheckStatus != null">`BILL_CHECK_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="goodsSettleAmount != null">`GOODS_SETTLE_AMOUNT`,</if>
        <if test="reconciliationAmount != null">`RECONCILIATION_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="settleAmount != null">#{settleAmount,jdbcType=BIGINT},</if>
        <if test="refundSettleAmount != null">#{refundSettleAmount,jdbcType=BIGINT},</if>
        <if test="paymentSettleAmount != null">#{paymentSettleAmount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="billCheckSource != null">#{billCheckSource,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="billCheckStatus != null">#{billCheckStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="goodsSettleAmount != null">#{goodsSettleAmount,jdbcType=BIGINT},</if>
        <if test="reconciliationAmount != null">#{reconciliationAmount,jdbcType=BIGINT},</if>
    </trim>
            </insert>

            <!--查询待处理的数据列表-->
            <select id="getBySettleDayAndPlatform" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        ACC_PLATFORM_SETTLE_BILL
        where
        SETTLE_DATE = #{settleDate,jdbcType=VARCHAR}
        and PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        and SP_ID = #{spId,jdbcType=VARCHAR}
        limit 1
            </select>

            <!--查询待处理的数据列表-->
            <delete id="deleteByBillNo" >
                    delete
        from
        ACC_PLATFORM_SETTLE_BILL
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
            </delete>
    </mapper>
