<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.PlatformStoreSettleBillDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.PlatformStoreSettleBillDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="FROZEN_COUNT" property="frozenCount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="FROZEN_AMOUNT" property="frozenAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SETTLE_AMOUNT" property="settleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="GOODS_SETTLE_COUNT" property="goodsSettleCount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="GOODS_SETTLE_AMOUNT" property="goodsSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="REFUND_SETTLE_COUNT" property="refundSettleCount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PAYMENT_SETTLE_COUNT" property="paymentSettleCount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="REFUND_SETTLE_AMOUNT" property="refundSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="PAYMENT_SETTLE_AMOUNT" property="paymentSettleAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="RECONCILIATION_AMOUNT" property="reconciliationAmount" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="SP_ID" property="spId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_NO" property="billNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE" property="source" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_DATE" property="settleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BILL_NO" property="storeBillNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_CODE" property="platformCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_STORE_ID" property="platformStoreId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_HANDLE_SOURCE" property="billHandleSource" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_STORE_NAME" property="platformStoreName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_SETTLE_DATE" property="platformSettleDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROFIT_SHARE_BATCH_ID" property="profitShareBatchId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BILL_HANDLE_STATUS" property="billHandleStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`FROZEN_COUNT`,`FROZEN_AMOUNT`,`SETTLE_AMOUNT`,`GOODS_SETTLE_COUNT`,`GOODS_SETTLE_AMOUNT`,`REFUND_SETTLE_COUNT`,`PAYMENT_SETTLE_COUNT`,`REFUND_SETTLE_AMOUNT`,`PAYMENT_SETTLE_AMOUNT`,`RECONCILIATION_AMOUNT`,`SP_ID`,`BILL_NO`,`BLOC_ID`,`REMARK`,`SOURCE`,`SETTLE_DATE`,`STORE_BILL_NO`,`PLATFORM_CODE`,`PLATFORM_STORE_ID`,`BILL_HANDLE_SOURCE`,`PLATFORM_STORE_NAME`,`PLATFORM_SETTLE_DATE`,`PROFIT_SHARE_BATCH_ID`,`BILL_HANDLE_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_PLATFORM_STORE_SETTLE_BILL-->
            <insert id="insert" >
            INSERT INTO ACC_PLATFORM_STORE_SETTLE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="settleAmount != null">`SETTLE_AMOUNT`,</if>
        <if test="refundSettleCount != null">`REFUND_SETTLE_COUNT`,</if>
        <if test="paymentSettleCount != null">`PAYMENT_SETTLE_COUNT`,</if>
        <if test="refundSettleAmount != null">`REFUND_SETTLE_AMOUNT`,</if>
        <if test="paymentSettleAmount != null">`PAYMENT_SETTLE_AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
        <if test="billHandleSource != null">`BILL_HANDLE_SOURCE`,</if>
        <if test="platformStoreName != null">`PLATFORM_STORE_NAME`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="profitShareBatchId != null">`PROFIT_SHARE_BATCH_ID`,</if>
        <if test="billHandleStatus != null">`BILL_HANDLE_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="frozenCount != null">`FROZEN_COUNT`,</if>
        <if test="outTaskId != null">`OUT_TASK_ID`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="settleAmount != null">#{settleAmount,jdbcType=BIGINT},</if>
        <if test="refundSettleCount != null">#{refundSettleCount,jdbcType=BIGINT},</if>
        <if test="paymentSettleCount != null">#{paymentSettleCount,jdbcType=BIGINT},</if>
        <if test="refundSettleAmount != null">#{refundSettleAmount,jdbcType=BIGINT},</if>
        <if test="paymentSettleAmount != null">#{paymentSettleAmount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
        <if test="billHandleSource != null">#{billHandleSource,jdbcType=VARCHAR},</if>
        <if test="platformStoreName != null">#{platformStoreName,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="profitShareBatchId != null">#{profitShareBatchId,jdbcType=VARCHAR},</if>
        <if test="billHandleStatus != null">#{billHandleStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="frozenCount != null">#{frozenCount,jdbcType=BIGINT},</if>
        <if test="outTaskId != null">#{outTaskId,jdbcType=VARCHAR},</if>
    </trim>
            </insert>

            <!--批量新增-->
            <select id="batchInsert" resultMap="BaseResultMap">
                    INSERT INTO ACC_PLATFORM_STORE_SETTLE_BILL (
            `SETTLE_AMOUNT`,
            `REFUND_SETTLE_COUNT`,
            `PAYMENT_SETTLE_COUNT`,
            `REFUND_SETTLE_AMOUNT`,
            `PAYMENT_SETTLE_AMOUNT`,
            `SP_ID`,
            `BILL_NO`,
            `BLOC_ID`,
            `SOURCE`,
            `SETTLE_DATE`,
            `STORE_BILL_NO`,
            `PLATFORM_CODE`,
            `PLATFORM_STORE_ID`,
            `PLATFORM_STORE_NAME`,
            `PLATFORM_SETTLE_DATE`,
            `FROZEN_AMOUNT`,
            `FROZEN_COUNT`,
            `goods_settle_count`,
            `goods_settle_amount`,
            `reconciliation_amount`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.settleAmount,jdbcType=BIGINT},
                #{item.refundSettleCount,jdbcType=BIGINT},
                #{item.paymentSettleCount,jdbcType=BIGINT},
                #{item.refundSettleAmount,jdbcType=BIGINT},
                #{item.paymentSettleAmount,jdbcType=BIGINT},
                #{item.spId,jdbcType=VARCHAR},
                #{item.billNo,jdbcType=VARCHAR},
                #{item.blocId,jdbcType=VARCHAR},
                #{item.source,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.storeBillNo,jdbcType=VARCHAR},
                #{item.platformCode,jdbcType=VARCHAR},
                #{item.platformStoreId,jdbcType=VARCHAR},
                #{item.platformStoreName,jdbcType=VARCHAR},
                #{item.platformSettleDate,jdbcType=VARCHAR},
                #{item.frozenAmount,jdbcType=BIGINT},
                #{item.frozenCount,jdbcType=BIGINT},
                #{item.goodsSettleCount,jdbcType=BIGINT},
                #{item.goodsSettleAmount,jdbcType=BIGINT},
                #{item.reconciliationAmount,jdbcType=BIGINT}
            )
        </foreach>
            </select>

            <!--查询待处理的数据列表-->
            <delete id="deleteByBillNo" >
                    delete
        from
        ACC_PLATFORM_STORE_SETTLE_BILL
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
            </delete>
    </mapper>
