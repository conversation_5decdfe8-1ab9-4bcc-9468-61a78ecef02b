<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardStockDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardStockDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PUBLISH_ORG_ID" property="publishOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STOCK" property="stock" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LOCK_STOCK" property="lockStock" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_SKU_ID`,`CARD_SPU_ID`,`PUBLISH_ORG_ID`,`IS_DEL`,`STOCK`,`LOCK_STOCK`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_STOCK-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="lockStock != null">`LOCK_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="lockStock != null">#{lockStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取库存列表根据skuId列表-->
            <select id="findCardStockListBySkuIdList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-PREPAY-CARD-STOCK-FINDCARDSTOCKLISTBYSKUIDLIST*/  <include refid="Base_Column_List" />
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
        <if test="list != null and list.size() &gt; 0">
            AND card_sku_id IN
            <foreach collection="list" item="cardSkuId" open="(" close=")" separator=",">
                #{cardSkuId,jdbcType=VARCHAR}
            </foreach>
        </if>
            </select>

            <!--根据skuid获取stock信息-->
            <select id="findCardStockBySkuId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-PREPAY-CARD-STOCK-FINDCARDSTOCKBYSKUID*/  <include refid="Base_Column_List" />
                FROM tp_prepay_card_stock
                WHERE is_del = 0
                AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
                AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
                limit 1
            </select>

            <!--获取卡种类skuId获取卡库存-->
            <select id="getCardStockBySkuIdAndPublishOrgId" resultType="java.lang.Integer">
                    SELECT
        stock - lock_stock
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND card_spu_id = #{cardSpuId, jdbcType=VARCHAR}
        AND card_sku_id = #{cardSkuId, jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
            </select>

            <!--根据发行组织ID获取卡库存信息-->
            <select id="getCardStockByPublishOrgId" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
        AND card_sku_id in
        <foreach collection="cardSkuIdList" item="cardSkuId" open="(" close=")" separator=",">
            #{cardSkuId,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--根据发行组织ID获取卡真实库存信息-->
            <select id="getCardRealStockByPublishOrgId" resultMap="BaseResultMap">
                    SELECT
        (stock - lock_stock) AS stock,
        card_sku_id AS cardSkuId
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
        <if test="list != null and list.size() &gt; 0">
            AND card_sku_id IN
            <foreach collection="list" item="cardSkuId" open="(" close=")" separator=",">
                #{cardSkuId,jdbcType=VARCHAR}
            </foreach>
        </if>
            </select>
    </mapper>
