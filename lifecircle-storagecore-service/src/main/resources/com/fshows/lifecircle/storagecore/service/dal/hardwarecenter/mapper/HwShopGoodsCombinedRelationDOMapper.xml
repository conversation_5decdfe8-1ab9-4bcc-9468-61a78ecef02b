<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwShopGoodsCombinedRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwShopGoodsCombinedRelationDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="GOODS_NAME" property="goodsName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GOODS_SPU_ID" property="goodsSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PRODUCT_TYPE" property="productType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EQUIPMENT_NUMBER" property="equipmentNumber" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`GOODS_NAME`,`GOODS_SPU_ID`,`IS_DEL`,`EQUIPMENT_ID`,`PRODUCT_TYPE`,`EQUIPMENT_NUMBER`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_SHOP_GOODS_COMBINED_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_SHOP_GOODS_COMBINED_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="equipmentNumber != null">`EQUIPMENT_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="equipmentNumber != null">#{equipmentNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据商品spuId查询关联套餐设备-->
            <select id="findGoodsCombinedByGoodsSpuId" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from HW_SHOP_GOODS_COMBINED_RELATION
        where goods_spu_id = #{goodsSpuId,jdbcType=VARCHAR}
        and is_del = 0
            </select>
    </mapper>
