<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.mapper.OrgRelationAccountDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fb_account_biz.dataobject.OrgRelationAccountDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="ORG_ID" property="orgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BLOC_ID" property="blocId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FULL_PATH" property="fullPath" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_ID" property="accountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_TYPE" property="accountType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MAX_BIND_NUM" property="maxBindNum" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`ORG_ID`,`BLOC_ID`,`FULL_PATH`,`ACCOUNT_ID`,`ACCOUNT_TYPE`,`DEL_FLAG`,`MAX_BIND_NUM`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:ACC_ORG_RELATION_ACCOUNT-->
            <insert id="insert" >
                    INSERT INTO ACC_ORG_RELATION_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="fullPath != null">`FULL_PATH`,</if>
            <if test="accountId != null">`ACCOUNT_ID`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="maxBindNum != null">`MAX_BIND_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="fullPath != null">#{fullPath,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
            <if test="accountType != null">#{accountType,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="maxBindNum != null">#{maxBindNum,jdbcType=SMALLINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--获取集团子户账户-->
            <select id="getBlocSubAccount" resultMap="BaseResultMap">
                    select /*MS-ACC-ORG-RELATION-ACCOUNT-GETBLOCSUBACCOUNT*/ <include refid="Base_Column_List" /> from ACC_ORG_RELATION_ACCOUNT
        where bloc_id = #{blocId,jdbcType=VARCHAR}
        and account_type = '3'
        and del_flag = 1
        limit 1
            </select>
    </mapper>
