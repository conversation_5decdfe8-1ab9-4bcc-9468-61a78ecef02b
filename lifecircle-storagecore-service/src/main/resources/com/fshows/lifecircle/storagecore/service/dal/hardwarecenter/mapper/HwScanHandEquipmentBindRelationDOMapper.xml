<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwScanHandEquipmentBindRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwScanHandEquipmentBindRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INIT_SN" property="initSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AGENT_USERNAME" property="agentUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SALESMAN_USERNAME" property="salesmanUsername" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INIT_SN`,`COMPANY`,`OPERATOR`,`USERNAME`,`AGENT_USERNAME`,`SALESMAN_USERNAME`,`UID`,`IS_DEL`,`AGENT_ID`,`SALESMAN_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_SCAN_HAND_EQUIPMENT_BIND_RELATION-->
            <insert id="insert" >
                    INSERT INTO HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--batchInsert:HW_SCAN_HAND_EQUIPMENT_BIND_RELATION-->
            <select id="batchAdd" resultMap="BaseResultMap">
                    INSERT INTO HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        (
        INIT_SN,
        OPERATOR,
        USERNAME,
        AGENT_USERNAME,
        SALESMAN_USERNAME,
        UID,
        AGENT_ID,
        SALESMAN_ID
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.initSn,jdbcType=VARCHAR},
            #{item.operator,jdbcType=VARCHAR},
            #{item.username,jdbcType=VARCHAR},
            #{item.agentUsername,jdbcType=VARCHAR},
            #{item.salesmanUsername,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.agentId,jdbcType=INTEGER},
            #{item.salesmanId,jdbcType=INTEGER}
            )
        </foreach>
            </select>

            <!--getByInitSn-->
            <select id="getByInitSn" resultMap="BaseResultMap">
                    select /*MS-HW-SCAN-HAND-EQUIPMENT-BIND-RELATION-GETBYINITSN*/ <include refid="Base_Column_List" />
        from HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        where init_sn = #{initSn,jdbcType=VARCHAR}
        and is_del = 0
        limit 1;
            </select>

            <!--分页查询刷掌设备关联关系列表 pageCount-->
            <select id="findScanHandEquipmentListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
 HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        where
        1=1
        <if test="initSn != null and initSn != ''">
            and init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="uid != null">
            and uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="agentId != null">
            and AGENT_ID = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="grantId != null">
            and SALESMAN_ID = #{grantId,jdbcType=INTEGER}
        </if>
        <if test="startTime != null and endTime != null">
            AND CREATE_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            AND CREATE_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        
            </select>
            <!--分页查询刷掌设备关联关系列表 pageResult-->
            <select id="findScanHandEquipmentListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.ScanHandEquipmentDTO">
                    select
        init_sn as initSn,
        uid,
        username,
        operator,
        company,
        agent_id as agentId,
        salesman_id as salesmanId,
        agent_username as agentUsername,
        salesman_username as salesmanUsername,
        create_time as createTime
        from HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        where
        1=1
        <if test="initSn != null and initSn != ''">
            and init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="uid != null">
            and uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="agentId != null">
            and AGENT_ID = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="grantId != null">
            and SALESMAN_ID = #{grantId,jdbcType=INTEGER}
        </if>
        <if test="startTime != null and endTime != null">
            AND CREATE_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            AND CREATE_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
            limit #{startRow},#{limit}
            </select>
    </mapper>
