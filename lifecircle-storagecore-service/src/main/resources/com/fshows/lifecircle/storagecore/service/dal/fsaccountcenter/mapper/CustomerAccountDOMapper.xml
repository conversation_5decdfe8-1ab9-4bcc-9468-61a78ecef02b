<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.CustomerAccountDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.CustomerAccountDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="ACTIVE" property="active" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="TOKEN_NO" property="tokenNo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_ACCOUNT_ID" property="outAccountId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_CUSTOMER_ID" property="outCustomerId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="OUT_MERCHANT_ID" property="outMerchantId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BALANCE_ACCOUNT_ID" property="balanceAccountId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_ACCOUNT_ID" property="customerAccountId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CUSTOMER_BIND_BANK_ID" property="customerBindBankId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="UNION_PAY_MERCHANT_ID" property="unionPayMerchantId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CHANNEL_TYPE" property="channelType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="WITHDRAWABLE_BALANCE" property="withdrawableBalance" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`ACTIVE`,`TOKEN_NO`,`CUSTOMER_ID`,`OUT_ACCOUNT_ID`,`OUT_CUSTOMER_ID`,`OUT_MERCHANT_ID`,`BALANCE_ACCOUNT_ID`,`CUSTOMER_ACCOUNT_ID`,`CUSTOMER_BIND_BANK_ID`,`UNION_PAY_MERCHANT_ID`,`BANK_TYPE`,`CHANNEL_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`WITHDRAWABLE_BALANCE`
        </sql>


        <!--insert:TP_CUSTOMER_ACCOUNT-->
        <insert id="insert">
            INSERT INTO TP_CUSTOMER_ACCOUNT
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="active != null">`ACTIVE`,</if>
                <if test="tokenNo != null">`TOKEN_NO`,</if>
                <if test="customerId != null">`CUSTOMER_ID`,</if>
                <if test="outAccountId != null">`OUT_ACCOUNT_ID`,</if>
                <if test="outCustomerId != null">`OUT_CUSTOMER_ID`,</if>
                <if test="outMerchantId != null">`OUT_MERCHANT_ID`,</if>
                <if test="balanceAccountId != null">`BALANCE_ACCOUNT_ID`,</if>
                <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="unionPayMerchantId != null">`UNION_PAY_MERCHANT_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="withdrawableBalance != null">`WITHDRAWABLE_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="outAccountId != null">#{outAccountId,jdbcType=VARCHAR},</if>
            <if test="outCustomerId != null">#{outCustomerId,jdbcType=VARCHAR},</if>
            <if test="outMerchantId != null">#{outMerchantId,jdbcType=VARCHAR},</if>
            <if test="balanceAccountId != null">#{balanceAccountId,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="unionPayMerchantId != null">#{unionPayMerchantId,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="withdrawableBalance != null">#{withdrawableBalance,jdbcType=DECIMAL},</if>
        </trim>
        </insert>

        <!--根据customerId得到账户信息-->
        <select id="getAccountByCustomerId" resultMap="BaseResultMap">
            select
            <include refid="Base_Column_List"/>
            from
            tp_customer_account
            where
            active = 'YES'
            and
            customer_id in
            <foreach close=")" collection="list" index="index" item="customerId" open="(" separator=",">
                #{customerId,jdbcType=VARCHAR}
            </foreach>
        </select>

        <!--根据customerId更新可提现金额-->
        <update id="updateWithdrawableBalanceByCustomerId">
            update
                tp_customer_account
            set withdrawable_balance = #{withdrawableBalance,jdbcType=DECIMAL}
            where customer_id = #{customerId,jdbcType=VARCHAR}
              and active = 'YES'
        </update>

        <!--根据客户查询生效的账户信息-->
        <select id="getActiveAccount" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM
            TP_CUSTOMER_ACCOUNT
            WHERE
            CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
            AND ACTIVE = 'YES'
            AND CHANNEL_TYPE=#{channelType,jdbcType=TINYINT}
            AND BANK_TYPE = #{bankType,jdbcType=TINYINT}
            LIMIT 1
        </select>
    </mapper>
