<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.SharePayOrderDetailDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.SharePayOrderDetailDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TASK_ID" property="taskId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_NO" property="tradeNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REFUND_SN" property="refundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_DATE" property="shareDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_ROLE" property="shareRole" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_REQ_NO" property="shareReqNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO_IN" property="merchantNoIn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_FRONT_NO" property="shareFrontNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO_OUT" property="merchantNoOut" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_REQ_NO" property="platformReqNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_MEMBER_ID" property="shareMemberId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_ORDER_STATUS" property="shareOrderStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LIQUIDATOR_REFUND_SN" property="liquidatorRefundSn" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ORIGINAL_SHARE_FRONT_NO" property="originalShareFrontNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ORDER_TYPE" property="orderType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="PLATFORM_SHARE_TIME" property="platformShareTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="SHARE_PRICE" property="sharePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="SHARE_PORTION" property="sharePortion" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REAL_SHARE_PRICE" property="realSharePrice" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="REFUNDED_AMOUNT" property="refundedAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`CARD_NO`,`REASON`,`REMARK`,`TASK_ID`,`ORDER_SN`,`TRADE_NO`,`REFUND_SN`,`SHARE_DATE`,`SHARE_ROLE`,`CUSTOMER_ID`,`SHARE_REQ_NO`,`MERCHANT_NO_IN`,`SHARE_FRONT_NO`,`MERCHANT_NO_OUT`,`PLATFORM_REQ_NO`,`SHARE_MEMBER_ID`,`SHARE_ORDER_STATUS`,`LIQUIDATOR_REFUND_SN`,`ORIGINAL_SHARE_FRONT_NO`,`UID`,`STORE_ID`,`BANK_TYPE`,`ORDER_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`PLATFORM_SHARE_TIME`,`SHARE_PRICE`,`SHARE_PORTION`,`REAL_SHARE_PRICE`,`REFUNDED_AMOUNT`
    </sql>


            <!--insert:TP_SHARE_PAY_ORDER_DETAIL-->
            <insert id="insert" >
                    INSERT INTO TP_SHARE_PAY_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="taskId != null">`TASK_ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="shareDate != null">`SHARE_DATE`,</if>
            <if test="shareRole != null">`SHARE_ROLE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="merchantNoIn != null">`MERCHANT_NO_IN`,</if>
            <if test="merchantNoOut != null">`MERCHANT_NO_OUT`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="shareOrderStatus != null">`SHARE_ORDER_STATUS`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="platformShareTime != null">`PLATFORM_SHARE_TIME`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="sharePortion != null">`SHARE_PORTION`,</if>
            <if test="realSharePrice != null">`real_share_price`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="taskId != null">#{taskId,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="shareDate != null">#{shareDate,jdbcType=VARCHAR},</if>
            <if test="shareRole != null">#{shareRole,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="merchantNoIn != null">#{merchantNoIn,jdbcType=VARCHAR},</if>
            <if test="merchantNoOut != null">#{merchantNoOut,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="shareOrderStatus != null">#{shareOrderStatus,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="platformShareTime != null">#{platformShareTime,jdbcType=TIMESTAMP},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="sharePortion != null">#{sharePortion,jdbcType=DECIMAL},</if>
            <if test="realSharePrice != null">#{realSharePrice,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--通过PlatformReqNo和MerchantNoIn,bankType更新real_share_price-->
            <update id="updateRealSharePriceByPlatformReqNoAndMerchantNoInAndBankType" >
                    update /*MS-TP-SHARE-PAY-ORDER-DETAIL-UPDATEREALSHAREPRICEBYPLATFORMREQNOANDMERCHANTNOINANDBANKTYPE*/ TP_SHARE_PAY_ORDER_DETAIL
        set real_share_price = #{realSharePrice,jdbcType=DECIMAL}
        where
        PLATFORM_REQ_NO = #{platformReqNo,jdbcType=VARCHAR}
        and
        MERCHANT_NO_in = #{merchantNoIn,jdbcType=VARCHAR}
        and
        bank_type = #{bankType,jdbcType=TINYINT}
            </update>

            <!--通过share_req_no和MerchantNoIn更新real_share_price-->
            <update id="updateRealSharePriceByShareReqNoAndMerchantNoIn" >
                    update /*MS-TP-SHARE-PAY-ORDER-DETAIL-UPDATEREALSHAREPRICEBYSHAREREQNOANDMERCHANTNOIN*/ TP_SHARE_PAY_ORDER_DETAIL
        set real_share_price = SHARE_PRICE - #{fee,jdbcType=DECIMAL}
        where
        SHARE_REQ_NO = #{shareReqNo,jdbcType=VARCHAR}
        and
        MERCHANT_NO_in = #{merchantNoIn,jdbcType=VARCHAR}
            </update>
    </mapper>
