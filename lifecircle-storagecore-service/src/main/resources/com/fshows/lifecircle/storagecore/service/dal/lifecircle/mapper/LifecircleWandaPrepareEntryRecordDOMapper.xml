<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleWandaPrepareEntryRecordDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleWandaPrepareEntryRecordDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="STORE_ID" property="storeId" jdbcType="BIGINT"
                javaType="Long"/>

        <result column="NAME" property="name" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MOBILE" property="mobile" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PLAZA_ID" property="plazaId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="WD_FLOOR" property="wdFloor" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PLAZA_NAME" property="plazaName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DETAIL_ADDRESS" property="detailAddress" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="WD_CATEGORYNAME" property="wdCategoryname" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UNITY_CAT_ID" property="unityCatId" jdbcType="SMALLINT"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`STORE_ID`,`NAME`,`MOBILE`,`PLAZA_ID`,`WD_FLOOR`,`PLAZA_NAME`,`STORE_NAME`,`DETAIL_ADDRESS`,`WD_CATEGORYNAME`,`UNITY_CAT_ID`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD-->
    <insert id="insert">
        INSERT INTO TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="storeId != null">STORE_ID,</if>
            <if test="unityCatId != null">UNITY_CAT_ID,</if>
            <if test="storeName != null">STORE_NAME,</if>
            <if test="detailAddress != null">DETAIL_ADDRESS,</if>
            <if test="wdFloor != null">WD_FLOOR,</if>
            <if test="wdCategoryname != null">WD_CATEGORYNAME,</if>
            <if test="plazaId != null">PLAZA_ID,</if>
            <if test="plazaName != null">PLAZA_NAME,</if>
            <if test="name != null">NAME,</if>
            <if test="mobile != null">MOBILE,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="detailAddress != null">#{detailAddress,jdbcType=VARCHAR},</if>
            <if test="wdFloor != null">#{wdFloor,jdbcType=VARCHAR},</if>
            <if test="wdCategoryname != null">#{wdCategoryname,jdbcType=VARCHAR},</if>
            <if test="plazaId != null">#{plazaId,jdbcType=VARCHAR},</if>
            <if test="plazaName != null">#{plazaName,jdbcType=VARCHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>
</mapper>
