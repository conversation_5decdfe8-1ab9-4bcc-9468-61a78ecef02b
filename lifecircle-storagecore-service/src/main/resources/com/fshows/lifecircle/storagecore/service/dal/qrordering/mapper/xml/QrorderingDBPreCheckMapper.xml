<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 14 as fg,'TP_QRORDERING_GOODS_CATEGORY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,STORE_ID,PRINTER_ID,CATEGORY_ID,CATEGORY_NAME,GS_CATEGORY_ID,LABEL_PRINTER_ID,PARENT_CATEGORY_ID,SORT,DEL_FLAG,OUT_MERCHANT_ID,SYSTEM_CATEGORY,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_GOODS_CATEGORY'
            AND COLUMN_NAME in('ID','STORE_ID','PRINTER_ID','CATEGORY_ID','CATEGORY_NAME','GS_CATEGORY_ID','LABEL_PRINTER_ID','PARENT_CATEGORY_ID','SORT','DEL_FLAG','OUT_MERCHANT_ID','SYSTEM_CATEGORY','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'TP_RECEIPT_CLIENTS_IMPORT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BATCH_NO,RECEIPT_ID,CLIENT_MARKERS,CLIENT_MESSAGE,IS_DEL,CREATE_TIME,UPDATE_TIME,RECEIVABLE_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_RECEIPT_CLIENTS_IMPORT'
            AND COLUMN_NAME in('ID','BATCH_NO','RECEIPT_ID','CLIENT_MARKERS','CLIENT_MESSAGE','IS_DEL','CREATE_TIME','UPDATE_TIME','RECEIVABLE_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 32 as fg,'TP_RECEIPT_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,EXT5,REMARK,ORDER_NO,PREPAY_ID,RECEIPT_ID,CUSTOMER_ID,PAY_ORDER_NO,SHARE_TOKEN_NO,CUSTOMER_PHONE,SHORT_PAY_ORDER_NO,DEL_FLAG,PAY_TIME,PAY_TYPE,STORE_ID,PAY_METHOD,PAY_STATUS,SCENE_VALUE,ORDER_SOURCE,ORDER_STATUS,REFUND_STATUS,IMPORT_CUSTOMER_ID,CREATE_TIME,UPDATE_TIME,FEE,RATE_FEE,ORDER_PRICE,ORDER_SUMPRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_RECEIPT_ORDER'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','EXT5','REMARK','ORDER_NO','PREPAY_ID','RECEIPT_ID','CUSTOMER_ID','PAY_ORDER_NO','SHARE_TOKEN_NO','CUSTOMER_PHONE','SHORT_PAY_ORDER_NO','DEL_FLAG','PAY_TIME','PAY_TYPE','STORE_ID','PAY_METHOD','PAY_STATUS','SCENE_VALUE','ORDER_SOURCE','ORDER_STATUS','REFUND_STATUS','IMPORT_CUSTOMER_ID','CREATE_TIME','UPDATE_TIME','FEE','RATE_FEE','ORDER_PRICE','ORDER_SUMPRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'FS_YOUDIAN_PLUS_MERCHANT_GRANT_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BELONG_NAME,MERCHANT_NAME,OPERATOR_NAME,OWN_RUN,DEL_FLAG,BELONG_ID,OPEN_MONTH,ACTION_TYPE,BELONG_TYPE,MERCHANT_ID,OPERATOR_ID,OPEN_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FS_YOUDIAN_PLUS_MERCHANT_GRANT_LOG'
            AND COLUMN_NAME in('ID','BELONG_NAME','MERCHANT_NAME','OPERATOR_NAME','OWN_RUN','DEL_FLAG','BELONG_ID','OPEN_MONTH','ACTION_TYPE','BELONG_TYPE','MERCHANT_ID','OPERATOR_ID','OPEN_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 16 as fg,'TP_QRORDERING_GROUP_ACTIVITY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GROUP_ACTIVITY_ID,GROUP_ACTIVITY_NAME,GROUP_ACTIVITY_CONTENT,GROUP_ACTIVITY_PICTURE,UID,DEL_FLAG,PAY_AFTER_SHOW,CAN_OUT_DELIVERY,ACTIVITY_PUSH_FLAG,DEFAULT_OUT_STORE_ID,GROUP_ACTIVITY_CLOSED,CREATE_TIME,UPDATE_TIME,GROUP_ACTIVITY_END_TIME,GROUP_ACTIVITY_START_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_GROUP_ACTIVITY'
            AND COLUMN_NAME in('ID','GROUP_ACTIVITY_ID','GROUP_ACTIVITY_NAME','GROUP_ACTIVITY_CONTENT','GROUP_ACTIVITY_PICTURE','UID','DEL_FLAG','PAY_AFTER_SHOW','CAN_OUT_DELIVERY','ACTIVITY_PUSH_FLAG','DEFAULT_OUT_STORE_ID','GROUP_ACTIVITY_CLOSED','CREATE_TIME','UPDATE_TIME','GROUP_ACTIVITY_END_TIME','GROUP_ACTIVITY_START_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 84 as fg,'TP_QRORDERING_DISH_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10,REMARK,STORE_ID,SUB_APPID,SUB_MCHID,ADDRESS_ID,CHANNEL_ID,TABLE_CODE,ACTIVITY_ID,CUSTOMER_ID,ORDER_ENTRY,OUT_STORE_ID,PAY_ORDER_NO,ACCESS_TOKEN,DISH_ORDER_NO,GROUP_FLOW_NO,MEAL_BRAND_NO,ORDER_POSTER,RECEIPT_NAME,TABLE_CODE_ID,RECEIPT_PHONE,SUB_APPSECRET,ORDER_AUTH_CODE,UPLOAD_ORDER_NO,RECEIPT_ADDRESS,APPLICATION_CODE,APPOINTMENT_TIME,BLOC_TABLE_CODE_ID,GROUP_ACTIVITY_ID,TABLE_CODE_QRCODE,APPOINTMENT_PHONE,PARENT_PAY_ORDER_NO,RECEIPT_NUMBER_PLATE,TAKE_ORDER_VALIDATE_CODE,MEAL_NO,DEL_FLAG,NFC_ORDER,PUSH_FLAG,ORDER_MODE,ORDER_TYPE,PRINT_FLAG,TAKEOUT_NO,MEAL_METHOD,REFUND_FLAG,SOURCE_TYPE,IS_TABLEWARE,ORDER_SOURCE,ORDER_STATUS,PERSON_COUNT,ACTIVITY_FLAG,DINING_METHOD,OUT_CASHIER_ID,PACKAGE_ORDER,COMPLETED_TIME,COMPLETED_TYPE,ORDER_CATEGORY,TAKE_ORDER_FLAG,DELIVERY_STATUS,TABLEWARE_COUNT,ORDER_GOODS_COUNT,DISTRIBUTION_MODE,LAST_REPORT_STATUS,FIRST_REPORT_STATUS,DELIVERY_CHANGE_TIME,CREATE_TIME,UPDATE_TIME,ORDER_PRICE,DELIVERY_FEE,REFUND_PRICE,TABLEWARE_FEE,ORDER_PACKAGE_FEE,RECEIPT_LATITUDE,RECEIPT_LONGITUDE,TABLEWARE_TOTAL_FEE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_DISH_ORDER'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','EXT5','EXT6','EXT7','EXT8','EXT9','EXT10','REMARK','STORE_ID','SUB_APPID','SUB_MCHID','ADDRESS_ID','CHANNEL_ID','TABLE_CODE','ACTIVITY_ID','CUSTOMER_ID','ORDER_ENTRY','OUT_STORE_ID','PAY_ORDER_NO','ACCESS_TOKEN','DISH_ORDER_NO','GROUP_FLOW_NO','MEAL_BRAND_NO','ORDER_POSTER','RECEIPT_NAME','TABLE_CODE_ID','RECEIPT_PHONE','SUB_APPSECRET','ORDER_AUTH_CODE','UPLOAD_ORDER_NO','RECEIPT_ADDRESS','APPLICATION_CODE','APPOINTMENT_TIME','BLOC_TABLE_CODE_ID','GROUP_ACTIVITY_ID','TABLE_CODE_QRCODE','APPOINTMENT_PHONE','PARENT_PAY_ORDER_NO','RECEIPT_NUMBER_PLATE','TAKE_ORDER_VALIDATE_CODE','MEAL_NO','DEL_FLAG','NFC_ORDER','PUSH_FLAG','ORDER_MODE','ORDER_TYPE','PRINT_FLAG','TAKEOUT_NO','MEAL_METHOD','REFUND_FLAG','SOURCE_TYPE','IS_TABLEWARE','ORDER_SOURCE','ORDER_STATUS','PERSON_COUNT','ACTIVITY_FLAG','DINING_METHOD','OUT_CASHIER_ID','PACKAGE_ORDER','COMPLETED_TIME','COMPLETED_TYPE','ORDER_CATEGORY','TAKE_ORDER_FLAG','DELIVERY_STATUS','TABLEWARE_COUNT','ORDER_GOODS_COUNT','DISTRIBUTION_MODE','LAST_REPORT_STATUS','FIRST_REPORT_STATUS','DELIVERY_CHANGE_TIME','CREATE_TIME','UPDATE_TIME','ORDER_PRICE','DELIVERY_FEE','REFUND_PRICE','TABLEWARE_FEE','ORDER_PACKAGE_FEE','RECEIPT_LATITUDE','RECEIPT_LONGITUDE','TABLEWARE_TOTAL_FEE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 30 as fg,'TP_RECEIPT_INFO' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QR_CODE,POSTER_URL,RECEIPT_ID,RECEIPT_FORM,RECEIPT_ITEM,RECEIPT_IMAGE,RECEIPT_TITLE,RECEIPT_EXPLAIN,RECEIPT_RICE_TEXT,WECHAT_POSTER_URL,DEL_FLAG,STORE_ID,SHOW_COUNT,MERCHANT_ID,NEED_UER_PAY,CREATE_PLACE,MOBILE_CHECK,RECEIPT_TYPE,INITIATOR_UID,RECEIPT_IS_LONG,RECEIPT_STATUS,RECEIPT_END_TIME,RECEIPT_START_TIME,CUSTOMER_REPEAT_PAY,RECEIPT_CREATE_TYPE,REUSE_CUSTOMER_FORM_VALUE,CREATE_TIME,UPDATE_TIME,RECEIPT_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_RECEIPT_INFO'
            AND COLUMN_NAME in('ID','QR_CODE','POSTER_URL','RECEIPT_ID','RECEIPT_FORM','RECEIPT_ITEM','RECEIPT_IMAGE','RECEIPT_TITLE','RECEIPT_EXPLAIN','RECEIPT_RICE_TEXT','WECHAT_POSTER_URL','DEL_FLAG','STORE_ID','SHOW_COUNT','MERCHANT_ID','NEED_UER_PAY','CREATE_PLACE','MOBILE_CHECK','RECEIPT_TYPE','INITIATOR_UID','RECEIPT_IS_LONG','RECEIPT_STATUS','RECEIPT_END_TIME','RECEIPT_START_TIME','CUSTOMER_REPEAT_PAY','RECEIPT_CREATE_TYPE','REUSE_CUSTOMER_FORM_VALUE','CREATE_TIME','UPDATE_TIME','RECEIPT_MONEY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'TP_RECEIPT_ORDER_EXTEND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,ORDER_NO,NICK_NAME,AVATAR_URL,FORM_VALUE,PAY_ORDER_NO,RECEIPT_FORM,DEL_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_RECEIPT_ORDER_EXTEND'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','ORDER_NO','NICK_NAME','AVATAR_URL','FORM_VALUE','PAY_ORDER_NO','RECEIPT_FORM','DEL_FLAG','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 27 as fg,'TP_QRORDERING_CUSTOMER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CITY,CODE,OPEN_ID,COUNTRY,UNION_ID,NICK_NAME,PROVINCE,SUB_APP_ID,AVATAR_URL,CUSTOMER_ID,PHONE_NUMBER,TRADE_OPEN_ID,HAVE_CERTIFIED,MEMBER_V2_OPEN_ID,GENDER,DEL_FLAG,AUTH_STATUS,MERCHANT_ID,CODE_INVALID,CUSTOMER_TYPE,MEMBER_USER_ID,CUSTOMER_SOURCE,INDEX_BARCODE_FRAME,AUTH_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_CUSTOMER'
            AND COLUMN_NAME in('ID','CITY','CODE','OPEN_ID','COUNTRY','UNION_ID','NICK_NAME','PROVINCE','SUB_APP_ID','AVATAR_URL','CUSTOMER_ID','PHONE_NUMBER','TRADE_OPEN_ID','HAVE_CERTIFIED','MEMBER_V2_OPEN_ID','GENDER','DEL_FLAG','AUTH_STATUS','MERCHANT_ID','CODE_INVALID','CUSTOMER_TYPE','MEMBER_USER_ID','CUSTOMER_SOURCE','INDEX_BARCODE_FRAME','AUTH_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 49 as fg,'TP_QRORDERING_PAY_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10,STORE_ID,PREPAY_ID,REQUEST_ID,CUSTOMER_ID,OUT_STORE_ID,PAY_ORDER_NO,CALL_BACK_URL,DISH_ORDER_NO,MEMBER_CARD_NO,PAY_CHANNEL_ID,OUT_MERCHANT_ID,PARENT_PAY_ORDER_NO,MEMBER_RIGHT_CARD_NO,MODE,TYPE,DEL_FLAG,PAY_TIME,PAY_TYPE,ORDER_TYPE,PAY_STATUS,ACTIVITY_FLAG,OUT_CASHIER_ID,CREATE_TIME,UPDATE_TIME,FEE,RATE_FEE,DISCOUNT,ORDER_PRICE,RED_PACKETS,COUPON_MONEY,REFUND_PRICE,ORDER_SUMPRICE,PAYABLE_AMOUNT,PLATFORM_DISCOUNT,TOTAL_DISCOUNT_AMOUNT,MEMBER_DISCOUNT_AMOUNT,ACTIVITY_DISCOUNT_AMOUNT,MEMBER_POINTS_DISCOUNT_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_PAY_ORDER'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','EXT5','EXT6','EXT7','EXT8','EXT9','EXT10','STORE_ID','PREPAY_ID','REQUEST_ID','CUSTOMER_ID','OUT_STORE_ID','PAY_ORDER_NO','CALL_BACK_URL','DISH_ORDER_NO','MEMBER_CARD_NO','PAY_CHANNEL_ID','OUT_MERCHANT_ID','PARENT_PAY_ORDER_NO','MEMBER_RIGHT_CARD_NO','MODE','TYPE','DEL_FLAG','PAY_TIME','PAY_TYPE','ORDER_TYPE','PAY_STATUS','ACTIVITY_FLAG','OUT_CASHIER_ID','CREATE_TIME','UPDATE_TIME','FEE','RATE_FEE','DISCOUNT','ORDER_PRICE','RED_PACKETS','COUPON_MONEY','REFUND_PRICE','ORDER_SUMPRICE','PAYABLE_AMOUNT','PLATFORM_DISCOUNT','TOTAL_DISCOUNT_AMOUNT','MEMBER_DISCOUNT_AMOUNT','ACTIVITY_DISCOUNT_AMOUNT','MEMBER_POINTS_DISCOUNT_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 15 as fg,'TP_QRORDERING_CUSTOMER_GOODS_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GOODS_ID,STORE_ID,GOODS_NAME,ACTIVITY_ID,CUSTOMER_ID,OUT_STORE_ID,PAY_ORDER_NO,ACTIVITY_GOODS_ID,DEL_FLAG,PAY_STATUS,SALE_NUMBER,ACTIVITY_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_CUSTOMER_GOODS_LOG'
            AND COLUMN_NAME in('ID','GOODS_ID','STORE_ID','GOODS_NAME','ACTIVITY_ID','CUSTOMER_ID','OUT_STORE_ID','PAY_ORDER_NO','ACTIVITY_GOODS_ID','DEL_FLAG','PAY_STATUS','SALE_NUMBER','ACTIVITY_FLAG','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'TP_QRORDERING_DISH_ORDER_EXTEND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,EXT2,EXT3,EXT4,EXT5,EXT6,EXT7,EXT8,EXT9,EXT10,DISH_LIST,PAY_ORDER_NO,DISH_ORDER_NO,ACTIVITY_DETAIL,UPLOAD_DISH_LIST,ACTIVITY_DISH_LIST,DEL_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_DISH_ORDER_EXTEND'
            AND COLUMN_NAME in('ID','EXT1','EXT2','EXT3','EXT4','EXT5','EXT6','EXT7','EXT8','EXT9','EXT10','DISH_LIST','PAY_ORDER_NO','DISH_ORDER_NO','ACTIVITY_DETAIL','UPLOAD_DISH_LIST','ACTIVITY_DISH_LIST','DEL_FLAG','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'TP_QRORDERING_TABLE_CODE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QRCODE,STORE_ID,BIND_FAIL,TABLE_CODE,BIND_FAIL_MSG,TABLE_CODE_ID,TABLE_CODE_URL,PARENT_TABLE_ID,DEL_FLAG,BIND_TIME,BIND_STATUS,QRCODE_TYPE,BIND_STATUS_NFC,TABLE_CODE_DISPLAY,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_TABLE_CODE'
            AND COLUMN_NAME in('ID','QRCODE','STORE_ID','BIND_FAIL','TABLE_CODE','BIND_FAIL_MSG','TABLE_CODE_ID','TABLE_CODE_URL','PARENT_TABLE_ID','DEL_FLAG','BIND_TIME','BIND_STATUS','QRCODE_TYPE','BIND_STATUS_NFC','TABLE_CODE_DISPLAY','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 58 as fg,'TP_GAS_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,PID,TOKEN,BILL_NO,CARD_NO,INST_NO,USER_ID,ADDRESS,BILL_KEY,BIZ_TYPE,ORDER_SN,BILL_DATE,CARD_INFO,CARD_TYPE,DEVICE_SN,OWNER_NAME,REQUEST_ID,CHARGE_INST,PRICE_LEVEL,SUB_BIZ_TYPE,SUPPLIER_ID,EXTEND_FIELD,FACTORY_CODE,FUBEI_ORDER_SN,SUPPLIER_NAME,FUBEI_REFUND_SN,SHORT_DEVICE_SN,ORGANIZATION_NO,USER_IDENTITY_CODE,DEL_FLAG,PAY_TIME,PAY_TYPE,STORE_ID,CODE_TYPE,PAY_STATUS,TRADE_DATE,IC_CARD_TYPE,MERCHANT_ID,PURCHASE_ID,REFUND_TIME,CHARGE_MODEL,WRITE_STATUS,REFUND_STATUS,WRITE_OFF_STATUS,CREATE_TIME,UPDATE_TIME,RATE,PRICE,BALANCE,CARD_VAL,RATE_FEE,PAY_AMOUNT,FINE_AMOUNT,ALLOW_AMOUNT,LADDER_AMOUNT,RECEIPT_AMOUNT,RECHARGE_VALUE,RECHARGE_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_GAS_ORDER'
            AND COLUMN_NAME in('ID','PID','TOKEN','BILL_NO','CARD_NO','INST_NO','USER_ID','ADDRESS','BILL_KEY','BIZ_TYPE','ORDER_SN','BILL_DATE','CARD_INFO','CARD_TYPE','DEVICE_SN','OWNER_NAME','REQUEST_ID','CHARGE_INST','PRICE_LEVEL','SUB_BIZ_TYPE','SUPPLIER_ID','EXTEND_FIELD','FACTORY_CODE','FUBEI_ORDER_SN','SUPPLIER_NAME','FUBEI_REFUND_SN','SHORT_DEVICE_SN','ORGANIZATION_NO','USER_IDENTITY_CODE','DEL_FLAG','PAY_TIME','PAY_TYPE','STORE_ID','CODE_TYPE','PAY_STATUS','TRADE_DATE','IC_CARD_TYPE','MERCHANT_ID','PURCHASE_ID','REFUND_TIME','CHARGE_MODEL','WRITE_STATUS','REFUND_STATUS','WRITE_OFF_STATUS','CREATE_TIME','UPDATE_TIME','RATE','PRICE','BALANCE','CARD_VAL','RATE_FEE','PAY_AMOUNT','FINE_AMOUNT','ALLOW_AMOUNT','LADDER_AMOUNT','RECEIPT_AMOUNT','RECHARGE_VALUE','RECHARGE_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 14 as fg,'TP_QRORDERING_CUSTOMER_ADDRESS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,LATITUDE,LONGITUDE,ADDRESS_ID,CUSTOMER_ID,NUMBER_PLATE,RECEIPT_NAME,RECEIPT_PHONE,RECEIPT_ADDRESS,SEX,DEL_FLAG,USE_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_CUSTOMER_ADDRESS'
            AND COLUMN_NAME in('ID','LATITUDE','LONGITUDE','ADDRESS_ID','CUSTOMER_ID','NUMBER_PLATE','RECEIPT_NAME','RECEIPT_PHONE','RECEIPT_ADDRESS','SEX','DEL_FLAG','USE_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 50 as fg,'TP_QRORDERING_STORE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,STORE_LAT,STORE_LNG,TEL,CITY,NOTE,COUNTY,ADDRESS,END_TIME,SERVICE,STORE_ID,PROVINCE,BRAND_NAME,RECOMMEND,START_TIME,STORE_AREA,STORE_LOGO,STORE_NAME,BRANCH_NAME,STORE_BRIEF,STORE_IMAGE,MINI_APP_CODE,SERVICE_MORE,LICENSE_NUMBER,TAKE_ORDER_RULE,APPOINTMENT_END_TIME,APPOINTMENT_START_TIME,KA,IS_SHOW,DEL_FLAG,AVG_PRICE,IS_ONLINE,ORDER_MODE,MEAL_METHOD,OUT_STORE_ID,UNITY_CAT_ID,JOIN_CHANNEL,DINING_METHOD,PICK_UP_SWITCH,NEED_TABLEWARE,OUT_MERCHANT_ID,DISPLAY_COMMENT,SINGLE_PRINT_CUT,TAKE_ORDER_METHOD,ORDER_REMIND_SWITCH,DEFAULT_DINING_METHOD,CUSTOM_PRODUCT_SHOW_MODE,CREATE_TIME,UPDATE_TIME,TABLEWARE_FEE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_STORE'
            AND COLUMN_NAME in('ID','STORE_LAT','STORE_LNG','TEL','CITY','NOTE','COUNTY','ADDRESS','END_TIME','SERVICE','STORE_ID','PROVINCE','BRAND_NAME','RECOMMEND','START_TIME','STORE_AREA','STORE_LOGO','STORE_NAME','BRANCH_NAME','STORE_BRIEF','STORE_IMAGE','MINI_APP_CODE','SERVICE_MORE','LICENSE_NUMBER','TAKE_ORDER_RULE','APPOINTMENT_END_TIME','APPOINTMENT_START_TIME','KA','IS_SHOW','DEL_FLAG','AVG_PRICE','IS_ONLINE','ORDER_MODE','MEAL_METHOD','OUT_STORE_ID','UNITY_CAT_ID','JOIN_CHANNEL','DINING_METHOD','PICK_UP_SWITCH','NEED_TABLEWARE','OUT_MERCHANT_ID','DISPLAY_COMMENT','SINGLE_PRINT_CUT','TAKE_ORDER_METHOD','ORDER_REMIND_SWITCH','DEFAULT_DINING_METHOD','CUSTOM_PRODUCT_SHOW_MODE','CREATE_TIME','UPDATE_TIME','TABLEWARE_FEE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 24 as fg,'TP_QRORDERING_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,UNIT,GOODS_ID,GS_SPU_ID,LABEL_ID,PICTURE,STORE_ID,CATEGORY_ID,DESCRIPTION,PICTURE_EXTEND,GOODS_TEMPLATE_ID,SORT,STATUS,DEL_FLAG,HOT_SALE,STOCK_NUM,LEAST_QUANTITY,RESET_STOCK_NUM,RESET_STOCK_FLAG,CREATE_TIME,UPDATE_TIME,PRICE,ORIGINAL_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_GOODS'
            AND COLUMN_NAME in('ID','NAME','UNIT','GOODS_ID','GS_SPU_ID','LABEL_ID','PICTURE','STORE_ID','CATEGORY_ID','DESCRIPTION','PICTURE_EXTEND','GOODS_TEMPLATE_ID','SORT','STATUS','DEL_FLAG','HOT_SALE','STOCK_NUM','LEAST_QUANTITY','RESET_STOCK_NUM','RESET_STOCK_FLAG','CREATE_TIME','UPDATE_TIME','PRICE','ORIGINAL_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'TP_QRORDERING_BASIC_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,IMAGE,SOURCE,GOODS_ID,DEL_FLAG,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_BASIC_GOODS'
            AND COLUMN_NAME in('ID','NAME','IMAGE','SOURCE','GOODS_ID','DEL_FLAG','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 6 as fg,'TP_QRORDERING_QRCODE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QRCODE,DEL_FLAG,QRCODE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_QRCODE'
            AND COLUMN_NAME in('ID','QRCODE','DEL_FLAG','QRCODE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 17 as fg,'TP_QRORDERING_ACTIVITY_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,UNIT,SPU_ID,PICTURE,GOODS_NAME,ACTIVITY_ID,OUT_STORE_ID,DESCRIPTION,ONLINE_SPU_ID,ACTIVITY_GOODS_ID,UID,DEL_FLAG,PURCHASE_LIMIT,CREATE_TIME,UPDATE_TIME,ACTIVITY_PRICE,ORIGINAL_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_ACTIVITY_GOODS'
            AND COLUMN_NAME in('ID','UNIT','SPU_ID','PICTURE','GOODS_NAME','ACTIVITY_ID','OUT_STORE_ID','DESCRIPTION','ONLINE_SPU_ID','ACTIVITY_GOODS_ID','UID','DEL_FLAG','PURCHASE_LIMIT','CREATE_TIME','UPDATE_TIME','ACTIVITY_PRICE','ORIGINAL_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 20 as fg,'TP_QRORDERING_GROUP_ACTIVITY_GOODS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CODE,UNIT,SPU_ID,PICTURE,GOODS_NAME,CATEGORY_ID,ONLINE_SPU_ID,CATEGORY_NAME,GROUP_ACTIVITY_ID,GROUP_ACTIVITY_GOODS_ID,UID,DEL_FLAG,LEFT_STOCK,OUT_STORE_ID,PURCHASE_LIMIT,CREATE_TIME,UPDATE_TIME,ACTIVITY_PRICE,ORIGINAL_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_QRORDERING_GROUP_ACTIVITY_GOODS'
            AND COLUMN_NAME in('ID','CODE','UNIT','SPU_ID','PICTURE','GOODS_NAME','CATEGORY_ID','ONLINE_SPU_ID','CATEGORY_NAME','GROUP_ACTIVITY_ID','GROUP_ACTIVITY_GOODS_ID','UID','DEL_FLAG','LEFT_STOCK','OUT_STORE_ID','PURCHASE_LIMIT','CREATE_TIME','UPDATE_TIME','ACTIVITY_PRICE','ORIGINAL_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 27 as fg,'TP_RECEIPT_REFUND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,ORDER_NO,REFUND_SN,RECEIPT_ID,PAY_ORDER_NO,RECEIPT_FORM,ERROR_MESSAGE,RECEIPT_TITLE,REFUND_BATCH_NO,SHORT_REFUND_SN,MERCHANT_ORDER_SN,RECEIPT_REFUND_SN,SHORT_PAY_ORDER_NO,IS_DEL,HANDLER,PAY_TYPE,STORE_ID,CASHIER_ID,IS_PART_REFUND,REFUND_SOURCE,REFUND_STATUS,CREATE_TIME,UPDATE_TIME,POUNDAGE,ORDER_PRICE,REFUND_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_RECEIPT_REFUND'
            AND COLUMN_NAME in('ID','REMARK','ORDER_NO','REFUND_SN','RECEIPT_ID','PAY_ORDER_NO','RECEIPT_FORM','ERROR_MESSAGE','RECEIPT_TITLE','REFUND_BATCH_NO','SHORT_REFUND_SN','MERCHANT_ORDER_SN','RECEIPT_REFUND_SN','SHORT_PAY_ORDER_NO','IS_DEL','HANDLER','PAY_TYPE','STORE_ID','CASHIER_ID','IS_PART_REFUND','REFUND_SOURCE','REFUND_STATUS','CREATE_TIME','UPDATE_TIME','POUNDAGE','ORDER_PRICE','REFUND_MONEY')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
