<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardSpuDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardSpuDO">
    <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="USE_DESC" property="useDesc" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="COVER_URL" property="coverUrl" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OPERATE_ID" property="operateId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CARD_SPU_NAME" property="cardSpuName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="OPERATE_NAME" property="operateName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SPU_STATUS" property="spuStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="IS_MINA_SALES" property="isMinaSales" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CARD_SHAPE_TYPE" property="cardShapeType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


    <sql id="Base_Column_List">
    `ID`,`USE_DESC`,`COVER_URL`,`CARD_SPU_ID`,`OPERATE_ID`,`CARD_SPU_NAME`,`OPERATE_NAME`,`IS_DEL`,`SPU_STATUS`,`IS_MINA_SALES`,`CARD_SHAPE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_PREPAY_CARD_SPU-->
    <insert id="insert">
        INSERT INTO TP_PREPAY_CARD_SPU
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="useDesc != null">`USE_DESC`,</if>
            <if test="coverUrl != null">`COVER_URL`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="spuStatus != null">`SPU_STATUS`,</if>
            <if test="isMinaSales != null">`IS_MINA_SALES`,</if>
            <if test="cardShapeType != null">`CARD_SHAPE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="useDesc != null">#{useDesc,jdbcType=VARCHAR},</if>
            <if test="coverUrl != null">#{coverUrl,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="spuStatus != null">#{spuStatus,jdbcType=TINYINT},</if>
            <if test="isMinaSales != null">#{isMinaSales,jdbcType=TINYINT},</if>
            <if test="cardShapeType != null">#{cardShapeType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--根据skuId查询spu信息-->
    <select id="getSpuBySkuId" resultMap="BaseResultMap">
                    select
        spu.*
        from
        `tp_prepay_card_sku` sku
        LEFT JOIN `tp_prepay_card_spu` spu on sku.`card_spu_id` = spu.`card_spu_id`
        where sku.`card_sku_id` = #{cardSkuId,jdbcType=VARCHAR}
        and spu.is_del = 0
        limit 1
            </select>

    <!--根据spuId集合查询spu集合-->
    <select id="findBySpuIdList" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tp_prepay_card_spu
        where
        is_del = 0
        and card_spu_id in
        <foreach collection="list" item="spuId" open="(" separator="," close=")">
            #{spuId,jdbcType=VARCHAR}
        </foreach>
    </select>
</mapper>
