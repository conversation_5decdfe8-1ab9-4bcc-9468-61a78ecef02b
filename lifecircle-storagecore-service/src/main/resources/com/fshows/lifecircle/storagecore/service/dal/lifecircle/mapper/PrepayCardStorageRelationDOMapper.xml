<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.PrepayCardStorageRelationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.PrepayCardStorageRelationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SKU_ID" property="cardSkuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_SPU_ID" property="cardSpuId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`CARD_NO`,`CARD_SKU_ID`,`CARD_SPU_ID`,`STORAGE_ORDER`,`IS_DEL`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_PREPAY_CARD_STORAGE_RELATION-->
            <insert id="insert" >
                    INSERT INTO TP_PREPAY_CARD_STORAGE_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--批量插入出入库关联信息-->
            <insert id="insertStorageRelationBatch" >
                    INSERT INTO
        tp_prepay_card_storage_relation (
        storage_order,
        card_spu_id,
        card_sku_id,
        card_no
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.storageOrder,jdbcType=VARCHAR},
            #{item.cardSpuId,jdbcType=VARCHAR},
            #{item.cardSkuId,jdbcType=VARCHAR},
            #{item.cardNo,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>
    </mapper>
