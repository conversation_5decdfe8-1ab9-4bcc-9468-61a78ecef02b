<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.mapper.ShareGroupDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.ShareGroupDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVE" property="active" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REASON" property="reason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GROUP_ID" property="groupId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_MODE" property="shareMode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHARE_TYPE" property="shareType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_TYPE" property="settleType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_TYPE" property="sourceType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GROUP_STATUS" property="groupStatus" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROTOCOL_PIC" property="protocolPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_REASON" property="importReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IMPORT_STATUS" property="importStatus" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="SHARE_GROUP_ID" property="shareGroupId" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLE_CYCLE_END" property="settleCycleEnd" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SHARE_GROUP_NAME" property="shareGroupName" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="MERCHANT_ORDER_SN" property="merchantOrderSn" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="REQ_SERIAL_NUMBER" property="reqSerialNumber" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="SETTLE_CYCLE_START" property="settleCycleStart" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UID" property="uid" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="PASS_NUM" property="passNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_TYPE" property="channelType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHARE_MEMBER_NUM" property="shareMemberNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`ACTIVE`,`REASON`,`GROUP_ID`,`SHARE_MODE`,`SHARE_TYPE`,`SETTLE_TYPE`,`SOURCE_TYPE`,`GROUP_STATUS`,`PROTOCOL_PIC`,`IMPORT_REASON`,`IMPORT_STATUS`,`SHARE_GROUP_ID`,`SETTLE_CYCLE_END`,`SHARE_GROUP_NAME`,`MERCHANT_ORDER_SN`,`REQ_SERIAL_NUMBER`,`SETTLE_CYCLE_START`,`UID`,`PASS_NUM`,`BANK_TYPE`,`CHANNEL_TYPE`,`SHARE_MEMBER_NUM`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_SHARE_GROUP-->
            <insert id="insert" >
                    INSERT INTO TP_SHARE_GROUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="settleType != null">`SETTLE_TYPE`,</if>
            <if test="settleCycleStart != null">`SETTLE_CYCLE_START`,</if>
            <if test="settleCycleEnd != null">`SETTLE_CYCLE_END`,</if>
            <if test="shareMode != null">`SHARE_MODE`,</if>
            <if test="shareType != null">`SHARE_TYPE`,</if>
            <if test="reqSerialNumber != null">`REQ_SERIAL_NUMBER`,</if>
            <if test="shareGroupName != null">`SHARE_GROUP_NAME`,</if>
            <if test="shareGroupId != null">`SHARE_GROUP_ID`,</if>
            <if test="shareMemberNum != null">`SHARE_MEMBER_NUM`,</if>
            <if test="passNum != null">`PASS_NUM`,</if>
            <if test="groupStatus != null">`GROUP_STATUS`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="importStatus != null">`IMPORT_STATUS`,</if>
            <if test="importReason != null">`IMPORT_REASON`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="settleType != null">#{settleType,jdbcType=VARCHAR},</if>
            <if test="settleCycleStart != null">#{settleCycleStart,jdbcType=VARCHAR},</if>
            <if test="settleCycleEnd != null">#{settleCycleEnd,jdbcType=VARCHAR},</if>
            <if test="shareMode != null">#{shareMode,jdbcType=VARCHAR},</if>
            <if test="shareType != null">#{shareType,jdbcType=VARCHAR},</if>
            <if test="reqSerialNumber != null">#{reqSerialNumber,jdbcType=VARCHAR},</if>
            <if test="shareGroupName != null">#{shareGroupName,jdbcType=VARCHAR},</if>
            <if test="shareGroupId != null">#{shareGroupId,jdbcType=VARCHAR},</if>
            <if test="shareMemberNum != null">#{shareMemberNum,jdbcType=INTEGER},</if>
            <if test="passNum != null">#{passNum,jdbcType=INTEGER},</if>
            <if test="groupStatus != null">#{groupStatus,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="importStatus != null">#{importStatus,jdbcType=VARCHAR},</if>
            <if test="importReason != null">#{importReason,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据Uid查询生效的分账组-->
            <select id="getByUid" resultMap="BaseResultMap">
                    SELECT /*MS-TP-SHARE-GROUP-GETBYUID*/  <include refid="Base_Column_List" />
        FROM
        tp_share_group
        WHERE uid = #{uid,jdbcType=INTEGER}
        AND bank_type = #{bankType,jdbcType=TINYINT}
        AND active = 'YES'
        limit 1
            </select>

            <!--批量插入-->
            <insert id="insertBatch" >
                INSERT INTO tp_share_group
                (
                group_id,channel_type,bank_type,token,uid,settle_type,req_serial_number,
                share_group_name,share_member_num,pass_num,group_status,active,import_status,
                import_reason,settle_cycle_start,settle_cycle_end,share_mode,share_type,protocol_pic,
                merchant_order_sn,source_type
                )
                VALUES
                <foreach collection="list" item="item" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=TINYINT},
            #{item.bankType,jdbcType=TINYINT},
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.settleType,jdbcType=VARCHAR},
            #{item.reqSerialNumber,jdbcType=VARCHAR},
            #{item.shareGroupName,jdbcType=VARCHAR},
            #{item.shareMemberNum,jdbcType=INTEGER},
            #{item.passNum,jdbcType=INTEGER},
            #{item.groupStatus,jdbcType=VARCHAR},
            #{item.active,jdbcType=VARCHAR},
            #{item.importStatus,jdbcType=VARCHAR},
            #{item.importReason,jdbcType=VARCHAR},
            #{item.settleCycleStart,jdbcType=VARCHAR},
            #{item.settleCycleEnd,jdbcType=VARCHAR},
            #{item.shareMode,jdbcType=VARCHAR},
            #{item.shareType,jdbcType=VARCHAR},
            #{item.protocolPic,jdbcType=VARCHAR},
            #{item.merchantOrderSn,jdbcType=VARCHAR},
            #{item.sourceType,jdbcType=VARCHAR}
            )
        </foreach>
            </insert>

            <!--更新成员数量-->
            <update id="updateNumByGroupId" >
                    UPDATE /*MS-TP-SHARE-GROUP-UPDATENUMBYGROUPID*/ tp_share_group
        SET
        share_member_num = share_member_num + #{shareMemberNum,jdbcType=INTEGER},
        pass_num = pass_num + #{passNum,jdbcType=INTEGER}
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
            </update>

            <!--通过 token、bank_type、channle_type 获得分账组 ID-->
            <select id="getShareGroupIdByTokenAndBankTypeAndChannelType" resultType="String">
                    SELECT
        GROUP_ID
        FROM
        TP_SHARE_GROUP
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND bank_type = #{bankType,jdbcType=TINYINT}
          AND channel_type = #{channelType,jdbcType=TINYINT}
          AND active = 'YES'
          AND group_status = 'SUCCESS'
                    LIMIT 1
            </select>

    <!--根据groupId查询分账组-->
    <select id="getShareGroupByGroupId" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tp_share_group
        where
        group_id = #{groupId,jdbcType=VARCHAR}
        limit 1
    </select>

    <!--根据merchantOrderSn查询分账组-->
    <select id="getShareGroupByMerchantOrderSn" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from
        tp_share_group
        where
        merchant_order_sn = #{merchantOrderSn,jdbcType=VARCHAR}
        limit 1
    </select>
</mapper>
