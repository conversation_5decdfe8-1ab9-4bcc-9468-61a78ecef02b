<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.mapper.TradeStoreWeekDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.lifecircleoperation.dataobject.TradeStoreWeekDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PT_WEEK" property="ptWeek" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="TRADE_WEEK" property="tradeWeek" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="TRADE_MONTH" property="tradeMonth" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`PT_WEEK`,`STORE_ID`,`TRADE_WEEK`,`TRADE_MONTH`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_TRADE_STORE_WEEK-->
    <insert id="insert">
        INSERT INTO TP_TRADE_STORE_WEEK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="ptWeek != null">`PT_WEEK`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="tradeWeek != null">`TRADE_WEEK`,</if>
            <if test="tradeMonth != null">`TRADE_MONTH`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="ptWeek != null">#{ptWeek,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="tradeWeek != null">#{tradeWeek,jdbcType=TINYINT},</if>
            <if test="tradeMonth != null">#{tradeMonth,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--获取交易商户Token总数-->
    <select id="getTradeWeekTokenCount" resultType="java.lang.Integer">
                    SELECT /*MS-TP-TRADE-STORE-WEEK-GETTRADEWEEKTOKENCOUNT*/  COUNT(DISTINCT token) FROM TP_TRADE_STORE_WEEK
        WHERE trade_month = #{tradeMonth,jdbcType=INTEGER} AND trade_week = #{tradeWeek,jdbcType=INTEGER}
            </select>

    <!--获取交易商户Token总数-->
    <select id="getTradeWeekTokenListByPage" resultType="java.lang.String">
                    SELECT /*MS-TP-TRADE-STORE-WEEK-GETTRADEWEEKTOKENLISTBYPAGE*/  DISTINCT token FROM TP_TRADE_STORE_WEEK
        WHERE trade_month = #{tradeMonth,jdbcType=INTEGER} AND trade_week = #{tradeWeek,jdbcType=INTEGER}
        ORDER BY token
        LIMIT #{startRow,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
            </select>
</mapper>
