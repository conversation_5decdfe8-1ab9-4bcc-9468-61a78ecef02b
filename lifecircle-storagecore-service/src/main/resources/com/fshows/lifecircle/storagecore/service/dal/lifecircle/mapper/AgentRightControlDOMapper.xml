<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.AgentRightControlDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.AgentRightControlDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

    <result column="FEE_DECIMAL_SCALE" property="feeDecimalScale" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="KEY_ID" property="keyId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_QQPAY" property="isQqpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_YIPAY" property="isYipay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZERO_FEE" property="zeroFee" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PRE_PAY" property="isPrePay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_TYPE" property="agentType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_H5PAY_WX" property="isH5payWx" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_OPENAPI" property="isOpenapi" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_SWITCH" property="nfcSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZF_END_TIME" property="zfEndTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTO_INCOME" property="autoIncome" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COST_DIFFER" property="costDiffer" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DCEP_SWITCH" property="dcepSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_BD_FREEZE" property="isBdFreeze" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_FAST_AUTH" property="isFastAuth" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_UNIONPAY" property="isUnionpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MODIFY_TIME" property="modifyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USE_THEN_PAY" property="useThenPay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ENABLE_LEVEL" property="enableLevel" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="HUIKE_SWITCH" property="huikeSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_FUBEI_LOAN" property="isFubeiLoan" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_HURRICANE" property="isHurricane" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MINA_BOOT_NUM" property="minaBootNum" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_GMV_GROUP" property="subGmvGroup" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ZF_START_TIME" property="zfStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_VERSION" property="agentVersion" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="GRANT_UPGRADE" property="grantUpgrade" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_POS_CARD_FEE" property="isPosCardFee" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SELF_FEE_SET" property="isSelfFeeSet" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_WECHAT_CITY" property="isWechatCity" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DRAGONFLY_OPEN" property="dragonflyOpen" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="HURRICANE_TIME" property="hurricaneTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DIRECT_SALES" property="isDirectSales" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_STAGING_OPEN" property="isStagingOpen" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MCH_SHARE_RIGHT" property="mchShareRight" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OLD_COST_DIFFER" property="oldCostDiffer" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_GMV_GROUP" property="superGmvGroup" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ALIPAY_DIRECT" property="isAlipayDirect" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_KOUBEI_ACCESS" property="isKoubeiAccess" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_WECHAT_DIRECT" property="isWechatDirect" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LOGIN_SMS_VERIFY" property="loginSmsVerify" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_OFFLINE_SALE" property="nfcOfflineSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ZERO_BUY_SALE" property="nfcZeroBuySale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="T1_SETTLE_SWITCH" property="t1SettleSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_TOUCH_TASK" property="alipayTouchTask" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_YZT_SWITCH" property="alipayYztSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ONLY_ATTENTION" property="isOnlyAttention" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_SUPER_SALESMAN" property="isSuperSalesman" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_REGION_SWITCH" property="nfcRegionSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_SESAME_GO_SALE" property="nfcSesameGoSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="OPEN_ZERO_FEE_TIME" property="openZeroFeeTime" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="OPERATION_CENTER" property="operationCenter" jdbcType="TINYINT"
            javaType="Integer"/>

            <result column="QUICK_CASH_SWITCH" property="quickCashSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STAGING_OPEN_TIME" property="stagingOpenTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UNBIND_SMS_VERIFY" property="unbindSmsVerify" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLOSE_ZERO_FEE_TIME" property="closeZeroFeeTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CUSTOMIZED_MINA" property="isCustomizedMina" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_NFC_REGION_AGENT" property="isNfcRegionAgent" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_OPEN_AFTER_PAYAD" property="isOpenAfterPayad" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MAX_NUMBER_OF_GRANT" property="maxNumberOfGrant" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ONE_LEVEL_GMV_GROUP" property="oneLevelGmvGroup" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PREPAY_CARD_SWITCH" property="prepayCardSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUPER_SALESMAN_NUM" property="superSalesmanNum" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="T1_SETTLE_BEGINDAY" property="t1SettleBeginday" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_LEVEL_VERSION" property="agentLevelVersion" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COEFFICIENT_DIFFER" property="coefficientDiffer" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="EASY_PAY_D0_WITHDRAW" property="easyPayD0Withdraw" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_IS_OPENAPI" property="merchantIsOpenapi" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ONLINE_CLUE_SALE" property="nfcOnlineClueSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ADVERTISING_RIGHT" property="isAdvertisingRight" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_CONFIG_OWN_CHANNEL" property="isConfigOwnChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_QUICK_PUSH_SERVICE" property="isQuickPushService" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRE_INVOICING_SWITCH" property="preInvoicingSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_REGIONAL_AGENT" property="alipayRegionalAgent" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COST_DIFFER_START_TIME" property="costDifferStartTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="COUPON_PAY_COMMISSION" property="couponPayCommission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MIN_NORMAL_COMMISSION" property="minNormalCommission" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_BUYOUT_SALE" property="nfcAlipayBuyoutSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_MAX_NUMBER_OF_GRANT" property="nfcMaxNumberOfGrant" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MAX_NUMBER_OF_EMPLOYEES" property="maxNumberOfEmployees" jdbcType="INTEGER"
        javaType="Integer"/>

    <result column="EXIST_INDIVIDUAL_SETTLE" property="existIndividualSettle" jdbcType="TINYINT"
            javaType="Integer"/>

            <result column="NFC_PAY_RETURN_LATER_SALE" property="nfcPayReturnLaterSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ALIPAY_PREPAY_CARD_SWITCH" property="alipayPrepayCardSwitch" jdbcType="TINYINT"
        javaType="Integer"/>

    <result column="CHANGE_VERSION_START_TIME" property="changeVersionStartTime" jdbcType="INTEGER"
            javaType="Integer"/>

            <result column="CHANNEL_VALUE_ADDED_AGENT" property="channelValueAddedAgent" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="REFUND_HANDLE_PERMISSION" property="refundHandlePermission" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_HANDLE_PERMISSION" property="settleHandlePermission" jdbcType="TINYINT"
        javaType="Integer"/>

    <result column="INDIVIDUAL_SETTLE_SHOW_SWITCH" property="individualSettleShowSwitch" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="PHP_AGENT_BACKEND_PERMISSIONS" property="phpAgentBackendPermissions" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_PAY_RETURN_LATER_SALE" property="nfcAlipayPayReturnLaterSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="NFC_ALIPAY_ZERO_ENJOY_FIRST_SALE" property="nfcAlipayZeroEnjoyFirstSale" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="MAX_COST_FEE" property="maxCostFee" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="COMMISSION_APPLY_AMOUNT" property="commissionApplyAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`FEE_DECIMAL_SCALE`,`KEY_ID`,`IS_QQPAY`,`IS_YIPAY`,`ZERO_FEE`,`IS_PRE_PAY`,`AGENT_TYPE`,`IS_H5PAY_WX`,`IS_OPENAPI`,`NFC_SWITCH`,`ZF_END_TIME`,`AUTO_INCOME`,`COST_DIFFER`,`CREATE_TIME`,`DCEP_SWITCH`,`IS_BD_FREEZE`,`IS_FAST_AUTH`,`IS_UNIONPAY`,`MODIFY_TIME`,`USE_THEN_PAY`,`ENABLE_LEVEL`,`HUIKE_SWITCH`,`IS_FUBEI_LOAN`,`IS_HURRICANE`,`MINA_BOOT_NUM`,`SUB_GMV_GROUP`,`ZF_START_TIME`,`AGENT_VERSION`,`GRANT_UPGRADE`,`IS_POS_CARD_FEE`,`IS_SELF_FEE_SET`,`IS_WECHAT_CITY`,`DRAGONFLY_OPEN`,`HURRICANE_TIME`,`IS_DIRECT_SALES`,`IS_STAGING_OPEN`,`MCH_SHARE_RIGHT`,`OLD_COST_DIFFER`,`SUPER_GMV_GROUP`,`IS_ALIPAY_DIRECT`,`IS_KOUBEI_ACCESS`,`IS_WECHAT_DIRECT`,`LOGIN_SMS_VERIFY`,`NFC_OFFLINE_SALE`,`NFC_ZERO_BUY_SALE`,`T1_SETTLE_SWITCH`,`ALIPAY_TOUCH_TASK`,`ALIPAY_YZT_SWITCH`,`IS_ONLY_ATTENTION`,`IS_SUPER_SALESMAN`,`NFC_REGION_SWITCH`,`NFC_SESAME_GO_SALE`,`OPEN_ZERO_FEE_TIME`,`OPERATION_CENTER`,`QUICK_CASH_SWITCH`,`STAGING_OPEN_TIME`,`UNBIND_SMS_VERIFY`,`CLOSE_ZERO_FEE_TIME`,`IS_CUSTOMIZED_MINA`,`IS_NFC_REGION_AGENT`,`IS_OPEN_AFTER_PAYAD`,`MAX_NUMBER_OF_GRANT`,`ONE_LEVEL_GMV_GROUP`,`PREPAY_CARD_SWITCH`,`SUPER_SALESMAN_NUM`,`T1_SETTLE_BEGINDAY`,`AGENT_LEVEL_VERSION`,`COEFFICIENT_DIFFER`,`EASY_PAY_D0_WITHDRAW`,`MERCHANT_IS_OPENAPI`,`NFC_ONLINE_CLUE_SALE`,`IS_ADVERTISING_RIGHT`,`IS_CONFIG_OWN_CHANNEL`,`IS_QUICK_PUSH_SERVICE`,`PRE_INVOICING_SWITCH`,`ALIPAY_REGIONAL_AGENT`,`COST_DIFFER_START_TIME`,`COUPON_PAY_COMMISSION`,`MIN_NORMAL_COMMISSION`,`NFC_ALIPAY_BUYOUT_SALE`,`NFC_MAX_NUMBER_OF_GRANT`,`MAX_NUMBER_OF_EMPLOYEES`,`EXIST_INDIVIDUAL_SETTLE`,`NFC_PAY_RETURN_LATER_SALE`,`ALIPAY_PREPAY_CARD_SWITCH`,`CHANGE_VERSION_START_TIME`,`CHANNEL_VALUE_ADDED_AGENT`,`REFUND_HANDLE_PERMISSION`,`SETTLE_HANDLE_PERMISSION`,`INDIVIDUAL_SETTLE_SHOW_SWITCH`,`PHP_AGENT_BACKEND_PERMISSIONS`,`NFC_ALIPAY_PAY_RETURN_LATER_SALE`,`NFC_ALIPAY_ZERO_ENJOY_FIRST_SALE`,`UPDATE_TIME`,`MAX_COST_FEE`,`COMMISSION_APPLY_AMOUNT`
    </sql>


            <!--insert:TP_AGENT_RIGHT_CONTROL-->
            <insert id="insert" >
                    INSERT INTO TP_AGENT_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="isQqpay != null">`IS_QQPAY`,</if>
            <if test="isYipay != null">`IS_YIPAY`,</if>
            <if test="zeroFee != null">`ZERO_FEE`,</if>
            <if test="isH5payWx != null">`IS_H5PAY_WX`,</if>
            <if test="isOpenapi != null">`IS_OPENAPI`,</if>
            <if test="zfEndTime != null">`ZF_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isBdFreeze != null">`IS_BD_FREEZE`,</if>
            <if test="isFastAuth != null">`IS_FAST_AUTH`,</if>
            <if test="isUnionpay != null">`IS_UNIONPAY`,</if>
            <if test="modifyTime != null">`MODIFY_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="isFubeiLoan != null">`IS_FUBEI_LOAN`,</if>
            <if test="isHurricane != null">`IS_HURRICANE`,</if>
            <if test="minaBootNum != null">`MINA_BOOT_NUM`,</if>
            <if test="zfStartTime != null">`ZF_START_TIME`,</if>
            <if test="grantUpgrade != null">`GRANT_UPGRADE`,</if>
            <if test="isWechatCity != null">`IS_WECHAT_CITY`,</if>
            <if test="hurricaneTime != null">`HURRICANE_TIME`,</if>
            <if test="isKoubeiAccess != null">`IS_KOUBEI_ACCESS`,</if>
            <if test="isOnlyAttention != null">`IS_ONLY_ATTENTION`,</if>
            <if test="isSuperSalesman != null">`IS_SUPER_SALESMAN`,</if>
            <if test="openZeroFeeTime != null">`OPEN_ZERO_FEE_TIME`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="closeZeroFeeTime != null">`CLOSE_ZERO_FEE_TIME`,</if>
            <if test="isCustomizedMina != null">`IS_CUSTOMIZED_MINA`,</if>
            <if test="superSalesmanNum != null">`SUPER_SALESMAN_NUM`,</if>
            <if test="merchantIsOpenapi != null">`MERCHANT_IS_OPENAPI`,</if>
            <if test="isAdvertisingRight != null">`IS_ADVERTISING_RIGHT`,</if>
            <if test="isQuickPushService != null">`IS_QUICK_PUSH_SERVICE`,</if>
            <if test="isConfigOwnChannel != null">`IS_CONFIG_OWN_CHANNEL`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="isQqpay != null">#{isQqpay,jdbcType=TINYINT},</if>
            <if test="isYipay != null">#{isYipay,jdbcType=TINYINT},</if>
            <if test="zeroFee != null">#{zeroFee,jdbcType=TINYINT},</if>
            <if test="isH5payWx != null">#{isH5payWx,jdbcType=TINYINT},</if>
            <if test="isOpenapi != null">#{isOpenapi,jdbcType=TINYINT},</if>
            <if test="zfEndTime != null">#{zfEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isBdFreeze != null">#{isBdFreeze,jdbcType=TINYINT},</if>
            <if test="isFastAuth != null">#{isFastAuth,jdbcType=TINYINT},</if>
            <if test="isUnionpay != null">#{isUnionpay,jdbcType=TINYINT},</if>
            <if test="modifyTime != null">#{modifyTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isFubeiLoan != null">#{isFubeiLoan,jdbcType=TINYINT},</if>
            <if test="isHurricane != null">#{isHurricane,jdbcType=TINYINT},</if>
            <if test="minaBootNum != null">#{minaBootNum,jdbcType=TINYINT},</if>
            <if test="zfStartTime != null">#{zfStartTime,jdbcType=INTEGER},</if>
            <if test="grantUpgrade != null">#{grantUpgrade,jdbcType=TINYINT},</if>
            <if test="isWechatCity != null">#{isWechatCity,jdbcType=TINYINT},</if>
            <if test="hurricaneTime != null">#{hurricaneTime,jdbcType=INTEGER},</if>
            <if test="isKoubeiAccess != null">#{isKoubeiAccess,jdbcType=TINYINT},</if>
            <if test="isOnlyAttention != null">#{isOnlyAttention,jdbcType=TINYINT},</if>
            <if test="isSuperSalesman != null">#{isSuperSalesman,jdbcType=TINYINT},</if>
            <if test="openZeroFeeTime != null">#{openZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="closeZeroFeeTime != null">#{closeZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="isCustomizedMina != null">#{isCustomizedMina,jdbcType=TINYINT},</if>
            <if test="superSalesmanNum != null">#{superSalesmanNum,jdbcType=SMALLINT},</if>
            <if test="merchantIsOpenapi != null">#{merchantIsOpenapi,jdbcType=TINYINT},</if>
            <if test="isAdvertisingRight != null">#{isAdvertisingRight,jdbcType=TINYINT},</if>
            <if test="isQuickPushService != null">#{isQuickPushService,jdbcType=TINYINT},</if>
            <if test="isConfigOwnChannel != null">#{isConfigOwnChannel,jdbcType=TINYINT},</if>
        </trim>
            </insert>

            <!--get:TP_AGENT_RIGHT_CONTROL-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-AGENT-RIGHT-CONTROL-GETBYID*/  <include refid="Base_Column_List" />
        FROM TP_AGENT_RIGHT_CONTROL
        WHERE
        ID
        = #{id,jdbcType=INTEGER}
            </select>

            <!--get:根据代理商ID[keyId]查询代理商权限信息-->
            <select id="getAgentRightControlByAgentId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-AGENT-RIGHT-CONTROL-GETAGENTRIGHTCONTROLBYAGENTID*/  <include refid="Base_Column_List" />
        FROM TP_AGENT_RIGHT_CONTROL
        WHERE
        key_id = #{keyId,jdbcType=INTEGER}
        limit 1
            </select>
    </mapper>
