<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.LifecirclemarketDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 8 as fg,'LM_CRM_ALIPAY_TASK_EMUN' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REASON_DESC,REASON_TYPE,REASON_VALUE,REASON_TYPE_DESC,BUSINESS_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_ALIPAY_TASK_EMUN'
            AND COLUMN_NAME in('ID','REASON_DESC','REASON_TYPE','REASON_VALUE','REASON_TYPE_DESC','BUSINESS_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 7 as fg,'LM_CRM_USER_ROLE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ROLE_ID,USER_ID,CREATE_BY,UPDATE_BY,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_USER_ROLE'
            AND COLUMN_NAME in('ID','ROLE_ID','USER_ID','CREATE_BY','UPDATE_BY','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 49 as fg,'LM_CRM_ALIPAY_TOUCH_TASK' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,BIND_TIME,PEND_TIME,LIMIT_TIME,ACCEPT_TIME,NOTIFY_TIME,RECEIVE_TIME,DISTRIBUTE_TIME,SN_LIST,TASK_NO,CITY_CODE,CITY_NAME,SHOP_NAME,SMID_LIST,STORE_PIC,USERNAME,AGENT_NAME,PRODUCT_ID,STORE_NAME,ACTIVITY_ID,FB_SMID_LIST,MARKET_NAME,REASON_DESC,CONTACT_NAME,PRODUCT_NAME,PRODUCT_TAGS,ACTIVITY_DESC,ACTIVITY_TYPE,DISTRICT_CODE,DISTRICT_NAME,PROVINCE_CODE,PROVINCE_NAME,SALESMAN_NAME,CONTACT_MOBILE,INTERIOR_PHOTO,REGISTER_PHOTO,FB_MERCHANT_ADDRESS,ALIPAY_MERCHANT_ADDRESS,UID,IS_DEL,AGENT_ID,MARKET_ID,WORKER_ID,SALESMAN_ID,TASK_STATUS,MERCHANT_TYPE,PENDING_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_ALIPAY_TOUCH_TASK'
            AND COLUMN_NAME in('ID','BIND_TIME','PEND_TIME','LIMIT_TIME','ACCEPT_TIME','NOTIFY_TIME','RECEIVE_TIME','DISTRIBUTE_TIME','SN_LIST','TASK_NO','CITY_CODE','CITY_NAME','SHOP_NAME','SMID_LIST','STORE_PIC','USERNAME','AGENT_NAME','PRODUCT_ID','STORE_NAME','ACTIVITY_ID','FB_SMID_LIST','MARKET_NAME','REASON_DESC','CONTACT_NAME','PRODUCT_NAME','PRODUCT_TAGS','ACTIVITY_DESC','ACTIVITY_TYPE','DISTRICT_CODE','DISTRICT_NAME','PROVINCE_CODE','PROVINCE_NAME','SALESMAN_NAME','CONTACT_MOBILE','INTERIOR_PHOTO','REGISTER_PHOTO','FB_MERCHANT_ADDRESS','ALIPAY_MERCHANT_ADDRESS','UID','IS_DEL','AGENT_ID','MARKET_ID','WORKER_ID','SALESMAN_ID','TASK_STATUS','MERCHANT_TYPE','PENDING_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'LM_MERCHANT_ACTIVITY_APPLY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APPLY_NO,COMPANY,SUB_MCH_ID,USERNAME,ACTIVITY_ID,MERCHANT_NO,OUT_APPLY_NO,REJECT_REASON,ACTIVITY_REBATE_REJECT_REASON,UID,IS_DEL,BELONG,CHANNEL_ID,ACTIVITY_STATUS,ACTIVITY_OPEN_TIME,ACTIVITY_SIGN_TIME,ACTIVITY_REBATE_APPLY_TIME,ACTIVITY_REBATE_BEGIN_MONTH,ACTIVITY_REBATE_APPLY_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_MERCHANT_ACTIVITY_APPLY'
            AND COLUMN_NAME in('ID','APPLY_NO','COMPANY','SUB_MCH_ID','USERNAME','ACTIVITY_ID','MERCHANT_NO','OUT_APPLY_NO','REJECT_REASON','ACTIVITY_REBATE_REJECT_REASON','UID','IS_DEL','BELONG','CHANNEL_ID','ACTIVITY_STATUS','ACTIVITY_OPEN_TIME','ACTIVITY_SIGN_TIME','ACTIVITY_REBATE_APPLY_TIME','ACTIVITY_REBATE_BEGIN_MONTH','ACTIVITY_REBATE_APPLY_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'LM_CRM_DOWNLOAD_CENTER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,FILE_URL,REMARKS,CREATE_BY,FILE_NAME,UPDATE_BY,DOWNLOAD_ID,STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_DOWNLOAD_CENTER'
            AND COLUMN_NAME in('ID','FILE_URL','REMARKS','CREATE_BY','FILE_NAME','UPDATE_BY','DOWNLOAD_ID','STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 21 as fg,'TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CODE_TYPE,PROVINCE,PRODUCT_ID,PRODUCT_NAME,GOODS_BAR_CODE,PRODUCT_BRAND,PRODUCT_IMAGE,PRODUCT_MODEL,PRODUCT_CATEGORY,ENERGY_EFFICIENCY,MANUFACT_CERT_CODE,UID,IS_DEL,OPERATOR_ID,PRODUCT_SORT,AVAILABLE_STATUS,CREATE_TIME,UPDATE_TIME,BASIS_PRICE,PRODUCT_PRICE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS'
            AND COLUMN_NAME in('ID','CODE_TYPE','PROVINCE','PRODUCT_ID','PRODUCT_NAME','GOODS_BAR_CODE','PRODUCT_BRAND','PRODUCT_IMAGE','PRODUCT_MODEL','PRODUCT_CATEGORY','ENERGY_EFFICIENCY','MANUFACT_CERT_CODE','UID','IS_DEL','OPERATOR_ID','PRODUCT_SORT','AVAILABLE_STATUS','CREATE_TIME','UPDATE_TIME','BASIS_PRICE','PRODUCT_PRICE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'LM_CRM_ACTIVITY_POLICY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVITY_ID,ACTIVITY_NAME,EXCLUDE_ACTIVITY_ID_LIST,IS_DEL,END_TIME,START_TIME,ACTIVITY_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_ACTIVITY_POLICY'
            AND COLUMN_NAME in('ID','ACTIVITY_ID','ACTIVITY_NAME','EXCLUDE_ACTIVITY_ID_LIST','IS_DEL','END_TIME','START_TIME','ACTIVITY_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'LM_SINAN_ACTIVITY_GOLD_DOCK_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,URL,OTHER_MSG,IS_DEL,DEAL_DATE,REPORT_TYPE,TARGET_TYPE,OPERATE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_SINAN_ACTIVITY_GOLD_DOCK_LOG'
            AND COLUMN_NAME in('ID','URL','OTHER_MSG','IS_DEL','DEAL_DATE','REPORT_TYPE','TARGET_TYPE','OPERATE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 18 as fg,'LM_SINAN_ACTIVITY_GOLD_MERCHANT_DAY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,MERCHANT_NO,MERCHANT_USERNAME,UID,AGENT_ID,PAY_TYPE,STORE_ID,TRADE_DAY,TRADE_NUM,REFUND_NUM,IS_CREDIT_CARD,INCOME_TRADE_NUM,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME,TRADE_MONEY,INCOME_MONEY,REFUND_MONEY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_SINAN_ACTIVITY_GOLD_MERCHANT_DAY'
            AND COLUMN_NAME in('ID','MERCHANT_NO','MERCHANT_USERNAME','UID','AGENT_ID','PAY_TYPE','STORE_ID','TRADE_DAY','TRADE_NUM','REFUND_NUM','IS_CREDIT_CARD','INCOME_TRADE_NUM','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME','TRADE_MONEY','INCOME_MONEY','REFUND_MONEY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'LM_CRM_CHANNEL_POLICY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,AGENT_TAG,POLICY_ID,POLICY_NAME,PAYMENT_DESC,AGENT_SAIL_TAG,IMPORT_LIST_URL,POLICY_FILE_URL,AGENT_WHITE_LIST,PAYMENT_PROOF_URL,POLICY_DESCRIPTION,EXCLUDE_POLICY_ID_LIST,IS_DEL,IS_SHOW,GRANT_CATEGORY,USER_DIMENSION,MARKET_CATEGORY,HAS_PAYMENT_PROOF,END_DATE,START_DATE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_CHANNEL_POLICY'
            AND COLUMN_NAME in('ID','AGENT_TAG','POLICY_ID','POLICY_NAME','PAYMENT_DESC','AGENT_SAIL_TAG','IMPORT_LIST_URL','POLICY_FILE_URL','AGENT_WHITE_LIST','PAYMENT_PROOF_URL','POLICY_DESCRIPTION','EXCLUDE_POLICY_ID_LIST','IS_DEL','IS_SHOW','GRANT_CATEGORY','USER_DIMENSION','MARKET_CATEGORY','HAS_PAYMENT_PROOF','END_DATE','START_DATE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'LM_CRM_ALIPAY_TOUCH_TASK_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,TASK_NO,ERROR_MSG,OPERATOR_ID,OPERATE_DESC,OPERATOR_NAME,OPERATE_TYPE,OPERATOR_TYPE,OPERATE_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_ALIPAY_TOUCH_TASK_LOG'
            AND COLUMN_NAME in('ID','REMARK','TASK_NO','ERROR_MSG','OPERATOR_ID','OPERATE_DESC','OPERATOR_NAME','OPERATE_TYPE','OPERATOR_TYPE','OPERATE_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'LM_ACTIVITY_TEMPLATE_CONFIG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CONFIG_NAME,CONFIG_VALUE,TEMPLATE_CODE,TEMPLATE_NAME,TEMPLATE_TYPE,CONFIG_ATTRIBUTE,CONFIG_MAPPING_FILE,IS_DEL,SORT_SCORE,EFFECTIVE_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_ACTIVITY_TEMPLATE_CONFIG'
            AND COLUMN_NAME in('ID','CONFIG_NAME','CONFIG_VALUE','TEMPLATE_CODE','TEMPLATE_NAME','TEMPLATE_TYPE','CONFIG_ATTRIBUTE','CONFIG_MAPPING_FILE','IS_DEL','SORT_SCORE','EFFECTIVE_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 22 as fg,'LM_CRM_CHANNEL_POLICY_FORM' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,POLICY_ID,POLICY_NAME,POLICY_FORM_ID,PROVINCE_MANAGER,OPERATIONAL_NOTES,RELATED_SIGN_ID_LIST,PAYMENT_PROOF_URL_LIST,IS_DEL,USER_ID,AGENT_ID,GRANT_ID,MARKET_ID,PARENT_ID,USER_TYPE,AUDIT_STATUS,RELATED_AGENT_ID,SETTLEMENT_START_TIME,CREATE_TIME,SUBMIT_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_CHANNEL_POLICY_FORM'
            AND COLUMN_NAME in('ID','REMARK','POLICY_ID','POLICY_NAME','POLICY_FORM_ID','PROVINCE_MANAGER','OPERATIONAL_NOTES','RELATED_SIGN_ID_LIST','PAYMENT_PROOF_URL_LIST','IS_DEL','USER_ID','AGENT_ID','GRANT_ID','MARKET_ID','PARENT_ID','USER_TYPE','AUDIT_STATUS','RELATED_AGENT_ID','SETTLEMENT_START_TIME','CREATE_TIME','SUBMIT_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 46 as fg,'LM_SINAN_ACTIVITY_POLICY_TYPE_INFO' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,CARD_BIN,END_TIME,POSTAR_EXT,START_TIME,TEMPLET_ID,EXPENSE_ACCOUNT,POLICY_TYPE_NAME,EMAIL_TARGET_LIST,POSTAR_FALL_AGET_ID,ADJUSTMENT_RATE_COST,LESHUA_ACTIVITY_CODE,WX_AUTH_EXPAND_QRCODE,ZFB_AUTH_EXPAND_QRCODE,ACTIVITY_INDUSTRY_TYPE,TYPE,IS_DEL,IS_BEAR_TAX,SHOW_WX_RATE,IS_SEND_EMAIL,ACTIVITY_TYPE,LIQUIDATION_TYPE,CAN_EXPORT_MERCHANT,GOLD_CARD_SETTLE_TYPE,READJUST_PRICES_FLAG,OPEN_CARD_BANK_STRATEGY,ACTIVITY_ACQUIRING_TYPE,CHECK_TERMINAL_RATE_FLAG,BANK_MANAGER_CODE_STRATEGY,BANK_MANAGER_NAME_STRATEGY,SHOW_CONTACT_NAME_STRATEGY,IS_WX_CUSTOMER_EXPAND_QRCODE,POSTAR_FALL_AGET_ID_STRATEGY,IS_ZFB_CUSTOMER_EXPAND_QRCODE,CREATE_TIME,UPDATE_TIME,WX_FEE_RATE,ZFB_FEE_RATE,DEBIT_FEE_RATE,MAX_ALLOWANCE,CREDIT_FEE_RATE,ALLOWANCE_AMOUNT_MAX,ALLOWANCE_AMOUNT_MIN,CLOUD_PAY_DBASIC_FEE_RATE,CLOUD_PAY_DOVERFLOW_FEE_RATE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_SINAN_ACTIVITY_POLICY_TYPE_INFO'
            AND COLUMN_NAME in('ID','REMARK','CARD_BIN','END_TIME','POSTAR_EXT','START_TIME','TEMPLET_ID','EXPENSE_ACCOUNT','POLICY_TYPE_NAME','EMAIL_TARGET_LIST','POSTAR_FALL_AGET_ID','ADJUSTMENT_RATE_COST','LESHUA_ACTIVITY_CODE','WX_AUTH_EXPAND_QRCODE','ZFB_AUTH_EXPAND_QRCODE','ACTIVITY_INDUSTRY_TYPE','TYPE','IS_DEL','IS_BEAR_TAX','SHOW_WX_RATE','IS_SEND_EMAIL','ACTIVITY_TYPE','LIQUIDATION_TYPE','CAN_EXPORT_MERCHANT','GOLD_CARD_SETTLE_TYPE','READJUST_PRICES_FLAG','OPEN_CARD_BANK_STRATEGY','ACTIVITY_ACQUIRING_TYPE','CHECK_TERMINAL_RATE_FLAG','BANK_MANAGER_CODE_STRATEGY','BANK_MANAGER_NAME_STRATEGY','SHOW_CONTACT_NAME_STRATEGY','IS_WX_CUSTOMER_EXPAND_QRCODE','POSTAR_FALL_AGET_ID_STRATEGY','IS_ZFB_CUSTOMER_EXPAND_QRCODE','CREATE_TIME','UPDATE_TIME','WX_FEE_RATE','ZFB_FEE_RATE','DEBIT_FEE_RATE','MAX_ALLOWANCE','CREDIT_FEE_RATE','ALLOWANCE_AMOUNT_MAX','ALLOWANCE_AMOUNT_MIN','CLOUD_PAY_DBASIC_FEE_RATE','CLOUD_PAY_DOVERFLOW_FEE_RATE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 23 as fg,'LM_SINAN_ACTIVITY_EXTEND' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CREATE_BY,LAST_UPDATE_BY,USER_ID,USER_STATUS,ALIPAY_LM_STATUS,ALIPAY_NFC_STATUS,GROUP_MEAL_STATUS,ALIPAY_MINA_STATUS,TEACH_TRAIN_STATUS,WECHAT_MINA_STATUS,CARD_ACTIVITY_STATUS,WECHAT_INDUSTRY_STATUS,RECEIPT_ACTIVITY_STATUS,FUBEI_BATTERY_LIFE_STATUS,INDUSTRY_ACTIVITY_STATUS,MERCHANT_ACTIVITY_STATUS,WECHAT_HIGH_SCHOOL_STATUS,ALIPAY_DIRECT_INCOME_STATUS,WECHAT_DIRECT_INCOME_STATUS,ALIPAY_SCHOOL_CANTEEN_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_SINAN_ACTIVITY_EXTEND'
            AND COLUMN_NAME in('ID','CREATE_BY','LAST_UPDATE_BY','USER_ID','USER_STATUS','ALIPAY_LM_STATUS','ALIPAY_NFC_STATUS','GROUP_MEAL_STATUS','ALIPAY_MINA_STATUS','TEACH_TRAIN_STATUS','WECHAT_MINA_STATUS','CARD_ACTIVITY_STATUS','WECHAT_INDUSTRY_STATUS','RECEIPT_ACTIVITY_STATUS','FUBEI_BATTERY_LIFE_STATUS','INDUSTRY_ACTIVITY_STATUS','MERCHANT_ACTIVITY_STATUS','WECHAT_HIGH_SCHOOL_STATUS','ALIPAY_DIRECT_INCOME_STATUS','WECHAT_DIRECT_INCOME_STATUS','ALIPAY_SCHOOL_CANTEEN_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,CITY_CODE,CATEGORY_CODE,CATEGORY_NAME,IS_DEL,CATEGORY_SORT,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY'
            AND COLUMN_NAME in('ID','CITY_CODE','CATEGORY_CODE','CATEGORY_NAME','IS_DEL','CATEGORY_SORT','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 85 as fg,'TP_USER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,QQ,TEL,AREA,BANK,CITY,LOGO,EMAIL,LEVEL,PHONE,UNAME,PEOPLE,REMARK,UTOKEN,WECHAT,ADDRESS,CARD_NUM,CARD_WEB,BUSINESS,CARDNAME,CONTACTS,FULL_PATH,NICKNAME,PASSWORD,PROVINCE,TURNOVER,USERNAME,PRIVILEGES,PROFESSION,CARD_ADDRESS,COMPANYNAME,LAST_LOGIN_IP,BUSINESS_AREA,BUSINESS_CITY,CUSTOMER_NOTE,LAST_LOCATION,VERIFYIMAGES,ALIPAYACCOUNT,OPEN_API_CALLBACK,BUSINESS_PROVINCE,UID,CASH,ROLE,TYPE,BELONG,IS_OPEN,IS_PASS,OWN_RUN,STATUS,IS_JDPAY,IS_WHITE,VIPTIME,CHILD_NUM,GROUP_NUM,IS_ALIPAY,PARENT_ID,PLATFORM,IS_SELF_FEE,CONFIG_TYPE,CREATETIME,IS_OPEN_MINA,IS_SALESMAN,IS_SHOW_TIPS,LOAN_STATUS,ACCOUNT_TYPE,IS_QUICK_CASH,SALESMAN_TAG,SUB_CONFIG_ID,USE_GROUP_NUM,CURRENT_LEVEL,SALES_STAFF_ID,IS_SCAN_SERVICE,LAST_LOGIN_TIME,CUSTOMER_SERVICE,IS_OPENAPI_ACCESS,IS_SUPER_SALESMAN,SUPER_SALESMAN_ID,ADVERTISEMENT_NUM,OPERATION_SERVICE,NEW_VERSION_USERS_NUM,SALESMAN_TAG_START_DAY,CREATE_TIME,UPDATE_TIME,FINANCE,ADVERTISEMENT_BALANCE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_USER'
            AND COLUMN_NAME in('ID','QQ','TEL','AREA','BANK','CITY','LOGO','EMAIL','LEVEL','PHONE','UNAME','PEOPLE','REMARK','UTOKEN','WECHAT','ADDRESS','CARD_NUM','CARD_WEB','BUSINESS','CARDNAME','CONTACTS','FULL_PATH','NICKNAME','PASSWORD','PROVINCE','TURNOVER','USERNAME','PRIVILEGES','PROFESSION','CARD_ADDRESS','COMPANYNAME','LAST_LOGIN_IP','BUSINESS_AREA','BUSINESS_CITY','CUSTOMER_NOTE','LAST_LOCATION','VERIFYIMAGES','ALIPAYACCOUNT','OPEN_API_CALLBACK','BUSINESS_PROVINCE','UID','CASH','ROLE','TYPE','BELONG','IS_OPEN','IS_PASS','OWN_RUN','STATUS','IS_JDPAY','IS_WHITE','VIPTIME','CHILD_NUM','GROUP_NUM','IS_ALIPAY','PARENT_ID','PLATFORM','IS_SELF_FEE','CONFIG_TYPE','CREATETIME','IS_OPEN_MINA','IS_SALESMAN','IS_SHOW_TIPS','LOAN_STATUS','ACCOUNT_TYPE','IS_QUICK_CASH','SALESMAN_TAG','SUB_CONFIG_ID','USE_GROUP_NUM','CURRENT_LEVEL','SALES_STAFF_ID','IS_SCAN_SERVICE','LAST_LOGIN_TIME','CUSTOMER_SERVICE','IS_OPENAPI_ACCESS','IS_SUPER_SALESMAN','SUPER_SALESMAN_ID','ADVERTISEMENT_NUM','OPERATION_SERVICE','NEW_VERSION_USERS_NUM','SALESMAN_TAG_START_DAY','CREATE_TIME','UPDATE_TIME','FINANCE','ADVERTISEMENT_BALANCE')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 9 as fg,'LM_CRM_LOSS_STORE_RULE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,IS_DEL,USER_ID,OFF_PERCENT,PUSH_STATUS,PUSH_FREQUENCY,CREATE_TIME,UPDATE_TIME,LAST_WEEK_AVERAGE_DAILY' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_LOSS_STORE_RULE'
            AND COLUMN_NAME in('ID','IS_DEL','USER_ID','OFF_PERCENT','PUSH_STATUS','PUSH_FREQUENCY','CREATE_TIME','UPDATE_TIME','LAST_WEEK_AVERAGE_DAILY')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 19 as fg,'LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,UNIONID,REAL_NAME,JOB_NUMBER,PRE_SALESMAN_COST_RATE_EXT,AFTER_SALESMAN_COST_RATE_EXT,IS_DEL,USER_ID,PRE_COMMISSION_TYPE,PRE_FIXED_COMMISSION,AFTER_COMMISSION_TYPE,PRE_NORMAL_COMMISSION,AFTER_FIXED_COMMISSION,AFTER_NORMAL_COMMISSION,PRE_FIXED_COMMISSION_EFFECTIVE_TIME,AFTER_FIXED_COMMISSION_EFFECTIVE_TIME,BEGIN_TIME,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG'
            AND COLUMN_NAME in('ID','UNIONID','REAL_NAME','JOB_NUMBER','PRE_SALESMAN_COST_RATE_EXT','AFTER_SALESMAN_COST_RATE_EXT','IS_DEL','USER_ID','PRE_COMMISSION_TYPE','PRE_FIXED_COMMISSION','AFTER_COMMISSION_TYPE','PRE_NORMAL_COMMISSION','AFTER_FIXED_COMMISSION','AFTER_NORMAL_COMMISSION','PRE_FIXED_COMMISSION_EFFECTIVE_TIME','AFTER_FIXED_COMMISSION_EFFECTIVE_TIME','BEGIN_TIME','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 76 as fg,'LM_CRM_WECHAT_AUTHORIZE_APPLY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,MOBILE,CERT_PIC,CERT_TYPE,SUB_MCHID,STORE_NAME,CERT_NUMBER,LICENSE_PIC,QRCODE_DATA,APPLYMENT_ID,COMPANY_NAME,LEGAL_PERSON,REJECT_PARAM,SUBJECT_TYPE,BUSINESS_CODE,CERT_TYPE_NAME,ID_CARD_NUMBER,MICRO_BIZ_TYPE,REJECT_REASON,STORE_ADDRESS,LICENSE_NUMBER,STORE_AREA_CODE,STORE_AREA_NAME,STORE_CITY_CODE,STORE_CITY_NAME,APPLYMENT_STATE,AUTHORIZE_STATE,COMPANY_ADDRESS,LICENSE_END_DATE,STORE_HEADER_PIC,STORE_INDOOR_PIC,COMPANY_PROVE_PIC,SUBJECT_TYPE_NAME,CONFIRM_MCHID_LIST,CONTACT_ID_DOC_COPY,CONTACT_PERIOD_END,LICENSE_BEGIN_DATE,MICRO_BIZ_TYPE_NAME,STORE_ADDRESS_CODE,APPLYMENT_STATE_MSG,STORE_PROVINCE_CODE,STORE_PROVINCE_NAME,CONTACT_PERIOD_BEGIN,IDENTIFICATION_NAME,IDENTIFICATION_TYPE,CONTACT_ID_DOC_COPY_BACK,IDENTIFICATION_NUMBER,IDENTIFICATION_ADDRESS,IDENTIFICATION_BACK_PIC,IDENTIFICATION_END_DATE,IDENTIFICATION_FRONT_PIC,IDENTIFICATION_TYPE_NAME,IDENTIFICATION_BEGIN_DATE,BUSINESS_AUTHORIZATION_LETTER,UID,IS_DEL,STORE_ID,CHANNEL_ID,AUDIT_STATUS,CONTACT_TYPE,APPLY_CHANNEL,CERT_TYPE_VALUE,LICENSE_IS_LONG,APPLYMENT_METHOD,APPLYMENT_STATUS,AUTHORIZE_STATUS,CONTACT_ID_DOC_TYPE,SUBJECT_TYPE_VALUE,MICRO_BIZ_TYPE_VALUE,CONTACT_PERIOD_IS_LONG,IDENTIFICATION_IS_LONG,PLATFORM_FIRST_AUDIT_TIME,IDENTIFICATION_TYPE_VALUE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_WECHAT_AUTHORIZE_APPLY'
            AND COLUMN_NAME in('ID','NAME','MOBILE','CERT_PIC','CERT_TYPE','SUB_MCHID','STORE_NAME','CERT_NUMBER','LICENSE_PIC','QRCODE_DATA','APPLYMENT_ID','COMPANY_NAME','LEGAL_PERSON','REJECT_PARAM','SUBJECT_TYPE','BUSINESS_CODE','CERT_TYPE_NAME','ID_CARD_NUMBER','MICRO_BIZ_TYPE','REJECT_REASON','STORE_ADDRESS','LICENSE_NUMBER','STORE_AREA_CODE','STORE_AREA_NAME','STORE_CITY_CODE','STORE_CITY_NAME','APPLYMENT_STATE','AUTHORIZE_STATE','COMPANY_ADDRESS','LICENSE_END_DATE','STORE_HEADER_PIC','STORE_INDOOR_PIC','COMPANY_PROVE_PIC','SUBJECT_TYPE_NAME','CONFIRM_MCHID_LIST','CONTACT_ID_DOC_COPY','CONTACT_PERIOD_END','LICENSE_BEGIN_DATE','MICRO_BIZ_TYPE_NAME','STORE_ADDRESS_CODE','APPLYMENT_STATE_MSG','STORE_PROVINCE_CODE','STORE_PROVINCE_NAME','CONTACT_PERIOD_BEGIN','IDENTIFICATION_NAME','IDENTIFICATION_TYPE','CONTACT_ID_DOC_COPY_BACK','IDENTIFICATION_NUMBER','IDENTIFICATION_ADDRESS','IDENTIFICATION_BACK_PIC','IDENTIFICATION_END_DATE','IDENTIFICATION_FRONT_PIC','IDENTIFICATION_TYPE_NAME','IDENTIFICATION_BEGIN_DATE','BUSINESS_AUTHORIZATION_LETTER','UID','IS_DEL','STORE_ID','CHANNEL_ID','AUDIT_STATUS','CONTACT_TYPE','APPLY_CHANNEL','CERT_TYPE_VALUE','LICENSE_IS_LONG','APPLYMENT_METHOD','APPLYMENT_STATUS','AUTHORIZE_STATUS','CONTACT_ID_DOC_TYPE','SUBJECT_TYPE_VALUE','MICRO_BIZ_TYPE_VALUE','CONTACT_PERIOD_IS_LONG','IDENTIFICATION_IS_LONG','PLATFORM_FIRST_AUDIT_TIME','IDENTIFICATION_TYPE_VALUE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 28 as fg,'LM_WECHAT_HIGH_SCHOOL_ACTIVITY_APPLY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APPLY_ID,COMPANY,SUB_MCH_ID,USERNAME,SHORT_NAME,ACTIVITY_ID,LICENSE_PIC,MERCHANT_NO,REJECT_INFO,STORE_HEAD_PIC,STORE_INDOOR_PIC,MANAGE_QUALIFICATION_PIC,SCHOOL_QUALIFICATION_PIC,COOPERATION_AGREEMENT_PIC,ACTIVITY_REBATE_REJECT_REASON,UID,IS_DEL,BELONG,SUBJECT_TYPE,ACTIVITY_STATUS,ACTIVITY_OPEN_TIME,ACTIVITY_SIGN_TIME,ACTIVITY_REBATE_APPLY_TIME,ACTIVITY_REBATE_BEGIN_MONTH,ACTIVITY_REBATE_APPLY_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_WECHAT_HIGH_SCHOOL_ACTIVITY_APPLY'
            AND COLUMN_NAME in('ID','APPLY_ID','COMPANY','SUB_MCH_ID','USERNAME','SHORT_NAME','ACTIVITY_ID','LICENSE_PIC','MERCHANT_NO','REJECT_INFO','STORE_HEAD_PIC','STORE_INDOOR_PIC','MANAGE_QUALIFICATION_PIC','SCHOOL_QUALIFICATION_PIC','COOPERATION_AGREEMENT_PIC','ACTIVITY_REBATE_REJECT_REASON','UID','IS_DEL','BELONG','SUBJECT_TYPE','ACTIVITY_STATUS','ACTIVITY_OPEN_TIME','ACTIVITY_SIGN_TIME','ACTIVITY_REBATE_APPLY_TIME','ACTIVITY_REBATE_BEGIN_MONTH','ACTIVITY_REBATE_APPLY_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 11 as fg,'DP_AGENT_OPERATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,UNION_ID,OPERATOR,REAL_NAME,JOB_NUMBER,OPERATOR_NUMBER,VALID,AGENT_ID,OPERATION_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'DP_AGENT_OPERATION'
            AND COLUMN_NAME in('ID','UNION_ID','OPERATOR','REAL_NAME','JOB_NUMBER','OPERATOR_NUMBER','VALID','AGENT_ID','OPERATION_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'LM_CRM_ACTIVITY_POLICY_TARGET' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,ACTIVITY_ID,IS_DEL,END_TIME,TARGET_ID,START_TIME,SPECIAL_MARK,SETTLE_AGENT_ID,REGISTER_STATUS,ACTIVITY_TARGET_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_ACTIVITY_POLICY_TARGET'
            AND COLUMN_NAME in('ID','REMARK','ACTIVITY_ID','IS_DEL','END_TIME','TARGET_ID','START_TIME','SPECIAL_MARK','SETTLE_AGENT_ID','REGISTER_STATUS','ACTIVITY_TARGET_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 24 as fg,'TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REASON,ADDRESS,COMPANY,USERNAME,LICENSE_PIC,CONTACT_NAME,LOGIC_GROUP_ID,AGREEMENT_ONE_PIC,AGREEMENT_TWO_PIC,CONTACT_POSITION,CONTACT_TELEPHONE,ALIPAY_LEGAL_PERSON_ACCOUNT,IS_DEL,PV_DAY,APPLY_TIME,AUDIT_TIME,PV_DAY_MEAL,EXPECT_TIME,MERCHANT_ID,EQUIPMENT_NUM,GROUP_MEAL_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY'
            AND COLUMN_NAME in('ID','REASON','ADDRESS','COMPANY','USERNAME','LICENSE_PIC','CONTACT_NAME','LOGIC_GROUP_ID','AGREEMENT_ONE_PIC','AGREEMENT_TWO_PIC','CONTACT_POSITION','CONTACT_TELEPHONE','ALIPAY_LEGAL_PERSON_ACCOUNT','IS_DEL','PV_DAY','APPLY_TIME','AUDIT_TIME','PV_DAY_MEAL','EXPECT_TIME','MERCHANT_ID','EQUIPMENT_NUM','GROUP_MEAL_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 10 as fg,'LM_MERCHANT_ACTIVITY_APPLY_EXT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,EXT1,APPLY_NO,APPLY_PIC_ATTRIBUTE,MERCHANT_ATTRIBUTE,APPLY_INFO_ATTRIBUTE,UID,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_MERCHANT_ACTIVITY_APPLY_EXT'
            AND COLUMN_NAME in('ID','EXT1','APPLY_NO','APPLY_PIC_ATTRIBUTE','MERCHANT_ATTRIBUTE','APPLY_INFO_ATTRIBUTE','UID','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'TP_ACTIVITY_PACKAGE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,TOKEN,PERIOD,MERCHANT_NUMBER,LIQUIDATION_TYPE,CREATE_TIME,UPDATE_TIME,PERIOD_END_DATE,PERIOD_BEGIN_DATE,TOTAL,BALANCE,TRADE_AMOUNT,UESD_TRADE_AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'TP_ACTIVITY_PACKAGE'
            AND COLUMN_NAME in('ID','TOKEN','PERIOD','MERCHANT_NUMBER','LIQUIDATION_TYPE','CREATE_TIME','UPDATE_TIME','PERIOD_END_DATE','PERIOD_BEGIN_DATE','TOTAL','BALANCE','TRADE_AMOUNT','UESD_TRADE_AMOUNT')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 12 as fg,'LM_ACTIVITY_TEMPLATE_RELATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACTIVITY_ID,SINAN_ACTIVITY_CODE,RELATION_TEMPLATE_CODE,RELATION_TEMPLATE_NAME,RELATION_TEMPLATE_TYPE,IS_DEL,FIT_RANGE,SORT_SCORE,EFFECTIVE_STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_ACTIVITY_TEMPLATE_RELATION'
            AND COLUMN_NAME in('ID','ACTIVITY_ID','SINAN_ACTIVITY_CODE','RELATION_TEMPLATE_CODE','RELATION_TEMPLATE_NAME','RELATION_TEMPLATE_TYPE','IS_DEL','FIT_RANGE','SORT_SCORE','EFFECTIVE_STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 13 as fg,'LM_MARKET_ACTIVITY_OPERATE_LOG' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,APPLY_NO,OPERATE_ID,OPERATE_DESC,OPERATE_NAME,ACTIVITY_CODE,ACTIVITY_NAME,OPERATE_ATTRIBUTE,IS_DEL,APPLY_STATUS,OPERATE_TYPE,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_MARKET_ACTIVITY_OPERATE_LOG'
            AND COLUMN_NAME in('ID','APPLY_NO','OPERATE_ID','OPERATE_DESC','OPERATE_NAME','ACTIVITY_CODE','ACTIVITY_NAME','OPERATE_ATTRIBUTE','IS_DEL','APPLY_STATUS','OPERATE_TYPE','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 6 as fg,'LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GROUP_ID,POLICY_ID,IS_DEL,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP'
            AND COLUMN_NAME in('ID','GROUP_ID','POLICY_ID','IS_DEL','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 6 as fg,'LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,GROUP_ID,IS_DEL,USER_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER'
            AND COLUMN_NAME in('ID','GROUP_ID','IS_DEL','USER_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 36 as fg,'LM_CRM_USER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,SYS_USER_ID,SALT,EMAIL,AVATAR,MOBILE,USER_ID,CREATE_BY,REAL_NAME,UPDATE_BY,JOB_NUMBER,LAST_LOGIN_IP,LOGIN_PASSWORD,DINGTALK_USER_ID,LAST_LOGIN_DEVICE,RELEVANT_TRIAL_ACCOUNT,SEX,USER_TYPE,ADD_SOURCE,USER_STATUS,IS_DIRECT_TYPE,IS_FIRST_START,ACCOUNT_STATUS,IS_SET_CLOSE_TIME,IS_FORMAL_ACCOUNT,IS_SET_FREEZE_TIME,IS_VIRTUAL_ACCOUNT,IS_DEFAULT_PWD_MODIFIED,CLOSE_TIME,HIRED_DATE,CREATE_TIME,FREEZE_TIME,UPDATE_TIME,REGULAR_DATE,DIMISSION_DATE,LAST_LOGIN_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_CRM_USER'
            AND COLUMN_NAME in('ID','SYS_USER_ID','SALT','EMAIL','AVATAR','MOBILE','USER_ID','CREATE_BY','REAL_NAME','UPDATE_BY','JOB_NUMBER','LAST_LOGIN_IP','LOGIN_PASSWORD','DINGTALK_USER_ID','LAST_LOGIN_DEVICE','RELEVANT_TRIAL_ACCOUNT','SEX','USER_TYPE','ADD_SOURCE','USER_STATUS','IS_DIRECT_TYPE','IS_FIRST_START','ACCOUNT_STATUS','IS_SET_CLOSE_TIME','IS_FORMAL_ACCOUNT','IS_SET_FREEZE_TIME','IS_VIRTUAL_ACCOUNT','IS_DEFAULT_PWD_MODIFIED','CLOSE_TIME','HIRED_DATE','CREATE_TIME','FREEZE_TIME','UPDATE_TIME','REGULAR_DATE','DIMISSION_DATE','LAST_LOGIN_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 57 as fg,'LM_SINAN_ACTIVITY_REGISTRATION' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,ACT_NM,ACCT_ID,COMPANY,CITY_NAME,INDUSTRY,STORE_IDS,USERNAME,CARD_PHONE,POSTAR_EXT,UNION_CODE,MERCHANT_NO,POLICY_TYPE,REJECT_INFO,SIGN_REGION,CONTACT_NAME,PROVINCE_NAME,SIGN_PROVINCE,STORE_HEAD_IMG,PLATFORM_ORG_ID,POSTAR_AGE_NAME,STORE_MONEY_IMG,ACTIVITY_NUMBER,BRANCH_BANK_CODE,BRANCH_BANK_NAME,STORE_INSIDE_IMG,DETAILED_ADDRESS,MERCHANT_ADDRESS,OPEN_BANK_CARD_DATE,POSTAR_FALL_AGET_ID,LS_GOLD_ACTIVITY_CODE,PLATFORM_RESPONSE_ID,BUSINESS_LICENSE_NAME,CUSTOMER_MANAGER_CODE,CUSTOMER_MANAGER_NAME,ACTIVITY_TYPE_INDUSTRY_POLICY,UID,IS_DEL,BELONG,END_TIME,STORE_ID,START_TIME,IS_CARD_OPEN,IS_SHARE_PAY,IS_CUT_CHANNEL,ACTIVITY_STATUS,CARD_SETTLE_TYPE,IS_CHANGE_BANK_NO,BANK_APPLY_STATUS,LIQUIDATION_TYPE,HAS_NOTIFY_MERCHANT,ACTIVITY_REGISTRATION_TYPE,CREATE_TIME,UPDATE_TIME,SHARE_PAY_TIME,CUT_CHANNEL_TIME,EVERYDAY_TRADE' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'LM_SINAN_ACTIVITY_REGISTRATION'
            AND COLUMN_NAME in('ID','ACT_NM','ACCT_ID','COMPANY','CITY_NAME','INDUSTRY','STORE_IDS','USERNAME','CARD_PHONE','POSTAR_EXT','UNION_CODE','MERCHANT_NO','POLICY_TYPE','REJECT_INFO','SIGN_REGION','CONTACT_NAME','PROVINCE_NAME','SIGN_PROVINCE','STORE_HEAD_IMG','PLATFORM_ORG_ID','POSTAR_AGE_NAME','STORE_MONEY_IMG','ACTIVITY_NUMBER','BRANCH_BANK_CODE','BRANCH_BANK_NAME','STORE_INSIDE_IMG','DETAILED_ADDRESS','MERCHANT_ADDRESS','OPEN_BANK_CARD_DATE','POSTAR_FALL_AGET_ID','LS_GOLD_ACTIVITY_CODE','PLATFORM_RESPONSE_ID','BUSINESS_LICENSE_NAME','CUSTOMER_MANAGER_CODE','CUSTOMER_MANAGER_NAME','ACTIVITY_TYPE_INDUSTRY_POLICY','UID','IS_DEL','BELONG','END_TIME','STORE_ID','START_TIME','IS_CARD_OPEN','IS_SHARE_PAY','IS_CUT_CHANNEL','ACTIVITY_STATUS','CARD_SETTLE_TYPE','IS_CHANGE_BANK_NO','BANK_APPLY_STATUS','LIQUIDATION_TYPE','HAS_NOTIFY_MERCHANT','ACTIVITY_REGISTRATION_TYPE','CREATE_TIME','UPDATE_TIME','SHARE_PAY_TIME','CUT_CHANNEL_TIME','EVERYDAY_TRADE')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
