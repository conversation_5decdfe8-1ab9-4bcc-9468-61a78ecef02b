<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.mapper.DpAgentOperationDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.DpAgentOperationDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="UNION_ID" property="unionId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR" property="operator" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OPERATOR_NUMBER" property="operatorNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="VALID" property="valid" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="OPERATION_TYPE" property="operationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`UNION_ID`,`OPERATOR`,`REAL_NAME`,`JOB_NUMBER`,`OPERATOR_NUMBER`,`VALID`,`AGENT_ID`,`OPERATION_TYPE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:DP_AGENT_OPERATION-->
            <insert id="insert" >
            INSERT INTO DP_AGENT_OPERATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="unionId != null">`UNION_ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="realName != null">`REAL_NAME`,</if>
        <if test="jobNumber != null">`JOB_NUMBER`,</if>
        <if test="operatorNumber != null">`OPERATOR_NUMBER`,</if>
        <if test="valid != null">`VALID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="operationType != null">`OPERATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
        <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
        <if test="operatorNumber != null">#{operatorNumber,jdbcType=VARCHAR},</if>
        <if test="valid != null">#{valid,jdbcType=TINYINT},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="operationType != null">#{operationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据类型与代理商ID查询-->
            <select id="listByAgentIdsAndTypes" resultMap="BaseResultMap">
                    SELECT
        a.agent_id,
        a.operation_type,
        a.union_id,
        a.real_name,
        a.job_number,
        a.operator,
        a.operator_number
        FROM
        dp_agent_operation a
        WHERE
        a.valid = 1
        AND a.operation_type IN
        <foreach item="type" index="index" collection="operationTypeList" open="(" separator="," close=")">
            #{type,jdbcType=INTEGER}
        </foreach>
        AND a.agent_id IN
        <foreach item="agentId" index="index" collection="agentIdList" open="(" separator="," close=")">
            #{agentId,jdbcType=INTEGER}
        </foreach>
            </select>
    </mapper>
