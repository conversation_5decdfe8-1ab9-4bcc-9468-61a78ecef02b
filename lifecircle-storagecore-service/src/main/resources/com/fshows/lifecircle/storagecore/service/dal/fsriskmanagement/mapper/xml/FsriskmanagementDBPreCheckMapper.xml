<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsriskmanagement.mapper.FsriskmanagementDBPreCheckMapper">
    <select id="dbColumnCheck" resultType="String">
SELECT CONCAT('表结构不一致 tbName:',tb_name,' 期望字段:',exp_columns,' 数据库中字段:',db_columns) as msg
FROM (
            SELECT
            COUNT(*)= 18 as fg,'FK_RISK_MERCHANT' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,REMARK,TYPE_ID,MERCHANT_PUNISH,MERCHANT_RISK_TYPE,RISKINFO_INDIRECT_MER_DESC,DEAL_TIME,RIS<PERSON>_LEVEL,MERCHANT_ID,FIRST_SIX_LEVEL,SINGLE_OUT_DAYS,RISK_DEAL_STATUS,MERCHANT_RISK_LEVEL,CURRENT_ANT_RISK_RANK,RISKINFO_INDIRECT_MER_SCORE,CREATE_TIME,UPDATE_TIME,RISK_RANK_UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FK_RISK_MERCHANT'
            AND COLUMN_NAME in('ID','REMARK','TYPE_ID','MERCHANT_PUNISH','MERCHANT_RISK_TYPE','RISKINFO_INDIRECT_MER_DESC','DEAL_TIME','RISK_LEVEL','MERCHANT_ID','FIRST_SIX_LEVEL','SINGLE_OUT_DAYS','RISK_DEAL_STATUS','MERCHANT_RISK_LEVEL','CURRENT_ANT_RISK_RANK','RISKINFO_INDIRECT_MER_SCORE','CREATE_TIME','UPDATE_TIME','RISK_RANK_UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'FK_DATA_TYPE' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,NAME,DATA_TYPE_ID,DATA_TYPE_CODE,PLATFORM_TYPE,RISK_SOURCE_ID,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FK_DATA_TYPE'
            AND COLUMN_NAME in('ID','NAME','DATA_TYPE_ID','DATA_TYPE_CODE','PLATFORM_TYPE','RISK_SOURCE_ID','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 8 as fg,'FK_COLLECT_DATA' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,URL,FAIL_URL,DATA_TYPE_ID,COLLECT_DATA_ID,STATUS,CREATE_TIME,UPDATE_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FK_COLLECT_DATA'
            AND COLUMN_NAME in('ID','URL','FAIL_URL','DATA_TYPE_ID','COLLECT_DATA_ID','STATUS','CREATE_TIME','UPDATE_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 46 as fg,'FK_RISK_WORK_ORDER' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,LAST_PUSH_TIME,PUSH_DEAD_LINE,REMARK,USERNAME,AGENT_NAME,REJECT_MSG,ALIPAY_SMID,DATA_TYPE_ID,MARKET_NAME,MERCHANT_NO,WECHAT_SMID,COMPANY_NAME,ORG_REJECT_MSG,REVIEWER_NAME,SALESMAN_NAME,TICKET_NUMBER,SUBMITTER_NAME,APPEAL_TEMPLATE,WECHAT_CHANNEL_NO,WORK_ORDER_NUMBER,RELEVANCE_WORK_NUMBER,RISK_CONTROL_DIMENSION,IS_DEL,IS_PUSH,AGENT_ID,MARKET_ID,KA_DEAL_FLAG,MERCHANT_ID,PUSH_STATUS,SALESMAN_ID,ORDER_STATUS,TRADE_STATUS,RISK_RECORD_ID,SETTLE_STATUS,SUBMITTER_TYPE,WORK_ORDER_TYPE,ORG_REVIEW_STATUS,SUBMIT_ORG_STATUS,APPEAL_TEMPLATE_TYPE,CREATE_TIME,REVIEM_TIME,UPDATE_TIME,ORG_REVIEW_TIME,SUBMITTER_TIME,FUBEI_SUBMITTER_TIME' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FK_RISK_WORK_ORDER'
            AND COLUMN_NAME in('ID','LAST_PUSH_TIME','PUSH_DEAD_LINE','REMARK','USERNAME','AGENT_NAME','REJECT_MSG','ALIPAY_SMID','DATA_TYPE_ID','MARKET_NAME','MERCHANT_NO','WECHAT_SMID','COMPANY_NAME','ORG_REJECT_MSG','REVIEWER_NAME','SALESMAN_NAME','TICKET_NUMBER','SUBMITTER_NAME','APPEAL_TEMPLATE','WECHAT_CHANNEL_NO','WORK_ORDER_NUMBER','RELEVANCE_WORK_NUMBER','RISK_CONTROL_DIMENSION','IS_DEL','IS_PUSH','AGENT_ID','MARKET_ID','KA_DEAL_FLAG','MERCHANT_ID','PUSH_STATUS','SALESMAN_ID','ORDER_STATUS','TRADE_STATUS','RISK_RECORD_ID','SETTLE_STATUS','SUBMITTER_TYPE','WORK_ORDER_TYPE','ORG_REVIEW_STATUS','SUBMIT_ORG_STATUS','APPEAL_TEMPLATE_TYPE','CREATE_TIME','REVIEM_TIME','UPDATE_TIME','ORG_REVIEW_TIME','SUBMITTER_TIME','FUBEI_SUBMITTER_TIME')
            GROUP BY TABLE_NAME
                UNION ALL
            SELECT
            COUNT(*)= 31 as fg,'FK_RISK_RECORD' as tb_name,group_concat(COLUMN_NAME) db_columns,
            'ID,MCH_ID,REASON,ORDER_SN,RECORD_ID,RISK_TYPE,TRADE_TIME,DATA_TYPE_ID,DEAL_METHOD,ORG_TRADE_NO,PAYER_PHONE,PUNISH_PLAN,PUNISH_TIME,FUBEI_TRADE_NO,RISK_TYPE_DESC,COLLECT_DATA_ID,COMPLAINT_TIME,SINGLE_OUT_DAYS,PLATFORM_STATUS,COMPLAINT_DETAIL,PLATFORM_TRADE_NO,SERVICE_PROVIDER,PLATFORM_DEAL_TIME,PLATFORM_RISK_TYPE,PUNISH_DESCRIPTION,MSID_TYPE,MERCHANT_ID,USER_COMPLAINT_TIMES,CREATE_TIME,UPDATE_TIME,AMOUNT' exp_columns
            FROM INFORMATION_SCHEMA.COLUMNS
            WHERE
            TABLE_NAME = 'FK_RISK_RECORD'
            AND COLUMN_NAME in('ID','MCH_ID','REASON','ORDER_SN','RECORD_ID','RISK_TYPE','TRADE_TIME','DATA_TYPE_ID','DEAL_METHOD','ORG_TRADE_NO','PAYER_PHONE','PUNISH_PLAN','PUNISH_TIME','FUBEI_TRADE_NO','RISK_TYPE_DESC','COLLECT_DATA_ID','COMPLAINT_TIME','SINGLE_OUT_DAYS','PLATFORM_STATUS','COMPLAINT_DETAIL','PLATFORM_TRADE_NO','SERVICE_PROVIDER','PLATFORM_DEAL_TIME','PLATFORM_RISK_TYPE','PUNISH_DESCRIPTION','MSID_TYPE','MERCHANT_ID','USER_COMPLAINT_TIMES','CREATE_TIME','UPDATE_TIME','AMOUNT')
            GROUP BY TABLE_NAME
    )a
    WHERE fg=0
    </select>
    </mapper>
