<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyAlipaySchoolCanteenActivityApplyDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyAlipaySchoolCanteenActivityApplyDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="SMID" property="smid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCENE" property="scene" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="APPLY_ID" property="applyId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_CODE" property="areaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA_NAME" property="areaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NAME" property="cardName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_CODE" property="cityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CITY_NAME" property="cityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCHOOL_ID" property="schoolId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_INFO" property="rejectInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACTIVITY_TYPE" property="activityType" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INDUSTRY_CODE" property="industryCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_CODE" property="provinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE_NAME" property="provinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_BRANCH_NAME" property="bankBranchName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SCHOOL_FULL_NAME" property="schoolFullName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_IN_DOOR_IMG" property="storeInDoorImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_ENTRANCE_IMG" property="storeEntranceImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BUSINESS_LICENSE_PIC" property="businessLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_BACK_PIC" property="legalIdCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACCOUNT_PROVE_PIC" property="bankAccountProvePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_FRONT_PIC" property="legalIdCardFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FINANCE_STORE_IN_DOOR_PIC" property="financeStoreInDoorPic" jdbcType="VARCHAR"
        javaType="String"/>

    <result column="BANK_COOPERATION_AGREEMENT_PIC" property="bankCooperationAgreementPic" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="UID" property="uid" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="BELONG" property="belong" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="HAS_SHOP_ID" property="hasShopId" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SCHOOL_TYPE" property="schoolType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="SUBJECT_TYPE" property="subjectType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ACTIVITY_STATUS" property="activityStatus" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="ACTIVITY_OPEN_TIME" property="activityOpenTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="ACTIVITY_SIGN_TIME" property="activitySignTime" jdbcType="INTEGER"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>
</resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`SMID`,`SCENE`,`CARD_NO`,`APPLY_ID`,`COMPANY`,`AREA_CODE`,`AREA_NAME`,`CARD_NAME`,`CITY_CODE`,`CITY_NAME`,`SCHOOL_ID`,`USERNAME`,`MERCHANT_NO`,`REJECT_INFO`,`ACTIVITY_TYPE`,`INDUSTRY_CODE`,`PROVINCE_CODE`,`PROVINCE_NAME`,`BANK_BRANCH_NAME`,`SCHOOL_FULL_NAME`,`STORE_IN_DOOR_IMG`,`STORE_ENTRANCE_IMG`,`BUSINESS_LICENSE_PIC`,`LEGAL_ID_CARD_BACK_PIC`,`BANK_ACCOUNT_PROVE_PIC`,`LEGAL_ID_CARD_FRONT_PIC`,`FINANCE_STORE_IN_DOOR_PIC`,`BANK_COOPERATION_AGREEMENT_PIC`,`UID`,`BELONG`,`CHANNEL_ID`,`HAS_SHOP_ID`,`SCHOOL_TYPE`,`SUBJECT_TYPE`,`ACTIVITY_STATUS`,`ACTIVITY_OPEN_TIME`,`ACTIVITY_SIGN_TIME`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY-->
            <insert id="insert" >
                    INSERT INTO TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="smid != null">`SMID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="cardName != null">`CARD_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="rejectInfo != null">`REJECT_INFO`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
            <if test="industryCode != null">`INDUSTRY_CODE`,</if>
            <if test="bankBranchName != null">`BANK_BRANCH_NAME`,</if>
            <if test="storeInDoorImg != null">`STORE_IN_DOOR_IMG`,</if>
            <if test="storeEntranceImg != null">`STORE_ENTRANCE_IMG`,</if>
            <if test="businessLicensePic != null">`BUSINESS_LICENSE_PIC`,</if>
            <if test="legalIdCardBackPic != null">`LEGAL_ID_CARD_BACK_PIC`,</if>
            <if test="bankAccountProvePic != null">`BANK_ACCOUNT_PROVE_PIC`,</if>
            <if test="legalIdCardFrontPic != null">`LEGAL_ID_CARD_FRONT_PIC`,</if>
            <if test="bankCooperationAgreementPic != null">`BANK_COOPERATION_AGREEMENT_PIC`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="hasShopId != null">`HAS_SHOP_ID`,</if>
            <if test="schoolType != null">`SCHOOL_TYPE`,</if>
            <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
            <if test="activityOpenTime != null">`ACTIVITY_OPEN_TIME`,</if>
            <if test="activitySignTime != null">`ACTIVITY_SIGN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="smid != null">#{smid,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="rejectInfo != null">#{rejectInfo,jdbcType=VARCHAR},</if>
            <if test="activityType != null">#{activityType,jdbcType=VARCHAR},</if>
            <if test="industryCode != null">#{industryCode,jdbcType=VARCHAR},</if>
            <if test="bankBranchName != null">#{bankBranchName,jdbcType=VARCHAR},</if>
            <if test="storeInDoorImg != null">#{storeInDoorImg,jdbcType=VARCHAR},</if>
            <if test="storeEntranceImg != null">#{storeEntranceImg,jdbcType=VARCHAR},</if>
            <if test="businessLicensePic != null">#{businessLicensePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBackPic != null">#{legalIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="bankAccountProvePic != null">#{bankAccountProvePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardFrontPic != null">#{legalIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="bankCooperationAgreementPic != null">#{bankCooperationAgreementPic,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="hasShopId != null">#{hasShopId,jdbcType=TINYINT},</if>
            <if test="schoolType != null">#{schoolType,jdbcType=TINYINT},</if>
            <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
            <if test="activityOpenTime != null">#{activityOpenTime,jdbcType=INTEGER},</if>
            <if test="activitySignTime != null">#{activitySignTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--crm后台，查询支付宝间连高校食堂活动报名导出列表 pageCount-->
            <select id="findApplyListCount" resultType="int">
                    SELECT
          COUNT(*) AS total 
        FROM
        tp_alipay_school_canteen_activity_apply regist
        LEFT JOIN tp_users users
        on users.id = regist.uid
        <where>
            <if test="username != null and username != ''">
                and users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantNo != null">
                and regist.merchant_no = #{merchantNo,jdbcType=INTEGER}
            </if>
            <if test="company != null and company != ''">
                and users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="list != null and list.size() &gt; 0">
                and regist.belong in
                <foreach close=")" collection="list" index="index" item="belong" open="(" separator=",">
                    #{belong, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="uid != null">
                and users.id = #{uid,jdbcType=INTEGER}
            </if>
            <if test="activityStatus != null and activityStatus != -1">
                and regist.activity_status = #{activityStatus, jdbcType=INTEGER}
            </if>
            <if test="activityStatusList != null and activityStatusList.size() &gt; 0">
                and regist.activity_status in
                <foreach close=")" collection="activityStatusList" index="index" item="status" open="(" separator=",">
                    #{status, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="null != hasShopId and hasShopId != -1">
                and regist.has_shop_id = #{hasShopId, jdbcType=INTEGER}
            </if>
            <if test="activityOpenTimeEndTime != null ">
                AND regist.activity_open_time <![CDATA[ <= ]]>  #{activityOpenTimeEndTime,jdbcType=INTEGER}
            </if>
            <if test="activityOpenTimeBeginTime != null ">
                AND regist.activity_open_time <![CDATA[ >= ]]>  #{activityOpenTimeBeginTime,jdbcType=INTEGER}
            </if>
            <if test="null != subjectType and subjectType != -1">
                and regist.subject_type = #{subjectType, jdbcType=INTEGER}
            </if>
        </where>
        
            </select>
            <!--crm后台，查询支付宝间连高校食堂活动报名导出列表 pageResult-->
            <select id="findApplyListResult"  resultType="com.fshows.lifecircle.storagecore.service.domain.dto.FindApplyPageDTO">
                SELECT
                users.id as merchantId,
                users.username as username,
                regist.merchant_no as merchantNo,
                regist.smid as smid,
                regist.activity_sign_time as activitySignTime,
                IFNULL(regist.company, users.company) as company,
                regist.belong as belong,
                regist.has_shop_id as hasShopId,
                IFNULL(regist.activity_status, 1) as activityStatus,
                regist.activity_open_time as activityOpenTime,
                regist.subject_type as subjectType
                FROM
                tp_alipay_school_canteen_activity_apply regist
                LEFT JOIN tp_users users
                on users.id = regist.uid
                <where>
            <if test="username != null and username != ''">
                and users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantNo != null">
                and regist.merchant_no = #{merchantNo,jdbcType=INTEGER}
            </if>
            <if test="company != null and company != ''">
                and users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="list != null and list.size() &gt; 0">
                and regist.belong in
                <foreach close=")" collection="list" index="index" item="belong" open="(" separator=",">
                    #{belong, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="uid != null">
                and users.id = #{uid,jdbcType=INTEGER}
            </if>
            <if test="activityStatus != null and activityStatus != -1">
                and regist.activity_status = #{activityStatus, jdbcType=INTEGER}
            </if>
            <if test="activityStatusList != null and activityStatusList.size() &gt; 0">
                and regist.activity_status in
                <foreach close=")" collection="activityStatusList" index="index" item="status" open="(" separator=",">
                    #{status, jdbcType=INTEGER}
                </foreach>
            </if>
                    <if test="null != hasShopId and hasShopId != -1">
                        and regist.has_shop_id = #{hasShopId, jdbcType=INTEGER}
                    </if>
                    <if test="activityOpenTimeEndTime != null ">
                        AND regist.activity_open_time <![CDATA[ <= ]]>  #{activityOpenTimeEndTime,jdbcType=INTEGER}
                    </if>
                    <if test="activityOpenTimeBeginTime != null ">
                        AND regist.activity_open_time <![CDATA[ >= ]]>  #{activityOpenTimeBeginTime,jdbcType=INTEGER}
                    </if>
                    <if test="null != subjectType and subjectType != -1">
                        and regist.subject_type = #{subjectType, jdbcType=INTEGER}
                    </if>
                </where>
        order by regist.`create_time` DESC
            limit #{startRow},#{limit}
            </select>
    </mapper>
