<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.mapper.PgShareOrderDetailReconciliationDOMapper">

    <resultMap id="BaseResultMap"
               type="com.fshows.lifecircle.storagecore.service.dal.fsBankPlatformGuardian.dataobject.PgShareOrderDetailReconciliationDO">
        <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

        <result column="IS_DEL" property="isDel" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="TOKEN" property="token" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FAIL_TYPE" property="failType" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="DEAL_STATE" property="dealState" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="CUSTOMER_ID" property="customerId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="MERCHANT_ID" property="merchantId" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="SHARE_REQ_NO" property="shareReqNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="FB_FINISH_TIME" property="fbFinishTime" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PLATFORM_REQ_NO" property="platformReqNo" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="PLATFORM_FINISH_TIME" property="platformFinishTime" jdbcType="VARCHAR"
                javaType="String"/>

        <result column="UID" property="uid" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CHANNEL" property="channel" jdbcType="TINYINT"
                javaType="Integer"/>

        <result column="HANDLE_DATE" property="handleDate" jdbcType="INTEGER"
                javaType="Integer"/>

        <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                javaType="java.util.Date"/>

        <result column="FB_TOTAL_AMOUNT" property="fbTotalAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>

        <result column="PLATFORM_TOTAL_AMOUNT" property="platformTotalAmount" jdbcType="DECIMAL"
                javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
        `ID`
        ,`IS_DEL`,`TOKEN`,`ORDER_SN`,`FAIL_TYPE`,`DEAL_STATE`,`CUSTOMER_ID`,`MERCHANT_ID`,`SHARE_REQ_NO`,`FB_FINISH_TIME`,`PLATFORM_REQ_NO`,`PLATFORM_FINISH_TIME`,`UID`,`CHANNEL`,`HANDLE_DATE`,`CREATE_TIME`,`UPDATE_TIME`,`FB_TOTAL_AMOUNT`,`PLATFORM_TOTAL_AMOUNT`
    </sql>


    <!--insert:PG_SHARE_ORDER_DETAIL_RECONCILIATION-->
    <insert id="insert">
        INSERT INTO PG_SHARE_ORDER_DETAIL_RECONCILIATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="failType != null">`FAIL_TYPE`,</if>
            <if test="dealState != null">`DEAL_STATE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="handleDate != null">`HANDLE_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbFinishTime != null">`FB_FINISH_TIME`,</if>
            <if test="platformFinishTime != null">`PLATFORM_FINISH_TIME`,</if>
            <if test="fbTotalAmount != null">`FB_TOTAL_AMOUNT`,</if>
            <if test="platformTotalAmount != null">`PLATFORM_TOTAL_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="failType != null">#{failType,jdbcType=VARCHAR},</if>
            <if test="dealState != null">#{dealState,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="handleDate != null">#{handleDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbFinishTime != null">#{fbFinishTime,jdbcType=VARCHAR},</if>
            <if test="platformFinishTime != null">#{platformFinishTime,jdbcType=VARCHAR},</if>
            <if test="fbTotalAmount != null">#{fbTotalAmount,jdbcType=DECIMAL},</if>
            <if test="platformTotalAmount != null">#{platformTotalAmount,jdbcType=DECIMAL},</if>
        </trim>
    </insert>
</mapper>
