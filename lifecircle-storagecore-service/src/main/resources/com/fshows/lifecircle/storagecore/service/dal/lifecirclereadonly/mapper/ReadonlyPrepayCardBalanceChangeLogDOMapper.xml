<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyPrepayCardBalanceChangeLogDOMapper">

<resultMap id="BaseResultMap"
           type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyPrepayCardBalanceChangeLogDO">
    <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

    <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CHANGE_REMARK" property="changeRemark" jdbcType="VARCHAR"
            javaType="String"/>

    <result column="CHANGE_TYPE" property="changeType" jdbcType="TINYINT"
            javaType="Integer"/>

    <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
            javaType="java.util.Date"/>

    <result column="AFTER_BALANCE" property="afterBalance" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="CHANGE_BALANCE" property="changeBalance" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="AFTER_TODAY_CONSUME_AMOUNT" property="afterTodayConsumeAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>

    <result column="CHANGE_TODAY_CONSUME_AMOUNT" property="changeTodayConsumeAmount" jdbcType="DECIMAL"
            javaType="java.math.BigDecimal"/>
</resultMap>


        <sql id="Base_Column_List">
    `ID`,`CARD_NO`,`ORDER_SN`,`CHANGE_REMARK`,`CHANGE_TYPE`,`CREATE_TIME`,`UPDATE_TIME`,`AFTER_BALANCE`,`CHANGE_BALANCE`,`AFTER_TODAY_CONSUME_AMOUNT`,`CHANGE_TODAY_CONSUME_AMOUNT`
    </sql>


        <!--insert:TP_PREPAY_CARD_BALANCE_CHANGE_LOG-->
        <insert id="insert">
            INSERT INTO TP_PREPAY_CARD_BALANCE_CHANGE_LOG
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="cardNo != null">`CARD_NO`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="changeRemark != null">`CHANGE_REMARK`,</if>
                <if test="changeType != null">`CHANGE_TYPE`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="afterBalance != null">`AFTER_BALANCE`,</if>
                <if test="changeBalance != null">`CHANGE_BALANCE`,</if>
                <if test="afterTodayConsumeAmount != null">`AFTER_TODAY_CONSUME_AMOUNT`,</if>
                <if test="changeTodayConsumeAmount != null">`CHANGE_TODAY_CONSUME_AMOUNT`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=BIGINT},</if>
                <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
                <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
                <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
                <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="afterBalance != null">#{afterBalance,jdbcType=DECIMAL},</if>
                <if test="changeBalance != null">#{changeBalance,jdbcType=DECIMAL},</if>
                <if test="afterTodayConsumeAmount != null">#{afterTodayConsumeAmount,jdbcType=DECIMAL},</if>
                <if test="changeTodayConsumeAmount != null">#{changeTodayConsumeAmount,jdbcType=DECIMAL},</if>
            </trim>
        </insert>

        <!--查询指定日期的余额变更日志-->
        <select id="findBalanceChangeLogByCardNoList" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM tp_prepay_card_balance_change_log
            where order_sn in
            <foreach close=")" collection="list" index="index" item="orderSn" open="(" separator=",">
                #{orderSn, jdbcType=VARCHAR}
            </foreach>
            and create_time <![CDATA[ >= ]]> #{startTime, jdbcType=TIMESTAMP}
            and create_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
        </select>
    </mapper>
