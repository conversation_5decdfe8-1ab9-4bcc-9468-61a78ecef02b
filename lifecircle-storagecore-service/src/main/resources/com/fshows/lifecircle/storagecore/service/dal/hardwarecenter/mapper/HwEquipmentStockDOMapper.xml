<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwEquipmentStockDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwEquipmentStockDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="CREATER" property="creater" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="JOB_NUMBER" property="jobNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STOCK_ORDER" property="stockOrder" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORAGE_ORDER" property="storageOrder" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="DEPOT" property="depot" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_NUM" property="orderNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="EXECUTE_TIME" property="executeTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>

        <resultMap id="EquipmentSnPurchaseExportMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentSnPurchaseExportMap">

            <result column="stock_order" property="stockOrder" javaType="java.lang.String"/>

            <result column="equipment_sn" property="equipmentSn" javaType="java.lang.String"/>

            <result column="equipment_name" property="equipmentName" javaType="java.lang.String"/>

            <result column="equipment_model" property="equipmentModel" javaType="java.lang.String"/>

            <result column="depot" property="depot" javaType="java.lang.Integer"/>
        </resultMap>

        <sql id="Base_Column_List">
            `ID`,`CREATER`,`JOB_NUMBER`,`STOCK_ORDER`,`STORAGE_ORDER`,`DEPOT`,`ORDER_NUM`,`CREATE_TIME`,`UPDATE_TIME`,`EXECUTE_TIME`
        </sql>


        <!--insert:HW_EQUIPMENT_STOCK-->
        <insert id="insert">
            INSERT INTO HW_EQUIPMENT_STOCK
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="creater != null">`CREATER`,</if>
                <if test="jobNumber != null">`JOB_NUMBER`,</if>
                <if test="stockOrder != null">`STOCK_ORDER`,</if>
                <if test="storageOrder != null">`STORAGE_ORDER`,</if>
                <if test="orderNum != null">`ORDER_NUM`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
            </trim>
        VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
                <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
                <if test="stockOrder != null">#{stockOrder,jdbcType=VARCHAR},</if>
                <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
                <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            </trim>
        </insert>

        <!--订单采购入库查询-->
        <select id="getPurchaseOrderList" resultMap="BaseResultMap">
            select /*MS-HW-EQUIPMENT-STOCK-GETPURCHASEORDERLIST*/
            <include refid="Base_Column_List"/>
            from hw_equipment_stock
            where execute_time BETWEEN #{startTime,jdbcType=VARCHAR}
            AND #{endTime,jdbcType=VARCHAR}
            and depot in
            <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
                #{depot,jdbcType=INTEGER}
            </foreach>
        </select>

        <!--设备采购入库查询-->
        <select id="getPurchaseEquipmentList" resultMap="EquipmentSnPurchaseExportMap">
            select /*MS-HW-EQUIPMENT-STOCK-GETPURCHASEEQUIPMENTLIST*/ c.system_sn
            equipment_sn,d.equipment_name,d.equipment_model,a.stock_order,a.depot
            from hw_equipment_stock a
            LEFT JOIN hw_equipment_order_relation b on a.stock_order = b.stock_order
            LEFT JOIN hw_equipment_sn c on b.sn_id = c.id and c.is_del = 0
            LEFT JOIN hw_equipment d on c.equipment_id = d.id
            where a.execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
            and a.depot in
            <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
                #{depot,jdbcType=INTEGER}
            </foreach>
            and b.is_del = 0
        </select>

        <!--查询采购入库单信息-->
        <select id="getPurchaseStockOrderInfo" resultMap="BaseResultMap">
            select /*MS-HW-EQUIPMENT-STOCK-GETPURCHASESTOCKORDERINFO*/
            <include refid="Base_Column_List"/>
            from hw_equipment_stock
            where stock_order = #{stockOrder,jdbcType=VARCHAR}
        </select>
    </mapper>
