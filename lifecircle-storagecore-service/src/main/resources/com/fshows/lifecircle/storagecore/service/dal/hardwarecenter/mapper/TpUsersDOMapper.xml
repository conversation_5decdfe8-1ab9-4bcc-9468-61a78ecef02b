<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpUsersDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpUsersDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INDIRECT" property="indirect" jdbcType="DOUBLE"
        javaType="Double"/>

            <result column="SALESPERCENT" property="salespercent" jdbcType="DOUBLE"
        javaType="Double"/>

            <result column="DISTRIBUTOR_SALESPERCENT" property="distributorSalespercent" jdbcType="DOUBLE"
        javaType="Double"/>

            <result column="MP" property="mp" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QQ" property="qq" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="AREA" property="area" jdbcType="CHAR"
        javaType="String"/>

            <result column="CITY" property="city" jdbcType="CHAR"
        javaType="String"/>

            <result column="EMAIL" property="email" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PHONE" property="phone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LASTIP" property="lastip" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MOBILE" property="mobile" jdbcType="CHAR"
        javaType="String"/>

            <result column="PEOPLE" property="people" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QRCODE" property="qrcode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STATUS" property="status" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY" property="company" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT" property="contact" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_NAME" property="bankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_USER" property="bankUser" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CREATEIP" property="createip" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LASTTIME" property="lasttime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PASSWORD" property="password" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLUGSAVE" property="plugsave" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PROVINCE" property="province" jdbcType="CHAR"
        javaType="String"/>

            <result column="REAL_NAME" property="realName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERNAME" property="username" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAYNUM" property="alipaynum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACOUNT" property="bankAcount" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="INDUSTRY_ID" property="industryId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERS_TOKEN" property="usersToken" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FUND_PASSWORD" property="fundPassword" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANYADDRESS" property="companyaddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="USERS_HEADERPIC" property="usersHeaderpic" jdbcType="CHAR"
        javaType="String"/>

            <result column="PROTOCOL_VERSION" property="protocolVersion" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FUND_PASSWORD_SALT" property="fundPasswordSalt" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_DEFAULT_LOGO" property="storeDefaultLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GID" property="gid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FOCUS" property="focus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MONEY" property="money" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AMOUNT" property="amount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BELONG" property="belong" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DIYNUM" property="diynum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ISPUSH" property="ispush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ROLE_ID" property="roleId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="VENDOR" property="vendor" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="API_USER" property="apiUser" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_NUM" property="cardNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_APPLY" property="isApply" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_JDPAY" property="isJdpay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UNION_ID" property="unionId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="VIPTIME" property="viptime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_ALIPAY" property="isAlipay" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MARKET_ID" property="marketId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PARENT_ID" property="parentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PAY_LIMIT" property="payLimit" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PLATFORM" property="platform" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SALESMAN" property="salesman" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WX_STATUS" property="wxStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="APPLY_TIME" property="applyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CONFIRM" property="isConfirm" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VERSION_ID" property="versionId" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIG_TYPE" property="configType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CONNECTNUM" property="connectnum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATETIME" property="createtime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="DEALAMOUNT" property="dealamount" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_GROUP_BUY" property="isGroupBuy" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_OPEN_MINA" property="isOpenMina" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_PROTOCOL" property="isProtocol" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LOAN_STATUS" property="loanStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ONLINETIME" property="onlinetime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PROTOCOL_ID" property="protocolId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALER_AUDIT" property="salerAudit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="VOICE_ON_OFF" property="voiceOnOff" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ACTIVITYNUM" property="activitynum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASHOUT_LOCK" property="cashoutLock" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CONFIRM_TIME" property="confirmTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_QUICK_CASH" property="isQuickCash" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MCARD_STATUS" property="mcardStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_CONFIG_ID" property="subConfigId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TOTALSMSNUM" property="totalsmsnum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="INCOME_STATUS" property="incomeStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_TYPE" property="merchantType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PROTOCOL_TIME" property="protocolTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="PS_MODIFY_TIME" property="psModifyTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TOTALSMSUSED" property="totalsmsused" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRANSFER_TIME" property="transferTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SCAN_SERVICE" property="isScanService" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="ISEMSCNPLPUSH" property="isemscnplpush" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PW_RESET_STATUS" property="pwResetStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RECHARGE_LIMIT" property="rechargeLimit" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="WECHAT_CARD_NUM" property="wechatCardNum" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="ATTACHMENTSIZE" property="attachmentsize" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTO_WITHDRAWAL" property="autoWithdrawal" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LASTLOGINMONTH" property="lastloginmonth" jdbcType="SMALLINT"
        javaType="Integer"/>

            <result column="LIFECIRCLE_TIME" property="lifecircleTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CARD_CREATE_STATUS" property="cardCreateStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LATESTONLINETIME" property="latestonlinetime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_SERVICENO_ACCESS" property="isServicenoAccess" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="FINANCE" property="finance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INDIRECT`,`SALESPERCENT`,`DISTRIBUTOR_SALESPERCENT`,`MP`,`QQ`,`AREA`,`CITY`,`EMAIL`,`PHONE`,`LASTIP`,`MOBILE`,`PEOPLE`,`QRCODE`,`STATUS`,`ADDRESS`,`COMPANY`,`CONTACT`,`STORE_ID`,`BANK_NAME`,`BANK_USER`,`CREATEIP`,`LASTTIME`,`PASSWORD`,`PLUGSAVE`,`PROVINCE`,`REAL_NAME`,`USERNAME`,`ALIPAYNUM`,`BANK_ACOUNT`,`INDUSTRY_ID`,`USERS_TOKEN`,`FUND_PASSWORD`,`COMPANYADDRESS`,`USERS_HEADERPIC`,`PROTOCOL_VERSION`,`FUND_PASSWORD_SALT`,`STORE_DEFAULT_LOGO`,`GID`,`FOCUS`,`MONEY`,`AMOUNT`,`BELONG`,`DIYNUM`,`ISPUSH`,`ROLE_ID`,`VENDOR`,`API_USER`,`CARD_NUM`,`IS_APPLY`,`IS_JDPAY`,`UNION_ID`,`VIPTIME`,`IS_ALIPAY`,`MARKET_ID`,`PARENT_ID`,`PAY_LIMIT`,`PLATFORM`,`SALESMAN`,`WX_STATUS`,`APPLY_TIME`,`IS_CONFIRM`,`VERSION_ID`,`CONFIG_TYPE`,`CONNECTNUM`,`CREATETIME`,`DEALAMOUNT`,`IS_GROUP_BUY`,`IS_OPEN_MINA`,`IS_PROTOCOL`,`LOAN_STATUS`,`ONLINETIME`,`PROTOCOL_ID`,`SALER_AUDIT`,`VOICE_ON_OFF`,`ACTIVITYNUM`,`CASHOUT_LOCK`,`CONFIRM_TIME`,`IS_QUICK_CASH`,`MCARD_STATUS`,`SUB_CONFIG_ID`,`TOTALSMSNUM`,`INCOME_STATUS`,`MERCHANT_TYPE`,`PROTOCOL_TIME`,`PS_MODIFY_TIME`,`TOTALSMSUSED`,`TRANSFER_TIME`,`IS_SCAN_SERVICE`,`ISEMSCNPLPUSH`,`PW_RESET_STATUS`,`RECHARGE_LIMIT`,`WECHAT_CARD_NUM`,`ATTACHMENTSIZE`,`AUTO_WITHDRAWAL`,`LASTLOGINMONTH`,`LIFECIRCLE_TIME`,`LIQUIDATION_TYPE`,`CARD_CREATE_STATUS`,`LATESTONLINETIME`,`IS_SERVICENO_ACCESS`,`UPDATE_TIME`,`FINANCE`
    </sql>


            <!--insert:TP_USERS-->
            <insert id="insert" >
                    INSERT INTO TP_USERS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="indirect != null">`INDIRECT`,</if>
            <if test="salespercent != null">`SALESPERCENT`,</if>
            <if test="distributorSalespercent != null">`DISTRIBUTOR_SALESPERCENT`,</if>
            <if test="mp != null">`MP`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="lastip != null">`LASTIP`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="people != null">`PEOPLE`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="contact != null">`CONTACT`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="bankUser != null">`BANK_USER`,</if>
            <if test="createip != null">`CREATEIP`,</if>
            <if test="lasttime != null">`LASTTIME`,</if>
            <if test="password != null">`PASSWORD`,</if>
            <if test="plugsave != null">`PLUGSAVE`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="alipaynum != null">`ALIPAYNUM`,</if>
            <if test="bankAcount != null">`BANK_ACOUNT`,</if>
            <if test="industryId != null">`INDUSTRY_ID`,</if>
            <if test="usersToken != null">`USERS_TOKEN`,</if>
            <if test="fundPassword != null">`FUND_PASSWORD`,</if>
            <if test="companyaddress != null">`COMPANYADDRESS`,</if>
            <if test="usersHeaderpic != null">`USERS_HEADERPIC`,</if>
            <if test="protocolVersion != null">`PROTOCOL_VERSION`,</if>
            <if test="fundPasswordSalt != null">`FUND_PASSWORD_SALT`,</if>
            <if test="storeDefaultLogo != null">`STORE_DEFAULT_LOGO`,</if>
            <if test="gid != null">`GID`,</if>
            <if test="focus != null">`FOCUS`,</if>
            <if test="money != null">`MONEY`,</if>
            <if test="amount != null">`AMOUNT`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="diynum != null">`DIYNUM`,</if>
            <if test="ispush != null">`ISPUSH`,</if>
            <if test="roleId != null">`ROLE_ID`,</if>
            <if test="vendor != null">`VENDOR`,</if>
            <if test="apiUser != null">`API_USER`,</if>
            <if test="cardNum != null">`CARD_NUM`,</if>
            <if test="isApply != null">`IS_APPLY`,</if>
            <if test="isJdpay != null">`IS_JDPAY`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="viptime != null">`VIPTIME`,</if>
            <if test="isAlipay != null">`IS_ALIPAY`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="payLimit != null">`PAY_LIMIT`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="salesman != null">`SALESMAN`,</if>
            <if test="wxStatus != null">`WX_STATUS`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="isConfirm != null">`IS_CONFIRM`,</if>
            <if test="versionId != null">`VERSION_ID`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="connectnum != null">`CONNECTNUM`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="dealamount != null">`DEALAMOUNT`,</if>
            <if test="isGroupBuy != null">`IS_GROUP_BUY`,</if>
            <if test="isOpenMina != null">`IS_OPEN_MINA`,</if>
            <if test="isProtocol != null">`IS_PROTOCOL`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="onlinetime != null">`ONLINETIME`,</if>
            <if test="protocolId != null">`PROTOCOL_ID`,</if>
            <if test="salerAudit != null">`SALER_AUDIT`,</if>
            <if test="voiceOnOff != null">`VOICE_ON_OFF`,</if>
            <if test="activitynum != null">`ACTIVITYNUM`,</if>
            <if test="cashoutLock != null">`CASHOUT_LOCK`,</if>
            <if test="confirmTime != null">`CONFIRM_TIME`,</if>
            <if test="isQuickCash != null">`IS_QUICK_CASH`,</if>
            <if test="mcardStatus != null">`MCARD_STATUS`,</if>
            <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
            <if test="totalsmsnum != null">`TOTALSMSNUM`,</if>
            <if test="incomeStatus != null">`INCOME_STATUS`,</if>
            <if test="protocolTime != null">`PROTOCOL_TIME`,</if>
            <if test="psModifyTime != null">`PS_MODIFY_TIME`,</if>
            <if test="totalsmsused != null">`TOTALSMSUSED`,</if>
            <if test="transferTime != null">`TRANSFER_TIME`,</if>
            <if test="isScanService != null">`IS_SCAN_SERVICE`,</if>
            <if test="isemscnplpush != null">`ISEMSCNPLPUSH`,</if>
            <if test="pwResetStatus != null">`PW_RESET_STATUS`,</if>
            <if test="rechargeLimit != null">`RECHARGE_LIMIT`,</if>
            <if test="wechatCardNum != null">`WECHAT_CARD_NUM`,</if>
            <if test="attachmentsize != null">`ATTACHMENTSIZE`,</if>
            <if test="autoWithdrawal != null">`AUTO_WITHDRAWAL`,</if>
            <if test="lastloginmonth != null">`LASTLOGINMONTH`,</if>
            <if test="lifecircleTime != null">`LIFECIRCLE_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="cardCreateStatus != null">`CARD_CREATE_STATUS`,</if>
            <if test="latestonlinetime != null">`LATESTONLINETIME`,</if>
            <if test="isServicenoAccess != null">`IS_SERVICENO_ACCESS`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finance != null">`FINANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="indirect != null">#{indirect,jdbcType=DOUBLE},</if>
            <if test="salespercent != null">#{salespercent,jdbcType=DOUBLE},</if>
            <if test="distributorSalespercent != null">#{distributorSalespercent,jdbcType=DOUBLE},</if>
            <if test="mp != null">#{mp,jdbcType=VARCHAR},</if>
            <if test="qq != null">#{qq,jdbcType=VARCHAR},</if>
            <if test="area != null">#{area,jdbcType=CHAR},</if>
            <if test="city != null">#{city,jdbcType=CHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="lastip != null">#{lastip,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=CHAR},</if>
            <if test="people != null">#{people,jdbcType=VARCHAR},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="contact != null">#{contact,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="bankUser != null">#{bankUser,jdbcType=VARCHAR},</if>
            <if test="createip != null">#{createip,jdbcType=VARCHAR},</if>
            <if test="lasttime != null">#{lasttime,jdbcType=VARCHAR},</if>
            <if test="password != null">#{password,jdbcType=VARCHAR},</if>
            <if test="plugsave != null">#{plugsave,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=CHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="alipaynum != null">#{alipaynum,jdbcType=VARCHAR},</if>
            <if test="bankAcount != null">#{bankAcount,jdbcType=VARCHAR},</if>
            <if test="industryId != null">#{industryId,jdbcType=VARCHAR},</if>
            <if test="usersToken != null">#{usersToken,jdbcType=VARCHAR},</if>
            <if test="fundPassword != null">#{fundPassword,jdbcType=VARCHAR},</if>
            <if test="companyaddress != null">#{companyaddress,jdbcType=VARCHAR},</if>
            <if test="usersHeaderpic != null">#{usersHeaderpic,jdbcType=CHAR},</if>
            <if test="protocolVersion != null">#{protocolVersion,jdbcType=VARCHAR},</if>
            <if test="fundPasswordSalt != null">#{fundPasswordSalt,jdbcType=VARCHAR},</if>
            <if test="storeDefaultLogo != null">#{storeDefaultLogo,jdbcType=VARCHAR},</if>
            <if test="gid != null">#{gid,jdbcType=INTEGER},</if>
            <if test="focus != null">#{focus,jdbcType=TINYINT},</if>
            <if test="money != null">#{money,jdbcType=INTEGER},</if>
            <if test="amount != null">#{amount,jdbcType=INTEGER},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="diynum != null">#{diynum,jdbcType=INTEGER},</if>
            <if test="ispush != null">#{ispush,jdbcType=INTEGER},</if>
            <if test="roleId != null">#{roleId,jdbcType=INTEGER},</if>
            <if test="vendor != null">#{vendor,jdbcType=INTEGER},</if>
            <if test="apiUser != null">#{apiUser,jdbcType=TINYINT},</if>
            <if test="cardNum != null">#{cardNum,jdbcType=INTEGER},</if>
            <if test="isApply != null">#{isApply,jdbcType=TINYINT},</if>
            <if test="isJdpay != null">#{isJdpay,jdbcType=TINYINT},</if>
            <if test="unionId != null">#{unionId,jdbcType=INTEGER},</if>
            <if test="viptime != null">#{viptime,jdbcType=INTEGER},</if>
            <if test="isAlipay != null">#{isAlipay,jdbcType=TINYINT},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="payLimit != null">#{payLimit,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="salesman != null">#{salesman,jdbcType=INTEGER},</if>
            <if test="wxStatus != null">#{wxStatus,jdbcType=TINYINT},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="isConfirm != null">#{isConfirm,jdbcType=TINYINT},</if>
            <if test="versionId != null">#{versionId,jdbcType=TINYINT},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="connectnum != null">#{connectnum,jdbcType=INTEGER},</if>
            <if test="createtime != null">#{createtime,jdbcType=INTEGER},</if>
            <if test="dealamount != null">#{dealamount,jdbcType=INTEGER},</if>
            <if test="isGroupBuy != null">#{isGroupBuy,jdbcType=TINYINT},</if>
            <if test="isOpenMina != null">#{isOpenMina,jdbcType=TINYINT},</if>
            <if test="isProtocol != null">#{isProtocol,jdbcType=INTEGER},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=TINYINT},</if>
            <if test="onlinetime != null">#{onlinetime,jdbcType=INTEGER},</if>
            <if test="protocolId != null">#{protocolId,jdbcType=INTEGER},</if>
            <if test="salerAudit != null">#{salerAudit,jdbcType=TINYINT},</if>
            <if test="voiceOnOff != null">#{voiceOnOff,jdbcType=TINYINT},</if>
            <if test="activitynum != null">#{activitynum,jdbcType=INTEGER},</if>
            <if test="cashoutLock != null">#{cashoutLock,jdbcType=TINYINT},</if>
            <if test="confirmTime != null">#{confirmTime,jdbcType=INTEGER},</if>
            <if test="isQuickCash != null">#{isQuickCash,jdbcType=TINYINT},</if>
            <if test="mcardStatus != null">#{mcardStatus,jdbcType=TINYINT},</if>
            <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
            <if test="totalsmsnum != null">#{totalsmsnum,jdbcType=INTEGER},</if>
            <if test="incomeStatus != null">#{incomeStatus,jdbcType=TINYINT},</if>
            <if test="protocolTime != null">#{protocolTime,jdbcType=INTEGER},</if>
            <if test="psModifyTime != null">#{psModifyTime,jdbcType=INTEGER},</if>
            <if test="totalsmsused != null">#{totalsmsused,jdbcType=INTEGER},</if>
            <if test="transferTime != null">#{transferTime,jdbcType=INTEGER},</if>
            <if test="isScanService != null">#{isScanService,jdbcType=TINYINT},</if>
            <if test="isemscnplpush != null">#{isemscnplpush,jdbcType=TINYINT},</if>
            <if test="pwResetStatus != null">#{pwResetStatus,jdbcType=TINYINT},</if>
            <if test="rechargeLimit != null">#{rechargeLimit,jdbcType=INTEGER},</if>
            <if test="wechatCardNum != null">#{wechatCardNum,jdbcType=INTEGER},</if>
            <if test="attachmentsize != null">#{attachmentsize,jdbcType=INTEGER},</if>
            <if test="autoWithdrawal != null">#{autoWithdrawal,jdbcType=TINYINT},</if>
            <if test="lastloginmonth != null">#{lastloginmonth,jdbcType=SMALLINT},</if>
            <if test="lifecircleTime != null">#{lifecircleTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="cardCreateStatus != null">#{cardCreateStatus,jdbcType=TINYINT},</if>
            <if test="latestonlinetime != null">#{latestonlinetime,jdbcType=INTEGER},</if>
            <if test="isServicenoAccess != null">#{isServicenoAccess,jdbcType=TINYINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finance != null">#{finance,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据id获取用户信息-->
            <select id="getById" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USERS-GETBYID*/  <include refid="Base_Column_List" />
        from tp_users
        where id = #{id,jdbcType=INTEGER}
            </select>

            <!--根据商户名查询商户id-->
            <select id="findUidListByBelongAndName" resultType="java.lang.Integer">
                    SELECT
        `ID`
        FROM
        TP_USERS
        WHERE
        PARENT_ID = 0
        AND USERNAME like CONCAT(#{username,jdbcType=VARCHAR},'%')
        <if test="belong != null">AND BELONG = #{belong,jdbcType=INTEGER}</if>
            </select>

            <!--查询商户id和商户账号集合-->
            <select id="findMerchantListByIdList" resultMap="BaseResultMap">
                    SELECT
        `ID`,USERNAME
        FROM
        TP_USERS
        WHERE
        `ID` IN
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--查询商户账户和商编-->
            <select id="findMerchantListByAgentId" resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.IdAndUserNameResultMap">
                    select /*MS-TP-USERS-FINDMERCHANTLISTBYAGENTID*/ id as id,username as username
        from tp_users
        where belong = #{belong,jdbcType=INTEGER}
            </select>

            <!---->
            <select id="getByUidList" resultType="com.fshows.lifecircle.storagecore.service.domain.dto.MerchantAgentDTO">
                    SELECT
        a.id uid,
        a.username AS username,
        a.company AS company,
        b.id AS agentId,
        b.username AS agentUsername,
        c.id AS salesmanId,
        c.username as salesmanUsername
        FROM
        tp_users a
        LEFT JOIN tp_user b ON a.belong = b.id
        LEFT JOIN tp_user c ON a.salesman = c.id
        where a.id in
        <foreach collection="list" item="uid" index="index" open="(" close=")" separator=",">
            #{uid,jdbcType=INTEGER}
        </foreach>
            </select>
    </mapper>
