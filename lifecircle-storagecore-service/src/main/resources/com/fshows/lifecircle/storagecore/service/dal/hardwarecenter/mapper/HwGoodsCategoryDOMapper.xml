<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.HwGoodsCategoryDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.HwGoodsCategoryDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="REMARK" property="remark" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PID" property="pid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SORT" property="sort" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CATEGORY_STATUS" property="categoryStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`REMARK`,`CATEGORY_NAME`,`PID`,`SORT`,`IS_DEL`,`CATEGORY_STATUS`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:HW_GOODS_CATEGORY-->
            <insert id="insert" >
            INSERT INTO HW_GOODS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="categoryName != null">`CATEGORY_NAME`,</if>
        <if test="pid != null">`PID`,</if>
        <if test="sort != null">`SORT`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="categoryStatus != null">`CATEGORY_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
        <if test="pid != null">#{pid,jdbcType=INTEGER},</if>
        <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="categoryStatus != null">#{categoryStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--获取关联数据的全部数据-->
            <select id="getByIdIn" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        hw_goods_category
        where
        id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and
        is_del = 0
            </select>

            <!--获取全部子数据-->
            <select id="getByPid" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        hw_goods_category
        where
        id = #{pid,jdbcType=INTEGER}
        and
        is_del = 0
            </select>

            <!--获取全部子数据-->
            <select id="getChildData" resultMap="BaseResultMap">
                    select
        <include refid="Base_Column_List" />
        from
        hw_goods_category
        where
        pid = #{id,jdbcType=INTEGER}
        and
        is_del = 0
            </select>

            <!--是否是一级类目id-->
            <select id="getOneLevel" resultType="int">
                    select
         count(*) 
        from
        hw_goods_category
        where
        pid = #{pid,jdbcType=INTEGER}
        and
        is_del = 0
            </select>
    </mapper>
