<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.TpEquipmentPurchaseOrderDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.dataobject.TpEquipmentPurchaseOrderDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="NOTE" property="note" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ORDER_SN" property="orderSn" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATER" property="creater" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EXAMINER" property="examiner" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ORDER_NUM" property="orderNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ARRIVAL_NUM" property="arrivalNum" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EXAMIN_TIME" property="examinTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="EQUIPMENT_ID" property="equipmentId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ORDER_STATUS" property="orderStatus" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PURCHASE_TYPE" property="purchaseType" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEPOT_LOCATION" property="depotLocation" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="ORDER_PRICE" property="orderPrice" jdbcType="DECIMAL"
                    javaType="java.math.BigDecimal"/>
        </resultMap>


        <sql id="Base_Column_List">
            `ID`,`NOTE`,`ORDER_SN`,`AGENT_ID`,`CREATER`,`EXAMINER`,`ORDER_NUM`,`ARRIVAL_NUM`,`CREATE_TIME`,`EXAMIN_TIME`,`UPDATE_TIME`,`EQUIPMENT_ID`,`ORDER_STATUS`,`PURCHASE_TYPE`,`DEPOT_LOCATION`,`ORDER_PRICE`
        </sql>


        <!--insert:TP_EQUIPMENT_PURCHASE_ORDER-->
        <insert id="insert">
            INSERT INTO TP_EQUIPMENT_PURCHASE_ORDER
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="note != null">`NOTE`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="agentId != null">`AGENT_ID`,</if>
                <if test="creater != null">`CREATER`,</if>
                <if test="examiner != null">`EXAMINER`,</if>
                <if test="orderNum != null">`ORDER_NUM`,</if>
                <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="examinTime != null">`EXAMIN_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="purchaseType != null">`PURCHASE_TYPE`,</if>
            <if test="depotLocation != null">`DEPOT_LOCATION`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=INTEGER},</if>
            <if test="examiner != null">#{examiner,jdbcType=INTEGER},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="examinTime != null">#{examinTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="purchaseType != null">#{purchaseType,jdbcType=TINYINT},</if>
            <if test="depotLocation != null">#{depotLocation,jdbcType=INTEGER},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
        </insert>
    </mapper>
