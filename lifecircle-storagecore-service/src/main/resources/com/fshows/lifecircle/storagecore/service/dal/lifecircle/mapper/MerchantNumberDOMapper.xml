<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.MerchantNumberDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantNumberDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="QRA" property="qra" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="QRC" property="qrc" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CUPS" property="cups" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE" property="source" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TERM_NO" property="termNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="DCEP_SMID" property="dcepSmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_PID" property="alipayPid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLOSE_TIME" property="closeTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_CODE" property="unionCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ALIPAY_SMID" property="alipaySmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CHANNEL_NUM" property="channelNum" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLOSE_STATE" property="closeState" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="MERCHANT_NO" property="merchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_TIME" property="rejectTime" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SUB_AGENT_ID" property="subAgentId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WECHAT_SMID" property="wechatSmid" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UNION_MCC_CODE" property="unionMccCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_ORG_ID" property="platformOrgId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TRADE_MERCHANT_NO" property="tradeMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PLATFORM_MERCHANT_NO" property="platformMerchantNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="END_TIME" property="endTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_PASSED" property="isPassed" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CHANNEL_ID" property="channelId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="START_TIME" property="startTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTH_STATUS" property="authStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_ACTIVITY" property="isActivity" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USED_STATUS" property="usedStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="USED_STORE_ID" property="usedStoreId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AUTH_SYNC_TIME" property="authSyncTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="MERCHANT_TYPE" property="merchantType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WECHANT_AUTH_STATUS" property="wechantAuthStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="MERCHANT_QUALIFICATION" property="merchantQualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`QRA`,`QRC`,`CUPS`,`TOKEN`,`SOURCE`,`TERM_NO`,`DCEP_SMID`,`ALIPAY_PID`,`CLOSE_TIME`,`UNION_CODE`,`ALIPAY_SMID`,`CHANNEL_NUM`,`CLOSE_STATE`,`MERCHANT_NO`,`REJECT_TIME`,`SUB_AGENT_ID`,`WECHAT_SMID`,`UNION_MCC_CODE`,`PLATFORM_ORG_ID`,`TRADE_MERCHANT_NO`,`PLATFORM_MERCHANT_NO`,`UID`,`END_TIME`,`STORE_ID`,`IS_PASSED`,`CHANNEL_ID`,`START_TIME`,`AUTH_STATUS`,`IS_ACTIVITY`,`USED_STATUS`,`USED_STORE_ID`,`AUTH_SYNC_TIME`,`MERCHANT_TYPE`,`WECHANT_AUTH_STATUS`,`MERCHANT_QUALIFICATION`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_MERCHANT_NUMBER-->
            <insert id="insert" >
                    INSERT INTO TP_MERCHANT_NUMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="alipayPid != null">`ALIPAY_PID`,</if>
            <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="channelNum != null">`CHANNEL_NUM`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="wechatSmid != null">`WECHAT_SMID`,</if>
            <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
            <if test="platformMerchantNo != null">`PLATFORM_MERCHANT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="isActivity != null">`IS_ACTIVITY`,</if>
            <if test="usedStatus != null">`USED_STATUS`,</if>
            <if test="usedStoreId != null">`USED_STORE_ID`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="merchantQualification != null">`MERCHANT_QUALIFICATION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="termNo != null">`TERM_NO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="alipayPid != null">#{alipayPid,jdbcType=VARCHAR},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="wechatSmid != null">#{wechatSmid,jdbcType=VARCHAR},</if>
            <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
            <if test="platformMerchantNo != null">#{platformMerchantNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="isActivity != null">#{isActivity,jdbcType=TINYINT},</if>
            <if test="usedStatus != null">#{usedStatus,jdbcType=TINYINT},</if>
            <if test="usedStoreId != null">#{usedStoreId,jdbcType=INTEGER},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
            <if test="merchantQualification != null">#{merchantQualification,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="termNo != null">#{termNo,jdbcType=VARCHAR},</if>
        </trim>
            </insert>

            <!--通过商编merchantNo,orgId获取用户信息-->
            <select id="getByMerchantNoAndOrgId" resultMap="BaseResultMap">
                    select /*MS-TP-MERCHANT-NUMBER-GETBYMERCHANTNOANDORGID*/ <include refid="Base_Column_List" />
        from TP_MERCHANT_NUMBER
        where merchant_no = #{merchantNo,jdbcType=VARCHAR}
        and PLATFORM_ORG_ID = #{orgId,jdbcType=VARCHAR}
        limit 1
            </select>

            <!--获取商编信息,根据商编查询-->
            <select id="getOneByMerchantNo" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MERCHANT-NUMBER-GETONEBYMERCHANTNO*/  <include refid="Base_Column_List" /> FROM
        TP_MERCHANT_NUMBER
        WHERE
        merchant_no = #{merchantNo,jdbcType=VARCHAR}
        AND used_status = 1
        ORDER BY id limit 1
            </select>

            <!--获取商编信息,根据门店ID查询-->
            <select id="getOneByStoreId" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MERCHANT-NUMBER-GETONEBYSTOREID*/  <include refid="Base_Column_List" /> FROM
        TP_MERCHANT_NUMBER
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        AND platform_org_id = #{platformOrgId,jdbcType=INTEGER}
        <if test="storeId != null">
            AND store_id = #{storeId,jdbcType=INTEGER}
        </if>
        AND merchant_no != ''
        AND used_status = 1
        ORDER BY id ASC limit 1
            </select>

            <!--获取商编信息,如果是总分店则获取总店信息-->
            <select id="getByPlatformOrgIdAndUid" resultMap="BaseResultMap">
                    SELECT /*MS-TP-MERCHANT-NUMBER-GETBYPLATFORMORGIDANDUID*/  <include refid="Base_Column_List" /> FROM
        TP_MERCHANT_NUMBER
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        <if test="platformOrgId != null">
            AND platform_org_id = #{platformOrgId,jdbcType=INTEGER}
        </if>
        AND merchant_no != ''
        AND used_status = 1
        AND merchant_type in (1,2,4)
        ORDER BY merchant_type ASC limit 1
            </select>

            <!--根据商户UID获取商编-->
            <select id="getMerchantNoByUidList" resultType="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.UidAndMerchantNoResultMap">
                    select /*MS-TP-MERCHANT-NUMBER-GETMERCHANTNOBYUIDLIST*/ uid,merchant_no as merchantNo
        from tp_merchant_number
        where uid in
        <foreach collection="list" item="uid" open="(" separator="," close=")">
            #{uid,jdbcType=INTEGER}
        </foreach>
            </select>

            <!--根据商编查询-->
            <select id="findByMerchantNoList" resultMap="BaseResultMap">
                    select /*MS-TP-MERCHANT-NUMBER-FINDBYMERCHANTNOLIST*/ <include refid="Base_Column_List" /> from tp_merchant_number
        where source=#{source,jdbcType=VARCHAR}
        AND merchant_no in
        <foreach collection="list" item="merchantNo" open="(" separator="," close=")">
            #{merchantNo,jdbcType=VARCHAR}
        </foreach>
            </select>
    </mapper>
