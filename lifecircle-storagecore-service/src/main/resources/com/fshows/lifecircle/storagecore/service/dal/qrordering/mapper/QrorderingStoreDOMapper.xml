<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.qrordering.mapper.QrorderingStoreDOMapper">

        <resultMap id="BaseResultMap"
                   type="com.fshows.lifecircle.storagecore.service.dal.qrordering.dataobject.QrorderingStoreDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="STORE_LAT" property="storeLat" jdbcType="REAL"
                    javaType="Float"/>

            <result column="STORE_LNG" property="storeLng" jdbcType="REAL"
                    javaType="Float"/>

            <result column="TEL" property="tel" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="CITY" property="city" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="NOTE" property="note" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="COUNTY" property="county" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="ADDRESS" property="address" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="END_TIME" property="endTime" jdbcType="CHAR"
                    javaType="String"/>

            <result column="SERVICE" property="service" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_ID" property="storeId" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="PROVINCE" property="province" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BRAND_NAME" property="brandName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="RECOMMEND" property="recommend" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="START_TIME" property="startTime" jdbcType="CHAR"
                    javaType="String"/>

            <result column="STORE_AREA" property="storeArea" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_LOGO" property="storeLogo" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="BRANCH_NAME" property="branchName" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_BRIEF" property="storeBrief" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="STORE_IMAGE" property="storeImage" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="MINI_APP_CODE" property="miniAppCode" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="SERVICE_MORE" property="serviceMore" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="LICENSE_NUMBER" property="licenseNumber" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="APPOINTMENT_END_TIME" property="appointmentEndTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="APPOINTMENT_START_TIME" property="appointmentStartTime" jdbcType="VARCHAR"
                    javaType="String"/>

            <result column="KA" property="ka" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="IS_SHOW" property="isShow" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="DEL_FLAG" property="delFlag" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="AVG_PRICE" property="avgPrice" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="IS_ONLINE" property="isOnline" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_MODE" property="orderMode" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="MEAL_METHOD" property="mealMethod" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="OUT_STORE_ID" property="outStoreId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="JOIN_CHANNEL" property="joinChannel" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="PICK_UP_SWITCH" property="pickUpSwitch" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="OUT_MERCHANT_ID" property="outMerchantId" jdbcType="INTEGER"
                    javaType="Integer"/>

            <result column="DISPLAY_COMMENT" property="displayComment" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="ORDER_REMIND_SWITCH" property="orderRemindSwitch" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CUSTOM_PRODUCT_SHOW_MODE" property="customProductShowMode" jdbcType="TINYINT"
                    javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
                    javaType="java.util.Date"/>
        </resultMap>


        <sql id="Base_Column_List">
    `ID`,`STORE_LAT`,`STORE_LNG`,`TEL`,`CITY`,`NOTE`,`COUNTY`,`ADDRESS`,`END_TIME`,`SERVICE`,`STORE_ID`,`PROVINCE`,`BRAND_NAME`,`RECOMMEND`,`START_TIME`,`STORE_AREA`,`STORE_LOGO`,`STORE_NAME`,`BRANCH_NAME`,`STORE_BRIEF`,`STORE_IMAGE`,`MINI_APP_CODE`,`SERVICE_MORE`,`LICENSE_NUMBER`,`APPOINTMENT_END_TIME`,`APPOINTMENT_START_TIME`,`KA`,`IS_SHOW`,`DEL_FLAG`,`AVG_PRICE`,`IS_ONLINE`,`ORDER_MODE`,`MEAL_METHOD`,`OUT_STORE_ID`,`JOIN_CHANNEL`,`PICK_UP_SWITCH`,`OUT_MERCHANT_ID`,`DISPLAY_COMMENT`,`ORDER_REMIND_SWITCH`,`CUSTOM_PRODUCT_SHOW_MODE`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


        <!--根据生活圈门店id查询详情-->
        <select id="getStoreInfoByOutStoreId" resultMap="BaseResultMap">
            select /*MS-TP-QRORDERING-STORE-GETSTOREINFOBYOUTSTOREID*/
            <include refid="Base_Column_List"/>
            from tp_qrordering_store where out_store_id = #{outStoreId,jdbcType=VARCHAR} and del_flag = 0 limit 1
        </select>

        <!--根据门店Id查询点单门店信息-->
        <select id="findStoreByStoreIdList" resultMap="BaseResultMap">
            SELECT
            <include refid="Base_Column_List"/>
            FROM tp_qrordering_store
            where store_id in
            <foreach collection="list" item="storeId" separator="," open="(" close=")">
                #{storeId,jdbcType=VARCHAR}
            </foreach>
            and DEL_FLAG = 0
        </select>
    </mapper>
