<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.mapper.ReadonlyLifecircleGeneralBankDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecirclereadonly.dataobject.ReadonlyLifecircleGeneralBankDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="BANK_FULL_NAME" property="bankFullName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_SHORT_NAME" property="bankShortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_PACKAGE_IMG" property="bankPackageImg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_PACKAGE_LOGO" property="bankPackageLogo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="GENERAL_BANK_CODE" property="generalBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SHOW_FLAG" property="showFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`BANK_FULL_NAME`,`BANK_SHORT_NAME`,`BANK_PACKAGE_IMG`,`BANK_PACKAGE_LOGO`,`GENERAL_BANK_CODE`,`IS_DEL`,`SHOW_FLAG`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_LIFECIRCLE_GENERAL_BANK-->
            <insert id="insert" >
            INSERT INTO TP_LIFECIRCLE_GENERAL_BANK
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="bankFullName != null">`BANK_FULL_NAME`,</if>
        <if test="bankShortName != null">`BANK_SHORT_NAME`,</if>
        <if test="bankPackageImg != null">`BANK_PACKAGE_IMG`,</if>
        <if test="bankPackageLogo != null">`BANK_PACKAGE_LOGO`,</if>
        <if test="generalBankCode != null">`GENERAL_BANK_CODE`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="showFlag != null">`SHOW_FLAG`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="bankFullName != null">#{bankFullName,jdbcType=VARCHAR},</if>
        <if test="bankShortName != null">#{bankShortName,jdbcType=VARCHAR},</if>
        <if test="bankPackageImg != null">#{bankPackageImg,jdbcType=VARCHAR},</if>
        <if test="bankPackageLogo != null">#{bankPackageLogo,jdbcType=VARCHAR},</if>
        <if test="generalBankCode != null">#{generalBankCode,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="showFlag != null">#{showFlag,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
            </insert>

            <!--根据银行code查询信息-->
            <select id="getByBankCode" resultMap="BaseResultMap">
                    select /*MS-TP-LIFECIRCLE-GENERAL-BANK-GETBYBANKCODE*/ <include refid="Base_Column_List" />
        from TP_LIFECIRCLE_GENERAL_BANK
        where
        is_del = 0
        and general_bank_code = #{generalBankCode,jdbcType=VARCHAR}
        LIMIT 1
            </select>
    </mapper>
