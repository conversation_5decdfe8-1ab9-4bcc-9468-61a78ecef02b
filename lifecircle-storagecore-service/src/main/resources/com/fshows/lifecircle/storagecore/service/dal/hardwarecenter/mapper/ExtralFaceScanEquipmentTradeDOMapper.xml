<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.mapper.ExtralFaceScanEquipmentTradeDOMapper">


    <select id="getList"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.param.FaceScanEquipActivityTradeParamDO"
            resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.FaceScanEquipActivityTradeResultDO">
        select

        esn.init_sn equipmentSn,
        esn.system_sn equipmentSystemSn,
        esn.pay_type equipmentSnPayType,
        esn.sn_status equipmentSnStatus,
        esn.trade_mode equipmentSnTradeMode,
        esn.cashier_mode equipmentSnCashierMode,
        esn.channel_type equipmentSnChannelType,
        esn.operate_type equipmentSnOperateType,
        esn.printer_setting equipmentSnPrinterSetting,
        esn.bind_time equipmentSnBindTime,
        esn.unbind_time equipmentSnUnBindTime,
        esn.create_time equipmentSnCreateTime,
        esn.update_time equipmentSnUpdateTime,
        e.id equipmentId,
        e.equipment_name equipmentName,
        e.equipment_model equipmentModel,
        e.equipment_type equipmentType,
        e.equipment_firm equipmentFirm,
        e.equipment_introduce equipmentIntroduce,
        e.equipment_pic equipmentPic,
        e.equipment_prefix equipmentPrefix,
        e.equipment_price equipmentPrice,
        e.platform equipmentPlatform,
        e.app_show equipmentAppShow,
        tstore.store_id storeId,
        tstore.store_name storeName,
        tmerchant.id merchantId,
        tmerchant.username merchantUsername,
        equipAgent.id agentId,
        equipAgent.username agentUsername,
        equipAgent.own_run agentOwnRun,
        facerecord.light_status lightStatus,
        facerecord.light_time lightTime,
        facerecord.activity_status activityStatus,
        facerecord.activity_time activityTime,
        facerecord.equipment_dau equipSnDau,
        facerecord.transaction_num equipSnTradeNum,
        facerecord.transaction_money equipSnTradeAmount,
        facerecord.code_scan_num equipSnScanFaceNum

        from hw_equipment_sn esn
        left join hw_equipment e on esn.equipment_id = e.id
        left join tp_user equipAgent on esn.agent_id = equipAgent.id
        left join tp_lifecircle_store tstore on esn.store_id = tstore.store_id
        left join tp_users tmerchant on esn.uid = tmerchant.id
        left join tp_face_scan_equipment_record facerecord on facerecord.equipment_sn = esn.init_sn

        where esn.is_del = 0
        and e.equipment_type = 4
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="includeSnStatusList != null and includeSnStatusList.size() > 0">
            and esn.sn_status in
            <foreach collection="includeSnStatusList" open="(" close=")" item="snStatus" separator=",">
                #{snStatus,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="equipmentId != null">
            and e.id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="storeNameLike != null and storeNameLike !=''">
            and tstore.store_name LIKE CONCAT(#{storeNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="merchantUsernameLike != null and merchantUsernameLike !=''">
            and tmerchant.username LIKE CONCAT(#{merchantUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="agentId != null">
            and equipAgent.id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="agentUsernameLike != null and agentUsernameLike !=''">
            and equipAgent.username LIKE CONCAT(#{agentUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 1">
            and equipAgent.own_run = 1
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 2">
            and equipAgent.own_run = 0 and equipAgent.belong = 0 and equipAgent.sub_config_id = 0
        </if>
        <if test="lightStatus != null">
            and facerecord.light_status = #{lightStatus,jdbcType=INTEGER}
        </if>
        <if test="lightStartTime != null">
            and facerecord.light_time <![CDATA[ >= ]]> #{lightStartTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="lightEndTime != null">
            and facerecord.light_time <![CDATA[ <= ]]> #{lightEndTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="activityStatus != null">
            and facerecord.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activityStartTime != null">
            and facerecord.activity_time <![CDATA[ >= ]]> #{activityStartTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
        <if test="activityEndTime != null">
            and facerecord.activity_time <![CDATA[ <= ]]> #{activityEndTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
        order by esn.create_time desc
    </select>

    <select id="getPageList"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.param.FaceScanEquipActivityTradeParamDO"
            resultType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.FaceScanEquipActivityTradeResultDO">
        select
        esn.init_sn equipmentSn,
        esn.system_sn equipmentSystemSn,
        esn.pay_type equipmentSnPayType,
        esn.sn_status equipmentSnStatus,
        esn.trade_mode equipmentSnTradeMode,
        esn.cashier_mode equipmentSnCashierMode,
        esn.channel_type equipmentSnChannelType,
        esn.operate_type equipmentSnOperateType,
        esn.printer_setting equipmentSnPrinterSetting,
        esn.bind_time equipmentSnBindTime,
        esn.unbind_time equipmentSnUnBindTime,
        esn.create_time equipmentSnCreateTime,
        esn.update_time equipmentSnUpdateTime,
        e.id equipmentId,
        e.equipment_name equipmentName,
        e.equipment_model equipmentModel,
        e.equipment_type equipmentType,
        e.equipment_firm equipmentFirm,
        e.equipment_introduce equipmentIntroduce,
        e.equipment_pic equipmentPic,
        e.equipment_prefix equipmentPrefix,
        e.equipment_price equipmentPrice,
        e.platform equipmentPlatform,
        e.app_show equipmentAppShow,
        tstore.store_id storeId,
        tstore.store_name storeName,
        tmerchant.id merchantId,
        tmerchant.username merchantUsername,
        equipAgent.id agentId,
        equipAgent.username agentUsername,
        equipAgent.own_run agentOwnRun,
        facerecord.light_status lightStatus,
        facerecord.light_time lightTime,
        facerecord.activity_status activityStatus,
        facerecord.activity_time activityTime,
        facerecord.equipment_dau equipSnDau,
        facerecord.transaction_num equipSnTradeNum,
        facerecord.transaction_money equipSnTradeAmount,
        facerecord.code_scan_num equipSnScanFaceNum

        from hw_equipment_sn esn
        left join hw_equipment e on esn.equipment_id = e.id
        left join tp_user equipAgent on esn.agent_id = equipAgent.id
        left join tp_lifecircle_store tstore on esn.store_id = tstore.store_id
        left join tp_users tmerchant on esn.uid = tmerchant.id
        left join tp_face_scan_equipment_record facerecord on facerecord.equipment_sn = esn.init_sn

        where esn.is_del = 0
        and e.equipment_type = 4
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="includeSnStatusList != null and includeSnStatusList.size() > 0">
            and esn.sn_status in
            <foreach collection="includeSnStatusList" open="(" close=")" item="snStatus" separator=",">
                #{snStatus,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="equipmentId != null">
            and e.id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="storeNameLike != null and storeNameLike !=''">
            and tstore.store_name LIKE CONCAT(#{storeNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="merchantUsernameLike != null and merchantUsernameLike !=''">
            and tmerchant.username LIKE CONCAT(#{merchantUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="agentId != null">
            and equipAgent.id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="agentUsernameLike != null and agentUsernameLike !=''">
            and equipAgent.username LIKE CONCAT(#{agentUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 1">
            and equipAgent.own_run = 1
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 2">
            and equipAgent.own_run = 0 and equipAgent.belong = 0 and equipAgent.sub_config_id = 0
        </if>
        <if test="lightStatus != null">
            and facerecord.light_status = #{lightStatus,jdbcType=INTEGER}
        </if>
        <if test="lightStartTime != null">
            and facerecord.light_time <![CDATA[ >= ]]> #{lightStartTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="lightEndTime != null">
            and facerecord.light_time <![CDATA[ <= ]]> #{lightEndTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="activityStatus != null">
            and facerecord.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activityStartTime != null">
            and facerecord.activity_time <![CDATA[ >= ]]> #{activityStartTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
        <if test="activityEndTime != null">
            and facerecord.activity_time <![CDATA[ <= ]]> #{activityEndTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
        order by esn.create_time desc
    </select>

    <select id="count"
            parameterType="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.param.FaceScanEquipActivityTradeParamDO"
            resultType="java.lang.Integer">
        select
        count(*)

        from hw_equipment_sn esn
        left join hw_equipment e on esn.equipment_id = e.id
        left join tp_user equipAgent on esn.agent_id = equipAgent.id
        left join tp_lifecircle_store tstore on esn.store_id = tstore.store_id
        left join tp_users tmerchant on esn.uid = tmerchant.id
        left join tp_face_scan_equipment_record facerecord on facerecord.equipment_sn = esn.init_sn

        where esn.is_del = 0
        and e.equipment_type = 4
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="includeSnStatusList != null and includeSnStatusList.size() > 0">
            and esn.sn_status in
            <foreach collection="includeSnStatusList" open="(" close=")" item="snStatus" separator=",">
                #{snStatus,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="equipmentId != null">
            and e.id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="storeNameLike != null and storeNameLike !=''">
            and tstore.store_name LIKE CONCAT(#{storeNameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="merchantUsernameLike != null and merchantUsernameLike !=''">
            and tmerchant.username LIKE CONCAT(#{merchantUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="agentId != null">
            and equipAgent.id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="agentUsernameLike != null and agentUsernameLike !=''">
            and equipAgent.username LIKE CONCAT(#{agentUsernameLike,jdbcType=VARCHAR},'%')
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 1">
            and equipAgent.own_run = 1
        </if>
        <if test="ownRunStatus != null and ownRunStatus == 2">
            and equipAgent.own_run = 0 and equipAgent.belong = 0 and equipAgent.sub_config_id = 0
        </if>
        <if test="lightStatus != null">
            and facerecord.light_status = #{lightStatus,jdbcType=INTEGER}
        </if>
        <if test="lightStartTime != null">
            and facerecord.light_time <![CDATA[ >= ]]> #{lightStartTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="lightEndTime != null">
            and facerecord.light_time <![CDATA[ <= ]]> #{lightEndTime,jdbcType=TIMESTAMP}
            and facerecord.light_status = 1
        </if>
        <if test="activityStatus != null">
            and facerecord.activity_status = #{activityStatus,jdbcType=INTEGER}
        </if>
        <if test="activityStartTime != null">
            and facerecord.activity_time <![CDATA[ >= ]]> #{activityStartTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
        <if test="activityEndTime != null">
            and facerecord.activity_time <![CDATA[ <= ]]> #{activityEndTime,jdbcType=TIMESTAMP}
            and facerecord.activity_status = 2
        </if>
    </select>
</mapper>
