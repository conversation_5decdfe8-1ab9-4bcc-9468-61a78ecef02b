<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleWandaDataSyncTempExtendDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleWandaDataSyncTempExtendDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="STORE_ID" property="storeId" jdbcType="BIGINT"
        javaType="Long"/>

            <result column="STORE_CASH_PIC" property="storeCashPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_BANK_PIC" property="clientBankPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PRE_CHECK_ERR_MSG" property="preCheckErrMsg" jdbcType="LONGVARCHAR"
        javaType="String"/>

            <result column="STORE_INSIDE_PIC" property="storeInsidePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LICENSE_PIC" property="clientLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_OCR_RESULT" property="licenseOcrResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_DOOR_HEAD_PIC" property="storeDoorHeadPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_ERR_MSG" property="clientCheckErrMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_MERCHANT_INFO" property="clientMerchantInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_STORE_CASH_PIC" property="sourceStoreCashPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PIC_SYNC_ERR_MSG" property="storePicSyncErrMsg" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_BANK_PIC_OCR_RES" property="clientBankPicOcrRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LICENSE_OCR_RES" property="clientLicenseOcrRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_STORE_INSIDE_PIC" property="sourceStoreInsidePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SOURCE_STORE_DOOR_HEAD_PIC" property="sourceStoreDoorHeadPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_LEGAL_BACK_PIC" property="clientCheckLegalBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_LEGAL_FRONT_PIC" property="clientCheckLegalFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_SETTLE_BACK_PIC" property="clientCheckSettleBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LEGAL_BACK_PIC_ORC_RES" property="clientLegalBackPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_CONTACT_BACK_PIC" property="clientCheckContactBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_SETTLE_FRONT_PIC" property="clientCheckSettleFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LEGAL_FRONT_PIC_ORC_RES" property="clientLegalFrontPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_SETTLE_BACK_PIC_ORC_RES" property="clientSettleBackPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CHECK_CONTACT_FRONT_PIC" property="clientCheckContactFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CONTACT_BACK_PIC_ORC_RES" property="clientContactBackPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_SETTLE_FRONT_PIC_ORC_RES" property="clientSettleFrontPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CONTACT_FRONT_PIC_ORC_RES" property="clientContactFrontPicOrcRes" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LEGAL_TWO_ELEMENT_AUTH_INFO" property="clientLegalTwoElementAuthInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_BANK_THREE_ELEMENT_AUTH_INFO" property="clientBankThreeElementAuthInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_SETTLE_TWO_ELEMENT_AUTH_INFO" property="clientSettleTwoElementAuthInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_CONTACT_TWO_ELEMENT_AUTH_INFO" property="clientContactTwoElementAuthInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CLIENT_LICENSE_THREE_ELEMENT_AUTH_INFO" property="clientLicenseThreeElementAuthInfo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_IDENTITY_CARD_BACK_OCR_RESULT" property="legalPersonIdentityCardBackOcrResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_IDENTITY_CARD_FRONT_OCR_RESULT" property="legalPersonIdentityCardFrontOcrResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_THREE_ELEMENT_AUTHENTICATION_RESULT" property="licenseThreeElementAuthenticationResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT" property="bankCardThreeElementAuthenticationResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_IDENTITY_CARD_TWO_ELEMENT_AUTHENTICATION_RESULT" property="legalPersonIdentityCardTwoElementAuthenticationResult" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="RE_CHECK_FLAG" property="reCheckFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="PRE_CHECK_FLAG" property="preCheckFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_PIC_SYNC_FLAG" property="storePicSyncFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLIENT_LEGAL_TWO_ELEMENT_AUTH_RESULT" property="clientLegalTwoElementAuthResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLIENT_SETTLE_TWO_ELEMENT_AUTH_RESULT" property="clientSettleTwoElementAuthResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLIENT_CONTACT_TWO_ELEMENT_AUTH_RESULT" property="clientContactTwoElementAuthResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLIENT_LICENSE_THREE_ELEMENT_AUTH_RESULT" property="clientLicenseThreeElementAuthResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CLIENT_BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT" property="clientBankCardThreeElementAuthenticationResult" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`STORE_ID`,`STORE_CASH_PIC`,`CLIENT_BANK_PIC`,`PRE_CHECK_ERR_MSG`,`STORE_INSIDE_PIC`,`CLIENT_LICENSE_PIC`,`LICENSE_OCR_RESULT`,`STORE_DOOR_HEAD_PIC`,`CLIENT_CHECK_ERR_MSG`,`CLIENT_MERCHANT_INFO`,`SOURCE_STORE_CASH_PIC`,`STORE_PIC_SYNC_ERR_MSG`,`CLIENT_BANK_PIC_OCR_RES`,`CLIENT_LICENSE_OCR_RES`,`SOURCE_STORE_INSIDE_PIC`,`SOURCE_STORE_DOOR_HEAD_PIC`,`CLIENT_CHECK_LEGAL_BACK_PIC`,`CLIENT_CHECK_LEGAL_FRONT_PIC`,`CLIENT_CHECK_SETTLE_BACK_PIC`,`CLIENT_LEGAL_BACK_PIC_ORC_RES`,`CLIENT_CHECK_CONTACT_BACK_PIC`,`CLIENT_CHECK_SETTLE_FRONT_PIC`,`CLIENT_LEGAL_FRONT_PIC_ORC_RES`,`CLIENT_SETTLE_BACK_PIC_ORC_RES`,`CLIENT_CHECK_CONTACT_FRONT_PIC`,`CLIENT_CONTACT_BACK_PIC_ORC_RES`,`CLIENT_SETTLE_FRONT_PIC_ORC_RES`,`CLIENT_CONTACT_FRONT_PIC_ORC_RES`,`CLIENT_LEGAL_TWO_ELEMENT_AUTH_INFO`,`CLIENT_BANK_THREE_ELEMENT_AUTH_INFO`,`CLIENT_SETTLE_TWO_ELEMENT_AUTH_INFO`,`CLIENT_CONTACT_TWO_ELEMENT_AUTH_INFO`,`CLIENT_LICENSE_THREE_ELEMENT_AUTH_INFO`,`LEGAL_PERSON_IDENTITY_CARD_BACK_OCR_RESULT`,`LEGAL_PERSON_IDENTITY_CARD_FRONT_OCR_RESULT`,`LICENSE_THREE_ELEMENT_AUTHENTICATION_RESULT`,`BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,`LEGAL_PERSON_IDENTITY_CARD_TWO_ELEMENT_AUTHENTICATION_RESULT`,`RE_CHECK_FLAG`,`PRE_CHECK_FLAG`,`STORE_PIC_SYNC_FLAG`,`CLIENT_LEGAL_TWO_ELEMENT_AUTH_RESULT`,`CLIENT_SETTLE_TWO_ELEMENT_AUTH_RESULT`,`CLIENT_CONTACT_TWO_ELEMENT_AUTH_RESULT`,`CLIENT_LICENSE_THREE_ELEMENT_AUTH_RESULT`,`CLIENT_BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


    <!--insert:TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND-->
    <insert id="insert">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="clientBankPic != null">`CLIENT_BANK_PIC`,</if>
            <if test="preCheckErrMsg != null">`PRE_CHECK_ERR_MSG`,</if>
            <if test="clientLicensePic != null">`CLIENT_LICENSE_PIC`,</if>
            <if test="licenseOcrResult != null">`LICENSE_OCR_RESULT`,</if>
            <if test="clientCheckErrMsg != null">`CLIENT_CHECK_ERR_MSG`,</if>
            <if test="clientMerchantInfo != null">`CLIENT_MERCHANT_INFO`,</if>
            <if test="clientBankPicOcrRes != null">`CLIENT_BANK_PIC_OCR_RES`,</if>
            <if test="clientLicenseOcrRes != null">`CLIENT_LICENSE_OCR_RES`,</if>
            <if test="clientCheckLegalBackPic != null">`CLIENT_CHECK_LEGAL_BACK_PIC`,</if>
            <if test="clientCheckLegalFrontPic != null">`CLIENT_CHECK_LEGAL_FRONT_PIC`,</if>
            <if test="clientCheckSettleBackPic != null">`CLIENT_CHECK_SETTLE_BACK_PIC`,</if>
            <if test="clientLegalBackPicOrcRes != null">`CLIENT_LEGAL_BACK_PIC_ORC_RES`,</if>
            <if test="clientCheckContactBackPic != null">`CLIENT_CHECK_CONTACT_BACK_PIC`,</if>
            <if test="clientCheckSettleFrontPic != null">`CLIENT_CHECK_SETTLE_FRONT_PIC`,</if>
            <if test="clientLegalFrontPicOrcRes != null">`CLIENT_LEGAL_FRONT_PIC_ORC_RES`,</if>
            <if test="clientSettleBackPicOrcRes != null">`CLIENT_SETTLE_BACK_PIC_ORC_RES`,</if>
            <if test="clientCheckContactFrontPic != null">`CLIENT_CHECK_CONTACT_FRONT_PIC`,</if>
            <if test="clientContactBackPicOrcRes != null">`CLIENT_CONTACT_BACK_PIC_ORC_RES`,</if>
            <if test="clientSettleFrontPicOrcRes != null">`CLIENT_SETTLE_FRONT_PIC_ORC_RES`,</if>
            <if test="clientContactFrontPicOrcRes != null">`CLIENT_CONTACT_FRONT_PIC_ORC_RES`,</if>
            <if test="clientLegalTwoElementAuthInfo != null">`CLIENT_LEGAL_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientBankThreeElementAuthInfo != null">`CLIENT_BANK_THREE_ELEMENT_AUTH_INFO`,</if>
            <if test="clientSettleTwoElementAuthInfo != null">`CLIENT_SETTLE_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientContactTwoElementAuthInfo != null">`CLIENT_CONTACT_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientLicenseThreeElementAuthInfo != null">`CLIENT_LICENSE_THREE_ELEMENT_AUTH_INFO`,</if>
            <if test="legalPersonIdentityCardBackOcrResult != null">`LEGAL_PERSON_IDENTITY_CARD_BACK_OCR_RESULT`,</if>
            <if test="legalPersonIdentityCardFrontOcrResult != null">`LEGAL_PERSON_IDENTITY_CARD_FRONT_OCR_RESULT`,</if>
            <if test="licenseThreeElementAuthenticationResult != null">`LICENSE_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="bankCardThreeElementAuthenticationResult != null">
                `BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="legalPersonIdentityCardTwoElementAuthenticationResult != null">
                `LEGAL_PERSON_IDENTITY_CARD_TWO_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="reCheckFlag != null">`RE_CHECK_FLAG`,</if>
            <if test="preCheckFlag != null">`PRE_CHECK_FLAG`,</if>
            <if test="clientLegalTwoElementAuthResult != null">`CLIENT_LEGAL_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientSettleTwoElementAuthResult != null">`CLIENT_SETTLE_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientContactTwoElementAuthResult != null">`CLIENT_CONTACT_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientLicenseThreeElementAuthResult != null">`CLIENT_LICENSE_THREE_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientBankCardThreeElementAuthenticationResult != null">
                `CLIENT_BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="clientBankPic != null">#{clientBankPic,jdbcType=VARCHAR},</if>
            <if test="preCheckErrMsg != null">#{preCheckErrMsg,jdbcType=LONGVARCHAR},</if>
            <if test="clientLicensePic != null">#{clientLicensePic,jdbcType=VARCHAR},</if>
            <if test="licenseOcrResult != null">#{licenseOcrResult,jdbcType=VARCHAR},</if>
            <if test="clientCheckErrMsg != null">#{clientCheckErrMsg,jdbcType=VARCHAR},</if>
            <if test="clientMerchantInfo != null">#{clientMerchantInfo,jdbcType=VARCHAR},</if>
            <if test="clientBankPicOcrRes != null">#{clientBankPicOcrRes,jdbcType=VARCHAR},</if>
            <if test="clientLicenseOcrRes != null">#{clientLicenseOcrRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckLegalBackPic != null">#{clientCheckLegalBackPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckLegalFrontPic != null">#{clientCheckLegalFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckSettleBackPic != null">#{clientCheckSettleBackPic,jdbcType=VARCHAR},</if>
            <if test="clientLegalBackPicOrcRes != null">#{clientLegalBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckContactBackPic != null">#{clientCheckContactBackPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckSettleFrontPic != null">#{clientCheckSettleFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientLegalFrontPicOrcRes != null">#{clientLegalFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientSettleBackPicOrcRes != null">#{clientSettleBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckContactFrontPic != null">#{clientCheckContactFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientContactBackPicOrcRes != null">#{clientContactBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientSettleFrontPicOrcRes != null">#{clientSettleFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientContactFrontPicOrcRes != null">#{clientContactFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientLegalTwoElementAuthInfo != null">#{clientLegalTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientBankThreeElementAuthInfo != null">#{clientBankThreeElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientSettleTwoElementAuthInfo != null">#{clientSettleTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientContactTwoElementAuthInfo != null">#{clientContactTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientLicenseThreeElementAuthInfo != null">
                #{clientLicenseThreeElementAuthInfo,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardBackOcrResult != null">
                #{legalPersonIdentityCardBackOcrResult,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardFrontOcrResult != null">
                #{legalPersonIdentityCardFrontOcrResult,jdbcType=VARCHAR},
            </if>
            <if test="licenseThreeElementAuthenticationResult != null">
                #{licenseThreeElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="bankCardThreeElementAuthenticationResult != null">
                #{bankCardThreeElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardTwoElementAuthenticationResult != null">
                #{legalPersonIdentityCardTwoElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="reCheckFlag != null">#{reCheckFlag,jdbcType=TINYINT},</if>
            <if test="preCheckFlag != null">#{preCheckFlag,jdbcType=TINYINT},</if>
            <if test="clientLegalTwoElementAuthResult != null">#{clientLegalTwoElementAuthResult,jdbcType=TINYINT},</if>
            <if test="clientSettleTwoElementAuthResult != null">#{clientSettleTwoElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientContactTwoElementAuthResult != null">
                #{clientContactTwoElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientLicenseThreeElementAuthResult != null">
                #{clientLicenseThreeElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientBankCardThreeElementAuthenticationResult != null">
                #{clientBankCardThreeElementAuthenticationResult,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </insert>

    <!--丙晟门店批量入驻-->
            <select id="batchSaveWandaStore" resultMap="BaseResultMap">
                    INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND
        (`store_id`,`pre_check_err_msg`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.storeId,jdbcType=BIGINT},'')
        </foreach>
            </select>

            <!--获取丙晟最大门店ID-->
            <select id="getMaxBsStoreId" resultType="java.lang.Long">
                    SELECT /*MS-TP-LIFECIRCLE-WANDA-DATA-SYNC-TEMP-EXTEND-GETMAXBSSTOREID*/  MAX( `store_id`)  FROM `tp_lifecircle_wanda_data_sync_temp_extend`
        WHERE `store_id` LIKE '900%';
            </select>
    </mapper>
