<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.UsersEntryExtDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.UsersEntryExtDO">
            <id column="ID" property="id" jdbcType="BIGINT" javaType="Long"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CONTACT" property="contact" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_NO" property="licenseNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SHORT_NAME" property="shortName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NAME" property="storeName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_NOTE" property="storeNote" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_ADDRESS" property="bizAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_PIC" property="licensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_PHONE" property="storePhone" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ORG_CODE" property="bankOrgCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_AREA_CODE" property="bizAreaCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_AREA_NAME" property="bizAreaName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_CITY_CODE" property="bizCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_CITY_NAME" property="bizCityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_LATITUDE" property="bizLatitude" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="COMPANY_NAME" property="companyName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_LONGITUDE" property="bizLongitude" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CATEGORY_NAME" property="categoryName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ISS_AUTHORITY" property="issAuthority" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REJECT_REASON" property="rejectReason" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_CASH_PIC" property="storeCashPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_NO" property="legalIdCardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_END_DATE" property="licenseEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OTHER_ASSIST_PIC" property="otherAssistPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_CODE" property="settleBankCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_NAME" property="settleBankName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_INSIDE_PIC" property="storeInsidePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC1" property="storeOtherPic1" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC2" property="storeOtherPic2" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_PIC3" property="storeOtherPic3" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_PROVINCE_CODE" property="bizProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BIZ_PROVINCE_NAME" property="bizProvinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_NAME" property="legalIdCardName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NO" property="settleAccountNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_NO" property="settlerIdCardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_BEGIN_DATE" property="licenseBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PANORAMA_VIDEO_URL" property="panoramaVideoUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_MOBILE" property="settleBankMobile" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_CIRCLE_FLAG" property="settleCircleFlag" jdbcType="CHAR"
        javaType="String"/>

            <result column="STORE_DOOR_HEAD_PIC" property="storeDoorHeadPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HAND_HOLD_ID_CARD_PIC" property="handHoldIdCardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LICENSE_REG_ADDRESS" property="licenseRegAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_ACCOUNT_NAME" property="settleAccountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_CARD_PIC" property="settleBankCardPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_NAME" property="settlerIdCardName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="HAND_HOLD_LICENSE_PIC" property="handHoldLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_BACK_PIC" property="legalIdCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_END_DATE" property="legalIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEVEL1_CATEGORY_NAME" property="level1CategoryName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_BIZ_LICENSE_PIC" property="storeBizLicensePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_FRONT_PIC" property="legalIdCardFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_OPEN_PERMIT_PIC" property="settleOpenPermitPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="STORE_OTHER_VIDEOS_URL" property="storeOtherVideosUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_ID_CARD_BEGIN_DATE" property="legalIdCardBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_CODE" property="settleBankBranchCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_NAME" property="settleBankBranchName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_BACK_PIC" property="settlerIdCardBackPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_END_DATE" property="settlerIdCardEndDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_FRONT_PIC" property="settlerIdCardFrontPic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_ID_CARD_BEGIN_DATE" property="settlerIdCardBeginDate" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PANORAMA_VIDEO_PREVIEW_URL" property="panoramaVideoPreviewUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLER_NOT_LEGAL_PROVE_PIC" property="settlerNotLegalProvePic" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="LEGAL_PERSON_IDCARD_ADDRESS" property="legalPersonIdcardAddress" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_CITY_CODE" property="settleBankBranchCityCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_CITY_NAME" property="settleBankBranchCityName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_PROVINCE_CODE" property="settleBankBranchProvinceCode" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SETTLE_BANK_BRANCH_PROVINCE_NAME" property="settleBankBranchProvinceName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_DEL" property="isDel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STORE_ID" property="storeId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CREATE_ID" property="createId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CATEGORY_ID" property="categoryId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="IS_CRM_AUDIT" property="isCrmAudit" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IS_FROM_SYNC" property="isFromSync" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SIGN_STATUS" property="signStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUBMIT_STEP" property="submitStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMPANY_TYPE" property="companyType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="JOIN_CHANNEL" property="joinChannel" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLER_TYPE" property="settlerType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="IDENTITY_TYPE" property="identityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LICENSE_IS_LONG" property="licenseIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="QUALIFICATION" property="qualification" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="DATA_IS_FROM_CLEAN" property="dataIsFromClean" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="RESET_SUBMIT_STEP" property="resetSubmitStep" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="COMPANY_CHILD_TYPE" property="companyChildType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LEVEL1_CATEGORY_ID" property="level1CategoryId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="LEGAL_ID_CARD_IS_LONG" property="legalIdCardIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_ACCOUNT_TYPE" property="settleAccountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SUB_MCH_ID_ROUTE_FLAG" property="subMchIdRouteFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_IDENTITY_TYPE" property="settleIdentityType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WX_DR_CR_SEPARATE_FLAG" property="wxDrCrSeparateFlag" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLER_ID_CARD_IS_LONG" property="settlerIdCardIsLong" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LEGAL_ID_CARD_IS_MAIN_LOAD" property="legalIdCardIsMainLoad" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLER_ID_CARD_IS_MAIN_LOAD" property="settlerIdCardIsMainLoad" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="SETTLE_BANK_BRANCH_CODE_IS_HAND" property="settleBankBranchCodeIsHand" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="UPDATE_TIME" property="updateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`TOKEN`,`CONTACT`,`LICENSE_NO`,`SHORT_NAME`,`STORE_NAME`,`STORE_NOTE`,`BIZ_ADDRESS`,`LICENSE_PIC`,`STORE_PHONE`,`BANK_ORG_CODE`,`BIZ_AREA_CODE`,`BIZ_AREA_NAME`,`BIZ_CITY_CODE`,`BIZ_CITY_NAME`,`BIZ_LATITUDE`,`COMPANY_NAME`,`BIZ_LONGITUDE`,`CATEGORY_NAME`,`ISS_AUTHORITY`,`REJECT_REASON`,`STORE_CASH_PIC`,`LEGAL_ID_CARD_NO`,`LICENSE_END_DATE`,`OTHER_ASSIST_PIC`,`SETTLE_BANK_CODE`,`SETTLE_BANK_NAME`,`STORE_INSIDE_PIC`,`STORE_OTHER_PIC1`,`STORE_OTHER_PIC2`,`STORE_OTHER_PIC3`,`BIZ_PROVINCE_CODE`,`BIZ_PROVINCE_NAME`,`LEGAL_ID_CARD_NAME`,`SETTLE_ACCOUNT_NO`,`SETTLER_ID_CARD_NO`,`LICENSE_BEGIN_DATE`,`PANORAMA_VIDEO_URL`,`SETTLE_BANK_MOBILE`,`SETTLE_CIRCLE_FLAG`,`STORE_DOOR_HEAD_PIC`,`HAND_HOLD_ID_CARD_PIC`,`LICENSE_REG_ADDRESS`,`SETTLE_ACCOUNT_NAME`,`SETTLE_BANK_CARD_PIC`,`SETTLER_ID_CARD_NAME`,`HAND_HOLD_LICENSE_PIC`,`LEGAL_ID_CARD_BACK_PIC`,`LEGAL_ID_CARD_END_DATE`,`LEVEL1_CATEGORY_NAME`,`STORE_BIZ_LICENSE_PIC`,`LEGAL_ID_CARD_FRONT_PIC`,`SETTLE_OPEN_PERMIT_PIC`,`STORE_OTHER_VIDEOS_URL`,`LEGAL_ID_CARD_BEGIN_DATE`,`SETTLE_BANK_BRANCH_CODE`,`SETTLE_BANK_BRANCH_NAME`,`SETTLER_ID_CARD_BACK_PIC`,`SETTLER_ID_CARD_END_DATE`,`SETTLER_ID_CARD_FRONT_PIC`,`SETTLER_ID_CARD_BEGIN_DATE`,`PANORAMA_VIDEO_PREVIEW_URL`,`SETTLER_NOT_LEGAL_PROVE_PIC`,`LEGAL_PERSON_IDCARD_ADDRESS`,`SETTLE_BANK_BRANCH_CITY_CODE`,`SETTLE_BANK_BRANCH_CITY_NAME`,`SETTLE_BANK_BRANCH_PROVINCE_CODE`,`SETTLE_BANK_BRANCH_PROVINCE_NAME`,`UID`,`IS_DEL`,`STORE_ID`,`CREATE_ID`,`CATEGORY_ID`,`IS_CRM_AUDIT`,`IS_FROM_SYNC`,`SIGN_STATUS`,`SUBMIT_STEP`,`COMPANY_TYPE`,`JOIN_CHANNEL`,`SETTLER_TYPE`,`IDENTITY_TYPE`,`LICENSE_IS_LONG`,`QUALIFICATION`,`DATA_IS_FROM_CLEAN`,`RESET_SUBMIT_STEP`,`COMPANY_CHILD_TYPE`,`LEVEL1_CATEGORY_ID`,`LEGAL_ID_CARD_IS_LONG`,`SETTLE_ACCOUNT_TYPE`,`SUB_MCH_ID_ROUTE_FLAG`,`SETTLE_IDENTITY_TYPE`,`WX_DR_CR_SEPARATE_FLAG`,`SETTLER_ID_CARD_IS_LONG`,`LEGAL_ID_CARD_IS_MAIN_LOAD`,`SETTLER_ID_CARD_IS_MAIN_LOAD`,`SETTLE_BANK_BRANCH_CODE_IS_HAND`,`CREATE_TIME`,`UPDATE_TIME`
    </sql>


            <!--insert:TP_USERS_ENTRY_EXT-->
            <insert id="insert" >
                    INSERT INTO TP_USERS_ENTRY_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="contact != null">`CONTACT`,</if>
            <if test="licenseNo != null">`LICENSE_NO`,</if>
            <if test="shortName != null">`SHORT_NAME`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="storeNote != null">`STORE_NOTE`,</if>
            <if test="bizAddress != null">`BIZ_ADDRESS`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="storePhone != null">`STORE_PHONE`,</if>
            <if test="bankOrgCode != null">`BANK_ORG_CODE`,</if>
            <if test="bizAreaCode != null">`BIZ_AREA_CODE`,</if>
            <if test="bizAreaName != null">`BIZ_AREA_NAME`,</if>
            <if test="bizCityCode != null">`BIZ_CITY_CODE`,</if>
            <if test="bizCityName != null">`BIZ_CITY_NAME`,</if>
            <if test="bizLatitude != null">`BIZ_LATITUDE`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="bizLongitude != null">`BIZ_LONGITUDE`,</if>
            <if test="categoryName != null">`CATEGORY_NAME`,</if>
            <if test="issAuthority != null">`ISS_AUTHORITY`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="storeCashPic != null">`STORE_CASH_PIC`,</if>
            <if test="legalIdCardNo != null">`LEGAL_ID_CARD_NO`,</if>
            <if test="licenseEndDate != null">`LICENSE_END_DATE`,</if>
            <if test="otherAssistPic != null">`OTHER_ASSIST_PIC`,</if>
            <if test="settleBankCode != null">`SETTLE_BANK_CODE`,</if>
            <if test="settleBankName != null">`SETTLE_BANK_NAME`,</if>
            <if test="storeInsidePic != null">`STORE_INSIDE_PIC`,</if>
            <if test="storeOtherPic1 != null">`STORE_OTHER_PIC1`,</if>
            <if test="storeOtherPic2 != null">`STORE_OTHER_PIC2`,</if>
            <if test="storeOtherPic3 != null">`STORE_OTHER_PIC3`,</if>
            <if test="bizProvinceCode != null">`BIZ_PROVINCE_CODE`,</if>
            <if test="bizProvinceName != null">`BIZ_PROVINCE_NAME`,</if>
            <if test="legalIdCardName != null">`LEGAL_ID_CARD_NAME`,</if>
            <if test="settleAccountNo != null">`SETTLE_ACCOUNT_NO`,</if>
            <if test="settlerIdCardNo != null">`SETTLER_ID_CARD_NO`,</if>
            <if test="licenseBeginDate != null">`LICENSE_BEGIN_DATE`,</if>
            <if test="panoramaVideoUrl != null">`PANORAMA_VIDEO_URL`,</if>
            <if test="settleBankMobile != null">`SETTLE_BANK_MOBILE`,</if>
            <if test="settleCircleFlag != null">`SETTLE_CIRCLE_FLAG`,</if>
            <if test="storeDoorHeadPic != null">`STORE_DOOR_HEAD_PIC`,</if>
            <if test="handHoldIdCardPic != null">`HAND_HOLD_ID_CARD_PIC`,</if>
            <if test="licenseRegAddress != null">`LICENSE_REG_ADDRESS`,</if>
            <if test="settleAccountName != null">`SETTLE_ACCOUNT_NAME`,</if>
            <if test="settleBankCardPic != null">`SETTLE_BANK_CARD_PIC`,</if>
            <if test="settlerIdCardName != null">`SETTLER_ID_CARD_NAME`,</if>
            <if test="handHoldLicensePic != null">`HAND_HOLD_LICENSE_PIC`,</if>
            <if test="legalIdCardBackPic != null">`LEGAL_ID_CARD_BACK_PIC`,</if>
            <if test="legalIdCardEndDate != null">`LEGAL_ID_CARD_END_DATE`,</if>
            <if test="level1CategoryName != null">`LEVEL1_CATEGORY_NAME`,</if>
            <if test="storeBizLicensePic != null">`STORE_BIZ_LICENSE_PIC`,</if>
            <if test="legalIdCardFrontPic != null">`LEGAL_ID_CARD_FRONT_PIC`,</if>
            <if test="settleOpenPermitPic != null">`SETTLE_OPEN_PERMIT_PIC`,</if>
            <if test="storeOtherVideosUrl != null">`STORE_OTHER_VIDEOS_URL`,</if>
            <if test="legalIdCardBeginDate != null">`LEGAL_ID_CARD_BEGIN_DATE`,</if>
            <if test="settleBankBranchCode != null">`SETTLE_BANK_BRANCH_CODE`,</if>
            <if test="settleBankBranchName != null">`SETTLE_BANK_BRANCH_NAME`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="panoramaVideoPreviewUrl != null">`PANORAMA_VIDEO_PREVIEW_URL`,</if>
            <if test="settlerNotLegalProvePic != null">`SETTLER_NOT_LEGAL_PROVE_PIC`,</if>
            <if test="legalPersonIdcardAddress != null">`LEGAL_PERSON_IDCARD_ADDRESS`,</if>
            <if test="settleBankBranchCityCode != null">`SETTLE_BANK_BRANCH_CITY_CODE`,</if>
            <if test="settleBankBranchCityName != null">`SETTLE_BANK_BRANCH_CITY_NAME`,</if>
            <if test="settleBankBranchProvinceCode != null">`SETTLE_BANK_BRANCH_PROVINCE_CODE`,</if>
            <if test="settleBankBranchProvinceName != null">`SETTLE_BANK_BRANCH_PROVINCE_NAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createId != null">`CREATE_ID`,</if>
            <if test="categoryId != null">`CATEGORY_ID`,</if>
            <if test="isCrmAudit != null">`IS_CRM_AUDIT`,</if>
            <if test="isFromSync != null">`IS_FROM_SYNC`,</if>
            <if test="signStatus != null">`SIGN_STATUS`,</if>
            <if test="submitStep != null">`SUBMIT_STEP`,</if>
            <if test="companyType != null">`COMPANY_TYPE`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="settlerType != null">`SETTLER_TYPE`,</if>
            <if test="identityType != null">`IDENTITY_TYPE`,</if>
            <if test="licenseIsLong != null">`LICENSE_IS_LONG`,</if>
            <if test="qualification != null">`QUALIFICATION`,</if>
            <if test="dataIsFromClean != null">`DATA_IS_FROM_CLEAN`,</if>
            <if test="resetSubmitStep != null">`RESET_SUBMIT_STEP`,</if>
            <if test="companyChildType != null">`COMPANY_CHILD_TYPE`,</if>
            <if test="level1CategoryId != null">`LEVEL1_CATEGORY_ID`,</if>
            <if test="legalIdCardIsLong != null">`LEGAL_ID_CARD_IS_LONG`,</if>
            <if test="settleAccountType != null">`SETTLE_ACCOUNT_TYPE`,</if>
            <if test="subMchIdRouteFlag != null">`SUB_MCH_ID_ROUTE_FLAG`,</if>
            <if test="settleIdentityType != null">`SETTLE_IDENTITY_TYPE`,</if>
            <if test="wxDrCrSeparateFlag != null">`WX_DR_CR_SEPARATE_FLAG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="legalIdCardIsMainLoad != null">`LEGAL_ID_CARD_IS_MAIN_LOAD`,</if>
            <if test="settlerIdCardIsMainLoad != null">`SETTLER_ID_CARD_IS_MAIN_LOAD`,</if>
            <if test="settleBankBranchCodeIsHand != null">`SETTLE_BANK_BRANCH_CODE_IS_HAND`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="contact != null">#{contact,jdbcType=VARCHAR},</if>
            <if test="licenseNo != null">#{licenseNo,jdbcType=VARCHAR},</if>
            <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="storeNote != null">#{storeNote,jdbcType=VARCHAR},</if>
            <if test="bizAddress != null">#{bizAddress,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="storePhone != null">#{storePhone,jdbcType=VARCHAR},</if>
            <if test="bankOrgCode != null">#{bankOrgCode,jdbcType=VARCHAR},</if>
            <if test="bizAreaCode != null">#{bizAreaCode,jdbcType=VARCHAR},</if>
            <if test="bizAreaName != null">#{bizAreaName,jdbcType=VARCHAR},</if>
            <if test="bizCityCode != null">#{bizCityCode,jdbcType=VARCHAR},</if>
            <if test="bizCityName != null">#{bizCityName,jdbcType=VARCHAR},</if>
            <if test="bizLatitude != null">#{bizLatitude,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="bizLongitude != null">#{bizLongitude,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="issAuthority != null">#{issAuthority,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="storeCashPic != null">#{storeCashPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardNo != null">#{legalIdCardNo,jdbcType=VARCHAR},</if>
            <if test="licenseEndDate != null">#{licenseEndDate,jdbcType=VARCHAR},</if>
            <if test="otherAssistPic != null">#{otherAssistPic,jdbcType=VARCHAR},</if>
            <if test="settleBankCode != null">#{settleBankCode,jdbcType=VARCHAR},</if>
            <if test="settleBankName != null">#{settleBankName,jdbcType=VARCHAR},</if>
            <if test="storeInsidePic != null">#{storeInsidePic,jdbcType=VARCHAR},</if>
            <if test="storeOtherPic1 != null">#{storeOtherPic1,jdbcType=VARCHAR},</if>
            <if test="storeOtherPic2 != null">#{storeOtherPic2,jdbcType=VARCHAR},</if>
            <if test="storeOtherPic3 != null">#{storeOtherPic3,jdbcType=VARCHAR},</if>
            <if test="bizProvinceCode != null">#{bizProvinceCode,jdbcType=VARCHAR},</if>
            <if test="bizProvinceName != null">#{bizProvinceName,jdbcType=VARCHAR},</if>
            <if test="legalIdCardName != null">#{legalIdCardName,jdbcType=VARCHAR},</if>
            <if test="settleAccountNo != null">#{settleAccountNo,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardNo != null">#{settlerIdCardNo,jdbcType=VARCHAR},</if>
            <if test="licenseBeginDate != null">#{licenseBeginDate,jdbcType=VARCHAR},</if>
            <if test="panoramaVideoUrl != null">#{panoramaVideoUrl,jdbcType=VARCHAR},</if>
            <if test="settleBankMobile != null">#{settleBankMobile,jdbcType=VARCHAR},</if>
            <if test="settleCircleFlag != null">#{settleCircleFlag,jdbcType=CHAR},</if>
            <if test="storeDoorHeadPic != null">#{storeDoorHeadPic,jdbcType=VARCHAR},</if>
            <if test="handHoldIdCardPic != null">#{handHoldIdCardPic,jdbcType=VARCHAR},</if>
            <if test="licenseRegAddress != null">#{licenseRegAddress,jdbcType=VARCHAR},</if>
            <if test="settleAccountName != null">#{settleAccountName,jdbcType=VARCHAR},</if>
            <if test="settleBankCardPic != null">#{settleBankCardPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardName != null">#{settlerIdCardName,jdbcType=VARCHAR},</if>
            <if test="handHoldLicensePic != null">#{handHoldLicensePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBackPic != null">#{legalIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardEndDate != null">#{legalIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="level1CategoryName != null">#{level1CategoryName,jdbcType=VARCHAR},</if>
            <if test="storeBizLicensePic != null">#{storeBizLicensePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardFrontPic != null">#{legalIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="settleOpenPermitPic != null">#{settleOpenPermitPic,jdbcType=VARCHAR},</if>
            <if test="storeOtherVideosUrl != null">#{storeOtherVideosUrl,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBeginDate != null">#{legalIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchCode != null">#{settleBankBranchCode,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchName != null">#{settleBankBranchName,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="panoramaVideoPreviewUrl != null">#{panoramaVideoPreviewUrl,jdbcType=VARCHAR},</if>
            <if test="settlerNotLegalProvePic != null">#{settlerNotLegalProvePic,jdbcType=VARCHAR},</if>
            <if test="legalPersonIdcardAddress != null">#{legalPersonIdcardAddress,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchCityCode != null">#{settleBankBranchCityCode,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchCityName != null">#{settleBankBranchCityName,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchProvinceCode != null">#{settleBankBranchProvinceCode,jdbcType=VARCHAR},</if>
            <if test="settleBankBranchProvinceName != null">#{settleBankBranchProvinceName,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createId != null">#{createId,jdbcType=INTEGER},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=INTEGER},</if>
            <if test="isCrmAudit != null">#{isCrmAudit,jdbcType=TINYINT},</if>
            <if test="isFromSync != null">#{isFromSync,jdbcType=TINYINT},</if>
            <if test="signStatus != null">#{signStatus,jdbcType=TINYINT},</if>
            <if test="submitStep != null">#{submitStep,jdbcType=TINYINT},</if>
            <if test="companyType != null">#{companyType,jdbcType=TINYINT},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="settlerType != null">#{settlerType,jdbcType=TINYINT},</if>
            <if test="identityType != null">#{identityType,jdbcType=TINYINT},</if>
            <if test="licenseIsLong != null">#{licenseIsLong,jdbcType=TINYINT},</if>
            <if test="qualification != null">#{qualification,jdbcType=TINYINT},</if>
            <if test="dataIsFromClean != null">#{dataIsFromClean,jdbcType=TINYINT},</if>
            <if test="resetSubmitStep != null">#{resetSubmitStep,jdbcType=TINYINT},</if>
            <if test="companyChildType != null">#{companyChildType,jdbcType=TINYINT},</if>
            <if test="level1CategoryId != null">#{level1CategoryId,jdbcType=INTEGER},</if>
            <if test="legalIdCardIsLong != null">#{legalIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="settleAccountType != null">#{settleAccountType,jdbcType=TINYINT},</if>
            <if test="subMchIdRouteFlag != null">#{subMchIdRouteFlag,jdbcType=TINYINT},</if>
            <if test="settleIdentityType != null">#{settleIdentityType,jdbcType=TINYINT},</if>
            <if test="wxDrCrSeparateFlag != null">#{wxDrCrSeparateFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="legalIdCardIsMainLoad != null">#{legalIdCardIsMainLoad,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsMainLoad != null">#{settlerIdCardIsMainLoad,jdbcType=TINYINT},</if>
            <if test="settleBankBranchCodeIsHand != null">#{settleBankBranchCodeIsHand,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
            </insert>

            <!--根据uid查询-->
            <select id="getByUid" resultMap="BaseResultMap">
                    SELECT /*MS-TP-USERS-ENTRY-EXT-GETBYUID*/  <include refid="Base_Column_List" />
        FROM tp_users_entry_ext
        WHERE uid = #{uid,jdbcType=INTEGER}
        AND is_del = 0
            </select>

            <!--查询商户入驻扩展信息-->
            <select id="getUsersEntryExtByUid" resultMap="BaseResultMap">
                    SELECT
        <include refid="Base_Column_List" />
        FROM
        TP_USERS_ENTRY_EXT
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        AND
        is_del = 0
            </select>

            <!--update table:TP_WECHAT_CODE-->
            <update id="updateClearResetSubmit" >
                    UPDATE /*MS-TP-USERS-ENTRY-EXT-UPDATECLEARRESETSUBMIT*/ tp_users_entry_ext
        SET reset_submit_step = 0
        WHERE uid = #{uid,jdbcType=INTEGER}
        LIMIT 1
            </update>

    <!--insert:TP_USERS_ENTRY_EXT-->
    <insert id="insertMerchantOpenAccountSubmit">
        INSERT INTO TP_USERS_ENTRY_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="submitStep != null">`SUBMIT_STEP`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="shortName != null">`SHORT_NAME`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="submitStep != null">#{submitStep,jdbcType=TINYINT},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
        </trim>
    </insert>
    </mapper>
