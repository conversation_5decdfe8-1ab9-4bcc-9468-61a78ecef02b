<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
    <mapper namespace="com.fshows.lifecircle.storagecore.service.dal.lifecircle.mapper.LifecircleWithdrawDOMapper">

<resultMap id="BaseResultMap" type="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.LifecircleWithdrawDO">
            <id column="ID" property="id" jdbcType="INTEGER" javaType="Integer"/>

            <result column="INFO" property="info" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="TOKEN" property="token" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_NO" property="cardNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="REMARKS" property="remarks" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="CARD_BANK" property="cardBank" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NO" property="serialNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="NOTIFY_URL" property="notifyUrl" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="FRONT_LOG_NO" property="frontLogNo" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="ACCOUNT_NAME" property="accountName" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="SERIAL_NUMBER" property="serialNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="BANK_ACCOUNT_ID" property="bankAccountId" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="OUT_SERIAL_NUMBER" property="outSerialNumber" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="WITHDRAW_VOUCHER" property="withdrawVoucher" jdbcType="VARCHAR"
        javaType="String"/>

            <result column="PID" property="pid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="UID" property="uid" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TYPE" property="type" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="STATUS" property="status" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="USER_ID" property="userId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="AGENT_ID" property="agentId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_CODE" property="bankCode" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BANK_TYPE" property="bankType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CASH_TYPE" property="cashType" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="TRAN_TIME" property="tranTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="BIND_BANK_ID" property="bindBankId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="CASH_STATUS" property="cashStatus" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="CREATE_TIME" property="createTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="FINISH_TIME" property="finishTime" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SALESMAN_ID" property="salesmanId" jdbcType="INTEGER"
        javaType="Integer"/>

            <result column="SETTLE_MODE" property="settleMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="BANK_ACCOUNT_TYPE" property="bankAccountType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="LIQUIDATION_TYPE" property="liquidationType" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="WITHDRAWALS_MODE" property="withdrawalsMode" jdbcType="TINYINT"
        javaType="Integer"/>

            <result column="AUTO_UPDATE_TIME" property="autoUpdateTime" jdbcType="TIMESTAMP"
        javaType="java.util.Date"/>

            <result column="BANK_RATE" property="bankRate" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CASH_AMOUNT" property="cashAmount" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="BANK_CHARGES" property="bankCharges" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>

            <result column="CHANGE_TODAY_BALANCE" property="changeTodayBalance" jdbcType="DECIMAL"
        javaType="java.math.BigDecimal"/>
    </resultMap>


    <sql id="Base_Column_List">
    `ID`,`INFO`,`TOKEN`,`CARD_NO`,`REMARKS`,`CARD_BANK`,`SERIAL_NO`,`NOTIFY_URL`,`FRONT_LOG_NO`,`ACCOUNT_NAME`,`SERIAL_NUMBER`,`BANK_ACCOUNT_ID`,`OUT_SERIAL_NUMBER`,`WITHDRAW_VOUCHER`,`PID`,`UID`,`TYPE`,`STATUS`,`USER_ID`,`AGENT_ID`,`BANK_CODE`,`BANK_TYPE`,`CASH_TYPE`,`TRAN_TIME`,`BIND_BANK_ID`,`CASH_STATUS`,`CREATE_TIME`,`FINISH_TIME`,`SALESMAN_ID`,`SETTLE_MODE`,`BANK_ACCOUNT_TYPE`,`LIQUIDATION_TYPE`,`WITHDRAWALS_MODE`,`AUTO_UPDATE_TIME`,`BANK_RATE`,`CASH_AMOUNT`,`BANK_CHARGES`,`CHANGE_TODAY_BALANCE`
    </sql>


            <!--insert:TP_LIFECIRCLE_WITHDRAW-->
            <insert id="insert" >
                    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_LIFECIRCLE_WITHDRAW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="info != null">`INFO`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="cardBank != null">`CARD_BANK`,</if>
            <if test="serialNo != null">`SERIAL_NO`,</if>
            <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
            <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="cashType != null">`CASH_TYPE`,</if>
            <if test="tranTime != null">`TRAN_TIME`,</if>
            <if test="cashStatus != null">`CASH_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="withdrawalsMode != null">`WITHDRAWALS_MODE`,</if>
            <if test="bankRate != null">`BANK_RATE`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
            <if test="bankCharges != null">`BANK_CHARGES`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="info != null">#{info,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="pid != null">#{pid,jdbcType=INTEGER},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
            <if test="cashType != null">#{cashType,jdbcType=INTEGER},</if>
            <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="withdrawalsMode != null">#{withdrawalsMode,jdbcType=TINYINT},</if>
            <if test="bankRate != null">#{bankRate,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
            <if test="bankCharges != null">#{bankCharges,jdbcType=DECIMAL},</if>
        </trim>
            </insert>

            <!--根据订单号更新提现记录状态-->
            <update id="updateStatusByOrderSn" >
                    UPDATE
        TP_LIFECIRCLE_WITHDRAW
        SET
        status = #{status,jdbcType=INTEGER},
        finish_time = #{finishTime,jdbcType=INTEGER}
        WHERE
        front_log_no = #{frontLogNo,jdbcType=VARCHAR}
            </update>

            <!--批量查询状态不为成功的提现数据-->
            <select id="getDiffWithdrawList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-WITHDRAW-GETDIFFWITHDRAWLIST*/  <include refid="Base_Column_List" /> FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
        AND STATUS = 2
            </select>

            <!--更新提现状态和时间(单条)-->
            <update id="updateDiffWithdraw" >
                    update /*MS-TP-LIFECIRCLE-WITHDRAW-UPDATEDIFFWITHDRAW*/ TP_LIFECIRCLE_WITHDRAW
        set
        finish_time = #{finishTime,jdbcType=INTEGER},
        tran_time = #{tranTime,jdbcType=INTEGER},
        status = 1,
        cash_status = 7,
        card_no = #{cardNo,jdbcType=VARCHAR},
        account_name = #{accountName,jdbcType=VARCHAR},
        card_bank = #{cardBank,jdbcType=VARCHAR}
        where SERIAL_NUMBER=#{serialNumber,jdbcType=VARCHAR}
            </update>

            <!--批量查询提现数据,用于补提状态更新-->
            <select id="getWithdrawList" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-WITHDRAW-GETWITHDRAWLIST*/  <include refid="Base_Column_List" /> FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
            </select>

            <!--更新提现状态和时间(单条)-->
            <update id="updateDiffWithdrawBySxPay" >
                    update /*MS-TP-LIFECIRCLE-WITHDRAW-UPDATEDIFFWITHDRAWBYSXPAY*/ TP_LIFECIRCLE_WITHDRAW
        set
        tran_time = #{tranTime,jdbcType=INTEGER},
        status = 1,
        cash_status = 7,
        card_no = #{cardNo,jdbcType=VARCHAR},
        account_name = #{accountName,jdbcType=VARCHAR},
        card_bank = #{cardBank,jdbcType=VARCHAR}
        where SERIAL_NUMBER=#{serialNumber,jdbcType=VARCHAR}
            </update>

            <!--通过serial_number获取提现单信息-->
            <select id="getBySerialNumber" resultMap="BaseResultMap">
                    SELECT /*MS-TP-LIFECIRCLE-WITHDRAW-GETBYSERIALNUMBER*/  <include refid="Base_Column_List" /> FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR}
        limit 1
            </select>
    </mapper>
