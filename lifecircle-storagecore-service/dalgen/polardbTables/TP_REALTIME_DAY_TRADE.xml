<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_REALTIME_DAY_TRADE" physicalName="TP_REALTIME_DAY_TRADE"
       remark="实时日维度交易表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_REALTIME_DAY_TRADE">
        INSERT INTO TP_REALTIME_DAY_TRADE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="mode != null">`MODE`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="tradeDay != null">`TRADE_DAY`,</if>
            <if test="tradeNum != null">`TRADE_NUM`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="refundNum != null">`REFUND_NUM`,</if>
            <if test="tradeWeek != null">`TRADE_WEEK`,</if>
            <if test="tradeMonth != null">`TRADE_MONTH`,</if>
            <if test="incomeTradeNum != null">`INCOME_TRADE_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="allFee != null">`ALL_FEE`,</if>
            <if test="tradeMoney != null">`TRADE_MONEY`,</if>
            <if test="incomeMoney != null">`INCOME_MONEY`,</if>
            <if test="refundMoney != null">`REFUND_MONEY`,</if>
            <if test="accountMoney != null">`ACCOUNT_MONEY`,</if>
            <if test="tradeRealMoney != null">`TRADE_REAL_MONEY`,</if>
            <if test="preferentialAmount != null">`PREFERENTIAL_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="mode != null">#{mode,jdbcType=TINYINT},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="tradeDay != null">#{tradeDay,jdbcType=INTEGER},</if>
            <if test="tradeNum != null">#{tradeNum,jdbcType=INTEGER},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="refundNum != null">#{refundNum,jdbcType=INTEGER},</if>
            <if test="tradeWeek != null">#{tradeWeek,jdbcType=TINYINT},</if>
            <if test="tradeMonth != null">#{tradeMonth,jdbcType=INTEGER},</if>
            <if test="incomeTradeNum != null">#{incomeTradeNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="allFee != null">#{allFee,jdbcType=DECIMAL},</if>
            <if test="tradeMoney != null">#{tradeMoney,jdbcType=DECIMAL},</if>
            <if test="incomeMoney != null">#{incomeMoney,jdbcType=DECIMAL},</if>
            <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
            <if test="accountMoney != null">#{accountMoney,jdbcType=DECIMAL},</if>
            <if test="tradeRealMoney != null">#{tradeRealMoney,jdbcType=DECIMAL},</if>
            <if test="preferentialAmount != null">#{preferentialAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>


    <operation name="getTradeTokenList" multiplicity="many" resulttype="java.lang.String" remark="获取交易商户Token列表">
        SELECT DISTINCT token FROM TP_REALTIME_DAY_TRADE
        WHERE trade_day BETWEEN #{startTime,jdbcType=INTEGER} AND #{endTime,jdbcType=INTEGER}
    </operation>

    <operation name="getTradeDayTokenCount" resulttype="java.lang.Integer" remark="获取交易商户Token总数">
        SELECT COUNT(DISTINCT token) FROM TP_REALTIME_DAY_TRADE WHERE trade_day = #{tradeDay,jdbcType=INTEGER}
    </operation>

    <operation name="getTradeDayTokenListByPage" multiplicity="many" resulttype="java.lang.String"
               remark="获取交易商户Token总数">
        SELECT DISTINCT token FROM TP_REALTIME_DAY_TRADE WHERE trade_day = #{tradeDay,jdbcType=INTEGER} ORDER BY token
        LIMIT #{startRow,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </operation>


    <operation name="findTradeDayListCount" multiplicity="one" resulttype="java.lang.Integer" remark="商户每天交易总数">
        SELECT <![CDATA[ count(*) ]]>
        FROM TP_REALTIME_DAY_TRADE
        WHERE trade_day = #{tradeDay,jdbcType=INTEGER}
        AND DEVICE_NO IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </operation>


    <operation name="getTradeDayList" multiplicity="paging" paging="SearchTradeDayList" remark="获取每天商户交易总数">
        SELECT *
        FROM TP_REALTIME_DAY_TRADE
        WHERE trade_day = #{tradeDay,jdbcType=INTEGER}
        AND DEVICE_NO IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
             #{item,jdbcType=VARCHAR}
       </foreach>
    </operation>

    <operation name="findMerchantTradeBillSummery" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.MerchantTradeSummeryDTO" remark="获取商户交易汇总">
        select
        token as token,
        -- 收款笔数
        sum(trade_num) as tradeNum,
        -- 交易金额
        sum(trade_real_money) as tradeMoney,
        -- 到账金额
        sum(account_money + subsidy_money) as accountMoney,
        -- 支付宝
        sum(
        case
        when pay_type in (2, 7, 21, 22, 23, 24) then trade_real_money
        else 0.00
        end
        ) as alipayTradeMoney,
        sum(
        case
        when pay_type in (2, 7, 21, 22, 23, 24) then account_money + subsidy_money
        else 0.00
        end
        ) as alipayAccountMoney,
        -- 微信
        sum(
        case
        when pay_type in (1, 11, 12, 13) then trade_real_money
        else 0.00
        end
        ) as wxTradeMoney,
        sum(
        case
        when pay_type in (1, 11, 12, 13) then account_money + subsidy_money
        else 0.00
        end
        ) as wxAccountMoney,
        -- 银联
        sum(
        case
        when pay_type in (5) then trade_real_money
        else 0.00
        end
        ) as unionTradeMoney,
        sum(
        case
        when pay_type in (5) then account_money + subsidy_money
        else 0.00
        end
        ) as unionAccountMoney
        FROM
        TP_REALTIME_DAY_TRADE
        WHERE
        token in
        <foreach collection="list" item="token" open="(" separator="," close=")">
            #{token,jdbcType=VARCHAR}
        </foreach>
        AND trade_day between #{startDay,jdbcType=INTEGER} and #{endDay,jdbcType=INTEGER}
        group by token
    </operation>

    <operation name="findTradeBillSummery" multiplicity="many" remark="获取商户交易汇总">
        select
        token as token,
        -- 收款笔数
        sum(trade_num) as trade_num,
        -- 交易金额
        sum(trade_money) as trade_money,
        -- 收款金额
        sum(trade_real_money) as trade_real_money,
        -- 实收金额
        sum(income_money) as income_money,
        -- 到账金额
        sum(account_money+subsidy_money) as account_money
        FROM TP_REALTIME_DAY_TRADE
        WHERE
        token in
        <foreach collection="list" item="token" open="(" separator="," close=")">
            #{token,jdbcType=VARCHAR}
        </foreach>
        AND trade_day between #{startDay,jdbcType=INTEGER} and #{endDay,jdbcType=INTEGER}
        <if test="payTypeList != null and payTypeList.size() > 0">
            AND pay_type in
            <foreach collection="payTypeList" item="payType" open="(" close=")" separator=",">
                #{payType,jdbcType=INTEGER}
            </foreach>
        </if>
        group by token
    </operation>

</table>
