<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_ACTIVITY_POLICY_TARGET" physicalName="LM_CRM_ACTIVITY_POLICY_TARGET"
       remark="活动政策报名对象表（面向线下登记活动）">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_ACTIVITY_POLICY_TARGET">
        INSERT INTO LM_CRM_ACTIVITY_POLICY_TARGET
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="targetId != null">`TARGET_ID`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="specialMark != null">`SPECIAL_MARK`,</if>
            <if test="registerStatus != null">`REGISTER_STATUS`,</if>
            <if test="activityTargetType != null">`ACTIVITY_TARGET_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="settleAgentId != null">`SETTLE_AGENT_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="targetId != null">#{targetId,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="specialMark != null">#{specialMark,jdbcType=TINYINT},</if>
            <if test="registerStatus != null">#{registerStatus,jdbcType=TINYINT},</if>
            <if test="activityTargetType != null">#{activityTargetType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="settleAgentId != null">#{settleAgentId,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="findListByActivityId" multiplicity="many">
        SELECT *
        FROM LM_CRM_ACTIVITY_POLICY_TARGET
        WHERE is_del = 0
        <if test="list != null and list.size() &gt; 0">
            AND activity_id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入活动政策报名">
        INSERT INTO LM_CRM_ACTIVITY_POLICY_TARGET (
        `REMARK`,
        `ACTIVITY_ID`,
        `IS_DEL`,
        `END_TIME`,
        `TARGET_ID`,
        `START_TIME`,
        `SPECIAL_MARK`,
        `REGISTER_STATUS`,
        `ACTIVITY_TARGET_TYPE`,
        `SETTLE_AGENT_ID`
        )VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.remark,jdbcType=VARCHAR},
            #{item.activityId,jdbcType=VARCHAR},
            #{item.isDel,jdbcType=TINYINT},
            #{item.endTime,jdbcType=INTEGER},
            #{item.targetId,jdbcType=INTEGER},
            #{item.startTime,jdbcType=INTEGER},
            #{item.specialMark,jdbcType=TINYINT},
            #{item.registerStatus,jdbcType=TINYINT},
            #{item.activityTargetType,jdbcType=TINYINT},
            #{item.settleAgentId,jdbcType=INTEGER}
            )
        </foreach>
    </operation>

    <operation name="findPage" multiplicity="paging" paging="ActivityPolicyTargetPage">
        SELECT *
        FROM LM_CRM_ACTIVITY_POLICY_TARGET
        WHERE
        is_del = 0
        AND activity_target_type = #{activityTargetType,jdbcType=INTEGER}
        <if test="activityIdList != null and activityIdList.size() &gt; 0">
            AND activity_id IN
            <foreach collection="activityIdList" item="activityId" open="(" close=")" separator=",">
                #{activityId,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="targetIdList != null and targetIdList.size() &gt; 0">
            AND target_id IN
            <foreach collection="targetIdList" item="targetId" open="(" close=")" separator=",">
                #{targetId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="registerStatus != null and registerStatus != 0">
            AND register_status = #{registerStatus,jdbcType=INTEGER}
        </if>
        <if test="specialMark != null and specialMark != 0">
            AND special_mark = #{specialMark,jdbcType=INTEGER}
        </if>
    </operation>

</table>
