<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS" physicalName="TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS"
       remark="国补商品表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS">
        INSERT INTO TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="productId != null">`PRODUCT_ID`,</if>
            <if test="productName != null">`PRODUCT_NAME`,</if>
            <if test="goodsBarCode != null">`GOODS_BAR_CODE`,</if>
            <if test="productBrand != null">`PRODUCT_BRAND`,</if>
            <if test="productImage != null">`PRODUCT_IMAGE`,</if>
            <if test="productModel != null">`PRODUCT_MODEL`,</if>
            <if test="productCategory != null">`PRODUCT_CATEGORY`,</if>
            <if test="energyEfficiency != null">`ENERGY_EFFICIENCY`,</if>
            <if test="manufactCertCode != null">`MANUFACT_CERT_CODE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="productSort != null">`PRODUCT_SORT`,</if>
            <if test="availableStatus != null">`AVAILABLE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="basisPrice != null">`BASIS_PRICE`,</if>
            <if test="productPrice != null">`PRODUCT_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="productId != null">#{productId,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="goodsBarCode != null">#{goodsBarCode,jdbcType=VARCHAR},</if>
            <if test="productBrand != null">#{productBrand,jdbcType=VARCHAR},</if>
            <if test="productImage != null">#{productImage,jdbcType=VARCHAR},</if>
            <if test="productModel != null">#{productModel,jdbcType=VARCHAR},</if>
            <if test="productCategory != null">#{productCategory,jdbcType=VARCHAR},</if>
            <if test="energyEfficiency != null">#{energyEfficiency,jdbcType=VARCHAR},</if>
            <if test="manufactCertCode != null">#{manufactCertCode,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="productSort != null">#{productSort,jdbcType=INTEGER},</if>
            <if test="availableStatus != null">#{availableStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="basisPrice != null">#{basisPrice,jdbcType=DECIMAL},</if>
            <if test="productPrice != null">#{productPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="countByGoodsBarCode" resulttype="java.lang.Integer">
        SELECT count(*) from TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS
        WHERE GOODS_BAR_CODE = #{goodsBarCode,jdbcType=VARCHAR}
        and uid = #{uid,jdbcType=INTEGER}
        and is_del = 0
    </operation>
</table>
