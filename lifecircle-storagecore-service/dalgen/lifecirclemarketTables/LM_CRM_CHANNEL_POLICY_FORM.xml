<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_CHANNEL_POLICY_FORM" physicalName="LM_CRM_CHANNEL_POLICY_FORM"
       remark="渠道政策报名表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_CHANNEL_POLICY_FORM">
        INSERT INTO LM_CRM_CHANNEL_POLICY_FORM
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="policyName != null">`POLICY_NAME`,</if>
            <if test="policyFormId != null">`POLICY_FORM_ID`,</if>
            <if test="provinceManager != null">`PROVINCE_MANAGER`,</if>
            <if test="operationalNotes != null">`OPERATIONAL_NOTES`,</if>
            <if test="relatedSignIdList != null">`RELATED_SIGN_ID_LIST`,</if>
            <if test="paymentProofUrlList != null">`PAYMENT_PROOF_URL_LIST`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="relatedAgentId != null">`RELATED_AGENT_ID`,</if>
            <if test="settlementStartTime != null">`SETTLEMENT_START_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="policyName != null">#{policyName,jdbcType=VARCHAR},</if>
            <if test="policyFormId != null">#{policyFormId,jdbcType=VARCHAR},</if>
            <if test="provinceManager != null">#{provinceManager,jdbcType=VARCHAR},</if>
            <if test="operationalNotes != null">#{operationalNotes,jdbcType=VARCHAR},</if>
            <if test="relatedSignIdList != null">#{relatedSignIdList,jdbcType=VARCHAR},</if>
            <if test="paymentProofUrlList != null">#{paymentProofUrlList,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="relatedAgentId != null">#{relatedAgentId,jdbcType=INTEGER},</if>
            <if test="settlementStartTime != null">#{settlementStartTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO
        LM_CRM_CHANNEL_POLICY_FORM(POLICY_ID,POLICY_NAME,POLICY_FORM_ID,USER_ID,PARENT_ID,AUDIT_STATUS,SUBMIT_TIME,SETTLEMENT_START_TIME,OPERATIONAL_NOTES)
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.policyId,jdbcType=VARCHAR},
            #{item.policyName,jdbcType=VARCHAR},
            #{item.policyFormId,jdbcType=VARCHAR},
            #{item.userId,jdbcType=INTEGER},
            #{item.parentId,jdbcType=INTEGER},
            #{item.auditStatus,jdbcType=TINYINT},
            #{item.submitTime,jdbcType=TIMESTAMP},
            #{item.settlementStartTime,jdbcType=INTEGER},
            #{item.operationalNotes,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>
    
    <operation name="getParentIdByAgentIdAndPolicyId" multiplicity="one" remark="查询报名代理商上级">
        SELECT
        parent_id
        FROM LM_CRM_CHANNEL_POLICY_FORM
        WHERE
        user_id = #{agentId,jdbcType=INTEGER}
        and policy_id = #{policyId,jdbcType=VARCHAR}
        and parent_id != 0
        and is_del = 0
        LIMIT 1
    </operation>

    <operation name="getByAgentIdAndPolicyId" multiplicity="one" remark="查询报名代理商">
        SELECT
        *
        FROM LM_CRM_CHANNEL_POLICY_FORM
        WHERE
        user_id = #{agentId,jdbcType=INTEGER}
        and policy_id = #{policyId,jdbcType=VARCHAR}
        and is_del = 0
        LIMIT 1
    </operation>
</table>
