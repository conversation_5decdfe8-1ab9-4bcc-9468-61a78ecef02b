<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_ALIPAY_TASK_EMUN" physicalName="LM_CRM_ALIPAY_TASK_EMUN"
       remark="支付宝碰一下枚举表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_ALIPAY_TASK_EMUN">
        INSERT INTO LM_CRM_ALIPAY_TASK_EMUN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reasonDesc != null">`REASON_DESC`,</if>
            <if test="reasonType != null">`REASON_TYPE`,</if>
            <if test="reasonValue != null">`REASON_VALUE`,</if>
            <if test="reasonTypeDesc != null">`REASON_TYPE_DESC`,</if>
            <if test="businessType != null">`BUSINESS_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="reasonDesc != null">#{reasonDesc,jdbcType=VARCHAR},</if>
            <if test="reasonType != null">#{reasonType,jdbcType=VARCHAR},</if>
            <if test="reasonValue != null">#{reasonValue,jdbcType=VARCHAR},</if>
            <if test="reasonTypeDesc != null">#{reasonTypeDesc,jdbcType=VARCHAR},</if>
            <if test="businessType != null">#{businessType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByBusinessType" paramtype="primitive" multiplicity="many">
        SELECT *
        FROM LM_CRM_ALIPAY_TASK_EMUN
        WHERE BUSINESS_TYPE = #{businessType,jdbcType=TINYINT}
    </operation>
</table>
