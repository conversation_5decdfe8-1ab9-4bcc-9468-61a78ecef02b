<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table physicalName="LM_CRM_USER" remark="LM_CRM_USER" sqlname="LM_CRM_USER">

    <operation name="insert" paramtype="object" remark="insert:LM_CRM_USER">
        INSERT INTO LM_CRM_USER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="sex != null">`SEX`,</if>
            <if test="salt != null">`SALT`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="avatar != null">`AVATAR`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="addSource != null">`ADD_SOURCE`,</if>
            <if test="hiredDate != null">`HIRED_DATE`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="sysUserId != null">`SYS_USER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="userStatus != null">`USER_STATUS`,</if>
            <if test="lastLoginIp != null">`LAST_LOGIN_IP`,</if>
            <if test="accountStatus != null">`ACCOUNT_STATUS`,</if>
            <if test="lastLoginTime != null">`LAST_LOGIN_TIME`,</if>
            <if test="loginPassword != null">`LOGIN_PASSWORD`,</if>
            <if test="dingtalkUserId != null">`DINGTALK_USER_ID`,</if>
            <if test="lastLoginDevice != null">`LAST_LOGIN_DEVICE`,</if>
            <if test="isDefaultPwdModified != null">`IS_DEFAULT_PWD_MODIFIED`,</if>
            <if test="isFormalAccount != null">`IS_FORMAL_ACCOUNT`,</if>
            <if test="relevantTrialAccount != null">`RELEVANT_TRIAL_ACCOUNT`,</if>
            <if test="freezeTime != null">`FREEZE_TIME`,</if>
            <if test="isSetFreezeTime != null">`IS_SET_FREEZE_TIME`,</if>
            <if test="closeTime != null">`CLOSE_TIME`,</if>
            <if test="isSetCloseTime != null">`IS_SET_CLOSE_TIME`,</if>
            <if test="isFirstStart != null">`IS_FIRST_START`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="isDirectType != null">`IS_DIRECT_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="sex != null">#{sex,jdbcType=TINYINT},</if>
            <if test="salt != null">#{salt,jdbcType=CHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="avatar != null">#{avatar,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="addSource != null">#{addSource,jdbcType=TINYINT},</if>
            <if test="hiredDate != null">#{hiredDate,jdbcType=TIMESTAMP},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="sysUserId != null">#{sysUserId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="userStatus != null">#{userStatus,jdbcType=TINYINT},</if>
            <if test="lastLoginIp != null">#{lastLoginIp,jdbcType=VARCHAR},</if>
            <if test="accountStatus != null">#{accountStatus,jdbcType=TINYINT},</if>
            <if test="lastLoginTime != null">#{lastLoginTime,jdbcType=TIMESTAMP},</if>
            <if test="loginPassword != null">#{loginPassword,jdbcType=CHAR},</if>
            <if test="dingtalkUserId != null">#{dingtalkUserId,jdbcType=VARCHAR},</if>
            <if test="lastLoginDevice != null">#{lastLoginDevice,jdbcType=VARCHAR},</if>
            <if test="isDefaultPwdModified != null">#{isDefaultPwdModified,jdbcType=TINYINT},</if>
            <if test="isFormalAccount != null">#{isFormalAccount,jdbcType=TINYINT},</if>
            <if test="relevantTrialAccount != null">#{relevantTrialAccount,jdbcType=VARCHAR},</if>
            <if test="freezeTime != null">#{freezeTime,jdbcType=TIMESTAMP},</if>
            <if test="isSetFreezeTime != null">#{isSetFreezeTime,jdbcType=TINYINT},</if>
            <if test="closeTime != null">#{closeTime,jdbcType=TIMESTAMP},</if>
            <if test="isSetCloseTime != null">#{isSetCloseTime,jdbcType=TINYINT},</if>
            <if test="isFirstStart != null">#{isFirstStart,jdbcType=TINYINT},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="isDirectType != null">#{isDirectType,jdbcType=TINYINT},</if>
        </trim>
    </operation>


    <operation multiplicity="one" name="getByUserId" remark="根据业务id查询">
        SELECT * FROM lm_crm_user
        WHERE user_id = #{userId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
</table>