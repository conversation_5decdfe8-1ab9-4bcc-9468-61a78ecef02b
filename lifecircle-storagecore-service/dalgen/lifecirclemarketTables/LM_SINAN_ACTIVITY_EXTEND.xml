<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_SINAN_ACTIVITY_EXTEND" physicalName="LM_SINAN_ACTIVITY_EXTEND" remark="司南活动状态扩展表">
    <operation name="getManagerExtendByUserId">
        select * from lm_sinan_activity_extend where uid=#{userId,jdbcType=INTEGER}
    </operation>

    <operation name="insertSiNanActivityExtend" paramtype="object">
        insert into LM_SINAN_ACTIVITY_EXTEND
        <if test="uid !=null ">`uid`</if>
        <if test="industryActivityStatus !=null ">`industry_activity_status`</if>
        <if test="merchantActivityStatus !=null ">`merchant_activity_status`</if>
        <if test="cardActivityStatus !=null ">`card_activity_status`</if>
        <if test="userStatus !=null ">`user_status`</if>
        <if test="createTime !=null ">`create_time`</if>
        <if test="updateTime !=null ">`update_time`</if>
        <if test="createBy !=null ">`creqte_by`</if>
        values
        (#{uid,jdbcType=INTEGER},#{industryActivityStatus,jdbcType=INTEGER},
        #{merchantActivityStatus,jdbcType=INTEGER},
        #{cardActivityStatus,jdbcType=INTEGER},#{userStatus,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},#{updateTime,jdbcType=TIMESTAMP},#{createBy,jdbcType=INTEGER})
    </operation>

    <!--更新活动状态信息-->
    <operation name="updateActivityStatus" paramtype="object">
        update LM_SINAN_ACTIVITY_EXTEND
        <set>
            <if test="userStatus != null">USER_STATUS = #{userStatus,jdbcType=INTEGER},</if>
            <if test="createBy != null">create_by = #{createBy,jdbcType=INTEGER},</if>
            <if test="industryActivityStatus != null">
                industry_activity_status=#{industryActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="merchantActivityStatus != null">
                merchant_activity_status=#{merchantActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="cardActivityStatus != null">
                card_activity_status=#{cardActivityStatus,jdbcType=INTEGER}
            </if>
            <if test="updateTime ! = null">
                update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>

        </set>
        where user_id = #{userId,jdbcType=INTEGER}
    </operation>

</table>