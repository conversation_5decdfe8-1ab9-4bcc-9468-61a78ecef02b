<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_DOWNLOAD_CENTER" physicalName="LM_CRM_DOWNLOAD_CENTER"
       remark="LM_CRM_DOWNLOAD_CENTER">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_DOWNLOAD_CENTER">
        INSERT INTO LM_CRM_DOWNLOAD_CENTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="fileUrl != null">`FILE_URL`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="downloadId != null">`DOWNLOAD_ID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="fileUrl != null">#{fileUrl,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="downloadId != null">#{downloadId,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertObj" paramtype="object" remark="insert:LM_CRM_DOWNLOAD_CENTER">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_CRM_DOWNLOAD_CENTER(
        ID
        ,FILE_URL
        ,REMARKS
        ,CREATE_BY
        ,FILE_NAME
        ,UPDATE_BY
        ,DOWNLOAD_ID
        ,STATUS
        ,CREATE_TIME
        ,UPDATE_TIME
        )VALUES(
        #{id,jdbcType=INTEGER}
        , #{fileUrl,jdbcType=VARCHAR}
        , #{remarks,jdbcType=VARCHAR}
        , #{createBy,jdbcType=VARCHAR}
        , #{fileName,jdbcType=VARCHAR}
        , #{updateBy,jdbcType=VARCHAR}
        , #{downloadId,jdbcType=VARCHAR}
        , #{status,jdbcType=TINYINT}
        , #{createTime,jdbcType=TIMESTAMP}
        , #{updateTime,jdbcType=TIMESTAMP}
        )
    </operation>

    <operation name="update" paramtype="object" remark="update table:LM_CRM_DOWNLOAD_CENTER">
        UPDATE LM_CRM_DOWNLOAD_CENTER
        SET
        file_url = #{fileUrl,jdbcType=VARCHAR}
        ,file_name = #{fileName,jdbcType=VARCHAR}
        ,STATUS = #{status,jdbcType=TINYINT}
        ,UPDATE_BY = #{updateBy,jdbcType=VARCHAR}
        ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
        DOWNLOAD_ID = #{downloadId,jdbcType=INTEGER}
    </operation>

</table>
