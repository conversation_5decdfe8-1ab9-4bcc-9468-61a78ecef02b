<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_ACTIVITY_TEMPLATE_CONFIG" physicalName="LM_ACTIVITY_TEMPLATE_CONFIG"
       remark="活动基础模板配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_ACTIVITY_TEMPLATE_CONFIG">
        INSERT INTO LM_ACTIVITY_TEMPLATE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="configName != null">`CONFIG_NAME`,</if>
            <if test="configValue != null">`CONFIG_VALUE`,</if>
            <if test="templateCode != null">`TEMPLATE_CODE`,</if>
            <if test="templateName != null">`TEMPLATE_NAME`,</if>
            <if test="templateType != null">`TEMPLATE_TYPE`,</if>
            <if test="configAttribute != null">`CONFIG_ATTRIBUTE`,</if>
            <if test="configMappingFile != null">`CONFIG_MAPPING_FILE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="sortScore != null">`SORT_SCORE`,</if>
            <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="configName != null">#{configName,jdbcType=VARCHAR},</if>
            <if test="configValue != null">#{configValue,jdbcType=VARCHAR},</if>
            <if test="templateCode != null">#{templateCode,jdbcType=VARCHAR},</if>
            <if test="templateName != null">#{templateName,jdbcType=VARCHAR},</if>
            <if test="templateType != null">#{templateType,jdbcType=VARCHAR},</if>
            <if test="configAttribute != null">#{configAttribute,jdbcType=VARCHAR},</if>
            <if test="configMappingFile != null">#{configMappingFile,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="sortScore != null">#{sortScore,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
