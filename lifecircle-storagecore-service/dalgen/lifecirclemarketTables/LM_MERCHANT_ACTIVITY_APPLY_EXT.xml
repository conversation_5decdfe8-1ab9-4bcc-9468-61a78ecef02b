<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_MERCHANT_ACTIVITY_APPLY_EXT" physicalName="LM_MERCHANT_ACTIVITY_APPLY_EXT"
       remark="活动申请单资料表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_MERCHANT_ACTIVITY_APPLY_EXT">
        INSERT INTO LM_MERCHANT_ACTIVITY_APPLY_EXT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="applyNo != null">`APPLY_NO`,</if>
            <if test="applyPicAttribute != null">`APPLY_PIC_ATTRIBUTE`,</if>
            <if test="merchantAttribute != null">`MERCHANT_ATTRIBUTE`,</if>
            <if test="applyInfoAttribute != null">`APPLY_INFO_ATTRIBUTE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="applyNo != null">#{applyNo,jdbcType=VARCHAR},</if>
            <if test="applyPicAttribute != null">#{applyPicAttribute,jdbcType=LONGVARCHAR},</if>
            <if test="merchantAttribute != null">#{merchantAttribute,jdbcType=VARCHAR},</if>
            <if test="applyInfoAttribute != null">#{applyInfoAttribute,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getMerchantActivityApplyExtByApplyNo">
        SELECT *
        FROM lm_merchant_activity_apply_ext
        WHERE apply_no = #{applyNo,jdbcType=VARCHAR}
        LIMIT 1;
    </operation>
</table>
