<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_ACTIVITY_POLICY" physicalName="LM_CRM_ACTIVITY_POLICY"
       remark="活动政策表（面向线下登记活动）">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_ACTIVITY_POLICY">
        INSERT INTO LM_CRM_ACTIVITY_POLICY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="activityName != null">`ACTIVITY_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findListByActivityId" multiplicity="many">
        SELECT *
        FROM LM_CRM_ACTIVITY_POLICY
        WHERE is_del = 0
        <if test="list != null and list.size() &gt; 0">
            AND activity_id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </operation>
</table>
