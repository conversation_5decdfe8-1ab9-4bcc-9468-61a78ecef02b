<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_WECHAT_AUTHORIZE_APPLY" physicalName="LM_CRM_WECHAT_AUTHORIZE_APPLY"
       remark="微信认证申请信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_WECHAT_AUTHORIZE_APPLY">
        INSERT INTO LM_CRM_WECHAT_AUTHORIZE_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="certPic != null">`CERT_PIC`,</if>
            <if test="certType != null">`CERT_TYPE`,</if>
            <if test="subMchid != null">`SUB_MCHID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="certNumber != null">`CERT_NUMBER`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="qrcodeData != null">`QRCODE_DATA`,</if>
            <if test="applymentId != null">`APPLYMENT_ID`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="legalPerson != null">`LEGAL_PERSON`,</if>
            <if test="rejectParam != null">`REJECT_PARAM`,</if>
            <if test="subjectType != null">`SUBJECT_TYPE`,</if>
            <if test="businessCode != null">`BUSINESS_CODE`,</if>
            <if test="certTypeName != null">`CERT_TYPE_NAME`,</if>
            <if test="idCardNumber != null">`ID_CARD_NUMBER`,</if>
            <if test="microBizType != null">`MICRO_BIZ_TYPE`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="storeAddress != null">`STORE_ADDRESS`,</if>
            <if test="licenseNumber != null">`LICENSE_NUMBER`,</if>
            <if test="storeAreaCode != null">`STORE_AREA_CODE`,</if>
            <if test="storeAreaName != null">`STORE_AREA_NAME`,</if>
            <if test="storeCityCode != null">`STORE_CITY_CODE`,</if>
            <if test="storeCityName != null">`STORE_CITY_NAME`,</if>
            <if test="applymentState != null">`APPLYMENT_STATE`,</if>
            <if test="authorizeState != null">`AUTHORIZE_STATE`,</if>
            <if test="companyAddress != null">`COMPANY_ADDRESS`,</if>
            <if test="licenseEndDate != null">`LICENSE_END_DATE`,</if>
            <if test="storeHeaderPic != null">`STORE_HEADER_PIC`,</if>
            <if test="storeIndoorPic != null">`STORE_INDOOR_PIC`,</if>
            <if test="companyProvePic != null">`COMPANY_PROVE_PIC`,</if>
            <if test="subjectTypeName != null">`SUBJECT_TYPE_NAME`,</if>
            <if test="confirmMchidList != null">`CONFIRM_MCHID_LIST`,</if>
            <if test="licenseBeginDate != null">`LICENSE_BEGIN_DATE`,</if>
            <if test="microBizTypeName != null">`MICRO_BIZ_TYPE_NAME`,</if>
            <if test="storeAddressCode != null">`STORE_ADDRESS_CODE`,</if>
            <if test="applymentStateMsg != null">`APPLYMENT_STATE_MSG`,</if>
            <if test="storeProvinceCode != null">`STORE_PROVINCE_CODE`,</if>
            <if test="storeProvinceName != null">`STORE_PROVINCE_NAME`,</if>
            <if test="identificationName != null">`IDENTIFICATION_NAME`,</if>
            <if test="identificationType != null">`IDENTIFICATION_TYPE`,</if>
            <if test="identificationNumber != null">`IDENTIFICATION_NUMBER`,</if>
            <if test="identificationBackPic != null">`IDENTIFICATION_BACK_PIC`,</if>
            <if test="identificationEndDate != null">`IDENTIFICATION_END_DATE`,</if>
            <if test="identificationFrontPic != null">`IDENTIFICATION_FRONT_PIC`,</if>
            <if test="identificationTypeName != null">`IDENTIFICATION_TYPE_NAME`,</if>
            <if test="identificationBeginDate != null">`IDENTIFICATION_BEGIN_DATE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="certTypeValue != null">`CERT_TYPE_VALUE`,</if>
            <if test="licenseIsLong != null">`LICENSE_IS_LONG`,</if>
            <if test="applymentMethod != null">`APPLYMENT_METHOD`,</if>
            <if test="applymentStatus != null">`APPLYMENT_STATUS`,</if>
            <if test="authorizeStatus != null">`AUTHORIZE_STATUS`,</if>
            <if test="subjectTypeValue != null">`SUBJECT_TYPE_VALUE`,</if>
            <if test="microBizTypeValue != null">`MICRO_BIZ_TYPE_VALUE`,</if>
            <if test="identificationIsLong != null">`IDENTIFICATION_IS_LONG`,</if>
            <if test="identificationTypeValue != null">`IDENTIFICATION_TYPE_VALUE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="certPic != null">#{certPic,jdbcType=VARCHAR},</if>
            <if test="certType != null">#{certType,jdbcType=VARCHAR},</if>
            <if test="subMchid != null">#{subMchid,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="certNumber != null">#{certNumber,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="qrcodeData != null">#{qrcodeData,jdbcType=VARCHAR},</if>
            <if test="applymentId != null">#{applymentId,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="legalPerson != null">#{legalPerson,jdbcType=VARCHAR},</if>
            <if test="rejectParam != null">#{rejectParam,jdbcType=VARCHAR},</if>
            <if test="subjectType != null">#{subjectType,jdbcType=VARCHAR},</if>
            <if test="businessCode != null">#{businessCode,jdbcType=VARCHAR},</if>
            <if test="certTypeName != null">#{certTypeName,jdbcType=VARCHAR},</if>
            <if test="idCardNumber != null">#{idCardNumber,jdbcType=VARCHAR},</if>
            <if test="microBizType != null">#{microBizType,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="storeAddress != null">#{storeAddress,jdbcType=VARCHAR},</if>
            <if test="licenseNumber != null">#{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="storeAreaCode != null">#{storeAreaCode,jdbcType=VARCHAR},</if>
            <if test="storeAreaName != null">#{storeAreaName,jdbcType=VARCHAR},</if>
            <if test="storeCityCode != null">#{storeCityCode,jdbcType=VARCHAR},</if>
            <if test="storeCityName != null">#{storeCityName,jdbcType=VARCHAR},</if>
            <if test="applymentState != null">#{applymentState,jdbcType=VARCHAR},</if>
            <if test="authorizeState != null">#{authorizeState,jdbcType=VARCHAR},</if>
            <if test="companyAddress != null">#{companyAddress,jdbcType=VARCHAR},</if>
            <if test="licenseEndDate != null">#{licenseEndDate,jdbcType=VARCHAR},</if>
            <if test="storeHeaderPic != null">#{storeHeaderPic,jdbcType=VARCHAR},</if>
            <if test="storeIndoorPic != null">#{storeIndoorPic,jdbcType=VARCHAR},</if>
            <if test="companyProvePic != null">#{companyProvePic,jdbcType=VARCHAR},</if>
            <if test="subjectTypeName != null">#{subjectTypeName,jdbcType=VARCHAR},</if>
            <if test="confirmMchidList != null">#{confirmMchidList,jdbcType=VARCHAR},</if>
            <if test="licenseBeginDate != null">#{licenseBeginDate,jdbcType=VARCHAR},</if>
            <if test="microBizTypeName != null">#{microBizTypeName,jdbcType=VARCHAR},</if>
            <if test="storeAddressCode != null">#{storeAddressCode,jdbcType=VARCHAR},</if>
            <if test="applymentStateMsg != null">#{applymentStateMsg,jdbcType=VARCHAR},</if>
            <if test="storeProvinceCode != null">#{storeProvinceCode,jdbcType=VARCHAR},</if>
            <if test="storeProvinceName != null">#{storeProvinceName,jdbcType=VARCHAR},</if>
            <if test="identificationName != null">#{identificationName,jdbcType=VARCHAR},</if>
            <if test="identificationType != null">#{identificationType,jdbcType=VARCHAR},</if>
            <if test="identificationNumber != null">#{identificationNumber,jdbcType=VARCHAR},</if>
            <if test="identificationBackPic != null">#{identificationBackPic,jdbcType=VARCHAR},</if>
            <if test="identificationEndDate != null">#{identificationEndDate,jdbcType=VARCHAR},</if>
            <if test="identificationFrontPic != null">#{identificationFrontPic,jdbcType=VARCHAR},</if>
            <if test="identificationTypeName != null">#{identificationTypeName,jdbcType=VARCHAR},</if>
            <if test="identificationBeginDate != null">#{identificationBeginDate,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="certTypeValue != null">#{certTypeValue,jdbcType=TINYINT},</if>
            <if test="licenseIsLong != null">#{licenseIsLong,jdbcType=TINYINT},</if>
            <if test="applymentMethod != null">#{applymentMethod,jdbcType=TINYINT},</if>
            <if test="applymentStatus != null">#{applymentStatus,jdbcType=TINYINT},</if>
            <if test="authorizeStatus != null">#{authorizeStatus,jdbcType=TINYINT},</if>
            <if test="subjectTypeValue != null">#{subjectTypeValue,jdbcType=TINYINT},</if>
            <if test="microBizTypeValue != null">#{microBizTypeValue,jdbcType=TINYINT},</if>
            <if test="identificationIsLong != null">#{identificationIsLong,jdbcType=TINYINT},</if>
            <if test="identificationTypeValue != null">#{identificationTypeValue,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findCrmWechatAuthMerchantList"
               multiplicity="paging"
               paging="FindCrmWechatAuthMerchantList"
               paramtype="primitive"
               remark="crm后台，查询微信认证商家列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.CrmWechatAuthMerchantSimpleInfoDTO">
        SELECT
        apply.applyment_method as applymentMethod,
        apply.create_time as createTime,
        apply.uid,
        users.username,
        users.company,
        apply.sub_mchid as subMchid,
        u.username as belongName,
        apply.audit_status as auditStatus,
        apply.reject_reason as rejectReason

        FROM
        lm_crm_wechat_authorize_apply apply
        LEFT JOIN
        tp_users users
        ON
        apply.uid = users.id
        LEFT JOIN
        tp_user u
        ON
        users.belong = u.id
        WHERE
        apply.is_del = 0
        AND
        users.parent_id = 0
        <if test="startDate != null and endDate != null and startDate != '' and endDate != ''">
            AND apply.create_time BETWEEN #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="subMchid != null and subMchid != ''">
            AND apply.sub_mchid = #{subMchid,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and company != ''">
            AND users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName != null and belongName != '' ">
            AND u.username LIKE CONCAT (#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="auditStatus != null and auditStatus != -1  and auditStatus != 0">
            AND apply.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
        ORDER BY apply.create_time DESC
    </operation>

    <operation name="findCrmWechatAuthMerchantListTotal"
               resulttype="int"
               multiplicity="one"
               paramtype="primitive"
               remark="crm后台，查询导出微信认证商家列表条数">
        SELECT
        COUNT(*) AS total
        FROM
        lm_crm_wechat_authorize_apply apply
        LEFT JOIN
        tp_users users
        ON
        apply.uid = users.id
        LEFT JOIN
        tp_user u
        ON
        users.belong = u.id
        WHERE
        apply.is_del = 0
        AND
        users.parent_id = 0
        <if test="startDate != null and endDate != null and startDate != '' and endDate != ''">
            AND apply.create_time BETWEEN #{startDate,jdbcType=VARCHAR} and #{endDate,jdbcType=VARCHAR}
        </if>
        <if test="uid != null">
            AND apply.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="subMchid != null and subMchid != ''">
            AND apply.sub_mchid = #{subMchid,jdbcType=VARCHAR}
        </if>
        <if test="username != null and username != ''">
            AND users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and company != ''">
            AND users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="belongName != null and belongName != '' ">
            AND u.username LIKE CONCAT (#{belongName,jdbcType=VARCHAR},'%')
        </if>
        <if test="auditStatus != null and auditStatus != -1  and auditStatus != 0">
            AND apply.audit_status = #{auditStatus,jdbcType=INTEGER}
        </if>
    </operation>

</table>
