<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_SINAN_ACTIVITY_REGISTRATION" physicalName="LM_SINAN_ACTIVITY_REGISTRATION" remark="付呗司南活动报名">

    <operation name="getManagerByUserId">
        select * from lm_sinan_activity_registration where uid=#{userId,jdbcType=INTEGER}
    </operation>

    <resultmap name="siNanActivityMap" type="SiNanActivityListDO">
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="商户id"></column>
        <column name="username" javatype="String" jdbctype="VARCHAR" remark="商户名称"></column>
        <column name="company" javatype="String" jdbctype="VARCHAR" remark="公司名称"></column>
        <column name="activity_status" javatype="Integer" jdbctype="INTEGER" remark="活动状态"></column>
    </resultmap>

    <operation name="getSinanActivityCount" resulttype="Integer">
        SELECT COUNT(*) FROM
        (
        SELECT
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>

            <if test="activityRegistrationType != null">
                AND activityRegistrationType = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
    </operation>


    <operation name="getSinanSuperActivityCount" resulttype="Integer">
        SELECT COUNT(*) FROM
        (
        SELECT
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND users.createtime > 1617206400
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>

            <if test="activityRegistrationType != null">
                AND activityRegistrationType = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
    </operation>


    <operation name="getSinanSuperActivityList" resultmap="siNanActivityMap" multiplicity="many">
        SELECT * FROM
        (
        SELECT
        users.id uid,
        users.username,
        IFNULL(apply.company, users.company) company,
        IFNULL(apply.activity_status, 1) activity_status,
        apply.store_id,
        CASE
        WHEN apply.activity_number != '' THEN UNIX_TIMESTAMP(apply.update_time)
        ELSE users.createtime
        END AS orderby_time
        FROM tp_users users
        LEFT JOIN lm_sinan_activity_registration apply
        ON users.id = apply.uid
        WHERE users.id IN
        <foreach item="item" index="index" open="(" separator="," close=")" collection="uidList">
            #{item,jdbcType=INTEGER}
        </foreach>
        AND users.createtime > 1617206400
        ) temp_table
        <where>
            <if test="activityStatus != null">
                AND activity_status = #{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="activityRegistrationType != null">
                AND activity_registration_type = #{activityRegistrationType,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY create_time DESC
        LIMIT #{startRow,jdbcType=INTEGER},#{offset,jdbcType=INTEGER}

    </operation>

    <operation name="insertCardInfo" paramtype="object">
        insert into lm_sinan_activity_registration
        (uid,username,company,industry,policy_type,is_card_open,merchant_address,detailed_address,card_phone,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},#{policyType,jdbcType=VARCHAR},
        #{isCardOpen,jdbcType=INTEGER},#{merchantAddress,jdbcType=VARCHAR},
        #{detailedAddress,jdbcType=VARCHAR},#{cardPhone,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER}
        #{activityNumber,jdbcType=VARCHAR},#{activityRegistrationType,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP},
        #{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
    </operation>

    <operation name="insertSuperStoreInfo" paramtype="object">
        insert into lm_sinan_activity_registration
        (uid,username,company,industry,store_ids,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},#{storeIds,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER},#{activityNumber,jdbcType=VARCHAR},#{activityRegistrationType,jdbcType=INTEGER},
        #{createTime,jdbcType=TIMESTAMP},#{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
    </operation>

    <operation name="insertIndustryInfo" paramtype="object">
        insert into lm_sinan_activity_registration
        (uid,username,company,industry,activity_type_industry_policy,everyday_trade,store_head_img,store_inside_img,store_money_img
        ,activity_status
        ,activity_number,activity_registration_type,create_time,merchant_no,belong)
        values
        (#{uid,jdbcType=INTEGER},#{username,jdbcType=VARCHAR},#{company,jdbcType=VARCHAR},#{industry,jdbcType=VARCHAR},
        #{activityTypeIndustryPolicy,jdbcType=VARCHAR},#{everydayTrade,jdbcType=DECIMAL},#{storeHeadImg,jdbcType=VARCHAR},
        #{storeInsideImg,jdbcType=VARCHAR},#{storeMoneyImg,jdbcType=VARCHAR},
        #{activityStatus,jdbcType=INTEGER},#{activityNumber,jdbcType=VARCHAR}
        ,#{activityRegistrationType,jdbcType=INTEGER},#{createTime,jdbcType=TIMESTAMP}
        ,#{merchantNo,jdbcType=VARCHAR},#{belong,jdbcType=INTEGER})
    </operation>

    <operation name="getCardActivityDetail" multiplicity="one">
        select * from lm_sinan_activity_registration where uid=#{uid,jdbcType=INTEGER}
    </operation>

    <operation name="updateSinanActivityInfo" paramtype="object">
        update lm_sinan_activity_registration
        <set>
            <if test="policyType!=null">
                policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="isCardOpen!=null">
                is_card_open=#{isCardOpen,jdbcType=INTEGER}
            </if>
            <if test="merchantAddress!=null">
                merchant_address=#{merchantAddress,jdbcType=VARCHAR}
            </if>
            <if test="detailedAddress!=null">
                detailed_address=#{detailedAddress,jdbcType=VARCHAR}
            </if>
            <if test="cardPhone!=null">
                card_phone=#{cardPhone,jdbcType=INTEGER}
            </if>
            <if test="activityTypeIndustryPolicy!=null">
                activity_type_industry_policy=#{activityTypeIndustryPolicy,jdbcType=VARCHAR}
            </if>
            <if test="activityTypeIndustryPolicy!=null">
                activity_type_industry_policy=#{activityTypeIndustryPolicy,jdbcType=VARCHAR}
            </if>
            <if test="everydayTrade!=null">
                everyday_trade=#{everydayTrade,jdbcType=DECIMAL}
            </if>
            <if test="storeHeadImg!=null">
                store_head_img=#{storeHeadImg,jdbcType=VARCHAR}
            </if>
            <if test="storeInsideImg!=null">
                store_inside_img=#{storeInsideImg,jdbcType=VARCHAR}
            </if>
            <if test="storeMoneyImg!=null">
                store_money_img=#{storeMoneyImg,jdbcType=VARCHAR}
            </if>
            <if test="storeIds!=null">
                store_ids=#{storeIds,jdbcType=VARCHAR}
            </if>
            <if test="updateTime!=null">
                update_time=#{updateTime,jdbcType=TIMESTAMP}
            </if>
            where activity_number=#{activityNumber,jdbcType=VARCHAR}
        </set>
    </operation>


    <operation name="getSinanJoinActivityInfoByActivityNumber">
        select * from lm_sinan_activity_registration where activity_number=#{activityNumber,jdbcType=VARCHAR}
    </operation>


    <operation name="getSinanCrmActivityInfoCountByUsername" resulttype="Integer">
        select count(*) from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and username=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
    </operation>

    <operation name="getSinanCrmActivityInfoCountByuid" resulttype="Integer">
        select count(*) from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
    </operation>

    <operation name="getSinanCrmActivityInfoCountByMerchantNo" resulttype="Integer">
        select count(*) from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
    </operation>

    <operation name="getSinanCrmActivityInfoCountBybelong" resulttype="Integer">
        select count(*) from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="activityRegistrationType !=null and activityRegistrationType!='' ">
                and activity_registration_type=#{activityRegistrationType,jdbcType=INTEGER}
            </if>
            <if test="policyType !=null and policyType!='' ">
                and policy_type=#{policyType,jdbcType=VARCHAR}
            </if>
            <if test="activityStatus !=null and activityStatus != '' ">
                and activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime !=null and searchActivityCreateTime!='' ">
                AND create_time <![CDATA[>=]]> #{searchActivityCreateTime,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityEndTime !=null and searchActivityEndTime!='' ">
                AND create_time <![CDATA[<=]]> #{searchActivityEndTime,jdbcType=VARCHAR}
            </if>
        </where>
    </operation>

    <operation name="updateActivityStatusByActivityNumber">
        update lm_sinan_activity_registration set activity_status=#{activityStatus,jdbcType=INTEGER},
        reject_info=#{rejectInfo,jdbcType=VARCHAR},
        update_time=#{updateTime,jdbcType=TIMESTAMP}
        where activity_number=#{activityNumber,jdbcType=VARCHAR}
    </operation>

    <operation name="getActivityStatusByActivityNumber">
        select *from lm_sinan_activity_registration where activity_number=#{activityNumber,jdbcType=VARCHAR}
    </operation>

    <operation name="getSinanCrmActivityInfoByUsername" multiplicity="paging" paging="SianaCrmActivByUserDetail">
        select * from lm_sinan_activity_registration
        <where>
            <if test="searchValue!=null and searchValue!='' ">
                and username LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>


    <operation name="getSinanCrmActivityInfoByMerchantNo" multiplicity="paging"
               paging="SianaCrmActivByMerchantDetail">
        select * from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and merchant_no=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>

    <operation name="getSinanCrmActivityInfoByBelong" multiplicity="paging" paging="SianaCrmActivByBelongDetail">
        select * from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and belong LIKE CONCAT (#{searchValue,jdbcType=VARCHAR},'%')
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>


    <operation name="getSinanCrmActivityInfoByUid" multiplicity="paging" paging="SianaCrmActivByUidDetail">
        select * from lm_sinan_activity_registration
        <where>
            <if test="searchValue !=null and searchValue!='' ">
                and uid=#{searchValue,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityName!=null">
                and activity_registration_type=#{searchActivityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                and policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                and activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>

    <operation name="findActivityRegistrationPage" multiplicity="paging" paging="findSianaCrmActivity">
        SELECT * FROM
        lm_sinan_activity_registration
        <where>
            <if test="username !=null and username!='' ">
                AND username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uid !=null and uid!=0 ">
                AND uid=#{uid,jdbcType=INTEGER}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="(" separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="activityName!=null and activityName!=-1 ">
                AND activity_registration_type=#{activityName,jdbcType=INTEGER}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="searchActivityStatusType!=null">
                AND activity_status=#{searchActivityStatusType,jdbcType=INTEGER}
            </if>
            <if test="isCutChannel!=null and isCutChannel!=0 ">
                AND is_cut_channel = #{isCutChannel,jdbcType=INTEGER}
            </if>
            <if test="isSharePay!=null and isSharePay!=0 ">
                AND is_share_pay = #{isSharePay,jdbcType=INTEGER}
            </if>
            <if test="searchActivityCreateTime!=null">
                AND create_time <![CDATA[ >= ]]> #{searchActivityCreateTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null and liquidationType!=0 ">
                AND liquidation_type = #{liquidationType,jdbcType=INTEGER}
            </if>
            <if test="signProvince !=null and signProvince!='' ">
                AND SIGN_PROVINCE LIKE CONCAT(#{signProvince,jdbcType=VARCHAR},'%')
            </if>
            <if test="signRegion !=null and signRegion!='' ">
                AND SIGN_REGION LIKE CONCAT(#{signRegion,jdbcType=VARCHAR},'%')
            </if>
            <if test="bankApplyStatus !=null ">
                AND BANK_APPLY_STATUS = #{bankApplyStatus,jdbcType=INTEGER}
            </if>
            <if test="isCardOpen !=null ">
                AND is_card_open = #{isCardOpen,jdbcType=INTEGER}
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>

    <operation name="findRegistrationPageGoldDashboard" multiplicity="paging"
               paging="findRegistrationPageGoldDashboard">
        <extraparams>
            <param name="isV2Agent" javatype="java.lang.Boolean"></param>
        </extraparams>
        SELECT lsar.* FROM
        lm_sinan_activity_registration lsar
        <if test="isV2Agent != null and isV2Agent == true">
            LEFT JOIN tp_users us on us.id = lsar.uid
            LEFT JOIN tp_grant_right_control salesmanTgrc on salesmanTgrc.key_id = us.salesman
            LEFT JOIN tp_grant_right_control marketTgrc on marketTgrc.key_id = us.market_id
        </if>
        <where>
            lsar.is_del = 0
            AND lsar.`activity_registration_type` = 1
            AND lsar.belong = #{belong,jdbcType=INTEGER}
            <if test="isV2Agent != null and isV2Agent == true">
                AND (salesmanTgrc.individual_settle = 0 OR salesmanTgrc.individual_settle is null)
                AND (marketTgrc.individual_settle = 0 OR marketTgrc.individual_settle is null)
            </if>
            <if test="username !=null and username!='' ">
                AND lsar.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="uidList != null and uidList.size &gt;0">
                AND lsar.uid IN
                <foreach close=")" collection="uidList" index="index" item="uid" open="(" separator=",">
                    #{uid,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="company!=null and company!=''">
                AND lsar.company=#{company,jdbcType=VARCHAR}
            </if>
            <if test="merchantNo!=null and merchantNo!=''">
                AND lsar.merchant_no=#{merchantNo,jdbcType=VARCHAR}
            </if>
            <if test="searchPolicyType!=null and searchPolicyType!='' ">
                AND lsar.policy_type=#{searchPolicyType,jdbcType=VARCHAR}
            </if>
            <if test="cardSettleType!=null">
                AND lsar.card_settle_type=#{cardSettleType,jdbcType=INTEGER}
            </if>
            <if test="isChangeBankNo!=null and isChangeBankNo!=0">
                AND lsar.is_change_bank_no=#{isChangeBankNo,jdbcType=INTEGER}
            </if>
            <if test="activityStatus!=null and activityStatus!=0">
                AND lsar.activity_status=#{activityStatus,jdbcType=INTEGER}
            </if>
            <if test="searchActivityStartTime!=null">
                AND lsar.create_time <![CDATA[ >= ]]> #{searchActivityStartTime,jdbcType=TIMESTAMP}
            </if>
            <if test="searchActivityEndTime !=null">
                AND lsar.create_time <![CDATA[ < ]]> #{searchActivityEndTime,jdbcType=TIMESTAMP}
            </if>
            <if test="liquidationType !=null">
                AND lsar.liquidation_type = #{liquidationType,jdbcType=TINYINT}
            </if>
        </where>
        ORDER BY lsar.create_time DESC
    </operation>


    <operation name="getByLiquidationTypeGoldCardRecently" multiplicity="many"
               remark="查询出来所有的审核中的金卡活动根据对应得通道类型">
        SELECT * FROM lm_sinan_activity_registration
        WHERE liquidation_type = #{liquidationType,jdbcType=TINYINT}
        AND activity_registration_type = 1
        AND activity_status = 2
        AND is_del = 0
        <if test="list != null and list.size &gt; 0">
            AND uid IN
            <foreach close=")" collection="list" index="index" item="uid" open="(" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        ORDER BY create_time DESC
    </operation>


    <operation name="updateStatusById" remark="根据ID进行设置审核状态">
        UPDATE lm_sinan_activity_registration
        SET activity_status = #{activityStatus,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=INTEGER}
    </operation>
</table>
