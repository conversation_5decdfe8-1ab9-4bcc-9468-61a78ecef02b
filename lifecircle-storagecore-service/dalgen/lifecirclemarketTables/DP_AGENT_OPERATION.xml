<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="DP_AGENT_OPERATION" physicalName="DP_AGENT_OPERATION"
    remark="代理商与运营拓展表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:DP_AGENT_OPERATION">
INSERT INTO DP_AGENT_OPERATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="unionId != null">`UNION_ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="realName != null">`REAL_NAME`,</if>
        <if test="jobNumber != null">`JOB_NUMBER`,</if>
        <if test="operatorNumber != null">`OPERATOR_NUMBER`,</if>
        <if test="valid != null">`VALID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="operationType != null">`OPERATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
        <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
        <if test="operatorNumber != null">#{operatorNumber,jdbcType=VARCHAR},</if>
        <if test="valid != null">#{valid,jdbcType=TINYINT},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="operationType != null">#{operationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>


    <operation name="listByAgentIdsAndTypes" multiplicity="many" remark="根据类型与代理商ID查询">
        SELECT
        a.agent_id,
        a.operation_type,
        a.union_id,
        a.real_name,
        a.job_number,
        a.operator,
        a.operator_number
        FROM
        dp_agent_operation a
        WHERE
        a.valid = 1
        AND a.operation_type IN
        <foreach item="type" index="index" collection="operationTypeList" open="(" separator="," close=")">
            #{type,jdbcType=INTEGER}
        </foreach>
        AND a.agent_id IN
        <foreach item="agentId" index="index" collection="agentIdList" open="(" separator="," close=")">
            #{agentId,jdbcType=INTEGER}
        </foreach>
    </operation>

</table>
