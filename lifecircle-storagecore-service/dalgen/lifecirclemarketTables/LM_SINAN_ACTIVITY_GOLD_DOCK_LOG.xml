<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_SINAN_ACTIVITY_GOLD_DOCK_LOG" physicalName="LM_SINAN_ACTIVITY_GOLD_DOCK_LOG"
       remark="金卡行动对接第三方记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_SINAN_ACTIVITY_GOLD_DOCK_LOG">
        INSERT INTO LM_SINAN_ACTIVITY_GOLD_DOCK_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="url != null">`URL`,</if>
            <if test="otherMsg != null">`OTHER_MSG`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="dealDate != null">`DEAL_DATE`,</if>
            <if test="reportType != null">`REPORT_TYPE`,</if>
            <if test="targetType != null">`TARGET_TYPE`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="url != null">#{url,jdbcType=VARCHAR},</if>
            <if test="otherMsg != null">#{otherMsg,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="dealDate != null">#{dealDate,jdbcType=INTEGER},</if>
            <if test="reportType != null">#{reportType,jdbcType=TINYINT},</if>
            <if test="targetType != null">#{targetType,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
