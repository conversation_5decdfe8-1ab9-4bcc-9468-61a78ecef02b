<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP" physicalName="LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP"
       remark="渠道政策结算组表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP">
        INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList">
        INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP(GROUP_ID,POLICY_ID) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.policyId,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="getByPolicyIdAndUserId" multiplicity="one" remark="根据政策ID和用户ID查询结算组信息">
        select t1.group_id
        from lm_crm_channel_policy_settlement_group t1
        left join lm_crm_channel_policy_settlement_group_member t2
        on t1.group_id = t2.group_id
        where t1.policy_id = #{policyId,jdbcType=VARCHAR}
        and t2.user_id = #{userId,jdbcType=INTEGER}
        and t1.is_del = 0
        and t2.is_del = 0
        limit 1
    </operation>
</table>
