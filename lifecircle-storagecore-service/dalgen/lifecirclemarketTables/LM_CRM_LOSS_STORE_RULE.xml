<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_LOSS_STORE_RULE" physicalName="LM_CRM_LOSS_STORE_RULE" remark="LM_CRM_LOSS_STORE_RULE">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_GROUP">
        INSERT INTO LM_CRM_LOSS_STORE_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="lastWeekAverageDaily != null">`LAST_WEEK_AVERAGE_DAILY`,</if>
            <if test="offPercent != null">`OFF_PERCENT`,</if>
            <if test="pushStatus != null">`PUSH_STATUS`,</if>
            <if test="pushFrequency != null">`PUSH_FREQUENCY`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="userId != null">#{userId,jdbcType=BIGINT},</if>
            <if test="lastWeekAverageDaily != null">#{lastWeekAverageDaily,jdbcType=INTEGER},</if>
            <if test="offPercent != null">#{offPercent,jdbcType=INTEGER},</if>
            <if test="pushStatus != null">#{pushStatus,jdbcType=INTEGER},</if>
            <if test="pushFrequency != null">#{pushFrequency,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <operation name="update" paramtype="object" multiplicity="one" remark="update:LM_CRM_LOSS_STORE_RULE">
        update LM_CRM_LOSS_STORE_RULE
        set update_time = now()
        <if test="userId != null">
            ,USER_ID = #{userId,jdbcType=INTEGER}
        </if>
        <if test="lastWeekAverageDaily != null">
            ,LAST_WEEK_AVERAGE_DAILY = #{lastWeekAverageDaily,jdbcType=INTEGER}
        </if>
        <if test="offPercent != null">
            ,OFF_PERCENT = #{offPercent,jdbcType=INTEGER}
        </if>
        <if test="pushStatus != null">
            ,PUSH_STATUS = #{pushStatus,jdbcType=INTEGER}
        </if>
        <if test="pushFrequency != null">
            ,PUSH_FREQUENCY = #{pushFrequency,jdbcType=INTEGER}
        </if>
        <if test="isDel != null">
            ,IS_DEL = #{isDel,jdbcType=INTEGER}
        </if>
        <if test="createTime != null">
            ,CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}
        </if>
        <if test="updateTime != null">
            ,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}
        </if>
        where
        id = #{id,jdbcType=BIGINT}
    </operation>


    <operation name="getOneByUserId" paramtype="primitive" multiplicity="one" remark="根据userId获取出来规则">
        SELECT * FROM LM_CRM_LOSS_STORE_RULE WHERE USER_ID = #{userId,jdbcType=INTEGER} and IS_DEL = 0 ORDER BY ID DESC
        LIMIT 1
    </operation>


</table>
