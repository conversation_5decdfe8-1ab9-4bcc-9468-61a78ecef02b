<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_MARKET_ACTIVITY_OPERATE_LOG" physicalName="LM_MARKET_ACTIVITY_OPERATE_LOG"
       remark="市场活动操作日志">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_MARKET_ACTIVITY_OPERATE_LOG">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_MARKET_ACTIVITY_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="operateDesc != null">`OPERATE_DESC`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="operateAttribute != null">`OPERATE_ATTRIBUTE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="activityCode != null">`ACTIVITY_CODE`,</if>
            <if test="activityName != null">`ACTIVITY_NAME`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="applyNo != null">`APPLY_NO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="operateDesc != null">#{operateDesc,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="operateAttribute != null">#{operateAttribute,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="activityCode != null">#{activityCode,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=TINYINT},</if>
            <if test="applyNo != null">#{applyNo,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation name="updateAttributeById" paramtype="object" remark="根据ID更新属性">
        UPDATE lm_market_activity_operate_log
        SET
        operate_desc = #{operateDesc,jdbcType=VARCHAR}
        ,operate_attribute = #{operateAttribute,jdbcType=VARCHAR}
        WHERE id = #{id,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="getByApplyNo" multiplicity="many" remark="获取报名记录">
        select
        *
        from
        lm_market_activity_operate_log
        where
        is_del = 0
        and apply_no = #{applyNo,jdbcType=VARCHAR}
        order by id desc
    </operation>

    <operation name="deleteByApplyNoAndStatus" paramtype="primitive" remark="删除相关日志">
        delete from lm_market_activity_operate_log
        where
        apply_no = #{applyNo,jdbcType=VARCHAR}
        and apply_status = #{applyStatus,jdbcType=TINYINT}
    </operation>
</table>
