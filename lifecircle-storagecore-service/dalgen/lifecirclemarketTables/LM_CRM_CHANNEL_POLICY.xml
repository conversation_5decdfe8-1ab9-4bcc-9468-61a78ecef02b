<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_CHANNEL_POLICY" physicalName="LM_CRM_CHANNEL_POLICY"
       remark="渠道政策">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_CHANNEL_POLICY">
        INSERT INTO LM_CRM_CHANNEL_POLICY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="agentTag != null">`AGENT_TAG`,</if>
            <if test="policyId != null">`POLICY_ID`,</if>
            <if test="policyName != null">`POLICY_NAME`,</if>
            <if test="paymentDesc != null">`PAYMENT_DESC`,</if>
            <if test="agentSailTag != null">`AGENT_SAIL_TAG`,</if>
            <if test="importListUrl != null">`IMPORT_LIST_URL`,</if>
            <if test="policyFileUrl != null">`POLICY_FILE_URL`,</if>
            <if test="agentWhiteList != null">`AGENT_WHITE_LIST`,</if>
            <if test="paymentProofUrl != null">`PAYMENT_PROOF_URL`,</if>
            <if test="policyDescription != null">`POLICY_DESCRIPTION`,</if>
            <if test="excludePolicyIdList != null">`EXCLUDE_POLICY_ID_LIST`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="grantCategory != null">`GRANT_CATEGORY`,</if>
            <if test="userDimension != null">`USER_DIMENSION`,</if>
            <if test="marketCategory != null">`MARKET_CATEGORY`,</if>
            <if test="hasPaymentProof != null">`HAS_PAYMENT_PROOF`,</if>
            <if test="endDate != null">`END_DATE`,</if>
            <if test="startDate != null">`START_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="agentTag != null">#{agentTag,jdbcType=VARCHAR},</if>
            <if test="policyId != null">#{policyId,jdbcType=VARCHAR},</if>
            <if test="policyName != null">#{policyName,jdbcType=VARCHAR},</if>
            <if test="paymentDesc != null">#{paymentDesc,jdbcType=VARCHAR},</if>
            <if test="agentSailTag != null">#{agentSailTag,jdbcType=VARCHAR},</if>
            <if test="importListUrl != null">#{importListUrl,jdbcType=VARCHAR},</if>
            <if test="policyFileUrl != null">#{policyFileUrl,jdbcType=VARCHAR},</if>
            <if test="agentWhiteList != null">#{agentWhiteList,jdbcType=VARCHAR},</if>
            <if test="paymentProofUrl != null">#{paymentProofUrl,jdbcType=VARCHAR},</if>
            <if test="policyDescription != null">#{policyDescription,jdbcType=VARCHAR},</if>
            <if test="excludePolicyIdList != null">#{excludePolicyIdList,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="grantCategory != null">#{grantCategory,jdbcType=TINYINT},</if>
            <if test="userDimension != null">#{userDimension,jdbcType=TINYINT},</if>
            <if test="marketCategory != null">#{marketCategory,jdbcType=TINYINT},</if>
            <if test="hasPaymentProof != null">#{hasPaymentProof,jdbcType=TINYINT},</if>
            <if test="endDate != null">#{endDate,jdbcType=TIMESTAMP},</if>
            <if test="startDate != null">#{startDate,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByPolicyNameList" multiplicity="many" remark="根据政策名称查询">
        SELECT
        *
        FROM LM_CRM_CHANNEL_POLICY
        WHERE is_del = 0
        AND `POLICY_NAME` IN
        <foreach collection="list" item="item" index="index" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
