<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY"
       physicalName="TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY"
       remark="国补类目表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY">
        INSERT INTO TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="categoryCode != null">`CATEGORY_CODE`,</if>
            <if test="categoryName != null">`CATEGORY_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="categorySort != null">`CATEGORY_SORT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="categoryCode != null">#{categoryCode,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="categorySort != null">#{categorySort,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <operation name="findAll" multiplicity="many">
        SELECT * from TP_MERCHANT_NATIONAL_SUBSIDY_PRODUCTS_CATEGORY
        WHERE IS_DEL=0
    </operation>

</table>
