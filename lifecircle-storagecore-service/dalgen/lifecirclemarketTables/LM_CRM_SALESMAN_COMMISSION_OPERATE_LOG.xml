<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG" physicalName="LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG"
       remark="分拥信息操作日志表">

    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="unionid != null">`UNIONID`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="preSalesmanCostRateExt != null">`PRE_SALESMAN_COST_RATE_EXT`,</if>
            <if test="afterSalesmanCostRateExt != null">`AFTER_SALESMAN_COST_RATE_EXT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="preCommissionType != null">`PRE_COMMISSION_TYPE`,</if>
            <if test="afterCommissionType != null">`AFTER_COMMISSION_TYPE`,</if>
            <if test="preNormalCommission != null">`PRE_NORMAL_COMMISSION`,</if>
            <if test="afterNormalCommission != null">`AFTER_NORMAL_COMMISSION`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="preFixedCommission != null">`pre_fixed_commission`,</if>
            <if test="afterFixedCommission != null">`after_fixed_commission`,</if>
            <if test="preFixedCommissionEffectiveTime != null">`pre_fixed_commission_effective_time`,</if>
            <if test="afterFixedCommissionEffectiveTime != null">`after_fixed_commission_effective_time`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="unionid != null">#{unionid,jdbcType=VARCHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="preSalesmanCostRateExt != null">#{preSalesmanCostRateExt,jdbcType=VARCHAR},</if>
            <if test="afterSalesmanCostRateExt != null">#{afterSalesmanCostRateExt,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="preCommissionType != null">#{preCommissionType,jdbcType=TINYINT},</if>
            <if test="afterCommissionType != null">#{afterCommissionType,jdbcType=TINYINT},</if>
            <if test="preNormalCommission != null">#{preNormalCommission,jdbcType=INTEGER},</if>
            <if test="afterNormalCommission != null">#{afterNormalCommission,jdbcType=INTEGER},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="preFixedCommission != null">#{preFixedCommission,jdbcType=TINYINT},</if>
            <if test="afterFixedCommission != null">#{afterFixedCommission,jdbcType=TINYINT},</if>
            <if test="preFixedCommissionEffectiveTime != null">#{preFixedCommissionEffectiveTime,jdbcType=INTEGER},</if>
            <if test="afterFixedCommissionEffectiveTime != null">
                #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER},
            </if>
        </trim>
    </operation>

    <operation name="getGrantLastFromCommissionChangeLog" multiplicity="one" remark="获取授理商最后一次分拥方式为分拥比例的变更记录">
        SELECT *
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        WHERE
        user_id = #{userId, jdbcType=INTEGER}
        AND pre_commission_type = 2
        AND is_del = 0
        ORDER BY id DESC
        LIMIT 1
    </operation>

    <operation name="findGrantChangeTypeLogList" multiplicity="many" remark="获取授理商交易方式从比例到成本的列表">
        SELECT
        *
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <where>
            is_del = 0
            AND pre_commission_type = 2
            AND after_commission_type IN (1,3)
            <if test="list !=null and list.size()&gt;0">
                AND user_id IN
                <foreach collection="list" item="userId" open="(" separator="," close=")">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
    </operation>


    <operation name="updateOperateLog" paramtype="primitive" remark="修改操作记录表">
        UPDATE
        LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        <set>
            <if test="afterCommissionType !=null">
                AFTER_COMMISSION_TYPE=#{afterCommissionType,jdbcType=VARCHAR},
            </if>
            <if test="afterNormalCommission !=null">
                after_normal_commission=#{afterNormalCommission,jdbcType=VARCHAR},
            </if>
            <if test="afterSalesmanCostRateExt !=null">
                AFTER_SALESMAN_COST_RATE_EXT=#{afterSalesmanCostRateExt,jdbcType=VARCHAR},
            </if>
            <if test="beginTime != null">
                begin_time = #{beginTime,jdbcType=TIMESTAMP},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="findUserFixCommissionLog" multiplicity="many" remark="查询用户固定比例操作日志">
        SELECT
        *
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND after_fixed_commission = #{afterFixedCommission, jdbcType=INTEGER}
        AND user_id = #{userId,jdbcType=INTEGER}
    </operation>


    <operation name="getNearAppointTimeMaxLogId" paramtype="primitive" resulttype="java.lang.Long"
               remark="查询最近一次指定月份的操作日志">
        SELECT
        MAX( `id`)
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND user_id = #{userId, jdbcType=INTEGER}
        AND after_fixed_commission_effective_time  <![CDATA[ <= ]]>
        #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER}
    </operation>

    <operation name="getAppointMothMaxLogId" paramtype="primitive" resulttype="java.lang.Long"
               remark="查询指定月份最大操作日志">
        SELECT
        MAX( `id`)
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND user_id = #{userId, jdbcType=INTEGER}
        AND after_fixed_commission_effective_time = #{afterFixedCommissionEffectiveTime,jdbcType=INTEGER}
    </operation>

    <operation name="getById" multiplicity="one" paramtype="primitive" remark="根据id查询日志记录">
        SELECT
        *
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        and id = #{id,jdbcType=BIGINT}
        limit 1
    </operation>

    <operation name="findFixCommissionLogByUserIdList" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.resultmap.FixedCommissionLogStaticInfo"
               remark="查询用户固定比例操作日志">
        SELECT
        user_id as userId,
        count(*) as num
        FROM LM_CRM_SALESMAN_COMMISSION_OPERATE_LOG
        where is_del = 0
        AND after_fixed_commission = #{afterFixedCommission, jdbcType=INTEGER}
        AND user_id in
        <foreach collection="list" index="index" item="userId" open="(" separator="," close=")">
            #{userId,jdbcType=INTEGER}
        </foreach>
        group by user_id
    </operation>

</table>
