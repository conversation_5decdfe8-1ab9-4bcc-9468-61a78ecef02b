<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ACTIVITY_PACKAGE" physicalName="TP_ACTIVITY_PACKAGE"
       remark="活动额度包信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_ACTIVITY_PACKAGE">
        INSERT INTO TP_ACTIVITY_PACKAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="period != null">`PERIOD`,</if>
            <if test="merchantNumber != null">`MERCHANT_NUMBER`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="periodEndDate != null">`PERIOD_END_DATE`,</if>
            <if test="periodBeginDate != null">`PERIOD_BEGIN_DATE`,</if>
            <if test="total != null">`TOTAL`,</if>
            <if test="balance != null">`BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="period != null">#{period,jdbcType=VARCHAR},</if>
            <if test="merchantNumber != null">#{merchantNumber,jdbcType=VARCHAR},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="periodEndDate != null">#{periodEndDate,jdbcType=TIMESTAMP},</if>
            <if test="periodBeginDate != null">#{periodBeginDate,jdbcType=TIMESTAMP},</if>
            <if test="total != null">#{total,jdbcType=DECIMAL},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getByLiquidationTypeAndMerchantNumber">
        select * from TP_ACTIVITY_PACKAGE
        where merchant_number= #{merchantNumber,jdbcType=VARCHAR}
        and liquidation_type= #{liquidationType,jdbcType=TINYINT}
        order by period_end_date desc limit 1
    </operation>
</table>
