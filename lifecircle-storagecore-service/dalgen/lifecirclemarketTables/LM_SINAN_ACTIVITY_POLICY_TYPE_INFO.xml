<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_SINAN_ACTIVITY_POLICY_TYPE_INFO" physicalName="LM_SINAN_ACTIVITY_POLICY_TYPE_INFO" remark="政策类型信息表">

    <operation name="getPolicyNameByPolicy" multiplicity="many">
        select id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
    </operation>

    <operation name="getActivityNameByActivity" multiplicity="many">
        select id,activity_type,activity_industry_type from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=2
    </operation>

    <operation name="getPolicyName" multiplicity="one">
        select id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
        and policy_type_name=#{policyTypeName,jdbcType=VARCHAR}
    </operation>

    <operation name="getActivityName" multiplicity="one">
        select id,activity_type,activity_industry_type from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=2
        and policy_type_name=#{policyTypeName,jdbcType=VARCHAR}
    </operation>

    <operation name="getActivityNameById" multiplicity="one">
        select id,activity_type,policy_type_name from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO where activity_type=1
        and id=#{id,jdbcType=INTEGER}
    </operation>

    <operation name="getActivityByIdIgnoreIsDel">
        select id,activity_type,policy_type_name
        from LM_SINAN_ACTIVITY_POLICY_TYPE_INFO
        where
        activity_type=1
        and id=#{id,jdbcType=INTEGER}
    </operation>
</table>