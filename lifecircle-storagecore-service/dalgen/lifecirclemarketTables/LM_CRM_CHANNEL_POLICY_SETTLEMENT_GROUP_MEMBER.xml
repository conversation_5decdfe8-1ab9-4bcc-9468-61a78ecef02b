<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER"
       physicalName="LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER"
       remark="渠道政策结算组成员表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER">
        INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList">
        INSERT INTO LM_CRM_CHANNEL_POLICY_SETTLEMENT_GROUP_MEMBER(GROUP_ID,USER_ID) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.userId,jdbcType=INTEGER}
            )
        </foreach>
    </operation>

    <operation name="findSettlementGroupMemberList" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.SettlementGroupMemberListDTO"
               remark="查询结算组成员列表">
        select
        t1.user_id as userId,
        t2.username,
        t1.create_time as createTime,
        t2.companyname as company
        from lm_crm_channel_policy_settlement_group_member t1
        left join tp_user t2 on t1.user_id = t2.id
        where t1.group_id = #{groupId,jdbcType=VARCHAR}
        <if test="userId != null">
            and t1.user_id = #{userId,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            and t2.username = #{username,jdbcType=VARCHAR}
        </if>
        and t1.is_del = 0
    </operation>
</table>
