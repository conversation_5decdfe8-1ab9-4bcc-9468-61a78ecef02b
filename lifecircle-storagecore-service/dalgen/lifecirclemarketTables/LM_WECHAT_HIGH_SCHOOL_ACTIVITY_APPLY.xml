<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_WECHAT_HIGH_SCHOOL_ACTIVITY_APPLY" physicalName="LM_WECHAT_HIGH_SCHOOL_ACTIVITY_APPLY"
       remark="微信高校活动申请单表">

    <operation name="findCrmApplyList" multiplicity="many" paramClass="com.fshows.lifecircle.storagecore.service.dal.lifecirclemarket.dataobject.FindCrmApplyListExportDO" remark="查询">
        select
        *
        from lm_wechat_high_school_activity_apply
        where activity_status in (2, 3, 4, 5, 6, 7, 8)
        <if test="uid != null">
            and uid =#{uid, jdbcType=INTEGER}
        </if>
        <if test="username != null and '' != username">
            and username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null and '' != company">
            and company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
        </if>
        <if test="list != null and list.size() > 0">
            and belong in
            <foreach collection="list" separator="," open="(" close=")" index="index" item="belong">
                #{belong, jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="subjectType != null">
            and subject_type = #{subjectType, jdbcType=INTEGER}
        </if>
        <if test="activityStatus != null">
            and activity_status = #{activityStatus,jdbcType=TINYINT}
        </if>
        <if test="activityOpenEndTime != null">
            AND activity_sign_time <![CDATA[ <= ]]>  #{activityOpenEndTime,jdbcType=INTEGER}
        </if>
        <if test="activityOpenStartTime != null">
            AND activity_sign_time <![CDATA[ >= ]]>  #{activityOpenStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStartTime != null">
            AND activity_rebate_apply_time <![CDATA[ >= ]]>  #{rebateApplyStartTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyEndTime != null">
            AND activity_rebate_apply_time <![CDATA[ <= ]]>  #{rebateApplyEndTime,jdbcType=INTEGER}
        </if>
        <if test="rebateApplyStatus != null">
            and activity_rebate_apply_status = #{rebateApplyStatus,jdbcType=TINYINT}
        </if>
        <if test="rebateBeginMonth != null">
            and activity_rebate_begin_month = #{rebateBeginMonth,jdbcType=INTEGER}
        </if>
        order by activity_sign_time desc, uid desc
    </operation>

    <operation name="getMerchantActivityApplyByUidSubMchId">
        SELECT *
        FROM lm_wechat_high_school_activity_apply
        WHERE uid = #{uid,jdbcType=INTEGER}
        and sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1;
    </operation>

    <operation name="updateRebateApplyInfoById" paramtype="object">
        update lm_wechat_high_school_activity_apply
        set update_time = now()
        <if test="activityRebateApplyStatus != null">
            ,ACTIVITY_REBATE_APPLY_STATUS = #{activityRebateApplyStatus,jdbcType=TINYINT}
        </if>
        <if test="activityRebateApplyTime != null">
            ,ACTIVITY_REBATE_APPLY_TIME = #{activityRebateApplyTime,jdbcType=INTEGER}
        </if>
        <if test="activityRebateBeginMonth != null">
            ,ACTIVITY_REBATE_BEGIN_MONTH = #{activityRebateBeginMonth,jdbcType=INTEGER}
        </if>
        <if test="activityRebateRejectReason != null">
            ,ACTIVITY_REBATE_REJECT_REASON = #{activityRebateRejectReason,jdbcType=VARCHAR}
        </if>
        where id = #{id,jdbcType=BIGINT}
    </operation>
</table>
