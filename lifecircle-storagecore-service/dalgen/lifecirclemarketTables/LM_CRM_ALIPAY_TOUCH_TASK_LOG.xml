<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_ALIPAY_TOUCH_TASK_LOG" physicalName="LM_CRM_ALIPAY_TOUCH_TASK_LOG"
       remark="任务日志">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_ALIPAY_TOUCH_TASK_LOG">
        INSERT INTO LM_CRM_ALIPAY_TOUCH_TASK_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="taskNo != null">`TASK_NO`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="operateDesc != null">`OPERATE_DESC`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="operatorType != null">`OPERATOR_TYPE`,</if>
            <if test="operateStatus != null">`OPERATE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="taskNo != null">#{taskNo,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="operateDesc != null">#{operateDesc,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="operatorType != null">#{operatorType,jdbcType=TINYINT},</if>
            <if test="operateStatus != null">#{operateStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findPendingReasonByTaskNoList" multiplicity="many" remark="查询任务挂起原因">
        select TASK_NO,ERROR_MSG
        from lm_crm_alipay_touch_task_log
        where TASK_NO IN
        <foreach collection="list" item="taskNo" open="(" separator="," close=")">
            #{taskNo,jdbcType=VARCHAR}
        </foreach>
        and OPERATE_TYPE = 9
        order by create_time desc
    </operation>
</table>
