<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_SINAN_ACTIVITY_GOLD_MERCHANT_DAY" physicalName="LM_SINAN_ACTIVITY_GOLD_MERCHANT_DAY"
       remark="金卡商编日汇总表">

    <operation name="findListByMerchantNoList" multiplicity="many" remark="根据商编号列表查询交易数据">
        SELECT
        merchant_no merchantNo,
        SUM(income_trade_num) incomeTradeNum,
        SUM(income_money) incomeMoney
        FROM lm_sinan_activity_gold_merchant_day
        WHERE
        merchant_no IN
        <foreach close=")" collection="list" index="index" item="merchantNo" open="(" separator=",">
            #{merchantNo,jdbcType=VARCHAR}
        </foreach>
        AND trade_day <![CDATA[ >= ]]> #{startDay,jdbcType=INTEGER}
        AND trade_day <![CDATA[ <= ]]> #{endDay,jdbcType=INTEGER}
        AND pay_type IN (1, 2, 11, 12, 7, 21, 22, 5, 9, 91, 13, 23)
        GROUP BY merchant_no
    </operation>

</table>
