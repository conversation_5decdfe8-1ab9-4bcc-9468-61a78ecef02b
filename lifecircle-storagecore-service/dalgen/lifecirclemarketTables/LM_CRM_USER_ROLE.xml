<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_USER_ROLE" physicalName="LM_CRM_USER_ROLE"
       remark="用户与角色关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_USER_ROLE">
        INSERT INTO LM_CRM_USER_ROLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="roleId != null">`ROLE_ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="roleId != null">#{roleId,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    <operation multiplicity="one" name="findByRoleIdAndUserId" remark="根据角色id用户id查询角色">
        SELECT *
        FROM lm_crm_user_role
        WHERE user_id = #{userId, jdbcType=VARCHAR} and role_id =#{roleId,jdbcType=VARCHAR} limit 1
    </operation>
</table>
