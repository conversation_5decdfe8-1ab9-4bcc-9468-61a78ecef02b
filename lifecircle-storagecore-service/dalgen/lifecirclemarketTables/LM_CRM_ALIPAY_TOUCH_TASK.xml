<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_CRM_ALIPAY_TOUCH_TASK" physicalName="LM_CRM_ALIPAY_TOUCH_TASK"
       remark="支付宝碰一下任务表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_CRM_ALIPAY_TOUCH_TASK">
        INSERT INTO LM_CRM_ALIPAY_TOUCH_TASK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="pendTime != null">`PEND_TIME`,</if>
            <if test="limitTime != null">`LIMIT_TIME`,</if>
            <if test="acceptTime != null">`ACCEPT_TIME`,</if>
            <if test="notifyTime != null">`NOTIFY_TIME`,</if>
            <if test="receiveTime != null">`RECEIVE_TIME`,</if>
            <if test="distributeTime != null">`DISTRIBUTE_TIME`,</if>
            <if test="snList != null">`SN_LIST`,</if>
            <if test="taskNo != null">`TASK_NO`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="shopName != null">`SHOP_NAME`,</if>
            <if test="smidList != null">`SMID_LIST`,</if>
            <if test="storePic != null">`STORE_PIC`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="productId != null">`PRODUCT_ID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="fbSmidList != null">`FB_SMID_LIST`,</if>
            <if test="marketName != null">`MARKET_NAME`,</if>
            <if test="reasonDesc != null">`REASON_DESC`,</if>
            <if test="contactName != null">`CONTACT_NAME`,</if>
            <if test="productName != null">`PRODUCT_NAME`,</if>
            <if test="productTags != null">`PRODUCT_TAGS`,</if>
            <if test="districtCode != null">`DISTRICT_CODE`,</if>
            <if test="districtName != null">`DISTRICT_NAME`,</if>
            <if test="provinceCode != null">`PROVINCE_CODE`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="salesmanName != null">`SALESMAN_NAME`,</if>
            <if test="contactMobile != null">`CONTACT_MOBILE`,</if>
            <if test="interiorPhoto != null">`INTERIOR_PHOTO`,</if>
            <if test="registerPhoto != null">`REGISTER_PHOTO`,</if>
            <if test="fbMerchantAddress != null">`FB_MERCHANT_ADDRESS`,</if>
            <if test="alipayMerchantAddress != null">`ALIPAY_MERCHANT_ADDRESS`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="taskStatus != null">`TASK_STATUS`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="pendingStatus != null">`PENDING_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=BIGINT},</if>
            <if test="pendTime != null">#{pendTime,jdbcType=BIGINT},</if>
            <if test="limitTime != null">#{limitTime,jdbcType=BIGINT},</if>
            <if test="acceptTime != null">#{acceptTime,jdbcType=BIGINT},</if>
            <if test="notifyTime != null">#{notifyTime,jdbcType=BIGINT},</if>
            <if test="receiveTime != null">#{receiveTime,jdbcType=BIGINT},</if>
            <if test="distributeTime != null">#{distributeTime,jdbcType=BIGINT},</if>
            <if test="snList != null">#{snList,jdbcType=VARCHAR},</if>
            <if test="taskNo != null">#{taskNo,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="shopName != null">#{shopName,jdbcType=VARCHAR},</if>
            <if test="smidList != null">#{smidList,jdbcType=VARCHAR},</if>
            <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="productId != null">#{productId,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="fbSmidList != null">#{fbSmidList,jdbcType=VARCHAR},</if>
            <if test="marketName != null">#{marketName,jdbcType=VARCHAR},</if>
            <if test="reasonDesc != null">#{reasonDesc,jdbcType=VARCHAR},</if>
            <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
            <if test="productName != null">#{productName,jdbcType=VARCHAR},</if>
            <if test="productTags != null">#{productTags,jdbcType=VARCHAR},</if>
            <if test="districtCode != null">#{districtCode,jdbcType=VARCHAR},</if>
            <if test="districtName != null">#{districtName,jdbcType=VARCHAR},</if>
            <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="salesmanName != null">#{salesmanName,jdbcType=VARCHAR},</if>
            <if test="contactMobile != null">#{contactMobile,jdbcType=VARCHAR},</if>
            <if test="interiorPhoto != null">#{interiorPhoto,jdbcType=VARCHAR},</if>
            <if test="registerPhoto != null">#{registerPhoto,jdbcType=VARCHAR},</if>
            <if test="fbMerchantAddress != null">#{fbMerchantAddress,jdbcType=VARCHAR},</if>
            <if test="alipayMerchantAddress != null">#{alipayMerchantAddress,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="taskStatus != null">#{taskStatus,jdbcType=TINYINT},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
            <if test="pendingStatus != null">#{pendingStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <operation name="findTaskList" multiplicity="many">
        select *
        from lm_crm_alipay_touch_task
        where is_del = 0
        <if test="taskNo != null and taskNo != ''">and task_no = #{taskNo,jdbcType=VARCHAR}</if>
        <if test="agentId != null">and agent_id = #{agentId,jdbcType=INTEGER}</if>
        <if test="salesmanId != null">
            and salesman_id = #{salesmanId,jdbcType=INTEGER}
        </if>
        <if test="salesmanName != null and salesmanName != ''">
            and salesman_name like CONCAT(#{salesmanName,jdbcType=VARCHAR},'%')
        </if>
        <if test="marketId != null">
            and market_id = #{marketId,jdbcType=INTEGER}
        </if>
        <if test="marketName != null and marketName != ''">
            and market_name like CONCAT(#{marketName,jdbcType=VARCHAR},'%')
        </if>
        <if test="notifyTimeStart != null and notifyTimeEnd != null">
            and notify_time between #{notifyTimeStart,jdbcType=BIGINT} and #{notifyTimeEnd,jdbcType=BIGINT}
        </if>
        <if test="taskStatus != null">and task_status = #{taskStatus,jdbcType=TINYINT}</if>
        <if test="contactName != null and contactName!= ''">and contact_name = #{contactName,jdbcType=VARCHAR}</if>
        <if test="contactMobile != null and contactMobile!= ''">and contact_mobile = #{contactMobile,jdbcType=VARCHAR}
        </if>
        <if test="storeName != null and storeName != ''">
            and store_name like CONCAT(#{storeName,jdbcType=VARCHAR},'%')
        </if>
        <if test="merchantType != null">and merchant_type = #{merchantType,jdbcType=TINYINT}</if>
        <if test="uid != null">and uid = #{uid,jdbcType=INTEGER}</if>
        <if test="username != null and username != ''">and username = #{username,jdbcType=VARCHAR}</if>
        <if test="cityName != null and cityName != ''">
            and city_name = like CONCAT(#{cityName,jdbcType=VARCHAR},'%')
        </if>
        <if test="provinceCode != null and provinceCode !=''">
            and province_code = #{provinceCode,jdbcType=VARCHAR}
        </if>
        <if test="cityCode != null and cityCode !=''">
            and city_code = #{cityCode,jdbcType=VARCHAR}
        </if>
        <if test="districtCode != null and districtCode !=''">
            and district_code = #{districtCode,jdbcType=VARCHAR}
        </if>
        <if test="pendingStatus != null">
            and pending_status = #{pendingStatus,jdbcType=INTEGER}
        </if>
        <if test="districtName != null and districtName != ''">
            and district_name = #{districtName,jdbcType=VARCHAR}
        </if>
        <if test="activityType != null and activityType != ''">
            and activity_type = #{activityType,jdbcType=VARCHAR}
        </if>
        order by notify_time desc
        limit 1000
    </operation>
</table>
