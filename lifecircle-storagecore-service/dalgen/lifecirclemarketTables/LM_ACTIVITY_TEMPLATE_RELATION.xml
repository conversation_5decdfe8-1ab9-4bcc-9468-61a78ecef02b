<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="LM_ACTIVITY_TEMPLATE_RELATION" physicalName="LM_ACTIVITY_TEMPLATE_RELATION"
       remark="活动模板配置关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:LM_ACTIVITY_TEMPLATE_RELATION">
        INSERT INTO LM_ACTIVITY_TEMPLATE_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="sinanActivityCode != null">`SINAN_ACTIVITY_CODE`,</if>
            <if test="relationTemplateCode != null">`RELATION_TEMPLATE_CODE`,</if>
            <if test="relationTemplateName != null">`RELATION_TEMPLATE_NAME`,</if>
            <if test="relationTemplateType != null">`RELATION_TEMPLATE_TYPE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="fitRange != null">`FIT_RANGE`,</if>
            <if test="sortScore != null">`SORT_SCORE`,</if>
            <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="sinanActivityCode != null">#{sinanActivityCode,jdbcType=VARCHAR},</if>
            <if test="relationTemplateCode != null">#{relationTemplateCode,jdbcType=VARCHAR},</if>
            <if test="relationTemplateName != null">#{relationTemplateName,jdbcType=VARCHAR},</if>
            <if test="relationTemplateType != null">#{relationTemplateType,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="fitRange != null">#{fitRange,jdbcType=TINYINT},</if>
            <if test="sortScore != null">#{sortScore,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <operation name="findActivityIdListByCode" multiplicity="many">
        SELECT *
        FROM lm_activity_template_relation
        WHERE is_del = 0
        AND effective_status = 1
        AND sinan_activity_code = #{sinanActivityCode, jdbcType=VARCHAR}
        GROUP BY activity_id
    </operation>
</table>
