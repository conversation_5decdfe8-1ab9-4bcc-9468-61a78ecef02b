<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY" physicalName="TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY"
       remark="企业团餐商户报名详情表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY">
        INSERT INTO TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="contactName != null">`CONTACT_NAME`,</if>
            <if test="logicGroupId != null">`LOGIC_GROUP_ID`,</if>
            <if test="agreementOnePic != null">`AGREEMENT_ONE_PIC`,</if>
            <if test="agreementTwoPic != null">`AGREEMENT_TWO_PIC`,</if>
            <if test="contactPosition != null">`CONTACT_POSITION`,</if>
            <if test="contactTelephone != null">`CONTACT_TELEPHONE`,</if>
            <if test="alipayLegalPersonAccount != null">`ALIPAY_LEGAL_PERSON_ACCOUNT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="pvDay != null">`PV_DAY`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="auditTime != null">`AUDIT_TIME`,</if>
            <if test="pvDayMeal != null">`PV_DAY_MEAL`,</if>
            <if test="expectTime != null">`EXPECT_TIME`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="equipmentNum != null">`EQUIPMENT_NUM`,</if>
            <if test="groupMealStatus != null">`GROUP_MEAL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
            <if test="logicGroupId != null">#{logicGroupId,jdbcType=VARCHAR},</if>
            <if test="agreementOnePic != null">#{agreementOnePic,jdbcType=VARCHAR},</if>
            <if test="agreementTwoPic != null">#{agreementTwoPic,jdbcType=VARCHAR},</if>
            <if test="contactPosition != null">#{contactPosition,jdbcType=VARCHAR},</if>
            <if test="contactTelephone != null">#{contactTelephone,jdbcType=VARCHAR},</if>
            <if test="alipayLegalPersonAccount != null">#{alipayLegalPersonAccount,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="pvDay != null">#{pvDay,jdbcType=INTEGER},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="auditTime != null">#{auditTime,jdbcType=INTEGER},</if>
            <if test="pvDayMeal != null">#{pvDayMeal,jdbcType=INTEGER},</if>
            <if test="expectTime != null">#{expectTime,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="equipmentNum != null">#{equipmentNum,jdbcType=INTEGER},</if>
            <if test="groupMealStatus != null">#{groupMealStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findGroupMealMerchantList" multiplicity="paging" paramtype="primitive"
               paging="GroupMealMerchantList" remark="分页查询crm后台商户审核列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.GroupMealMerchantDetailDTO">
        SELECT users.`ID` as merchantId,
        users.`BELONG` as agentId,
        users.`SALESMAN`as salesmanId,
        gma.username AS username,
        gma.company as company,
        gma.address as address,
        gma.AUDIT_TIME as auditTime ,
        gma.APPLY_TIME as applyTime,
        gma.create_time as firstApplyTime,
        gma.LICENSE_PIC as licensePic,
        gma.AGREEMENT_ONE_PIC as agreementOnePic,
        gma.AGREEMENT_TWO_PIC as agreementTwoPic,
        gma.GROUP_MEAL_STATUS as groupMealStatus,
        gma.CONTACT_POSITION as contactPosition,
        gma.CONTACT_NAME as contactName,
        gma.CONTACT_TELEPHONE as contactTelephone,
        gma.ALIPAY_LEGAL_PERSON_ACCOUNT as alipayLegalPersonAccount,
        gma.PV_DAY as pvDay,
        gma.PV_DAY_MEAL as pvDayMeal,
        gma.EXPECT_TIME as expectTime,
        gma.EQUIPMENT_NUM as equipmentNum,
        gma.reason as reason
        FROM TP_LIFECIRCLE_GROUP_MEAL_MERCHANT_APPLY gma
        LEFT JOIN TP_USERS users
        ON gma.merchant_id = users.`ID`
        <where>
            gma.is_del = 0
            <if test="username != null and username != '' ">
                AND gma.`username` like CONCAT ( #{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="company != null and company != '' ">
                AND gma.`company` like CONCAT ( #{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantId != null and merchantId != 0 ">
                AND gma.`merchant_id` = #{merchantId,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.apply_time BETWEEN #{applyStartTime,jdbcType=INTEGER} and #{applyEndTime,jdbcType=INTEGER}
            </if>
            <if test="applyStartTime  != null and applyStartTime  != 0 and applyEndTime   != null and applyEndTime   != 0">
                AND gma.audit_time BETWEEN #{auditStartTime,jdbcType=INTEGER} and #{auditEndTime,jdbcType=INTEGER}
            </if>
            <if test="groupMealStatusList != null and groupMealStatusList.size &gt;0">
                AND gma.GROUP_MEAL_STATUS IN
                <foreach close=")" collection="groupMealStatusList" index="index" item="groupMealStatus" open="("
                         separator=",">
                    #{groupMealStatus,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="agentUserIdList != null and agentUserIdList.size &gt;0">
                AND
                users.belong IN
                <foreach close=")" collection="agentUserIdList" index="index" item="userId" open="("
                         separator=",">
                    #{userId,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="salesManUserIdList != null and salesManUserIdList.size &gt;0">
                and
                users.salesman IN
                <foreach close=")" collection="salesManUserIdList" index="index" item="salesManId" open="("
                         separator=",">
                    #{salesManId,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ORDER BY gma.apply_time DESC, gma.CREATE_TIME DESC
    </operation>

</table>
