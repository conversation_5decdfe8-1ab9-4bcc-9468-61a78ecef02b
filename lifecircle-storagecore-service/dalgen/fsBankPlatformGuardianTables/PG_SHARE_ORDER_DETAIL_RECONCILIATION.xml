<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="PG_SHARE_ORDER_DETAIL_RECONCILIATION" physicalName="PG_SHARE_ORDER_DETAIL_RECONCILIATION"
       remark="分账单明细对账表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:PG_SHARE_ORDER_DETAIL_RECONCILIATION">
        INSERT INTO PG_SHARE_ORDER_DETAIL_RECONCILIATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="failType != null">`FAIL_TYPE`,</if>
            <if test="dealState != null">`DEAL_STATE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="handleDate != null">`HANDLE_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbFinishTime != null">`FB_FINISH_TIME`,</if>
            <if test="platformFinishTime != null">`PLATFORM_FINISH_TIME`,</if>
            <if test="fbTotalAmount != null">`FB_TOTAL_AMOUNT`,</if>
            <if test="platformTotalAmount != null">`PLATFORM_TOTAL_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="failType != null">#{failType,jdbcType=VARCHAR},</if>
            <if test="dealState != null">#{dealState,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="handleDate != null">#{handleDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbFinishTime != null">#{fbFinishTime,jdbcType=VARCHAR},</if>
            <if test="platformFinishTime != null">#{platformFinishTime,jdbcType=VARCHAR},</if>
            <if test="fbTotalAmount != null">#{fbTotalAmount,jdbcType=DECIMAL},</if>
            <if test="platformTotalAmount != null">#{platformTotalAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
</table>
