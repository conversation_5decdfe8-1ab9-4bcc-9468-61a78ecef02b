<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="PG_PLATFORM_SHARE_ORDER" physicalName="PG_PLATFORM_SHARE_ORDER"
       remark="平台方分账单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:PG_PLATFORM_SHARE_ORDER">
        INSERT INTO PG_PLATFORM_SHARE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="refundSn != null">`REFUND_SN`,</if>
            <if test="shareTime != null">`SHARE_TIME`,</if>
            <if test="tradeType != null">`TRADE_TYPE`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="settleDate != null">`SETTLE_DATE`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="shareState != null">`SHARE_STATE`,</if>
            <if test="shareDetail != null">`SHARE_DETAIL`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="feeAttribution != null">`FEE_ATTRIBUTION`,</if>
            <if test="platformRefundSn != null">`PLATFORM_REFUND_SN`,</if>
            <if test="shareStateRemark != null">`SHARE_STATE_REMARK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="otherSharePrice != null">`OTHER_SHARE_PRICE`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
            <if test="shareTime != null">#{shareTime,jdbcType=VARCHAR},</if>
            <if test="tradeType != null">#{tradeType,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="shareState != null">#{shareState,jdbcType=VARCHAR},</if>
            <if test="shareDetail != null">#{shareDetail,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="feeAttribution != null">#{feeAttribution,jdbcType=VARCHAR},</if>
            <if test="platformRefundSn != null">#{platformRefundSn,jdbcType=VARCHAR},</if>
            <if test="shareStateRemark != null">#{shareStateRemark,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="otherSharePrice != null">#{otherSharePrice,jdbcType=DECIMAL},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        </trim>
    </operation>
    <operation name="batchInsert" paramtype="objectList" remark="批量数据插入">
        INSERT INTO PG_PLATFORM_SHARE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `TRADE_NO`,
            `REFUND_SN`,
            `SHARE_TIME`,
            `TRADE_TYPE`,
            `MERCHANT_NO`,
            `SETTLE_DATE`,
            `SHARE_REQ_NO`,
            `SHARE_STATE`,
            `SHARE_DETAIL`,
            `PLATFORM_REQ_NO`,
            `FEE_ATTRIBUTION`,
            `PLATFORM_REFUND_SN`,
            `SHARE_STATE_REMARK`,
            `FEE`,
            `SHARE_PRICE`,
            `OTHER_SHARE_PRICE`,
            `ORDER_SN`,
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.tradeNo,jdbcType=VARCHAR},
                #{item.refundSn,jdbcType=VARCHAR},
                #{item.shareTime,jdbcType=VARCHAR},
                #{item.tradeType,jdbcType=VARCHAR},
                #{item.merchantNo,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.shareReqNo,jdbcType=VARCHAR},
                #{item.shareState,jdbcType=VARCHAR},
                #{item.shareDetail,jdbcType=VARCHAR},
                #{item.platformReqNo,jdbcType=VARCHAR},
                #{item.feeAttribution,jdbcType=VARCHAR},
                #{item.platformRefundSn,jdbcType=VARCHAR},
                #{item.shareStateRemark,jdbcType=VARCHAR},
                #{item.fee,jdbcType=DECIMAL},
                #{item.sharePrice,jdbcType=DECIMAL},
                #{item.otherSharePrice,jdbcType=DECIMAL},
                #{item.orderSn,jdbcType=VARCHAR},
            </trim>
        </foreach>
    </operation>
</table>
