<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="PG_SHARE_BILL_FILE" physicalName="PG_SHARE_BILL_FILE"
       remark="分账对账单下载">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:PG_SHARE_BILL_FILE">
        INSERT INTO PG_SHARE_BILL_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="state != null">`STATE`,</if>
            <if test="fileUrl != null">`FILE_URL`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="handleDate != null">`HANDLE_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="shareAmount != null">`SHARE_AMOUNT`,</if>
            <if test="shareIncome != null">`SHARE_INCOME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="state != null">#{state,jdbcType=VARCHAR},</if>
            <if test="fileUrl != null">#{fileUrl,jdbcType=VARCHAR},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="handleDate != null">#{handleDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="shareAmount != null">#{shareAmount,jdbcType=DECIMAL},</if>
            <if test="shareIncome != null">#{shareIncome,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="updateStateAndAmountByHandleDateAndChannel" remark="通过handle_date和channel来修改state">
        update PG_SHARE_BILL_FILE
        set
        state = #{state,jdbcType=VARCHAR},
        SHARE_AMOUNT = #{shareAmount,jdbcType=DECIMAL},
        SHARE_INCOME = #{shareIncome,jdbcType=DECIMAL}
        where
        handle_date = #{handleDate,jdbcType=INTEGER}
        and
        channel = #{channel,jdbcType=TINYINT}
    </operation>
    <operation name="getByHandleDateAndChannel" multiplicity="one" remark="通过handle_date,channel获取分账结算单">
        select *
        from PG_SHARE_BILL_FILE
        where
        handle_date = #{handleDate,jdbcType=INTEGER}
        and
        channel = #{channel,jdbcType=TINYINT}
        limit 1
    </operation>
</table>
