<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FK_RISK_WORK_ORDER" physicalName="FK_RISK_WORK_ORDER"
       remark="风控工单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FK_RISK_WORK_ORDER">
        INSERT INTO FK_RISK_WORK_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="rejectMsg != null">`REJECT_MSG`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
            <if test="marketName != null">`MARKET_NAME`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="wechatSmid != null">`WECHAT_SMID`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="orgRejectMsg != null">`ORG_REJECT_MSG`,</if>
            <if test="reviewerName != null">`REVIEWER_NAME`,</if>
            <if test="salesmanName != null">`SALESMAN_NAME`,</if>
            <if test="ticketNumber != null">`TICKET_NUMBER`,</if>
            <if test="submitterName != null">`SUBMITTER_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="riskRecordId != null">`RISK_RECORD_ID`,</if>
            <if test="submitterType != null">`SUBMITTER_TYPE`,</if>
            <if test="orgReviewStatus != null">`ORG_REVIEW_STATUS`,</if>
            <if test="workOrderNumber != null">`WORK_ORDER_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="reviemTime != null">`REVIEM_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orgReviewTime != null">`ORG_REVIEW_TIME`,</if>
            <if test="submitterTime != null">`SUBMITTER_TIME`,</if>
            <if test="fubeiSubmitterTime != null">`FUBEI_SUBMITTER_TIME`,</if>
            <if test="appealTemplate != null">`APPEAL_TEMPLATE`,</if>
            <if test="isPush != null">`is_push`,</if>
            <if test="appealTemplateType != null">`APPEAL_TEMPLATE_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="rejectMsg != null">#{rejectMsg,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
            <if test="marketName != null">#{marketName,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="wechatSmid != null">#{wechatSmid,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="orgRejectMsg != null">#{orgRejectMsg,jdbcType=VARCHAR},</if>
            <if test="reviewerName != null">#{reviewerName,jdbcType=VARCHAR},</if>
            <if test="salesmanName != null">#{salesmanName,jdbcType=VARCHAR},</if>
            <if test="ticketNumber != null">#{ticketNumber,jdbcType=VARCHAR},</if>
            <if test="submitterName != null">#{submitterName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="riskRecordId != null">#{riskRecordId,jdbcType=INTEGER},</if>
            <if test="submitterType != null">#{submitterType,jdbcType=TINYINT},</if>
            <if test="orgReviewStatus != null">#{orgReviewStatus,jdbcType=TINYINT},</if>
            <if test="workOrderNumber != null">#{workOrderNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="reviemTime != null">#{reviemTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orgReviewTime != null">#{orgReviewTime,jdbcType=TIMESTAMP},</if>
            <if test="submitterTime != null">#{submitterTime,jdbcType=TIMESTAMP},</if>
            <if test="fubeiSubmitterTime != null">#{fubeiSubmitterTime,jdbcType=TIMESTAMP},</if>
            <if test="appealTemplate != null">#{appealTemplate,jdbcType=LONGVARCHAR},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="appealTemplateType != null">#{appealTemplateType,jdbcType=TINYINT},</if>
        </trim>
    </operation>
    <operation name="getUniqueWorkOrder" multiplicity="one"
               paramtype="object"
               remark="根据条件查询工单">
        select *
        from fk_risk_work_order
        where
        is_del=0
        <if test="wechatSmid !=null ">
            and wechat_smid=#{wechatSmid,jdbcType=VARCHAR}
        </if>
        <if test="merchantId !=null ">
            and merchant_id=#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="merchantNo !=null ">
            and merchant_no=#{merchantNo,jdbcType=VARCHAR}
        </if>
        <if test="dataTypeId !=null ">
            and data_type_id=#{dataTypeId,jdbcType=VARCHAR}
        </if>
        <if test="orderStatus !=null ">
            and order_status !=#{orderStatus,jdbcType=INTEGER}
        </if>
        <if test="isPush !=null ">
            and is_push =#{isPush,jdbcType=INTEGER}
        </if>
        limit 1
    </operation>

    <operation name="getNotAppealWorkOrder" multiplicity="one"
               paramtype="object"
               remark="根据条件查询未申诉陈工的工单">
        select *
        from fk_risk_work_order
        where
        order_status not in(6,7)
        <if test="merchantId !=null ">
            and merchant_id=#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="isPush !=null ">
            and is_push =#{isPush,jdbcType=INTEGER}
        </if>
        limit 1
    </operation>
</table>
