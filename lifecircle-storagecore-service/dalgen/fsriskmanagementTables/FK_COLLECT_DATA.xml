<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="FK_COLLECT_DATA" physicalName="FK_COLLECT_DATA"
    remark="风控中心数据采集表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:FK_COLLECT_DATA">
INSERT INTO FK_COLLECT_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="url != null">`URL`,</if>
        <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
        <if test="collectDataId != null">`COLLECT_DATA_ID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="url != null">#{url,jdbcType=VARCHAR},</if>
        <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
        <if test="collectDataId != null">#{collectDataId,jdbcType=VARCHAR},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <!-- 根据唯一约束操作数据 -->
    <operation name="updateByCollectDataId" paramtype="object" remark="根据唯一约束UniqCollectData更新表:fk_collect_data">
        <![CDATA[
        UPDATE fk_collect_data
        SET
            STATUS          = #{status,jdbcType=TINYINT}
        WHERE
            COLLECT_DATA_ID = #{collectDataId,jdbcType=VARCHAR}
        ]]>
    </operation>

    <operation name="getByUniqCollectData" multiplicity="one" remark="根据唯一约束UniqCollectData获取数据:fk_collect_data">
        SELECT *
        FROM fk_collect_data
        WHERE
        <![CDATA[
            COLLECT_DATA_ID = #{collectDataId,jdbcType=VARCHAR}
        ]]>
        LIMIT 1
    </operation>
    </table>
