<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="FK_RISK_RECORD" physicalName="FK_RISK_RECORD"
           remark="风险记录表">
        <!--    &lt;&gt;   <> -->
        <operation name="insert" paramtype="object" remark="insert:FK_RISK_RECORD">
            <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
                SELECT
                LAST_INSERT_ID()
            </selectKey>
            INSERT INTO FK_RISK_RECORD
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">`ID`,</if>
                <if test="mchId != null">`MCH_ID`,</if>
                <if test="reason != null">`REASON`,</if>
                <if test="orderSn != null">`ORDER_SN`,</if>
                <if test="recordId != null">`RECORD_ID`,</if>
                <if test="riskType != null">`RISK_TYPE`,</if>
                <if test="tradeTime != null">`TRADE_TIME`,</if>
                <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
                <if test="dealMethod != null">`DEAL_METHOD`,</if>
                <if test="orgTradeNo != null">`ORG_TRADE_NO`,</if>
                <if test="payerPhone != null">`PAYER_PHONE`,</if>
                <if test="punishPlan != null">`PUNISH_PLAN`,</if>
                <if test="punishTime != null">`PUNISH_TIME`,</if>
                <if test="fubeiTradeNo != null">`FUBEI_TRADE_NO`,</if>
                <if test="riskTypeDesc != null">`RISK_TYPE_DESC`,</if>
                <if test="collectDataId != null">`COLLECT_DATA_ID`,</if>
                <if test="complaintTime != null">`COMPLAINT_TIME`,</if>
                <if test="singleOutDays != null">`SINGLE_OUT_DAYS`,</if>
                <if test="platformStatus != null">`PLATFORM_STATUS`,</if>
                <if test="complaintDetail != null">`COMPLAINT_DETAIL`,</if>
                <if test="platformTradeNo != null">`PLATFORM_TRADE_NO`,</if>
                <if test="serviceProvider != null">`SERVICE_PROVIDER`,</if>
                <if test="platformDealTime != null">`PLATFORM_DEAL_TIME`,</if>
                <if test="platformRiskType != null">`PLATFORM_RISK_TYPE`,</if>
                <if test="punishDescription != null">`PUNISH_DESCRIPTION`,</if>
                <if test="msidType != null">`MSID_TYPE`,</if>
                <if test="merchantId != null">`MERCHANT_ID`,</if>
                <if test="userComplaintTimes != null">`USER_COMPLAINT_TIMES`,</if>
                <if test="createTime != null">`CREATE_TIME`,</if>
                <if test="updateTime != null">`UPDATE_TIME`,</if>
                <if test="amount != null">`AMOUNT`,</if>
            </trim>
            VALUES
            <trim prefix="(" suffix=")" suffixOverrides=",">
                <if test="id != null">#{id,jdbcType=INTEGER},</if>
                <if test="mchId != null">#{mchId,jdbcType=VARCHAR},</if>
                <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
                <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
                <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
                <if test="riskType != null">#{riskType,jdbcType=VARCHAR},</if>
                <if test="tradeTime != null">#{tradeTime,jdbcType=VARCHAR},</if>
                <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
                <if test="dealMethod != null">#{dealMethod,jdbcType=VARCHAR},</if>
                <if test="orgTradeNo != null">#{orgTradeNo,jdbcType=VARCHAR},</if>
                <if test="payerPhone != null">#{payerPhone,jdbcType=VARCHAR},</if>
                <if test="punishPlan != null">#{punishPlan,jdbcType=VARCHAR},</if>
                <if test="punishTime != null">#{punishTime,jdbcType=VARCHAR},</if>
                <if test="fubeiTradeNo != null">#{fubeiTradeNo,jdbcType=VARCHAR},</if>
                <if test="riskTypeDesc != null">#{riskTypeDesc,jdbcType=VARCHAR},</if>
                <if test="collectDataId != null">#{collectDataId,jdbcType=VARCHAR},</if>
                <if test="complaintTime != null">#{complaintTime,jdbcType=VARCHAR},</if>
                <if test="singleOutDays != null">#{singleOutDays,jdbcType=VARCHAR},</if>
                <if test="platformStatus != null">#{platformStatus,jdbcType=VARCHAR},</if>
                <if test="complaintDetail != null">#{complaintDetail,jdbcType=VARCHAR},</if>
                <if test="platformTradeNo != null">#{platformTradeNo,jdbcType=VARCHAR},</if>
                <if test="serviceProvider != null">#{serviceProvider,jdbcType=VARCHAR},</if>
                <if test="platformDealTime != null">#{platformDealTime,jdbcType=VARCHAR},</if>
                <if test="platformRiskType != null">#{platformRiskType,jdbcType=VARCHAR},</if>
                <if test="punishDescription != null">#{punishDescription,jdbcType=VARCHAR},</if>
                <if test="msidType != null">#{msidType,jdbcType=TINYINT},</if>
                <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
                <if test="userComplaintTimes != null">#{userComplaintTimes,jdbcType=INTEGER},</if>
                <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
            </trim>
        </operation>

        <operation name="queryByOrderSn" multiplicity="many" remark="根据OrderSn 和 来源 获取数据:fk_risk_record">
            SELECT *
            FROM fk_risk_record
            WHERE
            order_sn = #{orderSn,jdbcType=VARCHAR}
            AND
            data_type_id = #{dataTypeId,jdbcType=VARCHAR}
        </operation>

        <operation name="queryByMerchantId" paramtype="primitive" multiplicity="one" remark="根据merchantId:fk_risk_record">
        SELECT *
        FROM fk_risk_record
        WHERE
        MERCHANT_ID = #{merchantId,jdbcType=INTEGER}
        ORDER BY CREATE_TIME DESC
        LIMIT 1
    </operation>
</table>
