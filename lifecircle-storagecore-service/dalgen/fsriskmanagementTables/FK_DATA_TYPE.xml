<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="FK_DATA_TYPE" physicalName="FK_DATA_TYPE"
    remark="采集数据来源类型表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:FK_DATA_TYPE">
INSERT INTO FK_DATA_TYPE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="name != null">`NAME`,</if>
        <if test="dataTypeId != null">`DATA_TYPE_ID`,</if>
        <if test="dataTypeCode != null">`DATA_TYPE_CODE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="name != null">#{name,jdbcType=VARCHAR},</if>
        <if test="dataTypeId != null">#{dataTypeId,jdbcType=VARCHAR},</if>
        <if test="dataTypeCode != null">#{dataTypeCode,jdbcType=VARCHAR},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getByUniqDataTypeId" multiplicity="one" remark="根据唯一约束UniqDataTypeId获取数据:fk_data_type">
        SELECT *
        FROM fk_data_type
        WHERE
        <![CDATA[
            DATA_TYPE_ID    = #{dataTypeId,jdbcType=VARCHAR}
        ]]>
        LIMIT 1
    </operation>
    </table>
