<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="FK_RISK_MERCHANT" physicalName="FK_RISK_MERCHANT"
    remark="风控商户表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:FK_RISK_MERCHANT">
INSERT INTO FK_RISK_MERCHANT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="typeId != null">`TYPE_ID`,</if>
        <if test="dealTime != null">`DEAL_TIME`,</if>
        <if test="riskLevel != null">`RISK_LEVEL`,</if>
        <if test="merchantId != null">`MERCHANT_ID`,</if>
        <if test="singleOutDays != null">`SINGLE_OUT_DAYS`,</if>
        <if test="riskDealStatus != null">`RISK_DEAL_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="typeId != null">#{typeId,jdbcType=VARCHAR},</if>
        <if test="dealTime != null">#{dealTime,jdbcType=INTEGER},</if>
        <if test="riskLevel != null">#{riskLevel,jdbcType=TINYINT},</if>
        <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
        <if test="singleOutDays != null">#{singleOutDays,jdbcType=TINYINT},</if>
        <if test="riskDealStatus != null">#{riskDealStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getByUniqMerchantId" multiplicity="one" remark="根据唯一约束UniqMerchantId获取数据:fk_risk_merchant">
        SELECT *
        FROM fk_risk_merchant
        WHERE
        <![CDATA[
            MERCHANT_ID     = #{merchantId,jdbcType=INTEGER}
        ]]>
        LIMIT 1
    </operation>
    </table>
