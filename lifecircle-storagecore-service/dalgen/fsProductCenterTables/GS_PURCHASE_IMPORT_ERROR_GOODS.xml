<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="GS_PURCHASE_IMPORT_ERROR_GOODS" physicalName="GS_PURCHASE_IMPORT_ERROR_GOODS"
    remark="采购导入错误商品表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:GS_PURCHASE_IMPORT_ERROR_GOODS">
INSERT INTO GS_PURCHASE_IMPORT_ERROR_GOODS
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="code != null">`CODE`,</if>
        <if test="gsUid != null">`GS_UID`,</if>
        <if test="price != null">`PRICE`,</if>
        <if test="goodName != null">`GOOD_NAME`,</if>
        <if test="gsStoreId != null">`GS_STORE_ID`,</if>
        <if test="businessId != null">`BUSINESS_ID`,</if>
        <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="num != null">`NUM`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="code != null">#{code,jdbcType=VARCHAR},</if>
        <if test="gsUid != null">#{gsUid,jdbcType=VARCHAR},</if>
        <if test="price != null">#{price,jdbcType=VARCHAR},</if>
        <if test="goodName != null">#{goodName,jdbcType=VARCHAR},</if>
        <if test="gsStoreId != null">#{gsStoreId,jdbcType=VARCHAR},</if>
        <if test="businessId != null">#{businessId,jdbcType=VARCHAR},</if>
        <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="num != null">#{num,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="findGoodsListByBusinessId" multiplicity="many" remark="根据业务id获取对应的商品信息">
        SELECT
        *
        FROM GS_PURCHASE_IMPORT_ERROR_GOODS
        WHERE BUSINESS_ID = #{businessId,jdbcType=VARCHAR}
        AND IS_DEL = 0
    </operation>
    </table>
