<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="GS_ONLINE_GOODS" physicalName="GS_ONLINE_GOODS"
       remark="网户商品sku表">

    <operation name="listOnlineGoodsByOnlineGoodsId" multiplicity="many" remark="get:GS_ONLINE_GOODS">
        select
        *
        from gs_online_goods
        where online_goods_id in
        <foreach collection="list" item="onlineGoodsId" open="(" separator="," close=")">
            #{onlineGoodsId,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
