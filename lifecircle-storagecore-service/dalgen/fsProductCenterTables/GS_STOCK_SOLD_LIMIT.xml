<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="GS_STOCK_SOLD_LIMIT" physicalName="GS_STOCK_SOLD_LIMIT"
       remark="库存-可售数量设置表">

    <operation name="getSoldLimitByGoodsIdList" multiplicity="many" remark="get:gs_stock_sold_limit">
        select
        *
        from gs_stock_sold_limit
        where goods_id in
        <foreach collection="list" open="(" separator="," close=")" item="item">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and is_del = 0
    </operation>
</table>
