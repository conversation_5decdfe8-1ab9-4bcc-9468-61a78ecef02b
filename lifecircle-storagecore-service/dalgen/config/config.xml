<?xml version="1.0" encoding="UTF-8"?>

<!-- ============================================================== -->
<!-- Master configuration file for auto-generation of dal. -->
<!-- ============================================================== -->

<config>
    <!-- ========================================================== -->
    <!-- The typemap("Type Map") maps from one java type to another -->
    <!-- java type. If you feel the original sql data type to java -->
    <!-- type mapping is not satisfactory, you can use typemap to -->
    <!-- convert it to a more appropriate one. -->
    <!-- ========================================================== -->
    <typemap from="java.sql.Date" to="java.util.Date"/>
    <typemap from="java.sql.Time" to="java.util.Date"/>
    <typemap from="java.sql.Timestamp" to="java.util.Date"/>
    <typemap from="byte" to="int"/>
    <typemap from="short" to="int"/>

    <!-- ========================================================== -->
    <!-- datasource config-->
    <!-- ========================================================== -->
    <!-- 硬件库 -->
    <database name="hardwarecenter" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="*****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_hardware_center"/>
    </database>

    <!-- 风控库 -->
    <database name="fsriskmanagement" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="*****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_risk_management"/>
    </database>

    <!-- 运营库 -->
    <database name="lifecircleoperation" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="**********************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_lifecircle_operation"/>
    </database>

    <!-- 生活圈主库 -->
    <database name="lifecircle" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="*****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="youdian_lifecircle"/>
    </database>

    <database name="lifecirclereadonly" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
          value="*****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="youdian_lifecircle"/>
    </database>

    <!-- 账户中心 -->
    <database name="fsaccountcenter" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_account_center"/>
    </database>

    <!-- 生活圈主库 -->
    <database name="loan" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="******************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_loan"/>
    </database>
    <!--Crm库-->
    <database class="org.gjt.mm.mysql.Driver" name="lifecirclemarket" type="mysql">
        <property name="url"
                  value="*******************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_lifecircle_market"/>
    </database>

    <!-- 财务中台（佣金库）-->
    <database name="financeplatform" class="org.gjt.mm.mysql.Driver"  type="mysql">
        <property name="url"
                  value="**********************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_finance_platform_dev"/>
    </database>

    <database name="qrordering" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="youdian_lifecircle"/>
    </database>

    <!-- 商品中心数据源 -->
    <database name="fsProductCenter" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="****************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_product_center"/>
    </database>

    <!--  实时统计数据源 -->
    <database name="polardb" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="*************************************************************************************************************"/>
        <property name="userid" value="fs_data_center"/>
        <property name="password" value="jkeJ7c1N6VfTLavk"/>
        <property name="schema" value="fs_data_center_test"/>
    </database>

    <!--总督对账库-->
    <database name="fsBankPlatformGuardian" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="************************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fs_bank_platform_guardian"/>
    </database>
    <database name="lifecircleactivity" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="******************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="youdian_lifecircle"/>
    </database>

    <!-- 账户中台库 -->
    <database name="fb_account_biz" class="org.gjt.mm.mysql.Driver" type="mysql">
        <property name="url"
                  value="*************************************************************************************************"/>
        <property name="userid" value="fshows"/>
        <property name="password" value="Fshows12#$"/>
        <property name="schema" value="fb_account_biz"/>
    </database>

    <!-- ========project.name pom.xml========================= -->
    <package value="com.fshows.lifecircle.storagecore.service.dal.${database.name}"/>

    <!-- ========================================================== -->
    <tablePrefixs database="fsriskmanagement">
        <tablePrefix value="fk_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="fsaccountcenter">
        <tablePrefix value="tp_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="lifecircle">
        <tablePrefix value="tp_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="lifecirclereadonly">
        <tablePrefix value="tp_" replace="readonly"/>
    </tablePrefixs>
    <tablePrefixs database="lifecircleoperation">
        <tablePrefix value="tp_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="loan">
        <tablePrefix value="tp_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="lifecirclemarket">
        <tablePrefix value="lm_" replace=""/>
        <tablePrefix value="tp_" replace="Crm"/>
    </tablePrefixs>
    <tablePrefixs database="financeplatform">
        <tablePrefix value="fp_" replace=""/>
    </tablePrefixs>
    <tablePrefixs database="qrordering">
        <tablePrefix value="tp_" replace=""/>
        <tablePrefix value="fs_" replace=""/>
    </tablePrefixs>
    <tablePath value="${database.name}_tables/"/>

    <tablePrefixs database="fsProductCenter">
        <tablePrefix value="gs_" replace=""/>
    </tablePrefixs>

    <tablePrefixs database="lifecircleactivity">
        <tablePrefix value="tp_" replace=""/>
    </tablePrefixs>

    <tablePrefixs database="polardb">
        <tablePrefix value="tp_" replace="Polar"/>
    </tablePrefixs>

    <tablePrefixs database="fsBankPlatformGuardian">
        <tablePrefix value="pg_" replace="Pg"/>
    </tablePrefixs>

    <tablePrefixs  database="fb_account_biz">
        <tablePrefix value="acc_" replace=""/>
    </tablePrefixs>

</config>