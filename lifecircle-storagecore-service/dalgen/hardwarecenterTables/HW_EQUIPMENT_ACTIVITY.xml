<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_ACTIVITY" physicalName="HW_EQUIPMENT_ACTIVITY"
       remark="硬件活动表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_ACTIVITY">
        INSERT INTO HW_EQUIPMENT_ACTIVITY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="activityName != null">`ACTIVITY_NAME`,</if>
            <if test="equipmentIds != null">`EQUIPMENT_IDS`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=VARCHAR},</if>
            <if test="startTime != null">#{startTime,jdbcType=VARCHAR},</if>
            <if test="activityName != null">#{activityName,jdbcType=VARCHAR},</if>
            <if test="equipmentIds != null">#{equipmentIds,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findActivityList" multiplicity="many" remark="获取活动列表">
        SELECT
        *
        FROM
        HW_EQUIPMENT_ACTIVITY
        WHERE IS_DEL = 0
        ORDER BY `ID` DESC
    </operation>

    <operation name="findActivityByActivityNameList" multiplicity="many" paramtype="primitive"
               remark="根据活动名称列表获取活动列表">
        SELECT
        *
        FROM
        HW_EQUIPMENT_ACTIVITY
        WHERE ACTIVITY_NAME IN
        <foreach collection="list" item="item" open="(" separator="," close=")">
            #{item, jdbcType=VARCHAR}
        </foreach>
        AND IS_DEL = 0
    </operation>

</table>
