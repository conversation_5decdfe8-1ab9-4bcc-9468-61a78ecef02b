<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_SN" physicalName="TP_EQUIPMENT_SN"
       remark="硬件sn码记录表">
    <!--    &lt;&gt;   <> -->

    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT_SN">
        INSERT INTO TP_EQUIPMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="systemSn != null">`SYSTEM_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="depot != null">`DEPOT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="lostTime != null">`LOST_TIME`,</if>
            <if test="snStatus != null">`SN_STATUS`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="rejectTime != null">`REJECT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cashierMode != null">`CASHIER_MODE`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="receiveTime != null">`RECEIVE_TIME`,</if>
            <if test="recoverTime != null">`RECOVER_TIME`,</if>
            <if test="damageStatus != null">`DAMAGE_STATUS`,</if>
            <if test="receiveStatus != null">`RECEIVE_STATUS`,</if>
            <if test="businessStatus != null">`BUSINESS_STATUS`,</if>
            <if test="distributeTime != null">`DISTRIBUTE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="systemSn != null">#{systemSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="depot != null">#{depot,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="lostTime != null">#{lostTime,jdbcType=INTEGER},</if>
            <if test="snStatus != null">#{snStatus,jdbcType=TINYINT},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="rejectTime != null">#{rejectTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="cashierMode != null">#{cashierMode,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="receiveTime != null">#{receiveTime,jdbcType=INTEGER},</if>
            <if test="recoverTime != null">#{recoverTime,jdbcType=INTEGER},</if>
            <if test="damageStatus != null">#{damageStatus,jdbcType=TINYINT},</if>
            <if test="receiveStatus != null">#{receiveStatus,jdbcType=TINYINT},</if>
            <if test="businessStatus != null">#{businessStatus,jdbcType=TINYINT},</if>
            <if test="distributeTime != null">#{distributeTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="getAgentByInitSn" paramtype="primitive" resulttype="Integer" remark="根据设备sn码查询代理商id">
        SELECT agent_id from tp_equipment_sn where is_del = 0 and init_sn = #{equipmentSn,jdbcType=VARCHAR}
    </operation>

    <resultmap name="equipmentCountSumMap" type="EquipmentCountSumMap">
        <column name="equipment_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="transaction_amount" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
        <column name="equipment_dau" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="transaction_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="code_scan_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="face_transaction_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="face_transaction_rate" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
    </resultmap>

    <operation name="getEquipmentCountSum" resultmap="equipmentCountSumMap" multiplicity="one" remark="刷脸设备查看汇总统计">
        select
        result.equipment_num,
        result.transaction_amount,
        result.equipment_dau,
        result.transaction_num,
        result.code_scan_num,
        round((result.transaction_num - result.code_scan_num) / result.transaction_num,4) face_transaction_rate,
        (result.transaction_num - result.code_scan_num) face_transaction_num
        from
        (
        SELECT
        count(a.init_sn) equipment_num,
        sum(h.transaction_money) transaction_amount,
        sum(h.equipment_dau) equipment_dau,
        sum(h.transaction_num) transaction_num,
        sum(h.code_scan_num) code_scan_num
        FROM
        tp_equipment_sn a
        INNER JOIN tp_equipment b ON b.id = a.equipment_id AND b.equipment_type = 4 AND b.is_del = 0
        <if test="equipmentSetId != null and equipmentSetId != -1">
            INNER JOIN tp_equipment_set c ON c.equipment_id = a.equipment_id
            AND c.id = #{equipmentSetId,jdbcType=INTEGER}
            AND c.start_time &lt; UNIX_TIMESTAMP( NOW( ) )
            AND c.is_del = 0
        </if>
        LEFT JOIN tp_users e ON a.uid = e.id
        LEFT JOIN tp_lifecircle_store f ON a.store_id = f.store_id
        INNER JOIN tp_user g ON a.agent_id = g.id AND g.own_run = 0 AND g.belong = 0 AND g.sub_config_id = 0
        LEFT JOIN tp_face_scan_equipment_record h ON a.init_sn = h.equipment_sn
        WHERE
        a.business_status = 1
        AND a.is_del = 0
        AND a.agent_id != 0
        <if test="initSn != null and initSn !=''">
            AND a.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="snOneStatus != null">
            AND a.sn_status = #{snOneStatus,jdbcType=INTEGER}
        </if>
        <if test="snTwoStatus != null">
            AND a.sn_status != #{snTwoStatus,jdbcType=INTEGER}
        </if>
        <if test="agentName != null and agentName != ''">
            AND g.username LIKE CONCAT(#{agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="lightStartTime != null and lightStartTime != '' and lightEndTime != null and lightEndTime != ''">
            AND h.light_time &gt;= #{lightStartTime,jdbcType=VARCHAR}
            AND h.light_time &lt;= #{lightEndTime,jdbcType=VARCHAR}
            AND h.light_status = 1
        </if>
        ) result
    </operation>

    <resultmap name="equipmentListPageMap" type="EquipmentListPageMap">
        <column name="equipment_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="sn_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="init_sn" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="sn_status" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="merchant_name" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="store_name" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="grant_name" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="transaction_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="code_scan_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="face_transaction_num" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="face_transaction_rate" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
        <column name="equipment_dau" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="transaction_money" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
        <column name="light_time" jdbctype="CURRENT_TIMESTAMP" javatype="java.util.Date" remark=""/>
        <column name="light_status" jdbctype="INTEGER" javatype="Integer" remark=""/>
    </resultmap>

    <operation name="getListPageEquipment" resultmap="equipmentListPageMap" multiplicity="many" remark="刷脸设备查看分页列表">
        select
        result.*,
        round(( result.transaction_num - result.code_scan_num )/result.transaction_num,4) face_transaction_rate,
        ( result.transaction_num - result.code_scan_num ) face_transaction_num
        from
        (
        SELECT
        a.equipment_id,
        a.id sn_id,
        a.init_sn,
        a.sn_status,
        e.username merchant_name,
        f.store_name,
        g.username grant_name,
        h.transaction_num,
        h.code_scan_num,
        h.equipment_dau,
        h.transaction_money,
        h.light_time,
        h.light_status
        FROM
        tp_equipment_sn a
        INNER JOIN tp_equipment b ON b.id = a.equipment_id AND b.equipment_type = 4 AND b.is_del = 0
        <if test="equipmentSetId != null and equipmentSetId != -1">
            INNER JOIN tp_equipment_set c ON c.equipment_id = a.equipment_id
            AND c.id = #{equipmentSetId,jdbcType=INTEGER}
            AND c.start_time &lt; UNIX_TIMESTAMP( NOW( ) )
            AND c.is_del = 0
        </if>
        LEFT JOIN tp_users e ON a.uid = e.id
        LEFT JOIN tp_lifecircle_store f ON a.store_id = f.store_id
        INNER JOIN tp_user g ON a.agent_id = g.id AND g.own_run = 0 AND g.belong = 0 AND g.sub_config_id = 0
        LEFT JOIN tp_face_scan_equipment_record h ON a.init_sn = h.equipment_sn
        WHERE
        a.business_status = 1
        AND a.is_del = 0
        AND a.agent_id != 0
        <if test="initSn != null and initSn !=''">
            AND a.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="snOneStatus != null">
            AND a.sn_status = #{snOneStatus,jdbcType=INTEGER}
        </if>
        <if test="snTwoStatus != null">
            AND a.sn_status != #{snTwoStatus,jdbcType=INTEGER}
        </if>
        <if test="agentName != null and agentName != ''">
            AND g.username LIKE CONCAT(#{agentName,jdbcType=VARCHAR},'%')
        </if>
        <if test="lightStartTime != null and lightStartTime != '' and lightEndTime != null and lightEndTime != ''">
            AND h.light_time &gt;= #{lightStartTime,jdbcType=VARCHAR}
            AND h.light_time &lt;= #{lightEndTime,jdbcType=VARCHAR}
            AND h.light_status = 1
        </if>
        ) result
    </operation>

    <resultmap name="AgentEquipmentListMap" type="AgentEquipmentListMap">
        <column name="equipment_id" jdbctype="INTEGER" javatype="Integer" remark="设备id"/>
        <column name="init_sn" jdbctype="VARCHAR" javatype="String" remark="设备sn"/>
        <column name="sn_status" jdbctype="INTEGER" javatype="Integer" remark="sn状态"/>
        <column name="merchant_name" jdbctype="VARCHAR" javatype="String" remark="商户名称"/>
        <column name="grant_name" jdbctype="VARCHAR" javatype="String" remark="受理商"/>
        <column name="store_name" jdbctype="VARCHAR" javatype="String" remark="门店名称"/>
        <column name="bind_time" jdbctype="INTEGER" javatype="Integer" remark="绑定时间"/>
    </resultmap>

    <operation name="agentEquipmentListQuery" multiplicity="many"
               resultmap="AgentEquipmentListMap" remark="代理商查询设备列表">
        SELECT esn.equipment_id,esn.init_sn,esn.sn_status,tusers.username merchant_name,tuser.username
        grant_name,store.store_name,bind.bind_time
        from tp_equipment_sn esn
        LEFT JOIN tp_user tuser on esn.grant_id = tuser.id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        LEFT JOIN tp_users tusers on tusers.id = esn.uid
        LEFT JOIN tp_equipment_bind bind on esn.id = bind.sn_id and bind.bind_status = 1 and bind.is_close = 0
        where
        esn.agent_id = #{agentId,jdbcType=INTEGER}
        <if test="bindStartTime != null and bindEndTime != null">
            and bind.bind_time BETWEEN #{bindStartTime,jdbcType=INTEGER} AND #{bindEndTime,jdbcType=INTEGER}
        </if>
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="snStatus != null and snStatus == 5">
            and esn.sn_status = #{snStatus,jdbcType=INTEGER}
        </if>
        <if test="snStatus != null and snStatus == 0">
            and esn.sn_status not in (1,3,5)
        </if>
        <if test="snStatus != null and snStatus == -1">
            and esn.sn_status not in (1,3)
        </if>
        <if test="grantId != null and grantId != -1">
            and esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        and esn.is_del = 0
        order by esn.id desc
    </operation>

    <operation name="agentEquipmentListQueryWithModel" multiplicity="many"
               resultmap="AgentEquipmentListMap" remark="代理商根据设备型号查询设备列表">
        SELECT esn.equipment_id,esn.init_sn,esn.sn_status,tusers.username merchant_name,
        tuser.username grant_name,store.store_name,bind.bind_time
        from tp_equipment_sn esn
        LEFT JOIN tp_user tuser on esn.grant_id = tuser.id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        LEFT JOIN tp_users tusers on tusers.id = esn.uid
        LEFT JOIN tp_equipment_set eset on eset.equipment_id = esn.equipment_id
        LEFT JOIN tp_equipment_bind bind on esn.id = bind.sn_id and bind.bind_status = 1 and bind.is_close = 0
        where
        esn.agent_id = #{agentId,jdbcType=INTEGER}
        <if test="bindStartTime != null and bindEndTime != null">
            and bind.bind_time BETWEEN #{bindStartTime,jdbcType=INTEGER} AND #{bindEndTime,jdbcType=INTEGER}
        </if>
        <if test="equipmentSn != null and equipmentSn !=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="snStatus != null and snStatus == 5">
            and esn.sn_status = #{snStatus,jdbcType=INTEGER}
        </if>
        <if test="snStatus != null and snStatus == 0">
            and esn.sn_status not in (1,3,5)
        </if>
        <if test="snStatus != null and snStatus == -1">
            and esn.sn_status not in (1,3)
        </if>
        <if test="grantId != null and grantId != -1">
            and esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        and eset.id in
        <foreach item="id" index="index" collection="list" open="(" separator="," close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
        and esn.is_del = 0
        order by esn.id desc
    </operation>

    <operation name="getByInitSnList" paramtype="primitive" multiplicity="many" remark="根据initSn列表查询">
        SELECT * FROM tp_equipment_sn
        WHERE init_sn in
        <foreach collection="list" open="(" close=")" item="sn" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        and is_del = 0
    </operation>
</table>
