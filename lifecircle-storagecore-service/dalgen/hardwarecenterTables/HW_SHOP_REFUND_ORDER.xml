<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_REFUND_ORDER" physicalName="HW_SHOP_REFUND_ORDER"
       remark="硬件商城退货订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_REFUND_ORDER">
        INSERT INTO HW_SHOP_REFUND_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="hwRefundOrderSn != null">`HW_REFUND_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="refundType != null">`REFUND_TYPE`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="refundPayTime != null">`REFUND_PAY_TIME`,</if>
            <if test="refundOrderStatus != null">`REFUND_ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finalGoodsSumprice != null">`FINAL_GOODS_SUMPRICE`,</if>
            <if test="refundBalancePrice != null">`REFUND_BALANCE_PRICE`,</if>
            <if test="refundPaymentPrice != null">`REFUND_PAYMENT_PRICE`,</if>
            <if test="refundGoodsSumprice != null">`REFUND_GOODS_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="hwRefundOrderSn != null">#{hwRefundOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="refundType != null">#{refundType,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="refundPayTime != null">#{refundPayTime,jdbcType=INTEGER},</if>
            <if test="refundOrderStatus != null">#{refundOrderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finalGoodsSumprice != null">#{finalGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="refundBalancePrice != null">#{refundBalancePrice,jdbcType=DECIMAL},</if>
            <if test="refundPaymentPrice != null">#{refundPaymentPrice,jdbcType=DECIMAL},</if>
            <if test="refundGoodsSumprice != null">#{refundGoodsSumprice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findRefundOrderPage" multiplicity="paging" paramtype="primitive"
               paging="findRefundOrderPage" remark="分页查询退货订单页面">
        SELECT *
        FROM `hw_shop_refund_order`
        <where>
            is_del= 0
            <if test="hwOrderSn != null and hwOrderSn != '' ">
                AND hw_order_sn = #{hwOrderSn, jdbcType=VARCHAR}
            </if>
            <if test="realName != null and realName != '' ">
                AND real_name = #{realName, jdbcType=VARCHAR}
            </if>
            <if test="refundOrderStatus != null and refundOrderStatus != 0 ">
                AND refund_order_status= #{refundOrderStatus, jdbcType=INTEGER}
            </if>
            <if test="isTest != null">
                AND is_test= #{isTest, jdbcType=INTEGER}
            </if>
            <if test="startDate != null">
                AND `CREATE_TIME` <![CDATA[>=]]> #{startDate,jdbcType=TIMESTAMP}
            </if>
            <if test="endDate != null">
                AND `CREATE_TIME` <![CDATA[<=]]> #{endDate,jdbcType=TIMESTAMP}
            </if>
            <if test="username != null and username != '' ">
                AND username = #{username, jdbcType=VARCHAR}
            </if>
            <if test="hwRefundOrderSnList != null and hwRefundOrderSnList.size() &gt; 0">
                AND hw_refund_order_sn IN
                <foreach collection="hwRefundOrderSnList" open="(" close=")" item="hwRefundOrderSn" separator=",">
                    #{hwRefundOrderSn,jdbcType=INTEGER}
                </foreach>
            </if>
        </where>
        ORDER BY create_time DESC
    </operation>
</table>
