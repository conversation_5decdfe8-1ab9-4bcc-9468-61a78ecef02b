<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_ORDER_SHIPMENTS_RECORD" physicalName="HW_SHOP_ORDER_SHIPMENTS_RECORD"
       remark="硬件商城订单发货记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_ORDER_SHIPMENTS_RECORD">
        INSERT INTO HW_SHOP_ORDER_SHIPMENTS_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="expressNo != null">`EXPRESS_NO`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="expressNo != null">#{expressNo,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findOrderSnListByExpressNo" paramtype="primitive" multiplicity="many" remark="根据物流单号查询订单号列表"
               resulttype="java.lang.String">
        SELECT
        `HW_ORDER_SN`
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        WHERE
        `EXPRESS_NO` = #{expressNo,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
    </operation>

    <operation name="findExpressNoList" multiplicity="many" paramtype="primitive" remark="批量查询订单发货单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
    </operation>

    <operation name="findListByExpressNo" multiplicity="many" paramtype="primitive" remark="批量查询订单发货单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_SHIPMENTS_RECORD
        WHERE
        `EXPRESS_NO` = #{expressNo,jdbcType=VARCHAR}
        AND IS_DEL = 0
    </operation>
</table>
