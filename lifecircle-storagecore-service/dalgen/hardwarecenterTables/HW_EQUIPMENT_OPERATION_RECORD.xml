<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_OPERATION_RECORD" physicalName="HW_EQUIPMENT_OPERATION_RECORD"
       remark="硬件系统操作日志表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_OPERATION_RECORD">
        INSERT INTO HW_EQUIPMENT_OPERATION_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="operateContent != null">`OPERATE_CONTENT`,</if>
            <if test="operateModule != null">`OPERATE_MODULE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="operateContent != null">#{operateContent,jdbcType=VARCHAR},</if>
            <if test="operateModule != null">#{operateModule,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" multiplicity="one"
               remark="insertBatch:HW_EQUIPMENT_OPERATION_RECORD">
        INSERT INTO HW_EQUIPMENT_OPERATION_RECORD
        (operate_module, operate_content, creater, job_number, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.operateModule, jdbcType=TINYINT},
            #{item.operateContent, jdbcType=VARCHAR},
            #{item.creater, jdbcType=VARCHAR},
            #{item.jobNumber, jdbcType=VARCHAR},
            #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </operation>
</table>
