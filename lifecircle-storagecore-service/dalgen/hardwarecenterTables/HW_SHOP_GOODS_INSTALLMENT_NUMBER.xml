<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_GOODS_INSTALLMENT_NUMBER" physicalName="HW_SHOP_GOODS_INSTALLMENT_NUMBER"
       remark="硬件商城pos分期期数表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_INSTALLMENT_NUMBER">
        INSERT INTO HW_SHOP_GOODS_INSTALLMENT_NUMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="attachmentUrl != null">`ATTACHMENT_URL`,</if>
            <if test="installmentId != null">`INSTALLMENT_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="repaymentType != null">`REPAYMENT_TYPE`,</if>
            <if test="repaymentStatus != null">`REPAYMENT_STATUS`,</if>
            <if test="realRepaymentTime != null">`REAL_REPAYMENT_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="predictRepaymentDate != null">`PREDICT_REPAYMENT_DATE`,</if>
            <if test="realRepaymentAmount != null">`REAL_REPAYMENT_AMOUNT`,</if>
            <if test="predictRepaymentAmount != null">`PREDICT_REPAYMENT_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="attachmentUrl != null">#{attachmentUrl,jdbcType=VARCHAR},</if>
            <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="repaymentType != null">#{repaymentType,jdbcType=TINYINT},</if>
            <if test="repaymentStatus != null">#{repaymentStatus,jdbcType=TINYINT},</if>
            <if test="realRepaymentTime != null">#{realRepaymentTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="predictRepaymentDate != null">#{predictRepaymentDate,jdbcType=TIMESTAMP},</if>
            <if test="realRepaymentAmount != null">#{realRepaymentAmount,jdbcType=DECIMAL},</if>
            <if test="predictRepaymentAmount != null">#{predictRepaymentAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findInstallmentNumberListByRepaymentDate" multiplicity="many" paramtype="primitive"
               remark="根据ID查询分期订单列表">
        SELECT *
        FROM hw_shop_goods_installment_number
        WHERE is_del = 0
        <if test="predictRepaymentStartDate != null">
            AND predict_repayment_date <![CDATA[>=]]> #{predictRepaymentStartDate,jdbcType=TIMESTAMP}
        </if>
        <if test="predictRepaymentEndDate != null">
            AND predict_repayment_date <![CDATA[<=]]> #{predictRepaymentEndDate,jdbcType=TIMESTAMP}
        </if>
    </operation>

    <operation name="findInstallmentNumberListByInstallmentIdList" multiplicity="many" paramtype="primitive"
               remark="根据ID查询分期订单列表">
        SELECT *
        FROM hw_shop_goods_installment_number
        WHERE is_del = 0
        AND installment_id IN
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
