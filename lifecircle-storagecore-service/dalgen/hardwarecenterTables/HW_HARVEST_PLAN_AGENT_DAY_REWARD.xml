<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_HARVEST_PLAN_AGENT_DAY_REWARD" physicalName="HW_HARVEST_PLAN_AGENT_DAY_REWARD"
       remark="丰收计划代理商设备奖励日统计表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_HARVEST_PLAN_AGENT_DAY_REWARD">
        INSERT INTO HW_HARVEST_PLAN_AGENT_DAY_REWARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="statisticsDay != null">`STATISTICS_DAY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="dayAmount != null">`DAY_AMOUNT`,</if>
            <if test="dayReward != null">`DAY_REWARD`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="statisticsDay != null">#{statisticsDay,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="dayAmount != null">#{dayAmount,jdbcType=DECIMAL},</if>
            <if test="dayReward != null">#{dayReward,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findRewardList" multiplicity="paging" paramtype="primitive"
               paging="FindRewardList"
               remark="分页查询代理商日佣金" resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.RewardDTO">
        select
        t1.statistics_day as statistics,
        t1.init_sn as equipmentSn,
        t2.equipment_model as equipmentModel,
        cast(t1.day_amount as char) as tradeAmount,
        cast(t1.day_reward as char) as awardAmount,
        t1.uid as merchantId,
        t1.belong as agentId
        from
        hw_harvest_plan_agent_day_reward t1
        left join
        hw_equipment t2
        on
        t1.equipment_id = t2.id
        where
        t1.statistics_day <![CDATA[>=]]> #{startDay,jdbcType=INTEGER}
        and t1.statistics_day <![CDATA[<=]]> #{endDay,jdbcType=INTEGER}
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="agentIdList != null and agentIdList.size() > 0 ">
            and t1.belong in
            <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                #{belong,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() > 0 ">
            and t1.uid in
            <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        and t1.is_del = 0
        ORDER BY t1.`statistics_day` DESC , t1.`day_amount` DESC
    </operation>
</table>
