<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT" physicalName="HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT"
       remark="丰收计划设备剩余结算金额表（代理商）">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT">
        INSERT INTO HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isStandard != null">`IS_STANDARD`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="remainAmount != null">`REMAIN_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isStandard != null">#{isStandard,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="remainAmount != null">#{remainAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入设备结算余额信息">
        INSERT INTO
        HW_HARVEST_PLAN_AGENT_REMAIN_AMOUNT
        (`INIT_SN`,`REMAIN_AMOUNT`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.initSn,jdbcType=VARCHAR},
            #{item.remainAmount,jdbcType=DECIMAL}
            )
        </foreach>
    </operation>
</table>
