<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_RELATE_AGENT_LOG" physicalName="HW_EQUIPMENT_RELATE_AGENT_LOG"
       remark="硬件关联代理商日志表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_RELATE_AGENT_LOG">
        INSERT INTO HW_EQUIPMENT_RELATE_AGENT_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
            <if test="remark != null">`remark`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="newAgentId != null">`NEW_AGENT_ID`,</if>
            <if test="oldAgentId != null">`OLD_AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="relateType != null">`RELATE_TYPE`,</if>
            <if test="oemBefore != null">`OEM_BEFORE`,</if>
            <if test="oemAfter != null">`OEM_AFTER`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=TINYINT},</if>
            <if test="newAgentId != null">#{newAgentId,jdbcType=INTEGER},</if>
            <if test="oldAgentId != null">#{oldAgentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="relateType != null">#{relateType,jdbcType=TINYINT},</if>
            <if test="oemBefore != null">#{oemBefore,jdbcType=VARCHAR},</if>
            <if test="oemAfter != null">#{oemAfter,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <resultmap name="SearchMapDo" type="RelateLogSearchMap">
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备码"/>
        <column name="old_agent_username" javatype="java.lang.String" jdbctype="VARCHAR" remark="原代理商"/>
        <column name="new_agent_username" javatype="java.lang.String" jdbctype="VARCHAR" remark="新代理商"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="业务类型：1-新增；2-更新"/>
        <column name="creater" javatype="java.lang.String" jdbctype="VARCHAR" remark="操作人"/>
        <column name="remark" javatype="java.lang.String" jdbctype="VARCHAR" remark="备注"/>
        <column name="create_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="创建时间"/>
        <column name="oem_before" javatype="java.lang.String" jdbctype="VARCHAR" remark="关联前oem名称"/>
        <column name="oem_after" javatype="java.lang.String" jdbctype="VARCHAR" remark="关联后oem名称"/>
        <column name="relate_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="关联类型：1 代理商   2 OEM商"/>
    </resultmap>

    <operation name="getListBySearch" paramtype="primitive" multiplicity="many" paging="GetListBySearch"
               resultmap="SearchMapDo" remark="分页查询">
        SELECT relog.equipment_sn,ouser.username as old_agent_username,nuser.username as
        new_agent_username,relog.biz_type,relog.creater,relog.create_time,relog.remark,relog.oem_before,relog.oem_after,relog.relate_type
        from hw_equipment_relate_agent_log relog LEFT JOIN tp_user ouser on ouser.id = relog.old_agent_id
        LEFT JOIN tp_user nuser on nuser.id = relog.new_agent_id
        where 1=1
        <if test="equipmentSn!=null and equipmentSn!=''">
            and relog.equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="bizType!=null and bizType!=-1">
            and relog.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="startTime!=null and endTime!=null">
            and relog.create_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by relog.create_time desc
    </operation>
</table>
