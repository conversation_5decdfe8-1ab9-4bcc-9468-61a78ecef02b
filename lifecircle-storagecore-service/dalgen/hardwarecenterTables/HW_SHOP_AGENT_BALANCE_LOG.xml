<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_AGENT_BALANCE_LOG" physicalName="HW_SHOP_AGENT_BALANCE_LOG"
       remark="硬件商城代理商余额流水明细表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_AGENT_BALANCE_LOG">
        INSERT INTO HW_SHOP_AGENT_BALANCE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="extendInfo != null">`EXTEND_INFO`,</if>
            <if test="agentAccount != null">`AGENT_ACCOUNT`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isMatch != null">`IS_MATCH`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="operateLogId != null">`OPERATE_LOG_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="changeAmount != null">`CHANGE_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="extendInfo != null">#{extendInfo,jdbcType=VARCHAR},</if>
            <if test="agentAccount != null">#{agentAccount,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isMatch != null">#{isMatch,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="operateLogId != null">#{operateLogId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="changeAmount != null">#{changeAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findBalancePageByBelong" multiplicity="paging" paramtype="primitive"
               paging="findBalancePageByBelong" remark="分页查询设备返佣金额关联关系列表">
        SELECT
        *
        FROM
        `hw_shop_agent_balance_log`
        WHERE
        `agent_id` = #{belong, jdbcType=INTEGER}
        ORDER BY
        `update_time` DESC
    </operation>

    <operation name="findBalanceList" multiplicity="paging" paramtype="primitive"
               paging="pageQueryBalanceListParam" remark="分页查询余额列表">
        select * from `hw_shop_agent_balance_log`
        where is_del = 0
        <if test="agentAccount != null and agentAccount != '' ">
            AND agent_account LIKE CONCAT (#{agentAccount,jdbcType=VARCHAR},'%')
        </if>
        <if test="operatorName != null and operatorName != '' ">
            AND operator_name LIKE CONCAT (#{operatorName,jdbcType=VARCHAR},'%')
        </if>
        <if test="operateType != null and operateType != 0 ">
            AND operate_type = #{operateType,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            AND `update_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND `update_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="isTest != null ">
            AND is_test = #{isTest,jdbcType=TINYINT}
        </if>
        ORDER BY `update_time`DESC,id DESC
    </operation>

    <operation name="getBalanceByOperateLogId" multiplicity="one" paramtype="primitive"
               paging="findBalanceByOperateLogId" remark="查询余额详情">
        SELECT
        *
        FROM
        `hw_shop_agent_balance_log`
        WHERE
        `operate_log_id` = #{operateLogId, jdbcType=VARCHAR}
        and is_del = 0
        limit 1
    </operation>
    <operation name="updateAgentInfoByOperateLogId" paramtype="object" multiplicity="one" remark="修改余额流水中代理商信息">
        UPDATE `hw_shop_agent_balance_log`
        <set>
            <if test="agentId != null">
                agent_id = #{agentId, jdbcType=INTEGER},
            </if>
            <if test="agentAccount != null">
                agent_account = #{agentAccount,jdbcType=VARCHAR},
            </if>
            update_time = now(),
            is_match = 0
        </set>
        WHERE
        `operate_log_id` = #{operateLogId, jdbcType=VARCHAR}
        and is_del = 0
    </operation>
</table>
