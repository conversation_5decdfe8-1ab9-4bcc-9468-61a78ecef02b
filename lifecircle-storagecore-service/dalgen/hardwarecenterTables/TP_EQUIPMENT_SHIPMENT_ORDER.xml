<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_SHIPMENT_ORDER" physicalName="TP_EQUIPMENT_SHIPMENT_ORDER"
       remark="(总后台出库/代理商入库)硬件物流订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT_SHIPMENT_ORDER">
        INSERT INTO TP_EQUIPMENT_SHIPMENT_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="orderNum != null">`ORDER_NUM`,</if>
            <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="examinTime != null">`EXAMIN_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="shipmentType != null">`SHIPMENT_TYPE`,</if>
            <if test="depotLocation != null">`DEPOT_LOCATION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="examinTime != null">#{examinTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="shipmentType != null">#{shipmentType,jdbcType=TINYINT},</if>
            <if test="depotLocation != null">#{depotLocation,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <resultmap name="AgentInStorageOrderDetailMap" type="AgentInStorageOrderDetailMap">
        <column name="order_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="订单号"/>
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备id"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="note" javatype="java.lang.String" jdbctype="VARCHAR" remark="备注（回库类型）"/>
    </resultmap>

    <operation name="agentInStorageOrderDetailQuery" multiplicity="many"
               resultmap="AgentInStorageOrderDetailMap" remark="代理商查询入库订单详情">
        SELECT
        shipment.order_sn,shipment.create_time,shipment.order_num,esn.init_sn,esn.equipment_id,ename.equipment_name
        from tp_equipment_shipment_order shipment
        LEFT JOIN tp_equipment_order_relation relation on shipment.id = relation.order_id
        LEFT JOIN tp_equipment_sn esn on esn.id = relation.sn_id
        LEFT JOIN tp_equipment e on e.id = esn.equipment_id
        LEFT JOIN tp_equipment_name ename on ename.id = e.equipment_name_id
        where
        shipment.agent_id = #{agentId,jdbcType=INTEGER}
        and shipment.order_sn = #{orderSn,jdbcType=VARCHAR}
        and relation.order_type = 3
        and relation.is_del = 0 and esn.is_del = 0
        order by esn.init_sn
    </operation>

    <operation name="agentBackStorageOrderDetailQuery" multiplicity="many"
               resultmap="AgentInStorageOrderDetailMap" remark="代理商查询回库订单详情">
        SELECT
        purchase.order_sn,purchase.create_time,purchase.order_num,purchase.note,esn.init_sn,esn.equipment_id,ename.equipment_name
        from tp_equipment_purchase_order purchase
        LEFT JOIN tp_equipment_order_relation relation on relation.order_id = purchase.id
        LEFT JOIN tp_equipment_sn esn on esn.id = relation.sn_id
        LEFT JOIN tp_equipment e on e.id = esn.equipment_id
        LEFT JOIN tp_equipment_name ename on ename.id = e.equipment_name_id
        where
        purchase.agent_id = #{agentId,jdbcType=INTEGER} and purchase.purchase_type = 2
        and purchase.order_sn = #{orderSn,jdbcType=VARCHAR}
        and relation.order_type = 2
        and relation.is_del = 0 and esn.is_del = 0
        order by esn.init_sn
    </operation>

</table>
