<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SCAN_HAND_EQUIPMENT_BIND_RELATION" physicalName="HW_SCAN_HAND_EQUIPMENT_BIND_RELATION"
       remark="商户刷掌设备关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SCAN_HAND_EQUIPMENT_BIND_RELATION">
        INSERT INTO HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    
    <operation name="batchAdd" paramtype="objectList" remark="batchInsert:HW_SCAN_HAND_EQUIPMENT_BIND_RELATION">
        INSERT INTO HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        (
        INIT_SN,
        OPERATOR,
        USERNAME,
        AGENT_USERNAME,
        SALESMAN_USERNAME,
        UID,
        AGENT_ID,
        SALESMAN_ID
        )
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.initSn,jdbcType=VARCHAR},
            #{item.operator,jdbcType=VARCHAR},
            #{item.username,jdbcType=VARCHAR},
            #{item.agentUsername,jdbcType=VARCHAR},
            #{item.salesmanUsername,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.agentId,jdbcType=INTEGER},
            #{item.salesmanId,jdbcType=INTEGER}
            )
        </foreach>
    </operation>

    <operation name="getByInitSn" multiplicity="one" remark="getByInitSn">
        select *
        from HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        where init_sn = #{initSn,jdbcType=VARCHAR}
        and is_del = 0
        limit 1;
    </operation>

    <operation name="findScanHandEquipmentList" multiplicity="paging" paramtype="primitive"
               paging="findScanHandEquipmentList" remark="分页查询刷掌设备关联关系列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.ScanHandEquipmentDTO">
        select
        init_sn as initSn,
        uid,
        username,
        operator,
        company,
        agent_id as agentId,
        salesman_id as salesmanId,
        agent_username as agentUsername,
        salesman_username as salesmanUsername,
        create_time as createTime
        from HW_SCAN_HAND_EQUIPMENT_BIND_RELATION
        where
        1=1
        <if test="initSn != null and initSn != ''">
            and init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="uid != null">
            and uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="agentId != null">
            and AGENT_ID = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="grantId != null">
            and SALESMAN_ID = #{grantId,jdbcType=INTEGER}
        </if>
        <if test="startTime != null and endTime != null">
            AND CREATE_TIME <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
            AND CREATE_TIME <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by create_time desc
    </operation>
</table>
