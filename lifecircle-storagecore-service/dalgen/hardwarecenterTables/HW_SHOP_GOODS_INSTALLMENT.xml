<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_GOODS_INSTALLMENT" physicalName="HW_SHOP_GOODS_INSTALLMENT"
       remark="硬件商城商品分期主表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_INSTALLMENT">
        INSERT INTO HW_SHOP_GOODS_INSTALLMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="agentName != null">`AGENT_NAME`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="orderAccount != null">`ORDER_ACCOUNT`,</if>
            <if test="installmentId != null">`INSTALLMENT_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="orderAccountId != null">`ORDER_ACCOUNT_ID`,</if>
            <if test="repaymentStatus != null">`REPAYMENT_STATUS`,</if>
            <if test="goodsInstallment != null">`GOODS_INSTALLMENT`,</if>
            <if test="goodsTotalNumber != null">`GOODS_TOTAL_NUMBER`,</if>
            <if test="installmentEndDate != null">`INSTALLMENT_END_DATE`,</if>
            <if test="installmentStartDate != null">`INSTALLMENT_START_DATE`,</if>
            <if test="remainingInstallment != null">`REMAINING_INSTALLMENT`,</if>
            <if test="orderTime != null">`ORDER_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="remainingAmount != null">`REMAINING_AMOUNT`,</if>
            <if test="outstandingAmount != null">`OUTSTANDING_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="agentName != null">#{agentName,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="orderAccount != null">#{orderAccount,jdbcType=VARCHAR},</if>
            <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="orderAccountId != null">#{orderAccountId,jdbcType=INTEGER},</if>
            <if test="repaymentStatus != null">#{repaymentStatus,jdbcType=TINYINT},</if>
            <if test="goodsInstallment != null">#{goodsInstallment,jdbcType=INTEGER},</if>
            <if test="goodsTotalNumber != null">#{goodsTotalNumber,jdbcType=INTEGER},</if>
            <if test="installmentEndDate != null">#{installmentEndDate,jdbcType=INTEGER},</if>
            <if test="installmentStartDate != null">#{installmentStartDate,jdbcType=INTEGER},</if>
            <if test="remainingInstallment != null">#{remainingInstallment,jdbcType=INTEGER},</if>
            <if test="orderTime != null">#{orderTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="remainingAmount != null">#{remainingAmount,jdbcType=DECIMAL},</if>
            <if test="outstandingAmount != null">#{outstandingAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findInstallmentPage" multiplicity="paging" paging="findInstallment" paramtype="primitive"
               remark="根据条件查询分期列表">
        SELECT *
        FROM hw_shop_goods_installment
        WHERE is_del = 0
        <if test="orderNo != null">
            AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="orderAccountId != null">
            AND order_account_id = #{orderAccountId,jdbcType=INTEGER}
        </if>
        <if test="orderAccount != null">
            AND order_account LIKE CONCAT (#{orderAccount,jdbcType=VARCHAR}, '%')
        </if>
        <if test="installmentIdList != null and installmentIdList.size() &gt; 0 ">
            AND installment_id IN
            <foreach collection="installmentIdList" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="repaymentStatus != null">
            AND repayment_status = #{repaymentStatus,jdbcType=TINYINT}
        </if>
        <if test="installmentType != null">
            AND installment_type = #{installmentType,jdbcType=TINYINT}
        </if>
        <if test="finishType != null">
            AND finish_type = #{finishType,jdbcType=TINYINT}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="orderEndTime != null">
            AND order_time <![CDATA[<=]]> #{orderEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="installmentStartDate != null">
            AND installment_start_date <![CDATA[>=]]> #{installmentStartDate,jdbcType=INTEGER}
        </if>
        <if test="installmentEndDate != null">
            AND installment_end_date <![CDATA[<=]]> #{installmentEndDate,jdbcType=INTEGER}
        </if>
        <if test="orderStartTime != null">
            AND order_time <![CDATA[>=]]> #{orderStartTime,jdbcType=TIMESTAMP}
        </if>
    </operation>
</table>
