<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_FACE_SCAN_EQUIPMENT_RECORD" physicalName="TP_FACE_SCAN_EQUIPMENT_RECORD"
    remark="扫脸设备消费记录总数据表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_FACE_SCAN_EQUIPMENT_RECORD">
INSERT INTO TP_FACE_SCAN_EQUIPMENT_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="codeScanNum != null">`CODE_SCAN_NUM`,</if>
        <if test="lightStatus != null">`LIGHT_STATUS`,</if>
        <if test="equipmentDau != null">`EQUIPMENT_DAU`,</if>
        <if test="faceScanDau != null">`FACE_SCAN_DAU`,</if>
        <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
        <if test="transactionNum != null">`TRANSACTION_NUM`,</if>
        <if test="lightTime != null">`LIGHT_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="activityTime != null">`ACTIVITY_TIME`,</if>
        <if test="transactionMoney != null">`TRANSACTION_MONEY`,</if>
        <if test="alipayLightTime != null">`ALIPAY_LIGHT_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="codeScanNum != null">#{codeScanNum,jdbcType=INTEGER},</if>
        <if test="lightStatus != null">#{lightStatus,jdbcType=TINYINT},</if>
        <if test="equipmentDau != null">#{equipmentDau,jdbcType=INTEGER},</if>
        <if test="faceScanDau != null">#{faceScanDau,jdbcType=INTEGER},</if>
        <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
        <if test="transactionNum != null">#{transactionNum,jdbcType=INTEGER},</if>
        <if test="lightTime != null">#{lightTime,jdbcType=TIMESTAMP},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="activityTime != null">#{activityTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionMoney != null">#{transactionMoney,jdbcType=DECIMAL},</if>
        <if test="alipayLightTime != null">#{alipayLightTime,jdbcType=INTEGER},</if>
    </trim>
    </operation>
    
    <resultmap name="ScanFaceRecordDo" type="ScanFaceRecordDO">
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备初始sn码"/>
        <column name="agent_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="所属代理商的id"/>
        <column name="equipment_dau" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备DAU"/>
        <column name="face_scan_dau" javatype="java.lang.Integer" jdbctype="INTEGER" remark="刷脸DAU"/>
        <column name="transaction_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="交易总笔数"/>
        <column name="transaction_money" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="交易总金额"/>
        <column name="code_scan_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="扫码总笔数"/>
        <column name="light_status" javatype="java.lang.Integer" jdbctype="INTEGER" remark="是否点亮： 1是 0否"/>
        <column name="light_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="点亮时间"/>
        <column name="create_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="创建时间"/>
    </resultmap>
    
    <operation name="getByEquipmentSn" paramtype="primitive" resultmap="ScanFaceRecordDo" remark="根据设备sn码查询">
        select
        equipment_sn,agent_id,equipment_dau,face_scan_dau,transaction_num,transaction_money,code_scan_num,light_status,light_time,create_time
        from tp_face_scan_equipment_record where equipment_sn = #{equipmentSn,jdbcType=VARCHAR} limit 1
    </operation>

    <operation name="update" paramtype="object" remark="更新数据">
        update TP_FACE_SCAN_EQUIPMENT_RECORD
        SET
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="agentId != null">agent_id = #{agentId,jdbcType=INTEGER},</if>
            <if test="codeScanNum != null">code_scan_num = #{codeScanNum,jdbcType=INTEGER},</if>
            <if test="lightStatus != null">light_status = #{lightStatus,jdbcType=TINYINT},</if>
            <if test="equipmentDau != null">equipment_dau = #{equipmentDau,jdbcType=INTEGER},</if>
            <if test="faceScanDau != null">face_scan_dau = #{faceScanDau,jdbcType=INTEGER},</if>
            <if test="transactionNum != null">transaction_num = #{transactionNum,jdbcType=INTEGER},</if>
            <if test="lightTime != null">light_time = #{lightTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">create_time = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="transactionMoney != null">transaction_money = #{transactionMoney,jdbcType=DECIMAL},</if>
            <if test="alipayLightTime != null">alipay_light_time = #{alipayLightTime,jdbcType=INTEGER},</if>
        </trim>
        WHERE
        equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
    </operation>

    <resultmap name="ScanFaceEquipmentMap"
               type="ScanFaceEquipmentMap">
        <column name="real_name" jdbctype="VARCHAR" javatype="String" remark="代理商姓名"/>
        <column name="agent_name" jdbctype="VARCHAR" javatype="String" remark="代理商名称"/>
        <column name="agent_id" jdbctype="INTEGER" javatype="Integer" remark="代理商id"/>
        <column name="own_run" jdbctype="INTEGER" javatype="Integer" remark="代理商类型"/>
        <column name="code_scan_num" jdbctype="INTEGER" javatype="Integer" remark="扫码总笔数"/>
        <column name="light_num" jdbctype="INTEGER" javatype="Integer" remark="点亮数量"/>
        <column name="equipment_dau" jdbctype="INTEGER" javatype="Integer" remark="设备总DAU"/>
        <column name="equipment_num" jdbctype="INTEGER" javatype="Integer" remark="设备总数"/>
        <column name="transaction_num" jdbctype="INTEGER" javatype="Integer" remark="设备交易总笔数"/>
        <column name="transaction_money" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark="设备交易总金额"/>
    </resultmap>

    <operation name="getScanFaceListExport" resultmap="ScanFaceEquipmentMap" multiplicity="many" remark="导出代理商扫脸设备信息">
        SELECT ue.username as agent_name,ue.contacts as
        real_name,sn.agent_id,
        count(*) as equipment_num,sum(light_status)as light_num,
        sum(equipment_dau) as equipment_dau,sum(transaction_num) as transaction_num,
        sum(transaction_money) as transaction_money,sum(code_scan_num) as code_scan_num
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.agent_id
        LEFT JOIN tp_face_scan_equipment_record face on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        where eq.equipment_type = 4
        and sn.agent_id !=0
        and ue.is_salesman = 0
        and ue.sub_config_id = 0
        and sn.is_del = 0
        <if test="ownRun !=null">
            and ue.own_run = #{ownRun,jdbcType = INTEGER}
        </if>
        <if test="agentId !=null">
            and ue.id = #{agentId,jdbcType = INTEGER}
        </if>
        GROUP BY sn.agent_id
        ORDER BY ue.create_time DESC
    </operation>

    <resultmap name="ScanFaceEquipmentDetailMap" type="ScanFaceEquipmentDetailMap">
        <column name="light_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="点亮时间"/>
        <column name="grant_name" jdbctype="VARCHAR" javatype="String" remark="授理商名称"/>
        <column name="id" jdbctype="INTEGER" javatype="Integer" remark="sn码id"/>
        <column name="bind_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="绑定时间"/>
        <column name="activity_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="活动报名时间"/>
        <column name="system_sn" jdbctype="VARCHAR" javatype="String" remark="sn码"/>
        <column name="store_name" jdbctype="VARCHAR" javatype="String" remark="门店名称"/>
        <column name="merchant_name" jdbctype="VARCHAR" javatype="String" remark="商户名称"/>
        <column name="sn_status" jdbctype="INTEGER" javatype="Integer" remark="设备sn状态"/>
        <column name="sn_store_id" jdbctype="INTEGER" javatype="Integer" remark="sn归属门店id"/>
        <column name="code_scan_num" jdbctype="INTEGER" javatype="Integer" remark="扫码总笔数"/>
        <column name="equipment_id" jdbctype="INTEGER" javatype="Integer" remark="设备id"/>
        <column name="face_store_id" jdbctype="INTEGER" javatype="Integer" remark="扫码门店id"/>
        <column name="light_status" jdbctype="INTEGER" javatype="Integer" remark="点亮状态"/>
        <column name="equipment_dau" jdbctype="INTEGER" javatype="Integer" remark="设备DAU"/>
        <column name="activity_status" jdbctype="INTEGER" javatype="Integer" remark="活动状态"/>
        <column name="transaction_num" jdbctype="INTEGER" javatype="Integer" remark="交易总笔数"/>
        <column name="transaction_money" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark="交易总金额"/>
    </resultmap>

    <operation name="getScanFaceDetailListExport" resultmap="ScanFaceEquipmentDetailMap" multiplicity="many"
               remark="导出代理商扫脸设备详情信息">
        SELECT face.activity_status,
        face.activity_time,
        sn.id,sn.bind_time,sn.unbind_time,
        ue.username as grant_name,
        sn.equipment_id,
        sn.init_sn as
        system_sn,
        sn.store_id as sn_store_id,
        sn.sn_status,
        us.username as
        merchant_name,
        store.store_name,
        face.equipment_dau,
        face.transaction_num,
        face.transaction_money,
        face.code_scan_num,
        face.light_status,
        face.light_time,
        face.store_id as face_store_id
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face
        on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        LEFT JOIN tp_users us on us.id = sn.uid
        LEFT JOIN tp_lifecircle_store store on store.store_id = sn.store_id
        where eq.equipment_type = 4
        and sn.is_del = 0
        and sn.agent_id = #{agentId,jdbcType = INTEGER}
        group by sn.init_sn
        ORDER BY sn.create_time DESC
    </operation>

    <operation name="getScanFaceDetailByDirectListExport" resultmap="ScanFaceEquipmentDetailMap" multiplicity="many"
               remark="导出直营代理商扫脸设备详情信息">
        SELECT face.activity_status,
        face.activity_time,
        sn.id,sn.bind_time,sn.unbind_time,
        ue.username as grant_name,
        sn.equipment_id,
        sn.init_sn as
        system_sn,
        sn.store_id as sn_store_id,
        sn.sn_status,
        us.username as
        merchant_name,
        store.store_name,
        face.equipment_dau,
        face.transaction_num,
        face.transaction_money,
        face.code_scan_num,
        face.light_status,
        face.light_time,
        face.store_id as face_store_id
        from hw_equipment_sn sn
        LEFT JOIN hw_equipment eq on sn.equipment_id = eq.id
        LEFT JOIN tp_user ue on ue.id = sn.grant_id
        LEFT JOIN tp_face_scan_equipment_record face
        on face.agent_id = sn.agent_id and face.equipment_sn = sn.init_sn
        LEFT JOIN tp_users us on us.id = face.uid
        LEFT JOIN tp_lifecircle_store store on store.store_id = face.store_id
        where eq.equipment_type = 4
        and sn.is_del = 0
        and sn.agent_id = #{agentId,jdbcType = INTEGER}
        group by sn.init_sn
        ORDER BY sn.create_time DESC
    </operation>

    <operation name="changeActivityMerchant" paramtype="primitive">
        UPDATE tp_face_scan_equipment_record SET
        uid = #{merchantId,jdbcType=INTEGER},
        store_id = 0,
        activity_status = 2,
        activity_time = #{activityTime,jdbcType=TIMESTAMP}
        WHERE equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
    </operation>

    <operation name="resetActivityMerchant" paramtype="primitive">
        UPDATE tp_face_scan_equipment_record SET
        uid = 0,
        store_id = 0,
        activity_status = 1
        WHERE equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
    </operation>

    <operation name="findByInitSn" paramtype="primitive" multiplicity="one">
        SELECT
        *
        FROM
        tp_face_scan_equipment_record
        WHERE
        equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
