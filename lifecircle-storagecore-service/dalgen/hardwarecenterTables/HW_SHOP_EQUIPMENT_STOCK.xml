<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_EQUIPMENT_STOCK" physicalName="HW_SHOP_EQUIPMENT_STOCK"
       remark="硬件商城设备库存表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_EQUIPMENT_STOCK">
        INSERT INTO HW_SHOP_EQUIPMENT_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="isTest != null">`IS_TEST`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="hwOrderStock != null">`HW_ORDER_STOCK`,</if>
            <if test="preSaleStock != null">`PRE_SALE_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="isTest != null">#{isTest,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="hwOrderStock != null">#{hwOrderStock,jdbcType=INTEGER},</if>
            <if test="preSaleStock != null">#{preSaleStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findStockPageList" multiplicity="many" paramtype="primitive" remark="分页查询商城硬件库存">
        SELECT
        stock.*
        FROM hw_shop_equipment_stock stock
        left join hw_equipment eq on stock.equipment_id = eq.id
        where stock.is_del = 0
        and stock.is_test = #{isTest, jdbcType=TINYINT}
        <if test="equipmentModel != null and '' != equipmentModel">
            and eq.equipment_model like concat(#{equipmentModel,jdbcType=VARCHAR},'%')
        </if>
        <if test="equipmentName != null and '' != equipmentName">
            and eq.equipment_name like concat(#{equipmentName,jdbcType=VARCHAR},'%')
        </if>
    </operation>
</table>
