<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_ORDER" physicalName="HW_SHOP_ORDER"
       remark="订单主表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_ORDER">
        INSERT INTO HW_SHOP_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="operateNote != null">`OPERATE_NOTE`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="noteImg != null">`NOTE_IMG`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="areaName != null">`AREA_NAME`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="recipient != null">`RECIPIENT`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="transferPictures != null">`TRANSFER_PICTURES`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="hwPayTime != null">`HW_PAY_TIME`,</if>
            <if test="hwPayType != null">`HW_PAY_TYPE`,</if>
            <if test="hwOrderStatus != null">`HW_ORDER_STATUS`,</if>
            <if test="isInstallment != null">`IS_INSTALLMENT`,</if>
            <if test="hwClientOrderStatus != null">`HW_CLIENT_ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balancePrice != null">`BALANCE_PRICE`,</if>
            <if test="paymentPrice != null">`PAYMENT_PRICE`,</if>
            <if test="baseExpressFee != null">`BASE_EXPRESS_FEE`,</if>
            <if test="finalExpressFee != null">`FINAL_EXPRESS_FEE`,</if>
            <if test="baseGoodsSumprice != null">`BASE_GOODS_SUMPRICE`,</if>
            <if test="baseOrderSumprice != null">`BASE_ORDER_SUMPRICE`,</if>
            <if test="finalGoodsSumprice != null">`FINAL_GOODS_SUMPRICE`,</if>
            <if test="finalOrderSumprice != null">`FINAL_ORDER_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="operateNote != null">#{operateNote,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="noteImg != null">#{noteImg,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="areaName != null">#{areaName,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="recipient != null">#{recipient,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="transferPictures != null">#{transferPictures,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="hwPayTime != null">#{hwPayTime,jdbcType=INTEGER},</if>
            <if test="hwPayType != null">#{hwPayType,jdbcType=TINYINT},</if>
            <if test="hwOrderStatus != null">#{hwOrderStatus,jdbcType=TINYINT},</if>
            <if test="isInstallment != null">#{isInstallment,jdbcType=TINYINT},</if>
            <if test="hwClientOrderStatus != null">#{hwClientOrderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balancePrice != null">#{balancePrice,jdbcType=DECIMAL},</if>
            <if test="paymentPrice != null">#{paymentPrice,jdbcType=DECIMAL},</if>
            <if test="baseExpressFee != null">#{baseExpressFee,jdbcType=DECIMAL},</if>
            <if test="finalExpressFee != null">#{finalExpressFee,jdbcType=DECIMAL},</if>
            <if test="baseGoodsSumprice != null">#{baseGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="baseOrderSumprice != null">#{baseOrderSumprice,jdbcType=DECIMAL},</if>
            <if test="finalGoodsSumprice != null">#{finalGoodsSumprice,jdbcType=DECIMAL},</if>
            <if test="finalOrderSumprice != null">#{finalOrderSumprice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findOrderPageList" paging="hwShopOrderList" multiplicity="paging" remark="分页查询硬件商品订单列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.HwShopOrderDetailDTO">
        SELECT
        CREATE_TIME as createTime,
        HW_ORDER_SN as hwOrderSn,
        USERNAME as username,
        USER_TYPE as userType,
        NOTE as note,
        OPERATE_NOTE as operateNote,
        NOTE_IMG as noteImg,
        IS_INSTALLMENT as isInstallment,
        FINAL_GOODS_SUMPRICE as finalGoodsSumprice,
        FINAL_EXPRESS_FEE as finalExpressFee,
        FINAL_ORDER_SUMPRICE as finalOrderSumprice,
        BALANCE_PRICE as balancePrice,
        PAYMENT_PRICE as paymentPrice,
        HW_PAY_TYPE as hwPayType,
        HW_ORDER_STATUS as hwOrderStatus,
        RECIPIENT as recipient,
        PHONE as phone,
        PROVINCE_NAME as provinceName,
        CITY_NAME as cityName,
        AREA_NAME as areaName,
        ADDRESS as address,
        ORDER_SN as orderSn,
        FINAL_SCORE as finalScore
        FROM
        HW_SHOP_ORDER
        WHERE
        `IS_DEL` = 0
        <if test="hwOrderSn != null and hwOrderSn != '' ">
            AND `HW_ORDER_SN` = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="list != null and list.size() &gt; 0">
            AND `HW_ORDER_SN` IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="username != null and username != '' ">
            AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="isInstallment != null">
            AND `IS_INSTALLMENT` = #{isInstallment,jdbcType=TINYINT}
        </if>
        <if test="userType != null">
            <choose>
                <when test="userType == 1">
                    AND `USER_TYPE` in (1,2)
                </when>
                <otherwise>
                    AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
                </otherwise>
            </choose>
        </if>
        <if test="hwPayType != null">
            AND `HW_PAY_TYPE` = #{hwPayType,jdbcType=TINYINT}
        </if>
        <if test="hwOrderStatus != null">
            AND `HW_ORDER_STATUS` = #{hwOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="hwClientOrderStatus != null">
            AND `HW_CLIENT_ORDER_STATUS` = #{hwClientOrderStatus,jdbcType=TINYINT}
        </if>
        <if test="startDate != null">
            AND `CREATE_TIME` <![CDATA[ >= ]]> #{startDate,jdbcType=TIMESTAMP}
        </if>
        <if test="endDate != null">
            AND `CREATE_TIME` <![CDATA[ <= ]]> #{endDate,jdbcType=TIMESTAMP}
        </if>
        <if test="isTest != null">
            AND `IS_TEST` = #{isTest, jdbcType=INTEGER}
        </if>
        ORDER BY `ID` DESC
    </operation>

    <operation name="findOrderListByHwOrderSn" paramtype="primitive" multiplicity="many" remark="根据订单查询订单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER
        <where>
            <if test="list != null and list.size() &gt; 0 ">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="item" open="(" close=")" separator=",">
                    #{item,jdbcType=VARCHAR}
                </foreach>
            </if>
        </where>
    </operation>

    <operation name="getOrderFullInfo" paramtype="primitive" multiplicity="one" remark="查询订单完整信息">
        SELECT
        *
        FROM
        HW_SHOP_ORDER
        WHERE
        `HW_ORDER_SN` = #{hwOrderSn,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
    </operation>

    <operation name="getLastBySnId" paramtype="primitive" multiplicity="one" remark="根据snId查询订单信息">
        SELECT od.* FROM hw_shop_order od
        INNER JOIN  hw_shop_order_storage_record record on record.hw_order_sn = od.hw_order_sn
        INNER JOIN hw_equipment_order_relation rela on rela.order_no = record.storage_order
        WHERE rela.sn_id = #{snId,jdbcType=INTEGER}
        AND od.IS_DEL = 0
        order by od.id desc
        limit 1
    </operation>
</table>
