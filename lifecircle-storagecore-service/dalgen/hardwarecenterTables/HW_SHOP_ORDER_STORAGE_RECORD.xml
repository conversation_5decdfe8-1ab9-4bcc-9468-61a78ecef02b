<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_ORDER_STORAGE_RECORD" physicalName="HW_SHOP_ORDER_STORAGE_RECORD"
       remark="硬件商城订单设备出(入)库记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_ORDER_STORAGE_RECORD">
        INSERT INTO HW_SHOP_ORDER_STORAGE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="direction != null">`DIRECTION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="direction != null">#{direction,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByStorageOrder" paramtype="primitive" multiplicity="one" remark="根据出库单号查询订单号"
               resulttype="java.lang.String">
        SELECT
        `HW_ORDER_SN`
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        WHERE
        `STORAGE_ORDER` = #{storageOrder,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
    </operation>

    <operation name="findStorageOrderList" multiplicity="many" paramtype="primitive" remark="批量查询订单出库单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
    </operation>

    <operation name="findListByStorage" paramtype="primitive" multiplicity="many" remark="根据出入库订单号查询订单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        WHERE
        `storage_order` = #{storageOrder,jdbcType=VARCHAR}
        AND `IS_DEL` = 0
    </operation>

    <operation name="findStorageOrderDOList" multiplicity="many" paramtype="primitive" remark="批量查询订单出库单列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_STORAGE_RECORD
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
    </operation>
</table>
