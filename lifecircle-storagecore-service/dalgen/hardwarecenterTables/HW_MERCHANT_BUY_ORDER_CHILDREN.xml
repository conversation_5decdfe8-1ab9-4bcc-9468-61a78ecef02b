<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_MERCHANT_BUY_ORDER_CHILDREN" physicalName="HW_MERCHANT_BUY_ORDER_CHILDREN"
       remark="商户购买设备订单子表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_MERCHANT_BUY_ORDER_CHILDREN">
        INSERT INTO HW_MERCHANT_BUY_ORDER_CHILDREN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="fbOrderSn != null">`FB_ORDER_SN`,</if>
            <if test="newInitSn != null">`NEW_INIT_SN`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="initSnHistory != null">`INIT_SN_HISTORY`,</if>
            <if test="childrenOrderSn != null">`CHILDREN_ORDER_SN`,</if>
            <if test="alipayAuditRemark != null">`ALIPAY_AUDIT_REMARK`,</if>
            <if test="alipayActivityInfoUrl != null">`ALIPAY_ACTIVITY_INFO_URL`,</if>
            <if test="alipaSalesEntryOrderId != null">`ALIPA_SALES_ENTRY_ORDER_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="isRefund != null">`IS_REFUND`,</if>
            <if test="workerId != null">`WORKER_ID`,</if>
            <if test="salesType != null">`SALES_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="confirmOrder != null">`CONFIRM_ORDER`,</if>
            <if test="sesameGoType != null">`SESAME_GO_TYPE`,</if>
            <if test="recycleStatus != null">`RECYCLE_STATUS`,</if>
            <if test="periodGenerate != null">`PERIOD_GENERATE`,</if>
            <if test="sesameGoStatus != null">`SESAME_GO_STATUS`,</if>
            <if test="workerParentId != null">`WORKER_PARENT_ID`,</if>
            <if test="alipayAuditStatus != null">`ALIPAY_AUDIT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="fbOrderSn != null">#{fbOrderSn,jdbcType=VARCHAR},</if>
            <if test="newInitSn != null">#{newInitSn,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="initSnHistory != null">#{initSnHistory,jdbcType=VARCHAR},</if>
            <if test="childrenOrderSn != null">#{childrenOrderSn,jdbcType=VARCHAR},</if>
            <if test="alipayAuditRemark != null">#{alipayAuditRemark,jdbcType=VARCHAR},</if>
            <if test="alipayActivityInfoUrl != null">#{alipayActivityInfoUrl,jdbcType=VARCHAR},</if>
            <if test="alipaSalesEntryOrderId != null">#{alipaSalesEntryOrderId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="isRefund != null">#{isRefund,jdbcType=TINYINT},</if>
            <if test="workerId != null">#{workerId,jdbcType=INTEGER},</if>
            <if test="salesType != null">#{salesType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="confirmOrder != null">#{confirmOrder,jdbcType=TINYINT},</if>
            <if test="sesameGoType != null">#{sesameGoType,jdbcType=TINYINT},</if>
            <if test="recycleStatus != null">#{recycleStatus,jdbcType=TINYINT},</if>
            <if test="periodGenerate != null">#{periodGenerate,jdbcType=TINYINT},</if>
            <if test="sesameGoStatus != null">#{sesameGoStatus,jdbcType=TINYINT},</if>
            <if test="workerParentId != null">#{workerParentId,jdbcType=INTEGER},</if>
            <if test="alipayAuditStatus != null">#{alipayAuditStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="selectLatestNotCloseOrderByNewSn">
        select * from hw_merchant_buy_order_children
        where new_init_sn = #{sn,jdbcType=VARCHAR}
        and order_status != 2
        and is_refund = 0
        and recycle_status = 0
        order by id desc limit 1
    </operation>
</table>
