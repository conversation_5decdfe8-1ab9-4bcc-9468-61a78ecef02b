<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_WORK_ORDER_LOG" physicalName="HW_EQUIPMENT_WORK_ORDER_LOG"
       remark="设备工单日志表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_WORK_ORDER_LOG">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_LOG( `INIT_SN`, `REMARK`, `OPERATOR`, `WORK_ORDER_SN`, `OPERATOR_ID`)
        values
        <foreach collection="list" item="log" separator=",">
            (
            #{log.initSn,jdbcType=VARCHAR},
            #{log.remark,jdbcType=VARCHAR},
            #{log.operator,jdbcType=VARCHAR},
            #{log.workOrderSn,jdbcType=VARCHAR},
            #{log.operatorId,jdbcType=VARCHAR}
            )
        </foreach>

    </operation>
</table>
