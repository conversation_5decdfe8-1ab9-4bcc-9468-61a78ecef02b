<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_HARVEST_PLAN_AGENT_BLACK" physicalName="HW_HARVEST_PLAN_AGENT_BLACK"
       remark="丰收计划代理商黑名单">

    <operation name="insert" paramtype="object" remark="insert:HW_HARVEST_PLAN_AGENT_WHITE">
        INSERT INTO HW_HARVEST_PLAN_AGENT_BLACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                agent_id,
            </if>
            <if test="activityId != null">
                activity_id,
            </if>
            <if test="isClose != null">
                is_close,
            </if>
            <if test="createTime != null">
                create_time,
            </if>
            <if test="updateTime != null">
                update_time,
            </if>
            <if test="jobNumber != null">
                job_number,
            </if>
            <if test="operatorName != null and operatorName != ''">
                operator_name,
            </if>
            <if test="remark != null and remark != ''">
                remark
            </if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="agentId != null">
                #{agentId, jdbcType=INTEGER},
            </if>
            <if test="activityId != null">
                #{activityId, jdbcType=INTEGER},
            </if>
            <if test="isClose != null">
                #{isClose, jdbcType=TINYINT},
            </if>
            <if test="createTime != null">
                #{createTime, jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                #{updateTime, jdbcType=TIMESTAMP},
            </if>
            <if test="jobNumber != null">
                #{jobNumber, jdbcType=INTEGER},
            </if>
            <if test="operatorName != null and operatorName != ''">
                #{operatorName, jdbcType=VARCHAR},
            </if>
            <if test="remark != null and remark != ''">
                #{remark, jdbcType=VARCHAR}
            </if>
        </trim>
    </operation>

    <operation name="getOneByAgentIdAndActivityId" remark="根据agentId和activityId查询" paramtype="primitive"
               multiplicity="one">
        select * from HW_HARVEST_PLAN_AGENT_BLACK
        where AGENT_ID = #{agentId, jdbcType=INTEGER}
        and ACTIVITY_ID = #{activityId, jdbcType=INTEGER}
        and IS_CLOSE = 0 LIMIT 1
    </operation>


    <operation name="insertBatch" multiplicity="one" paramtype="objectList" remark="批量插入白名单数据">
        INSERT INTO HW_HARVEST_PLAN_AGENT_BLACK
        (agent_id, activity_id, job_number, operator_name, remark)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.agentId, jdbcType=INTEGER},
            #{item.activityId, jdbcType=INTEGER},
            #{item.JobNumber, jdbcType=INTEGER},
            #{item.operatorName, jdbcType=VARCHAR},
            #{item.remark, jdbcType=VARCHAR})
        </foreach>
    </operation>
</table>
