<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_GOODS_INSTALLMENT_SN" physicalName="HW_SHOP_GOODS_INSTALLMENT_SN"
       remark="硬件商城pos分期SN">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_INSTALLMENT_SN">
        INSERT INTO HW_SHOP_GOODS_INSTALLMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
            <if test="installmentId != null">`INSTALLMENT_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
            <if test="installmentId != null">#{installmentId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getInstallmentIdBySn" multiplicity="one" paramtype="primitive" remark="根据SN查询分期ID">
        SELECT *
        FROM hw_shop_goods_installment_sn
        WHERE is_del = 0
        AND equipment_sn = #{equipmentSn, jdbcType=VARCHAR}
    </operation>

    <operation name="findInstallmentIdByInstallmentIdList" multiplicity="many" paramtype="primitive"
               remark="根据分期ID查询sn列表">
        SELECT *
        FROM hw_shop_goods_installment_sn
        WHERE is_del = 0
        <if test="list != null and list.size() &gt; 0 ">
            AND installment_id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
    </operation>

</table>
