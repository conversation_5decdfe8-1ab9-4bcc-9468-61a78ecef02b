<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="HW_SHOP_GOODS_SKU" physicalName="HW_SHOP_GOODS_SKU"
    remark="硬件商城商品SKU表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_SKU">
INSERT INTO HW_SHOP_GOODS_SKU
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
        <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="endNumber != null">`END_NUMBER`,</if>
        <if test="startNumber != null">`START_NUMBER`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="skuPrice != null">`SKU_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
        <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="endNumber != null">#{endNumber,jdbcType=INTEGER},</if>
        <if test="startNumber != null">#{startNumber,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="skuPrice != null">#{skuPrice,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <!--根据spuId查询对应sku列表-->
    <operation name="querySkuListByGoodsSpuId" multiplicity="many" paramtype="primitive" remark="根据spuId查询对应sku列表">
        SELECT * FROM HW_SHOP_GOODS_SKU
        WHERE is_del = 0
        AND goods_spu_id = #{goodsSpuId, jdbcType=VARCHAR};
    </operation>
    </table>
