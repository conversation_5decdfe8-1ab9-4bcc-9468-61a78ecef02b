<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_GOODS_COMBINED_RELATION" physicalName="HW_SHOP_GOODS_COMBINED_RELATION"
       remark="硬件商城套餐商品设备关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_COMBINED_RELATION">
        INSERT INTO HW_SHOP_GOODS_COMBINED_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="equipmentNumber != null">`EQUIPMENT_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="equipmentNumber != null">#{equipmentNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findGoodsCombinedByGoodsSpuId" multiplicity="many" paramtype="primitive"
               remark="根据商品spuId查询关联套餐设备">
        select
        *
        from HW_SHOP_GOODS_COMBINED_RELATION
        where goods_spu_id = #{goodsSpuId,jdbcType=VARCHAR}
        and is_del = 0
    </operation>
</table>
