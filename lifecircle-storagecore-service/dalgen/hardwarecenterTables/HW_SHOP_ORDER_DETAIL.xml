<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_ORDER_DETAIL" physicalName="HW_SHOP_ORDER_DETAIL"
       remark="订单详情表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_ORDER_DETAIL">
        INSERT INTO HW_SHOP_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isGroup != null">`IS_GROUP`,</if>
            <if test="goodsNumber != null">`GOODS_NUMBER`,</if>
            <if test="groupNumber != null">`GROUP_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="basePrice != null">`BASE_PRICE`,</if>
            <if test="finalPrice != null">`FINAL_PRICE`,</if>
            <if test="baseSumprice != null">`BASE_SUMPRICE`,</if>
            <if test="finalSumprice != null">`FINAL_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isGroup != null">#{isGroup,jdbcType=TINYINT},</if>
            <if test="goodsNumber != null">#{goodsNumber,jdbcType=INTEGER},</if>
            <if test="groupNumber != null">#{groupNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="basePrice != null">#{basePrice,jdbcType=DECIMAL},</if>
            <if test="finalPrice != null">#{finalPrice,jdbcType=DECIMAL},</if>
            <if test="baseSumprice != null">#{baseSumprice,jdbcType=DECIMAL},</if>
            <if test="finalSumprice != null">#{finalSumprice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findOrderDetailList" multiplicity="many" paramtype="primitive" remark="批量查询订单详情列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_DETAIL
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND `HW_ORDER_SN` IN
                <foreach collection="list" item="hwOrderSn" open="(" close=")" separator=",">
                    #{hwOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND IS_DEL = 0
        </where>
    </operation>

    <operation name="findOrderDetailByOrderSn" multiplicity="one" paramtype="primitive" remark="批量查询订单详情列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_DETAIL
        where HW_ORDER_SN = #{hwOrderSn,jdbcType=VARCHAR}
        AND GOODS_SPU_ID = #{goodsSpuId,jdbcType=VARCHAR}
        AND IS_DEL = 0
        limit 1
    </operation>

    <operation name="updateActivityNumberByOrderSnAndGoodsId" remark="根据orderSn和GoodsId扣减关联数量信息">
        update
        HW_SHOP_ORDER_DETAIL
        set
            had_join_activity_number = had_join_activity_number + 1
        WHERE
        HW_ORDER_SN = #{hwOrderSn,jdbcType=VARCHAR}
        AND GOODS_SPU_ID = #{goodsSpuId,jdbcType=VARCHAR}
        AND IS_DEL = 0
        limit 1
    </operation>


    <operation name="findOrderDetailByOrderSn" multiplicity="one" paramtype="primitive" remark="批量查询订单详情列表">
        SELECT
        *
        FROM
        HW_SHOP_ORDER_DETAIL
        where HW_ORDER_SN = #{hwOrderSn,jdbcType=VARCHAR}
        AND IS_DEL = 0
        and
        limit 1
    </operation>
</table>
