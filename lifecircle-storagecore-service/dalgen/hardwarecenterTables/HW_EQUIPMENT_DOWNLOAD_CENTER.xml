<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_DOWNLOAD_CENTER" physicalName="HW_EQUIPMENT_DOWNLOAD_CENTER"
       remark="硬件数据预约下载记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_DOWNLOAD_CENTER">
        INSERT INTO HW_EQUIPMENT_DOWNLOAD_CENTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="fileUrl != null">`FILE_URL`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="downloadId != null">`DOWNLOAD_ID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="moduleType != null">`MODULE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="fileUrl != null">#{fileUrl,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="downloadId != null">#{downloadId,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="moduleType != null">#{moduleType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="update" paramtype="object" remark="修改数据">
        UPDATE
        HW_EQUIPMENT_DOWNLOAD_CENTER
        <set>
            <if test="fileName != null">
                file_name = #{fileName,jdbcType=VARCHAR},
            </if>
            <if test="fileUrl != null">
                file_url = #{fileUrl,jdbcType=VARCHAR},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
        </set>
        WHERE
        download_id= #{downloadId,jdbcType=VARCHAR}
    </operation>

</table>
