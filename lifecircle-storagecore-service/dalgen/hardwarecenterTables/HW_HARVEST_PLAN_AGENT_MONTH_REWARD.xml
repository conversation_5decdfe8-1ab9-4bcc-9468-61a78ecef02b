<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_HARVEST_PLAN_AGENT_MONTH_REWARD" physicalName="HW_HARVEST_PLAN_AGENT_MONTH_REWARD"
       remark="丰收计划代理商设备奖励月统计表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_HARVEST_PLAN_AGENT_MONTH_REWARD">
        INSERT INTO HW_HARVEST_PLAN_AGENT_MONTH_REWARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="statisticsMonth != null">`STATISTICS_MONTH`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="monthAmount != null">`MONTH_AMOUNT`,</if>
            <if test="monthReward != null">`MONTH_REWARD`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="statisticsMonth != null">#{statisticsMonth,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="monthAmount != null">#{monthAmount,jdbcType=DECIMAL},</if>
            <if test="monthReward != null">#{monthReward,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findMonthRewardList" multiplicity="paging" paramtype="primitive"
               paging="FindMonthRewardList"
               remark="分页查询代理商月佣金"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.RewardDTO">
        SELECT
        t1.statistics_month as statistics,
        t1.init_sn as equipmentSn,
        t2.equipment_model as equipmentModel,
        cast(t1.month_amount as char) tradeAmount,
        cast(t1.month_reward as char) awardAmount,
        t1.uid as merchantId,
        t1.belong as agentId
        FROM
        hw_harvest_plan_agent_month_reward t1
        LEFT JOIN
        hw_equipment t2
        ON
        t2.id = t1.equipment_id
        WHERE
        t1.statistics_month = #{statisticsMonth,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
            and t1.belong in
            <foreach collection="agentIdList" item="belong" open="(" close=")" separator=",">
                #{belong,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
            and t1.uid in
            <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        and t1.is_del = 0
        ORDER BY t1.`statistics_month` DESC , t1.`month_amount` DESC
    </operation>


    <operation name="getMonthTotalReward" multiplicity="many" paramtype="primitive"
               paging="FindMonthTotalReward"
               remark="查询单设备，单月，单代理商奖励佣金之和"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.TotalRewardDTO">
        select
        init_sn as equipmentSn,
        belong as agentId,
        SUM(month_reward) as totalReward
        from
        hw_harvest_plan_agent_month_reward
        where
        statistics_month = #{statisticsMonth,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="agentIdList != null and agentIdList.size() &gt; 0 ">
            and belong in
            <foreach collection="agentIdList" item="agentId" open="(" close=")" separator=",">
                #{agentId,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="merchantIdList != null and merchantIdList.size() &gt; 0 ">
            and uid in
            <foreach collection="merchantIdList" item="merchantId" open="(" close=")" separator=",">
                #{merchantId,jdbcType=INTEGER}
            </foreach>
        </if>
        GROUP BY statistics_month,init_sn,belong
    </operation>
</table>
