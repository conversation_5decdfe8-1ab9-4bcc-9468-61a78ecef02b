<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_STOCK" physicalName="HW_EQUIPMENT_STOCK"
       remark="采购订单入库表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_STOCK">
        INSERT INTO HW_EQUIPMENT_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="stockOrder != null">`STOCK_ORDER`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="orderNum != null">`ORDER_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="stockOrder != null">#{stockOrder,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getPurchaseOrderList" multiplicity="many" remark="订单采购入库查询">
        select * from hw_equipment_stock
        where execute_time BETWEEN #{startTime,jdbcType=VARCHAR}
        AND #{endTime,jdbcType=VARCHAR}
        and depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
    </operation>

    <resultmap name="EquipmentSnPurchaseExportMap" type="EquipmentSnPurchaseExportMap">
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="stock_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="订单号"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="INTEGER" remark="仓库位置"/>
    </resultmap>

    <operation name="getPurchaseEquipmentList" multiplicity="many" resultmap="EquipmentSnPurchaseExportMap"
               remark="设备采购入库查询">
        select c.system_sn equipment_sn,d.equipment_name,d.equipment_model,a.stock_order,a.depot
        from hw_equipment_stock a
        LEFT JOIN hw_equipment_order_relation b on a.stock_order = b.stock_order
        LEFT JOIN hw_equipment_sn c on b.sn_id = c.id and c.is_del = 0
        LEFT JOIN hw_equipment d on c.equipment_id = d.id
        where a.execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
        and a.depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
        and b.is_del = 0
    </operation>

    <operation name="getPurchaseStockOrderInfo" multiplicity="one" remark="查询采购入库单信息">
        select *
        from hw_equipment_stock
        where stock_order = #{stockOrder,jdbcType=VARCHAR}
    </operation>
</table>
