<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_STORAGE_ORDER" physicalName="HW_EQUIPMENT_STORAGE_ORDER"
       remark="仓储订单表(采购/直营入库/总后台解绑入库/代理商入库)">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_STORAGE_ORDER">
        INSERT INTO HW_EQUIPMENT_STORAGE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="examiner != null">`EXAMINER`,</if>
            <if test="jobNumber != null">`JOB_NUMBER`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="examinerNumber != null">`EXAMINER_NUMBER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="orderNum != null">`ORDER_NUM`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=VARCHAR},</if>
            <if test="examiner != null">#{examiner,jdbcType=VARCHAR},</if>
            <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="examinerNumber != null">#{examinerNumber,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="bizType != null">#{bizType,jdbcType=TINYINT},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <resultmap name="InStoreroomDirectOrderInfoMap" type="InStoreroomDirectOrderInfoMap">
        <column name="storage_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="订单号"/>
        <column name="create_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="创建时间"/>
        <column name="username" javatype="java.lang.String" jdbctype="VARCHAR" remark="代理商名称"/>
        <column name="order_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="入库数量"/>
        <column name="operate_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="入库类型"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="出库类型"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
    </resultmap>

    <operation name="getInStoreroomDirectByStorageOrder" multiplicity="one"
               resultmap="InStoreroomDirectOrderInfoMap" remark="根据订单号查询直营入库订单信息">
        SELECT a.storage_order,a.create_time,b.username,a.order_num,a.operate_type,a.depot
        from hw_equipment_storage_order a
        LEFT JOIN tp_user b on a.agent_id = b.id
        where a.order_type = 1 and a.biz_type = 2
        and a.storage_order = #{storageOrder,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getUnDirectBackByStorageOrder" multiplicity="one" remark="根据订单号查询非直营回库订单信息">
        SELECT * from hw_equipment_storage_order
        where order_type = 1 and biz_type = 4
        and storage_order = #{storageOrder,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getByStorageOrder" multiplicity="one"
               resultmap="InStoreroomDirectOrderInfoMap" remark="根据订单号查询直营入库订单信息">
        SELECT a.storage_order,a.create_time,b.username,a.order_num,a.operate_type,a.biz_type
        from hw_equipment_storage_order a
        LEFT JOIN tp_user b on a.agent_id = b.id
        where a.order_type = #{orderType,jdbcType=TINYINT}
        <if test="bizType!=null">
            and a.biz_type = #{bizType,jdbcType=TINYINT}
        </if>
        <if test="storageOrder!=null and storageOrder!=''">
            and a.storage_order = #{storageOrder,jdbcType=VARCHAR}
        </if>
        limit 1

    </operation>
    
    <operation name="getStorageByOrder" multiplicity="one" paramtype="primitive" remark="根据采购单号获得采购信息">
        SELECT *
        FROM hw_equipment_storage_order
        WHERE storage_order = #{storageOrder,jdbcType=VARCHAR}
    </operation>

    <operation name="getBackAndShipmentOrderList" multiplicity="many" remark="订单直营入库及出库查询">
        select * from hw_equipment_storage_order
        where
        execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
        and order_type = #{orderType,jdbcType=INTEGER}
        <if test="orderType != null and orderType == 1">
            and order_status = 3 and biz_type in (2,4)
            and depot in
            <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                #{typeId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="orderType != null and orderType == 2">
            and biz_type in
            <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                #{typeId,jdbcType=TINYINT}
            </foreach>
        </if>
        and is_del = 0
    </operation>

    <resultmap name="EquipmentSnExportMap" type="EquipmentSnExportMap">
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="order_type" javatype="java.lang.Integer" jdbctype="INTEGER" remark="订单类型"/>
        <column name="storage_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="订单号"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="INTEGER" remark="仓库位置"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="INTEGER" remark="出库类型"/>
        <column name="depot_org" javatype="java.lang.Integer" jdbctype="INTEGER" remark="出库前仓库位置"/>
        <column name="unit_price" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="采购价"/>
    </resultmap>

    <operation name="getDirectAndShipmentEquipmentList" resultmap="EquipmentSnExportMap" multiplicity="many"
               remark="设备直营入库及出库查询">
        select c.system_sn
        equipment_sn,d.equipment_name,d.equipment_model,a.order_type,a.storage_order,a.depot,a.unit_price,c.depot
        depot_org,a.biz_type
        from hw_equipment_storage_order a
        LEFT JOIN hw_equipment_order_relation b on a.storage_order = b.order_no and b.is_del = 0
        LEFT JOIN hw_equipment_sn c on b.sn_id = c.id and c.is_del = 0
        LEFT JOIN hw_equipment d on c.equipment_id = d.id
        where a.execute_time BETWEEN #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
        and a.order_type = #{orderType,jdbcType=INTEGER}
        <if test="orderType != null and orderType == 1">
            and a.order_status = 3 and a.biz_type in (2,4)
            and a.depot in
            <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                #{typeId,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="orderType != null and orderType == 2">
            and a.biz_type in
            <foreach collection="list" open="(" close=")" item="typeId" separator=",">
                #{typeId,jdbcType=TINYINT}
            </foreach>
        </if>
        and a.is_del = 0
    </operation>

    <resultmap name="PurchaseOrderEquipmentInfoMap" type="PurchaseOrderEquipmentInfoMap">
        <column name="storage_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="订单号"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备id"/>
        <column name="create_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="创建时间"/>
        <column name="stock_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="采购入库订单"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="INTEGER" remark="仓库位置"/>
        <column name="system_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
    </resultmap>

    <operation name="getPurchaseOrderEquipmentInfo" resultmap="PurchaseOrderEquipmentInfoMap" multiplicity="many"
               remark="根据采购订单查询入库设备信息">
        select a.storage_order,a.equipment_id,a.create_time,b.stock_order,b.depot,d.system_sn
        FROM hw_equipment_storage_order a
        LEFT JOIN hw_equipment_stock b on b.storage_order = a.storage_order
        LEFT JOIN hw_equipment_order_relation c on c.stock_order = b.stock_order
        LEFT JOIN hw_equipment_sn d on d.id = c.sn_id
        where a.storage_order = #{storageOrder,jdbcType=VARCHAR}
        and a.is_del = 0 and c.is_del = 0 and d.is_del = 0
    </operation>

    <operation name="getOutByExecuteTime" paramtype="primitive" multiplicity="many" resulttype="String">
        SELECT esn.init_sn
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment_sn_change_log log on log.sn_id = esn.id and log.execute_time = rage.execute_time
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 2
        and log.handle_type = 11
        and log.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and log.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getOutByExecuteTimeForObject" paramtype="primitive" multiplicity="many"
               resultmap="InEuipmentSnMap">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,log.depot,log.execute_time,rage.biz_type
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment_sn_change_log log on log.sn_id = esn.id and log.execute_time = rage.execute_time
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 2
        and log.handle_type = 11
        and log.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and log.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getInByExecuteTime" paramtype="primitive" multiplicity="many" resulttype="String">
        SELECT esn.init_sn from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        where rage.order_type = 1
        and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
        and rage.order_status !=1
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <resultmap name="ByEquipmentIdMap" type="ByEquipmentIdMap">
        <column name="num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="数量"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="类型"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备属性id"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
    </resultmap>

    <resultmap name="ByEquipmentInMap" type="ByEquipmentInMap">
        <column name="num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="数量"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="类型"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备属性id"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
        <column name="operate_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="操作类型"/>
    </resultmap>

    <operation name="getOutByEquipmentId" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentIdMap"
               remark="查询出库设备信息">
        SELECT rage.biz_type,esn.equipment_id,lation.depot
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        where
        rage.order_type = 2
        and lation.is_del = 0
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and lation.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getInByEquipmentId" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentIdMap"
               remark="查询入库设备信息">
        SELECT count(*) num, rage.biz_type,esn.equipment_id,rage.depot
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
        where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and rage.order_status !=1
        and rage.order_type = 1
        and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
        GROUP BY rage.depot,esn.equipment_id,rage.biz_type
    </operation>

    <resultmap name="ByBizTypeMap" type="ByBizTypeMap">
        <column name="num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="数量"/>
        <column name="operate_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="操作类型"/>
    </resultmap>

    <operation name="getInByBizType" paramtype="primitive" multiplicity="many" resultmap="ByBizTypeMap"
               remark="根据biz_type查询入库设备信息">
        SELECT count(*) num,rage.operate_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
        where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and rage.order_type = 1
        and rage.order_status !=1
        and ((rage.order_status =2 and re.stock_order !='') or (rage.order_status =3))
        and rage.biz_type = #{bizType,jdbcType=TINYINT}
        and esn.depot = #{depot,jdbcType=TINYINT}
        and esn.equipment_id = #{equipmentId,jdbcType=INTEGER}
        GROUP BY rage.operate_type
    </operation>

    <operation name="getInDepot" paramtype="primitive" multiplicity="one" resulttype="Integer" remark="获取入库时仓库位置">
        SELECT rage.depot
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
        where
        esn.init_sn = #{initSn,jdbcType = VARCHAR}
        and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        and rage.order_type = 1
        ORDER BY rage.execute_time DESC LIMIT 1
    </operation>

    <operation name="getByOrderDepotSnList" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentIdMap"
               remark="根据snList，订单仓库位置查询">
        select count(*) as num ,depot,equipment_id from (SELECT init_sn,depot,equipment_id
        from (SELECT esn.init_sn,rage.depot,esn.equipment_id from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
        where rage.order_type = 1
        and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <if test="snList!=null and snList.size()>0">
            <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY rage.execute_time desc limit 10000) a
        GROUP BY a.init_sn)b group by b.depot,b.equipment_id;
    </operation>

    <resultmap name="InEuipmentSnMap" type="InEuipmentSnMap">
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备码"/>
        <column name="unit_price" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="采购价"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="stock_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="入库订单号"/>
        <column name="order_no" javatype="java.lang.String" jdbctype="VARCHAR" remark="采购订单号"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
        <column name="execute_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="执行时间"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="入库类型"/>
    </resultmap>

    <operation name="getInByExecuteTimeForStock" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,stock.depot,stock.execute_time,rage.biz_type
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment_stock stock on stock.stock_order = re.stock_order
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 1
        and rage.biz_type = 1
        and stock.id is not null
        and stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and stock.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getInByExecuteTimeForBack" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,rage.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 1
        and rage.biz_type != 1
        and re.stock_order = ''
        and re.id is not null
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and rage.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getOutSnByExecuteTimeAndLogDepot" paramtype="primitive" multiplicity="many"
               resultmap="InEuipmentSnMap">
        select esn.init_sn,la.depot from hw_equipment_sn esn
        left join hw_equipment_order_relation la on la.sn_id = esn.id
        left join hw_equipment_storage_order ra on ra.storage_order = la.order_no
        where ra.order_type = 2
        and ra.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and la.is_del = 0
        and la.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <operation name="getOutByExecuteTimeAndSn" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 2
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" open="(" close=")" item="sn" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getOutByExecuteTimeAndDepot" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,re.stock_order,re.order_no,re.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        left join hw_equipment_order_relation re on re.sn_id = esn.id
        left join hw_equipment_storage_order rage on rage.storage_order = re.order_no
        left join hw_equipment e on e.id = esn.equipment_id
        where rage.order_type = 2
        and re.is_del = 0
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and re.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>


    <operation name="getInByEquipmentIdForBack" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentInMap"
               remark="查询入库设备信息">
        SELECT rage.biz_type,esn.equipment_id,rage.depot,rage.operate_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
        where rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and rage.order_type = 1
        and rage.biz_type != 1
        and rage.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>
    <operation name="getInByEquipmentIdForStock" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentInMap"
               remark="查询入库设备信息">
        SELECT rage.biz_type,esn.equipment_id,stock.depot,rage.operate_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation re on re.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = re.order_no
        LEFT JOIN hw_equipment_stock stock on stock.storage_order = rage.storage_order and stock.stock_order =
        re.stock_order
        where stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        and rage.order_type = 1
        and rage.biz_type = 1
        and stock.depot in
        <foreach collection="list" open="(" close=")" item="depot" separator=",">
            #{depot,jdbcType=TINYINT}
        </foreach>
    </operation>

    <resultmap name="OrderDepotSnListMap" type="OrderDepotSnListMap">
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备码"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备属性id"/>
        <column name="execute_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="入库日期"/>
    </resultmap>


    <operation name="getByOrderDepotSnListForBack" paramtype="primitive" multiplicity="many"
               resultmap="OrderDepotSnListMap"
               remark="根据snList，订单仓库位置查询回库信息">
        select init_sn,depot,equipment_id,execute_time
        from(SELECT esn.init_sn,rage.depot,esn.equipment_id,rage.execute_time
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
        where rage.order_type = 1
        and rage.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        and rage.biz_type !=1
        and esn.init_sn in
        <if test="snList!=null and snList.size()>0">
            <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY rage.execute_time desc limit 100000 )a group by init_sn
    </operation>

    <operation name="getByOrderDepotSnListForStock" paramtype="primitive" multiplicity="many"
               resultmap="OrderDepotSnListMap"
               remark="根据snList，订单仓库位置查询采购信息">
        select init_sn,depot,equipment_id,execute_time
        from(SELECT esn.init_sn,rage.depot,esn.equipment_id,rage.execute_time
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation retion on retion.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = retion.order_no
        LEFT JOIN hw_equipment_stock stock on stock.storage_order = rage.storage_order and stock.stock_order =
        retion.stock_order
        where rage.order_type = 1
        and rage.biz_type = 1
        and stock.id is not null
        and stock.execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <if test="snList!=null and snList.size()>0">
            <foreach collection="snList" item="sn" open="(" close=")" separator=",">
                #{sn,jdbcType=VARCHAR}
            </foreach>
        </if>
        ORDER BY stock.execute_time desc limit 100000 )a group by init_sn
    </operation>
    <operation name="queryPurchaseInfo" multiplicity="one" paramtype="primitive"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.EquipmentStoreRoomModel"
               remark="查询采购信息">
        SELECT o.storage_order as storageOrder,e.equipment_name as equipmentName,e.equipment_model as equipmentModel,
        o.order_num as orderNum,o.arrival_num as arrivalNum,e.equipment_pic as
        equipmentPic,o.depot,is_union_authentication as isUnionAuthentication
        FROM hw_equipment_storage_order o
        LEFT JOIN hw_equipment e on o.equipment_id = e.id
        WHERE o.order_type = 1
        AND o.biz_type = 1
        AND o.is_del = 0
        AND o.storage_order = #{storageOrder,jdbcType=VARCHAR}
    </operation>
</table>
