<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_NAME" physicalName="TP_EQUIPMENT_NAME"
       remark="硬件名称表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT_NAME">
        INSERT INTO TP_EQUIPMENT_NAME
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="equipmentName != null">`EQUIPMENT_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="equipmentName != null">#{equipmentName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>

</table>
