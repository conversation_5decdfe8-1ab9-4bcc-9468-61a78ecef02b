<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_PERIODS_STATISTICS_RECORD" physicalName="HW_EQUIPMENT_PERIODS_STATISTICS_RECORD"
       remark="硬件库存期数统计记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_PERIODS_STATISTICS_RECORD">
        INSERT INTO HW_EQUIPMENT_PERIODS_STATISTICS_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
            <if test="periodsTime != null">`PERIODS_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
            <if test="periodsTime != null">#{periodsTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getMaxPeriodsTime" paramtype="primitive" multiplicity="one" resulttype="Integer"
               remark="获取指定日期可用期数">
        select MAX(periods_time) FROM HW_EQUIPMENT_PERIODS_STATISTICS_RECORD
        where periods_time &lt;=#{periodsTime,jdbcType=INTEGER}
    </operation>

    <resultmap name="ByEquipmentIdMap" type="ByEquipmentIdMap">
        <column name="num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="数量"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="类型"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备属性id"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
    </resultmap>

    <operation name="getByPeriodsTime" paramtype="primitive" multiplicity="many" resulttype="String" remark="查询指定时间的设备">
        SELECT equipment_sn from hw_equipment_periods_statistics_record
        where periods_time = #{periodsTime,jdbcType=INTEGER}
    </operation>

    <operation name="getByPeriods" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentIdMap"
               remark="查询期初设备信息">
        SELECT count(*) num,esn.equipment_id,esn.depot
        from hw_equipment_periods_statistics_record periods
        LEFT JOIN hw_equipment_sn esn on esn.init_sn = periods.equipment_sn
        where periods.periods_time BETWEEN #{startTime,jdbcType=INTEGER} and #{endTime,jdbcType=INTEGER}
        GROUP BY esn.depot,esn.equipment_id
    </operation>

</table>
