<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_WORK_ORDER_IMAGE" physicalName="HW_EQUIPMENT_WORK_ORDER_IMAGE"
       remark="设备工单图片表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_WORK_ORDER_IMAGE">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_IMAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="imageUrl != null">`IMAGE_URL`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="imageType != null">`IMAGE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="imageUrl != null">#{imageUrl,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="imageType != null">#{imageType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="batchInsert" paramtype="objectList" remark="batchInsert:HW_EQUIPMENT_WORK_ORDER_IMAGE">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER_IMAGE(`INIT_SN`,`IMAGE_URL`,`WORK_ORDER_SN`,`IMAGE_TYPE`)
        VALUES
        <foreach collection="list" item="img" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{img.initSn,jdbcType=VARCHAR},
                #{img.imageUrl,jdbcType=VARCHAR},
                #{img.workOrderSn,jdbcType=VARCHAR},
                #{img.imageType,jdbcType=TINYINT},
            </trim>
        </foreach>

    </operation>
</table>
