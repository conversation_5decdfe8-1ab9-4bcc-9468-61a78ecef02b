<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL" physicalName="HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL"
       remark="丰收计划授理商硬件积分日统计表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL">
        INSERT INTO HW_HARVEST_PLAN_SALESMAN_DAY_INTEGRAL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="salesman != null">`SALESMAN`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="statisticsDay != null">`STATISTICS_DAY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="dayAmount != null">`DAY_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="salesman != null">#{salesman,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="statisticsDay != null">#{statisticsDay,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="dayAmount != null">#{dayAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>


    <operation name="findSalesmanSnIntegralList" multiplicity="paging" paramtype="primitive"
               paging="FindSalesmanSnIntegralList" remark="分页查询受理商设备积分列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.SalesmanIntegralDTO">
        select
        t1.init_sn as equipmentSn,
        t1.statistics_day as statisticsDay,
        t2.equipment_model as equipmentModel,
        cast(t1.day_amount as char) as tradeAmount,
        t1.uid as merchantId,
        t1.salesman as salesmanId,
        t1.belong as agentId
        from hw_harvest_plan_salesman_day_integral t1
        left join hw_equipment t2 on t1.equipment_id = t2.id
        where
        t1.belong = #{belong,jdbcType=INTEGER}
        and t1.statistics_day <![CDATA[>=]]> #{startDay,jdbcType=INTEGER}
        and t1.statistics_day <![CDATA[<=]]> #{endDay,jdbcType=INTEGER}
        <if test="initSn != null and initSn != '' ">
            and t1.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="equipmentId != null">
            and t1.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="merchantIdList != null and merchantIdList.size() > 0">
            and t1.uid in
            <foreach collection="merchantIdList" item="uid" open="(" close=")" separator=",">
                #{uid,jdbcType=INTEGER}
            </foreach>
        </if>
        <if test="salesmanIdList != null and salesmanIdList.size() > 0">
            and t1.salesman in
            <foreach collection="salesmanIdList" item="salesman" open="(" close=")" separator=",">
                #{salesman,jdbcType=INTEGER}
            </foreach>
        </if>
        and t1.is_del = 0
        ORDER BY t1.statistics_day DESC,t1.day_amount DESC
    </operation>

</table>
