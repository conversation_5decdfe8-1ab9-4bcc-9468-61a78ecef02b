<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_PURCHASE_ORDER" physicalName="TP_EQUIPMENT_PURCHASE_ORDER"
       remark="(总后台入库/代理商出库)硬件物流订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT_PURCHASE_ORDER">
        INSERT INTO TP_EQUIPMENT_PURCHASE_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="creater != null">`CREATER`,</if>
            <if test="examiner != null">`EXAMINER`,</if>
            <if test="orderNum != null">`ORDER_NUM`,</if>
            <if test="arrivalNum != null">`ARRIVAL_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="examinTime != null">`EXAMIN_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="purchaseType != null">`PURCHASE_TYPE`,</if>
            <if test="depotLocation != null">`DEPOT_LOCATION`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="creater != null">#{creater,jdbcType=INTEGER},</if>
            <if test="examiner != null">#{examiner,jdbcType=INTEGER},</if>
            <if test="orderNum != null">#{orderNum,jdbcType=INTEGER},</if>
            <if test="arrivalNum != null">#{arrivalNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="examinTime != null">#{examinTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="purchaseType != null">#{purchaseType,jdbcType=TINYINT},</if>
            <if test="depotLocation != null">#{depotLocation,jdbcType=INTEGER},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

</table>
