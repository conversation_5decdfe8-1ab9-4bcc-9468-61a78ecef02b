<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_SALES_ORDER" physicalName="HW_SHOP_SALES_ORDER"
       remark="硬件商城报表中心销售订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_SALES_ORDER">
        INSERT INTO HW_SHOP_SALES_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="extendInfo != null">`EXTEND_INFO`,</if>
            <if test="salesOrder != null">`SALES_ORDER`,</if>
            <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
            <if test="depot != null">`DEPOT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isTest != null">`IS_TEST`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="salesType != null">`SALES_TYPE`,</if>
            <if test="bizPayTime != null">`BIZ_PAY_TIME`,</if>
            <if test="goodsSnNumber != null">`GOODS_SN_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="costPrice != null">`COST_PRICE`,</if>
            <if test="salesRate != null">`SALES_RATE`,</if>
            <if test="expressFee != null">`EXPRESS_FEE`,</if>
            <if test="salesPrice != null">`SALES_PRICE`,</if>
            <if test="costUnitPrice != null">`COST_UNIT_PRICE`,</if>
            <if test="onlinePayPrice != null">`ONLINE_PAY_PRICE`,</if>
            <if test="salesUnitPrice != null">`SALES_UNIT_PRICE`,</if>
            <if test="balancePayPrice != null">`BALANCE_PAY_PRICE`,</if>
            <if test="offlinePayPrice != null">`OFFLINE_PAY_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="extendInfo != null">#{extendInfo,jdbcType=VARCHAR},</if>
            <if test="salesOrder != null">#{salesOrder,jdbcType=VARCHAR},</if>
            <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
            <if test="depot != null">#{depot,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isTest != null">#{isTest,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="salesType != null">#{salesType,jdbcType=TINYINT},</if>
            <if test="bizPayTime != null">#{bizPayTime,jdbcType=INTEGER},</if>
            <if test="goodsSnNumber != null">#{goodsSnNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="costPrice != null">#{costPrice,jdbcType=DECIMAL},</if>
            <if test="salesRate != null">#{salesRate,jdbcType=DECIMAL},</if>
            <if test="expressFee != null">#{expressFee,jdbcType=DECIMAL},</if>
            <if test="salesPrice != null">#{salesPrice,jdbcType=DECIMAL},</if>
            <if test="costUnitPrice != null">#{costUnitPrice,jdbcType=DECIMAL},</if>
            <if test="onlinePayPrice != null">#{onlinePayPrice,jdbcType=DECIMAL},</if>
            <if test="salesUnitPrice != null">#{salesUnitPrice,jdbcType=DECIMAL},</if>
            <if test="balancePayPrice != null">#{balancePayPrice,jdbcType=DECIMAL},</if>
            <if test="offlinePayPrice != null">#{offlinePayPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findSalesOrderPageList" paging="hwShopSalesOrderList" multiplicity="paging" remark="分页查询硬件商品订单列表">
        SELECT
        *
        FROM
        HW_SHOP_SALES_ORDER
        WHERE
        `IS_DEL` = 0
        <if test="relationOrderNo != null and relationOrderNo != '' ">
            AND `RELATION_ORDER_NO` = #{relationOrderNo,jdbcType=VARCHAR}
        </if>
        <if test="list != null and list.size() &gt; 0 ">
            AND `RELATION_ORDER_NO` IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="username != null and username != '' ">
            AND `USERNAME` LIKE concat(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="goodsName != null and goodsName != '' ">
            AND `GOODS_NAME` LIKE concat(#{goodsName,jdbcType=VARCHAR},'%')
        </if>
        <if test="storageStartTime != null">
            AND `BIZ_TIME` <![CDATA[>=]]> #{storageStartTime,jdbcType=TIMESTAMP}
        </if>
        <if test="storageEndTime != null">
            AND `BIZ_TIME` <![CDATA[<=]]> #{storageEndTime,jdbcType=TIMESTAMP}
        </if>
        <if test="bizPayStartTime != null">
            AND `BIZ_PAY_TIME` <![CDATA[>=]]> #{bizPayStartTime,jdbcType=INTEGER}
        </if>
        <if test="bizPayEndTime != null">
            AND `BIZ_PAY_TIME` <![CDATA[<=]]> #{bizPayEndTime,jdbcType=INTEGER}
        </if>
        <if test="userId != null">
            AND `USER_ID` = #{userId,jdbcType=INTEGER}
        </if>
        <if test="userType != null">
            AND `USER_TYPE` = #{userType,jdbcType=TINYINT}
        </if>
        <if test="salesTypeList != null and salesTypeList.size() &gt; 0 ">
            AND `SALES_TYPE` IN
            <foreach collection="salesTypeList" item="salesType" open="(" close=")" separator=",">
                #{salesType, jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="isTest != null">
            AND is_test= #{isTest, jdbcType=INTEGER}
        </if>
        ORDER BY `BIZ_TIME` DESC
    </operation>

</table>
