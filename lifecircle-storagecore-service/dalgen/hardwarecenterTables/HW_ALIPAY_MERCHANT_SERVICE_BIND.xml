<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_ALIPAY_MERCHANT_SERVICE_BIND" physicalName="HW_ALIPAY_MERCHANT_SERVICE_BIND"
       remark="支付宝商服绑定表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_ALIPAY_MERCHANT_SERVICE_BIND">
        INSERT INTO HW_ALIPAY_MERCHANT_SERVICE_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="msid != null">`MSID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="identityNo != null">`IDENTITY_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=BIGINT},</if>
            <if test="msid != null">#{msid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="identityNo != null">#{identityNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findAllMerchant" multiplicity="many" remark="查询所有绑定的商户">
        select
        uid, token
        from hw_alipay_merchant_service_bind
    </operation>

    <operation name="countAllMerchant" multiplicity="one" resulttype="Integer" remark="统计所有绑定的商户">
        select
        count(*)
        from hw_alipay_merchant_service_bind
    </operation>

    <operation name="findPageAllMerchant" multiplicity="many" remark="查询所有绑定的商户">
        select
        id, uid, token
        from hw_alipay_merchant_service_bind
        where id > #{id,jdbcType=BIGINT}
        limit #{size,jdbcType=INTEGER}
    </operation>
</table>
