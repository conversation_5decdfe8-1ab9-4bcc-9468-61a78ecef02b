<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="HW_EQUIPMENT" physicalName="HW_EQUIPMENT"
    remark="硬件设备表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT">
INSERT INTO HW_EQUIPMENT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="jobNumber != null">`JOB_NUMBER`,</if>
        <if test="equipmentPic != null">`EQUIPMENT_PIC`,</if>
        <if test="equipmentFirm != null">`EQUIPMENT_FIRM`,</if>
        <if test="equipmentName != null">`EQUIPMENT_NAME`,</if>
        <if test="equipmentModel != null">`EQUIPMENT_MODEL`,</if>
        <if test="equipmentPrefix != null">`EQUIPMENT_PREFIX`,</if>
        <if test="equipmentIntroduce != null">`EQUIPMENT_INTRODUCE`,</if>
        <if test="appShow != null">`APP_SHOW`,</if>
        <if test="equipmentType != null">`EQUIPMENT_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="equipmentPrice != null">`EQUIPMENT_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="jobNumber != null">#{jobNumber,jdbcType=VARCHAR},</if>
        <if test="equipmentPic != null">#{equipmentPic,jdbcType=VARCHAR},</if>
        <if test="equipmentFirm != null">#{equipmentFirm,jdbcType=VARCHAR},</if>
        <if test="equipmentName != null">#{equipmentName,jdbcType=VARCHAR},</if>
        <if test="equipmentModel != null">#{equipmentModel,jdbcType=VARCHAR},</if>
        <if test="equipmentPrefix != null">#{equipmentPrefix,jdbcType=VARCHAR},</if>
        <if test="equipmentIntroduce != null">#{equipmentIntroduce,jdbcType=VARCHAR},</if>
        <if test="appShow != null">#{appShow,jdbcType=TINYINT},</if>
        <if test="equipmentType != null">#{equipmentType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="equipmentPrice != null">#{equipmentPrice,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getEquipmentById" multiplicity="one" paramtype="primitive" remark="根据设备Id获取设备信息">
        SELECT *
        FROM HW_EQUIPMENT
        WHERE id = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="getExportInventoryDetailedList" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.InventoryDetailedListModelMap"
               remark="库存清单列表导出">
        SELECT d.systemSn AS systemSn,d.equipmentName AS equipmentName,d.equipmentModel AS equipmentModel,d.orderNo AS
        orderNo,d.stockOrder AS stockOrder,o.biz_type AS bizType,o.operate_type AS operateType
        FROM
        (
        SELECT c.equipmentId,c.equipmentModel,c.equipmentName,c.systemSn,r.order_no AS orderNo,r.stock_order AS
        stockOrder
        FROM
        (
        SELECT e.id as equipmentId , e.equipment_model AS equipmentModel,e.equipment_name AS equipmentName,s.system_sn
        AS systemSn,s.id AS snId
        FROM hw_equipment as e
        LEFT JOIN hw_equipment_sn as s
        ON e.id = s.equipment_id
        WHERE s.sn_status = 2
        AND s.is_del = 0
        <if test="depot != null">
            AND s.depot != #{depot, jdbcType=TINYINT}
        </if>
        AND e.id IN
        <foreach close=")" collection="list" index="index" item="equipmentId" open="(" separator=",">
            #{equipmentId,jdbcType=INTEGER}
        </foreach>
        ) c
        LEFT JOIN hw_equipment_order_relation AS r
        ON c.snId = r.sn_id
        WHERE r.is_del = 0
        ) d
        LEFT JOIN hw_equipment_storage_order o
        ON d.orderNo = o.storage_order
        WHERE o.is_del = 0
        AND o.order_type = 1
    </operation>

    <operation name="getEquipmentNameList" multiplicity="many" remark="获取所有设备">
        SELECT *
        FROM hw_equipment
        GROUP BY equipment_name
    </operation>

    <operation name="getAll" paramtype="primitive" multiplicity="many" remark="查询所有设备型号信息">
        select * from hw_equipment
    </operation>

    <operation name="findEquipmentByIdList" multiplicity="many" paramtype="primitive" remark="根据id获取设备信息">
        SELECT
        *
        FROM HW_EQUIPMENT
        WHERE `ID` IN
        <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="findByInitSnList" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.InitSnEquipmentModel">
        select a.equipment_model,b.init_sn
        from hw_equipment a
        inner join hw_equipment_sn b on a.id=b.equipment_id
        where b.init_sn in
        <foreach collection="list" item="initSn" separator="," open="(" close=")">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
