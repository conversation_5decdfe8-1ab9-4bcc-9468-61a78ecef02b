<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_ORDER_RELATION" physicalName="HW_EQUIPMENT_ORDER_RELATION"
       remark="硬件入库记录详情表（记录入库订单号对应的设备sn码）">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_ORDER_RELATION">
        INSERT INTO HW_EQUIPMENT_ORDER_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="stockOrder != null">`STOCK_ORDER`,</if>
            <if test="snId != null">`SN_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="stockOrder != null">#{stockOrder,jdbcType=VARCHAR},</if>
            <if test="snId != null">#{snId,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <resultmap name="ShipmentEquipmentSnDo" type="ShipmentEquipmentSnDO">
        <column name="system_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="系统sn码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="主键id"/>
    </resultmap>

    <operation name="getByEquipmentSn" paramtype="primitive" multiplicity="many"
               resultmap="ShipmentEquipmentSnDo">
        SELECT esn.id,esn.system_sn,equipment.equipment_name,equipment.equipment_model
        from hw_equipment_order_relation relation
        LEFT JOIN hw_equipment_sn esn on esn.id = relation.sn_id
        LEFT JOIN hw_equipment equipment on equipment.id = esn.equipment_id
        WHERE relation.order_no = #{orderNo,jdbcType=VARCHAR}
        <if test="equipmentSn!=null and equipmentSn!=''">
            and esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
    </operation>
    <operation name="getSnIdList" multiplicity="many" paramtype="primitive"
               remark="根据订单号获取snId">
        SELECT *
        FROM HW_EQUIPMENT_ORDER_RELATION
        WHERE is_del = 0
        <if test="orderNo !=null and orderNo !=''">
            AND order_no = #{orderNo,jdbcType=VARCHAR}
        </if>
        <if test="stockOrder != null and stockOrder !=''">
            AND stock_order = #{stockOrder,jdbcType=VARCHAR}
        </if>
    </operation>

    <operation name="getSnListByOrderNo" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.hardwarecenter.resultmap.AgentEquipmentSnListMap" remark="新代理商后台出入库订单详情导出">
        SELECT s.init_sn AS initSn,
        eq.equipment_name AS equipmentName,
        eq.equipment_model AS equipmentModel
        FROM hw_equipment_order_relation AS r
        LEFT JOIN hw_equipment_sn AS s ON r.sn_id = s.id
        LEFT JOIN hw_equipment AS eq ON s.equipment_id = eq.id
        WHERE r.order_no = #{orderNo,jdbcType=VARCHAR}
        AND r.is_del = 0
        AND s.is_del = 0
    </operation>

    <operation name="getLastTimeOutStorageInfoBySnId" paramtype="primitive" multiplicity="one"
               remark="根据snId获取最近一次出库仓库位置">
        SELECT
        *
        FROM
        hw_equipment_order_relation
        WHERE sn_id = #{snId, jdbcType=INTEGER}
        AND order_no like 'SDB%'
        ORDER BY create_time DESC
        LIMIT 1;
    </operation>
</table>
