<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="HW_SHOP_ACTIVITY_SCORE_LOG" physicalName="HW_SHOP_ACTIVITY_SCORE_LOG"
    remark="硬件商城代理商积分流水明细表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:HW_SHOP_ACTIVITY_SCORE_LOG">
INSERT INTO HW_SHOP_ACTIVITY_SCORE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="operateLogId != null">`OPERATE_LOG_ID`,</if>
        <if test="operatorName != null">`OPERATOR_NAME`,</if>
        <if test="relationOrderNo != null">`RELATION_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="score != null">`SCORE`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="changeScore != null">`CHANGE_SCORE`,</if>
        <if test="operateType != null">`OPERATE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="operateLogId != null">#{operateLogId,jdbcType=VARCHAR},</if>
        <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
        <if test="relationOrderNo != null">#{relationOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="score != null">#{score,jdbcType=INTEGER},</if>
        <if test="accountId != null">#{accountId,jdbcType=INTEGER},</if>
        <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
        <if test="changeScore != null">#{changeScore,jdbcType=INTEGER},</if>
        <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>
    <operation name="findActivityScoreLogPage"
               multiplicity="paging"
               paging="ActivityScoreLog"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.hwshop.HwShopScoreLogExportModel"
               remark="查询活动积分流水分页列表">
        select
        `CREATE_TIME` as operateTime,
        `OPERATE_TYPE` as operateType,
        `ACTIVITY_ID` as activityId,
        `ACCOUNT_ID` as accountId,
        `ACCOUNT_NAME` as accountName,
        `RELATION_ORDER_NO` as orderSn,
        `SCORE` as afterChangeScore,
        `CHANGE_SCORE` as changeScore
        from `hw_shop_activity_score_log`
        where is_del = 0
        <if test="activityId != null and activityId > 0">
            AND activity_id = #{activityId, jdbcType=INTEGER}
        </if>
        <if test="accountName != null and accountName != '' ">
            AND account_name LIKE CONCAT (#{accountName,jdbcType=VARCHAR},'%')
        </if>
        <if test="operateType != null and operateType != 0 ">
            AND operate_type = #{operateType,jdbcType=INTEGER}
        </if>
        <if test="startTime != null">
            AND `create_time` <![CDATA[>=]]> #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null">
            AND `create_time` <![CDATA[<=]]> #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY `create_time` DESC, id desc
    </operation>

    </table>
