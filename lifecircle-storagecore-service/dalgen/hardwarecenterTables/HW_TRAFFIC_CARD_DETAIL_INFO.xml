<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="HW_TRAFFIC_CARD_DETAIL_INFO" physicalName="HW_TRAFFIC_CARD_DETAIL_INFO"
    remark="流量卡详情信息表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:HW_TRAFFIC_CARD_DETAIL_INFO">
INSERT INTO HW_TRAFFIC_CARD_DETAIL_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="iccid != null">`ICCID`,</if>
        <if test="msisdn != null">`MSISDN`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="spCode != null">`SP_CODE`,</if>
        <if test="carrier != null">`CARRIER`,</if>
        <if test="simType != null">`SIM_TYPE`,</if>
        <if test="bindInitSn != null">`BIND_INIT_SN`,</if>
        <if test="expiryDate != null">`EXPIRY_DATE`,</if>
        <if test="accountStatus != null">`ACCOUNT_STATUS`,</if>
        <if test="active != null">`ACTIVE`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="bindUid != null">`BIND_UID`,</if>
        <if test="bindAgentId != null">`BIND_AGENT_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="dataPlan != null">`DATA_PLAN`,</if>
        <if test="dataUsage != null">`DATA_USAGE`,</if>
        <if test="dataBalance != null">`DATA_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="iccid != null">#{iccid,jdbcType=VARCHAR},</if>
        <if test="msisdn != null">#{msisdn,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="spCode != null">#{spCode,jdbcType=VARCHAR},</if>
        <if test="carrier != null">#{carrier,jdbcType=VARCHAR},</if>
        <if test="simType != null">#{simType,jdbcType=VARCHAR},</if>
        <if test="bindInitSn != null">#{bindInitSn,jdbcType=VARCHAR},</if>
        <if test="expiryDate != null">#{expiryDate,jdbcType=VARCHAR},</if>
        <if test="accountStatus != null">#{accountStatus,jdbcType=VARCHAR},</if>
        <if test="active != null">#{active,jdbcType=TINYINT},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="bindUid != null">#{bindUid,jdbcType=INTEGER},</if>
        <if test="bindAgentId != null">#{bindAgentId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="dataPlan != null">#{dataPlan,jdbcType=DECIMAL},</if>
        <if test="dataUsage != null">#{dataUsage,jdbcType=DECIMAL},</if>
        <if test="dataBalance != null">#{dataBalance,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="findTrafficCardList" multiplicity="paging" paging="trafficCardQueryListParam"
               remark="查询代理商流量卡列表">
        SELECT
        *
        FROM HW_TRAFFIC_CARD_DETAIL_INFO
        <where>
            AGENT_ID = #{agentId,jdbcType=INTEGER}
            <if test="msisdn != '' or initSn != '' or uid != null">
                AND (MSISDN like concat('%',#{msisdn,jdbcType=VARCHAR},'%')
                OR BIND_INIT_SN like concat('%',#{initSn,jdbcType=VARCHAR},'%')
                <if test="uid != null">
                    OR BIND_UID like concat('%',#{uid,jdbcType=INTEGER},'%')
                </if>
                )
            </if>
            <if test="msisdn == '' and initSn == '' and uid == null">
                <if test="accountStatusList != null and accountStatusList.size > 0">
                    AND ACCOUNT_STATUS in
                    <foreach collection="accountStatusList" item="accountStatus" open="(" separator="," close=")">
                        #{accountStatus,jdbcType=VARCHAR}
                    </foreach>
                </if>
                <if test="active != null">
                    AND ACTIVE = #{active,jdbcType=TINYINT}
                </if>
                <if test="dataWarn != null">
                    AND DATA_WARN = #{dataWarn,jdbcType=TINYINT}
                </if>
                <if test="deleteWarn != null">
                    AND DELETE_WARN = #{deleteWarn,jdbcType=TINYINT}
                </if>
                <if test="expiryWarn != null">
                    AND EXPIRY_WARN = #{expiryWarn,jdbcType=TINYINT}
                </if>
            </if>
        </where>
    </operation>
    </table>
