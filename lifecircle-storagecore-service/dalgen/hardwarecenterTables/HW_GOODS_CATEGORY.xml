<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="HW_GOODS_CATEGORY" physicalName="HW_GOODS_CATEGORY"
    remark="商品分类表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:HW_GOODS_CATEGORY">
INSERT INTO HW_GOODS_CATEGORY
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="categoryName != null">`CATEGORY_NAME`,</if>
        <if test="pid != null">`PID`,</if>
        <if test="sort != null">`SORT`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="categoryStatus != null">`CATEGORY_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
        <if test="pid != null">#{pid,jdbcType=INTEGER},</if>
        <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="categoryStatus != null">#{categoryStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>


    <operation name="getByIdIn" multiplicity="many"  remark="获取关联数据的全部数据">
        select
        *
        from
        hw_goods_category
        where
        id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        and
        is_del = 0
    </operation>


    <operation name="getByPid" multiplicity="one"  remark="获取全部子数据">
        select
        *
        from
        hw_goods_category
        where
        id = #{pid,jdbcType=INTEGER}
        and
        is_del = 0
    </operation>

    <operation name="getChildData" multiplicity="many" remark="获取全部子数据">
        select
        *
        from
        hw_goods_category
        where
        pid = #{id,jdbcType=INTEGER}
        and
        is_del = 0
    </operation>

    <operation name="getOneLevel" resulttype="int" remark="是否是一级类目id">
        select
        count(*)
        from
        hw_goods_category
        where
        pid = #{pid,jdbcType=INTEGER}
        and
        is_del = 0
    </operation>
</table>
