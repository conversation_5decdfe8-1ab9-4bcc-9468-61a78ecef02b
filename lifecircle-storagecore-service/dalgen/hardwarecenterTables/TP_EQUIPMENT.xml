<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT" physicalName="TP_EQUIPMENT"
       remark="硬件设备表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT">
        INSERT INTO TP_EQUIPMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="equipmentFirm != null">`EQUIPMENT_FIRM`,</if>
            <if test="equipmentPrefix != null">`EQUIPMENT_PREFIX`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="equipmentType != null">`EQUIPMENT_TYPE`,</if>
            <if test="equipmentNameId != null">`EQUIPMENT_NAME_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="equipmentFirm != null">#{equipmentFirm,jdbcType=VARCHAR},</if>
            <if test="equipmentPrefix != null">#{equipmentPrefix,jdbcType=CHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operator != null">#{operator,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="equipmentType != null">#{equipmentType,jdbcType=TINYINT},</if>
            <if test="equipmentNameId != null">#{equipmentNameId,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <resultmap name="EquipmentNameListMap" type="EquipmentNameListMap">
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备id"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
    </resultmap>

    <operation name="getEquipmentNameList" resultmap="EquipmentNameListMap" multiplicity="many" remark="查询所有设备名称列表">
        select a.id equipment_id,a.equipment_firm,a.equipment_name_id,b.equipment_name
        from tp_equipment a
        LEFT JOIN tp_equipment_name b on b.id = a.equipment_name_id
    </operation>
</table>
