<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_REFUND_ORDER_DETAIL" physicalName="HW_SHOP_REFUND_ORDER_DETAIL"
       remark="订单退货详情表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_REFUND_ORDER_DETAIL">
        INSERT INTO HW_SHOP_REFUND_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSkuId != null">`GOODS_SKU_ID`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="hwRefundOrderSn != null">`HW_REFUND_ORDER_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="goodsNumber != null">`GOODS_NUMBER`,</if>
            <if test="goodsRefundNumber != null">`GOODS_REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="goodsPrice != null">`GOODS_PRICE`,</if>
            <if test="goodsSumprice != null">`GOODS_SUMPRICE`,</if>
            <if test="goodsRefundSumprice != null">`GOODS_REFUND_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSkuId != null">#{goodsSkuId,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="hwRefundOrderSn != null">#{hwRefundOrderSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="goodsNumber != null">#{goodsNumber,jdbcType=INTEGER},</if>
            <if test="goodsRefundNumber != null">#{goodsRefundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsPrice != null">#{goodsPrice,jdbcType=DECIMAL},</if>
            <if test="goodsSumprice != null">#{goodsSumprice,jdbcType=DECIMAL},</if>
            <if test="goodsRefundSumprice != null">#{goodsRefundSumprice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findRefundOrderDetailListByOrderSnList" paramtype="primitive" multiplicity="many"
               remark="查询退款订单详细信息">
        SELECT
        *
        FROM
        hw_shop_refund_order_detail
        <where>
            <if test="list != null and list.size() &gt; 0">
                AND hw_refund_order_sn IN
                <foreach collection="list" open="(" close=")" item="hwRefundOrderSn" separator=",">
                    #{hwRefundOrderSn,jdbcType=VARCHAR}
                </foreach>
            </if>
            AND `IS_DEL` = 0
        </where>
    </operation>

</table>
