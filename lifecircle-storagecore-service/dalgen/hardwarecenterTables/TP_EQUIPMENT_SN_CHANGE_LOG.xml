<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_SN_CHANGE_LOG" physicalName="TP_EQUIPMENT_SN_CHANGE_LOG"
       remark="硬件流动信息记录表">
    <!--    &lt;&gt;   <> -->

    <resultmap name="bingTimeMap" type="BingTimeMap">
        <column name="sn_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="create_time" jdbctype="INTEGER" javatype="Integer" remark=""/>
    </resultmap>

    <operation name="getBingTimeBatch" resultmap="bingTimeMap" multiplicity="many" remark="批量查询绑定时间">
        SELECT
        log.sn_id,
        log.create_time
        FROM
        ( SELECT * FROM tp_equipment_sn_change_log WHERE handle_type = 3 ORDER BY create_time ASC ) log
        WHERE log.sn_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        log.sn_id
    </operation>
</table>
