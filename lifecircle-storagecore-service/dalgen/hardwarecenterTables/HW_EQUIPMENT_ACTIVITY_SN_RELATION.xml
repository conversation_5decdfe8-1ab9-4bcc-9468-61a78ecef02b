<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_ACTIVITY_SN_RELATION" physicalName="HW_EQUIPMENT_ACTIVITY_SN_RELATION"
       remark="硬件活动设备SN关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_ACTIVITY_SN_RELATION">
        INSERT INTO HW_EQUIPMENT_ACTIVITY_SN_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="isDayStandard != null">`IS_DAY_STANDARD`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="buyerId != null">`BUYER_ID`,</if>
            <if test="buyerType != null">`BUYER_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="isDayStandard != null">#{isDayStandard,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="buyerId != null">#{buyerId,jdbcType=INTEGER},</if>
            <if test="buyerType != null">#{buyerType,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="findEquipmentList" paramtype="primitive" multiplicity="paging" paging="HarvestPlanEquipmentList"
               remark="根据设备SN批量查询活动设备参与活动信息"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.HarvestPlanEquipmentDTO">
        SELECT
        relation.init_sn as equipmentSn,
        equipment.equipment_model as equipmentModel,
        activity.activity_name as activityName,
        relation.create_time as createTime,
        trade.first_trade_time as firstTradeTime
        FROM
        hw_equipment_activity_sn_relation relation
        LEFT JOIN hw_equipment_activity_sn_first_trade trade ON relation.init_sn = trade.init_sn
        LEFT JOIN hw_equipment equipment ON relation.equipment_id = equipment.id
        LEFT JOIN hw_equipment_activity activity ON relation.activity_id = activity.id
        WHERE
        relation.create_time &gt; #{startJoinTime,jdbcType=TIMESTAMP}
        AND relation.create_time &lt; #{endJoinTime,jdbcType=TIMESTAMP}
        <if test="initSn != null and initSn != '' ">
            AND relation.init_sn = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="activityId != null">
            AND relation.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            AND relation.equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="startTradeTime != null">
            AND trade.first_trade_time &gt; #{startTradeTime,jdbcType=INTEGER}
        </if>
        <if test="endTradeTime != null">
            AND trade.first_trade_time &lt; #{endTradeTime,jdbcType=INTEGER}
        </if>
        AND relation.is_del = 0
        order by relation.`id` desc
    </operation>

    <operation name="findInitSnListBySnList" paramtype="primitive" multiplicity="many" resulttype="java.lang.String"
               remark="批量查询参与活动的设备SN">
        SELECT
        INIT_SN
        FROM
        HW_EQUIPMENT_ACTIVITY_SN_RELATION
        WHERE INIT_SN IN
        <foreach collection="list" index="index" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND IS_DEL = 0
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入设备活动关联信息">
        INSERT INTO
        HW_EQUIPMENT_ACTIVITY_SN_RELATION
        (`INIT_SN`,`ACTIVITY_ID`,`EQUIPMENT_ID`,`BUYER_ID`,`BUYER_TYPE`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.initSn,jdbcType=VARCHAR},
            #{item.activityId,jdbcType=INTEGER},
            #{item.equipmentId,jdbcType=INTEGER},
            #{item.buyerId,jdbcType=INTEGER},
            #{item.buyerType,jdbcType=INTEGER}
            )
        </foreach>
    </operation>
</table>
