<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_SN" physicalName="HW_EQUIPMENT_SN"
       remark="硬件sn码记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_SN">
        INSERT INTO HW_EQUIPMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="systemSn != null">`SYSTEM_SN`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="snStatus != null">`SN_STATUS`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="systemSn != null">#{systemSn,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="snStatus != null">#{snStatus,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByInitSn" paramtype="primitive" multiplicity="one" remark="根据设备SN码查询信息">
        select * from hw_equipment_sn where init_sn = #{initSn,jdbcType=VARCHAR} and is_del = 0
    </operation>

    <operation name="getByInitSnList" multiplicity="many" remark="根据 initSn 批量查询 设备SN 信息">
        SELECT *
        FROM hw_equipment_sn
        WHERE init_sn IN
        <foreach close=")" collection="list" index="index" item="initSn" open="(" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND is_del = 0
    </operation>

    <resultmap name="EquipmentSnInfoMap" type="EquipmentSnInfoMap">
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
    </resultmap>

    <operation name="getEquipmentSnByStorageOrder" multiplicity="many"
               resultmap="EquipmentSnInfoMap" remark="根据订单号查询设备sn信息">
        SELECT a.system_sn equipment_sn,d.equipment_name,d.equipment_model
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on a.id = b.sn_id
        LEFT JOIN hw_equipment_storage_order c on b.order_no = c.storage_order
        LEFT JOIN hw_equipment d on d.id = a.equipment_id
        where c.storage_order = #{storageOrder,jdbcType=VARCHAR}
    </operation>
    <resultmap name="GetAllBySearchParamMap" type="GetAllBySearchParamMap">
        <column name="sn_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="sn表自增id"/>
        <column name="equipment_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="sn_status" javatype="java.lang.Integer" jdbctype="TINYINT" remark="设备状态"/>
        <column name="agent_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="代理商名称"/>
        <column name="grant_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="授理商名称"/>
        <column name="merchant_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="商户名称"/>
        <column name="store_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="门店名称"/>
        <column name="last_bind_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="最近绑定时间"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
        <column name="trade_mode" javatype="java.lang.Integer" jdbctype="TINYINT" remark="交易模式 1售卖 2押金"/>
        <column name="market_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="营销人员名称"/>
    </resultmap>

    <operation name="getAllBySearchParamForDownload" paramtype="primitive" multiplicity="many"
               resultmap="GetAllBySearchParamMap" remark="根据查询条件查询所有设备">
        SELECT esn.id as sn_id,esn.init_sn as equipment_sn,e.equipment_name,e.equipment_model,esn.sn_status,u1.username
        as agent_name,esn.trade_mode,
        u2.username as grant_name,us.username as merchant_name
        ,store.store_name,bind_time as last_bind_time,esn.depot,u3.username as market_name
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment e on esn.equipment_id = e.id
        LEFT JOIN tp_users us on us.id = esn.uid
        LEFT JOIN tp_user u1 on u1.id = us.belong
        LEFT JOIN tp_user u2 on u2.id = us.salesman
        LEFT JOIN tp_user u3 on u3.id = us.market_id
        LEFT JOIN tp_lifecircle_store store on store.store_id = esn.store_id
        where 1=1
        <if test="equipmentSn!=null and equipmentSn!=''">
            AND esn.init_sn = #{equipmentSn,jdbcType=VARCHAR}
        </if>
        <if test="startBindTime!=null and endBindTime!=null">
            AND esn.bind_time BETWEEN #{startBindTime,jdbcType=TIMESTAMP} AND #{endBindTime,jdbcType=TIMESTAMP}
        </if>
        <if test="startCreateTime!=null and endCreateTime!=null">
            AND esn.create_time BETWEEN #{startCreateTime,jdbcType=TIMESTAMP} AND #{endCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="ownRun!=null and ownRun!=-1">
            AND u1.own_run = #{ownRun,jdbcType=TINYINT}
        </if>
        <if test="depot!=null and depot!=-1">
            AND esn.depot = #{depot,jdbcType=TINYINT}
        </if>
        <if test="depotIdList!=null and depotIdList.size() > 0">
            AND esn.depot in
            <foreach collection="depotIdList" open="(" close=")" item="depotItem" separator=",">
                #{depotItem,jdbcType=TINYINT}
            </foreach>
        </if>
        <if test="snStatus!=null and snStatus!=-1">
            AND esn.sn_status = #{snStatus,jdbcType=TINYINT}
        </if>
        <if test="tradeMode!=null and tradeMode!=-1">
            AND esn.trade_mode = #{tradeMode,jdbcType=TINYINT}
        </if>
        <if test="equipmentName!=null and equipmentName!=''">
            AND e.equipment_name = #{equipmentName,jdbcType=VARCHAR}
        </if>
        <if test="equipmentNameList != null and equipmentNameList.size() > 0">
            AND e.equipment_name in
            <foreach collection="equipmentNameList" open="(" close=")" item="equipmentNameItem" separator=",">
                #{equipmentNameItem,jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="equipmentModel!=null and equipmentModel!=''">
            AND e.equipment_model = #{equipmentModel,jdbcType=VARCHAR}
        </if>
        <if test="agentId!=null and agentId!=0">
            AND esn.agent_id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="grantId!=null and grantId!=0">
            AND esn.grant_id = #{grantId,jdbcType=INTEGER}
        </if>
        <if test="marketId!=null and marketId!=0">
            AND esn.market_id = #{marketId,jdbcType=INTEGER}
        </if>
        <if test="uid!=null and uid!=0">
            AND esn.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="storeId!=null and storeId!=0">
            AND esn.store_id = #{storeId,jdbcType=INTEGER}
        </if>
        <if test="oemId !=null">
            AND esn.oem_id = #{oemId,jdbcType=INTEGER}
        </if>
        order by esn.create_time desc
    </operation>

    <operation name="associationAgent" paramtype="primitive" remark="设备关联代理商">
        UPDATE hw_equipment_sn
        SET
        agent_id = #{agentId,jdbcType=INTEGER},
        oem_id = #{oemId,jdbcType=INTEGER},
        oem_name = #{oemName,jdbcType=VARCHAR},
        grant_id = #{grantId,jdbcType=INTEGER}
        WHERE init_sn = #{initSn,jdbcType=VARCHAR}
        AND is_del = 0
    </operation>

    <resultmap name="EquipmentStockExportMap" type="EquipmentStockExportMap">
        <column name="id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备sn码id"/>
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="初始sn码"/>
        <column name="system_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备sn码"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备id"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="INTEGER" remark="仓库位置"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="order_no" javatype="java.lang.String" jdbctype="VARCHAR" remark="入库订单号"/>
        <column name="stock_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="采购订单号"/>
        <column name="create_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="创建时间"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="入库类型"/>
        <column name="operate_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="操作类型"/>
        <column name="back_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="回库时间"/>
        <column name="purchase_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="采购入库时间"/>
        <column name="unit_price" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="单价"/>
    </resultmap>

    <operation name="equipmentStockExport" resultmap="EquipmentStockExportMap" multiplicity="many" remark="硬件库存一键导出">
        select id,init_sn,equipment_id,depot,equipment_name,equipment_model,order_no,stock_order,create_time,
        biz_type,operate_type,unit_price,back_time,purchase_time
        from (select a.id,a.init_sn,a.equipment_id,a.depot,c.equipment_name,c.equipment_model,
        b.order_no,b.stock_order,b.create_time,d.biz_type,d.operate_type,d.unit_price,d.execute_time
        back_time,e.execute_time
        purchase_time
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on b.sn_id = a.id and b.is_del = 0
        LEFT JOIN hw_equipment c on c.id = a.equipment_id
        LEFT JOIN hw_equipment_storage_order d on d.storage_order = b.order_no and d.is_del = 0
        LEFT JOIN hw_equipment_stock e on e.stock_order = b.stock_order
        where a.sn_status = 2 and a.is_del = 0
        and a.depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
        order by b.create_time desc) f group by id
    </operation>

    <resultmap name="InEuipmentSnMap" type="InEuipmentSnMap">
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备码"/>
        <column name="unit_price" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="采购价"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="stock_order" javatype="java.lang.String" jdbctype="VARCHAR" remark="入库订单号"/>
        <column name="order_no" javatype="java.lang.String" jdbctype="VARCHAR" remark="采购订单号"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
        <column name="execute_time" javatype="java.util.Date" jdbctype="TIMESTAMP" remark="执行时间"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="入库类型"/>
    </resultmap>
    <operation name="getBySnList" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap"
               remark="根据snList查询">
        select init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= #{orderType,jdbcType=TINYINT}
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc limit 10000) a
        group by a.init_sn
    </operation>

    <operation name="getBySnListForMiddle" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap"
               remark="根据snList查询">
        SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= #{orderType,jdbcType=TINYINT}
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc
    </operation>

    <operation name="equipmentStockExportBySnList" resultmap="EquipmentStockExportMap" multiplicity="many"
               remark="带参数硬件库存一键导出">
        select id,init_sn,system_sn,equipment_id,depot,equipment_name,equipment_model,order_no,stock_order,create_time,
        biz_type,operate_type,unit_price,back_time,purchase_time
        from (select a.id,a.init_sn,a.system_sn,a.equipment_id,a.depot,c.equipment_name,c.equipment_model,
        b.order_no,b.stock_order,b.create_time,d.biz_type,d.operate_type,d.unit_price,d.execute_time
        back_time,e.execute_time
        purchase_time
        from hw_equipment_sn a
        LEFT JOIN hw_equipment_order_relation b on b.sn_id = a.id and b.is_del = 0
        LEFT JOIN hw_equipment c on c.id = a.equipment_id
        LEFT JOIN hw_equipment_storage_order d on d.storage_order = b.order_no and d.is_del = 0
        LEFT JOIN hw_equipment_stock e on e.stock_order = b.stock_order
        where
        d.depot in
        <foreach close=")" collection="list" index="index" item="depot" open="(" separator=",">
            #{depot,jdbcType=INTEGER}
        </foreach>
        and a.init_sn in
        <foreach collection="snList" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by b.create_time desc limit 10000) f group by id
    </operation>

    <resultmap name="ByEquipmentIdMap" type="ByEquipmentIdMap">
        <column name="num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="数量"/>
        <column name="biz_type" javatype="java.lang.Integer" jdbctype="TINYINT" remark="类型"/>
        <column name="equipment_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="设备属性id"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
    </resultmap>

    <!--查询期初设备信息-->
    <operation name="getBySnListDepot" paramtype="primitive" multiplicity="many" resultmap="ByEquipmentIdMap">
        SELECT count(*) num,esn.equipment_id,esn.depot
        from hw_equipment_sn esn
        where esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        GROUP BY esn.depot,esn.equipment_id
    </operation>

    <resultmap name="GetModelByListMap" type="GetModelByListMap">
        <column name="init_sn" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备码"/>
        <column name="equipment_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备名称"/>
        <column name="equipment_model" javatype="java.lang.String" jdbctype="VARCHAR" remark="设备型号"/>
        <column name="equipment_price" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="设备价格"/>
        <column name="depot" javatype="java.lang.Integer" jdbctype="TINYINT" remark="仓库位置"/>
    </resultmap>

    <operation name="getModelByList" paramtype="primitive" multiplicity="many" resultmap="GetModelByListMap"
               remark="查询设备属性">
        select esn.depot,esn.init_sn,e.equipment_name,e.equipment_model,e.equipment_price from hw_equipment_sn esn left
        join
        hw_equipment e on e.id = esn.equipment_id
        where esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getBySnListForIn" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap"
               remark="时间段内入库查询">
        select init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,rage.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= 1
        and rage.biz_type !=1
        and lation.id is not null
        and rage.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by rage.execute_time desc limit 10000) a
        group by a.init_sn
    </operation>

    <operation name="getBySnListForStock" paramtype="primitive" multiplicity="many" resultmap="InEuipmentSnMap"
               remark="根据stock表executeTime查询">
        select init_sn,unit_price,equipment_name,equipment_model,stock_order,order_no,depot,execute_time,biz_type from
        (SELECT
        esn.init_sn,rage.unit_price,e.equipment_name,e.equipment_model,lation.stock_order,lation.order_no,esn.depot,stock.execute_time,rage.biz_type
        from hw_equipment_sn esn
        LEFT JOIN hw_equipment_order_relation lation on lation.sn_id = esn.id
        LEFT JOIN hw_equipment_storage_order rage on rage.storage_order = lation.order_no
        LEFT JOIN hw_equipment_stock stock on stock.stock_order = lation.stock_order
        LEFT JOIN hw_equipment e on e.id = esn.equipment_id
        where
        rage.order_type= 1
        and rage.biz_type = 1
        and stock.id is not null
        and stock.execute_time BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        and esn.init_sn in
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
        order by stock.execute_time desc limit 10000) a
        group by a.init_sn
    </operation>

    <operation name="findByInitSnList" paramtype="primitive" multiplicity="many" remark="根据设备SN批量查询"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.EquipmentSnDTO">
        SELECT sn.init_sn AS equipmentSn,
        sn.equipment_id AS equipmentId,
        equipment.equipment_model AS equipmentModel
        FROM hw_equipment_sn AS sn
        LEFT JOIN hw_equipment AS equipment ON sn.equipment_id = equipment.`id`
        WHERE sn.init_sn IN
        <foreach collection="list" index="index" item="initSn" open="(" close=")" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
        AND sn.is_del = 0
    </operation>

    <operation name="findSnAndUserTypeList" paramtype="primitive" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.EquipmentSnBelongDTO"
               remark="查询设备归属代理商信息集合">
        SELECT
        tuser.id userId,
        tuser.username username,
        tuser.type type,
        sn.init_sn initSn
        FROM
        hw_equipment_sn sn
        LEFT JOIN tp_user tuser ON sn.agent_id = tuser.id
        WHERE
        tuser.type != null
        AND sn.init_sn IN
        <foreach collection="list" item="sn" open="(" close=")" separator=",">
            #{sn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="updateSnBelongByVersion" paramtype="primitive" remark="修改设备归属">
        update hw_equipment_sn
        set
        agent_id = #{agentId,jdbcType=INTEGER},
        grant_id = 0,
        update_time = now()
        where init_sn = #{initSn,jdbcType=VARCHAR}
        and is_del = 0
        and agent_id = #{beforeAgentId,jdbcType=INTEGER}
    </operation>

    <operation name="updateEquipmentSnById" paramtype="object" remark="修改信息">
        UPDATE
        hw_equipment_sn
        <set>
            <if test="tpKey != null">
                tp_key = #{tpKey,jdbcType=INTEGER},
            </if>
            <if test="initSn != null">
                init_sn = #{initSn,jdbcType=VARCHAR},
            </if>
            <if test="systemSn != null">
                system_sn = #{systemSn,jdbcType=VARCHAR},
            </if>
            <if test="isDel != null">
                is_del = #{isDel,jdbcType=TINYINT},
            </if>
            <if test="agentId != null">
                agent_id = #{agentId,jdbcType=INTEGER},
            </if>
            <if test="grantId != null">
                grant_id = #{grantId,jdbcType=INTEGER},
            </if>
            <if test="marketId != null">
                market_id = #{marketId,jdbcType=INTEGER},
            </if>
            <if test="uid != null">
                uid = #{uid,jdbcType=INTEGER},
            </if>
            <if test="storeId != null">
                store_id = #{storeId,jdbcType=INTEGER},
            </if>
            <if test="cashierId != null">
                cashier_id = #{cashierId,jdbcType=INTEGER},
            </if>
            <if test="cashierMode != null">
                cashier_mode = #{cashierMode,jdbcType=TINYINT},
            </if>
            <if test="depot != null">
                depot = #{depot,jdbcType=TINYINT},
            </if>
            <if test="channelType != null">
                channel_type = #{channelType,jdbcType=TINYINT},
            </if>
            <if test="tradeMode != null">
                trade_mode = #{tradeMode,jdbcType=TINYINT},
            </if>
            <if test="payType != null">
                pay_type = #{payType,jdbcType=TINYINT},
            </if>
            <if test="truePrice != null">
                true_price = #{truePrice,jdbcType=DECIMAL},
            </if>
            <if test="printerSetting != null">
                printer_setting = #{printerSetting,jdbcType=TINYINT},
            </if>
            <if test="printerMasterSetting != null">
                printer_master_setting = #{printerMasterSetting,jdbcType=TINYINT},
            </if>
            <if test="bindTime != null">
                bind_time = #{bindTime,jdbcType=INTEGER},
            </if>
            <if test="unbindTime != null">
                unbind_time = #{unbindTime,jdbcType=INTEGER},
            </if>
            <if test="snStatus != null">
                sn_status = #{snStatus,jdbcType=TINYINT},
            </if>
            <if test="operateType != null">
                operate_type = #{operateType,jdbcType=TINYINT},
            </if>
            <if test="equipmentId != null">
                equipment_id = #{equipmentId,jdbcType=INTEGER},
            </if>
            <if test="createTime != null">
                create_time = #{createTime,jdbcType=TIMESTAMP},
            </if>
            <if test="updateTime != null">
                update_time = #{updateTime,jdbcType=TIMESTAMP},
            </if>
            <if test="distributeTime != null">
                distribute_time = #{distributeTime,jdbcType=TIMESTAMP},
            </if>
            <if test="receiveTime != null">
                receive_time = #{receiveTime,jdbcType=TIMESTAMP},
            </if>
            <if test="backTime != null">
                back_time = #{backTime,jdbcType=TIMESTAMP},
            </if>
            <if test="equipmentAliasName != null">
                equipment_alias_name = #{equipmentAliasName,jdbcType=VARCHAR},
            </if>
            <if test="codeType != null">
                code_type = #{codeType,jdbcType=TIMESTAMP},
            </if>
        </set>
        WHERE
        id= #{id,jdbcType=INTEGER}
    </operation>

    <operation name="updateRecycleStatusBySnList" remark="根据SN更新设备信息">
        UPDATE hw_equipment_sn
        set recycle_status = #{recycleStatus,jdbcType=TINYINT}
        where init_sn in
        <foreach open="(" close=")" collection="snList" item="initSn" separator=",">
            #{initSn,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>