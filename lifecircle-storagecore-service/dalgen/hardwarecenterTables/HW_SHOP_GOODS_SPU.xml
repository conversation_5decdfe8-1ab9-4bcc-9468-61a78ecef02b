<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_SHOP_GOODS_SPU" physicalName="HW_SHOP_GOODS_SPU"
       remark="硬件商城商品SPU表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_SHOP_GOODS_SPU">
        INSERT INTO HW_SHOP_GOODS_SPU
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="picture != null">`PICTURE`,</if>
            <if test="goodsDesc != null">`GOODS_DESC`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="goodsSpuId != null">`GOODS_SPU_ID`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isGroup != null">`IS_GROUP`,</if>
            <if test="priceType != null">`PRICE_TYPE`,</if>
            <if test="spuStatus != null">`SPU_STATUS`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="groupNumber != null">`GROUP_NUMBER`,</if>
            <if test="isInstallment != null">`IS_INSTALLMENT`,</if>
            <if test="isContainsExpress != null">`IS_CONTAINS_EXPRESS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="spuPrice != null">`SPU_PRICE`,</if>
            <if test="spuWeight != null">`SPU_WEIGHT`,</if>
            <if test="categoryIds != null">`CATEGORY_IDS`,</if>

        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="goodsDesc != null">#{goodsDesc,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="goodsSpuId != null">#{goodsSpuId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isGroup != null">#{isGroup,jdbcType=TINYINT},</if>
            <if test="priceType != null">#{priceType,jdbcType=TINYINT},</if>
            <if test="spuStatus != null">#{spuStatus,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="groupNumber != null">#{groupNumber,jdbcType=INTEGER},</if>
            <if test="isInstallment != null">#{isInstallment,jdbcType=TINYINT},</if>
            <if test="isContainsExpress != null">#{isContainsExpress,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="spuPrice != null">#{spuPrice,jdbcType=DECIMAL},</if>
            <if test="spuWeight != null">#{spuWeight,jdbcType=DECIMAL},</if>
            <if test="categoryIds != null">#{categoryIds,jdbcType=VARCHAR},</if>

        </trim>
    </operation>

    <!--根据条件模糊查询商品列表-->
    <operation name="findGoodsList" multiplicity="paging"
               paging="pageQueryGoodsListParam" paramtype="primitive" remark="根据条件查询ka账号列表">
        select * from HW_SHOP_GOODS_SPU
        where is_del = 0
        <if test="goodsName != null">
            and goods_name LIKE CONCAT ('%', #{goodsName,jdbcType=VARCHAR}, '%')
        </if>
        <if test="categoryIds != null and categoryIds != ''">
            AND
            (
            category_ids = #{categoryIds,jdbcType=VARCHAR} or
            <foreach collection="categoryIds.split(',')" index="index" item="item" open="(" separator="OR"
                     close=")">
                FIND_IN_SET(#{item,jdbcType=VARCHAR},category_ids)
            </foreach>
            )
        </if>
        <if test="isInstallment != null">
            and is_installment = #{isInstallment,jdbcType=INTEGER}
        </if>
        <if test="equipmentId != null">
            and equipment_id = #{equipmentId,jdbcType=INTEGER}
        </if>
        <if test="spuStatus != null">
            and spu_status = #{spuStatus,jdbcType=INTEGER}
        </if>
        <if test="isContainsExpress != null">
            and is_contains_express = #{isContainsExpress,jdbcType=INTEGER}
        </if>
        <if test="isTest != null">
            and is_test = #{isTest, jdbcType=INTEGER}
        </if>
        ORDER BY sort , create_time ASC
    </operation>

    <!--根据商品名称查询商品-->
    <operation name="getAllGoodsByGoodsName" multiplicity="many" resulttype="java.lang.String"
               remark="根据商品名称模糊查询商品">
        select goods_spu_id from HW_SHOP_GOODS_SPU
        where goods_name like concat('%', #{goodsName,jdbcType=VARCHAR} ,'%')
    </operation>

    <!--根据商品名称查询商品-->
    <operation name="getGoodsByGoodsName" multiplicity="one" remark="根据商品名称查询商品">
        select * from HW_SHOP_GOODS_SPU
        where goods_name = #{goodsName,jdbcType=VARCHAR}
        limit 1
    </operation>

    <!--根据商品id查询商品-->
    <operation name="getGoodsById" multiplicity="one" remark="根据商品id查询商品">
        select * from HW_SHOP_GOODS_SPU
        where id = #{id,jdbcType=INTEGER}
    </operation>

    <!--根据商品id查询商品-->
    <operation name="getGoodsByGoodsSpuId" multiplicity="one" remark="根据商品id查询商品">
        select * from HW_SHOP_GOODS_SPU
        where goods_spu_id = #{goodsSpuId,jdbcType=VARCHAR}
        and is_del = 0
    </operation>


    <operation name="getMaterialPartOfCombineSpu" paramtype="primitive" multiplicity="many"
               remark="获取套餐商品中关联物料商品的spu信息">
        select a.*
        from hw_shop_goods_spu a
        where a.id in (select b.equipment_id
        from hw_shop_goods_combined_relation b
        where b.goods_spu_id = #{goodsSpuId, jdbcType=VARCHAR}
        and b.product_type = 2
        and b.is_del = 0)
        and a.is_del = 0
    </operation>
</table>
