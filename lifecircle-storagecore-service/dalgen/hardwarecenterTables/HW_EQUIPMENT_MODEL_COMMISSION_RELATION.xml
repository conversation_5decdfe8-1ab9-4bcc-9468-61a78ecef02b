<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_MODEL_COMMISSION_RELATION" physicalName="HW_EQUIPMENT_MODEL_COMMISSION_RELATION"
       remark="活动硬件设备佣金关系表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_MODEL_COMMISSION_RELATION">
        INSERT INTO HW_EQUIPMENT_MODEL_COMMISSION_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="isNeedParseUrl != null">`IS_NEED_PARSE_URL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="commission != null">`COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="isNeedParseUrl != null">#{isNeedParseUrl,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="commission != null">#{commission,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findEquipmentCommissionList" paramtype="primitive" multiplicity="many" remark="查询全部设备最高返佣金额">
        SELECT
        *
        FROM
        HW_EQUIPMENT_MODEL_COMMISSION_RELATION
        WHERE IS_DEL = 0
    </operation>

    <operation name="getByActivityIdAndEquipmentIdAndProductType" paramtype="primitive" multiplicity="one" remark="查询全部设备最高返佣金额">
        SELECT *
        FROM `hw_equipment_model_commission_relation`
        WHERE activity_id = #{activityId,jdbcType=INTEGER}
        AND equipment_id = #{equipmentId,jdbcType=INTEGER}
        AND product_type = #{product_type,jdbcType=INTEGER}
        AND is_del = 0
        LIMIT 1
    </operation>

</table>
