<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_MAINTENANCE_RECORD" physicalName="HW_EQUIPMENT_MAINTENANCE_RECORD"
       remark="设备维保记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_MAINTENANCE_RECORD">
        INSERT INTO HW_EQUIPMENT_MAINTENANCE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="rightId != null">`RIGHT_ID`,</if>
            <if test="oldInitSn != null">`OLD_INIT_SN`,</if>
            <if test="done != null">`DONE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="rightId != null">#{rightId,jdbcType=VARCHAR},</if>
            <if test="oldInitSn != null">#{oldInitSn,jdbcType=VARCHAR},</if>
            <if test="done != null">#{done,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findDeviceMaintenanceList" multiplicity="many">
        select a.* from hw_equipment_maintenance_record a
        inner join hw_equipment_sn b on a.init_sn=b.init_sn
        where a.is_del=0
        <if test="initSn != null and initSn!=''">
            and a.init_sn=#{initSn,jdbcType=VARCHAR}
        </if>
        <if test="oldInitSn !=null and oldInitSn!=''">
            and a.old_init_sn=#{oldInitSn,jdbcType=VARCHAR}
        </if>

        <if test="equipmentId !=null">
            and b.equipment_id=#{equipmentId,jdbcType=INTEGER}
        </if>

        <if test="startTime !=null and endTime !=null">
            and a.create_time between #{startTime,jdbcType=TIMESTAMP}
            and #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by a.id desc limit 10000
    </operation>

</table>
