<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_WORK_BATCH" physicalName="HW_EQUIPMENT_WORK_BATCH"
       remark="设备维护批次表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_WORK_BATCH">
        INSERT INTO HW_EQUIPMENT_WORK_BATCH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="workBatchNo != null">`WORK_BATCH_NO`,</if>
            <if test="importFileUrl != null">`IMPORT_FILE_URL`,</if>
            <if test="operatorNumber != null">`OPERATOR_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="workBatchNo != null">#{workBatchNo,jdbcType=VARCHAR},</if>
            <if test="importFileUrl != null">#{importFileUrl,jdbcType=VARCHAR},</if>
            <if test="operatorNumber != null">#{operatorNumber,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
