<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_WORK_ORDER" physicalName="HW_EQUIPMENT_WORK_ORDER"
       remark="设备工单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_WORK_ORDER">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="auditRemark != null">`AUDIT_REMARK`,</if>
            <if test="logisticsNo != null">`LOGISTICS_NO`,</if>
            <if test="orderRemark != null">`ORDER_REMARK`,</if>
            <if test="workBatchNo != null">`WORK_BATCH_NO`,</if>
            <if test="workOrderSn != null">`WORK_ORDER_SN`,</if>
            <if test="abnormalRemark != null">`ABNORMAL_REMARK`,</if>
            <if test="maintenanceRemark != null">`MAINTENANCE_REMARK`,</if>
            <if test="pend != null">`PEND`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="backWay != null">`BACK_WAY`,</if>
            <if test="fbUnbind != null">`FB_UNBIND`,</if>
            <if test="workType != null">`WORK_TYPE`,</if>
            <if test="workerId != null">`WORKER_ID`,</if>
            <if test="zfbUnbind != null">`ZFB_UNBIND`,</if>
            <if test="assignTime != null">`ASSIGN_TIME`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="abnormalType != null">`ABNORMAL_TYPE`,</if>
            <if test="backWarehouse != null">`BACK_WAREHOUSE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="auditRemark != null">#{auditRemark,jdbcType=VARCHAR},</if>
            <if test="logisticsNo != null">#{logisticsNo,jdbcType=VARCHAR},</if>
            <if test="orderRemark != null">#{orderRemark,jdbcType=VARCHAR},</if>
            <if test="workBatchNo != null">#{workBatchNo,jdbcType=VARCHAR},</if>
            <if test="workOrderSn != null">#{workOrderSn,jdbcType=VARCHAR},</if>
            <if test="abnormalRemark != null">#{abnormalRemark,jdbcType=VARCHAR},</if>
            <if test="maintenanceRemark != null">#{maintenanceRemark,jdbcType=VARCHAR},</if>
            <if test="pend != null">#{pend,jdbcType=TINYINT},</if>
            <if test="source != null">#{source,jdbcType=TINYINT},</if>
            <if test="backWay != null">#{backWay,jdbcType=TINYINT},</if>
            <if test="fbUnbind != null">#{fbUnbind,jdbcType=TINYINT},</if>
            <if test="workType != null">#{workType,jdbcType=TINYINT},</if>
            <if test="workerId != null">#{workerId,jdbcType=INTEGER},</if>
            <if test="zfbUnbind != null">#{zfbUnbind,jdbcType=TINYINT},</if>
            <if test="assignTime != null">#{assignTime,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="abnormalType != null">#{abnormalType,jdbcType=TINYINT},</if>
            <if test="backWarehouse != null">#{backWarehouse,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        </trim>
    </operation>


    <operation name="batchInsert" paramtype="objectList" remark="批量新增">
        INSERT INTO HW_EQUIPMENT_WORK_ORDER
        (INIT_SN,AGENT_ID,GRANT_ID,WORKER_ID,WORK_TYPE,ORDER_REMARK,WORK_BATCH_NO,WORK_ORDER_SN,SOURCE,FB_UNBIND,ZFB_UNBIND,ORDER_STATUS,ASSIGN_TIME,STORE_ID)
        values
        <foreach collection="list" item="order" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{order.initSn,jdbcType=VARCHAR},
                #{order.agentId,jdbcType=INTEGER},
                #{order.grantId,jdbcType=INTEGER},
                #{order.workerId,jdbcType=INTEGER},
                #{order.workType,jdbcType=TINYINT},
                #{order.orderRemark,jdbcType=VARCHAR},
                #{order.workBatchNo,jdbcType=VARCHAR},
                #{order.workOrderSn,jdbcType=VARCHAR},
                #{order.source,jdbcType=TINYINT},
                #{order.fbUnbind,jdbcType=TINYINT},
                #{order.zfbUnbind,jdbcType=TINYINT},
                #{order.orderStatus,jdbcType=TINYINT},
                #{order.assignTime,jdbcType=INTEGER},
                #{order.storeId,jdbcType=INTEGER},
            </trim>
        </foreach>
    </operation>

    <operation name="updateSelective" paramtype="object">
        update hw_equipment_work_order
        <trim prefix="set" suffixOverrides=",">
            <if test="orderRemark != null">`ORDER_REMARK`=#{orderRemark,jdbcType=VARCHAR},</if>
            <if test="workerId != null">`WORKER_ID`=#{workerId,jdbcType=INTEGER},</if>
        </trim>
        where work_order_sn=#{workOrderSn,jdbcType=VARCHAR}
    </operation>

    <operation name="getByInitSnAndStatusNotEnd">
        SELECT * FROM HW_EQUIPMENT_WORK_ORDER
        WHERE INIT_SN = #{initSn,jdbcType=VARCHAR}
        and IS_DEL = 0 and order_status !=5 order by id desc limit 1
    </operation>

    <operation name="countUnFinishByInitSn" resulttype="java.lang.Integer" remark="根据设备SN查询未完成的工单">"
        select count(*)
        from hw_equipment_work_order
        where init_sn=#{initSn,jdbcType=VARCHAR} and is_del=0 and order_status != 5
    </operation>

    <operation name="getLastWorkOrderByInitSn" remark="查询设备最后一条工单">
        SELECT * FROM HW_EQUIPMENT_WORK_ORDER
        WHERE INIT_SN = #{initSn,jdbcType=VARCHAR}
        and IS_DEL = 0 order by id desc limit 1
    </operation>

</table>
