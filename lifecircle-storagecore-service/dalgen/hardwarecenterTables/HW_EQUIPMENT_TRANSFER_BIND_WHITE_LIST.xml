<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST" physicalName="HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST"
       remark="硬件设备换绑白名单">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST">
        INSERT INTO HW_EQUIPMENT_TRANSFER_BIND_WHITE_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="afterUserId != null">`AFTER_USER_ID`,</if>
            <if test="beforeUserId != null">`BEFORE_USER_ID`,</if>
            <if test="effectiveStatus != null">`EFFECTIVE_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="afterUserId != null">#{afterUserId,jdbcType=INTEGER},</if>
            <if test="beforeUserId != null">#{beforeUserId,jdbcType=INTEGER},</if>
            <if test="effectiveStatus != null">#{effectiveStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="batchInsert" paramtype="objectList" remark="批量保存">
        insert into hw_equipment_transfer_bind_white_list
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `INIT_SN`,
            `OPERATOR`,
            `OPERATOR_NAME`,
            `AFTER_USER_ID`,
            `BEFORE_USER_ID`,
            `EFFECTIVE_STATUS`,
        </trim>
        VALUES
        <foreach collection="list" item="item" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.initSn,jdbcType=VARCHAR},
                #{item.operator,jdbcType=VARCHAR},
                #{item.operatorName,jdbcType=VARCHAR},
                #{item.afterUserId,jdbcType=INTEGER},
                #{item.beforeUserId,jdbcType=INTEGER},
                #{item.effectiveStatus,jdbcType=TINYINT},
            </trim>
        </foreach>
    </operation>

    <operation name="findByInitSn" paramtype="primitive" multiplicity="many" remark="根据sn查询名单">
        select
        *
        from hw_equipment_transfer_bind_white_list
        where is_del = 0
        and init_sn = #{initSn,jdbcType=VARCHAR}
        <if test="effectiveStatus != null">
            and effective_status = #{effectiveStatus, jdbcType=TINYINT}
        </if>
    </operation>

    <operation name="findByInitSnList" paramtype="primitive" multiplicity="many" remark="根据sn查询名单">
        select
        *
        from hw_equipment_transfer_bind_white_list
        where is_del = 0
        and init_sn in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        <if test="effectiveStatus != null">
            and effective_status = #{effectiveStatus, jdbcType=TINYINT}
        </if>
    </operation>

    <operation name="batchUpdateByIdList" paramtype="primitive" remark="批量修改">
        update hw_equipment_transfer_bind_white_list
        set update_time = now()
        <if test="effectiveStatus != null">
            effective_status = #{effectiveStatus, jdbcType=TINYINT}
        </if>
        where id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </operation>
</table>
