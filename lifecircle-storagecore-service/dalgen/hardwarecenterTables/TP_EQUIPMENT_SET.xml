<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_SET" physicalName="TP_EQUIPMENT_SET"
       remark="硬件信息设置表">
    <!--    &lt;&gt;   <> -->

    <resultmap name="equipmentModelMap" type="EquipmentModelMap">
        <column name="equipment_model" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="equipment_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
    </resultmap>

    <operation name="getEquipmentModelBatch" resultmap="equipmentModelMap" multiplicity="many" remark="批量查询设备型号">
        SELECT
        eset.equipment_id,
        eset.equipment_model
        FROM
        ( SELECT * FROM tp_equipment_set WHERE start_time &lt; UNIX_TIMESTAMP( NOW( ) ) AND is_del = 0 ORDER BY
        create_time ASC ) eset
        WHERE eset.equipment_id IN
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
        GROUP BY
        eset.equipment_id
    </operation>

    <operation name="getEquipmentModelById" multiplicity="many" remark="根据设备id获取硬件型号列表">
        SELECT id,equipment_model from
        (SELECT eset.* from tp_equipment eq
        LEFT JOIN tp_equipment_set eset on eq.id = eset.equipment_id
        where eq.equipment_name_id = #{equipmentId,jdbcType=INTEGER}
        and start_time &lt; UNIX_TIMESTAMP(NOW()) and eset.is_del = 0 and eq.is_del = 0
        ORDER BY eset.create_time DESC) e
        GROUP BY e.equipment_id
    </operation>

    <operation name="getEquipmentModelList" multiplicity="many" remark="查询所有设备型号列表">
        select id,equipment_id,equipment_model
        from
        (select id,equipment_id,equipment_model
        from tp_equipment_set
        where start_time <![CDATA[<]]> unix_timestamp(now()) and is_del =0
        ORDER BY create_time DESC) e
        GROUP BY equipment_id
    </operation>
</table>
