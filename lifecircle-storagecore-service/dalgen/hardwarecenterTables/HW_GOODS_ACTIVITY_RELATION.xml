<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_GOODS_ACTIVITY_RELATION" physicalName="HW_GOODS_ACTIVITY_RELATION"
       remark="活动硬件物料佣金关系表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_GOODS_ACTIVITY_RELATION">
        INSERT INTO HW_GOODS_ACTIVITY_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="hwOrderSn != null">`HW_ORDER_SN`,</if>
            <if test="uid != null">`uid`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="spuId != null">`SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="goodsId != null">`GOODS_ID`,</if>
            <if test="bindMonth != null">`BIND_MONTH`</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="activityId != null">#{activityId,jdbcType=INTEGER},</if>
            <if test="hwOrderSn != null">#{hwOrderSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="spuId != null">#{spuId,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="bindMonth != null">#{bindMonth,jdbcType=VARCHAR}</if>

        </trim>
    </operation>

    <operation name="getGoodsActivityRelation" remark="获取物料信息">
        SELECT *
        FROM hw_goods_activity_relation
        WHERE  store_id = #{storeId,jdbcType=INTEGER}
        AND activity_id = #{activityId,jdbcType=INTEGER}
        AND goods_id = #{goodsId,jdbcType=VARCHAR}
        and is_del = 0
        limit 1
    </operation>

    <operation name="findGoodsCommissionList" multiplicity="paging" paramtype="primitive"
               paging="findGoodsCommissionList" remark="分页查询物料返佣金额关联关系列表"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.GoodsCommissionDTO">
        SELECT
        t1.id,
        t1.uid,
        t1.store_id AS storeId,
        t1.hw_order_sn AS hwOrderSn,
        t1.activity_id AS activityId,
        t1.create_time AS createTime,
        t1.bind_month,
        t2.activity_name AS activityName,
        t3.goods_name AS goodsName
        FROM
        `hw_goods_activity_relation` t1
        LEFT JOIN hw_equipment_activity t2 ON t1.activity_id = t2.id
        LEFT JOIN hw_shop_goods_spu t3 ON t1.spu_id = t3.id
        WHERE
        t1.is_del = 0
        <if test="startCreateTime != null">
            AND t1.create_time &gt; #{startCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endCreateTime != null">
            AND t1.create_time &lt; #{endCreateTime,jdbcType=TIMESTAMP}
        </if>
        <if test="uid != null">
            AND t1.uid = #{uid,jdbcType=INTEGER}
        </if>
        <if test="activityId != null">
            AND t1.activity_id = #{activityId,jdbcType=INTEGER}
        </if>
        <if test="goodsId != null and goodsId != '' ">
            AND t3.goods_spu_id = #{goodsId,jdbcType=VARCHAR}
        </if>
        <if test="hwOrderSn != null and hwOrderSn != ''">
            AND t1.hw_order_sn = #{hwOrderSn,jdbcType=VARCHAR}
        </if>
        <if test="bindMonth != null and bindMonth != ''">
            AND t1.bind_month = #{bindMonth,jdbcType=VARCHAR}
        </if>
        order by t1.create_time desc
    </operation>

</table>
