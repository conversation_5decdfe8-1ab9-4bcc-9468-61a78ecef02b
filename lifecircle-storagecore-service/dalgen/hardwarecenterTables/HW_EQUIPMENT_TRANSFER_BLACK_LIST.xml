<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_TRANSFER_BLACK_LIST" physicalName="HW_EQUIPMENT_TRANSFER_BLACK_LIST"
       remark="设备流转黑名单，在黑名单里的设备不允许流转给其他代理商">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_TRANSFER_BLACK_LIST">
        INSERT INTO HW_EQUIPMENT_TRANSFER_BLACK_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>



    <operation name="updateAgentById" paramtype="primitive" remark="根据id更新归属代理商">
        update HW_EQUIPMENT_TRANSFER_BLACK_LIST set agent_id = #{agentId,jdbcType=INTEGER}
        where id = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="getByInitSn" paramtype="primitive" multiplicity="one" remark="根据设备sn查询">
        select * from HW_EQUIPMENT_TRANSFER_BLACK_LIST where init_sn = #{nitSn,jdbcType=VARCHAR} limit 1
    </operation>

</table>
