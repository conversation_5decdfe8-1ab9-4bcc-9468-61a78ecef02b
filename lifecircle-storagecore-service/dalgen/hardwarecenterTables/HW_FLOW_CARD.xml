<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_FLOW_CARD" physicalName="HW_FLOW_CARD"
       remark="硬件流量卡表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_FLOW_CARD">
        INSERT INTO HW_FLOW_CARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="iccid != null">`ICCID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="simCard != null">`SIM_CARD`,</if>
            <if test="operator != null">`OPERATOR`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="cardType != null">`CARD_TYPE`,</if>
            <if test="totalFlow != null">`TOTAL_FLOW`,</if>
            <if test="beyondFlow != null">`BEYOND_FLOW`,</if>
            <if test="cardStatus != null">`CARD_STATUS`,</if>
            <if test="lastQueryDay != null">`LAST_QUERY_DAY`,</if>
            <if test="residualFlow != null">`RESIDUAL_FLOW`,</if>
            <if test="effectiveTime != null">`EFFECTIVE_TIME`,</if>
            <if test="expirationTime != null">`EXPIRATION_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="iccid != null">#{iccid,jdbcType=VARCHAR},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="simCard != null">#{simCard,jdbcType=VARCHAR},</if>
            <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
            <if test="totalFlow != null">#{totalFlow,jdbcType=INTEGER},</if>
            <if test="beyondFlow != null">#{beyondFlow,jdbcType=INTEGER},</if>
            <if test="cardStatus != null">#{cardStatus,jdbcType=TINYINT},</if>
            <if test="lastQueryDay != null">#{lastQueryDay,jdbcType=INTEGER},</if>
            <if test="residualFlow != null">#{residualFlow,jdbcType=INTEGER},</if>
            <if test="effectiveTime != null">#{effectiveTime,jdbcType=INTEGER},</if>
            <if test="expirationTime != null">#{expirationTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="queryCrmFlowCardInfoList" multiplicity="paging" paging="CrmFlowCardInfo"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.harvest.FlowCardListExportModel">
        SELECT a.init_sn as initSn,agent_id as agentId,uid as merchantId, `store_id` as storeId ,total_flow as
        totalFlow, residual_flow as residualFlow,beyond_flow as beyondFlow,operator,
        expiration_time as expirationTime,effective_time as effectiveTime,CARD_STATUS as cardStatus,card_type as
        cardType
        FROM HW_FLOW_CARD a
        LEFT JOIN `hw_equipment_sn` b on a.init_sn= b.`init_sn`
        where a.IS_DEL=0
        <if test="cardType!=null">
            and card_type = #{cardType,jdbcType=TINYINT}
        </if>
        <if test="agentId!=null">
            and `agent_id` = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="merchantId!=null">
            and `uid` = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="expireStartTime!=null and expireEndTime!=null">
            and expiration_time between #{expireStartTime,jdbcType=INTEGER} and #{expireEndTime,jdbcType=INTEGER}
        </if>
        <if test="initSn!=null and initSn!=''">
            and a.`init_sn` = #{initSn,jdbcType=VARCHAR}
        </if>
        <if test="iccid!=null and iccid!=''">
            and `iccid` = #{iccid,jdbcType=VARCHAR}
        </if>
        <if test="overFlow!=null">
            and #{overFlow,jdbcType=INTEGER} = #{overFlow,jdbcType=INTEGER}
        </if>
        <if test="overFlow==1">
            and BEYOND_FLOW > 0
        </if>
        <if test="overFlow==2">
            and BEYOND_FLOW =0
        </if>
        order by a.id
    </operation>

</table>
