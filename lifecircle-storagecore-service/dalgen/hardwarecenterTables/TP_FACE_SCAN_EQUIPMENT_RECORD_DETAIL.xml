<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL" physicalName="TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL"
    remark="扫脸设备消费记录明细表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL">
INSERT INTO TP_FACE_SCAN_EQUIPMENT_RECORD_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="createBy != null">`CREATE_BY`,</if>
        <if test="updateBy != null">`UPDATE_BY`,</if>
        <if test="equipmentSn != null">`EQUIPMENT_SN`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="codeScanNum != null">`CODE_SCAN_NUM`,</if>
        <if test="equipmentDau != null">`EQUIPMENT_DAU`,</if>
        <if test="faceScanDau != null">`FACE_SCAN_DAU`,</if>
        <if test="transactionNum != null">`TRANSACTION_NUM`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="transactionTime != null">`TRANSACTION_TIME`,</if>
        <if test="transactionMoney != null">`TRANSACTION_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
        <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
        <if test="equipmentSn != null">#{equipmentSn,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="codeScanNum != null">#{codeScanNum,jdbcType=INTEGER},</if>
        <if test="equipmentDau != null">#{equipmentDau,jdbcType=INTEGER},</if>
        <if test="faceScanDau != null">#{faceScanDau,jdbcType=INTEGER},</if>
        <if test="transactionNum != null">#{transactionNum,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionTime != null">#{transactionTime,jdbcType=TIMESTAMP},</if>
        <if test="transactionMoney != null">#{transactionMoney,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getByEquipmentTime" paramtype="primitive" multiplicity="one" resulttype="Integer"
               remark="根据日期，设备查询记录">
        select count(*) from tp_face_scan_equipment_record_detail
        where equipment_sn = #{equipmentSn,jdbcType=VARCHAR}
        and transaction_time = #{transactionTime,jdbcType=TIMESTAMP}
    </operation>
    </table>
