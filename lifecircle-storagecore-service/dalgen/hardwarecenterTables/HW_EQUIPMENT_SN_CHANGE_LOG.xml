<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="HW_EQUIPMENT_SN_CHANGE_LOG" physicalName="HW_EQUIPMENT_SN_CHANGE_LOG"
       remark="设备流转记录表(代理商名下)">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:HW_EQUIPMENT_SN_CHANGE_LOG">
        INSERT INTO HW_EQUIPMENT_SN_CHANGE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="snId != null">`SN_ID`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="handleType != null">`HANDLE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="snId != null">#{snId,jdbcType=INTEGER},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="handleType != null">#{handleType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getLastLogBySnId"  remark="查询设备最后的绑定记录">
        SELECT *
        from hw_equipment_sn_change_log
        where handle_type = 3
        and sn_id = #{snId,jdbcType = INTEGER}
        ORDER BY create_time DESC limit 1
    </operation>

    <operation name="getFirstBind" paramtype="primitive" multiplicity="one" remark="获取首次绑定记录">
        SELECT * from hw_equipment_sn_change_log
        where sn_id = #{snId,jdbcType = INTEGER}
        and handle_type = 3
        ORDER BY create_time ASC limit 1
    </operation>

    <operation name="getInDepot" paramtype="primitive" multiplicity="one" resulttype="Integer" remark="获取入库时仓库位置">
        select depot from hw_equipment_sn_change_log where sn_id = (select id from hw_equipment_sn where init_sn =
        #{initSn,jdbcType = VARCHAR} LIMIT 1)
        AND execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        AND handle_type IN (8,10,14)
        ORDER BY execute_time DESC LIMIT 1
    </operation>

    <operation name="getOutDepot" paramtype="primitive" multiplicity="one" resulttype="Integer" remark="获取出库时仓库位置">
        select depot from hw_equipment_sn_change_log where sn_id = (select id from hw_equipment_sn where init_sn =
        #{initSn,jdbcType = VARCHAR} LIMIT 1)
        AND execute_time <![CDATA[ <= ]]> #{executeTime,jdbcType=TIMESTAMP}
        AND handle_type = 11
        ORDER BY execute_time DESC LIMIT 1
    </operation>

</table>
