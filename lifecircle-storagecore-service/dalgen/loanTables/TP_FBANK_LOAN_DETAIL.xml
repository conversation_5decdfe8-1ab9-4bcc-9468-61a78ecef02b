<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FBANK_LOAN_DETAIL" physicalName="TP_FBANK_LOAN_DETAIL"
       remark="富民贷款的放款记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FBANK_LOAN_DETAIL">
        INSERT INTO TP_FBANK_LOAN_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="irrType != null">`IRR_TYPE`,</if>
            <if test="failCode != null">`FAIL_CODE`,</if>
            <if test="failReason != null">`FAIL_REASON`,</if>
            <if test="loanBillId != null">`LOAN_BILL_ID`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="tradeStatus != null">`TRADE_STATUS`,</if>
            <if test="businessCode != null">`BUSINESS_CODE`,</if>
            <if test="businessType != null">`BUSINESS_TYPE`,</if>
            <if test="repaymentWay != null">`REPAYMENT_WAY`,</if>
            <if test="loanInApplyId != null">`LOAN_IN_APPLY_ID`,</if>
            <if test="loanOutApplyId != null">`LOAN_OUT_APPLY_ID`,</if>
            <if test="platformCustId != null">`PLATFORM_CUST_ID`,</if>
            <if test="platformCustName != null">`PLATFORM_CUST_NAME`,</if>
            <if test="borrowerIdCardLastFour != null">`BORROWER_ID_CARD_LAST_FOUR`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="periods != null">`PERIODS`,</if>
            <if test="currency != null">`CURRENCY`,</if>
            <if test="loanDate != null">`LOAN_DATE`,</if>
            <if test="loanEndDate != null">`LOAN_END_DATE`,</if>
            <if test="loanStartDate != null">`LOAN_START_DATE`,</if>
            <if test="completionTime != null">`COMPLETION_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="irrRate != null">`IRR_RATE`,</if>
            <if test="loanAmt != null">`LOAN_AMT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="irrType != null">#{irrType,jdbcType=VARCHAR},</if>
            <if test="failCode != null">#{failCode,jdbcType=VARCHAR},</if>
            <if test="failReason != null">#{failReason,jdbcType=VARCHAR},</if>
            <if test="loanBillId != null">#{loanBillId,jdbcType=VARCHAR},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=VARCHAR},</if>
            <if test="tradeStatus != null">#{tradeStatus,jdbcType=VARCHAR},</if>
            <if test="businessCode != null">#{businessCode,jdbcType=VARCHAR},</if>
            <if test="businessType != null">#{businessType,jdbcType=VARCHAR},</if>
            <if test="repaymentWay != null">#{repaymentWay,jdbcType=VARCHAR},</if>
            <if test="loanInApplyId != null">#{loanInApplyId,jdbcType=VARCHAR},</if>
            <if test="loanOutApplyId != null">#{loanOutApplyId,jdbcType=VARCHAR},</if>
            <if test="platformCustId != null">#{platformCustId,jdbcType=VARCHAR},</if>
            <if test="platformCustName != null">#{platformCustName,jdbcType=VARCHAR},</if>
            <if test="borrowerIdCardLastFour != null">#{borrowerIdCardLastFour,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="periods != null">#{periods,jdbcType=INTEGER},</if>
            <if test="currency != null">#{currency,jdbcType=TINYINT},</if>
            <if test="loanDate != null">#{loanDate,jdbcType=INTEGER},</if>
            <if test="loanEndDate != null">#{loanEndDate,jdbcType=INTEGER},</if>
            <if test="loanStartDate != null">#{loanStartDate,jdbcType=INTEGER},</if>
            <if test="completionTime != null">#{completionTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="irrRate != null">#{irrRate,jdbcType=DECIMAL},</if>
            <if test="loanAmt != null">#{loanAmt,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getLoanDataExport" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.loan.dataobject.FbankLoanDataDo"
               remark="查询授信列表数据">
        SELECT
        u.merchant_id as merchantId,
        loan.completion_time as completionTime,
        loan.loan_amt as loanAmt,
        loan.periods as periods,
        loan.irr_rate as irrRate,
        loan.repayment_way as repaymentWay,
        IFNULL(sum(repay.repay_pri_amt), 0) as repayPriAmt,
        IFNULL(sum(repay.repay_int_amt), 0) AS repayIntAmt,
        IFNULL(sum(repay.repay_pin_amt), 0) AS repayPinAmt
        FROM
        tp_fbank_loan_detail loan
        INNER JOIN tp_merchant_loan_white_list u ON loan.platform_cust_id = u.uuid
        LEFT JOIN tp_fbank_repay_detail repay ON loan.loan_bill_id = repay.loan_bill_id
        AND repay.repay_status = 2
        WHERE loan.is_del = 0
        <if test="completeStartDate != null and completeEndDate != null">
            AND loan.completion_time BETWEEN #{completeStartDate,jdbcType=INTEGER} AND
            #{completeEndDate,jdbcType=INTEGER}
        </if>
        <if test="merchantId != null">
            AND u.merchant_id =#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="tradeStatus != null">
            AND loan.trade_status=#{tradeStatus,jdbcType=VARCHAR}
        </if>
        group by loan.loan_bill_id
    </operation>
</table>
