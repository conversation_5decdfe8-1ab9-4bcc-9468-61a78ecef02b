<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LOAN_SHIMIAO_ORDER" physicalName="TP_LOAN_SHIMIAO_ORDER"
       remark="世渺生意贷订单数据">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LOAN_SHIMIAO_ORDER">
        INSERT INTO TP_LOAN_SHIMIAO_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderId != null">`ORDER_ID`,</if>
            <if test="userName != null">`USER_NAME`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="statusDesc != null">`STATUS_DESC`,</if>
            <if test="userMobile != null">`USER_MOBILE`,</if>
            <if test="companyName != null">`COMPANY_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="loanTerm != null">`LOAN_TERM`,</if>
            <if test="loanTime != null">`LOAN_TIME`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="approvedTerm != null">`APPROVED_TERM`,</if>
            <if test="approvedTime != null">`APPROVED_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderCreateTime != null">`ORDER_CREATE_TIME`,</if>
            <if test="loanAmount != null">`LOAN_AMOUNT`,</if>
            <if test="monthFeeRate != null">`MONTH_FEE_RATE`,</if>
            <if test="approvedAmount != null">`APPROVED_AMOUNT`,</if>
            <if test="preCreditAmount != null">`PRE_CREDIT_AMOUNT`,</if>
            <if test="merchantIdInner != null">`MERCHANT_ID_INNER`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orderId != null">#{orderId,jdbcType=VARCHAR},</if>
            <if test="userName != null">#{userName,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=VARCHAR},</if>
            <if test="statusDesc != null">#{statusDesc,jdbcType=VARCHAR},</if>
            <if test="userMobile != null">#{userMobile,jdbcType=VARCHAR},</if>
            <if test="companyName != null">#{companyName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="loanTerm != null">#{loanTerm,jdbcType=INTEGER},</if>
            <if test="loanTime != null">#{loanTime,jdbcType=INTEGER},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=INTEGER},</if>
            <if test="approvedTerm != null">#{approvedTerm,jdbcType=INTEGER},</if>
            <if test="approvedTime != null">#{approvedTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderCreateTime != null">#{orderCreateTime,jdbcType=TIMESTAMP},</if>
            <if test="loanAmount != null">#{loanAmount,jdbcType=DECIMAL},</if>
            <if test="monthFeeRate != null">#{monthFeeRate,jdbcType=DECIMAL},</if>
            <if test="approvedAmount != null">#{approvedAmount,jdbcType=DECIMAL},</if>
            <if test="preCreditAmount != null">#{preCreditAmount,jdbcType=DECIMAL},</if>
            <if test="merchantIdInner != null">#{merchantIdInner,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="findApplyLoanDataList" multiplicity="many">
        SELECT *
        FROM `tp_loan_shimiao_order`
        WHERE 1=1
        <if test="startTime!=null and endTime!=null">
            and `order_create_time` BETWEEN #{startTime,jdbcType=TIMESTAMP} AND #{endTime,jdbcType=TIMESTAMP}
        </if>
        <if test="orderStatus != null">
            AND `order_status` = #{orderStatus,jdbcType=INTEGER}
        </if>
        <if test="merchantId != null">
            AND `merchant_id`= #{merchantId,jdbcType=VARCHAR}
        </if>
        ORDER BY order_create_time DESC
    </operation>

    <operation name="findLoanApprovedList" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.loan.ShimiaoApprovedDataModel">
        SELECT
        t.MERCHANT_ID_INNER AS merchantId,
        t.loan_time AS approvedDate,
        t.loan_amount AS approvedAmount,
        t.loan_term AS approvedPeriods,
        t.month_fee_rate monthServiceRate,
        tt.totalShouoldPayAmt AS residualRepaymentAmount,
        IF(t.order_status = 310, 1, 0) AS overdue,
        t.`loan_amount` <![CDATA[*]]> 0.02 / 12 <![CDATA[*]]> t.loan_term AS commission
        FROM
        tp_loan_shimiao_order t
        LEFT JOIN (SELECT
        t.order_id,
        SUM(t.should_pay_amount) totalShouoldPayAmt
        FROM tp_loan_shimiao_repayment_list t
        WHERE t.repay_date > NOW()
        GROUP BY t.order_id
        ) tt ON tt.order_id = t.order_id
        WHERE t.`order_status` IN (250, 310, 330)
        <if test="startTime!=null and endTime!=null">
            and t.`loan_time` BETWEEN #{startTime,jdbcType=INTEGER} AND #{endTime,jdbcType=INTEGER}
        </if>
        <if test="merchantId != null">
            AND t.`merchant_id`= #{merchantId,jdbcType=VARCHAR}
        </if>
        ORDER BY t.CREATE_TIME DESC
    </operation>
</table>
