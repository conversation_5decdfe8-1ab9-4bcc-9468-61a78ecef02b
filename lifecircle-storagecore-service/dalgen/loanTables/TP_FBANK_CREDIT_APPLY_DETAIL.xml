<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FBANK_CREDIT_APPLY_DETAIL" physicalName="TP_FBANK_CREDIT_APPLY_DETAIL"
       remark="富民贷款的授信记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FBANK_CREDIT_APPLY_DETAIL">
        INSERT INTO TP_FBANK_CREDIT_APPLY_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="failCode != null">`FAIL_CODE`,</if>
            <if test="failReason != null">`FAIL_REASON`,</if>
            <if test="businessCode != null">`BUSINESS_CODE`,</if>
            <if test="creditStatus != null">`CREDIT_STATUS`,</if>
            <if test="platformCustId != null">`PLATFORM_CUST_ID`,</if>
            <if test="creditInApplyId != null">`CREDIT_IN_APPLY_ID`,</if>
            <if test="creditOutApplyId != null">`CREDIT_OUT_APPLY_ID`,</if>
            <if test="platformCustName != null">`PLATFORM_CUST_NAME`,</if>
            <if test="borrowerIdCardLastFour != null">`BORROWER_ID_CARD_LAST_FOUR`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="applyDate != null">`APPLY_DATE`,</if>
            <if test="completionTime != null">`COMPLETION_TIME`,</if>
            <if test="activableApplyDate != null">`ACTIVABLE_APPLY_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="creditLimit != null">`CREDIT_LIMIT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="failCode != null">#{failCode,jdbcType=VARCHAR},</if>
            <if test="failReason != null">#{failReason,jdbcType=VARCHAR},</if>
            <if test="businessCode != null">#{businessCode,jdbcType=VARCHAR},</if>
            <if test="creditStatus != null">#{creditStatus,jdbcType=VARCHAR},</if>
            <if test="platformCustId != null">#{platformCustId,jdbcType=VARCHAR},</if>
            <if test="creditInApplyId != null">#{creditInApplyId,jdbcType=VARCHAR},</if>
            <if test="creditOutApplyId != null">#{creditOutApplyId,jdbcType=VARCHAR},</if>
            <if test="platformCustName != null">#{platformCustName,jdbcType=VARCHAR},</if>
            <if test="borrowerIdCardLastFour != null">#{borrowerIdCardLastFour,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="applyDate != null">#{applyDate,jdbcType=INTEGER},</if>
            <if test="completionTime != null">#{completionTime,jdbcType=INTEGER},</if>
            <if test="activableApplyDate != null">#{activableApplyDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="creditLimit != null">#{creditLimit,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
    <operation name="getCreditApplyExport" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.loan.dataobject.FbankCreditApplyDo"
               remark="查询导出的授信记录">
        SELECT
        a.merchant_id as merchantId,
        b.apply_date as applyDate,
        b.completion_time as completionTime,
        b.credit_status as creditStatus,
        b.credit_limit as creditLimit,
        b.fail_code as failCode,
        b.fail_reason as failReason,
        b.borrower_id_card_last_four as borrowerIdCardLastFour,
        b.platform_cust_name as platformCustName
        FROM
        tp_fbank_credit_apply_detail b LEFT JOIN
        tp_merchant_loan_white_list a ON b.platform_cust_id = a.uuid
        WHERE b.is_del = 0
        <if test="applyStartDate != null and applyEndDate != null">
            AND b.apply_date BETWEEN #{applyStartDate,jdbcType=INTEGER} AND #{applyEndDate,jdbcType=INTEGER}
        </if>
        <if test="completeStartDate != null and completeEndDate != null">
            AND b.completion_time BETWEEN #{completeStartDate,jdbcType=INTEGER} AND #{completeEndDate,jdbcType=INTEGER}
        </if>
        <if test="merchantId != null">
            AND a.merchant_id =#{merchantId,jdbcType=INTEGER}
        </if>
        <if test="creditStatus != null">
            AND b.credit_status=#{creditStatus,jdbcType=VARCHAR}
        </if>
    </operation>
</table>
