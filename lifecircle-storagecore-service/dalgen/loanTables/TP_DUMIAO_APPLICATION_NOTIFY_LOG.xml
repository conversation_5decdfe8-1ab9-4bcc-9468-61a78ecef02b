<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_DUMIAO_APPLICATION_NOTIFY_LOG" physicalName="TP_DUMIAO_APPLICATION_NOTIFY_LOG"
       remark="TP_DUMIAO_APPLICATION_NOTIFY_LOG">

    <operation remark="查询应用数据" name="findApplicationData" multiplicity="many">
        SELECT * from TP_DUMIAO_APPLICATION_NOTIFY_LOG
        <where>
            <if test="merchantId!=null">
                merchant_id = #{merchantId, jdbcType=INTEGER}
            </if>
            <if test="uuid!=null">
                AND uuid=#{uuid,jdbcType=VARCHAR}
            </if>
            <if test="applyId!=null">
                AND apply_id = #{applyId,jdbcType=VARCHAR}
            </if>
            <if test="statusList!=null and statusList.size>0">
                AND status IN
                <foreach collection="statusList" item="status" open="(" close=")" separator=",">
                    #{status,jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="applyStartDate!=null">
                AND apply_time <![CDATA[>=]]> #{applyStartDate,jdbcType=TIMESTAMP}
            </if>
            <if test="applyEndDate!=null">
                AND apply_time <![CDATA[<=]]> #{applyEndDate,jdbcType=TIMESTAMP}
            </if>
        </where>
    </operation>

</table>
