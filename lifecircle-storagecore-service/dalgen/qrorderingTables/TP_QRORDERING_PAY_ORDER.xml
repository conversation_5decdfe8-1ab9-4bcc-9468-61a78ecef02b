<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_PAY_ORDER" physicalName="TP_QRORDERING_PAY_ORDER"
       remark="支付订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_PAY_ORDER">
        INSERT INTO TP_QRORDERING_PAY_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="ext4 != null">`EXT4`,</if>
            <if test="ext5 != null">`EXT5`,</if>
            <if test="ext6 != null">`EXT6`,</if>
            <if test="ext7 != null">`EXT7`,</if>
            <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="prepayId != null">`PREPAY_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="outStoreId != null">`OUT_STORE_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="callBackUrl != null">`CALL_BACK_URL`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="mode != null">`MODE`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="activityFlag != null">`ACTIVITY_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="rateFee != null">`RATE_FEE`,</if>
            <if test="discount != null">`DISCOUNT`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="redPackets != null">`RED_PACKETS`,</if>
            <if test="couponMoney != null">`COUPON_MONEY`,</if>
            <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
            <if test="platformDiscount != null">`PLATFORM_DISCOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="prepayId != null">#{prepayId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="outStoreId != null">#{outStoreId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="callBackUrl != null">#{callBackUrl,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="mode != null">#{mode,jdbcType=TINYINT},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="activityFlag != null">#{activityFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
            <if test="discount != null">#{discount,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="redPackets != null">#{redPackets,jdbcType=DECIMAL},</if>
            <if test="couponMoney != null">#{couponMoney,jdbcType=DECIMAL},</if>
            <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
            <if test="platformDiscount != null">#{platformDiscount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="listByPayOrderNoList" paramtype="primitive" multiplicity="many" remark="根据订单号列表批量查询订单详情">
        SELECT
        *
        FROM
        tp_qrordering_pay_order
        WHERE
        pay_order_no in
        <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
            #{payOrderNo,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
