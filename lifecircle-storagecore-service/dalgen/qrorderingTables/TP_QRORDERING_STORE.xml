<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_STORE" physicalName="TP_QRORDERING_STORE"
       remark="门店信息表">

    <operation name="getStoreInfoByOutStoreId" remark="根据生活圈门店id查询详情">
        select * from tp_qrordering_store where out_store_id = #{outStoreId,jdbcType=VARCHAR} and del_flag = 0 limit 1
    </operation>

    <operation name="findStoreByStoreIdList" multiplicity="many" remark="根据门店Id查询点单门店信息">
        SELECT
        *
        FROM tp_qrordering_store
        where store_id in
        <foreach collection="list" item="storeId" separator="," open="(" close=")">
            #{storeId,jdbcType=VARCHAR}
        </foreach>
        and DEL_FLAG = 0
    </operation>

</table>
