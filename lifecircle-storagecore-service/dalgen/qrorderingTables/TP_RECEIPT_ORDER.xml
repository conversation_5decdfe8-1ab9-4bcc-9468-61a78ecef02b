<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_RECEIPT_ORDER" physicalName="TP_RECEIPT_ORDER"
       remark="收款单业务订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_RECEIPT_ORDER">
        INSERT INTO TP_RECEIPT_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="prepayId != null">`PREPAY_ID`,</if>
            <if test="receiptId != null">`RECEIPT_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="prepayId != null">#{prepayId,jdbcType=VARCHAR},</if>
            <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findReceiptOrderLists" remark="收款单订单列表" multiplicity="many">
        select * from tp_receipt_order
        where order_status = 1 and pay_status=2 and receipt_id=#{receiptId,jdbcType=VARCHAR}
    </operation>

    <operation name="getReceiptInfoByIds" remark="根据收款单id集合获取收款单详情" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.ReceiptOrderInfoModel">
        select
        tro.order_no as orderNo,
        tro.pay_order_no as payOrderNo,
        tro.receipt_id as receiptId,
        tro.customer_id as customerId,
        tro.store_id as storeId,
        tro.order_status as orderStatus,
        tro.order_price as orderPrice,
        tro.pay_status as payStatus,
        tro.order_sumprice as orderSumprice,
        tro.pay_type as payType,
        tro.pay_time as payTime,
        tro.prepay_id as prepayId,
        tro.remark as remark,
        tro.create_time as createTime,
        tro.update_time as updateTime,
        tro.refund_status as refundStatus,
        troe.receipt_form as receiptForm,
        troe.form_value as formValue,
        troe.nick_name as nickName
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.pay_status = 2
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getReceiptOrderCount" remark="根据收款单id集合获取收款单订单数量" multiplicity="one"
               resulttype="java.lang.Integer">
        select
        count(*)
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getReceiptInfoByIdsLimit" remark="根据收款单id集合获取收款单详情分页查询" multiplicity="paging"
               paging="receiptOrderDetails"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.ReceiptOrderInfoModel">
        select
        tro.order_no as orderNo,
        tro.pay_order_no as payOrderNo,
        tro.receipt_id as receiptId,
        tro.customer_id as customerId,
        tro.store_id as storeId,
        tro.order_status as orderStatus,
        tro.order_price as orderPrice,
        tro.pay_status as payStatus,
        tro.order_sumprice as orderSumprice,
        tro.pay_type as payType,
        tro.pay_time as payTime,
        tro.prepay_id as prepayId,
        tro.remark as remark,
        tro.create_time as createTime,
        tro.update_time as updateTime,
        tro.refund_status as refundStatus,
        troe.receipt_form as receiptForm,
        troe.form_value as formValue,
        troe.nick_name as nickName
        from tp_receipt_order as tro
        LEFT JOIN tp_receipt_order_extend as troe ON tro.order_no = troe.order_no
        WHERE tro.del_flag = 0
        AND tro.pay_status = 2
        AND tro.receipt_id IN
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="selectFilterOrder" multiplicity="many">
        select * from tp_receipt_order
        where receipt_id=#{receiptId,jdbcType=VARCHAR}
        and pay_time >= #{startTime,jdbcType=INTEGER}
        and pay_time <![CDATA[<=]]>  #{endTime,jdbcType=INTEGER}
        and order_status = 1
        and pay_status=2
        <if test="refundStatus != null and !refundStatus.contains(-1)">
            and refund_status in
            <foreach collection="refundStatus" item="status" open="(" close=")" separator=",">
                #{status,jdbcType=INTEGER}
            </foreach>
        </if>
    </operation>


</table>
