<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_GOODS" physicalName="TP_QRORDERING_GOODS"
       remark="自研扫码点餐商品信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_GOODS">
        INSERT INTO TP_QRORDERING_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="unit != null">`UNIT`,</if>
            <if test="goodsId != null">`GOODS_ID`,</if>
            <if test="labelId != null">`LABEL_ID`,</if>
            <if test="picture != null">`PICTURE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="categoryId != null">`CATEGORY_ID`,</if>
            <if test="description != null">`DESCRIPTION`,</if>
            <if test="goodsTemplateId != null">`GOODS_TEMPLATE_ID`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="hotSale != null">`HOT_SALE`,</if>
            <if test="stockNum != null">`STOCK_NUM`,</if>
            <if test="resetStockNum != null">`RESET_STOCK_NUM`,</if>
            <if test="resetStockFlag != null">`RESET_STOCK_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="price != null">`PRICE`,</if>
            <if test="originalPrice != null">`ORIGINAL_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="unit != null">#{unit,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="labelId != null">#{labelId,jdbcType=VARCHAR},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="goodsTemplateId != null">#{goodsTemplateId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="hotSale != null">#{hotSale,jdbcType=TINYINT},</if>
            <if test="stockNum != null">#{stockNum,jdbcType=INTEGER},</if>
            <if test="resetStockNum != null">#{resetStockNum,jdbcType=INTEGER},</if>
            <if test="resetStockFlag != null">#{resetStockFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="price != null">#{price,jdbcType=DECIMAL},</if>
            <if test="originalPrice != null">#{originalPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation remark="通过商品id列表批量获取商品信息" name="findByGoodsIds" multiplicity="many">
        SELECT
        goods_id,
        name,
        price,
        unit,
        category_id
        FROM tp_qrordering_goods
        WHERE goods_id IN
        <foreach collection="list" item="goodsId" separator="," open="(" close=")">
            #{goodsId,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </operation>

</table>
