<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_DISH_ORDER" physicalName="TP_QRORDERING_DISH_ORDER"
       remark="扫码点单订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_DISH_ORDER">
        INSERT INTO TP_QRORDERING_DISH_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="ext4 != null">`EXT4`,</if>
            <if test="ext5 != null">`EXT5`,</if>
            <if test="ext6 != null">`EXT6`,</if>
            <if test="ext7 != null">`EXT7`,</if>
            <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="subAppid != null">`SUB_APPID`,</if>
            <if test="subMchid != null">`SUB_MCHID`,</if>
            <if test="addressId != null">`ADDRESS_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="tableCode != null">`TABLE_CODE`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="orderEntry != null">`ORDER_ENTRY`,</if>
            <if test="outStoreId != null">`OUT_STORE_ID`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="accessToken != null">`ACCESS_TOKEN`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="orderPoster != null">`ORDER_POSTER`,</if>
            <if test="tableCodeId != null">`TABLE_CODE_ID`,</if>
            <if test="subAppsecret != null">`SUB_APPSECRET`,</if>
            <if test="uploadOrderNo != null">`UPLOAD_ORDER_NO`,</if>
            <if test="applicationCode != null">`APPLICATION_CODE`,</if>
            <if test="appointmentTime != null">`APPOINTMENT_TIME`,</if>
            <if test="appointmentPhone != null">`APPOINTMENT_PHONE`,</if>
            <if test="takeOrderValidateCode != null">`TAKE_ORDER_VALIDATE_CODE`,</if>
            <if test="mealNo != null">`MEAL_NO`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="pushFlag != null">`PUSH_FLAG`,</if>
            <if test="orderMode != null">`ORDER_MODE`,</if>
            <if test="printFlag != null">`PRINT_FLAG`,</if>
            <if test="takeoutNo != null">`TAKEOUT_NO`,</if>
            <if test="mealMethod != null">`MEAL_METHOD`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="activityFlag != null">`ACTIVITY_FLAG`,</if>
            <if test="takeOrderFlag != null">`TAKE_ORDER_FLAG`,</if>
            <if test="orderGoodsCount != null">`ORDER_GOODS_COUNT`,</if>
            <if test="distributionMode != null">`DISTRIBUTION_MODE`,</if>
            <if test="lastReportStatus != null">`LAST_REPORT_STATUS`,</if>
            <if test="firstReportStatus != null">`FIRST_REPORT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="deliveryFee != null">`DELIVERY_FEE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="subAppid != null">#{subAppid,jdbcType=VARCHAR},</if>
            <if test="subMchid != null">#{subMchid,jdbcType=VARCHAR},</if>
            <if test="addressId != null">#{addressId,jdbcType=VARCHAR},</if>
            <if test="channelId != null">#{channelId,jdbcType=VARCHAR},</if>
            <if test="tableCode != null">#{tableCode,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="orderEntry != null">#{orderEntry,jdbcType=VARCHAR},</if>
            <if test="outStoreId != null">#{outStoreId,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="accessToken != null">#{accessToken,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="orderPoster != null">#{orderPoster,jdbcType=VARCHAR},</if>
            <if test="tableCodeId != null">#{tableCodeId,jdbcType=VARCHAR},</if>
            <if test="subAppsecret != null">#{subAppsecret,jdbcType=VARCHAR},</if>
            <if test="uploadOrderNo != null">#{uploadOrderNo,jdbcType=VARCHAR},</if>
            <if test="applicationCode != null">#{applicationCode,jdbcType=VARCHAR},</if>
            <if test="appointmentTime != null">#{appointmentTime,jdbcType=VARCHAR},</if>
            <if test="appointmentPhone != null">#{appointmentPhone,jdbcType=VARCHAR},</if>
            <if test="takeOrderValidateCode != null">#{takeOrderValidateCode,jdbcType=VARCHAR},</if>
            <if test="mealNo != null">#{mealNo,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="pushFlag != null">#{pushFlag,jdbcType=TINYINT},</if>
            <if test="orderMode != null">#{orderMode,jdbcType=TINYINT},</if>
            <if test="printFlag != null">#{printFlag,jdbcType=TINYINT},</if>
            <if test="takeoutNo != null">#{takeoutNo,jdbcType=INTEGER},</if>
            <if test="mealMethod != null">#{mealMethod,jdbcType=TINYINT},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="activityFlag != null">#{activityFlag,jdbcType=TINYINT},</if>
            <if test="takeOrderFlag != null">#{takeOrderFlag,jdbcType=TINYINT},</if>
            <if test="orderGoodsCount != null">#{orderGoodsCount,jdbcType=INTEGER},</if>
            <if test="distributionMode != null">#{distributionMode,jdbcType=TINYINT},</if>
            <if test="lastReportStatus != null">#{lastReportStatus,jdbcType=TINYINT},</if>
            <if test="firstReportStatus != null">#{firstReportStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="deliveryFee != null">#{deliveryFee,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findMerchantAdminOrderListByStoreId" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.MerchantAdminOrderListModel"
               remark="商户后台订单列表">

        SELECT
        d.store_id as storeId,
        d.dish_order_no as dishOrderNo,
        d.pay_order_no as payOrderNo,
        d.create_time as createTime,
        d.activity_flag as activityFlag,
        d.remark as remark,
        d.distribution_mode as distributionMode,
        d.address_id as addressId,
        d.takeout_no as takeoutNo,
        d.appointment_time as appointmentTime,
        p.order_sumprice as orderPrice
        FROM
        tp_qrordering_dish_order d
        INNER JOIN tp_qrordering_pay_order p ON d.pay_order_no = p.pay_order_no
        AND d.store_id = p.store_id

        WHERE d.store_id=#{storeId,jdbcType=VARCHAR}
        AND p.pay_status=2
        <if test=" distributionMode != null">
            AND d.distribution_mode=#{distributionMode,jdbcType=INTEGER}
        </if>
        <if test=" activityFlag != null">
            AND d.activity_flag=#{activityFlag,jdbcType=INTEGER}
        </if>
        AND d.create_time <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
        AND d.create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}
        order by d.create_time
    </operation>

    <operation name="findMerchantAdminOrderGoodsListByStoreId" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.MerchantAdminOrderGoodsListModel"
               remark="商户后台订单商品列表">

        SELECT
        d.activity_flag as activityFlag,
        g.goods_id as goodsId,
        g.activity_goods_id as activityGoodsId,
        sum(g.sale_number) as saleNumber
        FROM
        tp_qrordering_dish_order d
        INNER JOIN tp_qrordering_customer_goods_log g ON d.pay_order_no = g.pay_order_no
        AND d.store_id = g.store_id

        WHERE d.store_id=#{storeId,jdbcType=VARCHAR}
        <if test="distributionMode != null">
            AND d.distribution_mode=#{distributionMode,jdbcType=INTEGER}
        </if>
        <if test="activityFlag != null">
            AND g.activity_flag=#{activityFlag,jdbcType=INTEGER}
        </if>
        AND d.create_time <![CDATA[>=]]> #{startTime,jdbcType=VARCHAR}
        AND d.create_time <![CDATA[<=]]> #{endTime,jdbcType=VARCHAR}

        GROUP BY g.goods_id,g.activity_goods_id
        ORDER BY saleNumber desc,d.id desc
    </operation>

    <operation name="listAdminGroupActivityOrder" multiplicity="many" paramtype="primitive">
        SELECT
        a.*
        FROM tp_qrordering_dish_order a
        LEFT JOIN TP_QRORDERING_PAY_ORDER b ON a.dish_order_no = b.dish_order_no
        WHERE
        b.pay_status = 2
        AND a.store_id = #{storeId,jdbcType=VARCHAR}
        AND a.order_type = 2
        AND a.group_activity_id = #{groupActivityId,jdbcType=VARCHAR}
        AND a.del_flag = 0
        ORDER BY create_time DESC
    </operation>

    <operation name="listAdminGroupActivityOrderGoods" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.AdminGroupActivityOrderGoodsModel"
               remark="收银机后台团购活动订单按商品查询列表">
        SELECT
        store_id as storeId,
        out_store_id as outStoreId,
        activity_id as groupActivityId,
        activity_goods_id as activityGoodsId,
        sale_number as saleNumber
        FROM
        (
        SELECT
        dish.store_id,
        log.out_store_id,
        log.activity_id,
        log.activity_goods_id,
        sum(log.sale_number) as sale_number
        FROM
        tp_qrordering_dish_order dish
        INNER JOIN tp_qrordering_customer_goods_log log ON dish.pay_order_no = log.pay_order_no
        AND dish.store_id = log.store_id
        WHERE dish.store_id = #{storeId,jdbcType=VARCHAR}
        AND dish.group_activity_id = #{groupActivityId,jdbcType=VARCHAR}
        AND dish.order_type = 2
        GROUP BY dish.store_id, log.out_store_id, log.activity_id, log.activity_goods_id
        ) temp_table
        ORDER BY saleNumber DESC
    </operation>

    <operation remark="根据支付订单号查询订单信息" name="findByPayOrderNos" multiplicity="many">
        select
        `id`,
        `customer_id`,
        `store_id`,
        `out_store_id`,
        `take_order_validate_code`,
        `take_order_flag`,
        `dish_order_no`,
        `upload_order_no`,
        `pay_order_no`,
        `activity_id`,
        `activity_flag`,
        `meal_no`,
        `channel_id`,
        `source_type`,
        `sub_appid`,
        `sub_appsecret`,
        `sub_mchid`,
        `order_goods_count`,
        `table_code_id`,
        `table_code`,
        `remark`,
        `application_code`,
        `order_entry`,
        `first_report_status`,
        `last_report_status`,
        `order_status`,
        `push_flag`,
        `print_flag`,
        `access_token`,
        `meal_method`,
        `order_mode`,
        `appointment_time`,
        `appointment_phone`,
        `distribution_mode`,
        `takeout_no`,
        `address_id`,
        `delivery_fee`,
        `order_poster`,
        `ext1`,
        `ext2`,
        `ext3`,
        `ext4`,
        `ext5`,
        `ext6`,
        `ext7`,
        `ext8`,
        `ext9`,
        `ext10`,
        `del_flag`,
        `create_time`,
        `update_time`,
        `delivery_status`,
        `delivery_change_time`,
        `order_package_fee`,
        `order_price`,
        `order_type`,
        `group_flow_no`,
        `group_activity_id`,
        `receipt_name`,
        `receipt_phone`,
        `receipt_address`,
        `receipt_number_plate`,
        `receipt_longitude`,
        `receipt_latitude`
        from tp_qrordering_dish_order
        where pay_order_no IN
        <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
            #{payOrderNo,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
        ORDER BY id desc
    </operation>

</table>
