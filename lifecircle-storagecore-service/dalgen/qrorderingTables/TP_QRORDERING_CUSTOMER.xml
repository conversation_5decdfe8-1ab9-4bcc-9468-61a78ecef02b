<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_CUSTOMER" physicalName="TP_QRORDERING_CUSTOMER"
       remark="扫码点餐用户信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_CUSTOMER">
        INSERT INTO TP_QRORDERING_CUSTOMER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="code != null">`CODE`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="country != null">`COUNTRY`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="nickName != null">`NICK_NAME`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="subAppId != null">`SUB_APP_ID`,</if>
            <if test="avatarUrl != null">`AVATAR_URL`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="phoneNumber != null">`PHONE_NUMBER`,</if>
            <if test="tradeOpenId != null">`TRADE_OPEN_ID`,</if>
            <if test="haveCertified != null">`HAVE_CERTIFIED`,</if>
            <if test="gender != null">`GENDER`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="codeInvalid != null">`CODE_INVALID`,</if>
            <if test="customerType != null">`CUSTOMER_TYPE`,</if>
            <if test="memberUserId != null">`MEMBER_USER_ID`,</if>
            <if test="customerSource != null">`CUSTOMER_SOURCE`,</if>
            <if test="indexBarcodeFrame != null">`INDEX_BARCODE_FRAME`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>
            <if test="code != null">#{code,jdbcType=CHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="country != null">#{country,jdbcType=VARCHAR},</if>
            <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
            <if test="nickName != null">#{nickName,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="subAppId != null">#{subAppId,jdbcType=VARCHAR},</if>
            <if test="avatarUrl != null">#{avatarUrl,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="phoneNumber != null">#{phoneNumber,jdbcType=VARCHAR},</if>
            <if test="tradeOpenId != null">#{tradeOpenId,jdbcType=VARCHAR},</if>
            <if test="haveCertified != null">#{haveCertified,jdbcType=CHAR},</if>
            <if test="gender != null">#{gender,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="codeInvalid != null">#{codeInvalid,jdbcType=INTEGER},</if>
            <if test="customerType != null">#{customerType,jdbcType=TINYINT},</if>
            <if test="memberUserId != null">#{memberUserId,jdbcType=INTEGER},</if>
            <if test="customerSource != null">#{customerSource,jdbcType=TINYINT},</if>
            <if test="indexBarcodeFrame != null">#{indexBarcodeFrame,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="listCustomerByCustomerId" paramtype="primitive" multiplicity="many" remark="查询顾客">
        SELECT * FROM tp_qrordering_customer
        WHERE customer_id IN
        <foreach collection="list" item="customerId" open="(" close=")" separator=",">
            #{customerId,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </operation>

</table>
