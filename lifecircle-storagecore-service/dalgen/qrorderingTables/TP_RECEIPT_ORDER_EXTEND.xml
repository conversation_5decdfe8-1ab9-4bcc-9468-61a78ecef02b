<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_RECEIPT_ORDER_EXTEND" physicalName="TP_RECEIPT_ORDER_EXTEND"
       remark="收款单订单扩展表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_RECEIPT_ORDER_EXTEND">
        INSERT INTO TP_RECEIPT_ORDER_EXTEND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="nickName != null">`NICK_NAME`,</if>
            <if test="avatarUrl != null">`AVATAR_URL`,</if>
            <if test="formValue != null">`FORM_VALUE`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="receiptForm != null">`RECEIPT_FORM`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="nickName != null">#{nickName,jdbcType=VARCHAR},</if>
            <if test="avatarUrl != null">#{avatarUrl,jdbcType=VARCHAR},</if>
            <if test="formValue != null">#{formValue,jdbcType=VARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findReceiptOrderExtendLists" remark="收款单订单扩展列表" multiplicity="many">
        select ORDER_NO,RECEIPT_FORM from TP_RECEIPT_ORDER_EXTEND
        where order_no in
        <foreach collection="list" item="orderNo" open="(" close=")" separator=",">
            #{orderNo,jdbcType=VARCHAR}
        </foreach>

    </operation>

</table>
