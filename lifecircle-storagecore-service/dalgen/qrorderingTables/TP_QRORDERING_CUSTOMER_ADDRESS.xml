<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_CUSTOMER_ADDRESS" physicalName="TP_QRORDERING_CUSTOMER_ADDRESS"
       remark="c端用户地址表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_CUSTOMER_ADDRESS">
        INSERT INTO TP_QRORDERING_CUSTOMER_ADDRESS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="latitude != null">`LATITUDE`,</if>
            <if test="longitude != null">`LONGITUDE`,</if>
            <if test="addressId != null">`ADDRESS_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="numberPlate != null">`NUMBER_PLATE`,</if>
            <if test="receiptName != null">`RECEIPT_NAME`,</if>
            <if test="receiptPhone != null">`RECEIPT_PHONE`,</if>
            <if test="receiptAddress != null">`RECEIPT_ADDRESS`,</if>
            <if test="sex != null">`SEX`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="latitude != null">#{latitude,jdbcType=REAL},</if>
            <if test="longitude != null">#{longitude,jdbcType=REAL},</if>
            <if test="addressId != null">#{addressId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="numberPlate != null">#{numberPlate,jdbcType=VARCHAR},</if>
            <if test="receiptName != null">#{receiptName,jdbcType=VARCHAR},</if>
            <if test="receiptPhone != null">#{receiptPhone,jdbcType=CHAR},</if>
            <if test="receiptAddress != null">#{receiptAddress,jdbcType=VARCHAR},</if>
            <if test="sex != null">#{sex,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findAllByAddressIds" multiplicity="many" remark="通过address_id列表查询配送地址信息">
        SELECT *
        FROM
        TP_QRORDERING_CUSTOMER_ADDRESS
        WHERE
        address_id in
        <foreach collection="list" item="addressId" separator="," open="(" close=")">
            #{addressId,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
