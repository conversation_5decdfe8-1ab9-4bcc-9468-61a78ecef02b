<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_GOODS_CATEGORY" physicalName="TP_QRORDERING_GOODS_CATEGORY"
       remark="商品分类表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_GOODS_CATEGORY">
        INSERT INTO TP_QRORDERING_GOODS_CATEGORY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="printerId != null">`PRINTER_ID`,</if>
            <if test="categoryId != null">`CATEGORY_ID`,</if>
            <if test="categoryName != null">`CATEGORY_NAME`,</if>
            <if test="parentCategoryId != null">`PARENT_CATEGORY_ID`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="outMerchantId != null">`OUT_MERCHANT_ID`,</if>
            <if test="systemCategory != null">`SYSTEM_CATEGORY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="printerId != null">#{printerId,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
            <if test="categoryName != null">#{categoryName,jdbcType=VARCHAR},</if>
            <if test="parentCategoryId != null">#{parentCategoryId,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="outMerchantId != null">#{outMerchantId,jdbcType=INTEGER},</if>
            <if test="systemCategory != null">#{systemCategory,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByCategoryIds" paramtype="primitive" multiplicity="many" remark="批量获取非默认分组">
        select CATEGORY_ID,CATEGORY_NAME from TP_QRORDERING_GOODS_CATEGORY
        where
        `CATEGORY_ID` in
        <foreach collection="list" item="categoryId" open="(" close=")" separator=",">
            #{categoryId,jdbcType=VARCHAR}
        </foreach>
        and system_category = 2
        and DEL_FLAG = 0
    </operation>

</table>
