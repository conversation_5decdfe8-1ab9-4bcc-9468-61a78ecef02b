<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_CUSTOMER_GOODS_LOG" physicalName="TP_QRORDERING_CUSTOMER_GOODS_LOG"
       remark="用户点餐记录表">

    <operation name="getGroupActivityLogList" paramtype="primitive" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.qrordering.resultmap.GroupActivityGoodsCustomerLogDO"
               remark="根据门店，活动，活动商品获取团购接龙活动订单支付号">
        SELECT
        log.`out_store_id` as outStoreId,
        log.`activity_id` as activityId ,
        log.`activity_goods_id` as activityGoodsId,
        log.`sale_number` as saleNumber,
        dishorder.`take_order_flag` as takeOrderFlag
        from `tp_qrordering_customer_goods_log` log
        LEFT JOIN `tp_qrordering_pay_order` payorder on log.`pay_order_no` = payorder.`pay_order_no`
        LEFT JOIN `tp_qrordering_dish_order` dishorder on payorder.`pay_order_no` = dishorder.`pay_order_no`
        WHERE log.activity_id = #{activityId,jdbcType=VARCHAR}
        AND log.activity_goods_id = #{activityGoodsId,jdbcType=VARCHAR}
        AND log.out_store_id = #{outStoreId,jdbcType=VARCHAR}
        AND log.del_flag = 0
    </operation>

</table>
