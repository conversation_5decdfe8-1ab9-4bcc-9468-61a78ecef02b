<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_GROUP_ACTIVITY" physicalName="TP_QRORDERING_GROUP_ACTIVITY"
       remark="团购接龙活动表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_GROUP_ACTIVITY">
        INSERT INTO TP_QRORDERING_GROUP_ACTIVITY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupActivityId != null">`GROUP_ACTIVITY_ID`,</if>
            <if test="groupActivityName != null">`GROUP_ACTIVITY_NAME`,</if>
            <if test="groupActivityContent != null">`GROUP_ACTIVITY_CONTENT`,</if>
            <if test="groupActivityPicture != null">`GROUP_ACTIVITY_PICTURE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payAfterShow != null">`PAY_AFTER_SHOW`,</if>
            <if test="canOutDelivery != null">`CAN_OUT_DELIVERY`,</if>
            <if test="activityPushFlag != null">`ACTIVITY_PUSH_FLAG`,</if>
            <if test="defaultOutStoreId != null">`DEFAULT_OUT_STORE_ID`,</if>
            <if test="groupActivityClosed != null">`GROUP_ACTIVITY_CLOSED`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="groupActivityEndTime != null">`GROUP_ACTIVITY_END_TIME`,</if>
            <if test="groupActivityStartTime != null">`GROUP_ACTIVITY_START_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupActivityId != null">#{groupActivityId,jdbcType=VARCHAR},</if>
            <if test="groupActivityName != null">#{groupActivityName,jdbcType=VARCHAR},</if>
            <if test="groupActivityContent != null">#{groupActivityContent,jdbcType=LONGVARCHAR},</if>
            <if test="groupActivityPicture != null">#{groupActivityPicture,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payAfterShow != null">#{payAfterShow,jdbcType=TINYINT},</if>
            <if test="canOutDelivery != null">#{canOutDelivery,jdbcType=TINYINT},</if>
            <if test="activityPushFlag != null">#{activityPushFlag,jdbcType=TINYINT},</if>
            <if test="defaultOutStoreId != null">#{defaultOutStoreId,jdbcType=INTEGER},</if>
            <if test="groupActivityClosed != null">#{groupActivityClosed,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="groupActivityEndTime != null">#{groupActivityEndTime,jdbcType=TIMESTAMP},</if>
            <if test="groupActivityStartTime != null">#{groupActivityStartTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="listActivityByActivityId" paramtype="primitive" multiplicity="many" remark="查询活动">
        SELECT * FROM tp_qrordering_group_activity
        WHERE group_activity_id IN
        <foreach collection="list" item="groupActivityId" open="(" close=")" separator=",">
            #{groupActivityId,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </operation>

</table>
