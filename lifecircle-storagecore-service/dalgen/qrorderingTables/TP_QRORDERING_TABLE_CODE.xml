<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_QRORDERING_TABLE_CODE" physicalName="TP_QRORDERING_TABLE_CODE"
    remark="桌码绑定表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_TABLE_CODE">
INSERT INTO TP_QRORDERING_TABLE_CODE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="qrcode != null">`QRCODE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="tableCode != null">`TABLE_CODE`,</if>
        <if test="tableCodeId != null">`TABLE_CODE_ID`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="bindStatus != null">`BIND_STATUS`,</if>
        <if test="tableCodeDisplay != null">`TABLE_CODE_DISPLAY`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
        <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
        <if test="tableCode != null">#{tableCode,jdbcType=VARCHAR},</if>
        <if test="tableCodeId != null">#{tableCodeId,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
        <if test="tableCodeDisplay != null">#{tableCodeDisplay,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>
    <operation name="insertBatch" paramtype="objectList">
        INSERT INTO TP_QRORDERING_TABLE_CODE
        (`QRCODE`, `STORE_ID`, `TABLE_CODE_ID`, `BIND_STATUS`, `TABLE_CODE`) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (#{item.qrcode,jdbcType=VARCHAR}, #{item.storeId,jdbcType=VARCHAR}, #{item.tableCodeId,jdbcType=VARCHAR},
            #{item.bindStatus,jdbcType=TINYINT}, #{item.tableCode,jdbcType=VARCHAR})
        </foreach>
    </operation>
    </table>
