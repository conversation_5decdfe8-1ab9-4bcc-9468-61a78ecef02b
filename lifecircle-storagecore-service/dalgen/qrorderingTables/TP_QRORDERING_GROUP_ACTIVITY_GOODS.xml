<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_GROUP_ACTIVITY_GOODS" physicalName="TP_QRORDERING_GROUP_ACTIVITY_GOODS"
       remark="团购接龙活动商品表">

    <operation name="getGroupActivityGoods" multiplicity="one" paramtype="primitive" remark="根据团购商品ID查询门店团购商品">
        SELECT *
        FROM tp_qrordering_group_activity_goods
        WHERE
        `out_store_id` = #{outStoreId,jdbcType=INTEGER}
        AND `group_activity_id` = #{groupActivityId,jdbcType=VARCHAR}
        AND `group_activity_goods_id` = #{groupActivityGoodsId,jdbcType=VARCHAR}
        AND del_flag = 0
    </operation>

</table>
