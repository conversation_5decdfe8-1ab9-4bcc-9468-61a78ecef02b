<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_RECEIPT_CLIENTS_IMPORT" physicalName="TP_RECEIPT_CLIENTS_IMPORT"
       remark="顾客信息导入表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_RECEIPT_CLIENTS_IMPORT">
        INSERT INTO TP_RECEIPT_CLIENTS_IMPORT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="receiptId != null">`RECEIPT_ID`,</if>
            <if test="clientMarkers != null">`CLIENT_MARKERS`,</if>
            <if test="clientMessage != null">`CLIENT_MESSAGE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="receivableAmount != null">`RECEIVABLE_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
            <if test="clientMarkers != null">#{clientMarkers,jdbcType=VARCHAR},</if>
            <if test="clientMessage != null">#{clientMessage,jdbcType=LONGVARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="receivableAmount != null">#{receivableAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>


    <operation name="getImportDOByReceiptId" multiplicity="one" paramtype="primitive"
               remark="根据收款单id取一条顾客导入表数据">
        SELECT
        *
        FROM
        TP_RECEIPT_CLIENTS_IMPORT
        WHERE
        RECEIPT_ID = #{receiptId,jdbcType=VARCHAR}
        AND IS_DEL = 0
        LIMIT 1
    </operation>

    <operation name="findClientMarkersByReceiptId" multiplicity="many" paramtype="primitive"
               resulttype="java.lang.String" remark="根据收款单id查询导入表数据">
        SELECT
        CLIENT_MARKERS
        FROM
        TP_RECEIPT_CLIENTS_IMPORT
        WHERE
        RECEIPT_ID = #{receiptId,jdbcType=VARCHAR}
        AND IS_DEL = 0
    </operation>
</table>
