<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_QRCODE" physicalName="TP_QRORDERING_QRCODE"
       remark="空二维码表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_QRCODE">
        INSERT INTO TP_QRORDERING_QRCODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="qrcodeType != null">`QRCODE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="qrcodeType != null">#{qrcodeType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO TP_QRORDERING_QRCODE(
        QRCODE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.qrcode,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="insertBatchWithQrcodeType" paramtype="objectList" remark="批量插入">
        INSERT INTO
        TP_QRORDERING_QRCODE
        (
        QRCODE,
        QRCODE_TYPE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.qrcode,jdbcType=VARCHAR},
            #{item.qrcodeType,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

</table>
