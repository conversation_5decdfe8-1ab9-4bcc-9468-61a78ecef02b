<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_BASIC_GOODS" physicalName="TP_QRORDERING_BASIC_GOODS"
       remark="菜品库基础表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_BASIC_GOODS">
        INSERT INTO TP_QRORDERING_BASIC_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="image != null">`IMAGE`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="goodsId != null">`GOODS_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="image != null">#{image,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="goodsId != null">#{goodsId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByNames" multiplicity="many">
        select *from TP_QRORDERING_BASIC_GOODS
        where `name` in
        <foreach collection="list" item="name" separator="," open="(" close=")">
            #{name,jdbcType=VARCHAR}
        </foreach>
        and DEL_FLAG=0
    </operation>

    <operation name="findByNamesAndGroupByName" multiplicity="many">
        select *from TP_QRORDERING_BASIC_GOODS
        where `name` in
        <foreach collection="list" item="name" separator="," open="(" close=")">
            #{name,jdbcType=VARCHAR}
        </foreach>
        and DEL_FLAG=0
        GROUP BY `name`
    </operation>

</table>
