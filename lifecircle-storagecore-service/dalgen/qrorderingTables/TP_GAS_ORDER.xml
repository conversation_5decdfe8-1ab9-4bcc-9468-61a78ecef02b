<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_GAS_ORDER" physicalName="TP_GAS_ORDER"
       remark="燃气缴费订单">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_GAS_ORDER">
        INSERT INTO TP_GAS_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="billNo != null">`BILL_NO`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="instNo != null">`INST_NO`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="billKey != null">`BILL_KEY`,</if>
            <if test="bizType != null">`BIZ_TYPE`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="cardInfo != null">`CARD_INFO`,</if>
            <if test="deviceSn != null">`DEVICE_SN`,</if>
            <if test="ownerName != null">`OWNER_NAME`,</if>
            <if test="requestId != null">`REQUEST_ID`,</if>
            <if test="chargeInst != null">`CHARGE_INST`,</if>
            <if test="priceLevel != null">`PRICE_LEVEL`,</if>
            <if test="subBizType != null">`SUB_BIZ_TYPE`,</if>
            <if test="supplierId != null">`SUPPLIER_ID`,</if>
            <if test="extendField != null">`EXTEND_FIELD`,</if>
            <if test="factoryCode != null">`FACTORY_CODE`,</if>
            <if test="fubeiOrderSn != null">`FUBEI_ORDER_SN`,</if>
            <if test="supplierName != null">`SUPPLIER_NAME`,</if>
            <if test="fubeiRefundSn != null">`FUBEI_REFUND_SN`,</if>
            <if test="organizationNo != null">`ORGANIZATION_NO`,</if>
            <if test="userIdentityCode != null">`USER_IDENTITY_CODE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="tradeDate != null">`TRADE_DATE`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="purchaseId != null">`PURCHASE_ID`,</if>
            <if test="refundTime != null">`REFUND_TIME`,</if>
            <if test="chargeModel != null">`CHARGE_MODEL`,</if>
            <if test="writeStatus != null">`WRITE_STATUS`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="writeOffStatus != null">`WRITE_OFF_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="rate != null">`RATE`,</if>
            <if test="price != null">`PRICE`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="cardVal != null">`CARD_VAL`,</if>
            <if test="rateFee != null">`RATE_FEE`,</if>
            <if test="payAmount != null">`PAY_AMOUNT`,</if>
            <if test="fineAmount != null">`FINE_AMOUNT`,</if>
            <if test="allowAmount != null">`ALLOW_AMOUNT`,</if>
            <if test="ladderAmount != null">`LADDER_AMOUNT`,</if>
            <if test="receiptAmount != null">`RECEIPT_AMOUNT`,</if>
            <if test="rechargeValue != null">`RECHARGE_VALUE`,</if>
            <if test="rechargeAmount != null">`RECHARGE_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="pid != null">#{pid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="instNo != null">#{instNo,jdbcType=VARCHAR},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="billKey != null">#{billKey,jdbcType=VARCHAR},</if>
            <if test="bizType != null">#{bizType,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=VARCHAR},</if>
            <if test="cardInfo != null">#{cardInfo,jdbcType=VARCHAR},</if>
            <if test="deviceSn != null">#{deviceSn,jdbcType=VARCHAR},</if>
            <if test="ownerName != null">#{ownerName,jdbcType=VARCHAR},</if>
            <if test="requestId != null">#{requestId,jdbcType=VARCHAR},</if>
            <if test="chargeInst != null">#{chargeInst,jdbcType=VARCHAR},</if>
            <if test="priceLevel != null">#{priceLevel,jdbcType=VARCHAR},</if>
            <if test="subBizType != null">#{subBizType,jdbcType=VARCHAR},</if>
            <if test="supplierId != null">#{supplierId,jdbcType=VARCHAR},</if>
            <if test="extendField != null">#{extendField,jdbcType=VARCHAR},</if>
            <if test="factoryCode != null">#{factoryCode,jdbcType=VARCHAR},</if>
            <if test="fubeiOrderSn != null">#{fubeiOrderSn,jdbcType=VARCHAR},</if>
            <if test="supplierName != null">#{supplierName,jdbcType=VARCHAR},</if>
            <if test="fubeiRefundSn != null">#{fubeiRefundSn,jdbcType=VARCHAR},</if>
            <if test="organizationNo != null">#{organizationNo,jdbcType=VARCHAR},</if>
            <if test="userIdentityCode != null">#{userIdentityCode,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="purchaseId != null">#{purchaseId,jdbcType=INTEGER},</if>
            <if test="refundTime != null">#{refundTime,jdbcType=INTEGER},</if>
            <if test="chargeModel != null">#{chargeModel,jdbcType=TINYINT},</if>
            <if test="writeStatus != null">#{writeStatus,jdbcType=TINYINT},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="writeOffStatus != null">#{writeOffStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="rate != null">#{rate,jdbcType=DECIMAL},</if>
            <if test="price != null">#{price,jdbcType=DECIMAL},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="cardVal != null">#{cardVal,jdbcType=DECIMAL},</if>
            <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
            <if test="payAmount != null">#{payAmount,jdbcType=DECIMAL},</if>
            <if test="fineAmount != null">#{fineAmount,jdbcType=DECIMAL},</if>
            <if test="allowAmount != null">#{allowAmount,jdbcType=DECIMAL},</if>
            <if test="ladderAmount != null">#{ladderAmount,jdbcType=DECIMAL},</if>
            <if test="receiptAmount != null">#{receiptAmount,jdbcType=DECIMAL},</if>
            <if test="rechargeValue != null">#{rechargeValue,jdbcType=DECIMAL},</if>
            <if test="rechargeAmount != null">#{rechargeAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findGasOrder" multiplicity="many" remark="查询支付成功待充值和充值成功，实际写卡失败" paramtype="primitive"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.GasOrderDTO">
        SELECT
        order_sn orderSn,
        store_id storeId,
        device_sn deviceSn,
        pay_time payTime,
        bill_key billKey,
        owner_name ownerName,
        pay_status payStatus,
        write_off_status writeOffStatus
        from
        tp_gas_order
        where
        pay_status=1 and write_off_status=0 and create_time between #{startTime,jdbcType=VARCHAR} AND
        #{endTime,jdbcType=VARCHAR}
        union all
        SELECT
        order_sn orderSn,
        store_id storeId,
        device_sn deviceSn,
        pay_time payTime,
        bill_key billKey,
        owner_name ownerName,
        pay_status payStatus,
        write_off_status writeOffStatus
        from
        tp_gas_order
        where
        write_status=2 and create_time between #{startTime,jdbcType=VARCHAR} AND #{endTime,jdbcType=VARCHAR}
    </operation>

</table>
