<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_DISH_ORDER_EXTEND" physicalName="TP_QRORDERING_DISH_ORDER_EXTEND"
       remark="扫码点单订单扩展表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_DISH_ORDER_EXTEND">
        INSERT INTO TP_QRORDERING_DISH_ORDER_EXTEND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ext1 != null">`EXT1`,</if>
            <if test="ext2 != null">`EXT2`,</if>
            <if test="ext3 != null">`EXT3`,</if>
            <if test="ext4 != null">`EXT4`,</if>
            <if test="ext5 != null">`EXT5`,</if>
            <if test="ext6 != null">`EXT6`,</if>
            <if test="ext7 != null">`EXT7`,</if>
            <if test="ext8 != null">`EXT8`,</if>
            <if test="ext9 != null">`EXT9`,</if>
            <if test="ext10 != null">`EXT10`,</if>
            <if test="dishList != null">`DISH_LIST`,</if>
            <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
            <if test="dishOrderNo != null">`DISH_ORDER_NO`,</if>
            <if test="uploadDishList != null">`UPLOAD_DISH_LIST`,</if>
            <if test="activityDishList != null">`ACTIVITY_DISH_LIST`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
            <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
            <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
            <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
            <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
            <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
            <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
            <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
            <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
            <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
            <if test="dishList != null">#{dishList,jdbcType=LONGVARCHAR},</if>
            <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
            <if test="dishOrderNo != null">#{dishOrderNo,jdbcType=VARCHAR},</if>
            <if test="uploadDishList != null">#{uploadDishList,jdbcType=LONGVARCHAR},</if>
            <if test="activityDishList != null">#{activityDishList,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByDishOrderNos" paramtype="primitive" multiplicity="many" remark="根据订单号列表批量查询订单详情">
        SELECT
        *
        FROM
        tp_qrordering_dish_order_extend
        WHERE
        dish_order_no in
        <foreach collection="list" item="dishOrderNo" separator="," open="(" close=")">
            #{dishOrderNo,jdbcType=VARCHAR}
        </foreach>
        AND del_flag = 0
    </operation>

    <operation name="findAllByDishOrderNos" paramtype="primitive" multiplicity="many" remark="根据订单号列表批量查询订单详情">
        SELECT
        *
        FROM
        tp_qrordering_dish_order_extend
        WHERE
        dish_order_no in
        <foreach collection="list" item="dishOrderNo" separator="," open="(" close=")">
            #{dishOrderNo,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
