<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_RECEIPT_INFO" physicalName="TP_RECEIPT_INFO"
       remark="收款单详情表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_RECEIPT_INFO">
        INSERT INTO TP_RECEIPT_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qrCode != null">`QR_CODE`,</if>
            <if test="posterUrl != null">`POSTER_URL`,</if>
            <if test="receiptId != null">`RECEIPT_ID`,</if>
            <if test="receiptForm != null">`RECEIPT_FORM`,</if>
            <if test="receiptImage != null">`RECEIPT_IMAGE`,</if>
            <if test="receiptExplain != null">`RECEIPT_EXPLAIN`,</if>
            <if test="wechatPosterUrl != null">`WECHAT_POSTER_URL`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="showCount != null">`SHOW_COUNT`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="receiptType != null">`RECEIPT_TYPE`,</if>
            <if test="initiatorUid != null">`INITIATOR_UID`,</if>
            <if test="receiptStatus != null">`RECEIPT_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="receiptMoney != null">`RECEIPT_MONEY`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qrCode != null">#{qrCode,jdbcType=VARCHAR},</if>
            <if test="posterUrl != null">#{posterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
            <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
            <if test="receiptImage != null">#{receiptImage,jdbcType=VARCHAR},</if>
            <if test="receiptExplain != null">#{receiptExplain,jdbcType=VARCHAR},</if>
            <if test="wechatPosterUrl != null">#{wechatPosterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="showCount != null">#{showCount,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="receiptType != null">#{receiptType,jdbcType=TINYINT},</if>
            <if test="initiatorUid != null">#{initiatorUid,jdbcType=INTEGER},</if>
            <if test="receiptStatus != null">#{receiptStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="receiptMoney != null">#{receiptMoney,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getReceiptListDetail" remark="收款单详情" multiplicity="one">
        select * from tp_receipt_info
        where del_flag=0 and receipt_id=#{receiptId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getReceiptInfoByIds" remark="根据收款单id集合获取收款单详情" multiplicity="many">
        select * from tp_receipt_info
        where del_flag=0 and receipt_id in
        <foreach collection="list" item="receiptId" separator="," open="(" close=")">
            #{receiptId,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
