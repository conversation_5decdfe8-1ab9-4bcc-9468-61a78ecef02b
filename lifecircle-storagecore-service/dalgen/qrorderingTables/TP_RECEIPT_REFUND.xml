<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_RECEIPT_REFUND" physicalName="TP_RECEIPT_REFUND"
    remark="收款单退款表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_RECEIPT_REFUND">
INSERT INTO TP_RECEIPT_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderNo != null">`ORDER_NO`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="receiptId != null">`RECEIPT_ID`,</if>
        <if test="payOrderNo != null">`PAY_ORDER_NO`,</if>
        <if test="receiptForm != null">`RECEIPT_FORM`,</if>
        <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
        <if test="receiptTitle != null">`RECEIPT_TITLE`,</if>
        <if test="refundBatchNo != null">`REFUND_BATCH_NO`,</if>
        <if test="shortRefundSn != null">`SHORT_REFUND_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="receiptRefundSn != null">`RECEIPT_REFUND_SN`,</if>
        <if test="shortPayOrderNo != null">`SHORT_PAY_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="handler != null">`HANDLER`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="isPartRefund != null">`IS_PART_REFUND`,</if>
        <if test="refundSource != null">`REFUND_SOURCE`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="poundage != null">`POUNDAGE`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="refundMoney != null">`REFUND_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="receiptId != null">#{receiptId,jdbcType=VARCHAR},</if>
        <if test="payOrderNo != null">#{payOrderNo,jdbcType=VARCHAR},</if>
        <if test="receiptForm != null">#{receiptForm,jdbcType=LONGVARCHAR},</if>
        <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
        <if test="receiptTitle != null">#{receiptTitle,jdbcType=VARCHAR},</if>
        <if test="refundBatchNo != null">#{refundBatchNo,jdbcType=VARCHAR},</if>
        <if test="shortRefundSn != null">#{shortRefundSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="receiptRefundSn != null">#{receiptRefundSn,jdbcType=VARCHAR},</if>
        <if test="shortPayOrderNo != null">#{shortPayOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="handler != null">#{handler,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="isPartRefund != null">#{isPartRefund,jdbcType=TINYINT},</if>
        <if test="refundSource != null">#{refundSource,jdbcType=TINYINT},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="poundage != null">#{poundage,jdbcType=DECIMAL},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getRefundSum" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.OrderRefundTypeDTO">
        select pay_order_no orderSn,is_part_refund isPartRefund,sum(refund_money) refundMoney,sum(poundage) refundFee, count(*) refundedCount from tp_receipt_refund
        WHERE `pay_order_no` IN
        <foreach collection="list" item="payOrderNo" separator="," open="(" close=")">
            #{payOrderNo,jdbcType=VARCHAR}
        </foreach>
    </operation>
    </table>
