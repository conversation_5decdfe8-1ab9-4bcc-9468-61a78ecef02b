<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_QRORDERING_ACTIVITY_GOODS" physicalName="TP_QRORDERING_ACTIVITY_GOODS"
       remark="活动商品表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_QRORDERING_ACTIVITY_GOODS">
        INSERT INTO TP_QRORDERING_ACTIVITY_GOODS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="unit != null">`UNIT`,</if>
            <if test="picture != null">`PICTURE`,</if>
            <if test="goodsName != null">`GOODS_NAME`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="description != null">`DESCRIPTION`,</if>
            <if test="activityGoodsId != null">`ACTIVITY_GOODS_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="purchaseLimit != null">`PURCHASE_LIMIT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="activityPrice != null">`ACTIVITY_PRICE`,</if>
            <if test="originalPrice != null">`ORIGINAL_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="unit != null">#{unit,jdbcType=VARCHAR},</if>
            <if test="picture != null">#{picture,jdbcType=VARCHAR},</if>
            <if test="goodsName != null">#{goodsName,jdbcType=VARCHAR},</if>
            <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
            <if test="description != null">#{description,jdbcType=VARCHAR},</if>
            <if test="activityGoodsId != null">#{activityGoodsId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="purchaseLimit != null">#{purchaseLimit,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="activityPrice != null">#{activityPrice,jdbcType=DECIMAL},</if>
            <if test="originalPrice != null">#{originalPrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findByActivityGoodsIds" paramtype="primitive" multiplicity="many"
               remark="根据活动id查询活动商品">
        select
        ACTIVITY_GOODS_ID,
        GOODS_NAME,
        ACTIVITY_PRICE,
        UNIT

        from TP_QRORDERING_ACTIVITY_GOODS
        where ACTIVITY_GOODS_ID in
        <foreach collection="list" item="activityGoodsId" open="(" close=")" separator=",">
            #{activityGoodsId,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
