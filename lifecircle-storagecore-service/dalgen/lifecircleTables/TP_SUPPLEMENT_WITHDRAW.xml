<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_SUPPLEMENT_WITHDRAW" physicalName="TP_SUPPLEMENT_WITHDRAW"
    remark="提现记录表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_SUPPLEMENT_WITHDRAW">
INSERT INTO TP_SUPPLEMENT_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="info != null">`INFO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardBank != null">`CARD_BANK`,</if>
        <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
        <if test="bankAccountId != null">`BANK_ACCOUNT_ID`,</if>
        <if test="newSerialNumber != null">`NEW_SERIAL_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="adminId != null">`ADMIN_ID`,</if>
        <if test="bankCode != null">`BANK_CODE`,</if>
        <if test="cashType != null">`CASH_TYPE`,</if>
        <if test="tranTime != null">`TRAN_TIME`,</if>
        <if test="cashStatus != null">`CASH_STATUS`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="info != null">#{info,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
        <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
        <if test="bankAccountId != null">#{bankAccountId,jdbcType=VARCHAR},</if>
        <if test="newSerialNumber != null">#{newSerialNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="adminId != null">#{adminId,jdbcType=INTEGER},</if>
        <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
        <if test="cashType != null">#{cashType,jdbcType=INTEGER},</if>
        <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
        <if test="cashStatus != null">#{cashStatus,jdbcType=TINYINT},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getDiffSupplementWithdrawList" multiplicity="many" remark="批量查询状态不为成功的提现数据">
        SELECT * FROM
        TP_SUPPLEMENT_WITHDRAW
        WHERE NEW_SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
        AND cash_status in (1,2,3,4,5,6)
    </operation>

    <operation name="updateDiffWithdraw" paramtype="object" remark="更新补提现状态和时间(单条)">
            update TP_SUPPLEMENT_WITHDRAW
            set
            finish_time = #{finishTime,jdbcType=INTEGER},
            tran_time = #{tranTime,jdbcType=INTEGER},
            cash_status  = 0
            where NEW_SERIAL_NUMBER=#{newSerialNumber,jdbcType=VARCHAR}
    </operation>

    <operation name="updateDiffWithdrawBySxPay" paramtype="object" remark="更新补提现状态和时间(单条)">
        update TP_SUPPLEMENT_WITHDRAW
        set
        tran_time = #{tranTime,jdbcType=INTEGER},
        cash_status = 0
        where NEW_SERIAL_NUMBER=#{newSerialNumber,jdbcType=VARCHAR}
    </operation>
    </table>
