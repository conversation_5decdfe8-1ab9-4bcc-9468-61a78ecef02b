<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_ROLE" physicalName="TP_LIFECIRCLE_ROLE"
    remark="TP_LIFECIRCLE_ROLE">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_ROLE">
INSERT INTO TP_LIFECIRCLE_ROLE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="name != null">`NAME`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="accessList != null">`ACCESS_LIST`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="isShow != null">`IS_SHOW`,</if>
        <if test="isRefund != null">`IS_REFUND`,</if>
        <if test="isWaiter != null">`IS_WAITER`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="isMoreLogin != null">`IS_MORE_LOGIN`,</if>
        <if test="isCollectMoney != null">`IS_COLLECT_MONEY`,</if>
        <if test="isMemberCashReceipt != null">`IS_MEMBER_CASH_RECEIPT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="name != null">#{name,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="accessList != null">#{accessList,jdbcType=LONGVARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
        <if test="isRefund != null">#{isRefund,jdbcType=TINYINT},</if>
        <if test="isWaiter != null">#{isWaiter,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="isMoreLogin != null">#{isMoreLogin,jdbcType=TINYINT},</if>
        <if test="isCollectMoney != null">#{isCollectMoney,jdbcType=TINYINT},</if>
        <if test="isMemberCashReceipt != null">#{isMemberCashReceipt,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getRoleDetailByRoleIdList" multiplicity="many" remark="通过角色id集合获取角色详情">
        SELECT *
        FROM TP_LIFECIRCLE_ROLE
        WHERE id IN
        <foreach collection="list" item="id" separator="," open="(" close=")">
            #{id,jdbcType=INTEGER}
        </foreach>
    </operation>
    </table>
