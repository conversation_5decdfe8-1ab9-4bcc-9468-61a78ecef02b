<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_BALANCE_ACCOUNT" physicalName="TP_BALANCE_ACCOUNT"
       remark="商户余额账户表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_BALANCE_ACCOUNT">
        INSERT INTO TP_BALANCE_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="todayBalanceUpdateTime != null">`TODAY_BALANCE_UPDATE_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="todayBalance != null">`TODAY_BALANCE`,</if>
            <if test="totalBalance != null">`TOTAL_BALANCE`,</if>
            <if test="withdrawableBalance != null">`WITHDRAWABLE_BALANCE`,</if>
            <if test="illegalFreezeBalance != null">`ILLEGAL_FREEZE_BALANCE`,</if>
            <if test="settlementFreezeBalance != null">`SETTLEMENT_FREEZE_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="todayBalanceUpdateTime != null">#{todayBalanceUpdateTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="todayBalance != null">#{todayBalance,jdbcType=DECIMAL},</if>
            <if test="totalBalance != null">#{totalBalance,jdbcType=DECIMAL},</if>
            <if test="withdrawableBalance != null">#{withdrawableBalance,jdbcType=DECIMAL},</if>
            <if test="illegalFreezeBalance != null">#{illegalFreezeBalance,jdbcType=DECIMAL},</if>
            <if test="settlementFreezeBalance != null">#{settlementFreezeBalance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getByTokenForUpdate" multiplicity="one" remark="根据商户token查询商户并加锁">
        SELECT
        *
        FROM
        TP_BALANCE_ACCOUNT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        LIMIT 1
        FOR UPDATE
    </operation>

    <operation name="updateReduceSettleAddWithdrawByToken" remark="根据token减商户的结算冻结金额和加可提金额">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        settlement_freeze_balance = settlement_freeze_balance - #{tradeMoney,jdbcType=DECIMAL},
        withdrawable_balance = withdrawable_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateReduceSettlementFreezeByToken" remark="根据token减商户的结算冻结金额">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        settlement_freeze_balance = settlement_freeze_balance - #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateAddSettlementFreezeByToken" remark="根据token增加商户的结算冻结金额">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        settlement_freeze_balance = settlement_freeze_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateReduceIllegalAddSettleByToken"
               remark="减违规冻结金额（illegal_freeze_balance）,加结算冻结金额（settlement_freeze_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        illegal_freeze_balance = illegal_freeze_balance - #{tradeMoney,jdbcType=DECIMAL},
        settlement_freeze_balance = settlement_freeze_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateReduceIllegalAddWithdrawByToken"
               remark="减违规冻结金额（illegal_freeze_balance）,加可提金额（withdrawable_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        illegal_freeze_balance = illegal_freeze_balance - #{tradeMoney,jdbcType=DECIMAL},
        withdrawable_balance = withdrawable_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateReduceIllegalReduceTotalByToken"
               remark="减违规冻结金额（illegal_freeze_balance）,减总余额（total_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        illegal_freeze_balance = illegal_freeze_balance - #{tradeMoney,jdbcType=DECIMAL},
        total_balance = total_balance - #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateAddIllegalByToken" remark="加违规冻结金额（illegal_freeze_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        illegal_freeze_balance = illegal_freeze_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateAddWithdrawByToken" remark="增加可提现金额（withdrawable_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        withdrawable_balance = withdrawable_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateReduceTotalByToken" remark="回盘提现，减总余额（total_balance）">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        total_balance = total_balance - #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="updateAddShareFreezeBalanceByToken" remark="根据token增加分账冻结余额">
        UPDATE
        TP_BALANCE_ACCOUNT
        SET
        share_freeze_balance = share_freeze_balance + #{tradeMoney,jdbcType=DECIMAL}
        WHERE
        token = #{token,jdbcType=VARCHAR}
    </operation>
    <operation name="updateReduceTotalBalanceAndSettleFreezeBalanceByToken" remark="通过token扣减总余额和冻结余额">
        update TP_BALANCE_ACCOUNT
        set
        total_balance = #{afterTotalBalance,jdbcType=DECIMAL},
        settlement_freeze_balance = #{afterSettlementFreezeBalance,jdbcType=DECIMAL},
        withdrawable_balance = #{afterWithdrawBalance,jdbcType=DECIMAL}
        where token = #{token,jdbcType=VARCHAR}
    </operation>
</table>
