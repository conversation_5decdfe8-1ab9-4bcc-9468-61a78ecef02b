<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND" physicalName="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND"
       remark="万达数据同步临时扩展表">

    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="clientBankPic != null">`CLIENT_BANK_PIC`,</if>
            <if test="preCheckErrMsg != null">`PRE_CHECK_ERR_MSG`,</if>
            <if test="clientLicensePic != null">`CLIENT_LICENSE_PIC`,</if>
            <if test="licenseOcrResult != null">`LICENSE_OCR_RESULT`,</if>
            <if test="clientCheckErrMsg != null">`CLIENT_CHECK_ERR_MSG`,</if>
            <if test="clientMerchantInfo != null">`CLIENT_MERCHANT_INFO`,</if>
            <if test="clientBankPicOcrRes != null">`CLIENT_BANK_PIC_OCR_RES`,</if>
            <if test="clientLicenseOcrRes != null">`CLIENT_LICENSE_OCR_RES`,</if>
            <if test="clientCheckLegalBackPic != null">`CLIENT_CHECK_LEGAL_BACK_PIC`,</if>
            <if test="clientCheckLegalFrontPic != null">`CLIENT_CHECK_LEGAL_FRONT_PIC`,</if>
            <if test="clientCheckSettleBackPic != null">`CLIENT_CHECK_SETTLE_BACK_PIC`,</if>
            <if test="clientLegalBackPicOrcRes != null">`CLIENT_LEGAL_BACK_PIC_ORC_RES`,</if>
            <if test="clientCheckContactBackPic != null">`CLIENT_CHECK_CONTACT_BACK_PIC`,</if>
            <if test="clientCheckSettleFrontPic != null">`CLIENT_CHECK_SETTLE_FRONT_PIC`,</if>
            <if test="clientLegalFrontPicOrcRes != null">`CLIENT_LEGAL_FRONT_PIC_ORC_RES`,</if>
            <if test="clientSettleBackPicOrcRes != null">`CLIENT_SETTLE_BACK_PIC_ORC_RES`,</if>
            <if test="clientCheckContactFrontPic != null">`CLIENT_CHECK_CONTACT_FRONT_PIC`,</if>
            <if test="clientContactBackPicOrcRes != null">`CLIENT_CONTACT_BACK_PIC_ORC_RES`,</if>
            <if test="clientSettleFrontPicOrcRes != null">`CLIENT_SETTLE_FRONT_PIC_ORC_RES`,</if>
            <if test="clientContactFrontPicOrcRes != null">`CLIENT_CONTACT_FRONT_PIC_ORC_RES`,</if>
            <if test="clientLegalTwoElementAuthInfo != null">`CLIENT_LEGAL_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientBankThreeElementAuthInfo != null">`CLIENT_BANK_THREE_ELEMENT_AUTH_INFO`,</if>
            <if test="clientSettleTwoElementAuthInfo != null">`CLIENT_SETTLE_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientContactTwoElementAuthInfo != null">`CLIENT_CONTACT_TWO_ELEMENT_AUTH_INFO`,</if>
            <if test="clientLicenseThreeElementAuthInfo != null">`CLIENT_LICENSE_THREE_ELEMENT_AUTH_INFO`,</if>
            <if test="legalPersonIdentityCardBackOcrResult != null">`LEGAL_PERSON_IDENTITY_CARD_BACK_OCR_RESULT`,</if>
            <if test="legalPersonIdentityCardFrontOcrResult != null">`LEGAL_PERSON_IDENTITY_CARD_FRONT_OCR_RESULT`,</if>
            <if test="licenseThreeElementAuthenticationResult != null">`LICENSE_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="bankCardThreeElementAuthenticationResult != null">
                `BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="legalPersonIdentityCardTwoElementAuthenticationResult != null">
                `LEGAL_PERSON_IDENTITY_CARD_TWO_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="reCheckFlag != null">`RE_CHECK_FLAG`,</if>
            <if test="preCheckFlag != null">`PRE_CHECK_FLAG`,</if>
            <if test="clientLegalTwoElementAuthResult != null">`CLIENT_LEGAL_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientSettleTwoElementAuthResult != null">`CLIENT_SETTLE_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientContactTwoElementAuthResult != null">`CLIENT_CONTACT_TWO_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientLicenseThreeElementAuthResult != null">`CLIENT_LICENSE_THREE_ELEMENT_AUTH_RESULT`,</if>
            <if test="clientBankCardThreeElementAuthenticationResult != null">
                `CLIENT_BANK_CARD_THREE_ELEMENT_AUTHENTICATION_RESULT`,
            </if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="clientBankPic != null">#{clientBankPic,jdbcType=VARCHAR},</if>
            <if test="preCheckErrMsg != null">#{preCheckErrMsg,jdbcType=LONGVARCHAR},</if>
            <if test="clientLicensePic != null">#{clientLicensePic,jdbcType=VARCHAR},</if>
            <if test="licenseOcrResult != null">#{licenseOcrResult,jdbcType=VARCHAR},</if>
            <if test="clientCheckErrMsg != null">#{clientCheckErrMsg,jdbcType=VARCHAR},</if>
            <if test="clientMerchantInfo != null">#{clientMerchantInfo,jdbcType=VARCHAR},</if>
            <if test="clientBankPicOcrRes != null">#{clientBankPicOcrRes,jdbcType=VARCHAR},</if>
            <if test="clientLicenseOcrRes != null">#{clientLicenseOcrRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckLegalBackPic != null">#{clientCheckLegalBackPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckLegalFrontPic != null">#{clientCheckLegalFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckSettleBackPic != null">#{clientCheckSettleBackPic,jdbcType=VARCHAR},</if>
            <if test="clientLegalBackPicOrcRes != null">#{clientLegalBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckContactBackPic != null">#{clientCheckContactBackPic,jdbcType=VARCHAR},</if>
            <if test="clientCheckSettleFrontPic != null">#{clientCheckSettleFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientLegalFrontPicOrcRes != null">#{clientLegalFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientSettleBackPicOrcRes != null">#{clientSettleBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientCheckContactFrontPic != null">#{clientCheckContactFrontPic,jdbcType=VARCHAR},</if>
            <if test="clientContactBackPicOrcRes != null">#{clientContactBackPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientSettleFrontPicOrcRes != null">#{clientSettleFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientContactFrontPicOrcRes != null">#{clientContactFrontPicOrcRes,jdbcType=VARCHAR},</if>
            <if test="clientLegalTwoElementAuthInfo != null">#{clientLegalTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientBankThreeElementAuthInfo != null">#{clientBankThreeElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientSettleTwoElementAuthInfo != null">#{clientSettleTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientContactTwoElementAuthInfo != null">#{clientContactTwoElementAuthInfo,jdbcType=VARCHAR},</if>
            <if test="clientLicenseThreeElementAuthInfo != null">
                #{clientLicenseThreeElementAuthInfo,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardBackOcrResult != null">
                #{legalPersonIdentityCardBackOcrResult,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardFrontOcrResult != null">
                #{legalPersonIdentityCardFrontOcrResult,jdbcType=VARCHAR},
            </if>
            <if test="licenseThreeElementAuthenticationResult != null">
                #{licenseThreeElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="bankCardThreeElementAuthenticationResult != null">
                #{bankCardThreeElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="legalPersonIdentityCardTwoElementAuthenticationResult != null">
                #{legalPersonIdentityCardTwoElementAuthenticationResult,jdbcType=VARCHAR},
            </if>
            <if test="reCheckFlag != null">#{reCheckFlag,jdbcType=TINYINT},</if>
            <if test="preCheckFlag != null">#{preCheckFlag,jdbcType=TINYINT},</if>
            <if test="clientLegalTwoElementAuthResult != null">#{clientLegalTwoElementAuthResult,jdbcType=TINYINT},</if>
            <if test="clientSettleTwoElementAuthResult != null">#{clientSettleTwoElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientContactTwoElementAuthResult != null">
                #{clientContactTwoElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientLicenseThreeElementAuthResult != null">
                #{clientLicenseThreeElementAuthResult,jdbcType=TINYINT},
            </if>
            <if test="clientBankCardThreeElementAuthenticationResult != null">
                #{clientBankCardThreeElementAuthenticationResult,jdbcType=TINYINT},
            </if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="batchSaveWandaStore" paramtype="objectList" remark="丙晟门店批量入驻">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP_EXTEND
        (`store_id`,`pre_check_err_msg`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (#{item.storeId,jdbcType=BIGINT},'')
        </foreach>
    </operation>


    <operation name="getMaxBsStoreId" resulttype="java.lang.Long" remark="获取丙晟最大门店ID">
        SELECT MAX( `store_id`)  FROM `tp_lifecircle_wanda_data_sync_temp_extend`
        WHERE `store_id` LIKE '900%';
    </operation>
</table>
