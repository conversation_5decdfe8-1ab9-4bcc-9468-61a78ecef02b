<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_WANDA_ORG" physicalName="TP_WANDA_ORG" remark="万达组织信息">

    <operation name="insert" paramtype="object" remark="insert:TP_WANDA_ORG">
        INSERT INTO TP_WANDA_ORG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="orgId != null">ORG_ID,</if>
            <if test="orgName != null">ORG_NAME,</if>
            <if test="fubeiOrgId != null">FUBEI_ORG_ID,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="orgName != null">#{orgName,jdbcType=VARCHAR},</if>
            <if test="fubeiOrgId != null">#{fubeiOrgId,jdbcType=VARCHAR},</if>
        </trim>
    </operation>


    <operation name="getOneByOrgId" paramtype="primitive" multiplicity="one" remark="根据组织ID获取万达组织配置">
        select * from tp_wanda_org where org_id = #{orgId, jdbcType=VARCHAR} limit 1
    </operation>

    <operation name="getOneByOrgName" paramtype="primitive" multiplicity="one" remark="根据组织名称获取万达组织配置">
        select * from tp_wanda_org where org_name = #{orgName, jdbcType=VARCHAR} limit 1
    </operation>
</table>
