<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_REFUND" physicalName="TP_LIFECIRCLE_REFUND"
       remark="TP_LIFECIRCLE_REFUND">

    <operation name="findOrderRefundType" multiplicity="many"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.OrderRefundTypeDTO">
        select
        order_sn as orderSn,
        is_part_refund as isPartRefund,
        sum( `refund_money`) as refundMoney,
        sum(poundage) as refundFee,
        count(*) as refundedCount
        from tp_lifecircle_refund
        where refund_status in (1,2)
        and order_sn in
        <foreach collection="list" item="orderSn" open="(" separator="," close=")">
            #{orderSn,jdbcType=INTEGER}
        </foreach>
        GROUP BY order_sn
    </operation>

</table>
