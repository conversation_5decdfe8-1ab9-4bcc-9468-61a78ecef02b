<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD" physicalName="TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD"
       remark="商户证件临期记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD">
        INSERT INTO TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="recordId != null">`RECORD_ID`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="licensePic != null">`LICENSE_PIC`,</if>
            <if test="licenseEndDate != null">`LICENSE_END_DATE`,</if>
            <if test="licenseBeginDate != null">`LICENSE_BEGIN_DATE`,</if>
            <if test="legalIdCardBackPic != null">`LEGAL_ID_CARD_BACK_PIC`,</if>
            <if test="legalIdCardEndDate != null">`LEGAL_ID_CARD_END_DATE`,</if>
            <if test="legalIdCardFrontPic != null">`LEGAL_ID_CARD_FRONT_PIC`,</if>
            <if test="legalIdCardBeginDate != null">`LEGAL_ID_CARD_BEGIN_DATE`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="originalLicenseEndDate != null">`ORIGINAL_LICENSE_END_DATE`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="originalLegalIdCardEndDate != null">`ORIGINAL_LEGAL_ID_CARD_END_DATE`,</if>
            <if test="originalSettlerIdCardEndDate != null">`ORIGINAL_SETTLER_ID_CARD_END_DATE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="salemanId != null">`SALEMAN_ID`,</if>
            <if test="licenseIsLong != null">`LICENSE_IS_LONG`,</if>
            <if test="processStatus != null">`PROCESS_STATUS`,</if>
            <if test="legalIdCardIsLong != null">`LEGAL_ID_CARD_IS_LONG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="qualification != null">`QUALIFICATION`,</if>
            <if test="credentialsStatus != null">`CREDENTIALS_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="merchantNo != null"> `MERCHANT_NO`,</if>
            <if test="isNotice != null">`IS_NOTICE`,</if>
            <if test="legalIdCardExpiredFlag != null">`legal_id_card_expired_flag`,</if>
            <if test="licenseExpiredFlag != null">`license_expired_flag`,</if>
            <if test="settlerIdCardExpiredFlag != null">`settler_id_card_expired_flag`,</if>
            <if test="last30TradeNum != null">`last_30_trade_num`,</if>
            <if test="last30TradeMoney != null">`last_30_trade_money`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
            <if test="licenseEndDate != null">#{licenseEndDate,jdbcType=VARCHAR},</if>
            <if test="licenseBeginDate != null">#{licenseBeginDate,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBackPic != null">#{legalIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardEndDate != null">#{legalIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="legalIdCardFrontPic != null">#{legalIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBeginDate != null">#{legalIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="originalLicenseEndDate != null">#{originalLicenseEndDate,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="originalLegalIdCardEndDate != null">#{originalLegalIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="originalSettlerIdCardEndDate != null">#{originalSettlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=TINYINT},</if>
            <if test="salemanId != null">#{salemanId,jdbcType=INTEGER},</if>
            <if test="licenseIsLong != null">#{licenseIsLong,jdbcType=TINYINT},</if>
            <if test="processStatus != null">#{processStatus,jdbcType=TINYINT},</if>
            <if test="legalIdCardIsLong != null">#{legalIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="qualification != null">#{qualification,jdbcType=TINYINT},</if>
            <if test="credentialsStatus != null">#{credentialsStatus,jdbcType=TINYINT},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="isNotice != null">#{isNotice,jdbcType=TINYINT},</if>
            <if test="legalIdCardExpiredFlag != null">#{legalIdCardExpiredFlag,jdbcType=TINYINT},</if>
            <if test="licenseExpiredFlag != null">#{licenseExpiredFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardExpiredFlag != null">#{settlerIdCardExpiredFlag,jdbcType=TINYINT},</if>
            <if test="last30TradeNum != null">#{last30TradeNum,jdbcType=INTEGER},</if>
            <if test="last30TradeMoney != null">#{last30TradeMoney,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getByUid" paramtype="primitive" multiplicity="one" remark="根据商户id查询临期记录">
        SELECT
        *
        FROM
        `TP_MERCHANT_CREDENTIALS_EXPIRE_RECORD`
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        and process_status in (0, 1, 3)
        AND `IS_DEL` = 0
        limit 1;
    </operation>
</table>
