<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_ACTIVITY_MCHID" physicalName="TP_LIFECIRCLE_ACTIVITY_MCHID"
    remark="活动子商户号表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_ACTIVITY_MCHID">
INSERT INTO TP_LIFECIRCLE_ACTIVITY_MCHID
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="errorMsg != null">`ERROR_MSG`,</if>
        <if test="subMchId != null">`SUB_MCH_ID`,</if>
        <if test="shortName != null">`SHORT_NAME`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="merchantName != null">`MERCHANT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="step != null">`STEP`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="isFinish != null">`IS_FINISH`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="storeStatus != null">`STORE_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
        <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
        <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
        <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
        <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="step != null">#{step,jdbcType=TINYINT},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="storeStatus != null">#{storeStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getBySubMchId" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        uid
        FROM
        TP_LIFECIRCLE_ACTIVITY_MCHID
        WHERE
        sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getInfoBySubMchId" multiplicity="one" remark="根据子商户号查询信息">
        SELECT
        *
        FROM
        TP_LIFECIRCLE_ACTIVITY_MCHID
        WHERE
        sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
