<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_LESHUA_ENTRY_APPLICATION" physicalName="TP_MERCHANT_LESHUA_ENTRY_APPLICATION"
       remark="乐刷直连独立结算商户进件申请表">

    <operation multiplicity="one" name="getByAlipaySmid" remark="获取高校独立结算信息">
        select * from TP_MERCHANT_LESHUA_ENTRY_APPLICATION
        where uid = #{uid,jdbcType=INTEGER} and alipay_smid = #{alipaySmid,jdbcType=VARCHAR}
    </operation>

    <operation multiplicity="one" name="getByMerchantNo" remark="根据乐刷商户获取申请单">
        SELECT *
        FROM `tp_merchant_leshua_entry_application`
        WHERE `merchant_no`= #{merchantNo,jdbcType=VARCHAR}
        LIMIT 1;
    </operation>
    <operation multiplicity="one" name="getByUidAndStoreIdAndShopType" remark="根据商户ID,门店ID以及总行类型获取申请单">
        SELECT *
        FROM `tp_merchant_leshua_entry_application`
        where uid = #{uid,jdbcType=INTEGER}
        <if test="storeId != null">
            and `store_id` = #{storeId,jdbcType=INTEGER}
        </if>
        <if test="shopType != null">
            and shop_type = #{shopType,jdbcType=INTEGER}
        </if>
        ORDER BY id asc
        LIMIT 1;
    </operation>
</table>
