<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_BIND_BANK" physicalName="TP_LIFECIRCLE_BIND_BANK"
       remark="TP_LIFECIRCLE_BIND_BANK">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_BIND_BANK">
        INSERT INTO TP_LIFECIRCLE_BIND_BANK
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="acctId != null">`ACCT_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="cardPic != null">`CARD_PIC`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="idNumber != null">`ID_NUMBER`,</if>
            <if test="isActive != null">`IS_ACTIVE`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="addFrom != null">`ADD_FROM`,</if>
            <if test="bindWay != null">`BIND_WAY`,</if>
            <if test="bindFrom != null">`BIND_FROM`,</if>
            <if test="bindType != null">`BIND_TYPE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="acctId != null">#{acctId,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="cardPic != null">#{cardPic,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="idNumber != null">#{idNumber,jdbcType=VARCHAR},</if>
            <if test="isActive != null">#{isActive,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="addFrom != null">#{addFrom,jdbcType=TINYINT},</if>
            <if test="bindWay != null">#{bindWay,jdbcType=INTEGER},</if>
            <if test="bindFrom != null">#{bindFrom,jdbcType=TINYINT},</if>
            <if test="bindType != null">#{bindType,jdbcType=INTEGER},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
        </trim>
    </operation>

    <operation name="getCardBankAndCardNoById" multiplicity="many" remark="根据id批量查询出银行卡信息">
        select
        *
        from
        tp_lifecircle_bind_bank
        where
        id in
        <foreach close=")" collection="list" index="index" item="id" open="(" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="getCardInfoByAcctIdList" multiplicity="many" remark="根据银行卡得到银行卡信息">
        select
        *
        from
        tp_lifecircle_bind_bank
        where
        is_del = 0
        and
        bind_status = 1
        and
        is_active = 'y'
        and
        acct_id in
        <foreach close=")" collection="list" index="index" item="acctId" open="(" separator=",">
            #{acctId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getByToken" multiplicity="one" remark="根据token和当前date查询批量登记挂账数据列表">
        SELECT *
        FROM
        tp_lifecircle_bind_bank
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND bind_status = 1
        AND check_status in (0,1)
        AND is_del = 0
        AND is_active = 'y'
        ORDER BY update_time DESC
        LIMIT 1
    </operation>
    <operation name="getLastByToken" multiplicity="one" remark="通过token获取商户最新一条绑卡记录">
        select * from tp_lifecircle_bind_bank
        where token = #{token,jdbcType=VARCHAR}
        order by id desc limit 1
    </operation>

    <operation name="getBindBankByUid" multiplicity="one" remark="根据uid查询商户所有的绑卡记录">
        SELECT
        *
        FROM
        tp_lifecircle_bind_bank
        WHERE
        uid = #{uid, jdbcType=INTEGER}
        and is_del = 0
        limit 1
    </operation>
</table>
