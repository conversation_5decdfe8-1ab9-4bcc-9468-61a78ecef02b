<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG" physicalName="TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG"
       remark="预付卡上报监管平台记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG">
        INSERT INTO TP_PREPAY_CARD_UPLOAD_REGULATORY_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="flowNo != null">`FLOW_NO`,</if>
            <if test="orgIdList != null">`ORG_ID_LIST`,</if>
            <if test="orgNumber != null">`ORG_NUMBER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="uploadTime != null">`UPLOAD_TIME`,</if>
            <if test="uploadStatus != null">`UPLOAD_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="calculateEndTime != null">`CALCULATE_END_TIME`,</if>
            <if test="calculateStartTime != null">`CALCULATE_START_TIME`,</if>
            <if test="fileOssUrl != null">`file_oss_url`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="flowNo != null">#{flowNo,jdbcType=VARCHAR},</if>
            <if test="orgIdList != null">#{orgIdList,jdbcType=VARCHAR},</if>
            <if test="orgNumber != null">#{orgNumber,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="uploadTime != null">#{uploadTime,jdbcType=INTEGER},</if>
            <if test="uploadStatus != null">#{uploadStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="calculateEndTime != null">#{calculateEndTime,jdbcType=TIMESTAMP},</if>
            <if test="calculateStartTime != null">#{calculateStartTime,jdbcType=TIMESTAMP},</if>
            <if test="fileOssUrl != null">#{fileOssUrl,jdbcType=VARCHAR},</if>
        </trim>
    </operation>
</table>
