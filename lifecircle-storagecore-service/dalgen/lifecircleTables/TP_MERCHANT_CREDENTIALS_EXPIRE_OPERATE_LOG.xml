<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG" physicalName="TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG"
    remark="商户证件临期记录操作日志表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG">
INSERT INTO TP_MERCHANT_CREDENTIALS_EXPIRE_OPERATE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="content != null">`CONTENT`,</if>
        <if test="operator != null">`OPERATOR`,</if>
        <if test="recordId != null">`RECORD_ID`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="content != null">#{content,jdbcType=VARCHAR},</if>
        <if test="operator != null">#{operator,jdbcType=VARCHAR},</if>
        <if test="recordId != null">#{recordId,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>
    </table>
