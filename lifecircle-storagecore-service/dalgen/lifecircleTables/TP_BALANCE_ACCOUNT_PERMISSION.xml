<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_BALANCE_ACCOUNT_PERMISSION" physicalName="TP_BALANCE_ACCOUNT_PERMISSION"
       remark="商户余额账户权限">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_BALANCE_ACCOUNT_PERMISSION">
        INSERT INTO TP_BALANCE_ACCOUNT_PERMISSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="withdraw != null">`WITHDRAW`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="quickWithdraw != null">`QUICK_WITHDRAW`,</if>
            <if test="autoWithdraw != null">`AUTOWITHDRAW`,</if>
            <if test="hasWindowpop != null">`HASWINDOWPOP`,</if>
            <if test="positionRefund != null">`POSITION_REFUND`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="withdraw != null">#{withdraw,jdbcType=TINYINT},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="quickWithdraw != null">#{quickWithdraw,jdbcType=TINYINT},</if>
            <if test="autoWithdraw != null">#{autoWithdraw,jdbcType=TINYINT},</if>
            <if test="hasWindowpop != null">#{hasWindowpop,jdbcType=TINYINT},</if>
            <if test="positionRefund != null">#{positionRefund,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByUid" multiplicity="one" remark="根据uid查询商户余额账户权限">
        select * from TP_BALANCE_ACCOUNT_PERMISSION where
        uid=#{uid,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="getByToken" multiplicity="one" remark="根据token查询商户余额账户权限">
        select * from TP_BALANCE_ACCOUNT_PERMISSION where
        token=#{token,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
</table>
