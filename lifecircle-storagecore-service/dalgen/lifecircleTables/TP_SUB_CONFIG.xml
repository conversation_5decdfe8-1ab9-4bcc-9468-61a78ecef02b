<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SUB_CONFIG" physicalName="TP_SUB_CONFIG"
       remark="TP_SUB_CONFIG">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SUB_CONFIG">
        INSERT INTO TP_SUB_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="topMsg != null">`TOP_MSG`,</if>
            <if test="subName != null">`SUB_NAME`,</if>
            <if test="userUrl != null">`USER_URL`,</if>
            <if test="webLogo != null">`WEB_LOGO`,</if>
            <if test="adminUrl != null">`ADMIN_URL`,</if>
            <if test="agentUrl != null">`AGENT_URL`,</if>
            <if test="postLogo != null">`POST_LOGO`,</if>
            <if test="userLogo != null">`USER_LOGO`,</if>
            <if test="adminLogo != null">`ADMIN_LOGO`,</if>
            <if test="agentLogo != null">`AGENT_LOGO`,</if>
            <if test="bottomMsg != null">`BOTTOM_MSG`,</if>
            <if test="qrcodeUrl != null">`QRCODE_URL`,</if>
            <if test="subDomain != null">`SUB_DOMAIN`,</if>
            <if test="wxMpAppid != null">`WX_MP_APPID`,</if>
            <if test="dishesName != null">`DISHES_NAME`,</if>
            <if test="subCompany != null">`SUB_COMPANY`,</if>
            <if test="agentAdText != null">`AGENT_AD_TEXT`,</if>
            <if test="appBindText != null">`APP_BIND_TEXT`,</if>
            <if test="dishesPhone != null">`DISHES_PHONE`,</if>
            <if test="userLoginBg != null">`USER_LOGIN_BG`,</if>
            <if test="voiceRemind != null">`VOICE_REMIND`,</if>
            <if test="welcomeWord != null">`WELCOME_WORD`,</if>
            <if test="adminLoginBg != null">`ADMIN_LOGIN_BG`,</if>
            <if test="agentLoginBg != null">`AGENT_LOGIN_BG`,</if>
            <if test="postDocument != null">`POST_DOCUMENT`,</if>
            <if test="redPacketMsg != null">`RED_PACKET_MSG`,</if>
            <if test="protocolTitle != null">`PROTOCOL_TITLE`,</if>
            <if test="userLoginLogo != null">`USER_LOGIN_LOGO`,</if>
            <if test="wapActiveBand != null">`WAP_ACTIVE_BAND`,</if>
            <if test="wapIndexTitle != null">`WAP_INDEX_TITLE`,</if>
            <if test="wapMemberBand != null">`WAP_MEMBER_BAND`,</if>
            <if test="wapUserHeader != null">`WAP_USER_HEADER`,</if>
            <if test="wxMpAppsecret != null">`WX_MP_APPSECRET`,</if>
            <if test="adminLoginLogo != null">`ADMIN_LOGIN_LOGO`,</if>
            <if test="agentLoginLogo != null">`AGENT_LOGIN_LOGO`,</if>
            <if test="userLoginTitle != null">`USER_LOGIN_TITLE`,</if>
            <if test="adminLoginTitle != null">`ADMIN_LOGIN_TITLE`,</if>
            <if test="agentLoginTitle != null">`AGENT_LOGIN_TITLE`,</if>
            <if test="protocolContent != null">`PROTOCOL_CONTENT`,</if>
            <if test="redPacketNotice != null">`RED_PACKET_NOTICE`,</if>
            <if test="dishesCompanyUrl != null">`DISHES_COMPANY_URL`,</if>
            <if test="paySuccessNotice != null">`PAY_SUCCESS_NOTICE`,</if>
            <if test="qrcodeBackground != null">`QRCODE_BACKGROUND`,</if>
            <if test="wapDefaultHeader != null">`WAP_DEFAULT_HEADER`,</if>
            <if test="appCustomerMobile != null">`APP_CUSTOMER_MOBILE`,</if>
            <if test="brandAbbreviation != null">`BRAND_ABBREVIATION`,</if>
            <if test="wxOfficialAccount != null">`WX_OFFICIAL_ACCOUNT`,</if>
            <if test="wapDefaultNickname != null">`WAP_DEFAULT_NICKNAME`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="videoOnOff != null">`VIDEO_ON_OFF`,</if>
            <if test="voiceOnOff != null">`VOICE_ON_OFF`,</if>
            <if test="bindProtocol != null">`BIND_PROTOCOL`,</if>
            <if test="isOpenDishes != null">`IS_OPEN_DISHES`,</if>
            <if test="isShowProtocol != null">`IS_SHOW_PROTOCOL`,</if>
            <if test="dishesAuthStatus != null">`DISHES_AUTH_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="advertisementRental != null">`ADVERTISEMENT_RENTAL`,</if>
            <if test="advertisementBalance != null">`ADVERTISEMENT_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="topMsg != null">#{topMsg,jdbcType=VARCHAR},</if>
            <if test="subName != null">#{subName,jdbcType=VARCHAR},</if>
            <if test="userUrl != null">#{userUrl,jdbcType=VARCHAR},</if>
            <if test="webLogo != null">#{webLogo,jdbcType=VARCHAR},</if>
            <if test="adminUrl != null">#{adminUrl,jdbcType=VARCHAR},</if>
            <if test="agentUrl != null">#{agentUrl,jdbcType=VARCHAR},</if>
            <if test="postLogo != null">#{postLogo,jdbcType=VARCHAR},</if>
            <if test="userLogo != null">#{userLogo,jdbcType=VARCHAR},</if>
            <if test="adminLogo != null">#{adminLogo,jdbcType=VARCHAR},</if>
            <if test="agentLogo != null">#{agentLogo,jdbcType=VARCHAR},</if>
            <if test="bottomMsg != null">#{bottomMsg,jdbcType=VARCHAR},</if>
            <if test="qrcodeUrl != null">#{qrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="subDomain != null">#{subDomain,jdbcType=VARCHAR},</if>
            <if test="wxMpAppid != null">#{wxMpAppid,jdbcType=VARCHAR},</if>
            <if test="dishesName != null">#{dishesName,jdbcType=CHAR},</if>
            <if test="subCompany != null">#{subCompany,jdbcType=VARCHAR},</if>
            <if test="agentAdText != null">#{agentAdText,jdbcType=VARCHAR},</if>
            <if test="appBindText != null">#{appBindText,jdbcType=VARCHAR},</if>
            <if test="dishesPhone != null">#{dishesPhone,jdbcType=CHAR},</if>
            <if test="userLoginBg != null">#{userLoginBg,jdbcType=VARCHAR},</if>
            <if test="voiceRemind != null">#{voiceRemind,jdbcType=VARCHAR},</if>
            <if test="welcomeWord != null">#{welcomeWord,jdbcType=VARCHAR},</if>
            <if test="adminLoginBg != null">#{adminLoginBg,jdbcType=VARCHAR},</if>
            <if test="agentLoginBg != null">#{agentLoginBg,jdbcType=VARCHAR},</if>
            <if test="postDocument != null">#{postDocument,jdbcType=VARCHAR},</if>
            <if test="redPacketMsg != null">#{redPacketMsg,jdbcType=VARCHAR},</if>
            <if test="protocolTitle != null">#{protocolTitle,jdbcType=VARCHAR},</if>
            <if test="userLoginLogo != null">#{userLoginLogo,jdbcType=VARCHAR},</if>
            <if test="wapActiveBand != null">#{wapActiveBand,jdbcType=VARCHAR},</if>
            <if test="wapIndexTitle != null">#{wapIndexTitle,jdbcType=VARCHAR},</if>
            <if test="wapMemberBand != null">#{wapMemberBand,jdbcType=VARCHAR},</if>
            <if test="wapUserHeader != null">#{wapUserHeader,jdbcType=VARCHAR},</if>
            <if test="wxMpAppsecret != null">#{wxMpAppsecret,jdbcType=VARCHAR},</if>
            <if test="adminLoginLogo != null">#{adminLoginLogo,jdbcType=VARCHAR},</if>
            <if test="agentLoginLogo != null">#{agentLoginLogo,jdbcType=VARCHAR},</if>
            <if test="userLoginTitle != null">#{userLoginTitle,jdbcType=VARCHAR},</if>
            <if test="adminLoginTitle != null">#{adminLoginTitle,jdbcType=VARCHAR},</if>
            <if test="agentLoginTitle != null">#{agentLoginTitle,jdbcType=VARCHAR},</if>
            <if test="protocolContent != null">#{protocolContent,jdbcType=LONGVARCHAR},</if>
            <if test="redPacketNotice != null">#{redPacketNotice,jdbcType=VARCHAR},</if>
            <if test="dishesCompanyUrl != null">#{dishesCompanyUrl,jdbcType=CHAR},</if>
            <if test="paySuccessNotice != null">#{paySuccessNotice,jdbcType=VARCHAR},</if>
            <if test="qrcodeBackground != null">#{qrcodeBackground,jdbcType=VARCHAR},</if>
            <if test="wapDefaultHeader != null">#{wapDefaultHeader,jdbcType=VARCHAR},</if>
            <if test="appCustomerMobile != null">#{appCustomerMobile,jdbcType=VARCHAR},</if>
            <if test="brandAbbreviation != null">#{brandAbbreviation,jdbcType=VARCHAR},</if>
            <if test="wxOfficialAccount != null">#{wxOfficialAccount,jdbcType=VARCHAR},</if>
            <if test="wapDefaultNickname != null">#{wapDefaultNickname,jdbcType=VARCHAR},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="videoOnOff != null">#{videoOnOff,jdbcType=TINYINT},</if>
            <if test="voiceOnOff != null">#{voiceOnOff,jdbcType=TINYINT},</if>
            <if test="bindProtocol != null">#{bindProtocol,jdbcType=TINYINT},</if>
            <if test="isOpenDishes != null">#{isOpenDishes,jdbcType=TINYINT},</if>
            <if test="isShowProtocol != null">#{isShowProtocol,jdbcType=TINYINT},</if>
            <if test="dishesAuthStatus != null">#{dishesAuthStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="advertisementRental != null">#{advertisementRental,jdbcType=DECIMAL},</if>
            <if test="advertisementBalance != null">#{advertisementBalance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getById" paramtype="primitive" multiplicity="one" remark="根据id查询">
        select * from tp_sub_config where id = #{id,jdbcType=INTEGER}
    </operation>
</table>
