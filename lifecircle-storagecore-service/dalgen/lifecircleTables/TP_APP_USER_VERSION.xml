<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_APP_USER_VERSION" physicalName="TP_APP_USER_VERSION"
       remark="TP_APP_USER_VERSION">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_APP_USER_VERSION">
        INSERT INTO TP_APP_USER_VERSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="phoneBrand != null">`PHONE_BRAND`,</if>
            <if test="phoneModel != null">`PHONE_MODEL`,</if>
            <if test="sysVersion != null">`SYS_VERSION`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="versionType != null">`VERSION_TYPE`,</if>
            <if test="pubVersionId != null">`PUB_VERSION_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="phoneBrand != null">#{phoneBrand,jdbcType=VARCHAR},</if>
            <if test="phoneModel != null">#{phoneModel,jdbcType=VARCHAR},</if>
            <if test="sysVersion != null">#{sysVersion,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="versionType != null">#{versionType,jdbcType=TINYINT},</if>
            <if test="pubVersionId != null">#{pubVersionId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    <resultmap name="UserVersionResultMap" type="UserVersionResult">
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="用户 ID"/>
        <column name="phone_model" property="phoneModel" javatype="String" jdbctype="VARCHAR" remark="手机品牌"/>
        <column name="sys_version" property="sysVersion" javatype="String" jdbctype="VARCHAR" remark="系统版本"/>
        <column name="username" javatype="String" jdbctype="VARCHAR" remark="用户名"/>
    </resultmap>
    <operation name="findByVersionId" paramtype="primitive" multiplicity="many" resultmap="UserVersionResultMap"
               remark="根据version_id 查询列表">
        SELECT
        a.uid,
        a.phone_model,
        a.sys_version,
        u.username
        FROM
        tp_app_user_version AS a
        LEFT JOIN tp_users as u ON a.uid = u.id
        WHERE a.pub_version_id = #{versionId,jdbcType=INTEGER}
        AND a.sub_config_id = 0
    </operation>
</table>
