<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table physicalName="TP_USERS_TOKEN" remark="TP_USERS_TOKEN" sqlname="TP_USERS_TOKEN">

    <operation name="insert" paramtype="object" remark="insert:TP_USERS_TOKEN">
        INSERT INTO
        <selectKey keyProperty="token" order="AFTER" resultType="java.lang.Integer">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        tp_users_token(token) VALUES(0)
    </operation>
</table>