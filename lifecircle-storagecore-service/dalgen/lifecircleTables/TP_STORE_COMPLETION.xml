<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_STORE_COMPLETION" physicalName="TP_STORE_COMPLETION"
       remark="门店资料补全表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_STORE_COMPLETION">
        INSERT INTO TP_STORE_COMPLETION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="picMoney != null">`PIC_MONEY`,</if>
            <if test="picInside != null">`PIC_INSIDE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isRead != null">`IS_READ`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="picMoney != null">#{picMoney,jdbcType=VARCHAR},</if>
            <if test="picInside != null">#{picInside,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isRead != null">#{isRead,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <!--根据门店ID查询门店资料补全-->
    <operation name="getByStoreId" multiplicity="one" remark="根据 store_id 查询记录">
        SELECT `pic_inside`,`pic_money`
        FROM TP_STORE_COMPLETION
        WHERE
        STORE_ID
        = #{storeId,jdbcType=INTEGER}
        order by create_time desc
        LIMIT 1
    </operation>
</table>
