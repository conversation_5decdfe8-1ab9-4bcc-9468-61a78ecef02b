<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CRM_PREPOSE_BLACK_LIST" physicalName="TP_CRM_PREPOSE_BLACK_LIST"
       remark="CRM商户前置审核黑名单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CRM_PREPOSE_BLACK_LIST">
        INSERT INTO TP_CRM_PREPOSE_BLACK_LIST
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="sysUserId != null">`SYS_USER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="blackListId != null">`BLACK_LIST_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="sysUserId != null">#{sysUserId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="blackListId != null">#{blackListId,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation multiplicity="one" name="getBySysUserId" remark="根据用户id查询">
        select
        id,
        black_list_id,
        sys_user_id,
        status,
        create_by,
        update_by,
        remarks,
        create_time,
        update_time
        from tp_crm_prepose_black_list
        where sys_user_id = #{sysUserId,jdbcType=INTEGER} and status = 1
    </operation>

    <operation multiplicity="many" name="getBlankList" remark="查询所有生效的黑名单信息">
        select
        id,
        black_list_id,
        sys_user_id,
        status,
        create_by,
        update_by,
        remarks,
        create_time,
        update_time
        from tp_crm_prepose_black_list
        where status = 1
    </operation>

    <resultmap name="preAuditBlackList" type="PreAuditBlackListDO">
        <column javatype="java.lang.String" jdbctype="VARCHAR" name="black_list_id" remark="黑名单ID"/>
        <column javatype="java.lang.Integer" jdbctype="INTEGER" name="sys_user_id" remark="代理商或授理商ID"/>
        <column javatype="java.lang.String" jdbctype="VARCHAR" name="username" remark="代理商或授理商账号"/>
        <column javatype="java.lang.Integer" jdbctype="TINYINT" name="is_salesman" remark="账号类型"/>
        <column javatype="java.util.Date" jdbctype="DATE" name="create_time" remark="审核时间"/>
    </resultmap>

    <operation multiplicity="paging" name="getPreAuditBlackListListBySearch" paging="GetPreAuditBlackListList"
               remark="查询前置黑名单列表" resultmap="preAuditBlackList">
        SELECT
        a.black_list_id, a.sys_user_id, a.create_time, b.username, b.is_salesman
        FROM tp_crm_prepose_black_list a
        LEFT JOIN tp_user b ON a.sys_user_id = b.id
        WHERE a.status = 1
        <if test="isSalesman != null and isSalesman != -1">
            AND b.is_salesman = #{isSalesman,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND b.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')

        </if>
        <if test="beginDate != null and endDate != null">
            AND a.create_time BETWEEN #{beginDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        </if>
        ORDER BY a.create_time DESC
    </operation>

    <operation multiplicity="many" name="exportPreAuditBlackListListBySearch" remark="导出前置黑名单列表"
               resultmap="preAuditBlackList">
        SELECT
        a.black_list_id, a.sys_user_id, a.create_time, b.username, b.is_salesman
        FROM tp_crm_prepose_black_list a
        LEFT JOIN tp_user b ON a.sys_user_id = b.id
        WHERE a.status = 1
        <if test="isSalesman != null and isSalesman != -1">
            AND b.is_salesman = #{isSalesman,jdbcType=INTEGER}
        </if>
        <if test="username != null and username != ''">
            AND b.username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="beginDate != null and endDate != null">
            AND a.create_time BETWEEN #{beginDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
        </if>
        ORDER BY a.create_time DESC
    </operation>

</table>
