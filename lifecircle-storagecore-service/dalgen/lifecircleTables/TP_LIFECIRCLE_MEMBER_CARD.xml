<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_MEMBER_CARD" physicalName="TP_LIFECIRCLE_MEMBER_CARD"
    remark="生活圈会员卡表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_MEMBER_CARD">
INSERT INTO TP_LIFECIRCLE_MEMBER_CARD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="tplId != null">`TPL_ID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="isShowAmount != null">`IS_SHOW_AMOUNT`,</if>
        <if test="recommendUid != null">`RECOMMEND_UID`,</if>
        <if test="activationTime != null">`ACTIVATION_TIME`,</if>
        <if test="lastConsumeTime != null">`LAST_CONSUME_TIME`,</if>
        <if test="recharge != null">`RECHARGE`,</if>
        <if test="totalAmount != null">`TOTAL_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="tplId != null">#{tplId,jdbcType=INTEGER},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="isShowAmount != null">#{isShowAmount,jdbcType=TINYINT},</if>
        <if test="recommendUid != null">#{recommendUid,jdbcType=INTEGER},</if>
        <if test="activationTime != null">#{activationTime,jdbcType=INTEGER},</if>
        <if test="lastConsumeTime != null">#{lastConsumeTime,jdbcType=INTEGER},</if>
        <if test="recharge != null">#{recharge,jdbcType=DECIMAL},</if>
        <if test="totalAmount != null">#{totalAmount,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="countByUserIdsAndToken" resulttype="Integer" remark="查询用户在某个商家已激活的会员卡">
        SELECT
        count(1)
        FROM
        TP_LIFECIRCLE_MEMBER_CARD
        WHERE
        token = #{token,jdbcType=VARCHAR} and
        card_type = 1 and
        status = 1 and
        user_id in (
        <foreach collection="userIds" item="userId" separator=",">
            #{userId, jdbcType=INTEGER}
        </foreach>
        )
    </operation>

    </table>
