<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ALIPAY_DIRECT_BILL" physicalName="TP_ALIPAY_DIRECT_BILL"
       remark="支付宝直连对账单文件记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_ALIPAY_DIRECT_BILL">
        INSERT INTO TP_ALIPAY_DIRECT_BILL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="billType != null">`BILL_TYPE`,</if>
            <if test="ossBucket != null">`OSS_BUCKET`,</if>
            <if test="billFileUrl != null">`BILL_FILE_URL`,</if>
            <if test="billStatus != null">`BILL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=CHAR},</if>
            <if test="billType != null">#{billType,jdbcType=VARCHAR},</if>
            <if test="ossBucket != null">#{ossBucket,jdbcType=VARCHAR},</if>
            <if test="billFileUrl != null">#{billFileUrl,jdbcType=VARCHAR},</if>
            <if test="billStatus != null">#{billStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="queryBillRecordByParam" remark="根据商户token和账单日期查询账单">
        SELECT
        *
        FROM
        TP_ALIPAY_DIRECT_BILL
        WHERE TOKEN = #{token,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER}
        AND BILL_TYPE = #{billType,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="updateBillStatusToSuccess" paramtype="object" remark="将对账单上传状态变更为上传成功">
        UPDATE
        TP_ALIPAY_DIRECT_BILL
        SET BILL_STATUS = #{billStatus,jdbcType=INTEGER},
        BILL_FILE_URL = #{billFileUrl,jdbcType=VARCHAR},
        REMARK = #{remark,jdbcType=VARCHAR}
        WHERE TOKEN = #{token,jdbcType=VARCHAR}
        AND BILL_DATE = #{billDate,jdbcType=INTEGER}
        AND BILL_TYPE = #{billType,jdbcType=VARCHAR}
    </operation>
</table>
