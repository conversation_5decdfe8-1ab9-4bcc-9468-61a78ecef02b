<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ALIPAY_TEMPLATE_MINA_BIND_CODE" physicalName="TP_ALIPAY_TEMPLATE_MINA_BIND_CODE"
       remark="支付宝模板小程序绑定 code 码表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_ALIPAY_TEMPLATE_MINA_BIND_CODE">
        INSERT INTO TP_ALIPAY_TEMPLATE_MINA_BIND_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="codeUrl != null">`CODE_URL`,</if>
            <if test="alipayRouteGroup != null">`ALIPAY_ROUTE_GROUP`,</if>
            <if test="alipayTemplateUrl != null">`ALIPAY_TEMPLATE_URL`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="codeType != null">`CODE_TYPE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="codeUrl != null">#{codeUrl,jdbcType=VARCHAR},</if>
            <if test="alipayRouteGroup != null">#{alipayRouteGroup,jdbcType=VARCHAR},</if>
            <if test="alipayTemplateUrl != null">#{alipayTemplateUrl,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="codeType != null">#{codeType,jdbcType=TINYINT},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="unbindById" paramtype="primitive" remark="解绑二维码绑定状态">
        UPDATE tp_alipay_template_mina_bind_code
        SET del_flag = 1
        WHERE alipay_route_group = #{alipayRouteGroup,jdbcType=VARCHAR}
    </operation>

</table>
