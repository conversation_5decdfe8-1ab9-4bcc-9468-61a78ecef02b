<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_APP_GRAY_MERCHANT" physicalName="TP_APP_GRAY_MERCHANT"
       remark="灰度商户表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_APP_GRAY_MERCHANT">
        INSERT INTO TP_APP_GRAY_MERCHANT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceSn != null">`DEVICE_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="grayFileId != null">`GRAY_FILE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceSn != null">#{deviceSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="grayFileId != null">#{grayFileId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO TP_APP_GRAY_MERCHANT
        (
        TOKEN,UID,GRAY_FILE_ID
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.grayFileId,jdbcType=INTEGER}
            )
        </foreach>

    </operation>
    <operation name="insertDeviceBatch" paramtype="objectList" remark="批量插入灰度设备">
        INSERT INTO TP_APP_GRAY_MERCHANT
        (
        TOKEN,UID,GRAY_FILE_ID,DEVICE_SN
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.grayFileId,jdbcType=INTEGER},
            #{item.deviceSn,jdbcType=VARCHAR}
            )
        </foreach>

    </operation>
</table>
