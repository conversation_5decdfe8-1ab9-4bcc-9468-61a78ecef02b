<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_AGENT_OPEN" physicalName="TP_LIFECIRCLE_AGENT_OPEN"
       remark="开放平台代理商配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_AGENT_OPEN">
        INSERT INTO TP_LIFECIRCLE_AGENT_OPEN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="secret != null">`SECRET`,</if>
            <if test="callbackUrl != null">`CALLBACK_URL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="pid != null">#{pid,jdbcType=VARCHAR},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="secret != null">#{secret,jdbcType=VARCHAR},</if>
            <if test="callbackUrl != null">#{callbackUrl,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation remark="通过代理商id获取配置" name="getInfoByAgentId" multiplicity="one">
        SELECT *
        FROM TP_LIFECIRCLE_AGENT_OPEN
        WHERE agent_id = #{agentId,jdbcType=INTEGER}
        LIMIT 1
    </operation>

</table>
