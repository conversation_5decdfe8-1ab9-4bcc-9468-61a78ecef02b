<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_COLLEGE_HARDWARE_CONFIG" physicalName="TP_COLLEGE_HARDWARE_CONFIG"
       remark="微信高校硬件配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_COLLEGE_HARDWARE_CONFIG">
        INSERT INTO TP_COLLEGE_HARDWARE_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="isDelete != null">`IS_DELETE`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="isDelete != null">#{isDelete,jdbcType=TINYINT},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getCollegeHardwareList" multiplicity="many" remark="获取微信高校硬件">
        SELECT
        TOKEN,DEVICE_NO,USER_ID,STORE_ID
        from TP_COLLEGE_HARDWARE_CONFIG WHERE type = #{type,jdbcType=INTEGER}
        AND IS_DELETE = 0
        AND USER_ID > 0;
    </operation>


</table>
