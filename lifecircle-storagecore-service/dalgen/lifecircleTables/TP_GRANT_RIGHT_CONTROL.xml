<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_GRANT_RIGHT_CONTROL" physicalName="TP_GRANT_RIGHT_CONTROL"
       remark="TP_GRANT_RIGHT_CONTROL">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_GRANT_RIGHT_CONTROL">
        INSERT INTO TP_GRANT_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="autoIncome != null">`AUTO_INCOME`,</if>
            <if test="commSettle != null">`COMM_SETTLE`,</if>
            <if test="t1SettleSwitch != null">`T1_SETTLE_SWITCH`,</if>
            <if test="maxMarketNumber != null">`MAX_MARKET_NUMBER`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="createSubAccount != null">`CREATE_SUB_ACCOUNT`,</if>
            <if test="t1SettleBeginDay != null">`T1_SETTLE_BEGIN_DAY`,</if>
            <if test="taxUndertakeType != null">`TAX_UNDERTAKE_TYPE`,</if>
            <if test="alipayTouchSwitch != null">`ALIPAY_TOUCH_SWITCH`,</if>
            <if test="maxAccountsNumber != null">`MAX_ACCOUNTS_NUMBER`,</if>
            <if test="isConfigOwnChannel != null">`IS_CONFIG_OWN_CHANNEL`,</if>
            <if test="t1SettleShowSwitch != null">`T1_SETTLE_SHOW_SWITCH`,</if>
            <if test="oldTaxUndertakeType != null">`OLD_TAX_UNDERTAKE_TYPE`,</if>
            <if test="taxUndertakeStartTime != null">`TAX_UNDERTAKE_START_TIME`,</if>
            <if test="commissionApplyAmount != null">`COMMISSION_APPLY_AMOUNT`,</if>
            <if test="freeHandlingFeeApplyAmount != null">`FREE_HANDLING_FEE_APPLY_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="autoIncome != null">#{autoIncome,jdbcType=TINYINT},</if>
            <if test="commSettle != null">#{commSettle,jdbcType=TINYINT},</if>
            <if test="t1SettleSwitch != null">#{t1SettleSwitch,jdbcType=TINYINT},</if>
            <if test="maxMarketNumber != null">#{maxMarketNumber,jdbcType=INTEGER},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="createSubAccount != null">#{createSubAccount,jdbcType=TINYINT},</if>
            <if test="t1SettleBeginDay != null">#{t1SettleBeginDay,jdbcType=INTEGER},</if>
            <if test="taxUndertakeType != null">#{taxUndertakeType,jdbcType=TINYINT},</if>
            <if test="alipayTouchSwitch != null">#{alipayTouchSwitch,jdbcType=TINYINT},</if>
            <if test="maxAccountsNumber != null">#{maxAccountsNumber,jdbcType=INTEGER},</if>
            <if test="isConfigOwnChannel != null">#{isConfigOwnChannel,jdbcType=TINYINT},</if>
            <if test="t1SettleShowSwitch != null">#{t1SettleShowSwitch,jdbcType=TINYINT},</if>
            <if test="oldTaxUndertakeType != null">#{oldTaxUndertakeType,jdbcType=TINYINT},</if>
            <if test="taxUndertakeStartTime != null">#{taxUndertakeStartTime,jdbcType=INTEGER},</if>
            <if test="commissionApplyAmount != null">#{commissionApplyAmount,jdbcType=DECIMAL},</if>
            <if test="freeHandlingFeeApplyAmount != null">#{freeHandlingFeeApplyAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getByKeyId" multiplicity="one" remark="getByKeyId:TP_GRANT_RIGHT_CONTROL">
        SELECT
        *
        FROM TP_GRANT_RIGHT_CONTROL
        WHERE `KEY_ID` = #{keyId,jdbcType=INTEGER}
        limit 1
    </operation>

    <operation name="getByKeyIdList" multiplicity="many" remark="getByKeyIdList:TP_GRANT_RIGHT_CONTROL">
        SELECT
        *
        FROM TP_GRANT_RIGHT_CONTROL
        WHERE `KEY_ID` in
        <foreach collection="keyIdList" open="(" close=")" item="keyId" index="index" separator=",">
            #{keyId,jdbcType=INTEGER}
        </foreach>
    </operation>
</table>
