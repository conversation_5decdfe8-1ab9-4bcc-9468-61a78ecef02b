<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_MCHID" physicalName="TP_LIFECIRCLE_MCHID"
    remark="TP_LIFECIRCLE_MCHID">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_MCHID">
INSERT INTO TP_LIFECIRCLE_MCHID
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="errorMsg != null">`ERROR_MSG`,</if>
        <if test="subMchId != null">`SUB_MCH_ID`,</if>
        <if test="step != null">`STEP`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="isFinish != null">`IS_FINISH`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="agentAccountId != null">`AGENT_ACCOUNT_ID`,</if>
        <if test="wechantAuthStatus != null">`WECHANT_AUTH_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
        <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
        <if test="step != null">#{step,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="agentAccountId != null">#{agentAccountId,jdbcType=INTEGER},</if>
        <if test="wechantAuthStatus != null">#{wechantAuthStatus,jdbcType=INTEGER},</if>
    </trim>
    </operation>

    <operation name="getBySubMchId" multiplicity="one" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        user.uid
        FROM
        tp_lifecircle_mchid mchid
        LEFT JOIN tp_wxuser user ON mchid.token = user.token
        WHERE
        mchid.sub_mch_id = #{subMchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
