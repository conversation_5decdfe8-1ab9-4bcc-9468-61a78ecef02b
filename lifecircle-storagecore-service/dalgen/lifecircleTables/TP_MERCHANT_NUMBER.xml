<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_NUMBER" physicalName="TP_MERCHANT_NUMBER"
       remark="商编表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_NUMBER">
        INSERT INTO TP_MERCHANT_NUMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="alipayPid != null">`ALIPAY_PID`,</if>
            <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="channelNum != null">`CHANNEL_NUM`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="wechatSmid != null">`WECHAT_SMID`,</if>
            <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
            <if test="platformMerchantNo != null">`PLATFORM_MERCHANT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="isActivity != null">`IS_ACTIVITY`,</if>
            <if test="usedStatus != null">`USED_STATUS`,</if>
            <if test="usedStoreId != null">`USED_STORE_ID`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="merchantQualification != null">`MERCHANT_QUALIFICATION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="termNo != null">`TERM_NO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="alipayPid != null">#{alipayPid,jdbcType=VARCHAR},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="wechatSmid != null">#{wechatSmid,jdbcType=VARCHAR},</if>
            <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
            <if test="platformMerchantNo != null">#{platformMerchantNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="isActivity != null">#{isActivity,jdbcType=TINYINT},</if>
            <if test="usedStatus != null">#{usedStatus,jdbcType=TINYINT},</if>
            <if test="usedStoreId != null">#{usedStoreId,jdbcType=INTEGER},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
            <if test="merchantQualification != null">#{merchantQualification,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="termNo != null">#{termNo,jdbcType=VARCHAR},</if>
        </trim>
    </operation>
    <operation name="getByMerchantNoAndOrgId" multiplicity="one" remark="通过商编merchantNo,orgId获取用户信息">
        select *
        from TP_MERCHANT_NUMBER
        where merchant_no = #{merchantNo,jdbcType=VARCHAR}
        and PLATFORM_ORG_ID = #{orgId,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getOneByMerchantNo" multiplicity="one" paramtype="primitive"
               remark="获取商编信息,根据商编查询">
        SELECT * FROM
        TP_MERCHANT_NUMBER
        WHERE
        merchant_no = #{merchantNo,jdbcType=VARCHAR}
        AND used_status = 1
        ORDER BY id limit 1
    </operation>

    <operation name="getOneByStoreId" multiplicity="one" paramtype="primitive"
               remark="获取商编信息,根据门店ID查询">
        SELECT * FROM
        TP_MERCHANT_NUMBER
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        AND platform_org_id = #{platformOrgId,jdbcType=INTEGER}
        <if test="storeId != null">
            AND store_id = #{storeId,jdbcType=INTEGER}
        </if>
        AND merchant_no != ''
        AND used_status = 1
        ORDER BY id ASC limit 1
    </operation>

    <operation name="getByPlatformOrgIdAndUid" multiplicity="one" paramtype="primitive"
               remark="获取商编信息,如果是总分店则获取总店信息">
        SELECT * FROM
        TP_MERCHANT_NUMBER
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        <if test="platformOrgId != null">
            AND platform_org_id = #{platformOrgId,jdbcType=INTEGER}
        </if>
        AND merchant_no != ''
        AND used_status = 1
        AND merchant_type in (1,2,4)
        ORDER BY merchant_type ASC limit 1
    </operation>

    <operation name="getMerchantNoByUidList" multiplicity="many" paramtype="primitive"
               resulttype="com.fshows.lifecircle.storagecore.service.dal.lifecircle.resultmap.UidAndMerchantNoResultMap"
               remark="根据商户UID获取商编">
        select uid,merchant_no as merchantNo
        from tp_merchant_number
        where uid in
        <foreach collection="list" item="uid" open="(" separator="," close=")">
            #{uid,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="findByMerchantNoList" multiplicity="many" remark="根据商编查询">
        select * from tp_merchant_number
        where source=#{source,jdbcType=VARCHAR}
        AND merchant_no in
        <foreach collection="list" item="merchantNo" open="(" separator="," close=")">
            #{merchantNo,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
