<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_APP_GRAY_FILE" physicalName="TP_APP_GRAY_FILE"
       remark="app 灰度文件表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_APP_GRAY_FILE">
        INSERT INTO TP_APP_GRAY_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="fileUri != null">`FILE_URI`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="pubVersionId != null">`PUB_VERSION_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="fileUri != null">#{fileUri,jdbcType=VARCHAR},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="pubVersionId != null">#{pubVersionId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    <operation name="findByVersionId" paramtype="primitive" multiplicity="many" remark="根据 pub_version_id 获得灰度文件">
        SELECT
        *
        FROM
        tp_app_gray_file
        WHERE
        PUB_VERSION_ID = #{versionId, jdbcType=INTEGER}
        AND
        is_del = 0
    </operation>
    <operation name="batchFindByIdList" paramtype="primitive" multiplicity="many" remark="根据 ID 批量获得灰度文件">
        SELECT
        *
        FROM
        tp_app_gray_file
        WHERE
        PUB_VERSION_ID = #{versionId, jdbcType=INTEGER}
        AND
        is_del = #{isDel,jdbcType=INTEGER}
        <if test="list != null">
            AND
            id IN
            <foreach collection="list" item="id" open="(" close=")" separator=",">
                #{id,jdbcType=INTEGER}
            </foreach>
        </if>
    </operation>
</table>
