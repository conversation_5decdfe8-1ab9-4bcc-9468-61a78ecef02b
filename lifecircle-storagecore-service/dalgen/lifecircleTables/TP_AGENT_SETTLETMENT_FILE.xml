<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_AGENT_SETTLETMENT_FILE" physicalName="TP_AGENT_SETTLETMENT_FILE"
    remark="代理商订单结算回调文件表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_AGENT_SETTLETMENT_FILE">
INSERT INTO TP_AGENT_SETTLETMENT_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fileKey != null">`FILE_KEY`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="fileType != null">`FILE_TYPE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fileKey != null">#{fileKey,jdbcType=VARCHAR},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="fileType != null">#{fileType,jdbcType=TINYINT},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>
    <operation name="getSettlementDataByAgentId" multiplicity="one" remark="查询代理商某天的结算记录">
        SELECT
        *
        FROM
        TP_AGENT_SETTLETMENT_FILE
        WHERE
        agent_id = #{agentId, jdbcType=INTEGER}
        AND settle_date = #{settleDate,jdbcType=INTEGER}
        LIMIT 1
    </operation>
    </table>
