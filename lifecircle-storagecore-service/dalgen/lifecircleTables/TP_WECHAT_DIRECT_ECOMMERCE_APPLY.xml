<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_WECHAT_DIRECT_ECOMMERCE_APPLY" physicalName="TP_WECHAT_DIRECT_ECOMMERCE_APPLY" remark="微信收款商业版进件申请信息表">
    <!--    &lt;&gt;   <> -->

    <operation name="insert" paramtype="object" remark="insert:TP_WECHAT_DIRECT_ECOMMERCE_APPLY">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_WECHAT_DIRECT_ECOMMERCE_APPLY(
            ID
            ,BANK_NAME
            ,SUB_MCHID
            ,ACTIVITY_ID
            ,ID_CARD_NAME
            ,LICENSE_PIC
            ,SUBMIT_NAME
            ,ACCOUNT_BANK
            ,ACCOUNT_NAME
            ,APPLYMENT_ID
            ,BIZ_STORE_LAT
            ,BIZ_STORE_LNG
            ,CONTACT_NAME
            ,LEGAL_PERSON
            ,ACTIVITY_RATE
            ,BANK_AREA_CODE
            ,BANK_BRANCH_ID
            ,BANK_CITY_CODE
            ,BIZ_STORE_NAME
            ,BUSINESS_CODE
            ,CONTACT_EMAIL
            ,ID_CARD_NUMBER
            ,MERCHANT_NAME
            ,SERVICE_PHONE
            ,SETTLEMENT_ID
            ,ACCOUNT_NUMBER
            ,ID_CARD_BACK_PIC
            ,LICENSE_NUMBER
            ,ORG_LICENSE_PIC
            ,APPLYMENT_STATE
            ,BIZ_STORE_ADDRESS
            ,BIZ_STORE_CASH_PIC
            ,CONTACT_ID_NUMBER
            ,ID_CARD_PERIOD_END
            ,BANK_PROVINCE_CODE
            ,BIZ_STORE_AREA_CODE
            ,BIZ_STORE_CITY_CODE
            ,ID_CARD_FRONTAL_PIC
            ,ORG_LICENSE_NUMBER
            ,APPLYMENT_STATE_MSG
            ,BIZ_STORE_INDOOR_PIC
            ,ID_CARD_PERIOD_BEGIN
            ,MERCHANT_SHORTNAME
            ,CONTACT_MOBILE_PHONE
            ,BIZ_STORE_ENTRANCE_PIC
            ,CONTACT_WECHAT_OPENID
            ,ORG_LICENSE_PERIOD_END
            ,QUALIFICATION_TYPE_ID
            ,BIZ_STORE_PROVINCE_CODE
            ,ORG_LICENSE_PERIOD_BEGIN
            ,UID
            ,IS_DEL
            ,ADD_SOURCE
            ,SUBJECT_TYPE
            ,APPLYMENT_STATUS
            ,BANK_ACCOUNT_TYPE
            ,LICENSE_UNIT_FLAG
            ,SUBJECT_TYPE_VALUE
            ,ID_CARD_PERIOD_IS_LONG
            ,ORG_LICENSE_PERIOD_IS_LONG
            ,CREATE_TIME
            ,SUBMIT_TIME
            ,UPDATE_TIME
        )VALUES(
             #{id,jdbcType=INTEGER}
            , #{bankName,jdbcType=VARCHAR}
            , #{subMchid,jdbcType=VARCHAR}
            , #{activityId,jdbcType=VARCHAR}
            , #{idCardName,jdbcType=VARCHAR}
            , #{licensePic,jdbcType=VARCHAR}
            , #{submitName,jdbcType=VARCHAR}
            , #{accountBank,jdbcType=VARCHAR}
            , #{accountName,jdbcType=VARCHAR}
            , #{applymentId,jdbcType=VARCHAR}
            , #{bizStoreLat,jdbcType=VARCHAR}
            , #{bizStoreLng,jdbcType=VARCHAR}
            , #{contactName,jdbcType=VARCHAR}
            , #{legalPerson,jdbcType=VARCHAR}
            , #{activityRate,jdbcType=VARCHAR}
            , #{bankAreaCode,jdbcType=VARCHAR}
            , #{bankBranchId,jdbcType=VARCHAR}
            , #{bankCityCode,jdbcType=VARCHAR}
            , #{bizStoreName,jdbcType=VARCHAR}
            , #{businessCode,jdbcType=VARCHAR}
            , #{contactEmail,jdbcType=VARCHAR}
            , #{idCardNumber,jdbcType=VARCHAR}
            , #{merchantName,jdbcType=VARCHAR}
            , #{servicePhone,jdbcType=VARCHAR}
            , #{settlementId,jdbcType=VARCHAR}
            , #{accountNumber,jdbcType=VARCHAR}
            , #{idCardBackPic,jdbcType=VARCHAR}
            , #{licenseNumber,jdbcType=VARCHAR}
            , #{orgLicensePic,jdbcType=VARCHAR}
            , #{applymentState,jdbcType=VARCHAR}
            , #{bizStoreAddress,jdbcType=VARCHAR}
            , #{bizStoreCashPic,jdbcType=VARCHAR}
            , #{contactIdNumber,jdbcType=VARCHAR}
            , #{idCardPeriodEnd,jdbcType=VARCHAR}
            , #{bankProvinceCode,jdbcType=VARCHAR}
            , #{bizStoreAreaCode,jdbcType=VARCHAR}
            , #{bizStoreCityCode,jdbcType=VARCHAR}
            , #{idCardFrontalPic,jdbcType=VARCHAR}
            , #{orgLicenseNumber,jdbcType=VARCHAR}
            , #{applymentStateMsg,jdbcType=VARCHAR}
            , #{bizStoreIndoorPic,jdbcType=VARCHAR}
            , #{idCardPeriodBegin,jdbcType=VARCHAR}
            , #{merchantShortname,jdbcType=VARCHAR}
            , #{contactMobilePhone,jdbcType=VARCHAR}
            , #{bizStoreEntrancePic,jdbcType=VARCHAR}
            , #{contactWechatOpenid,jdbcType=VARCHAR}
            , #{orgLicensePeriodEnd,jdbcType=VARCHAR}
            , #{qualificationTypeId,jdbcType=VARCHAR}
            , #{bizStoreProvinceCode,jdbcType=VARCHAR}
            , #{orgLicensePeriodBegin,jdbcType=VARCHAR}
            , #{uid,jdbcType=INTEGER}
            , #{isDel,jdbcType=TINYINT}
            , #{addSource,jdbcType=TINYINT}
            , #{subjectType,jdbcType=TINYINT}
            , #{applymentStatus,jdbcType=TINYINT}
            , #{bankAccountType,jdbcType=TINYINT}
            , #{licenseUnitFlag,jdbcType=TINYINT}
            , #{subjectTypeValue,jdbcType=TINYINT}
            , #{idCardPeriodIsLong,jdbcType=TINYINT}
            , #{orgLicensePeriodIsLong,jdbcType=TINYINT}
            , #{createTime,jdbcType=TIMESTAMP}
            , #{submitTime,jdbcType=TIMESTAMP}
            , #{updateTime,jdbcType=TIMESTAMP}
        )
    </operation>

    <operation name="update" paramtype="object" remark="update table:TP_WECHAT_DIRECT_ECOMMERCE_APPLY">
        UPDATE TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        SET
            BANK_NAME       = #{bankName,jdbcType=VARCHAR}
            ,SUB_MCHID       = #{subMchid,jdbcType=VARCHAR}
            ,ACTIVITY_ID     = #{activityId,jdbcType=VARCHAR}
            ,ID_CARD_NAME    = #{idCardName,jdbcType=VARCHAR}
            ,LICENSE_PIC     = #{licensePic,jdbcType=VARCHAR}
            ,SUBMIT_NAME     = #{submitName,jdbcType=VARCHAR}
            ,ACCOUNT_BANK    = #{accountBank,jdbcType=VARCHAR}
            ,ACCOUNT_NAME    = #{accountName,jdbcType=VARCHAR}
            ,APPLYMENT_ID    = #{applymentId,jdbcType=VARCHAR}
            ,BIZ_STORE_LAT   = #{bizStoreLat,jdbcType=VARCHAR}
            ,BIZ_STORE_LNG   = #{bizStoreLng,jdbcType=VARCHAR}
            ,CONTACT_NAME    = #{contactName,jdbcType=VARCHAR}
            ,LEGAL_PERSON    = #{legalPerson,jdbcType=VARCHAR}
            ,ACTIVITY_RATE   = #{activityRate,jdbcType=VARCHAR}
            ,BANK_AREA_CODE  = #{bankAreaCode,jdbcType=VARCHAR}
            ,BANK_BRANCH_ID  = #{bankBranchId,jdbcType=VARCHAR}
            ,BANK_CITY_CODE  = #{bankCityCode,jdbcType=VARCHAR}
            ,BIZ_STORE_NAME  = #{bizStoreName,jdbcType=VARCHAR}
            ,BUSINESS_CODE   = #{businessCode,jdbcType=VARCHAR}
            ,CONTACT_EMAIL   = #{contactEmail,jdbcType=VARCHAR}
            ,ID_CARD_NUMBER  = #{idCardNumber,jdbcType=VARCHAR}
            ,MERCHANT_NAME   = #{merchantName,jdbcType=VARCHAR}
            ,SERVICE_PHONE   = #{servicePhone,jdbcType=VARCHAR}
            ,SETTLEMENT_ID   = #{settlementId,jdbcType=VARCHAR}
            ,ACCOUNT_NUMBER  = #{accountNumber,jdbcType=VARCHAR}
            ,ID_CARD_BACK_PIC = #{idCardBackPic,jdbcType=VARCHAR}
            ,LICENSE_NUMBER  = #{licenseNumber,jdbcType=VARCHAR}
            ,ORG_LICENSE_PIC = #{orgLicensePic,jdbcType=VARCHAR}
            ,APPLYMENT_STATE = #{applymentState,jdbcType=VARCHAR}
            ,BIZ_STORE_ADDRESS = #{bizStoreAddress,jdbcType=VARCHAR}
            ,BIZ_STORE_CASH_PIC = #{bizStoreCashPic,jdbcType=VARCHAR}
            ,CONTACT_ID_NUMBER = #{contactIdNumber,jdbcType=VARCHAR}
            ,ID_CARD_PERIOD_END = #{idCardPeriodEnd,jdbcType=VARCHAR}
            ,BANK_PROVINCE_CODE = #{bankProvinceCode,jdbcType=VARCHAR}
            ,BIZ_STORE_AREA_CODE = #{bizStoreAreaCode,jdbcType=VARCHAR}
            ,BIZ_STORE_CITY_CODE = #{bizStoreCityCode,jdbcType=VARCHAR}
            ,ID_CARD_FRONTAL_PIC = #{idCardFrontalPic,jdbcType=VARCHAR}
            ,ORG_LICENSE_NUMBER = #{orgLicenseNumber,jdbcType=VARCHAR}
            ,APPLYMENT_STATE_MSG = #{applymentStateMsg,jdbcType=VARCHAR}
            ,BIZ_STORE_INDOOR_PIC = #{bizStoreIndoorPic,jdbcType=VARCHAR}
            ,ID_CARD_PERIOD_BEGIN = #{idCardPeriodBegin,jdbcType=VARCHAR}
            ,MERCHANT_SHORTNAME = #{merchantShortname,jdbcType=VARCHAR}
            ,CONTACT_MOBILE_PHONE = #{contactMobilePhone,jdbcType=VARCHAR}
            ,BIZ_STORE_ENTRANCE_PIC = #{bizStoreEntrancePic,jdbcType=VARCHAR}
            ,CONTACT_WECHAT_OPENID = #{contactWechatOpenid,jdbcType=VARCHAR}
            ,ORG_LICENSE_PERIOD_END = #{orgLicensePeriodEnd,jdbcType=VARCHAR}
            ,QUALIFICATION_TYPE_ID = #{qualificationTypeId,jdbcType=VARCHAR}
            ,BIZ_STORE_PROVINCE_CODE = #{bizStoreProvinceCode,jdbcType=VARCHAR}
            ,ORG_LICENSE_PERIOD_BEGIN = #{orgLicensePeriodBegin,jdbcType=VARCHAR}
            ,UID             = #{uid,jdbcType=INTEGER}
            ,IS_DEL          = #{isDel,jdbcType=TINYINT}
            ,ADD_SOURCE      = #{addSource,jdbcType=TINYINT}
            ,SUBJECT_TYPE    = #{subjectType,jdbcType=TINYINT}
            ,APPLYMENT_STATUS = #{applymentStatus,jdbcType=TINYINT}
            ,BANK_ACCOUNT_TYPE = #{bankAccountType,jdbcType=TINYINT}
            ,LICENSE_UNIT_FLAG = #{licenseUnitFlag,jdbcType=TINYINT}
            ,SUBJECT_TYPE_VALUE = #{subjectTypeValue,jdbcType=TINYINT}
            ,ID_CARD_PERIOD_IS_LONG = #{idCardPeriodIsLong,jdbcType=TINYINT}
            ,ORG_LICENSE_PERIOD_IS_LONG = #{orgLicensePeriodIsLong,jdbcType=TINYINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,SUBMIT_TIME     = #{submitTime,jdbcType=TIMESTAMP}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
        WHERE
            ID              = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="deleteById" multiplicity="one" remark="delete:TP_WECHAT_DIRECT_ECOMMERCE_APPLY">
        DELETE FROM
            TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE
            ID = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="getById" multiplicity="one" remark="get:TP_WECHAT_DIRECT_ECOMMERCE_APPLY">
        SELECT *
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE
            ID = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="getBySubMchIdList" multiplicity="many" resulttype="java.lang.String" remark="根据子商户号查询">
        SELECT sub_mchid
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE sub_mchid IN
        <foreach close=")" collection="list" index="index" item="subMchId" open="(" separator=",">
            #{subMchId,jdbcType=VARCHAR}
        </foreach>
        AND is_del = 0
    </operation>

    <operation name="countBySubMchId" multiplicity="one" resulttype="java.lang.Integer" remark="根据子商户号统计">
        SELECT COUNT(*)
        FROM TP_WECHAT_DIRECT_ECOMMERCE_APPLY
        WHERE sub_mchid = #{subMchid,jdbcType=VARCHAR}
        AND is_del = 0
    </operation>

    <operation name="insertMerchantWeb" paramtype="object" remark="Web 添加商户">
        INSERT INTO tp_wechat_direct_ecommerce_apply
        (
         sub_mchid
        ,channel_id
        ,submit_name
        ,uid
        ,add_source
        ,apply_status
        ,audit_status
        ,create_time
        ,submit_time
        ,submit_step
        ,update_time
        )
        VALUES
        (
          #{subMchid,jdbcType=VARCHAR}
        , #{channelId,jdbcType=INTEGER}
        , #{submitName,jdbcType=VARCHAR}
        , #{uid,jdbcType=INTEGER}
        , #{addSource,jdbcType=TINYINT}
        , #{applyStatus,jdbcType=TINYINT}
        , #{auditStatus,jdbcType=TINYINT}
        , #{createTime,jdbcType=TIMESTAMP}
        , #{submitTime,jdbcType=TIMESTAMP}
        , #{submitStep,jdbcType=INTEGER}
        , #{updateTime,jdbcType=TIMESTAMP}
        )
    </operation>

    <operation name="insertMerchantWebBatch" paramtype="objectList" remark="Web 添加商户批量插入">
        INSERT INTO tp_wechat_direct_ecommerce_apply(
        sub_mchid
        ,channel_id
        ,submit_name
        ,uid
        ,add_source
        ,apply_status
        ,audit_status
        ,create_time
        ,submit_time
        ,submit_step
        ,update_time
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
              #{item.subMchid,jdbcType=VARCHAR}
            , #{item.channelId,jdbcType=INTEGER}
            , #{item.submitName,jdbcType=VARCHAR}
            , #{item.uid,jdbcType=INTEGER}
            , #{item.addSource,jdbcType=TINYINT}
            , #{item.applyStatus,jdbcType=TINYINT}
            , #{item.auditStatus,jdbcType=TINYINT}
            , #{item.createTime,jdbcType=TIMESTAMP}
            , #{item.submitTime,jdbcType=TIMESTAMP}
            , #{item.submitStep,jdbcType=INTEGER}
            , #{item.updateTime,jdbcType=TIMESTAMP}
            )
        </foreach>
    </operation>
</table>
