<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MYBANK_RIGHT_CONTROL" physicalName="TP_MYBANK_RIGHT_CONTROL"
       remark="网商权限控制表（代理商网商权限，主体商户与isvorgid一对一）">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MYBANK_RIGHT_CONTROL">
        INSERT INTO TP_MYBANK_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mybankIsvOrgId != null">`MYBANK_ISV_ORG_ID`,</if>
            <if test="mybankAccountNo != null">`MYBANK_ACCOUNT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="mybankAutoBatch != null">`MYBANK_AUTO_BATCH`,</if>
            <if test="mybankFundsUnfreeze != null">`MYBANK_FUNDS_UNFREEZE`,</if>
            <if test="mybankAutoReplenishment != null">`MYBANK_AUTO_REPLENISHMENT`,</if>
            <if test="mybankBalancePermission != null">`MYBANK_BALANCE_PERMISSION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="mybankIsvOrgId != null">#{mybankIsvOrgId,jdbcType=VARCHAR},</if>
            <if test="mybankAccountNo != null">#{mybankAccountNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="mybankAutoBatch != null">#{mybankAutoBatch,jdbcType=TINYINT},</if>
            <if test="mybankFundsUnfreeze != null">#{mybankFundsUnfreeze,jdbcType=TINYINT},</if>
            <if test="mybankAutoReplenishment != null">#{mybankAutoReplenishment,jdbcType=TINYINT},</if>
            <if test="mybankBalancePermission != null">#{mybankBalancePermission,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByAgentId" multiplicity="one" remark="根据代理商id查询代理商网商权限记录">
        SELECT * FROM `tp_mybank_right_control` where `user_type` = 2 and `user_id` = #{userId,jdbcType=INTEGER} limit 1
    </operation>
</table>
