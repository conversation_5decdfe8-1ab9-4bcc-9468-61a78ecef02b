<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MYBANK_BILL_FILE" physicalName="TP_MYBANK_BILL_FILE"
       remark="网商对账单文件记录">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MYBANK_BILL_FILE">
        INSERT INTO TP_MYBANK_BILL_FILE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="billType != null">`BILL_TYPE`,</if>
            <if test="filePath != null">`FILE_PATH`,</if>
            <if test="isvOrgId != null">`ISV_ORG_ID`,</if>
            <if test="ossBucket != null">`OSS_BUCKET`,</if>
            <if test="lastDownloadUrl != null">`LAST_DOWNLOAD_URL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="downloadCount != null">`DOWNLOAD_COUNT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="billType != null">#{billType,jdbcType=VARCHAR},</if>
            <if test="filePath != null">#{filePath,jdbcType=VARCHAR},</if>
            <if test="isvOrgId != null">#{isvOrgId,jdbcType=VARCHAR},</if>
            <if test="ossBucket != null">#{ossBucket,jdbcType=VARCHAR},</if>
            <if test="lastDownloadUrl != null">#{lastDownloadUrl,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="billDate != null">#{billDate,jdbcType=INTEGER},</if>
            <if test="downloadCount != null">#{downloadCount,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation remark="根据时间查询对账单记录" name="getLastBillByBillDate" >
        SELECT *
        FROM `tp_mybank_bill_file`
        where `agent_id` = #{agentId,jdbcType=INTEGER}
        and bill_type = #{billType,jdbcType=VARCHAR}
        and isv_org_id = #{isvOrgId,jdbcType=VARCHAR}
        and `bill_date` = #{billDate,jdbcType=INTEGER}
        order by id desc limit 1
    </operation>

    <operation remark="根据时间查询对账单记录" name="updateLastBillAndCount" >
        update `tp_mybank_bill_file` set
        LAST_DOWNLOAD_URL = #{lastDownloadUrl,jdbcType=VARCHAR},
        DOWNLOAD_COUNT = DOWNLOAD_COUNT + 1
        where id = #{id,jdbcType=INTEGER}
    </operation>

</table>
