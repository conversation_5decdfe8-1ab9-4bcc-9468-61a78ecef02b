<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_REFUND_TASK_DETAIL" physicalName="TP_REFUND_TASK_DETAIL"
       remark="批量退款任务明细表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_REFUND_TASK_DETAIL">
        INSERT INTO TP_REFUND_TASK_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="refundSn != null">`REFUND_SN`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="refundAmount != null">`REFUND_AMOUNT`,</if>
            <if test="refundTaskId != null">`REFUND_TASK_ID`,</if>
            <if test="preCheckStatus != null">`PRE_CHECK_STATUS`,</if>
            <if test="refundTaskDetailId != null">`REFUND_TASK_DETAIL_ID`,</if>
            <if test="taskDate != null">`TASK_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=VARCHAR},</if>
            <if test="refundAmount != null">#{refundAmount,jdbcType=VARCHAR},</if>
            <if test="refundTaskId != null">#{refundTaskId,jdbcType=VARCHAR},</if>
            <if test="preCheckStatus != null">#{preCheckStatus,jdbcType=VARCHAR},</if>
            <if test="refundTaskDetailId != null">#{refundTaskDetailId,jdbcType=VARCHAR},</if>
            <if test="taskDate != null">#{taskDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findByTaskId" multiplicity="many" remark="批量退款明细查询" paramtype="primitive">
        select * from TP_REFUND_TASK_DETAIL
        where `REFUND_TASK_ID` = #{refundTaskId,jdbcType=VARCHAR}
        <if test="preCheckStatusList != null">and `PRE_CHECK_STATUS` in
            <foreach collection="preCheckStatusList" separator="," item="item" javatype="java.lang.String" open="(" close=")">
                #{item}
            </foreach>
        </if>
        <if test="applyStatusList != null">and `APPLY_STATUS` in
            <foreach collection="applyStatusList" separator="," item="item1" javatype="java.lang.String" open="(" close=")">
                #{item1}
            </foreach>
        </if>
    </operation>
</table>
