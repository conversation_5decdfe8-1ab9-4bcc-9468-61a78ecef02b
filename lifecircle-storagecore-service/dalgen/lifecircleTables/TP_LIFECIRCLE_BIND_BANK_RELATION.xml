<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_BIND_BANK_RELATION" physicalName="TP_LIFECIRCLE_BIND_BANK_RELATION"
       remark="绑卡信息关联外部卡令牌关系表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_BIND_BANK_RELATION">
        INSERT INTO TP_LIFECIRCLE_BIND_BANK_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="bindBankId != null">`BIND_BANK_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getBankRelationByTokenNo" multiplicity="many" remark="根据tokenNo批量查出bindBankRelation记录">
        select
        *
        from
        tp_lifecircle_bind_bank_relation
        where
        token_no in
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
