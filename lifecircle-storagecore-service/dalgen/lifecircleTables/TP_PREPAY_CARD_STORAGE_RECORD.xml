<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_STORAGE_RECORD" physicalName="TP_PREPAY_CARD_STORAGE_RECORD"
       remark="预付卡出入库记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_STORAGE_RECORD">
        INSERT INTO TP_PREPAY_CARD_STORAGE_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="relationOrder != null">`RELATION_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateType != null">`OPERATE_TYPE`,</if>
            <if test="recordNumber != null">`RECORD_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="relationOrder != null">#{relationOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateType != null">#{operateType,jdbcType=TINYINT},</if>
            <if test="recordNumber != null">#{recordNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
