<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ALIPAY_DIRECT_ANT_STORE_APPLY" physicalName="TP_ALIPAY_DIRECT_ANT_STORE_APPLY"
       remark="TP_ALIPAY_DIRECT_ANT_STORE_APPLY">

    <operation name="getOneByUid" paramtype="primitive" multiplicity="one" remark="根据商户ID获取蚂蚁门店进件申请单">
        SELECT * FROM TP_ALIPAY_DIRECT_ANT_STORE_APPLY WHERE uid = #{uid,jdbcType=INTEGER} AND is_del = 0 ORDER BY ID
        DESC LIMIT 1
    </operation>

    <operation name="getOneByIdAndUidAndApplyNumber" paramtype="primitive" multiplicity="one"
               remark="根据申请单ID和Uid以及申请单ID获取蚂蚁门店进件申请单">
        SELECT * FROM TP_ALIPAY_DIRECT_ANT_STORE_APPLY WHERE id = #{id,jdbcType=INTEGER} AND uid =
        #{uid,jdbcType=INTEGER} AND apply_number = #{applyNumber,jdbcType=VARCHAR} AND is_del = 0 LIMIT 1
    </operation>


</table>
