<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_BANK_ACCOUNT" physicalName="TP_BANK_ACCOUNT"
       remark="银行账户表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_BANK_ACCOUNT">
        INSERT INTO TP_BANK_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="accountId != null">`ACCOUNT_ID`,</if>
            <if test="bankCardNo != null">`BANK_CARD_NO`,</if>
            <if test="bankOrderSn != null">`BANK_ORDER_SN`,</if>
            <if test="bankCertName != null">`BANK_CERT_NAME`,</if>
            <if test="accountCat != null">`ACCOUNT_CAT`,</if>
            <if test="bankMerchantId != null">`BANK_MERCHANT_ID`,</if>
            <if test="bankAccountInfo != null">`BANK_ACCOUNT_INFO`,</if>
            <if test="freezeExternalNo != null">`FREEZE_EXTERNAL_NO`,</if>
            <if test="custId != null">`CUST_ID`,</if>
            <if test="syncLiquidationReason != null">`SYNC_LIQUIDATION_REASON`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="syncLiquidationStatus != null">`SYNC_LIQUIDATION_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
            <if test="bankCardNo != null">#{bankCardNo,jdbcType=VARCHAR},</if>
            <if test="bankOrderSn != null">#{bankOrderSn,jdbcType=VARCHAR},</if>
            <if test="bankCertName != null">#{bankCertName,jdbcType=VARCHAR},</if>
            <if test="accountCat != null">#{accountCat,jdbcType=TINYINT},</if>
            <if test="bankMerchantId != null">#{bankMerchantId,jdbcType=VARCHAR},</if>
            <if test="bankAccountInfo != null">#{bankAccountInfo,jdbcType=LONGVARCHAR},</if>
            <if test="freezeExternalNo != null">#{freezeExternalNo,jdbcType=VARCHAR},</if>
            <if test="custId != null">#{custId,jdbcType=VARCHAR},</if>
            <if test="syncLiquidationReason != null">#{syncLiquidationReason,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="syncLiquidationStatus != null">#{syncLiquidationStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByUidAndBankType" multiplicity="one" remark="根据uid和bankType查询商户银行账户信息">
        SELECT * FROM
        tp_bank_account
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        AND bank_type = #{bankType,jdbcType=TINYINT}
        LIMIT 1
    </operation>

    <operation name="getMerchantByToken" multiplicity="one" remark="重新入驻前根据token获取status">
        SELECT *
        FROM
        tp_bank_account
        WHERE
        TOKEN = #{token,jdbcType=VARCHAR}
        AND BANK_TYPE =#{bankType,jdbcType=TINYINT}
        AND CHANNEL_TYPE=#{channelType,jdbcType=TINYINT}
        AND account_cat = 2
        LIMIT 1
    </operation>

    <operation name="getBankAccountInfoByUidList" multiplicity="many" remark="根据uid查询bankAccount记录">
        select
        *
        from
        tp_bank_account
        where
        status = 4
        and
        account_cat = 2
        and
        sync_liquidation_status = 4
        and
        bank_type = 3
        and
        uid in
        <foreach close=")" collection="list" index="index" item="uid" open="(" separator=",">
            #{uid,jdbcType=INTEGER}
        </foreach>

    </operation>

    <operation name="getByUidAndBankTypeAndStatus" multiplicity="one" remark="根据uid、银行类型、状态获得查询商户银行账户信息">
        select
        *
        from
        TP_BANK_ACCOUNT
        where
        uid=#{uid,jdbcType=INTEGER}
        AND
        bank_type = #{bankType,jdbcType=TINYINT}
        AND
        status = #{status,jdbcType=TINYINT}
        LIMIT 1
    </operation>

</table>
