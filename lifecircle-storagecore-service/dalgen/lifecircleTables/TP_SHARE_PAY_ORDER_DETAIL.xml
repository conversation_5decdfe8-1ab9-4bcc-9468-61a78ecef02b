<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_PAY_ORDER_DETAIL" physicalName="TP_SHARE_PAY_ORDER_DETAIL"
       remark="分账订单明细">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_PAY_ORDER_DETAIL">
        INSERT INTO TP_SHARE_PAY_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="taskId != null">`TASK_ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="shareDate != null">`SHARE_DATE`,</if>
            <if test="shareRole != null">`SHARE_ROLE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="merchantNoIn != null">`MERCHANT_NO_IN`,</if>
            <if test="merchantNoOut != null">`MERCHANT_NO_OUT`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="shareOrderStatus != null">`SHARE_ORDER_STATUS`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="platformShareTime != null">`PLATFORM_SHARE_TIME`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="sharePortion != null">`SHARE_PORTION`,</if>
            <if test="realSharePrice != null">`real_share_price`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="taskId != null">#{taskId,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="shareDate != null">#{shareDate,jdbcType=VARCHAR},</if>
            <if test="shareRole != null">#{shareRole,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="merchantNoIn != null">#{merchantNoIn,jdbcType=VARCHAR},</if>
            <if test="merchantNoOut != null">#{merchantNoOut,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="shareOrderStatus != null">#{shareOrderStatus,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="platformShareTime != null">#{platformShareTime,jdbcType=TIMESTAMP},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="sharePortion != null">#{sharePortion,jdbcType=DECIMAL},</if>
            <if test="realSharePrice != null">#{realSharePrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
    <operation name="updateRealSharePriceByPlatformReqNoAndMerchantNoInAndBankType"
               remark="通过PlatformReqNo和MerchantNoIn,bankType更新real_share_price">
        update TP_SHARE_PAY_ORDER_DETAIL
        set real_share_price = #{realSharePrice,jdbcType=DECIMAL}
        where
        PLATFORM_REQ_NO = #{platformReqNo,jdbcType=VARCHAR}
        and
        MERCHANT_NO_in = #{merchantNoIn,jdbcType=VARCHAR}
        and
        bank_type = #{bankType,jdbcType=TINYINT}
    </operation>
    <operation name="updateRealSharePriceByShareReqNoAndMerchantNoIn"
               remark="通过share_req_no和MerchantNoIn更新real_share_price">
        update TP_SHARE_PAY_ORDER_DETAIL
        set real_share_price = SHARE_PRICE - #{fee,jdbcType=DECIMAL}
        where
        SHARE_REQ_NO = #{shareReqNo,jdbcType=VARCHAR}
        and
        MERCHANT_NO_in = #{merchantNoIn,jdbcType=VARCHAR}
    </operation>
</table>
