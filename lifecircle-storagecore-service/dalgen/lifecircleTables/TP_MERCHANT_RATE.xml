<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_RATE" physicalName="TP_MERCHANT_RATE"
       remark="商户费率表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_RATE">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_MERCHANT_RATE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="syncState != null">`SYNC_STATE`,</if>
            <if test="existMaxFee != null">`EXIST_MAX_FEE`,</if>
            <if test="isEffective != null">`IS_EFFECTIVE`,</if>
            <if test="paymentChannel != null">`PAYMENT_CHANNEL`,</if>
            <if test="productCode != null">`PRODUCT_CODE`,</if>
            <if test="existSalesmanCost != null">`EXIST_SALESMAN_COST`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbRate != null">`FB_RATE`,</if>
            <if test="oemRate != null">`OEM_RATE`,</if>
            <if test="maxFbFee != null">`MAX_FB_FEE`,</if>
            <if test="agentRate != null">`AGENT_RATE`,</if>
            <if test="maxOemFee != null">`MAX_OEM_FEE`,</if>
            <if test="channelRate != null">`CHANNEL_RATE`,</if>
            <if test="maxAgentFee != null">`MAX_AGENT_FEE`,</if>
            <if test="paymentRate != null">`PAYMENT_RATE`,</if>
            <if test="withdrawalFee != null">`WITHDRAWAL_FEE`,</if>
            <if test="salesmanRate != null">`SALESMAN_RATE`,</if>
            <if test="maxChannelFee != null">`MAX_CHANNEL_FEE`,</if>
            <if test="maxPaymentFee != null">`MAX_PAYMENT_FEE`,</if>
            <if test="maxSalesmanFee != null">`MAX_SALESMAN_FEE`,</if>
            <if test="fbWithdrawalFee != null">`FB_WITHDRAWAL_FEE`,</if>
            <if test="agentWithdrawalFee != null">`AGENT_WITHDRAWAL_FEE`,</if>
            <if test="channelWithdrawalFee != null">`CHANNEL_WITHDRAWAL_FEE`,</if>
            <if test="logId != null">`LOG_ID`,</if>
            <if test="oemWithdrawalFee != null">`oem_withdrawal_fee`,</if>
            <if test="salesmanWithdrawalFee != null">`salesman_withdrawal_fee`,</if>
            <if test="existWithdrawalFee != null">`exist_withdrawal_fee`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="syncState != null">#{syncState,jdbcType=VARCHAR},</if>
            <if test="existMaxFee != null">#{existMaxFee,jdbcType=VARCHAR},</if>
            <if test="isEffective != null">#{isEffective,jdbcType=VARCHAR},</if>
            <if test="paymentChannel != null">#{paymentChannel,jdbcType=VARCHAR},</if>
            <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
            <if test="existSalesmanCost != null">#{existSalesmanCost,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbRate != null">#{fbRate,jdbcType=DECIMAL},</if>
            <if test="oemRate != null">#{oemRate,jdbcType=DECIMAL},</if>
            <if test="maxFbFee != null">#{maxFbFee,jdbcType=DECIMAL},</if>
            <if test="agentRate != null">#{agentRate,jdbcType=DECIMAL},</if>
            <if test="maxOemFee != null">#{maxOemFee,jdbcType=DECIMAL},</if>
            <if test="channelRate != null">#{channelRate,jdbcType=DECIMAL},</if>
            <if test="maxAgentFee != null">#{maxAgentFee,jdbcType=DECIMAL},</if>
            <if test="paymentRate != null">#{paymentRate,jdbcType=DECIMAL},</if>
            <if test="withdrawalFee != null">#{withdrawalFee,jdbcType=DECIMAL},</if>
            <if test="salesmanRate != null">#{salesmanRate,jdbcType=DECIMAL},</if>
            <if test="maxChannelFee != null">#{maxChannelFee,jdbcType=DECIMAL},</if>
            <if test="maxPaymentFee != null">#{maxPaymentFee,jdbcType=DECIMAL},</if>
            <if test="maxSalesmanFee != null">#{maxSalesmanFee,jdbcType=DECIMAL},</if>
            <if test="fbWithdrawalFee != null">#{fbWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="agentWithdrawalFee != null">#{agentWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="channelWithdrawalFee != null">#{channelWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="logId != null">#{logId,jdbcType=VARCHAR},</if>
            <if test="oemWithdrawalFee != null">#{oemWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="salesmanWithdrawalFee != null">#{salesmanWithdrawalFee,jdbcType=DECIMAL},</if>
            <if test="existWithdrawalFee != null">#{existWithdrawalFee,jdbcType=VARCHAR},</if>
        </trim>
    </operation>


</table>
