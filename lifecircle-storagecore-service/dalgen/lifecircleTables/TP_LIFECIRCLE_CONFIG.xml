<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_CONFIG" physicalName="TP_LIFECIRCLE_CONFIG"
    remark="子商户号记录表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_CONFIG">
INSERT INTO TP_LIFECIRCLE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="appid != null">`APPID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="sslPath != null">`SSL_PATH`,</if>
        <if test="appsecret != null">`APPSECRET`,</if>
        <if test="partnerkey != null">`PARTNERKEY`,</if>
        <if test="merchantName != null">`MERCHANT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="level != null">`LEVEL`,</if>
        <if test="isLink != null">`IS_LINK`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="merchant != null">`MERCHANT`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="globNumber != null">`GLOB_NUMBER`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="isAuthorize != null">`IS_AUTHORIZE`,</if>
        <if test="liquiConfigStatus != null">`LIQUI_CONFIG_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="sslPath != null">#{sslPath,jdbcType=VARCHAR},</if>
        <if test="appsecret != null">#{appsecret,jdbcType=VARCHAR},</if>
        <if test="partnerkey != null">#{partnerkey,jdbcType=VARCHAR},</if>
        <if test="merchantName != null">#{merchantName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="level != null">#{level,jdbcType=TINYINT},</if>
        <if test="isLink != null">#{isLink,jdbcType=TINYINT},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="merchant != null">#{merchant,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="globNumber != null">#{globNumber,jdbcType=TINYINT},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="isAuthorize != null">#{isAuthorize,jdbcType=TINYINT},</if>
        <if test="liquiConfigStatus != null">#{liquiConfigStatus,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getBySubMchId" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        uid
        FROM
        tp_lifecircle_config
        WHERE
        mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
