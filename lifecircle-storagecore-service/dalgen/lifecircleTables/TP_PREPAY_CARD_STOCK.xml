<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_STOCK" physicalName="TP_PREPAY_CARD_STOCK"
       remark="预付卡库存表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_STOCK">
        INSERT INTO TP_PREPAY_CARD_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="publishOrgId != null">`PUBLISH_ORG_ID`,</if>
            <if test="lockStock != null">`LOCK_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="publishOrgId != null">#{publishOrgId,jdbcType=VARCHAR},</if>
            <if test="lockStock != null">#{lockStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="updateStockAddByStockAndCardSku" remark="根据stock和skuId增加库存信息">
        update
        tp_prepay_card_stock
        <set>
            <if test="stock != null">
                stock = stock + #{stock,jdbcType=INTEGER},
            </if>
            <if test="lockStock != null">
                lock_stock = lock_stock + #{lockStock,jdbcType=INTEGER},
            </if>
        </set>
        WHERE
        is_del =0
        AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
    </operation>

    <operation name="updateStockSubtractByStockAndCardSku" remark="根据stock和skuId扣减库存信息">
        update
        tp_prepay_card_stock
        <set>
            <if test="stock != null">
                stock = stock - #{stock,jdbcType=INTEGER},
            </if>
            <if test="lockStock != null">
                lock_stock = lock_stock - #{lockStock,jdbcType=INTEGER},
            </if>
        </set>
        WHERE
        is_del =0
        AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
    </operation>
</table>
