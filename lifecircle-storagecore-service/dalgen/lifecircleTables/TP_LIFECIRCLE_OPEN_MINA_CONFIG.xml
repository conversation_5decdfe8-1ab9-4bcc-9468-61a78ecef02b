<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_OPEN_MINA_CONFIG" physicalName="TP_LIFECIRCLE_OPEN_MINA_CONFIG"
    remark="TP_LIFECIRCLE_OPEN_MINA_CONFIG">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_OPEN_MINA_CONFIG">
INSERT INTO TP_LIFECIRCLE_OPEN_MINA_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="minaAppId != null">`MINA_APP_ID`,</if>
        <if test="mchidAppid != null">`MCHID_APPID`,</if>
        <if test="minaAppSecret != null">`MINA_APP_SECRET`,</if>
        <if test="principalName != null">`PRINCIPAL_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="relationStatus != null">`RELATION_STATUS`,</if>
        <if test="liquidationConfigStatus != null">`LIQUIDATION_CONFIG_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="minaAppId != null">#{minaAppId,jdbcType=VARCHAR},</if>
        <if test="mchidAppid != null">#{mchidAppid,jdbcType=VARCHAR},</if>
        <if test="minaAppSecret != null">#{minaAppSecret,jdbcType=VARCHAR},</if>
        <if test="principalName != null">#{principalName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="relationStatus != null">#{relationStatus,jdbcType=TINYINT},</if>
        <if test="liquidationConfigStatus != null">#{liquidationConfigStatus,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getBySubMchId" multiplicity="one" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        uid
        FROM
        tp_lifecircle_open_mina_config
        WHERE
        mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
