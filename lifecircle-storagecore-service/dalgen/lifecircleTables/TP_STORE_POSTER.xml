<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_STORE_POSTER" physicalName="TP_STORE_POSTER"
       remark="门店海报表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_STORE_POSTER">
        INSERT INTO TP_STORE_POSTER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="memberQrcodeUrl != null">`MEMBER_QRCODE_URL`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="memberQrcodeUrl != null">#{memberQrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByMerchantId" multiplicity="many" remark="根据 商户Id 获得海报列表">
        SELECT
        *
        FROM
        TP_STORE_POSTER
        WHERE
        merchant_id = #{merchantId,jdbcType=INTEGER}
    </operation>
</table>
