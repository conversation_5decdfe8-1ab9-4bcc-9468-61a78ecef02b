<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_ALIPAY_MERCHANT_INFO" physicalName="TP_ALIPAY_MERCHANT_INFO"
    remark="TP_ALIPAY_MERCHANT_INFO">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_ALIPAY_MERCHANT_INFO">
INSERT INTO TP_ALIPAY_MERCHANT_INFO
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="memo != null">`MEMO`,</if>
        <if test="msid != null">`MSID`,</if>
        <if test="name != null">`NAME`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="aliasName != null">`ALIAS_NAME`,</if>
        <if test="shortName != null">`SHORT_NAME`,</if>
        <if test="categoryId != null">`CATEGORY_ID`,</if>
        <if test="externalId != null">`EXTERNAL_ID`,</if>
        <if test="contactName != null">`CONTACT_NAME`,</if>
        <if test="contactEmail != null">`CONTACT_EMAIL`,</if>
        <if test="contactPhone != null">`CONTACT_PHONE`,</if>
        <if test="servicePhone != null">`SERVICE_PHONE`,</if>
        <if test="contactMobile != null">`CONTACT_MOBILE`,</if>
        <if test="subMerchantId != null">`SUB_MERCHANT_ID`,</if>
        <if test="liquidationMerchantId != null">`LIQUIDATION_MERCHANT_ID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="indirectLevel != null">`INDIRECT_LEVEL`,</if>
        <if test="upgradeStatus != null">`UPGRADE_STATUS`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="alipayAuthStatus != null">`ALIPAY_AUTH_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="memo != null">#{memo,jdbcType=VARCHAR},</if>
        <if test="msid != null">#{msid,jdbcType=VARCHAR},</if>
        <if test="name != null">#{name,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="aliasName != null">#{aliasName,jdbcType=VARCHAR},</if>
        <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
        <if test="categoryId != null">#{categoryId,jdbcType=VARCHAR},</if>
        <if test="externalId != null">#{externalId,jdbcType=VARCHAR},</if>
        <if test="contactName != null">#{contactName,jdbcType=VARCHAR},</if>
        <if test="contactEmail != null">#{contactEmail,jdbcType=VARCHAR},</if>
        <if test="contactPhone != null">#{contactPhone,jdbcType=VARCHAR},</if>
        <if test="servicePhone != null">#{servicePhone,jdbcType=VARCHAR},</if>
        <if test="contactMobile != null">#{contactMobile,jdbcType=VARCHAR},</if>
        <if test="subMerchantId != null">#{subMerchantId,jdbcType=VARCHAR},</if>
        <if test="liquidationMerchantId != null">#{liquidationMerchantId,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="indirectLevel != null">#{indirectLevel,jdbcType=TINYINT},</if>
        <if test="upgradeStatus != null">#{upgradeStatus,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="alipayAuthStatus != null">#{alipayAuthStatus,jdbcType=TINYINT},</if>
    </trim>
    </operation>
    <operation name="getByLiquidationMerchantId" multiplicity="one" remark="根据清算平台商户号查询商户信息">
        SELECT
        *
        FROM
        TP_ALIPAY_MERCHANT_INFO
        WHERE
        liquidation_merchant_id = #{liquidationMerchantId, jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getUidByUnionMerchantNo" resulttype="java.lang.Integer" multiplicity="one" remark="根据随行付商户号查询商户号">
        SELECT uid
        FROM TP_ALIPAY_MERCHANT_INFO
        WHERE
        union_merchant_no
        = #{unionMerchantNo,jdbcType=VARCHAR}
        limit 1
    </operation>
    </table>
