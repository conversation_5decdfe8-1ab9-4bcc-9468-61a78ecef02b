<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USER_RIGHT_CONTROL" physicalName="TP_USER_RIGHT_CONTROL"
       remark="TP_USER_RIGHT_CONTROL">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USER_RIGHT_CONTROL">
        INSERT INTO TP_USER_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="isAlipayPrePay != null">`IS_ALIPAY_PRE_PAY`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="isSign != null">`IS_SIGN`,</if>
            <if test="sendMe != null">`SEND_ME`,</if>
            <if test="isQqpay != null">`IS_QQPAY`,</if>
            <if test="isYipay != null">`IS_YIPAY`,</if>
            <if test="viewNum != null">`VIEW_NUM`,</if>
            <if test="zeroFee != null">`ZERO_FEE`,</if>
            <if test="isPrePay != null">`IS_PRE_PAY`,</if>
            <if test="isEncrypt != null">`IS_ENCRYPT`,</if>
            <if test="isH5payWx != null">`IS_H5PAY_WX`,</if>
            <if test="isOpenapi != null">`IS_OPENAPI`,</if>
            <if test="mchidMode != null">`MCHID_MODE`,</if>
            <if test="zfEndTime != null">`ZF_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isUnionpay != null">`IS_UNIONPAY`,</if>
            <if test="isWithdraw != null">`IS_WITHDRAW`,</if>
            <if test="modifyTime != null">`MODIFY_TIME`,</if>
            <if test="freeYoudian != null">`FREE_YOUDIAN`,</if>
            <if test="zfStartTime != null">`ZF_START_TIME`,</if>
            <if test="isAlipayAuth != null">`IS_ALIPAY_AUTH`,</if>
            <if test="isVerification != null">`IS_VERIFICATION`,</if>
            <if test="syncAlipayCard != null">`SYNC_ALIPAY_CARD`,</if>
            <if test="isOnlyAttention != null">`IS_ONLY_ATTENTION`,</if>
            <if test="lastHelpVideoId != null">`LAST_HELP_VIDEO_ID`,</if>
            <if test="marketingSwitch != null">`MARKETING_SWITCH`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="closeZeroFeeTime != null">`CLOSE_ZERO_FEE_TIME`,</if>
            <if test="isCustomizedMina != null">`IS_CUSTOMIZED_MINA`,</if>
            <if test="specialRatePower != null">`SPECIAL_RATE_POWER`,</if>
            <if test="customerAddFriend != null">`CUSTOMER_ADD_FRIEND`,</if>
            <if test="isNewMemberSystem != null">`IS_NEW_MEMBER_SYSTEM`,</if>
            <if test="marketingActivity != null">`MARKETING_ACTIVITY`,</if>
            <if test="qrorderingBroadcast != null">`QRORDERING_BROADCAST`,</if>
            <if test="superMerchantAccess != null">`SUPER_MERCHANT_ACCESS`,</if>
            <if test="qrorderingBroadcastCycle != null">`QRORDERING_BROADCAST_CYCLE`,</if>
            <if test="qrorderingMissingBroadcast != null">`QRORDERING_MISSING_BROADCAST`,</if>
            <if test="isDouyinWriteoff != null">`IS_DOUYIN_WRITEOFF`,</if>
            <if test="meituanGroupSwitch != null">`MEITUAN_GROUP_SWITCH`,</if>
            <if test="alipayVoucherSwitch != null">`alipay_voucher_switch`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="isAlipayPrePay != null">#{isAlipayPrePay,jdbcType=VARCHAR},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="isSign != null">#{isSign,jdbcType=TINYINT},</if>
            <if test="sendMe != null">#{sendMe,jdbcType=TINYINT},</if>
            <if test="isQqpay != null">#{isQqpay,jdbcType=TINYINT},</if>
            <if test="isYipay != null">#{isYipay,jdbcType=TINYINT},</if>
            <if test="viewNum != null">#{viewNum,jdbcType=TINYINT},</if>
            <if test="zeroFee != null">#{zeroFee,jdbcType=TINYINT},</if>
            <if test="isPrePay != null">#{isPrePay,jdbcType=TINYINT},</if>
            <if test="isEncrypt != null">#{isEncrypt,jdbcType=TINYINT},</if>
            <if test="isH5payWx != null">#{isH5payWx,jdbcType=TINYINT},</if>
            <if test="isOpenapi != null">#{isOpenapi,jdbcType=TINYINT},</if>
            <if test="mchidMode != null">#{mchidMode,jdbcType=TINYINT},</if>
            <if test="zfEndTime != null">#{zfEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isUnionpay != null">#{isUnionpay,jdbcType=TINYINT},</if>
            <if test="isWithdraw != null">#{isWithdraw,jdbcType=TINYINT},</if>
            <if test="modifyTime != null">#{modifyTime,jdbcType=INTEGER},</if>
            <if test="freeYoudian != null">#{freeYoudian,jdbcType=TINYINT},</if>
            <if test="zfStartTime != null">#{zfStartTime,jdbcType=INTEGER},</if>
            <if test="isAlipayAuth != null">#{isAlipayAuth,jdbcType=TINYINT},</if>
            <if test="isVerification != null">#{isVerification,jdbcType=TINYINT},</if>
            <if test="syncAlipayCard != null">#{syncAlipayCard,jdbcType=TINYINT},</if>
            <if test="isOnlyAttention != null">#{isOnlyAttention,jdbcType=TINYINT},</if>
            <if test="lastHelpVideoId != null">#{lastHelpVideoId,jdbcType=INTEGER},</if>
            <if test="marketingSwitch != null">#{marketingSwitch,jdbcType=TINYINT},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="closeZeroFeeTime != null">#{closeZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="isCustomizedMina != null">#{isCustomizedMina,jdbcType=TINYINT},</if>
            <if test="specialRatePower != null">#{specialRatePower,jdbcType=TINYINT},</if>
            <if test="customerAddFriend != null">#{customerAddFriend,jdbcType=TINYINT},</if>
            <if test="isNewMemberSystem != null">#{isNewMemberSystem,jdbcType=TINYINT},</if>
            <if test="marketingActivity != null">#{marketingActivity,jdbcType=TINYINT},</if>
            <if test="qrorderingBroadcast != null">#{qrorderingBroadcast,jdbcType=TINYINT},</if>
            <if test="superMerchantAccess != null">#{superMerchantAccess,jdbcType=TINYINT},</if>
            <if test="qrorderingBroadcastCycle != null">#{qrorderingBroadcastCycle,jdbcType=TINYINT},</if>
            <if test="qrorderingMissingBroadcast != null">#{qrorderingMissingBroadcast,jdbcType=TINYINT},</if>
            <if test="isDouyinWriteoff != null">#{isDouyinWriteoff,jdbcType=TINYINT},</if>
            <if test="meituanGroupSwitch != null">#{meituanGroupSwitch,jdbcType=TINYINT},</if>
            <if test="alipayVoucherSwitch != null">#{alipayVoucherSwitch,jdbcType=TINYINT},</if>
        </trim>
    </operation>

    <operation name="getUserRightControlByKeyIds" multiplicity="many" remark="根据用户IDS[keyId]查找用户权限信息列表">
        SELECT * FROM TP_USER_RIGHT_CONTROL
        WHERE key_id IN
        <foreach collection="list" item="uid" index="index" separator="," open="(" close=")">
            #{uid, jdbcType=INTEGER}
        </foreach>
    </operation>

</table>
