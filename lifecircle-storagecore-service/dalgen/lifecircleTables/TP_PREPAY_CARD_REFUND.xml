<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_PREPAY_CARD_REFUND" physicalName="TP_PREPAY_CARD_REFUND"
    remark="退货单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_REFUND">
INSERT INTO TP_PREPAY_CARD_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="orgId != null">`ORG_ID`,</if>
        <if test="refundNo != null">`REFUND_NO`,</if>
        <if test="operateId != null">`OPERATE_ID`,</if>
        <if test="customerId != null">`CUSTOMER_ID`,</if>
        <if test="operateName != null">`OPERATE_NAME`,</if>
        <if test="customerName != null">`CUSTOMER_NAME`,</if>
        <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="refundNumber != null">`REFUND_NUMBER`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="refundPrice != null">`REFUND_PRICE`,</if>
        <if test="isRePutCard != null">`IS_RE_PUT_CARD`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
        <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
        <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
        <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
        <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
        <if test="customerName != null">#{customerName,jdbcType=VARCHAR},</if>
        <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="refundPrice != null">#{refundPrice,jdbcType=DECIMAL},</if>
        <if test="isRePutCard != null">#{isRePutCard,jdbcType=TINYINT},</if>
    </trim>
    </operation>
    </table>
