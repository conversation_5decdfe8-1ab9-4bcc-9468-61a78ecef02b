<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_STORE" physicalName="TP_LIFECIRCLE_STORE"
       remark="门店信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_STORE">
        INSERT INTO TP_LIFECIRCLE_STORE
        <selectKey keyProperty="storeId" order="AFTER" resultType="java.lang.Integer">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeLat != null">`STORE_LAT`,</if>
            <if test="storeLng != null">`STORE_LNG`,</if>
            <if test="key != null">`KEY`,</if>
            <if test="tel != null">`TEL`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="note != null">`NOTE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="county != null">`COUNTY`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="operate != null">`OPERATE`,</if>
            <if test="service != null">`SERVICE`,</if>
            <if test="deviceNo != null">`DEVICE_NO`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="brandName != null">`BRAND_NAME`,</if>
            <if test="objection != null">`OBJECTION`,</if>
            <if test="recommend != null">`RECOMMEND`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="storeArea != null">`STORE_AREA`,</if>
            <if test="storeLogo != null">`STORE_LOGO`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="denialType != null">`DENIAL_TYPE`,</if>
            <if test="storeBrief != null">`STORE_BRIEF`,</if>
            <if test="storeImage != null">`STORE_IMAGE`,</if>
            <if test="examineJson != null">`EXAMINE_JSON`,</if>
            <if test="serviceMore != null">`SERVICE_MORE`,</if>
            <if test="licenseNumber != null">`LICENSE_NUMBER`,</if>
            <if test="mainStoreInfo != null">`MAIN_STORE_INFO`,</if>
            <if test="recId != null">`REC_ID`,</if>
            <if test="isHorn != null">`IS_HORN`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="avgPrice != null">`AVG_PRICE`,</if>
            <if test="isOnline != null">`IS_ONLINE`,</if>
            <if test="joinTime != null">`JOIN_TIME`,</if>
            <if test="printNum != null">`PRINT_NUM`,</if>
            <if test="autoprint != null">`AUTOPRINT`,</if>
            <if test="bindLogId != null">`BIND_LOG_ID`,</if>
            <if test="printCode != null">`PRINT_CODE`,</if>
            <if test="categoryid != null">`CATEGORYID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isPromoted != null">`IS_PROMOTED`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="examineTime != null">`EXAMINE_TIME`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="printerType != null">`PRINTER_TYPE`,</if>
            <if test="storeStatus != null">`STORE_STATUS`,</if>
            <if test="isBindKoubei != null">`IS_BIND_KOUBEI`,</if>
            <if test="isTakepartin != null">`IS_TAKEPARTIN`,</if>
            <if test="takingOrders != null">`TAKING_ORDERS`,</if>
            <if test="examineStatus != null">`EXAMINE_STATUS`,</if>
            <if test="storeBelongId != null">`STORE_BELONG_ID`,</if>
            <if test="displayComment != null">`DISPLAY_COMMENT`,</if>
            <if test="indexRecommend != null">`INDEX_RECOMMEND`,</if>
            <if test="preferentialId != null">`PREFERENTIAL_ID`,</if>
            <if test="isHornCancelPay != null">`IS_HORN_CANCEL_PAY`,</if>
            <if test="takingOrdersType != null">`TAKING_ORDERS_TYPE`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="storeLat != null">#{storeLat,jdbcType=REAL},</if>
            <if test="storeLng != null">#{storeLng,jdbcType=REAL},</if>
            <if test="key != null">#{key,jdbcType=VARCHAR},</if>
            <if test="tel != null">#{tel,jdbcType=VARCHAR},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>
            <if test="note != null">#{note,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="county != null">#{county,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="endTime != null">#{endTime,jdbcType=CHAR},</if>
            <if test="operate != null">#{operate,jdbcType=LONGVARCHAR},</if>
            <if test="service != null">#{service,jdbcType=VARCHAR},</if>
            <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="brandName != null">#{brandName,jdbcType=VARCHAR},</if>
            <if test="objection != null">#{objection,jdbcType=VARCHAR},</if>
            <if test="recommend != null">#{recommend,jdbcType=VARCHAR},</if>
            <if test="startTime != null">#{startTime,jdbcType=CHAR},</if>
            <if test="storeArea != null">#{storeArea,jdbcType=VARCHAR},</if>
            <if test="storeLogo != null">#{storeLogo,jdbcType=LONGVARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="denialType != null">#{denialType,jdbcType=VARCHAR},</if>
            <if test="storeBrief != null">#{storeBrief,jdbcType=VARCHAR},</if>
            <if test="storeImage != null">#{storeImage,jdbcType=LONGVARCHAR},</if>
            <if test="examineJson != null">#{examineJson,jdbcType=LONGVARCHAR},</if>
            <if test="serviceMore != null">#{serviceMore,jdbcType=VARCHAR},</if>
            <if test="licenseNumber != null">#{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="mainStoreInfo != null">#{mainStoreInfo,jdbcType=VARCHAR},</if>
            <if test="recId != null">#{recId,jdbcType=INTEGER},</if>
            <if test="isHorn != null">#{isHorn,jdbcType=TINYINT},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="avgPrice != null">#{avgPrice,jdbcType=INTEGER},</if>
            <if test="isOnline != null">#{isOnline,jdbcType=TINYINT},</if>
            <if test="joinTime != null">#{joinTime,jdbcType=INTEGER},</if>
            <if test="printNum != null">#{printNum,jdbcType=TINYINT},</if>
            <if test="autoprint != null">#{autoprint,jdbcType=TINYINT},</if>
            <if test="bindLogId != null">#{bindLogId,jdbcType=INTEGER},</if>
            <if test="printCode != null">#{printCode,jdbcType=TINYINT},</if>
            <if test="categoryid != null">#{categoryid,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isPromoted != null">#{isPromoted,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="examineTime != null">#{examineTime,jdbcType=INTEGER},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="printerType != null">#{printerType,jdbcType=TINYINT},</if>
            <if test="storeStatus != null">#{storeStatus,jdbcType=TINYINT},</if>
            <if test="isBindKoubei != null">#{isBindKoubei,jdbcType=TINYINT},</if>
            <if test="isTakepartin != null">#{isTakepartin,jdbcType=TINYINT},</if>
            <if test="takingOrders != null">#{takingOrders,jdbcType=TINYINT},</if>
            <if test="examineStatus != null">#{examineStatus,jdbcType=TINYINT},</if>
            <if test="storeBelongId != null">#{storeBelongId,jdbcType=INTEGER},</if>
            <if test="displayComment != null">#{displayComment,jdbcType=TINYINT},</if>
            <if test="indexRecommend != null">#{indexRecommend,jdbcType=TINYINT},</if>
            <if test="preferentialId != null">#{preferentialId,jdbcType=INTEGER},</if>
            <if test="isHornCancelPay != null">#{isHornCancelPay,jdbcType=TINYINT},</if>
            <if test="takingOrdersType != null">#{takingOrdersType,jdbcType=TINYINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="update" paramtype="object" remark="update table:TP_LIFECIRCLE_STORE">
        UPDATE TP_LIFECIRCLE_STORE
        SET
        <trim prefix="" suffix="" suffixOverrides=",">
            <if test="key != null">`KEY` = #{key,jdbcType=VARCHAR},</if>
            <if test="tel != null">TEL = #{tel,jdbcType=VARCHAR},</if>
            <if test="city != null">CITY = #{city,jdbcType=VARCHAR},</if>
            <if test="note != null">NOTE = #{note,jdbcType=VARCHAR},</if>
            <if test="recId != null">REC_ID = #{recId,jdbcType=INTEGER},</if>
            <if test="token != null">TOKEN = #{token,jdbcType=VARCHAR},</if>
            <if test="county != null">COUNTY = #{county,jdbcType=VARCHAR},</if>
            <if test="isHorn != null">IS_HORN = #{isHorn,jdbcType=TINYINT},</if>
            <if test="isShow != null">IS_SHOW = #{isShow,jdbcType=TINYINT},</if>
            <if test="address != null">ADDRESS = #{address,jdbcType=VARCHAR},</if>
            <if test="agentId != null">AGENT_ID = #{agentId,jdbcType=INTEGER},</if>
            <if test="endTime != null">END_TIME = #{endTime,jdbcType=CHAR},</if>
            <if test="operate != null">OPERATE = #{operate,jdbcType=LONGVARCHAR},</if>
            <if test="service != null">SERVICE = #{service,jdbcType=VARCHAR},</if>
            <if test="avgPrice != null">AVG_PRICE = #{avgPrice,jdbcType=INTEGER},</if>
            <if test="deviceNo != null">DEVICE_NO = #{deviceNo,jdbcType=VARCHAR},</if>
            <if test="isOnline != null">IS_ONLINE = #{isOnline,jdbcType=TINYINT},</if>
            <if test="joinTime != null">JOIN_TIME = #{joinTime,jdbcType=INTEGER},</if>
            <if test="printNum != null">PRINT_NUM = #{printNum,jdbcType=TINYINT},</if>
            <if test="province != null">PROVINCE = #{province,jdbcType=VARCHAR},</if>
            <if test="storeLat != null">STORE_LAT = #{storeLat,jdbcType=REAL},</if>
            <if test="storeLng != null">STORE_LNG = #{storeLng,jdbcType=REAL},</if>
            <if test="autoprint != null">AUTOPRINT = #{autoprint,jdbcType=TINYINT},</if>
            <if test="bindLogId != null">BIND_LOG_ID = #{bindLogId,jdbcType=INTEGER},</if>
            <if test="brandName != null">BRAND_NAME = #{brandName,jdbcType=VARCHAR},</if>
            <if test="objection != null">OBJECTION = #{objection,jdbcType=VARCHAR},</if>
            <if test="printCode != null">PRINT_CODE = #{printCode,jdbcType=TINYINT},</if>
            <if test="recommend != null">RECOMMEND = #{recommend,jdbcType=VARCHAR},</if>
            <if test="startTime != null">START_TIME = #{startTime,jdbcType=CHAR},</if>
            <if test="storeArea != null">STORE_AREA = #{storeArea,jdbcType=VARCHAR},</if>
            <if test="storeLogo != null">STORE_LOGO = #{storeLogo,jdbcType=LONGVARCHAR},</if>
            <if test="storeName != null">STORE_NAME = #{storeName,jdbcType=VARCHAR},</if>
            <if test="branchName != null">BRANCH_NAME = #{branchName,jdbcType=VARCHAR},</if>
            <if test="categoryid != null">CATEGORYID = #{categoryid,jdbcType=INTEGER},</if>
            <if test="createTime != null">CREATE_TIME = #{createTime,jdbcType=INTEGER},</if>
            <if test="isPromoted != null">IS_PROMOTED = #{isPromoted,jdbcType=TINYINT},</if>
            <if test="storeBrief != null">STORE_BRIEF = #{storeBrief,jdbcType=VARCHAR},</if>
            <if test="storeImage != null">STORE_IMAGE = #{storeImage,jdbcType=LONGVARCHAR},</if>
            <if test="unityCatId != null">UNITY_CAT_ID = #{unityCatId,jdbcType=SMALLINT},</if>
            <if test="updateTime != null">UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="examineJson != null">EXAMINE_JSON = #{examineJson,jdbcType=LONGVARCHAR},</if>
            <if test="examineTime != null">EXAMINE_TIME = #{examineTime,jdbcType=INTEGER},</if>
            <if test="joinChannel != null">JOIN_CHANNEL = #{joinChannel,jdbcType=TINYINT},</if>
            <if test="printerType != null">PRINTER_TYPE = #{printerType,jdbcType=TINYINT},</if>
            <if test="serviceMore != null">SERVICE_MORE = #{serviceMore,jdbcType=VARCHAR},</if>
            <if test="storeStatus != null">STORE_STATUS = #{storeStatus,jdbcType=TINYINT},</if>
            <if test="isBindKoubei != null">IS_BIND_KOUBEI = #{isBindKoubei,jdbcType=TINYINT},</if>
            <if test="isTakepartin != null">IS_TAKEPARTIN = #{isTakepartin,jdbcType=TINYINT},</if>
            <if test="takingOrders != null">TAKING_ORDERS = #{takingOrders,jdbcType=TINYINT},</if>
            <if test="examineStatus != null">EXAMINE_STATUS = #{examineStatus,jdbcType=TINYINT},</if>
            <if test="licenseNumber != null">LICENSE_NUMBER = #{licenseNumber,jdbcType=VARCHAR},</if>
            <if test="mainStoreInfo != null">MAIN_STORE_INFO = #{mainStoreInfo,jdbcType=VARCHAR},</if>
            <if test="storeBelongId != null">STORE_BELONG_ID = #{storeBelongId,jdbcType=INTEGER},</if>
            <if test="displayComment != null">DISPLAY_COMMENT = #{displayComment,jdbcType=TINYINT},</if>
            <if test="indexRecommend != null">INDEX_RECOMMEND = #{indexRecommend,jdbcType=TINYINT},</if>
            <if test="preferentialId != null">PREFERENTIAL_ID = #{preferentialId,jdbcType=INTEGER},</if>
            <if test="isHornCancelPay != null">IS_HORN_CANCEL_PAY = #{isHornCancelPay,jdbcType=TINYINT},</if>
            <if test="takingOrdersType != null">TAKING_ORDERS_TYPE = #{takingOrdersType,jdbcType=TINYINT},</if>
            <if test="settlementType != null">SETTLEMENT_TYPE = #{settlementType,jdbcType=TINYINT},</if>
            <if test="merchantQualification != null">MERCHANT_QUALIFICATION = #{merchantQualification,jdbcType=TINYINT},</if>
        </trim>
        WHERE
        STORE_ID = #{storeId,jdbcType=INTEGER}
    </operation>

    <operation name="getByStoreId" multiplicity="one" remark="根据 store_id 查询记录">
        SELECT *
        FROM tp_lifecircle_store
        WHERE
        `store_id` = #{storeId,jdbcType=INTEGER}
        limit 1
    </operation>

    <operation name="getStoreNameByStoreId" multiplicity="one" remark="根据storeId获取门店名称">
        SELECT
        `store_name` AS storeName
        FROM tp_lifecircle_store
        WHERE
        `store_id` = #{storeId,jdbcType=INTEGER}
        limit 1
    </operation>

    <operation name="findStoreNameByStoreIds" multiplicity="many" remark="根据门店id查询门店名称">
        SELECT
        STORE_ID,
        STORE_NAME,
        AUTOPRINT
        FROM TP_LIFECIRCLE_STORE
        WHERE
        STORE_ID IN
        <foreach collection="list" item="storeId" open="(" close=")" separator=",">
            #{storeId,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="getFirstStoreIdByUid" paramtype="primitive" multiplicity="one" remark="根据UID查询首家门店ID"
               resulttype="java.lang.Integer">
        SELECT
        store.STORE_ID
        FROM
        TP_LIFECIRCLE_STORE store
        INNER JOIN TP_USERS users ON store.TOKEN = users.USERS_TOKEN
        WHERE
        users.`ID` = #{uid,jdbcType=INTEGER}
        AND users.PARENT_ID = 0
        AND users.INCOME_STATUS IN (3,5)
        ORDER BY
        store.STORE_ID ASC
        LIMIT 1
    </operation>

    <operation name="getOneByStoreName" multiplicity="one" paramtype="primitive" remark="根据店铺名称获取店铺信息">
        select
        STORE_LAT,STORE_LNG,TEL,CITY,NOTE,TOKEN,COUNTY,ADDRESS,END_TIME,OPERATE,SERVICE,DEVICE_NO,PROVINCE,BRAND_NAME,OBJECTION,RECOMMEND,START_TIME,STORE_AREA,STORE_LOGO,STORE_NAME,BRANCH_NAME,DENIAL_TYPE,STORE_BRIEF,STORE_IMAGE,EXAMINE_JSON,SERVICE_MORE,LICENSE_NUMBER,MAIN_STORE_INFO,REC_ID,IS_HORN,IS_SHOW,AGENT_ID,STORE_ID,AVG_PRICE,IS_ONLINE,JOIN_TIME,PRINT_NUM,SETTLE_TO,AUTOPRINT,BIND_LOG_ID,PRINT_CODE,STORE_TYPE,CATEGORYID,CREATE_TIME,IS_PROMOTED,UNITY_CAT_ID,EXAMINE_TIME,JOIN_CHANNEL,PRINTER_TYPE,STORE_STATUS,IS_BIND_KOUBEI,IS_NEED_INCOME,IS_TAKEPARTIN,TAKING_ORDERS,EXAMINE_STATUS,STORE_BELONG_ID,DISPLAY_COMMENT,INDEX_RECOMMEND,PREFERENTIAL_ID,SETTLEMENT_TYPE,IS_HORN_CANCEL_PAY,SETTLE_START_TIME,TAKING_ORDERS_TYPE,MERCHANT_QUALIFICATION,UPDATE_TIME
        from tp_lifecircle_store
        where
        store_name = #{storeName,jdbcType=VARCHAR}
        limit 1
    </operation>
</table>
