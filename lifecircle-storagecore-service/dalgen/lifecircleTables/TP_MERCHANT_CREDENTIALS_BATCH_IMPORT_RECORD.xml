<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD" physicalName="TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD"
       remark="商户临期批量导入记录">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD">
        INSERT INTO TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="batchId != null">`BATCH_ID`,</if>
            <if test="failUrl != null">`FAIL_URL`,</if>
            <if test="taskStatus != null">`TASK_STATUS`,</if>
            <if test="fileName != null">`FILE_NAME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="batchId != null">#{batchId,jdbcType=VARCHAR},</if>
            <if test="failUrl != null">#{failUrl,jdbcType=VARCHAR},</if>
            <if test="taskStatus != null">#{taskStatus,jdbcType=TINYINT},</if>
            <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="updateByBatchId" paramtype="object">
        UPDATE TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        <set>
            <if test="reason != null">`REASON` = #{reason,jdbcType=VARCHAR},</if>
            <if test="failUrl != null">`FAIL_URL` = #{failUrl,jdbcType=VARCHAR},</if>
            <if test="taskStatus != null">`TASK_STATUS` = #{taskStatus,jdbcType=TINYINT},</if>
            <if test="fileName != null">`FILE_NAME` = #{fileName,jdbcType=VARCHAR},</if>
        </set>
        WHERE `BATCH_ID` = #{batchId,jdbcType=VARCHAR}
    </operation>

    <operation name="getByBatchId" multiplicity="one" remark="根据批次号查询导入记录">
        SELECT
        *
        FROM TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        WHERE `BATCH_ID` = #{batchId,jdbcType=VARCHAR}
        limit 1;
    </operation>

    <operation name="findImportRecordList" multiplicity="paging" paging="MerchantCredentialsImportRecordList">
        SELECT
        *
        FROM TP_MERCHANT_CREDENTIALS_BATCH_IMPORT_RECORD
        WHERE 1=1
        ORDER BY ID DESC
    </operation>
</table>
