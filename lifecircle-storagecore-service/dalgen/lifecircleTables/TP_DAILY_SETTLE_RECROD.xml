<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_DAILY_SETTLE_RECROD" physicalName="TP_DAILY_SETTLE_RECROD"
       remark="每日结算记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_DAILY_SETTLE_RECROD">
        INSERT INTO TP_DAILY_SETTLE_RECROD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mno != null">`MNO`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="serialNo != null">`SERIAL_NO`,</if>
            <if test="settleDt != null">`SETTLE_DT`,</if>
            <if test="refReason != null">`REF_REASON`,</if>
            <if test="settleTime != null">`SETTLE_TIME`,</if>
            <if test="settleBnkNo != null">`SETTLE_BNK_NO`,</if>
            <if test="settleBankNm != null">`SETTLE_BANK_NM`,</if>
            <if test="settleBankNo != null">`SETTLE_BANK_NO`,</if>
            <if test="settleStatus != null">`SETTLE_STATUS`,</if>
            <if test="settleAccountNm != null">`SETTLE_ACCOUNT_NM`,</if>
            <if test="settleAccountType != null">`SETTLE_ACCOUNT_TYPE`,</if>
            <if test="channel != null">`CHANNEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="amount != null">`AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="mno != null">#{mno,jdbcType=VARCHAR},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="settleDt != null">#{settleDt,jdbcType=VARCHAR},</if>
            <if test="refReason != null">#{refReason,jdbcType=VARCHAR},</if>
            <if test="settleTime != null">#{settleTime,jdbcType=VARCHAR},</if>
            <if test="settleBnkNo != null">#{settleBnkNo,jdbcType=VARCHAR},</if>
            <if test="settleBankNm != null">#{settleBankNm,jdbcType=VARCHAR},</if>
            <if test="settleBankNo != null">#{settleBankNo,jdbcType=VARCHAR},</if>
            <if test="settleStatus != null">#{settleStatus,jdbcType=VARCHAR},</if>
            <if test="settleAccountNm != null">#{settleAccountNm,jdbcType=VARCHAR},</if>
            <if test="settleAccountType != null">#{settleAccountType,jdbcType=VARCHAR},</if>
            <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="amount != null">#{amount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
    <operation name="batchInsert" paramtype="objectList" remark="批量插入记录">
        replace INTO TP_DAILY_SETTLE_RECROD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            `MNO`,
            `ORG_ID`,
            `SERIAL_NO`,
            `SETTLE_DT`,
            `REF_REASON`,
            `SETTLE_TIME`,
            `SETTLE_BNK_NO`,
            `SETTLE_BANK_NM`,
            `SETTLE_BANK_NO`,
            `SETTLE_STATUS`,
            `SETTLE_ACCOUNT_NM`,
            `SETTLE_ACCOUNT_TYPE`,
            `CHANNEL`,
            `FEE`,
            `AMOUNT`
        </trim>
        VALUES
        <foreach collection="list" item="record" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{record.mno,jdbcType=VARCHAR},
                #{record.orgId,jdbcType=VARCHAR},
                #{record.serialNo,jdbcType=VARCHAR},
                #{record.settleDt,jdbcType=VARCHAR},
                #{record.refReason,jdbcType=VARCHAR},
                #{record.settleTime,jdbcType=VARCHAR},
                #{record.settleBnkNo,jdbcType=VARCHAR},
                #{record.settleBankNm,jdbcType=VARCHAR},
                #{record.settleBankNo,jdbcType=VARCHAR},
                #{record.settleStatus,jdbcType=VARCHAR},
                #{record.settleAccountNm,jdbcType=VARCHAR},
                #{record.settleAccountType,jdbcType=VARCHAR},
                #{record.channel,jdbcType=TINYINT},
                #{record.fee,jdbcType=DECIMAL},
                #{record.amount,jdbcType=DECIMAL},
            </trim>
        </foreach>
    </operation>
</table>
