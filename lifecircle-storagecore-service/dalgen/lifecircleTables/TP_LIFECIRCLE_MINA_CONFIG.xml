<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_MINA_CONFIG" physicalName="TP_LIFECIRCLE_MINA_CONFIG"
    remark="TP_LIFECIRCLE_MINA_CONFIG">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_MINA_CONFIG">
INSERT INTO TP_LIFECIRCLE_MINA_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="appid != null">`APPID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="liquidationConfigStatus != null">`LIQUIDATION_CONFIG_STATUS`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="liquidationConfigStatus != null">#{liquidationConfigStatus,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getBySubMchId" multiplicity="one" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        uid
        FROM
        tp_lifecircle_mina_config
        WHERE
        mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
