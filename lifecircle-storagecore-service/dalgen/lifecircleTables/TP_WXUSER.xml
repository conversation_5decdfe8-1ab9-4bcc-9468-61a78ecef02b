<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_WXUSER" physicalName="TP_WXUSER"
       remark="TP_WXUSER">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_WXUSER">
        INSERT INTO TP_WXUSER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="ordercancel != null">`ORDERCANCEL`,</if>
            <if test="contribution != null">`CONTRIBUTION`,</if>
            <if test="appDefaultSacle != null">`APP_DEFAULT_SACLE`,</if>
            <if test="orderCommission != null">`ORDER_COMMISSION`,</if>
            <if test="rank1Commission != null">`RANK_1_COMMISSION`,</if>
            <if test="rank2Commission != null">`RANK_2_COMMISSION`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="tel != null">`TEL`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="text != null">`TEXT`,</if>
            <if test="wxid != null">`WXID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="bgImg != null">`BG_IMG`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="sToken != null">`S_TOKEN`,</if>
            <if test="urlimg != null">`URLIMG`,</if>
            <if test="weixin != null">`WEIXIN`,</if>
            <if test="wxname != null">`WXNAME`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="focusus != null">`FOCUSUS`,</if>
            <if test="shopUrl != null">`SHOP_URL`,</if>
            <if test="funcInfo != null">`FUNC_INFO`,</if>
            <if test="goodsTag != null">`GOODS_TAG`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="shareImg != null">`SHARE_IMG`,</if>
            <if test="storeUrl != null">`STORE_URL`,</if>
            <if test="appsecret != null">`APPSECRET`,</if>
            <if test="headerpic != null">`HEADERPIC`,</if>
            <if test="notifyUrl != null">`NOTIFY_URL`,</if>
            <if test="qrcodeUrl != null">`QRCODE_URL`,</if>
            <if test="cacertpath != null">`CACERTPATH`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="partnerkey != null">`PARTNERKEY`,</if>
            <if test="paysignkey != null">`PAYSIGNKEY`,</if>
            <if test="shareTitle != null">`SHARE_TITLE`,</if>
            <if test="shopsecret != null">`SHOPSECRET`,</if>
            <if test="ticketLogo != null">`TICKET_LOGO`,</if>
            <if test="storeQrcode != null">`STORE_QRCODE`,</if>
            <if test="tpllistname != null">`TPLLISTNAME`,</if>
            <if test="tpltypename != null">`TPLTYPENAME`,</if>
            <if test="inviteQrcode != null">`INVITE_QRCODE`,</if>
            <if test="notifySecret != null">`NOTIFY_SECRET`,</if>
            <if test="originalLogo != null">`ORIGINAL_LOGO`,</if>
            <if test="refreshToken != null">`REFRESH_TOKEN`,</if>
            <if test="shareContent != null">`SHARE_CONTENT`,</if>
            <if test="tplcontentname != null">`TPLCONTENTNAME`,</if>
            <if test="distributorQrcode != null">`DISTRIBUTOR_QRCODE`,</if>
            <if test="distributorSalespercent != null">`DISTRIBUTOR_SALESPERCENT`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="cityId != null">`CITY_ID`,</if>
            <if test="shopId != null">`SHOP_ID`,</if>
            <if test="colorId != null">`COLOR_ID`,</if>
            <if test="merchant != null">`MERCHANT`,</if>
            <if test="partnerid != null">`PARTNERID`,</if>
            <if test="handleTime != null">`HANDLE_TIME`,</if>
            <if test="openMobile != null">`OPEN_MOBILE`,</if>
            <if test="provinceId != null">`PROVINCE_ID`,</if>
            <if test="salerAudit != null">`SALER_AUDIT`,</if>
            <if test="confirmgood != null">`CONFIRMGOOD`,</if>
            <if test="isAuthorize != null">`IS_AUTHORIZE`,</if>
            <if test="verifyTypeInfo != null">`VERIFY_TYPE_INFO`,</if>
            <if test="serviceTypeInfo != null">`SERVICE_TYPE_INFO`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="condition != null">`CONDITION`,</if>
            <if test="appMincash != null">`APP_MINCASH`,</if>
            <if test="dealamount != null">`DEALAMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="ordercancel != null">#{ordercancel,jdbcType=REAL},</if>
            <if test="contribution != null">#{contribution,jdbcType=REAL},</if>
            <if test="appDefaultSacle != null">#{appDefaultSacle,jdbcType=REAL},</if>
            <if test="orderCommission != null">#{orderCommission,jdbcType=REAL},</if>
            <if test="rank1Commission != null">#{rank1Commission,jdbcType=REAL},</if>
            <if test="rank2Commission != null">#{rank2Commission,jdbcType=REAL},</if>
            <if test="qq != null">#{qq,jdbcType=CHAR},</if>
            <if test="tel != null">#{tel,jdbcType=CHAR},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>
            <if test="text != null">#{text,jdbcType=LONGVARCHAR},</if>
            <if test="wxid != null">#{wxid,jdbcType=VARCHAR},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="bgImg != null">#{bgImg,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="sToken != null">#{sToken,jdbcType=VARCHAR},</if>
            <if test="urlimg != null">#{urlimg,jdbcType=VARCHAR},</if>
            <if test="weixin != null">#{weixin,jdbcType=VARCHAR},</if>
            <if test="wxname != null">#{wxname,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="focusus != null">#{focusus,jdbcType=VARCHAR},</if>
            <if test="shopUrl != null">#{shopUrl,jdbcType=VARCHAR},</if>
            <if test="funcInfo != null">#{funcInfo,jdbcType=LONGVARCHAR},</if>
            <if test="goodsTag != null">#{goodsTag,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="shareImg != null">#{shareImg,jdbcType=VARCHAR},</if>
            <if test="storeUrl != null">#{storeUrl,jdbcType=VARCHAR},</if>
            <if test="appsecret != null">#{appsecret,jdbcType=VARCHAR},</if>
            <if test="headerpic != null">#{headerpic,jdbcType=CHAR},</if>
            <if test="notifyUrl != null">#{notifyUrl,jdbcType=VARCHAR},</if>
            <if test="qrcodeUrl != null">#{qrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="cacertpath != null">#{cacertpath,jdbcType=VARCHAR},</if>
            <if test="createtime != null">#{createtime,jdbcType=VARCHAR},</if>
            <if test="partnerkey != null">#{partnerkey,jdbcType=VARCHAR},</if>
            <if test="paysignkey != null">#{paysignkey,jdbcType=VARCHAR},</if>
            <if test="shareTitle != null">#{shareTitle,jdbcType=VARCHAR},</if>
            <if test="shopsecret != null">#{shopsecret,jdbcType=VARCHAR},</if>
            <if test="ticketLogo != null">#{ticketLogo,jdbcType=VARCHAR},</if>
            <if test="storeQrcode != null">#{storeQrcode,jdbcType=VARCHAR},</if>
            <if test="tpllistname != null">#{tpllistname,jdbcType=VARCHAR},</if>
            <if test="tpltypename != null">#{tpltypename,jdbcType=VARCHAR},</if>
            <if test="inviteQrcode != null">#{inviteQrcode,jdbcType=VARCHAR},</if>
            <if test="notifySecret != null">#{notifySecret,jdbcType=CHAR},</if>
            <if test="originalLogo != null">#{originalLogo,jdbcType=VARCHAR},</if>
            <if test="refreshToken != null">#{refreshToken,jdbcType=VARCHAR},</if>
            <if test="shareContent != null">#{shareContent,jdbcType=VARCHAR},</if>
            <if test="tplcontentname != null">#{tplcontentname,jdbcType=VARCHAR},</if>
            <if test="distributorQrcode != null">#{distributorQrcode,jdbcType=VARCHAR},</if>
            <if test="distributorSalespercent != null">#{distributorSalespercent,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="cityId != null">#{cityId,jdbcType=INTEGER},</if>
            <if test="shopId != null">#{shopId,jdbcType=INTEGER},</if>
            <if test="colorId != null">#{colorId,jdbcType=INTEGER},</if>
            <if test="merchant != null">#{merchant,jdbcType=TINYINT},</if>
            <if test="partnerid != null">#{partnerid,jdbcType=INTEGER},</if>
            <if test="handleTime != null">#{handleTime,jdbcType=INTEGER},</if>
            <if test="openMobile != null">#{openMobile,jdbcType=TINYINT},</if>
            <if test="provinceId != null">#{provinceId,jdbcType=INTEGER},</if>
            <if test="salerAudit != null">#{salerAudit,jdbcType=TINYINT},</if>
            <if test="confirmgood != null">#{confirmgood,jdbcType=INTEGER},</if>
            <if test="isAuthorize != null">#{isAuthorize,jdbcType=TINYINT},</if>
            <if test="verifyTypeInfo != null">#{verifyTypeInfo,jdbcType=TINYINT},</if>
            <if test="serviceTypeInfo != null">#{serviceTypeInfo,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="condition != null">#{condition,jdbcType=DECIMAL},</if>
            <if test="appMincash != null">#{appMincash,jdbcType=DECIMAL},</if>
            <if test="dealamount != null">#{dealamount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation remark="通过token获取uid" name="getUidByToken" resulttype="java.lang.Integer" multiplicity="one">
        SELECT uid
        FROM TP_WXUSER
        WHERE token = #{token,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation remark="通过uid获取token" name="getTokenByUid" resulttype="java.lang.String" multiplicity="one">
        SELECT token
        FROM TP_WXUSER
        WHERE uid = #{uid,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="getUidAndTokenListByTokens" multiplicity="many" remark="通过tokens查询uid列表">
        SELECT uid, token FROM TP_WXUSER
        WHERE token IN
        <foreach collection="list" item="token" index="index" separator="," open="(" close=")">
            #{token, jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
