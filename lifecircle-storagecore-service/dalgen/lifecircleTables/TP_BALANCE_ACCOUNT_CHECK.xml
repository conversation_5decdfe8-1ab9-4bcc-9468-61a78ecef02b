<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_BALANCE_ACCOUNT_CHECK" physicalName="TP_BALANCE_ACCOUNT_CHECK"
    remark="账户对账结果表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_BALANCE_ACCOUNT_CHECK">
INSERT INTO TP_BALANCE_ACCOUNT_CHECK
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="counterofferCheckResult != null">`COUNTEROFFER_CHECK_RESULT`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="counterofferMoney != null">`COUNTEROFFER_MONEY`,</if>
        <if test="yestedayTradeMoney != null">`YESTEDAY_TRADE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="counterofferCheckResult != null">#{counterofferCheckResult,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="counterofferMoney != null">#{counterofferMoney,jdbcType=DECIMAL},</if>
        <if test="yestedayTradeMoney != null">#{yestedayTradeMoney,jdbcType=DECIMAL},</if>
    </trim>
    </operation>
    <operation name="getByTokenAndTradeDate" remark="根据商户token和交易日期">
        SELECT
        *
        FROM
        TP_BALANCE_ACCOUNT_CHECK
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="updateByTokenAndTradeDate" paramtype="object" remark="根据商户token和交易日期进行更新">
        UPDATE
        TP_BALANCE_ACCOUNT_CHECK
        SET
        yesteday_trade_money = #{yestedayTradeMoney,jdbcType=DECIMAL},
        counteroffer_money = #{counterofferMoney,jdbcType=DECIMAL},
        counteroffer_check_result = #{counterofferCheckResult,jdbcType=TINYINT}
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
    </operation>
    </table>
