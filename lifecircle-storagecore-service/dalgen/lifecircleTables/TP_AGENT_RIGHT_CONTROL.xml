<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table physicalName="TP_AGENT_RIGHT_CONTROL" remark="TP_AGENT_RIGHT_CONTROL" sqlname="TP_AGENT_RIGHT_CONTROL">

    <operation name="insert" paramtype="object" remark="insert:TP_AGENT_RIGHT_CONTROL">
        INSERT INTO TP_AGENT_RIGHT_CONTROL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="keyId != null">`KEY_ID`,</if>
            <if test="isQqpay != null">`IS_QQPAY`,</if>
            <if test="isYipay != null">`IS_YIPAY`,</if>
            <if test="zeroFee != null">`ZERO_FEE`,</if>
            <if test="isH5payWx != null">`IS_H5PAY_WX`,</if>
            <if test="isOpenapi != null">`IS_OPENAPI`,</if>
            <if test="zfEndTime != null">`ZF_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isBdFreeze != null">`IS_BD_FREEZE`,</if>
            <if test="isFastAuth != null">`IS_FAST_AUTH`,</if>
            <if test="isUnionpay != null">`IS_UNIONPAY`,</if>
            <if test="modifyTime != null">`MODIFY_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="isFubeiLoan != null">`IS_FUBEI_LOAN`,</if>
            <if test="isHurricane != null">`IS_HURRICANE`,</if>
            <if test="minaBootNum != null">`MINA_BOOT_NUM`,</if>
            <if test="zfStartTime != null">`ZF_START_TIME`,</if>
            <if test="grantUpgrade != null">`GRANT_UPGRADE`,</if>
            <if test="isWechatCity != null">`IS_WECHAT_CITY`,</if>
            <if test="hurricaneTime != null">`HURRICANE_TIME`,</if>
            <if test="isKoubeiAccess != null">`IS_KOUBEI_ACCESS`,</if>
            <if test="isOnlyAttention != null">`IS_ONLY_ATTENTION`,</if>
            <if test="isSuperSalesman != null">`IS_SUPER_SALESMAN`,</if>
            <if test="openZeroFeeTime != null">`OPEN_ZERO_FEE_TIME`,</if>
            <if test="quickCashSwitch != null">`QUICK_CASH_SWITCH`,</if>
            <if test="closeZeroFeeTime != null">`CLOSE_ZERO_FEE_TIME`,</if>
            <if test="isCustomizedMina != null">`IS_CUSTOMIZED_MINA`,</if>
            <if test="superSalesmanNum != null">`SUPER_SALESMAN_NUM`,</if>
            <if test="merchantIsOpenapi != null">`MERCHANT_IS_OPENAPI`,</if>
            <if test="isAdvertisingRight != null">`IS_ADVERTISING_RIGHT`,</if>
            <if test="isQuickPushService != null">`IS_QUICK_PUSH_SERVICE`,</if>
            <if test="isConfigOwnChannel != null">`IS_CONFIG_OWN_CHANNEL`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="keyId != null">#{keyId,jdbcType=INTEGER},</if>
            <if test="isQqpay != null">#{isQqpay,jdbcType=TINYINT},</if>
            <if test="isYipay != null">#{isYipay,jdbcType=TINYINT},</if>
            <if test="zeroFee != null">#{zeroFee,jdbcType=TINYINT},</if>
            <if test="isH5payWx != null">#{isH5payWx,jdbcType=TINYINT},</if>
            <if test="isOpenapi != null">#{isOpenapi,jdbcType=TINYINT},</if>
            <if test="zfEndTime != null">#{zfEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isBdFreeze != null">#{isBdFreeze,jdbcType=TINYINT},</if>
            <if test="isFastAuth != null">#{isFastAuth,jdbcType=TINYINT},</if>
            <if test="isUnionpay != null">#{isUnionpay,jdbcType=TINYINT},</if>
            <if test="modifyTime != null">#{modifyTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="isFubeiLoan != null">#{isFubeiLoan,jdbcType=TINYINT},</if>
            <if test="isHurricane != null">#{isHurricane,jdbcType=TINYINT},</if>
            <if test="minaBootNum != null">#{minaBootNum,jdbcType=TINYINT},</if>
            <if test="zfStartTime != null">#{zfStartTime,jdbcType=INTEGER},</if>
            <if test="grantUpgrade != null">#{grantUpgrade,jdbcType=TINYINT},</if>
            <if test="isWechatCity != null">#{isWechatCity,jdbcType=TINYINT},</if>
            <if test="hurricaneTime != null">#{hurricaneTime,jdbcType=INTEGER},</if>
            <if test="isKoubeiAccess != null">#{isKoubeiAccess,jdbcType=TINYINT},</if>
            <if test="isOnlyAttention != null">#{isOnlyAttention,jdbcType=TINYINT},</if>
            <if test="isSuperSalesman != null">#{isSuperSalesman,jdbcType=TINYINT},</if>
            <if test="openZeroFeeTime != null">#{openZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="quickCashSwitch != null">#{quickCashSwitch,jdbcType=TINYINT},</if>
            <if test="closeZeroFeeTime != null">#{closeZeroFeeTime,jdbcType=INTEGER},</if>
            <if test="isCustomizedMina != null">#{isCustomizedMina,jdbcType=TINYINT},</if>
            <if test="superSalesmanNum != null">#{superSalesmanNum,jdbcType=SMALLINT},</if>
            <if test="merchantIsOpenapi != null">#{merchantIsOpenapi,jdbcType=TINYINT},</if>
            <if test="isAdvertisingRight != null">#{isAdvertisingRight,jdbcType=TINYINT},</if>
            <if test="isQuickPushService != null">#{isQuickPushService,jdbcType=TINYINT},</if>
            <if test="isConfigOwnChannel != null">#{isConfigOwnChannel,jdbcType=TINYINT},</if>

        </trim>
    </operation>

    <operation multiplicity="one" name="getById" remark="get:TP_AGENT_RIGHT_CONTROL">
        SELECT *
        FROM TP_AGENT_RIGHT_CONTROL
        WHERE
        ID
        = #{id,jdbcType=INTEGER}
    </operation>

    <operation multiplicity="one" name="getAgentRightControlByAgentId"
               remark="get:根据代理商ID[keyId]查询代理商权限信息">
        SELECT *
        FROM TP_AGENT_RIGHT_CONTROL
        WHERE
        key_id = #{keyId,jdbcType=INTEGER}
        limit 1
    </operation>

</table>
