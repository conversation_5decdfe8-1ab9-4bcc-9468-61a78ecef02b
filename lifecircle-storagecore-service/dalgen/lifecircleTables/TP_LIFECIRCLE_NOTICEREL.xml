<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_NOTICEREL" physicalName="TP_LIFECIRCLE_NOTICEREL"
       remark="TP_LIFECIRCLE_NOTICEREL">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_NOTICEREL">
        INSERT INTO TP_LIFECIRCLE_NOTICEREL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="posterUrl != null">`POSTER_URL`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="usersId != null">`USERS_ID`,</if>
            <if test="noticeId != null">`NOTICE_ID`,</if>
            <if test="childrenId != null">`CHILDREN_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="isRecommend != null">`IS_RECOMMEND`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="posterUrl != null">#{posterUrl,jdbcType=LONGVARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="usersId != null">#{usersId,jdbcType=INTEGER},</if>
            <if test="noticeId != null">#{noticeId,jdbcType=INTEGER},</if>
            <if test="childrenId != null">#{childrenId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="isRecommend != null">#{isRecommend,jdbcType=TINYINT},</if>
            <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
        </trim>
    </operation>
    <operation name="findPosterUrlByActivityId" paramtype="primitive" resulttype="String" multiplicity="many"
               remark="根据活动 ID 获得海报地址列表">
        SELECT
        poster_url
        FROM
        TP_LIFECIRCLE_NOTICEREL
        WHERE
        notice_id = #{noticeId,jdbcType=INTEGER}
    </operation>
</table>
