<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PA_BANK_UNIONPAY_CODE" physicalName="TP_PA_BANK_UNIONPAY_CODE"
       remark="TP_PA_BANK_UNIONPAY_CODE">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PA_BANK_UNIONPAY_CODE">
        INSERT INTO TP_PA_BANK_UNIONPAY_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByUnionpayCode" paramtype="primitive" multiplicity="one" resulttype="String"
               remark="根据联行号查询开户行">
        SELECT branch_name from tp_pa_bank_unionpay_code
        where unionpay_code = #{unionpayCode,jdbcType=VARCHAR}
    </operation>

    <operation name="getInfoByUnionPayCode" paramtype="primitive" multiplicity="one"
               remark="根据联行号查询信息">
        SELECT * from tp_pa_bank_unionpay_code
        where unionpay_code = #{unionpayCode,jdbcType=VARCHAR} limit 1
    </operation>
</table>
