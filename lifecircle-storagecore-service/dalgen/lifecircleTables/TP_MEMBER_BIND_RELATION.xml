<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_MEMBER_BIND_RELATION" physicalName="TP_MEMBER_BIND_RELATION"
    remark="付呗用户关联表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_MEMBER_BIND_RELATION">
INSERT INTO TP_MEMBER_BIND_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext0 != null">`EXT0`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="phone != null">`PHONE`,</if>
        <if test="userIds != null">`USER_IDS`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="wxUserId != null">`WX_USER_ID`,</if>
        <if test="alipayUserId != null">`ALIPAY_USER_ID`,</if>
        <if test="entityUserId != null">`ENTITY_USER_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="ext0 != null">#{ext0,jdbcType=VARCHAR},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="phone != null">#{phone,jdbcType=CHAR},</if>
        <if test="userIds != null">#{userIds,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="wxUserId != null">#{wxUserId,jdbcType=INTEGER},</if>
        <if test="alipayUserId != null">#{alipayUserId,jdbcType=INTEGER},</if>
        <if test="entityUserId != null">#{entityUserId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="selectByPhone" paramtype="primitive" multiplicity="one" remark="根据手机号码获取用户关联信息">
        select * from TP_MEMBER_BIND_RELATION where phone = #{phone,jdbcType=VARCHAR} and card_type = 1 limit 1
    </operation>

    </table>
