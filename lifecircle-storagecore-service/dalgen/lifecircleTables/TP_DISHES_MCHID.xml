<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_DISHES_MCHID" physicalName="TP_DISHES_MCHID"
    remark="TP_DISHES_MCHID">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_DISHES_MCHID">
INSERT INTO TP_DISHES_MCHID
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="appid != null">`APPID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="errorMsg != null">`ERROR_MSG`,</if>
        <if test="step != null">`STEP`,</if>
        <if test="isFinish != null">`IS_FINISH`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
        <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
        <if test="step != null">#{step,jdbcType=TINYINT},</if>
        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getByMchId" resulttype="java.lang.Integer" remark="根据子商户号查询商户id">
        SELECT
        user.uid
        FROM
        tp_dishes_mchid mchid
        LEFT JOIN tp_wxuser user ON mchid.token = user.token
        WHERE
        mchid.mchid = #{mchId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
