<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_WECHAT_DIRECT_CONFIG" physicalName="TP_WECHAT_DIRECT_CONFIG"
       remark="微信直连商户配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_WECHAT_DIRECT_CONFIG">
        INSERT INTO TP_WECHAT_DIRECT_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="handler != null">`HANDLER`,</if>
            <if test="subName != null">`SUB_NAME`,</if>
            <if test="subAppid != null">`SUB_APPID`,</if>
            <if test="subMchId != null">`SUB_MCH_ID`,</if>
            <if test="appsecret != null">`APPSECRET`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="handler != null">#{handler,jdbcType=VARCHAR},</if>
            <if test="subName != null">#{subName,jdbcType=VARCHAR},</if>
            <if test="subAppid != null">#{subAppid,jdbcType=VARCHAR},</if>
            <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
            <if test="appsecret != null">#{appsecret,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getWechatByMchId" multiplicity="many" remark="根据子商户id查询商户">
        SELECT *
        FROM TP_WECHAT_DIRECT_CONFIG
        WHERE
        sub_mch_id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and type = 2
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="insert:TP_WECHAT_DIRECT_CONFIG 批量插入">
        INSERT INTO TP_WECHAT_DIRECT_CONFIG(
        HANDLER
        ,SUB_MCH_ID
        ,UID
        ,TYPE
        ,STATUS
        ,CHANNEL_ID
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.handler,jdbcType=VARCHAR}
            , #{item.subMchId,jdbcType=VARCHAR}
            , #{item.uid,jdbcType=INTEGER}
            , #{item.type,jdbcType=TINYINT}
            , #{item.status,jdbcType=TINYINT}
            , #{item.channelId,jdbcType=INTEGER}
            )
        </foreach>
    </operation>
</table>
