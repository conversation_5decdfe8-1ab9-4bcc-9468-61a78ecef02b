<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD" physicalName="TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD"
       remark="商户批量迁移文件上传表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD">
        INSERT INTO TP_CRM_MERCHANT_TRANSFER_BATCH_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
