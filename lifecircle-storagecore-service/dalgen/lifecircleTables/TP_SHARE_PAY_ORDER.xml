<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_PAY_ORDER" physicalName="TP_SHARE_PAY_ORDER"
       remark="分账订单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_PAY_ORDER">
        INSERT INTO TP_SHARE_PAY_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="taskId != null">`TASK_ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="tradeNo != null">`TRADE_NO`,</if>
            <if test="shareDate != null">`SHARE_DATE`,</if>
            <if test="shareReqNo != null">`SHARE_REQ_NO`,</if>
            <if test="shareSource != null">`SHARE_SOURCE`,</if>
            <if test="merchantReqNo != null">`MERCHANT_REQ_NO`,</if>
            <if test="platformReqNo != null">`PLATFORM_REQ_NO`,</if>
            <if test="orderShareType != null">`ORDER_SHARE_TYPE`,</if>
            <if test="shareOrderStatus != null">`SHARE_ORDER_STATUS`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="platformShareTime != null">`PLATFORM_SHARE_TIME`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="allSharePrice != null">`ALL_SHARE_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="taskId != null">#{taskId,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="tradeNo != null">#{tradeNo,jdbcType=VARCHAR},</if>
            <if test="shareDate != null">#{shareDate,jdbcType=VARCHAR},</if>
            <if test="shareReqNo != null">#{shareReqNo,jdbcType=VARCHAR},</if>
            <if test="shareSource != null">#{shareSource,jdbcType=VARCHAR},</if>
            <if test="merchantReqNo != null">#{merchantReqNo,jdbcType=VARCHAR},</if>
            <if test="platformReqNo != null">#{platformReqNo,jdbcType=VARCHAR},</if>
            <if test="orderShareType != null">#{orderShareType,jdbcType=VARCHAR},</if>
            <if test="shareOrderStatus != null">#{shareOrderStatus,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="platformShareTime != null">#{platformShareTime,jdbcType=TIMESTAMP},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="allSharePrice != null">#{allSharePrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
    <operation name="updateFeeAndallSharePriceByPlatformReqNoAndBankType" remark="通过platform_req_no,bank_type更新手续费">
        update TP_SHARE_PAY_ORDER
        set fee = #{fee,jdbcType=DECIMAL},
        ALL_SHARE_PRICE = #{allSharePrice,jdbcType=DECIMAL}
        where
        platform_req_no = #{platformReqNo,jdbcType=VARCHAR}
        and
        bank_type = #{bankType,jdbcType=TINYINT}
    </operation>
    <operation name="updateFeeAndAllSharePriceByShareReqNo" remark="通过share_req_no来更新订单的手续费和分账金额">
        update TP_SHARE_PAY_ORDER
        set fee = #{fee,jdbcType=DECIMAL},
        ALL_SHARE_PRICE = #{allSharePrice,jdbcType=DECIMAL}
        where
        SHARE_REQ_NO = #{shareReqNo,jdbcType=VARCHAR}
    </operation>
</table>
