<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LESHUA_COUNTEROFFER_FILE" physicalName="TP_LESHUA_COUNTEROFFER_FILE"
    remark="乐刷回盘文件">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LESHUA_COUNTEROFFER_FILE">
    <selectKey keyProperty="id" resultType="int" order="AFTER">
        SELECT LAST_INSERT_ID() as id
    </selectKey>
    INSERT INTO TP_LESHUA_COUNTEROFFER_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="tradeMoney != null">`TRADE_MONEY`,</if>
        <if test="fileName != null">`FILE_NAME`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="tradeNum != null">`TRADE_NUM`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="dealStatus != null">`DEAL_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="platformOrgId != null">`platform_org_id`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="tradeMoney != null">#{tradeMoney,jdbcType=DECIMAL},</if>
        <if test="fileName != null">#{fileName,jdbcType=VARCHAR},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="tradeNum != null">#{tradeNum,jdbcType=INTEGER},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="dealStatus != null">#{dealStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR}</if>
    </trim>
    </operation>

    <operation name="getByTradeDateAndChannelAndPlatformOrgId" multiplicity="one" remark="根据交易时间和通道查询回盘文件记录">
        SELECT
        id,file_name,deal_status
        FROM
        TP_LESHUA_COUNTEROFFER_FILE
        WHERE
        trade_date = #{tradeDate,jdbcType=INTEGER}
        AND channel = #{channel,jdbcType=TINYINT}
        and platform_org_id = #{platformOrgId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="updateTradeMoneyById" remark="根据id更新回盘文件记录总金额和总记录数">
        UPDATE
            TP_LESHUA_COUNTEROFFER_FILE
        SET
            trade_money = #{tradeMoney,jdbcType=DECIMAL},
            trade_num = #{tradeNum,jdbcType=INTEGER},
            file_name = #{fileName,jdbcType=VARCHAR}
        WHERE
            id = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="updateDealStatusById" remark="根据id更新回盘文件记录的处理状态">
        UPDATE
        TP_LESHUA_COUNTEROFFER_FILE
        SET
        deal_status = #{dealStatus,jdbcType=TINYINT}
        WHERE
        id = #{id,jdbcType=INTEGER}
    </operation>

    <operation name="updateTradeMoneyAndDealStatusById" remark="通过id更新trade_moeny,trade_num和deal_status">
        UPDATE
        TP_LESHUA_COUNTEROFFER_FILE
        SET
        trade_money = #{tradeMoney,jdbcType=DECIMAL},
        trade_num = #{tradeNum,jdbcType=INTEGER},
        deal_status = #{dealStatus,jdbcType=TINYINT}
        WHERE
        id = #{id,jdbcType=INTEGER}
    </operation>
</table>
