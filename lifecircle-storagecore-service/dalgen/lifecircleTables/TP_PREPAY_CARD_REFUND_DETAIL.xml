<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_REFUND_DETAIL" physicalName="TP_PREPAY_CARD_REFUND_DETAIL"
       remark="退货明细单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_REFUND_DETAIL">
        INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="refundNo != null">`REFUND_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入记录">
        INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            refund_no,
            sales_order_no,
            card_spu_id,
            card_spu_name,
            card_sku_id,
            card_amount,
            card_price,
            refund_number,
            org_id,
            publish_org_id
        </trim>
        VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            <trim prefix="(" suffix=")" suffixOverrides=",">
                #{item.refundNo,jdbcType=VARCHAR},
                #{item.salesOrderNo,jdbcType=VARCHAR},
                #{item.cardSpuId,jdbcType=VARCHAR},
                #{item.cardSpuName,jdbcType=VARCHAR},
                #{item.cardSkuId,jdbcType=VARCHAR},
                #{item.cardAmount,jdbcType=DECIMAL},
                #{item.cardPrice,jdbcType=DECIMAL},
                #{item.refundNumber,jdbcType=INTEGER},
                #{item.orgId,jdbcType=VARCHAR},
                #{item.publishOrgId,jdbcType=VARCHAR},
            </trim>
        </foreach>
    </operation>
</table>
