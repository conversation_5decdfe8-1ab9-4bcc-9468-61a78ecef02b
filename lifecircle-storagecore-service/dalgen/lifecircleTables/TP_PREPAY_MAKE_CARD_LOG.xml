<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_MAKE_CARD_LOG" physicalName="TP_PREPAY_MAKE_CARD_LOG"
       remark="预付卡制卡记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_MAKE_CARD_LOG">
        INSERT INTO TP_PREPAY_MAKE_CARD_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="batchNo != null">`BATCH_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="makeNumber != null">`MAKE_NUMBER`,</if>
            <if test="makeStatus != null">`MAKE_STATUS`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="batchNo != null">#{batchNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="makeNumber != null">#{makeNumber,jdbcType=INTEGER},</if>
            <if test="makeStatus != null">#{makeStatus,jdbcType=TINYINT},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertMakeCardBatch" paramtype="objectList" remark="批量插入制卡详情">
        INSERT INTO
        tp_prepay_make_card_log (
        batch_no,
        card_spu_id,
        card_spu_name,
        card_sku_id,
        publish_org_id,
        make_number,
        operate_id,
        operate_name,
        submit_time,
        make_status
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.batchNo,jdbcType=VARCHAR},
            #{item.cardSpuId,jdbcType=VARCHAR},
            #{item.cardSpuName,jdbcType=VARCHAR},
            #{item.cardSkuId,jdbcType=VARCHAR},
            #{item.publishOrgId,jdbcType=VARCHAR},
            #{item.makeNumber,jdbcType=INTEGER},
            #{item.operateId,jdbcType=VARCHAR},
            #{item.operateName,jdbcType=VARCHAR},
            #{item.submitTime,jdbcType=INTEGER},
            #{item.makeStatus,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <operation name="updateMakeCardByBatchNo" remark="根据制卡批次号修改制卡状态">
        update
        tp_prepay_make_card_log
        set
        make_status = #{makeStatus,jdbcType=TINYINT}
        <if test="finishTime !=null ">
            ,finish_time = #{finishTime,jdbcType=INTEGER}
        </if>
        where
        is_del = 0
        and batch_no = #{batchNo,jdbcType=VARCHAR}
    </operation>
</table>
