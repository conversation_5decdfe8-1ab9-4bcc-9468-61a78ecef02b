<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_APP_DEVICE_VERSION" physicalName="TP_APP_DEVICE_VERSION"
       remark="设备当前按照APP版本">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_APP_DEVICE_VERSION">
        INSERT INTO TP_APP_DEVICE_VERSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="deviceSn != null">`DEVICE_SN`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="pubVersionId != null">`PUB_VERSION_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="deviceSn != null">#{deviceSn,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="pubVersionId != null">#{pubVersionId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <resultmap name="DeviceVersionResultMap" type="DeviceVersionResult">
        <column name="device_sn" javatype="String" jdbctype="VARCHAR" remark="设备编码"/>
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="商户id"/>
        <column name="username" javatype="String" jdbctype="VARCHAR" remark="商户名称"/>
        <column name="platform" javatype="Integer" jdbctype="INTEGER" remark="平台"/>
        <column name="platform_name" javatype="String" jdbctype="VARCHAR" remark="平台名称"/>
    </resultmap>

    <operation name="findByVersionId" paramtype="primitive" multiplicity="many" resultmap="DeviceVersionResultMap"
               remark="根据version_id 分页查询列表">
        select a.device_sn deviceSn,u.id uid,u.username username,a.platform platform
        from tp_app_device_version a
        left join tp_equipment_sn e on a.device_sn = e.init_sn
        left join tp_users as u on e.uid = u.id
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
        limit #{pageStartNum,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </operation>


    <operation name="findByVersionIdNew" paramtype="primitive" multiplicity="many" resultmap="DeviceVersionResultMap"
               remark="根据version_id 分页查询列表">
        select
        a.device_sn deviceSn,
        a.platform platform
        from tp_app_device_version a
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
        limit #{pageStartNum,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </operation>


    <operation name="countByVersionId" resulttype="Integer" multiplicity="one" remark="根据version_id 统计数量列表">
        SELECT
        count(*)
        from tp_app_device_version a
        where a.pub_version_id = #{versionId,jdbcType=INTEGER}
    </operation>
</table>
