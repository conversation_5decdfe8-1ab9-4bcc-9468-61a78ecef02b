<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_WECHAT_CHANNEL" physicalName="TP_WECHAT_CHANNEL"
       remark="微信服务商表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_WECHAT_CHANNEL">
        INSERT INTO TP_WECHAT_CHANNEL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="mchid != null">`MCHID`,</if>
            <if test="serialNo != null">`SERIAL_NO`,</if>
            <if test="privateKey != null">`PRIVATE_KEY`,</if>
            <if test="channelName != null">`CHANNEL_NAME`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="privateKey != null">#{privateKey,jdbcType=VARCHAR},</if>
            <if test="channelName != null">#{channelName,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getChanelIdByName" multiplicity="many" remark="根据服务商名称获取id">
        select
        *
        from
        TP_WECHAT_CHANNEL
        where
        channel_name in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=VARCHAR}
        </foreach>
        and
        type = 2
        and
        status = 1
    </operation>
</table>
