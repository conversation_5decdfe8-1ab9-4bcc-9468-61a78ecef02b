<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_OPERATE_LOG" physicalName="TP_PREPAY_CARD_OPERATE_LOG"
       remark="预付卡操作日志表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_OPERATE_LOG">
        INSERT INTO TP_PREPAY_CARD_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="afterParam != null">`AFTER_PARAM`,</if>
            <if test="beforeParam != null">`BEFORE_PARAM`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="action != null">`ACTION`,</if>
            <if test="modular != null">`MODULAR`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="afterParam != null">#{afterParam,jdbcType=VARCHAR},</if>
            <if test="beforeParam != null">#{beforeParam,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="action != null">#{action,jdbcType=TINYINT},</if>
            <if test="modular != null">#{modular,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
</table>
