<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ALIPAY_DIRECT_PAY_APPLY" physicalName="TP_ALIPAY_DIRECT_PAY_APPLY"
       remark="支付宝直连进件申请单">

    <operation name="findAlipayDirectPayApplyList" multiplicity="many" remark="查询支付宝N7直连申请单信息">
        SELECT *
        FROM
            TP_ALIPAY_DIRECT_PAY_APPLY
        WHERE is_del = 0
        <if test="merchantId != null and merchantId != 0">
            AND merchant_id = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="username!=null and username!='' ">
            and username=#{username,jdbcType=VARCHAR}
        </if>
        <if test="alipayAccount !=null and alipayAccount!='' ">
            and alipay_account=#{alipayAccount,jdbcType=VARCHAR}
        </if>
        <if test="applyTimeStart!=null">
            AND apply_time <![CDATA[ >= ]]> #{applyTimeStart,jdbcType=TIMESTAMP}
        </if>
        <if test="applyTimeEnd !=null">
            AND apply_time <![CDATA[ < ]]> #{applyTimeEnd,jdbcType=TIMESTAMP}
        </if>
        <if test="checkStatus !=null and checkStatus != -1 ">
            and check_status=#{checkStatus,jdbcType=INTEGER}
        </if>
        order by apply_time desc
    </operation>
</table>
