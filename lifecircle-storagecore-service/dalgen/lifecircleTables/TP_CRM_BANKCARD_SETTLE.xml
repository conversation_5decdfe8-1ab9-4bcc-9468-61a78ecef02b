<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CRM_BANKCARD_SETTLE" physicalName="TP_CRM_BANKCARD_SETTLE"
       remark="银行卡进件表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CRM_BANKCARD_SETTLE">
        INSERT INTO TP_CRM_BANKCARD_SETTLE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="createBy != null">`CREATE_BY`,</if>
            <if test="termList != null">`TERM_LIST`,</if>
            <if test="updateBy != null">`UPDATE_BY`,</if>
            <if test="allinpayMchId != null">`ALLINPAY_MCH_ID`,</if>
            <if test="bankCardSettleId != null">`BANK_CARD_SETTLE_ID`,</if>
            <if test="allinpaySettleStatus != null">`ALLINPAY_SETTLE_STATUS`,</if>
            <if test="allinpaySettleFailMsg != null">`ALLINPAY_SETTLE_FAIL_MSG`,</if>
            <if test="allinpayProtocolStatus != null">`ALLINPAY_PROTOCOL_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="settleStatus != null">`SETTLE_STATUS`,</if>
            <if test="protocolStatus != null">`PROTOCOL_STATUS`,</if>
            <if test="allinpaySettleCallbackStatus != null">`ALLINPAY_SETTLE_CALLBACK_STATUS`,</if>
            <if test="allinpayProtocolCallbackStatus != null">`ALLINPAY_PROTOCOL_CALLBACK_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="settleTime != null">`SETTLE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="protocolTime != null">`PROTOCOL_TIME`,</if>
            <if test="settleStatusUpdateTime != null">`SETTLE_STATUS_UPDATE_TIME`,</if>
            <if test="allinpaySettleCallbackTime != null">`ALLINPAY_SETTLE_CALLBACK_TIME`,</if>
            <if test="allinpayProtocolCallbackTime != null">`ALLINPAY_PROTOCOL_CALLBACK_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="createBy != null">#{createBy,jdbcType=VARCHAR},</if>
            <if test="termList != null">#{termList,jdbcType=VARCHAR},</if>
            <if test="updateBy != null">#{updateBy,jdbcType=VARCHAR},</if>
            <if test="allinpayMchId != null">#{allinpayMchId,jdbcType=VARCHAR},</if>
            <if test="bankCardSettleId != null">#{bankCardSettleId,jdbcType=VARCHAR},</if>
            <if test="allinpaySettleStatus != null">#{allinpaySettleStatus,jdbcType=VARCHAR},</if>
            <if test="allinpaySettleFailMsg != null">#{allinpaySettleFailMsg,jdbcType=VARCHAR},</if>
            <if test="allinpayProtocolStatus != null">#{allinpayProtocolStatus,jdbcType=VARCHAR},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="settleStatus != null">#{settleStatus,jdbcType=TINYINT},</if>
            <if test="protocolStatus != null">#{protocolStatus,jdbcType=TINYINT},</if>
            <if test="allinpaySettleCallbackStatus != null">#{allinpaySettleCallbackStatus,jdbcType=TINYINT},</if>
            <if test="allinpayProtocolCallbackStatus != null">#{allinpayProtocolCallbackStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="settleTime != null">#{settleTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="protocolTime != null">#{protocolTime,jdbcType=TIMESTAMP},</if>
            <if test="settleStatusUpdateTime != null">#{settleStatusUpdateTime,jdbcType=TIMESTAMP},</if>
            <if test="allinpaySettleCallbackTime != null">#{allinpaySettleCallbackTime,jdbcType=TIMESTAMP},</if>
            <if test="allinpayProtocolCallbackTime != null">#{allinpayProtocolCallbackTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByAllinpay_mch_id" paramtype="primitive" resulttype="Integer" remark="根据通联商户号查询商户id">
        select merchant_id from tp_crm_bankcard_settle where allinpay_mch_id = #{allinpayMchId,jdbcType=VARCHAR} limit 1
    </operation>
</table>
