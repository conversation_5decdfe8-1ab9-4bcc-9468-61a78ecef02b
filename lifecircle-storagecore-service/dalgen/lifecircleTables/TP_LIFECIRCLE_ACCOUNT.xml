<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_ACCOUNT" physicalName="TP_LIFECIRCLE_ACCOUNT"
       remark="商户账户表 记录商户实名信息">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_ACCOUNT">
        INSERT INTO TP_LIFECIRCLE_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="balance != null">`BALANCE`,</if>
            <if test="imbalance != null">`IMBALANCE`,</if>
            <if test="prevAmount != null">`PREV_AMOUNT`,</if>
            <if test="todayIncome != null">`TODAY_INCOME`,</if>
            <if test="totalAmount != null">`TOTAL_AMOUNT`,</if>
            <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
            <if test="commissionRate != null">`COMMISSION_RATE`,</if>
            <if test="availableBalance != null">`AVAILABLE_BALANCE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="idcard != null">`IDCARD`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="cardBack != null">`CARD_BACK`,</if>
            <if test="cardTime != null">`CARD_TIME`,</if>
            <if test="discribe != null">`DISCRIBE`,</if>
            <if test="cardPlace != null">`CARD_PLACE`,</if>
            <if test="licenseid != null">`LICENSEID`,</if>
            <if test="categoryId != null">`CATEGORY_ID`,</if>
            <if test="custAcctId != null">`CUST_ACCT_ID`,</if>
            <if test="accountName != null">`ACCOUNT_NAME`,</if>
            <if test="idcardphoto != null">`IDCARDPHOTO`,</if>
            <if test="identityImg != null">`IDENTITY_IMG`,</if>
            <if test="legalPerson != null">`LEGAL_PERSON`,</if>
            <if test="licensename != null">`LICENSENAME`,</if>
            <if test="licensephoto != null">`LICENSEPHOTO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="errCount != null">`ERR_COUNT`,</if>
            <if test="isSubmit != null">`IS_SUBMIT`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="handleTime != null">`HANDLE_TIME`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="examineType != null">`EXAMINE_TYPE`,</if>
            <if test="joinChannel != null">`JOIN_CHANNEL`,</if>
            <if test="custAcctTime != null">`CUST_ACCT_TIME`,</if>
            <if test="licenseround != null">`LICENSEROUND`,</if>
            <if test="verifyStatus != null">`VERIFY_STATUS`,</if>
            <if test="todayIncomeUpdate != null">`TODAY_INCOME_UPDATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
            <if test="imbalance != null">#{imbalance,jdbcType=DECIMAL},</if>
            <if test="prevAmount != null">#{prevAmount,jdbcType=DECIMAL},</if>
            <if test="todayIncome != null">#{todayIncome,jdbcType=DECIMAL},</if>
            <if test="totalAmount != null">#{totalAmount,jdbcType=DECIMAL},</if>
            <if test="frozenAmount != null">#{frozenAmount,jdbcType=DECIMAL},</if>
            <if test="commissionRate != null">#{commissionRate,jdbcType=DECIMAL},</if>
            <if test="availableBalance != null">#{availableBalance,jdbcType=DECIMAL},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="idcard != null">#{idcard,jdbcType=CHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="cardBack != null">#{cardBack,jdbcType=VARCHAR},</if>
            <if test="cardTime != null">#{cardTime,jdbcType=VARCHAR},</if>
            <if test="discribe != null">#{discribe,jdbcType=VARCHAR},</if>
            <if test="cardPlace != null">#{cardPlace,jdbcType=VARCHAR},</if>
            <if test="licenseid != null">#{licenseid,jdbcType=VARCHAR},</if>
            <if test="categoryId != null">#{categoryId,jdbcType=CHAR},</if>
            <if test="custAcctId != null">#{custAcctId,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="idcardphoto != null">#{idcardphoto,jdbcType=VARCHAR},</if>
            <if test="identityImg != null">#{identityImg,jdbcType=VARCHAR},</if>
            <if test="legalPerson != null">#{legalPerson,jdbcType=VARCHAR},</if>
            <if test="licensename != null">#{licensename,jdbcType=VARCHAR},</if>
            <if test="licensephoto != null">#{licensephoto,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=INTEGER},</if>
            <if test="errCount != null">#{errCount,jdbcType=INTEGER},</if>
            <if test="isSubmit != null">#{isSubmit,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="handleTime != null">#{handleTime,jdbcType=INTEGER},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="examineType != null">#{examineType,jdbcType=TINYINT},</if>
            <if test="joinChannel != null">#{joinChannel,jdbcType=TINYINT},</if>
            <if test="custAcctTime != null">#{custAcctTime,jdbcType=INTEGER},</if>
            <if test="licenseround != null">#{licenseround,jdbcType=SMALLINT},</if>
            <if test="verifyStatus != null">#{verifyStatus,jdbcType=TINYINT},</if>
            <if test="todayIncomeUpdate != null">#{todayIncomeUpdate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="batchFindTokenByUidList" resulttype="String" multiplicity="many" remark="根据 uid 列表获得 token 列表">
        SELECT
        token
        FROM
        tp_lifecircle_account
        <where>
            uid IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </where>
    </operation>

    <operation name="updateBalanceUpdateTimeByToken" remark="根据商户token更新商户的总余额">
        UPDATE TP_LIFECIRCLE_ACCOUNT SET balance = balance - #{tradeMoney,jdbcType=DECIMAL}, update_time = #{updateTime,jdbcType=INTEGER} WHERE token = #{token,jdbcType=VARCHAR}
    </operation>

    <operation name="getByTokenForUpdate" multiplicity="one" remark="根据用户token 查询 ACCOUNT 信息">
        SELECT * FROM TP_LIFECIRCLE_ACCOUNT WHERE TOKEN = #{token,jdbcType=VARCHAR} FOR UPDATE
    </operation>

    <operation name="getByUid" multiplicity="one" remark="根据用户 ID 查询 ACCOUNT 信息">
        SELECT
        *
        FROM TP_LIFECIRCLE_ACCOUNT
        WHERE
        UID = #{uid,jdbcType=INTEGER}
    </operation>


</table>
