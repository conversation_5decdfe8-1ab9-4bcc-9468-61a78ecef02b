<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USERS" physicalName="TP_USERS"
       remark="商户信息表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USERS">
        INSERT INTO TP_USERS
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="finance != null">`FINANCE`,</if>
            <if test="indirect != null">`INDIRECT`,</if>
            <if test="salespercent != null">`SALESPERCENT`,</if>
            <if test="distributorSalespercent != null">`DISTRIBUTOR_SALESPERCENT`,</if>
            <if test="mp != null">`MP`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="lastip != null">`LASTIP`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="people != null">`PEOPLE`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="contact != null">`CONTACT`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="bankUser != null">`BANK_USER`,</if>
            <if test="createip != null">`CREATEIP`,</if>
            <if test="lasttime != null">`LASTTIME`,</if>
            <if test="password != null">`PASSWORD`,</if>
            <if test="plugsave != null">`PLUGSAVE`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="alipaynum != null">`ALIPAYNUM`,</if>
            <if test="bankAcount != null">`BANK_ACOUNT`,</if>
            <if test="industryId != null">`INDUSTRY_ID`,</if>
            <if test="usersToken != null">`USERS_TOKEN`,</if>
            <if test="companyaddress != null">`COMPANYADDRESS`,</if>
            <if test="usersHeaderpic != null">`USERS_HEADERPIC`,</if>
            <if test="protocolVersion != null">`PROTOCOL_VERSION`,</if>
            <if test="storeDefaultLogo != null">`STORE_DEFAULT_LOGO`,</if>
            <if test="gid != null">`GID`,</if>
            <if test="focus != null">`FOCUS`,</if>
            <if test="money != null">`MONEY`,</if>
            <if test="amount != null">`AMOUNT`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="diynum != null">`DIYNUM`,</if>
            <if test="ispush != null">`ISPUSH`,</if>
            <if test="roleId != null">`ROLE_ID`,</if>
            <if test="vendor != null">`VENDOR`,</if>
            <if test="apiUser != null">`API_USER`,</if>
            <if test="cardNum != null">`CARD_NUM`,</if>
            <if test="isApply != null">`IS_APPLY`,</if>
            <if test="isJdpay != null">`IS_JDPAY`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="viptime != null">`VIPTIME`,</if>
            <if test="isAlipay != null">`IS_ALIPAY`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="payLimit != null">`PAY_LIMIT`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="salesman != null">`SALESMAN`,</if>
            <if test="wxStatus != null">`WX_STATUS`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="isConfirm != null">`IS_CONFIRM`,</if>
            <if test="versionId != null">`VERSION_ID`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="connectnum != null">`CONNECTNUM`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="dealamount != null">`DEALAMOUNT`,</if>
            <if test="isGroupBuy != null">`IS_GROUP_BUY`,</if>
            <if test="isOpenMina != null">`IS_OPEN_MINA`,</if>
            <if test="isProtocol != null">`IS_PROTOCOL`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="onlinetime != null">`ONLINETIME`,</if>
            <if test="protocolId != null">`PROTOCOL_ID`,</if>
            <if test="salerAudit != null">`SALER_AUDIT`,</if>
            <if test="voiceOnOff != null">`VOICE_ON_OFF`,</if>
            <if test="activitynum != null">`ACTIVITYNUM`,</if>
            <if test="cashoutLock != null">`CASHOUT_LOCK`,</if>
            <if test="confirmTime != null">`CONFIRM_TIME`,</if>
            <if test="isQuickCash != null">`IS_QUICK_CASH`,</if>
            <if test="mcardStatus != null">`MCARD_STATUS`,</if>
            <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
            <if test="totalsmsnum != null">`TOTALSMSNUM`,</if>
            <if test="incomeStatus != null">`INCOME_STATUS`,</if>
            <if test="protocolTime != null">`PROTOCOL_TIME`,</if>
            <if test="psModifyTime != null">`PS_MODIFY_TIME`,</if>
            <if test="totalsmsused != null">`TOTALSMSUSED`,</if>
            <if test="transferTime != null">`TRANSFER_TIME`,</if>
            <if test="isScanService != null">`IS_SCAN_SERVICE`,</if>
            <if test="isemscnplpush != null">`ISEMSCNPLPUSH`,</if>
            <if test="rechargeLimit != null">`RECHARGE_LIMIT`,</if>
            <if test="wechatCardNum != null">`WECHAT_CARD_NUM`,</if>
            <if test="attachmentsize != null">`ATTACHMENTSIZE`,</if>
            <if test="autoWithdrawal != null">`AUTO_WITHDRAWAL`,</if>
            <if test="lastloginmonth != null">`LASTLOGINMONTH`,</if>
            <if test="lifecircleTime != null">`LIFECIRCLE_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="cardCreateStatus != null">`CARD_CREATE_STATUS`,</if>
            <if test="latestonlinetime != null">`LATESTONLINETIME`,</if>
            <if test="isServicenoAccess != null">`IS_SERVICENO_ACCESS`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="finance != null">#{finance,jdbcType=DECIMAL},</if>
            <if test="indirect != null">#{indirect,jdbcType=DOUBLE},</if>
            <if test="salespercent != null">#{salespercent,jdbcType=DOUBLE},</if>
            <if test="distributorSalespercent != null">#{distributorSalespercent,jdbcType=DOUBLE},</if>
            <if test="mp != null">#{mp,jdbcType=VARCHAR},</if>
            <if test="qq != null">#{qq,jdbcType=VARCHAR},</if>
            <if test="area != null">#{area,jdbcType=CHAR},</if>
            <if test="city != null">#{city,jdbcType=CHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="lastip != null">#{lastip,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=CHAR},</if>
            <if test="people != null">#{people,jdbcType=VARCHAR},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="contact != null">#{contact,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="bankUser != null">#{bankUser,jdbcType=VARCHAR},</if>
            <if test="createip != null">#{createip,jdbcType=VARCHAR},</if>
            <if test="lasttime != null">#{lasttime,jdbcType=VARCHAR},</if>
            <if test="password != null">#{password,jdbcType=VARCHAR},</if>
            <if test="plugsave != null">#{plugsave,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=CHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="alipaynum != null">#{alipaynum,jdbcType=VARCHAR},</if>
            <if test="bankAcount != null">#{bankAcount,jdbcType=VARCHAR},</if>
            <if test="industryId != null">#{industryId,jdbcType=VARCHAR},</if>
            <if test="usersToken != null">#{usersToken,jdbcType=VARCHAR},</if>
            <if test="companyaddress != null">#{companyaddress,jdbcType=VARCHAR},</if>
            <if test="usersHeaderpic != null">#{usersHeaderpic,jdbcType=CHAR},</if>
            <if test="protocolVersion != null">#{protocolVersion,jdbcType=VARCHAR},</if>
            <if test="storeDefaultLogo != null">#{storeDefaultLogo,jdbcType=VARCHAR},</if>
            <if test="gid != null">#{gid,jdbcType=INTEGER},</if>
            <if test="focus != null">#{focus,jdbcType=TINYINT},</if>
            <if test="money != null">#{money,jdbcType=INTEGER},</if>
            <if test="amount != null">#{amount,jdbcType=INTEGER},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="diynum != null">#{diynum,jdbcType=INTEGER},</if>
            <if test="ispush != null">#{ispush,jdbcType=INTEGER},</if>
            <if test="roleId != null">#{roleId,jdbcType=INTEGER},</if>
            <if test="vendor != null">#{vendor,jdbcType=INTEGER},</if>
            <if test="apiUser != null">#{apiUser,jdbcType=TINYINT},</if>
            <if test="cardNum != null">#{cardNum,jdbcType=INTEGER},</if>
            <if test="isApply != null">#{isApply,jdbcType=TINYINT},</if>
            <if test="isJdpay != null">#{isJdpay,jdbcType=TINYINT},</if>
            <if test="unionId != null">#{unionId,jdbcType=INTEGER},</if>
            <if test="viptime != null">#{viptime,jdbcType=INTEGER},</if>
            <if test="isAlipay != null">#{isAlipay,jdbcType=TINYINT},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="payLimit != null">#{payLimit,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="salesman != null">#{salesman,jdbcType=INTEGER},</if>
            <if test="wxStatus != null">#{wxStatus,jdbcType=TINYINT},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="isConfirm != null">#{isConfirm,jdbcType=TINYINT},</if>
            <if test="versionId != null">#{versionId,jdbcType=TINYINT},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="connectnum != null">#{connectnum,jdbcType=INTEGER},</if>
            <if test="createtime != null">#{createtime,jdbcType=INTEGER},</if>
            <if test="dealamount != null">#{dealamount,jdbcType=INTEGER},</if>
            <if test="isGroupBuy != null">#{isGroupBuy,jdbcType=TINYINT},</if>
            <if test="isOpenMina != null">#{isOpenMina,jdbcType=TINYINT},</if>
            <if test="isProtocol != null">#{isProtocol,jdbcType=INTEGER},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=TINYINT},</if>
            <if test="onlinetime != null">#{onlinetime,jdbcType=INTEGER},</if>
            <if test="protocolId != null">#{protocolId,jdbcType=INTEGER},</if>
            <if test="salerAudit != null">#{salerAudit,jdbcType=TINYINT},</if>
            <if test="voiceOnOff != null">#{voiceOnOff,jdbcType=TINYINT},</if>
            <if test="activitynum != null">#{activitynum,jdbcType=INTEGER},</if>
            <if test="cashoutLock != null">#{cashoutLock,jdbcType=TINYINT},</if>
            <if test="confirmTime != null">#{confirmTime,jdbcType=INTEGER},</if>
            <if test="isQuickCash != null">#{isQuickCash,jdbcType=TINYINT},</if>
            <if test="mcardStatus != null">#{mcardStatus,jdbcType=TINYINT},</if>
            <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
            <if test="totalsmsnum != null">#{totalsmsnum,jdbcType=INTEGER},</if>
            <if test="incomeStatus != null">#{incomeStatus,jdbcType=TINYINT},</if>
            <if test="protocolTime != null">#{protocolTime,jdbcType=INTEGER},</if>
            <if test="psModifyTime != null">#{psModifyTime,jdbcType=INTEGER},</if>
            <if test="totalsmsused != null">#{totalsmsused,jdbcType=INTEGER},</if>
            <if test="transferTime != null">#{transferTime,jdbcType=INTEGER},</if>
            <if test="isScanService != null">#{isScanService,jdbcType=TINYINT},</if>
            <if test="isemscnplpush != null">#{isemscnplpush,jdbcType=TINYINT},</if>
            <if test="rechargeLimit != null">#{rechargeLimit,jdbcType=INTEGER},</if>
            <if test="wechatCardNum != null">#{wechatCardNum,jdbcType=INTEGER},</if>
            <if test="attachmentsize != null">#{attachmentsize,jdbcType=INTEGER},</if>
            <if test="autoWithdrawal != null">#{autoWithdrawal,jdbcType=TINYINT},</if>
            <if test="lastloginmonth != null">#{lastloginmonth,jdbcType=SMALLINT},</if>
            <if test="lifecircleTime != null">#{lifecircleTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="cardCreateStatus != null">#{cardCreateStatus,jdbcType=TINYINT},</if>
            <if test="latestonlinetime != null">#{latestonlinetime,jdbcType=INTEGER},</if>
            <if test="isServicenoAccess != null">#{isServicenoAccess,jdbcType=TINYINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <resultmap name="UidAndTokenResultMap" type="UidAndTokenResultMap">
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="用户 ID"/>
        <column name="token" javatype="String" jdbctype="VARCHAR" remark="token"/>
    </resultmap>

    <resultmap name="usersExtraBaseDOResultMap"
               type="UsersExtraBaseDO">
        <column name="uid" jdbctype="INTEGER" javatype="Integer" remark="用户ID"/>
        <column name="username" jdbctype="VARCHAR" javatype="String" remark="用户名"/>
        <column name="merchant_type" jdbctype="INTEGER" javatype="Integer" remark="商户类型"/>
        <column name="income_status" jdbctype="INTEGER" javatype="Integer" remark="进件状态"/>
    </resultmap>

    <operation name="batchFindUidListByTokenList" multiplicity="many" resultmap="UidAndTokenResultMap"
               remark="根据token 列表获得 uid 列表">
        SELECT
        users.id uid,
        wx.token token
        FROM
        tp_users users JOIN tp_wxuser wx ON users.id = wx.uid
        <where>
            wx.token IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </operation>

    <resultmap name="usersBaseInfoResultMap" type="UsersBaseInfoResultMap">
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="商户id"/>
        <column name="username" javatype="String" jdbctype="VARCHAR" remark="商户账号"/>
        <column name="company" javatype="String" jdbctype="VARCHAR" remark="公司名称"/>
        <column name="address" javatype="String" jdbctype="VARCHAR" remark="商户地址"/>
    </resultmap>
    <operation name="batchFindUsersByUidList" multiplicity="many" resultmap="usersBaseInfoResultMap" remark="根据商户id列表获取商户基本信息列表">
        SELECT
            id uid,
            username,
            company,
            address
        FROM tp_users
        <where>
            id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </where>
    </operation>

    <operation remark="通过用户名获取用户id" name="getIdByUserName" resulttype="java.lang.Integer" multiplicity="one">
        SELECT id
        FROM TP_USERS
        WHERE username = #{userName,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation remark="通过商户id列表获取商户名称" name="findUserNamesAndBelongByIds" multiplicity="many">
        SELECT id,USERNAME,BELONG,SALESMAN,USERS_TOKEN
        FROM TP_USERS
        WHERE id IN
        <foreach collection="list" item="merchantId" open="(" close=")" separator=",">
            #{merchantId,jdbcType=INTEGER}
        </foreach>
    </operation>


    <resultmap name="merchantAccountMap" type="MerchantAccountMap">
        <column name="bank_no" javatype="String" jdbctype="VARCHAR" remark="超级网银号"/>
        <column name="acct_id" javatype="String" jdbctype="VARCHAR" remark="账号"/>
        <column name="mobile" javatype="String" jdbctype="VARCHAR" remark="手机号"/>
        <column name="bind_type" javatype="Integer" jdbctype="INTEGER" remark="绑定类型 1个人绑定 2企业绑定"/>
        <column name="bank_name" javatype="String" jdbctype="VARCHAR" remark="银行名称"/>
        <column name="unionpay_code" javatype="String" jdbctype="VARCHAR" remark="大小额联行号"/>
        <column name="account_name" javatype="String" jdbctype="VARCHAR" remark="账户名称"/>
    </resultmap>
    <operation name="getMerchantAccountInfo" paramtype="primitive" multiplicity="one" resultmap="merchantAccountMap" remark="获取商户账户信息">
        SELECT bank.bank_no,bank.acct_id,bank.mobile,bank.bind_type,bank.bank_name,bank.unionpay_code,account.account_name
        FROM tp_lifecircle_bind_bank bank INNER JOIN tp_lifecircle_account account on account.uid = bank.uid
        where
        account.auth_status = 2 and bank.bind_status = 1 and bank.is_del = 0
        and bank.uid = #{merchantId,jdbcType=INTEGER}
        limit 1
    </operation>

    <operation remark="通过商户id获取代理商id" name="getBelongById" resulttype="java.lang.Integer" multiplicity="one">
        SELECT belong
        FROM TP_USERS
        WHERE id = #{id,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation remark="通过过商户id获取用户信息" name="getUserNameById" multiplicity="one">
        SELECT id,username,users_token,parent_id
        FROM TP_USERS
        WHERE id = #{id,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation remark="通过过商户id获取list" name="getUserListById" multiplicity="many">
        SELECT
        *
        FROM TP_USERS
        WHERE id in
        <foreach item="item" index="index" collection="list" open="(" separator="," close=")">
            #{item,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="getMinIdByBelong" multiplicity="one" resulttype="Integer" remark="根据belong查询minId">
        SELECT
        min(id)
        FROM TP_USERS
        WHERE belong = #{belong,jdbcType=INTEGER}
    </operation>

    <operation name="getMaxIdByBelong" multiplicity="one" resulttype="Integer" remark="根据belong查询maxId">
        SELECT
        max(id)
        FROM TP_USERS
        WHERE belong = #{belong,jdbcType=INTEGER}
    </operation>

    <operation remark="通过代理商id获取list" name="getUserListByBelong" multiplicity="many">
        SELECT
        *
        FROM TP_USERS
        WHERE
        belong = #{belong,jdbcType=INTEGER}
        and parent_id = 0
        LIMIT #{pageNum,jdbcType=INTEGER},#{pageSize,jdbcType=INTEGER}
    </operation>

    <operation name="countUserListByBelong" resulttype="Integer" multiplicity="one" remark="通过代理商id获取list">
        SELECT
        count(*)
        FROM TP_USERS
        WHERE
        belong = #{belong,jdbcType=INTEGER}
        and parent_id = 0
    </operation>

    <operation name="getByStoreId"
               paramtype="primitive"
               multiplicity="one"
               remark="通过门店ID查询商户信息"
               resultmap="usersExtraBaseDOResultMap">
        SELECT
        mch.`id` as uid,
        mch.`username` as username,
        mch.`merchant_type` as merchant_type,
        mch.`income_status` as income_status
        FROM `tp_lifecircle_store` store
        LEFT JOIN `tp_wxuser` wx on wx.`token` = store.`token`
        LEFT JOIN tp_users mch on wx.`uid` = mch.`id`
        WHERE store.`store_id` = #{storeId, jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="getByUid"
               paramtype="primitive"
               multiplicity="one"
               resultmap="usersExtraBaseDOResultMap">
        SELECT
        mch.`id` as uid,
        mch.`username` as username,
        mch.`merchant_type` as merchant_type,
        mch.`income_status` as income_status
        FROM `tp_users` mch
        WHERE mch.`id` = #{uid, jdbcType=INTEGER}
        LIMIT 1
    </operation>


    <operation name="getMerchantInfoById" paramtype="primitive" multiplicity="one" remark="通过商户 id 获取商户信息">
        SELECT *
        FROM TP_USERS
        WHERE id = #{id,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="updateIncomeStatusByUids" paramtype="primitive" remark="批量更新商户进件状态">
        UPDATE TP_USERS
        SET income_status = #{incomeStatus,jdbcType=TINYINT}
        where id in (
        <foreach collection="list" item="id" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
        )
    </operation>

    <operation name="updateIncomStatus" paramtype="primitive" remark="修改商户进件状态">
        UPDATE tp_users
        SET income_status = #{incomeStatus,jdbcType=INTEGER}
        WHERE id = #{id,jdbcType=INTEGER}
    </operation>
</table>
