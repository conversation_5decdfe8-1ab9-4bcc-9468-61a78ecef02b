<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_APP_UPDATE" physicalName="TP_LIFECIRCLE_APP_UPDATE"
       remark="付呗app版本更新表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_APP_UPDATE">
        INSERT INTO TP_LIFECIRCLE_APP_UPDATE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="apkUrl != null">`APK_URL`,</if>
            <if test="appName != null">`APP_NAME`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="oemCode != null">`OEM_CODE`,</if>
            <if test="versionName != null">`VERSION_NAME`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="pushCount != null">`PUSH_COUNT`,</if>
            <if test="greyStatus != null">`GREY_STATUS`,</if>
            <if test="isGreyFlag != null">`IS_GREY_FLAG`,</if>
            <if test="versionCode != null">`VERSION_CODE`,</if>
            <if test="versionType != null">`VERSION_TYPE`,</if>
            <if test="windowsType != null">`WINDOWS_TYPE`,</if>
            <if test="isForceUpdate != null">`IS_FORCE_UPDATE`,</if>
            <if test="isForceUpdateNew != null">`IS_FORCE_UPDATE_NEW`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="apkUrl != null">#{apkUrl,jdbcType=VARCHAR},</if>
            <if test="appName != null">#{appName,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="oemCode != null">#{oemCode,jdbcType=VARCHAR},</if>
            <if test="versionName != null">#{versionName,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="pushCount != null">#{pushCount,jdbcType=TINYINT},</if>
            <if test="greyStatus != null">#{greyStatus,jdbcType=TINYINT},</if>
            <if test="isGreyFlag != null">#{isGreyFlag,jdbcType=TINYINT},</if>
            <if test="versionCode != null">#{versionCode,jdbcType=INTEGER},</if>
            <if test="versionType != null">#{versionType,jdbcType=TINYINT},</if>
            <if test="windowsType != null">#{windowsType,jdbcType=TINYINT},</if>
            <if test="isForceUpdate != null">#{isForceUpdate,jdbcType=TINYINT},</if>
            <if test="isForceUpdateNew != null">#{isForceUpdateNew,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    <operation name="getById" multiplicity="one" paramtype="primitive" remark="根据 ID 获得版本信息">
        SELECT *
        FROM
        TP_LIFECIRCLE_APP_UPDATE
        WHERE
        id = #{id,jdbcType=INTEGER}
        LIMIT 1
    </operation>
</table>
