<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT" physicalName="TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT"
    remark="批量登记挂账数据">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT">
INSERT INTO TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="uname != null">`UNAME`,</if>
        <if test="bankKey != null">`BANK_KEY`,</if>
        <if test="bankNum != null">`BANK_NUM`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="pubToPri != null">`PUB_TO_PRI`,</if>
        <if test="tradeType != null">`TRADE_TYPE`,</if>
        <if test="curentDate != null">`CURENT_DATE`,</if>
        <if test="totalTradeNums != null">`TOTAL_TRADE_NUMS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="subsidy != null">`SUBSIDY`,</if>
        <if test="fubeiGain != null">`FUBEI_GAIN`,</if>
        <if test="leshuaGain != null">`LESHUA_GAIN`,</if>
        <if test="balanceMoney != null">`BALANCE_MONEY`,</if>
        <if test="channelCharges != null">`CHANNEL_CHARGES`,</if>
        <if test="merchantCharges != null">`MERCHANT_CHARGES`,</if>
        <if test="totalTradeMoney != null">`TOTAL_TRADE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="mchid != null">#{mchid,jdbcType=LONGVARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="uname != null">#{uname,jdbcType=VARCHAR},</if>
        <if test="bankKey != null">#{bankKey,jdbcType=VARCHAR},</if>
        <if test="bankNum != null">#{bankNum,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="pubToPri != null">#{pubToPri,jdbcType=TINYINT},</if>
        <if test="tradeType != null">#{tradeType,jdbcType=TINYINT},</if>
        <if test="curentDate != null">#{curentDate,jdbcType=INTEGER},</if>
        <if test="totalTradeNums != null">#{totalTradeNums,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="subsidy != null">#{subsidy,jdbcType=DECIMAL},</if>
        <if test="fubeiGain != null">#{fubeiGain,jdbcType=DECIMAL},</if>
        <if test="leshuaGain != null">#{leshuaGain,jdbcType=DECIMAL},</if>
        <if test="balanceMoney != null">#{balanceMoney,jdbcType=DECIMAL},</if>
        <if test="channelCharges != null">#{channelCharges,jdbcType=DECIMAL},</if>
        <if test="merchantCharges != null">#{merchantCharges,jdbcType=DECIMAL},</if>
        <if test="totalTradeMoney != null">#{totalTradeMoney,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getByTokenAndCurrentDate" multiplicity="one" remark="根据token和当前date查询批量登记挂账数据">
        SELECT
        bank_name,bank_key
        FROM
        TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND curent_date = #{curentDate,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="findByTokenAndCurrentDate" multiplicity="many" remark="根据token和当前date查询批量登记挂账数据列表">
        SELECT
        bank_name,bank_key,balance_money,subsidy,total_trade_money,merchant_charges,trade_type
        FROM
        TP_NEW_LIFECIRCLE_BATCH_REGISTER_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND curent_date = #{curentDate,jdbcType=INTEGER}
    </operation>
    </table>
