<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP" physicalName="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP"
       remark="万达数据同步临时表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="adId != null">`AD_ID`,</if>
            <if test="cityId != null">`CITY_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="provinceId != null">`PROVINCE_ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="owner != null">`OWNER`,</if>
            <if test="adName != null">`AD_NAME`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="region != null">`REGION`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="license != null">`LICENSE`,</if>
            <if test="plazaId != null">`PLAZA_ID`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="latitude != null">`LATITUDE`,</if>
            <if test="storePic != null">`STORE_PIC`,</if>
            <if test="errorCode != null">`ERROR_CODE`,</if>
            <if test="longitude != null">`LONGITUDE`,</if>
            <if test="plazaName != null">`PLAZA_NAME`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="businessId != null">`BUSINESS_ID`,</if>
            <if test="traderName != null">`TRADER_NAME`,</if>
            <if test="bankCardPic != null">`BANK_CARD_PIC`,</if>
            <if test="cityCompany != null">`CITY_COMPANY`,</if>
            <if test="businessName != null">`BUSINESS_NAME`,</if>
            <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
            <if test="idCardNumber != null">`ID_CARD_NUMBER`,</if>
            <if test="ownerBackUrl != null">`OWNER_BACK_URL`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="accountMobile != null">`ACCOUNT_MOBILE`,</if>
            <if test="ownerFrontUrl != null">`OWNER_FRONT_URL`,</if>
            <if test="incomeErrorMsg != null">`INCOME_ERROR_MSG`,</if>
            <if test="insideScenePic != null">`INSIDE_SCENE_PIC`,</if>
            <if test="settlerIdCardNo != null">`SETTLER_ID_CARD_NO`,</if>
            <if test="businessPlacePic != null">`BUSINESS_PLACE_PIC`,</if>
            <if test="contactIdDocCopy != null">`CONTACT_ID_DOC_COPY`,</if>
            <if test="contactPeriodEnd != null">`CONTACT_PERIOD_END`,</if>
            <if test="licenseElectronic != null">`LICENSE_ELECTRONIC`,</if>
            <if test="settlerIdCardName != null">`SETTLER_ID_CARD_NAME`,</if>
            <if test="contactPeriodBegin != null">`CONTACT_PERIOD_BEGIN`,</if>
            <if test="ownerCertificateNo != null">`OWNER_CERTIFICATE_NO`,</if>
            <if test="contactIdDocCopyBack != null">`CONTACT_ID_DOC_COPY_BACK`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="authorizeSaveErrorMsg != null">`AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="settlerNotLegalProvePic != null">`SETTLER_NOT_LEGAL_PROVE_PIC`,</if>
            <if test="businessAuthorizationLetter != null">`BUSINESS_AUTHORIZATION_LETTER`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="syncFlag != null">`SYNC_FLAG`,</if>
            <if test="traderType != null">`TRADER_TYPE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="contactType != null">`CONTACT_TYPE`,</if>
            <if test="settlerType != null">`SETTLER_TYPE`,</if>
            <if test="callIncomeStatus != null">`CALL_INCOME_STATUS`,</if>
            <if test="contactIdDocType != null">`CONTACT_ID_DOC_TYPE`,</if>
            <if test="authorizeSaveFlag != null">`AUTHORIZE_SAVE_FLAG`,</if>
            <if test="settlerIdCardType != null">`SETTLER_ID_CARD_TYPE`,</if>
            <if test="contactPeriodIsLong != null">`CONTACT_PERIOD_IS_LONG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="storePicCheckStatus != null">`STORE_PIC_CHECK_STATUS`,</if>
            <if test="ownerCertificateType != null">`OWNER_CERTIFICATE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="effectiveEndDate != null">`EFFECTIVE_END_DATE`,</if>
            <if test="certificateEndDate != null">`CERTIFICATE_END_DATE`,</if>
            <if test="effectiveStartDate != null">`EFFECTIVE_START_DATE`,</if>
            <if test="certificateStartDate != null">`CERTIFICATE_START_DATE`,</if>
            <if test="businessType != null">`business_type`,</if>
            <if test="wdCategoryname != null">`wd_categoryname`,</if>
            <if test="floor != null">`floor`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=BIGINT},</if>
            <if test="adId != null">#{adId,jdbcType=BIGINT},</if>
            <if test="cityId != null">#{cityId,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="provinceId != null">#{provinceId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="owner != null">#{owner,jdbcType=VARCHAR},</if>
            <if test="adName != null">#{adName,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="region != null">#{region,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="license != null">#{license,jdbcType=VARCHAR},</if>
            <if test="plazaId != null">#{plazaId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="latitude != null">#{latitude,jdbcType=VARCHAR},</if>
            <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
            <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
            <if test="longitude != null">#{longitude,jdbcType=VARCHAR},</if>
            <if test="plazaName != null">#{plazaName,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="businessId != null">#{businessId,jdbcType=VARCHAR},</if>
            <if test="traderName != null">#{traderName,jdbcType=VARCHAR},</if>
            <if test="bankCardPic != null">#{bankCardPic,jdbcType=VARCHAR},</if>
            <if test="cityCompany != null">#{cityCompany,jdbcType=VARCHAR},</if>
            <if test="businessName != null">#{businessName,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
            <if test="idCardNumber != null">#{idCardNumber,jdbcType=VARCHAR},</if>
            <if test="ownerBackUrl != null">#{ownerBackUrl,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="accountMobile != null">#{accountMobile,jdbcType=VARCHAR},</if>
            <if test="ownerFrontUrl != null">#{ownerFrontUrl,jdbcType=VARCHAR},</if>
            <if test="incomeErrorMsg != null">#{incomeErrorMsg,jdbcType=VARCHAR},</if>
            <if test="insideScenePic != null">#{insideScenePic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardNo != null">#{settlerIdCardNo,jdbcType=VARCHAR},</if>
            <if test="businessPlacePic != null">#{businessPlacePic,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopy != null">#{contactIdDocCopy,jdbcType=VARCHAR},</if>
            <if test="contactPeriodEnd != null">#{contactPeriodEnd,jdbcType=VARCHAR},</if>
            <if test="licenseElectronic != null">#{licenseElectronic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardName != null">#{settlerIdCardName,jdbcType=VARCHAR},</if>
            <if test="contactPeriodBegin != null">#{contactPeriodBegin,jdbcType=VARCHAR},</if>
            <if test="ownerCertificateNo != null">#{ownerCertificateNo,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopyBack != null">#{contactIdDocCopyBack,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="authorizeSaveErrorMsg != null">#{authorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settlerNotLegalProvePic != null">#{settlerNotLegalProvePic,jdbcType=VARCHAR},</if>
            <if test="businessAuthorizationLetter != null">
                #{businessAuthorizationLetter,jdbcType=VARCHAR},
            </if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="syncFlag != null">#{syncFlag,jdbcType=TINYINT},</if>
            <if test="traderType != null">#{traderType,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
            <if test="contactType != null">#{contactType,jdbcType=TINYINT},</if>
            <if test="settlerType != null">#{settlerType,jdbcType=TINYINT},</if>
            <if test="callIncomeStatus != null">#{callIncomeStatus,jdbcType=TINYINT},</if>
            <if test="contactIdDocType != null">#{contactIdDocType,jdbcType=TINYINT},</if>
            <if test="authorizeSaveFlag != null">#{authorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardType != null">#{settlerIdCardType,jdbcType=TINYINT},</if>
            <if test="contactPeriodIsLong != null">#{contactPeriodIsLong,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="storePicCheckStatus != null">#{storePicCheckStatus,jdbcType=TINYINT},</if>
            <if test="ownerCertificateType != null">#{ownerCertificateType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="effectiveEndDate != null">#{effectiveEndDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateEndDate != null">#{certificateEndDate,jdbcType=TIMESTAMP},</if>
            <if test="effectiveStartDate != null">#{effectiveStartDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateStartDate != null">#{certificateStartDate,jdbcType=TIMESTAMP},</if>
            <if test="businessType != null">#{businessType,jdbcType=TINYINT},</if>
            <if test="wdCategoryname != null">#{wdCategoryname,jdbcType=VARCHAR},</if>
            <if test="floor != null">#{floor,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation name="updateByStoreId" paramtype="object" remark="根据表主键填充设备序列号">
        UPDATE
        tp_lifecircle_wanda_data_sync_temp
        SET
        `sync_flag`= #{syncFlag,jdbcType=TINYINT},
        `trader_type`= #{traderType,jdbcType=TINYINT},
        `store_name`= #{storeName,jdbcType=VARCHAR},
        `account_mobile`= #{accountMobile,jdbcType=VARCHAR},
        `business_id`= #{businessId,jdbcType=VARCHAR},
        `business_name`= #{businessName,jdbcType=VARCHAR},
        `owner`= #{owner,jdbcType=VARCHAR},
        `owner_certificate_type`= #{ownerCertificateType,jdbcType=TINYINT},
        `owner_certificate_no`= #{ownerCertificateNo,jdbcType=VARCHAR},
        `owner_front_url`= #{ownerFrontUrl,jdbcType=VARCHAR},
        `owner_back_url`= #{ownerBackUrl,jdbcType=VARCHAR},
        `certificate_start_date`= #{certificateStartDate,jdbcType=TIMESTAMP},
        `certificate_end_date`= #{certificateEndDate,jdbcType=TIMESTAMP},
        `province_id` = #{provinceId,jdbcType=BIGINT},
        `province_name`= #{provinceName,jdbcType=VARCHAR},
        `city_id` = #{cityId,jdbcType=BIGINT},
        `city_name`= #{cityName,jdbcType=VARCHAR},
        `ad_id` = #{adId,jdbcType=BIGINT},
        `ad_name`= #{adName,jdbcType=VARCHAR},
        `address`= #{address,jdbcType=VARCHAR},
        `longitude`= #{longitude,jdbcType=VARCHAR},
        `latitude`= #{latitude,jdbcType=VARCHAR},
        `license`= #{license,jdbcType=VARCHAR},
        `trader_name`= #{traderName,jdbcType=VARCHAR},
        `license_electronic`= #{licenseElectronic,jdbcType=VARCHAR},
        `effective_start_date`= #{effectiveStartDate,jdbcType=TIMESTAMP},
        `effective_end_date`= #{effectiveEndDate,jdbcType=TIMESTAMP},
        `bank_type`= #{bankType,jdbcType=TINYINT},
        `bank_name`= #{bankName,jdbcType=VARCHAR},
        `bank_no`= #{bankNo,jdbcType=VARCHAR},
        `plaza_id`= #{plazaId,jdbcType=VARCHAR},
        `plaza_name` = #{plazaName,jdbcType=VARCHAR}
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </operation>


    <operation name="updateOfflineConfirmInfoByStoreId" paramtype="object" remark="更新线下确认信息">
        update
        tp_lifecircle_wanda_data_sync_temp
        set
        has_offline_confirm = #{hasOfflineConfirm,jdbcType=TINYINT},
        store_head_pic = #{storeHeadPic, jdbcType=VARCHAR},
        store_manage_pic = #{storeManagePic, jdbcType=VARCHAR},
        store_finance_pic = #{storeFinancePic, jdbcType=VARCHAR}
        where store_id = #{storeId,jdbcType=BIGINT}
    </operation>

    <operation name="updateFailByStoreId" paramtype="object" remark="更新失败原因">
        UPDATE
        tp_lifecircle_wanda_data_sync_temp
        SET
        `sync_flag`= #{syncFlag,jdbcType=TINYINT},
        `error_code` = #{errorCode,jdbcType=VARCHAR},
        `error_message` = #{errorMessage,jdbcType=VARCHAR}
        WHERE store_id = #{storeId,jdbcType=BIGINT}
    </operation>

    <operation name="batchSaveWandaStore" paramtype="objectList" remark="丙晟门店批量入驻">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        (`store_id`, `store_name` , `longitude` , `latitude` , `plaza_id` , `plaza_name` ,
        `city_company` , `region` ,`certificate_start_date`,`certificate_end_date`,`effective_start_date`,
        `effective_end_date`,`sync_flag`,`floor`,`wd_categoryname`,`business_type`)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.storeId,jdbcType=BIGINT},
            #{item.storeName,jdbcType=VARCHAR},
            #{item.longitude,jdbcType=VARCHAR},
            #{item.latitude,jdbcType=VARCHAR},
            #{item.plazaId,jdbcType=VARCHAR},
            #{item.plazaName,jdbcType=VARCHAR},
            #{item.cityCompany,jdbcType=VARCHAR},
            #{item.region,jdbcType=VARCHAR},
            NOW(), NOW(), NOW(), NOW(), '1',
            #{item.floor,jdbcType=VARCHAR},
            #{item.wdCategoryname,jdbcType=VARCHAR},
            #{item.businessType,jdbcType=TINYINT}
            )
        </foreach>
    </operation>

    <operation name="getLocationByPlazaName" paramtype="primitive" remark="根据广场名获取广场位置信息">
        select longitude,latitude from TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP where plaza_name =  #{plazaName,jdbcType=VARCHAR} limit 1
    </operation>


    <operation name="getByPlazaIdAndPlazaNameAndStoreName" multiplicity="one" remark="根据广场id和门店名获取万达门店信息">
        SELECT *  FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        WHERE plaza_id = #{plazaId,jdbcType=VARCHAR}
        and plaza_name = #{plazaName,jdbcType=VARCHAR}
        and store_name = #{storeName,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getOneByPlazaId" multiplicity="one" paramtype="primitive" remark="根据广场ID获取一个条同步信息">
        SELECT * FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP WHERE plaza_id = #{plazaId,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getCurrentWandaPrepareEntryStoreId" multiplicity="one" resulttype="Integer"
               remark="获取当前万达门店预进件门店ID起始位置，">
        select store_id from `tp_lifecircle_wanda_data_sync_temp`
        WHERE `store_id` between 9500000 and 9600000
        order by `store_id` DESC
        LIMIT 1;
    </operation>

    <operation name="getOneByStoreName" multiplicity="one" paramtype="primitive"
               remark="根据门店名称获取一个条同步信息">
        SELECT * FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP WHERE store_name = #{storeName,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
</table>
