<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_SYSTEM" physicalName="TP_LIFECIRCLE_SYSTEM"
    remark="TP_LIFECIRCLE_SYSTEM">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_SYSTEM">
INSERT INTO TP_LIFECIRCLE_SYSTEM
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="keys != null">`KEYS`,</if>
        <if test="title != null">`TITLE`,</if>
        <if test="messages != null">`MESSAGES`,</if>
        <if test="strValues != null">`STR_VALUES`,</if>
        <if test="whiteList != null">`WHITE_LIST`,</if>
        <if test="values != null">`VALUES`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="keys != null">#{keys,jdbcType=VARCHAR},</if>
        <if test="title != null">#{title,jdbcType=VARCHAR},</if>
        <if test="messages != null">#{messages,jdbcType=VARCHAR},</if>
        <if test="strValues != null">#{strValues,jdbcType=VARCHAR},</if>
        <if test="whiteList != null">#{whiteList,jdbcType=VARCHAR},</if>
        <if test="values != null">#{values,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getByKeys" multiplicity="one" remark="根据 keys 查询记录">
        SELECT
        id
        ,`keys`
        ,title
        ,`values`
        ,messages
        ,str_values
        ,white_list
        FROM tp_lifecircle_system
        WHERE
        `keys` = #{keys,jdbcType=VARCHAR}
        limit 1
    </operation>
    </table>
