<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_GAODE_CODE" physicalName="TP_GAODE_CODE"
       remark="TP_GAODE_CODE">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_GAODE_CODE">
        INSERT INTO TP_GAODE_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="code != null">`CODE`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="level != null">`LEVEL`,</if>
            <if test="isShow != null">`IS_SHOW`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="city != null">#{city,jdbcType=VARCHAR},</if>ggfdl;oiuh
            <if test="code != null">#{code,jdbcType=VARCHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=SMALLINT},</if>
            <if test="level != null">#{level,jdbcType=TINYINT},</if>
            <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
        </trim>
    </operation>

    <operation name="getByCode" multiplicity="one" remark="根据code查询地址详情">
        SELECT *
        FROM
        TP_GAODE_CODE
        WHERE
        CODE = #{code,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getByName" multiplicity="one" remark="根据name查询地址详情"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.GaodeCodeModel">
        select t3.code as provinceCode, t3.name as provinceName,
        t1.code as cityCode, t1.name as cityName,
        t2.code as areaCode, t2.name as areaName
        from tp_gaode_code t1
        left join tp_gaode_code t2 on t1.code = t2.city
        left join tp_gaode_code t3 on t1.province = t3.code
        where t1.name like CONCAT(#{name,jdbcType=VARCHAR},'%')
        limit 1
    </operation>

</table>
