<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_APP_USER_INFO" physicalName="TP_APP_USER_INFO"
       remark="app商户手机版本信息">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_APP_USER_INFO">
        INSERT INTO TP_APP_USER_INFO
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uuid != null">`UUID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="phoneBrand != null">`PHONE_BRAND`,</if>
            <if test="phoneModel != null">`PHONE_MODEL`,</if>
            <if test="sysVersion != null">`SYS_VERSION`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="isSys != null">`IS_SYS`,</if>
            <if test="isLock != null">`IS_LOCK`,</if>
            <if test="lockUid != null">`LOCK_UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="lockTime != null">`LOCK_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="superPushChannelId != null">`SUPER_PUSH_CHANNEL_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="uuid != null">#{uuid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="phoneBrand != null">#{phoneBrand,jdbcType=VARCHAR},</if>
            <if test="phoneModel != null">#{phoneModel,jdbcType=VARCHAR},</if>
            <if test="sysVersion != null">#{sysVersion,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="isSys != null">#{isSys,jdbcType=TINYINT},</if>
            <if test="isLock != null">#{isLock,jdbcType=TINYINT},</if>
            <if test="lockUid != null">#{lockUid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="lockTime != null">#{lockTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="superPushChannelId != null">#{superPushChannelId,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="getAppInfoByTokenAndUUID" multiplicity="one" remark="根据token和uid商户的推送配置信息">
        SELECT * FROM TP_APP_USER_INFO
        WHERE
        uid = #{uid,jdbcType=INTEGER}
        AND token = #{token,jdbcType=VARCHAR}
        <if test="uuid != null and uuid != ''">
            AND uuid = #{uuid,jdbcType=VARCHAR}
        </if>
        AND is_del = 0
        ORDER BY id desc
        LIMIT 1
    </operation>

    <operation name="findAppUserInfoListByUidList" multiplicity="many" remark="查询所有有效的app用户数量">
        SELECT * FROM TP_APP_USER_INFO
        WHERE is_del = 0
        AND uid in
        <foreach collection="list" index="index" item="uid" open="(" separator="," close=")">
            #{uid,jdbcType=INTEGER}
        </foreach>
    </operation>

</table>
