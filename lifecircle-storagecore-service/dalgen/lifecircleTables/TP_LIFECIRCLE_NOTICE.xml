<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_NOTICE" physicalName="TP_LIFECIRCLE_NOTICE"
       remark="TP_LIFECIRCLE_NOTICE">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_NOTICE">
        INSERT INTO TP_LIFECIRCLE_NOTICE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="recommend != null">`RECOMMEND`,</if>
            <if test="activation != null">`ACTIVATION`,</if>
            <if test="rechargeLimit != null">`RECHARGE_LIMIT`,</if>
            <if test="title != null">`TITLE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="consumAmount != null">`CONSUM_AMOUNT`,</if>
            <if test="consumReturn != null">`CONSUM_RETURN`,</if>
            <if test="posterZipUrl != null">`POSTER_ZIP_URL`,</if>
            <if test="rechargeAmount != null">`RECHARGE_AMOUNT`,</if>
            <if test="rechargeReturn != null">`RECHARGE_RETURN`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="isPush != null">`IS_PUSH`,</if>
            <if test="isSend != null">`IS_SEND`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="isLimit != null">`IS_LIMIT`,</if>
            <if test="sendNum != null">`SEND_NUM`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="timeLimit != null">`TIME_LIMIT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="returnCash != null">`RETURN_CASH`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
            <if test="consumReturnMode != null">`CONSUM_RETURN_MODE`,</if>
            <if test="consumptionReturnNum != null">`CONSUMPTION_RETURN_NUM`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="recommend != null">#{recommend,jdbcType=DECIMAL},</if>
            <if test="activation != null">#{activation,jdbcType=DECIMAL},</if>
            <if test="rechargeLimit != null">#{rechargeLimit,jdbcType=DECIMAL},</if>
            <if test="title != null">#{title,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="content != null">#{content,jdbcType=LONGVARCHAR},</if>
            <if test="consumAmount != null">#{consumAmount,jdbcType=VARCHAR},</if>
            <if test="consumReturn != null">#{consumReturn,jdbcType=VARCHAR},</if>
            <if test="posterZipUrl != null">#{posterZipUrl,jdbcType=VARCHAR},</if>
            <if test="rechargeAmount != null">#{rechargeAmount,jdbcType=VARCHAR},</if>
            <if test="rechargeReturn != null">#{rechargeReturn,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="isPush != null">#{isPush,jdbcType=TINYINT},</if>
            <if test="isSend != null">#{isSend,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="isLimit != null">#{isLimit,jdbcType=TINYINT},</if>
            <if test="sendNum != null">#{sendNum,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="timeLimit != null">#{timeLimit,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="returnCash != null">#{returnCash,jdbcType=TINYINT},</if>
            <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
            <if test="consumReturnMode != null">#{consumReturnMode,jdbcType=TINYINT},</if>
            <if test="consumptionReturnNum != null">#{consumptionReturnNum,jdbcType=SMALLINT},</if>
        </trim>
    </operation>

    <operation name="updatePosterZipUrlByActivityId" paramtype="primitive" remark="修改海报压缩包地址">
        UPDATE
        TP_LIFECIRCLE_NOTICE
        SET
        poster_zip_url = #{posterZipUrl,jdbcType=VARCHAR}
        WHERE
        id = #{id,jdbcType=INTEGER}
    </operation>
</table>
