<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_ENTRY_APPLICATION" physicalName="TP_MERCHANT_ENTRY_APPLICATION"
       remark="随行付直连商户进件申请表">
        <!--    &lt;&gt;   <> -->
        <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_ENTRY_APPLICATION">
                INSERT INTO TP_MERCHANT_ENTRY_APPLICATION
                <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="id != null">`ID`,</if>
                        <if test="address != null">`ADDRESS`,</if>
                        <if test="applyId != null">`APPLY_ID`,</if>
                        <if test="mccCode != null">`MCC_CODE`,</if>
                        <if test="orgCode != null">`ORG_CODE`,</if>
                        <if test="cityCode != null">`CITY_CODE`,</if>
                        <if test="storePic != null">`STORE_PIC`,</if>
                        <if test="taxRegNo != null">`TAX_REG_NO`,</if>
                        <if test="alipayPid != null">`ALIPAY_PID`,</if>
                        <if test="parentMno != null">`PARENT_MNO`,</if>
                        <if test="unionCode != null">`UNION_CODE`,</if>
                        <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
                        <if test="bankNumber != null">`BANK_NUMBER`,</if>
                        <if test="handleType != null">`HANDLE_TYPE`,</if>
                        <if test="identityNo != null">`IDENTITY_NO`,</if>
                        <if test="licensePic != null">`LICENSE_PIC`,</if>
                        <if test="merchantNo != null">`MERCHANT_NO`,</if>
                        <if test="orgCodePic != null">`ORG_CODE_PIC`,</if>
                        <if test="districtCode != null">`DISTRICT_CODE`,</if>
                        <if test="identityName != null">`IDENTITY_NAME`,</if>
                        <if test="provinceCode != null">`PROVINCE_CODE`,</if>
                        <if test="rejectReason != null">`REJECT_REASON`,</if>
                        <if test="applicationId != null">`APPLICATION_ID`,</if>
                        <if test="handIdcardPic != null">`HAND_IDCARD_PIC`,</if>
                        <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
                        <if test="branchBankName != null">`BRANCH_BANK_NAME`,</if>
                        <if test="insideScenePic != null">`INSIDE_SCENE_PIC`,</if>
                        <if test="merchantMobile != null">`MERCHANT_MOBILE`,</if>
                        <if test="wechatSubMchId != null">`WECHAT_SUB_MCH_ID`,</if>
                        <if test="letterOfAuthPic != null">`LETTER_OF_AUTH_PIC`,</if>
                        <if test="settleLicenseNo != null">`SETTLE_LICENSE_NO`,</if>
                        <if test="businessPlacePic != null">`BUSINESS_PLACE_PIC`,</if>
                        <if test="settleBankcardNo != null">`SETTLE_BANKCARD_NO`,</if>
                        <if test="wechatChannelNum != null">`WECHAT_CHANNEL_NUM`,</if>
                        <if test="customerTelephone != null">`CUSTOMER_TELEPHONE`,</if>
                        <if test="merchantShortName != null">`MERCHANT_SHORT_NAME`,</if>
                        <if test="settleAccountName != null">`SETTLE_ACCOUNT_NAME`,</if>
                        <if test="supportTradeTypes != null">`SUPPORT_TRADE_TYPES`,</if>
                        <if test="supportPayChannels != null">`SUPPORT_PAY_CHANNELS`,</if>
                        <if test="bankcardOppositePic != null">`BANKCARD_OPPOSITE_PIC`,</if>
                        <if test="bankcardPositivePic != null">`BANKCARD_POSITIVE_PIC`,</if>
                        <if test="businessLicenseCode != null">`BUSINESS_LICENSE_CODE`,</if>
                        <if test="businessLicenseName != null">`BUSINESS_LICENSE_NAME`,</if>
                        <if test="taxRegistLicensePic != null">`TAX_REGIST_LICENSE_PIC`,</if>
                        <if test="settleLicenseEndTime != null">`SETTLE_LICENSE_END_TIME`,</if>
                        <if test="businessLicenseEndTime != null">`BUSINESS_LICENSE_END_TIME`,</if>
                        <if test="legalPersonOppositePic != null">`LEGAL_PERSON_OPPOSITE_PIC`,</if>
                        <if test="legalPersonPositivePic != null">`LEGAL_PERSON_POSITIVE_PIC`,</if>
                        <if test="settleLicenseStartTime != null">`SETTLE_LICENSE_START_TIME`,</if>
                        <if test="businessLicenseStartTime != null">`BUSINESS_LICENSE_START_TIME`,</if>
                        <if test="openingAccountLicensePic != null">`OPENING_ACCOUNT_LICENSE_PIC`,</if>
                        <if test="legalPersonLicenseEndTime != null">`LEGAL_PERSON_LICENSE_END_TIME`,</if>
                        <if test="legalPersonLicenseStartTime != null">`LEGAL_PERSON_LICENSE_START_TIME`,</if>
                        <if test="settlePersonIdcardOppositePic != null">`SETTLE_PERSON_IDCARD_OPPOSITE_PIC`,</if>
                        <if test="settlePersonIdcardPositivePic != null">`SETTLE_PERSON_IDCARD_POSITIVE_PIC`,</if>
                        <if test="uid != null">`UID`,</if>
                        <if test="storeId != null">`STORE_ID`,</if>
                        <if test="isFinish != null">`IS_FINISH`,</if>
                        <if test="isSubmit != null">`IS_SUBMIT`,</if>
                        <if test="userFeeId != null">`USER_FEE_ID`,</if>
                        <if test="configStep != null">`CONFIG_STEP`,</if>
                        <if test="settleType != null">`SETTLE_TYPE`,</if>
                        <if test="applyStatus != null">`APPLY_STATUS`,</if>
                        <if test="activityType != null">`ACTIVITY_TYPE`,</if>
                        <if test="identityType != null">`IDENTITY_TYPE`,</if>
                        <if test="licenseMatch != null">`LICENSE_MATCH`,</if>
                        <if test="merchantType != null">`MERCHANT_TYPE`,</if>
                        <if test="isRejectedStore != null">`IS_REJECTED_STORE`,</if>
                        <if test="operationalType != null">`OPERATIONAL_TYPE`,</if>
                        <if test="independentModel != null">`INDEPENDENT_MODEL`,</if>
                        <if test="wechantChannelId != null">`WECHANT_CHANNEL_ID`,</if>
                        <if test="isRejectedAccount != null">`IS_REJECTED_ACCOUNT`,</if>
                        <if test="settleAccountType != null">`SETTLE_ACCOUNT_TYPE`,</if>
                        <if test="isRejectedBankcard != null">`IS_REJECTED_BANKCARD`,</if>
                        <if test="isMigrateLiquidation != null">`IS_MIGRATE_LIQUIDATION`,</if>
                        <if test="merchantQualification != null">`MERCHANT_QUALIFICATION`,</if>
                        <if test="createTime != null">`CREATE_TIME`,</if>
                        <if test="updateTime != null">`UPDATE_TIME`,</if>
                        <if test="unionRate != null">`UNION_RATE`,</if>
                        <if test="alipayRate != null">`ALIPAY_RATE`,</if>
                        <if test="wechatRate != null">`WECHAT_RATE`,</if>
                </trim>
                VALUES
                <trim prefix="(" suffix=")" suffixOverrides=",">
                        <if test="id != null">#{id,jdbcType=BIGINT},</if>
                        <if test="address != null">#{address,jdbcType=VARCHAR},</if>
                        <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
                        <if test="mccCode != null">#{mccCode,jdbcType=VARCHAR},</if>
                        <if test="orgCode != null">#{orgCode,jdbcType=VARCHAR},</if>
                        <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
                        <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
                        <if test="taxRegNo != null">#{taxRegNo,jdbcType=VARCHAR},</if>
                        <if test="alipayPid != null">#{alipayPid,jdbcType=VARCHAR},</if>
                        <if test="parentMno != null">#{parentMno,jdbcType=VARCHAR},</if>
                        <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
                        <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
                        <if test="bankNumber != null">#{bankNumber,jdbcType=VARCHAR},</if>
                        <if test="handleType != null">#{handleType,jdbcType=VARCHAR},</if>
                        <if test="identityNo != null">#{identityNo,jdbcType=VARCHAR},</if>
                        <if test="licensePic != null">#{licensePic,jdbcType=VARCHAR},</if>
                        <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
                        <if test="orgCodePic != null">#{orgCodePic,jdbcType=VARCHAR},</if>
                        <if test="districtCode != null">#{districtCode,jdbcType=VARCHAR},</if>
                        <if test="identityName != null">#{identityName,jdbcType=VARCHAR},</if>
                        <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
                        <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
                        <if test="applicationId != null">#{applicationId,jdbcType=VARCHAR},</if>
                        <if test="handIdcardPic != null">#{handIdcardPic,jdbcType=VARCHAR},</if>
                        <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
                        <if test="branchBankName != null">#{branchBankName,jdbcType=VARCHAR},</if>
                        <if test="insideScenePic != null">#{insideScenePic,jdbcType=VARCHAR},</if>
                        <if test="merchantMobile != null">#{merchantMobile,jdbcType=VARCHAR},</if>
                        <if test="wechatSubMchId != null">#{wechatSubMchId,jdbcType=VARCHAR},</if>
                        <if test="letterOfAuthPic != null">#{letterOfAuthPic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseNo != null">#{settleLicenseNo,jdbcType=VARCHAR},</if>
                        <if test="businessPlacePic != null">#{businessPlacePic,jdbcType=VARCHAR},</if>
                        <if test="settleBankcardNo != null">#{settleBankcardNo,jdbcType=VARCHAR},</if>
                        <if test="wechatChannelNum != null">#{wechatChannelNum,jdbcType=VARCHAR},</if>
                        <if test="customerTelephone != null">#{customerTelephone,jdbcType=VARCHAR},</if>
                        <if test="merchantShortName != null">#{merchantShortName,jdbcType=VARCHAR},</if>
                        <if test="settleAccountName != null">#{settleAccountName,jdbcType=VARCHAR},</if>
                        <if test="supportTradeTypes != null">#{supportTradeTypes,jdbcType=VARCHAR},</if>
                        <if test="supportPayChannels != null">#{supportPayChannels,jdbcType=VARCHAR},</if>
                        <if test="bankcardOppositePic != null">#{bankcardOppositePic,jdbcType=VARCHAR},</if>
                        <if test="bankcardPositivePic != null">#{bankcardPositivePic,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseCode != null">#{businessLicenseCode,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseName != null">#{businessLicenseName,jdbcType=VARCHAR},</if>
                        <if test="taxRegistLicensePic != null">#{taxRegistLicensePic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseEndTime != null">#{settleLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseEndTime != null">#{businessLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="legalPersonOppositePic != null">#{legalPersonOppositePic,jdbcType=VARCHAR},</if>
                        <if test="legalPersonPositivePic != null">#{legalPersonPositivePic,jdbcType=VARCHAR},</if>
                        <if test="settleLicenseStartTime != null">#{settleLicenseStartTime,jdbcType=VARCHAR},</if>
                        <if test="businessLicenseStartTime != null">#{businessLicenseStartTime,jdbcType=VARCHAR},</if>
                        <if test="openingAccountLicensePic != null">#{openingAccountLicensePic,jdbcType=VARCHAR},</if>
                        <if test="legalPersonLicenseEndTime != null">#{legalPersonLicenseEndTime,jdbcType=VARCHAR},</if>
                        <if test="legalPersonLicenseStartTime != null">
                                #{legalPersonLicenseStartTime,jdbcType=VARCHAR},
                        </if>
                        <if test="settlePersonIdcardOppositePic != null">
                                #{settlePersonIdcardOppositePic,jdbcType=VARCHAR},
                        </if>
                        <if test="settlePersonIdcardPositivePic != null">
                                #{settlePersonIdcardPositivePic,jdbcType=VARCHAR},
                        </if>
                        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
                        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
                        <if test="isFinish != null">#{isFinish,jdbcType=TINYINT},</if>
                        <if test="isSubmit != null">#{isSubmit,jdbcType=TINYINT},</if>
                        <if test="userFeeId != null">#{userFeeId,jdbcType=INTEGER},</if>
                        <if test="configStep != null">#{configStep,jdbcType=TINYINT},</if>
                        <if test="settleType != null">#{settleType,jdbcType=TINYINT},</if>
                        <if test="applyStatus != null">#{applyStatus,jdbcType=TINYINT},</if>
                        <if test="activityType != null">#{activityType,jdbcType=TINYINT},</if>
                        <if test="identityType != null">#{identityType,jdbcType=TINYINT},</if>
                        <if test="licenseMatch != null">#{licenseMatch,jdbcType=TINYINT},</if>
                        <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
                        <if test="isRejectedStore != null">#{isRejectedStore,jdbcType=TINYINT},</if>
                        <if test="operationalType != null">#{operationalType,jdbcType=TINYINT},</if>
                        <if test="independentModel != null">#{independentModel,jdbcType=TINYINT},</if>
                        <if test="wechantChannelId != null">#{wechantChannelId,jdbcType=INTEGER},</if>
                        <if test="isRejectedAccount != null">#{isRejectedAccount,jdbcType=TINYINT},</if>
                        <if test="settleAccountType != null">#{settleAccountType,jdbcType=TINYINT},</if>
                        <if test="isRejectedBankcard != null">#{isRejectedBankcard,jdbcType=TINYINT},</if>
                        <if test="isMigrateLiquidation != null">#{isMigrateLiquidation,jdbcType=TINYINT},</if>
                        <if test="merchantQualification != null">#{merchantQualification,jdbcType=TINYINT},</if>
                        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
                        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
                        <if test="unionRate != null">#{unionRate,jdbcType=DECIMAL},</if>
                        <if test="alipayRate != null">#{alipayRate,jdbcType=DECIMAL},</if>
                        <if test="wechatRate != null">#{wechatRate,jdbcType=DECIMAL},</if>
                </trim>
        </operation>

        <operation name="getByMerchantNo" paramtype="primitive" multiplicity="one" remark="根据商户商编查询进件申请单信息">
                SELECT
                *
                FROM
                tp_merchant_entry_application
                WHERE
                merchant_no = #{merchantNo,jdbcType=INTEGER}
                AND handle_type = 'STORE_INCOME'
                ORDER BY
                id desc
                LIMIT 1
        </operation>

        <operation name="getOneByMerchantNo" paramtype="primitive" multiplicity="one" remark="根据商户商编查询申请单信息">
                SELECT
                *
                FROM
                tp_merchant_entry_application
                WHERE
                merchant_no = #{merchantNo,jdbcType=INTEGER}
                ORDER BY
                id desc
                LIMIT 1
        </operation>

        <operation name="getByUid" paramtype="primitive" multiplicity="one" remark="根据uid,机构号查询">
                SELECT
                *
                FROM
                tp_merchant_entry_application
                WHERE
                uid = #{uid,jdbcType=INTEGER}
                and platform_org_id = #{platformOrgId,jdbcType=VARCHAR}
                ORDER BY
                id asc
                LIMIT 1
        </operation>

</table>
