<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_UNITY_CATEGORY" physicalName="TP_UNITY_CATEGORY"
       remark="所有平台使用的统一类目">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_UNITY_CATEGORY">
        INSERT INTO TP_UNITY_CATEGORY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="catName != null">`CAT_NAME`,</if>
            <if test="alipayMcc != null">`ALIPAY_MCC`,</if>
            <if test="sort != null">`SORT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="level != null">`LEVEL`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="wxPersonalId != null">`WX_PERSONAL_ID`,</if>
            <if test="wxEnterpriseId != null">`WX_ENTERPRISE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="catName != null">#{catName,jdbcType=VARCHAR},</if>
            <if test="alipayMcc != null">#{alipayMcc,jdbcType=VARCHAR},</if>
            <if test="sort != null">#{sort,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="level != null">#{level,jdbcType=SMALLINT},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="wxPersonalId != null">#{wxPersonalId,jdbcType=INTEGER},</if>
            <if test="wxEnterpriseId != null">#{wxEnterpriseId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getMccByUnityCatId" multiplicity="one" remark="根据UnityCatId获取随行付对应MCC编码">
        SELECT *
        FROM TP_UNITY_CATEGORY
        WHERE id=#{unityCatId,jdbcType=INTEGER}
    </operation>


    <operation name="getOneByCatNameAndLevel" multiplicity="one" remark="根据类目名称和等级进行查询">
        SELECT *
        FROM TP_UNITY_CATEGORY
        WHERE cat_name=#{catName,jdbcType=VARCHAR}
        AND level=#{level,jdbcType=SMALLINT}
    </operation>

    <operation name="findAll" multiplicity="many" remark="获取全部">
        SELECT *
        FROM TP_UNITY_CATEGORY
    </operation>
</table>
