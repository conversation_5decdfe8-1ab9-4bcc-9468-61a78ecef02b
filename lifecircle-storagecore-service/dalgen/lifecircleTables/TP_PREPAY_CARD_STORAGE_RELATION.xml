<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_STORAGE_RELATION" physicalName="TP_PREPAY_CARD_STORAGE_RELATION"
       remark="预付卡出入库关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_STORAGE_RELATION">
        INSERT INTO TP_PREPAY_CARD_STORAGE_RELATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="storageOrder != null">`STORAGE_ORDER`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertStorageRelationBatch" paramtype="objectList" remark="批量插入出入库关联信息">
        INSERT INTO
        tp_prepay_card_storage_relation (
        storage_order,
        card_spu_id,
        card_sku_id,
        card_no
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.storageOrder,jdbcType=VARCHAR},
            #{item.cardSpuId,jdbcType=VARCHAR},
            #{item.cardSkuId,jdbcType=VARCHAR},
            #{item.cardNo,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>
</table>
