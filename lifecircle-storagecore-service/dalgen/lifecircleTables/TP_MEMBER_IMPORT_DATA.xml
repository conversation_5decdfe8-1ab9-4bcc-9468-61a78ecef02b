<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_MEMBER_IMPORT_DATA" physicalName="TP_MEMBER_IMPORT_DATA"
    remark="外部导入会员数据表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_MEMBER_IMPORT_DATA">
INSERT INTO TP_MEMBER_IMPORT_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="email != null">`EMAIL`,</if>
        <if test="phone != null">`PHONE`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="realName != null">`REAL_NAME`,</if>
        <if test="alipayUserid != null">`ALIPAY_USERID`,</if>
        <if test="importCardNo != null">`IMPORT_CARD_NO`,</if>
        <if test="sex != null">`SEX`,</if>
        <if test="birthDay != null">`BIRTH_DAY`,</if>
        <if test="integral != null">`INTEGRAL`,</if>
        <if test="birthYear != null">`BIRTH_YEAR`,</if>
        <if test="birthMonth != null">`BIRTH_MONTH`,</if>
        <if test="sourceFileId != null">`SOURCE_FILE_ID`,</if>
        <if test="collarCardTime != null">`COLLAR_CARD_TIME`,</if>
        <if test="lastConsumeTime != null">`LAST_CONSUME_TIME`,</if>
        <if test="password != null">`PASSWORD`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="cardRechargeAmount != null">`CARD_RECHARGE_AMOUNT`,</if>
        <if test="giftRechargeAmount != null">`GIFT_RECHARGE_AMOUNT`,</if>
        <if test="actualRechargeAmount != null">`ACTUAL_RECHARGE_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="email != null">#{email,jdbcType=VARCHAR},</if>
        <if test="phone != null">#{phone,jdbcType=CHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="realName != null">#{realName,jdbcType=CHAR},</if>
        <if test="alipayUserid != null">#{alipayUserid,jdbcType=VARCHAR},</if>
        <if test="importCardNo != null">#{importCardNo,jdbcType=VARCHAR},</if>
        <if test="sex != null">#{sex,jdbcType=TINYINT},</if>
        <if test="birthDay != null">#{birthDay,jdbcType=SMALLINT},</if>
        <if test="integral != null">#{integral,jdbcType=INTEGER},</if>
        <if test="birthYear != null">#{birthYear,jdbcType=SMALLINT},</if>
        <if test="birthMonth != null">#{birthMonth,jdbcType=SMALLINT},</if>
        <if test="sourceFileId != null">#{sourceFileId,jdbcType=INTEGER},</if>
        <if test="collarCardTime != null">#{collarCardTime,jdbcType=INTEGER},</if>
        <if test="lastConsumeTime != null">#{lastConsumeTime,jdbcType=INTEGER},</if>
        <if test="password != null">#{password,jdbcType=CHAR},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="cardRechargeAmount != null">#{cardRechargeAmount,jdbcType=DECIMAL},</if>
        <if test="giftRechargeAmount != null">#{giftRechargeAmount,jdbcType=DECIMAL},</if>
        <if test="actualRechargeAmount != null">#{actualRechargeAmount,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation remark="根据条件查询是否已存在导入记录" name="countRecord" resulttype="Integer">
        SELECT count(*)
        FROM TP_MEMBER_IMPORT_DATA
        WHERE token=#{token,jdbcType=VARCHAR} AND phone=#{phone,jdbcType=CHAR}
    </operation>

    </table>
