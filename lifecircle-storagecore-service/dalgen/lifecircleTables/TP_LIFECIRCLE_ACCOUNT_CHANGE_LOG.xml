<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG" physicalName="TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG"
    remark="账户余额变更记录表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG">
INSERT INTO TP_LIFECIRCLE_ACCOUNT_CHANGE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="memo != null">`MEMO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="opType != null">`OP_TYPE`,</if>
        <if test="changeType != null">`CHANGE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="withdrawId != null">`WITHDRAW_ID`,</if>
        <if test="balance != null">`BALANCE`,</if>
        <if test="afterBalance != null">`AFTER_BALANCE`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="availableBalance != null">`AVAILABLE_BALANCE`,</if>
        <if test="afterFrozenAmount != null">`AFTER_FROZEN_AMOUNT`,</if>
        <if test="afterAvailableBalance != null">`AFTER_AVAILABLE_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="memo != null">#{memo,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="opType != null">#{opType,jdbcType=TINYINT},</if>
        <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        <if test="withdrawId != null">#{withdrawId,jdbcType=INTEGER},</if>
        <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        <if test="afterBalance != null">#{afterBalance,jdbcType=DECIMAL},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=DECIMAL},</if>
        <if test="availableBalance != null">#{availableBalance,jdbcType=DECIMAL},</if>
        <if test="afterFrozenAmount != null">#{afterFrozenAmount,jdbcType=DECIMAL},</if>
        <if test="afterAvailableBalance != null">#{afterAvailableBalance,jdbcType=DECIMAL},</if>
    </trim>
    </operation>
    </table>
