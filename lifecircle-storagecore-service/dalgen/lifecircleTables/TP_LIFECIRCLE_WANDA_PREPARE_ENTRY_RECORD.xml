<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD" physicalName="TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD"
       remark="万达门店预进件记录表">

    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD">
        INSERT INTO TP_LIFECIRCLE_WANDA_PREPARE_ENTRY_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="storeId != null">STORE_ID,</if>
            <if test="unityCatId != null">UNITY_CAT_ID,</if>
            <if test="storeName != null">STORE_NAME,</if>
            <if test="detailAddress != null">DETAIL_ADDRESS,</if>
            <if test="wdFloor != null">WD_FLOOR,</if>
            <if test="wdCategoryname != null">WD_CATEGORYNAME,</if>
            <if test="plazaId != null">PLAZA_ID,</if>
            <if test="plazaName != null">PLAZA_NAME,</if>
            <if test="name != null">NAME,</if>
            <if test="mobile != null">MOBILE,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="detailAddress != null">#{detailAddress,jdbcType=VARCHAR},</if>
            <if test="wdFloor != null">#{wdFloor,jdbcType=VARCHAR},</if>
            <if test="wdCategoryname != null">#{wdCategoryname,jdbcType=VARCHAR},</if>
            <if test="plazaId != null">#{plazaId,jdbcType=VARCHAR},</if>
            <if test="plazaName != null">#{plazaName,jdbcType=VARCHAR},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

</table>
