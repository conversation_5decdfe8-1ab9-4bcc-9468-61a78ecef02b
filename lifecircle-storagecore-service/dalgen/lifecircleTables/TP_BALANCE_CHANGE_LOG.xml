<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_BALANCE_CHANGE_LOG" physicalName="TP_BALANCE_CHANGE_LOG"
    remark="商户余额变更记录表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_BALANCE_CHANGE_LOG">
INSERT INTO TP_BALANCE_CHANGE_LOG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="changeRemark != null">`CHANGE_REMARK`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="changeType != null">`CHANGE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="afterTodayBalance != null">`AFTER_TODAY_BALANCE`,</if>
        <if test="afterTotalBalance != null">`AFTER_TOTAL_BALANCE`,</if>
        <if test="changeTodayBalance != null">`CHANGE_TODAY_BALANCE`,</if>
        <if test="changeTotalBalance != null">`CHANGE_TOTAL_BALANCE`,</if>
        <if test="afterVipFreezeBalance != null">`AFTER_VIP_FREEZE_BALANCE`,</if>
        <if test="changeVipFreezeBalance != null">`CHANGE_VIP_FREEZE_BALANCE`,</if>
        <if test="afterShareFreezeBalance != null">`AFTER_SHARE_FREEZE_BALANCE`,</if>
        <if test="afterWithdrawableBalance != null">`AFTER_WITHDRAWABLE_BALANCE`,</if>
        <if test="changeShareFreezeBalance != null">`CHANGE_SHARE_FREEZE_BALANCE`,</if>
        <if test="afterIllegalFreezeBalance != null">`AFTER_ILLEGAL_FREEZE_BALANCE`,</if>
        <if test="changeWithdrawableBalance != null">`CHANGE_WITHDRAWABLE_BALANCE`,</if>
        <if test="changeIllegalFreezeBalance != null">`CHANGE_ILLEGAL_FREEZE_BALANCE`,</if>
        <if test="afterSettlementFreezeBalance != null">`AFTER_SETTLEMENT_FREEZE_BALANCE`,</if>
        <if test="changeSettlementFreezeBalance != null">`CHANGE_SETTLEMENT_FREEZE_BALANCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="afterTodayBalance != null">#{afterTodayBalance,jdbcType=DECIMAL},</if>
        <if test="afterTotalBalance != null">#{afterTotalBalance,jdbcType=DECIMAL},</if>
        <if test="changeTodayBalance != null">#{changeTodayBalance,jdbcType=DECIMAL},</if>
        <if test="changeTotalBalance != null">#{changeTotalBalance,jdbcType=DECIMAL},</if>
        <if test="afterVipFreezeBalance != null">#{afterVipFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeVipFreezeBalance != null">#{changeVipFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterShareFreezeBalance != null">#{afterShareFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterWithdrawableBalance != null">#{afterWithdrawableBalance,jdbcType=DECIMAL},</if>
        <if test="changeShareFreezeBalance != null">#{changeShareFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterIllegalFreezeBalance != null">#{afterIllegalFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeWithdrawableBalance != null">#{changeWithdrawableBalance,jdbcType=DECIMAL},</if>
        <if test="changeIllegalFreezeBalance != null">#{changeIllegalFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="afterSettlementFreezeBalance != null">#{afterSettlementFreezeBalance,jdbcType=DECIMAL},</if>
        <if test="changeSettlementFreezeBalance != null">#{changeSettlementFreezeBalance,jdbcType=DECIMAL},</if>
    </trim>
    </operation>
    </table>
