<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_BANK_PACKAGE" physicalName="TP_LIFECIRCLE_BANK_PACKAGE"
       remark="商户银行卡卡包">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_BANK_PACKAGE">
        INSERT INTO TP_LIFECIRCLE_BANK_PACKAGE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="acctId != null">`ACCT_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="backImg != null">`BACK_IMG`,</if>
            <if test="cardPic != null">`CARD_PIC`,</if>
            <if test="areaCode != null">`AREA_CODE`,</if>
            <if test="areaName != null">`AREA_NAME`,</if>
            <if test="bankLogo != null">`BANK_LOGO`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="cityCode != null">`CITY_CODE`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="idNumber != null">`ID_NUMBER`,</if>
            <if test="isActive != null">`IS_ACTIVE`,</if>
            <if test="accountName != null">`ACCOUNT_NAME`,</if>
            <if test="provinceCode != null">`PROVINCE_CODE`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="removeStatus != null">`REMOVE_STATUS`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="rejectedReason != null">`REJECTED_REASON`,</if>
            <if test="letterOfAuthPic != null">`LETTER_OF_AUTH_PIC`,</if>
            <if test="accountOpeningPermitPic != null">`ACCOUNT_OPENING_PERMIT_PIC`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="addFrom != null">`ADD_FROM`,</if>
            <if test="bindWay != null">`BIND_WAY`,</if>
            <if test="bindFrom != null">`BIND_FROM`,</if>
            <if test="bindType != null">`BIND_TYPE`,</if>
            <if test="auditTime != null">`AUDIT_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="withdrawFlag != null">`WITHDRAW_FLAG`,</if>
            <if test="reviewBankcardRejectionStatus != null">`REVIEW_BANKCARD_REJECTION_STATUS`,</if>
            <if test="bindBankId != null">`BIND_BANK_ID`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="acctId != null">#{acctId,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="backImg != null">#{backImg,jdbcType=VARCHAR},</if>
            <if test="cardPic != null">#{cardPic,jdbcType=VARCHAR},</if>
            <if test="areaCode != null">#{areaCode,jdbcType=VARCHAR},</if>
            <if test="areaName != null">#{areaName,jdbcType=VARCHAR},</if>
            <if test="bankLogo != null">#{bankLogo,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cityCode != null">#{cityCode,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="idNumber != null">#{idNumber,jdbcType=VARCHAR},</if>
            <if test="isActive != null">#{isActive,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="provinceCode != null">#{provinceCode,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="removeStatus != null">#{removeStatus,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="rejectedReason != null">#{rejectedReason,jdbcType=VARCHAR},</if>
            <if test="letterOfAuthPic != null">#{letterOfAuthPic,jdbcType=VARCHAR},</if>
            <if test="accountOpeningPermitPic != null">#{accountOpeningPermitPic,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="addFrom != null">#{addFrom,jdbcType=TINYINT},</if>
            <if test="bindWay != null">#{bindWay,jdbcType=INTEGER},</if>
            <if test="bindFrom != null">#{bindFrom,jdbcType=TINYINT},</if>
            <if test="bindType != null">#{bindType,jdbcType=INTEGER},</if>
            <if test="auditTime != null">#{auditTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
            <if test="withdrawFlag != null">#{withdrawFlag,jdbcType=INTEGER},</if>
            <if test="reviewBankcardRejectionStatus != null">#{reviewBankcardRejectionStatus,jdbcType=TINYINT},</if>
            <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
        </trim>
    </operation>

</table>
