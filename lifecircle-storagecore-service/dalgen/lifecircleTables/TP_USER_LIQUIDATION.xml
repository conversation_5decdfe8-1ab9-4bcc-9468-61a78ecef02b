<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USER_LIQUIDATION" physicalName="TP_USER_LIQUIDATION"
       remark="用户与通道关系表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USER_LIQUIDATION">
        INSERT INTO TP_USER_LIQUIDATION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="errorMsg != null">`ERROR_MSG`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="step != null">`STEP`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="userType != null">`USER_TYPE`,</if>
            <if test="beginTime != null">`BEGIN_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="errorMsg != null">#{errorMsg,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="step != null">#{step,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="userType != null">#{userType,jdbcType=TINYINT},</if>
            <if test="beginTime != null">#{beginTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

</table>
