<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LESHUA_COUNTEROFFER_FILE_REPORT" physicalName="TP_LESHUA_COUNTEROFFER_FILE_REPORT"
    remark="乐刷回盘文件记录">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LESHUA_COUNTEROFFER_FILE_REPORT">
INSERT INTO TP_LESHUA_COUNTEROFFER_FILE_REPORT
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="resultDesc != null">`RESULT_DESC`,</if>
        <if test="updateDate != null">`UPDATE_DATE`,</if>
        <if test="coupletNumber != null">`COUPLET_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="flag != null">`FLAG`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="tradeMoney != null">`TRADE_MONEY`,</if>
        <if test="isAcct != null">`IS_ACCT`,</if>
        <if test="channel != null">`CHANNEL`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="resultDesc != null">#{resultDesc,jdbcType=VARCHAR},</if>
        <if test="updateDate != null">#{updateDate,jdbcType=VARCHAR},</if>
        <if test="coupletNumber != null">#{coupletNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="flag != null">#{flag,jdbcType=TINYINT},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="tradeMoney != null">#{tradeMoney,jdbcType=DECIMAL},</if>
        <if test="isAcct != null">#{isAcct,jdbcType=TINYINT},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
    </trim>
    </operation>

    <operation name="getByOrderSn" multiplicity="one" remark="根据订单号获取回盘记录">
        SELECT
        id, flag, token, uid, trade_money, bank_name, card_no, result_desc, update_date
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        order_sn = #{orderSn, jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="updateFlagByOrderSn" paramtype="object" remark="根据订单号更新重新打款记录状态">
        UPDATE
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        SET
        flag = #{flag,jdbcType=INTEGER},
        result_desc = #{resultDesc,jdbcType=VARCHAR},
        update_date = #{updateDate,jdbcType=VARCHAR}
        WHERE
        order_sn = #{orderSn, jdbcType=VARCHAR}
    </operation>

    <operation name="getByTokenAndTradeDate" multiplicity="one" remark="根据商户token和交易日期获取回盘记录">
        SELECT
        sum(trade_money) as trade_money
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND
        trade_date = #{tradeDate,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="selectSettlementDataByToken" multiplicity="many" remark="查询商户某天的结算记录">
        SELECT
        *
        FROM
        TP_LESHUA_COUNTEROFFER_FILE_REPORT
        WHERE
        token = #{token, jdbcType=VARCHAR}
        AND trade_date = #{tradeDate,jdbcType=INTEGER}
        AND role_type = "MAIN"
        AND order_type = "SETTLEMENT"
    </operation>
    </table>
