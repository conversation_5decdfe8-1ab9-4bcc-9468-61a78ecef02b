<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WITHDRAW" physicalName="TP_LIFECIRCLE_WITHDRAW"
       remark="提现记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WITHDRAW">
        <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_LIFECIRCLE_WITHDRAW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="info != null">`INFO`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="cardBank != null">`CARD_BANK`,</if>
            <if test="serialNo != null">`SERIAL_NO`,</if>
            <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
            <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
            <if test="pid != null">`PID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="cashType != null">`CASH_TYPE`,</if>
            <if test="tranTime != null">`TRAN_TIME`,</if>
            <if test="cashStatus != null">`CASH_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="settleMode != null">`SETTLE_MODE`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="withdrawalsMode != null">`WITHDRAWALS_MODE`,</if>
            <if test="bankRate != null">`BANK_RATE`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
            <if test="bankCharges != null">`BANK_CHARGES`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="info != null">#{info,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
            <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="pid != null">#{pid,jdbcType=INTEGER},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=INTEGER},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
            <if test="cashType != null">#{cashType,jdbcType=INTEGER},</if>
            <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="withdrawalsMode != null">#{withdrawalsMode,jdbcType=TINYINT},</if>
            <if test="bankRate != null">#{bankRate,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
            <if test="bankCharges != null">#{bankCharges,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="updateStatusByOrderSn" remark="根据订单号更新提现记录状态">
        UPDATE
        TP_LIFECIRCLE_WITHDRAW
        SET
        status = #{status,jdbcType=INTEGER},
        finish_time = #{finishTime,jdbcType=INTEGER}
        WHERE
        front_log_no = #{frontLogNo,jdbcType=VARCHAR}
    </operation>

    <operation name="getDiffWithdrawList" multiplicity="many" remark="批量查询状态不为成功的提现数据">
        SELECT * FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
        AND STATUS = 2
    </operation>

    <operation name="updateDiffWithdraw" paramtype="object" remark="更新提现状态和时间(单条)">
        update TP_LIFECIRCLE_WITHDRAW
        set
        finish_time = #{finishTime,jdbcType=INTEGER},
        tran_time = #{tranTime,jdbcType=INTEGER},
        status = 1,
        cash_status = 7,
        card_no = #{cardNo,jdbcType=VARCHAR},
        account_name = #{accountName,jdbcType=VARCHAR},
        card_bank = #{cardBank,jdbcType=VARCHAR}
        where SERIAL_NUMBER=#{serialNumber,jdbcType=VARCHAR}
    </operation>

    <operation name="getWithdrawList" multiplicity="many" remark="批量查询提现数据,用于补提状态更新">
        SELECT * FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="updateDiffWithdrawBySxPay" paramtype="object" remark="更新提现状态和时间(单条)">
        update TP_LIFECIRCLE_WITHDRAW
        set
        tran_time = #{tranTime,jdbcType=INTEGER},
        status = 1,
        cash_status = 7,
        card_no = #{cardNo,jdbcType=VARCHAR},
        account_name = #{accountName,jdbcType=VARCHAR},
        card_bank = #{cardBank,jdbcType=VARCHAR}
        where SERIAL_NUMBER=#{serialNumber,jdbcType=VARCHAR}
    </operation>
    <operation name="getBySerialNumber" multiplicity="one" remark="通过serial_number获取提现单信息">
        SELECT * FROM TP_LIFECIRCLE_WITHDRAW
        WHERE SERIAL_NUMBER = #{serialNumber,jdbcType=VARCHAR}
        limit 1
    </operation>
</table>
