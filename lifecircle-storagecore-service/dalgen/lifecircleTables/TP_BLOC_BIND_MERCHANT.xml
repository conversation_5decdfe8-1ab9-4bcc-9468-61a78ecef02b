<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_BLOC_BIND_MERCHANT" physicalName="TP_BLOC_BIND_MERCHANT" remark="集团绑定的商户">

    <operation name="insert" paramtype="object" remark="insert:LM_AGENT_INFO_CHANGE_LOG">
        INSERT INTO TP_BLOC_BIND_MERCHANT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">ID,</if>
            <if test="blocId != null">BLOC_ID,</if>
            <if test="type != null">TYPE,</if>
            <if test="token != null">TOKEN,</if>
            <if test="isDel != null">IS_DEL,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            <if test="updateTime != null">UPDATE_TIME,</if>
            <if test="uid != null">UID,</if>
            <if test="orgId != null">ORG_ID,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

</table>
