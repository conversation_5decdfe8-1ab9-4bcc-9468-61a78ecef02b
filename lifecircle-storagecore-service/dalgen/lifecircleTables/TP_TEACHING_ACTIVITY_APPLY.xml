<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_TEACHING_ACTIVITY_APPLY" physicalName="TP_TEACHING_ACTIVITY_APPLY"
       remark="教培活动商户报名">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_TEACHING_ACTIVITY_APPLY">
        INSERT INTO TP_TEACHING_ACTIVITY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bizNo != null">`BIZ_NO`,</if>
            <if test="mchid != null">`MCHID`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="storeImg != null">`STORE_IMG`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="specialImg != null">`SPECIAL_IMG`,</if>
            <if test="applyStatus != null">`APPLY_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="storeLeaseImg != null">`STORE_LEASE_IMG`,</if>
            <if test="leshuaMerchantNo != null">`LESHUA_MERCHANT_NO`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bizStatus != null">`BIZ_STATUS`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="applySuccessTime != null">`APPLY_SUCCESS_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="merchantRate != null">`MERCHANT_RATE`,</if>
            <if test="address != null">`ADDRESS`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bizNo != null">#{bizNo,jdbcType=VARCHAR},</if>
            <if test="mchid != null">#{mchid,jdbcType=VARCHAR},</if>
            <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="storeImg != null">#{storeImg,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="specialImg != null">#{specialImg,jdbcType=LONGVARCHAR},</if>
            <if test="applyStatus != null">#{applyStatus,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="storeLeaseImg != null">#{storeLeaseImg,jdbcType=LONGVARCHAR},</if>
            <if test="leshuaMerchantNo != null">#{leshuaMerchantNo,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="bizStatus != null">#{bizStatus,jdbcType=TINYINT},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=INTEGER},</if>
            <if test="applySuccessTime != null">#{applySuccessTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="merchantRate != null">#{merchantRate,jdbcType=DECIMAL},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>

        </trim>
    </operation>

    <operation name="findListByMultiCondition" multiplicity="many" paramtype="primitive"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.TeachingActivityListDTO"
               remark="根据条件查询教培活动列表">
        SELECT
        id,
        mchid,
        company,
        store_img as storeImg,
        username,
        special_img as specialImg,
        reject_reason as rejectReason,
        store_lease_img as storeLeaseImg,
        leshua_merchant_no as leshuaMerchantNo,
        uid,
        biz_status as bizStatus,
        submit_time as submitTime,
        apply_success_time as applySuccessTime,
        create_time as createTime,
        address
        FROM TP_TEACHING_ACTIVITY_APPLY
        WHERE
        del_flag = 0
        <if test="uid != null">
            AND UID = #{uid,jdbcType=INTEGER}
        </if>
        <if test="username != null and username !='' ">
            AND USERNAME LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
        </if>
        <if test="mchid != null and mchid != ''">
            AND MCHID = #{mchid,jdbcType=VARCHAR}
        </if>
        <if test="leshuaMerchantNo != null and leshuaMerchantNo != ''">
            AND LESHUA_MERCHANT_NO = #{leshuaMerchantNo,jdbcType=VARCHAR}
        </if>
        <if test="bizStatus != null">
            AND BIZ_STATUS = #{bizStatus,jdbcType=INTEGER}
        </if>
        <if test="submitStartTime != null and submitEndTime != null">
            and submit_time between #{submitStartTime,jdbcType=INTEGER} and #{submitEndTime,jdbcType=INTEGER}
        </if>
        ORDER BY submit_time DESC
    </operation>

    <operation name="getTeachingActivityDetailById" multiplicity="one">
        select * from TP_TEACHING_ACTIVITY_APPLY
        WHERE id = #{id,jdbcType=INTEGER}
    </operation>
</table>
