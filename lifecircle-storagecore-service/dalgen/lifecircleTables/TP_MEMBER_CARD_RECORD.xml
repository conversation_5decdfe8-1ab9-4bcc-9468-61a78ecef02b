<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MEMBER_CARD_RECORD" physicalName="TP_MEMBER_CARD_RECORD"
       remark="会员实体卡入库记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MEMBER_CARD_RECORD">
        INSERT INTO TP_MEMBER_CARD_RECORD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="getLastOne">
        SELECT * FROM `tp_member_card_record` ORDER BY `id` DESC LIMIT 1
    </operation>

    <operation name="batchInsert" paramtype="objectList">
        insert into tp_member_card_record(card_no,create_time,update_time) values
        <foreach collection="list" item="data" separator=",">
            (#{data.cardNo,jdbcType=VARCHAR},#{data.createTime,jdbcType=INTEGER},#{data.updateTime,jdbcType=INTEGER})
        </foreach>
    </operation>
</table>
