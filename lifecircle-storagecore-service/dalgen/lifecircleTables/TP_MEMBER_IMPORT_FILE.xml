<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_MEMBER_IMPORT_FILE" physicalName="TP_MEMBER_IMPORT_FILE"
    remark="外部导入会员文件表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_MEMBER_IMPORT_FILE">
    <selectKey resultType="java.lang.Integer" keyProperty="id" order="AFTER">
        SELECT
        LAST_INSERT_ID()
    </selectKey>
INSERT INTO TP_MEMBER_IMPORT_FILE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="fileUrl != null">`FILE_URL`,</if>
        <if test="fileName != null">`FILE_NAME`,</if>
        <if test="fileType != null">`FILE_TYPE`,</if>
        <if test="sourceId != null">`SOURCE_ID`,</if>
        <if test="importStatus != null">`IMPORT_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="autoActivation != null">`AUTO_ACTIVATION`,</if>
        <if test="pointPower != null">`POINT_POWER`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="fileUrl != null">#{fileUrl,jdbcType=LONGVARCHAR},</if>
        <if test="fileName != null">#{fileName,jdbcType=LONGVARCHAR},</if>
        <if test="fileType != null">#{fileType,jdbcType=TINYINT},</if>
        <if test="sourceId != null">#{sourceId,jdbcType=INTEGER},</if>
        <if test="importStatus != null">#{importStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="autoActivation != null">#{autoActivation,jdbcType=TINYINT},</if>
        <if test="pointPower != null">#{pointPower,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
    </trim>
    </operation>

    <operation name="updateDataById" remark="根据id更新导入记录状态及失败原因">
        UPDATE TP_MEMBER_IMPORT_FILE
        SET
        import_status = #{importStatus,jdbcType=INTEGER},
        error_msg = #{errorMsg,jdbcType=VARCHAR}
        WHERE
        ID = #{id,jdbcType=INTEGER}
    </operation>

    </table>
