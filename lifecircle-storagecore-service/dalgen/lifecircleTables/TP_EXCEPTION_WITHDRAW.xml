<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_EXCEPTION_WITHDRAW" physicalName="TP_EXCEPTION_WITHDRAW"
    remark="提现记录表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_EXCEPTION_WITHDRAW">
INSERT INTO TP_EXCEPTION_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="info != null">`INFO`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardBank != null">`CARD_BANK`,</if>
        <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
        <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="bankCode != null">`BANK_CODE`,</if>
        <if test="tranTime != null">`TRAN_TIME`,</if>
        <if test="bindBankId != null">`BIND_BANK_ID`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="bankRate != null">`BANK_RATE`,</if>
        <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        <if test="bankCharges != null">`BANK_CHARGES`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="info != null">#{info,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardBank != null">#{cardBank,jdbcType=VARCHAR},</if>
        <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
        <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="status != null">#{status,jdbcType=INTEGER},</if>
        <if test="bankCode != null">#{bankCode,jdbcType=INTEGER},</if>
        <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
        <if test="bindBankId != null">#{bindBankId,jdbcType=INTEGER},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="bankRate != null">#{bankRate,jdbcType=DECIMAL},</if>
        <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        <if test="bankCharges != null">#{bankCharges,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入异常提现记录">
        INSERT INTO TP_EXCEPTION_WITHDRAW (
        token,
        uid,
        serial_number,
        cash_amount,
        status,
        finish_time,
        front_log_no,
        tran_time,
        liquidation_type,
        bind_bank_id,
        card_no,
        card_bank
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.cashAmount,jdbcType=DECIMAL},
            #{item.status,jdbcType=INTEGER},
            #{item.finishTime,jdbcType=INTEGER},
            #{item.frontLogNo,jdbcType=VARCHAR},
            #{item.tranTime,jdbcType=INTEGER},
            #{item.liquidationType,jdbcType=TINYINT},
            #{item.bindBankId,jdbcType=INTEGER},
            #{item.cardNo,jdbcType=VARCHAR},
            #{item.cardBank,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="getExceptionWithdrawList" multiplicity="many" remark="查询已经存在的异常提现单">
        SELECT * FROM
        TP_EXCEPTION_WITHDRAW
        WHERE serial_number IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="insertBatchBySxPay" paramtype="objectList" remark="批量插入异常提现记录">
        INSERT INTO TP_EXCEPTION_WITHDRAW (
        token,
        uid,
        serial_number,
        cash_amount,
        status,
        front_log_no,
        tran_time,
        liquidation_type,
        bind_bank_id,
        card_no,
        card_bank
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.cashAmount,jdbcType=DECIMAL},
            #{item.status,jdbcType=INTEGER},
            #{item.frontLogNo,jdbcType=VARCHAR},
            #{item.tranTime,jdbcType=INTEGER},
            #{item.liquidationType,jdbcType=TINYINT},
            #{item.bindBankId,jdbcType=INTEGER},
            #{item.cardNo,jdbcType=VARCHAR},
            #{item.cardBank,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>


</table>
