<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_COMMUNICATIONS_STATEMENT_ACCOUNT" physicalName="TP_COMMUNICATIONS_STATEMENT_ACCOUNT"
       remark="通联三通对账单记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_COMMUNICATIONS_STATEMENT_ACCOUNT">
        INSERT INTO TP_COMMUNICATIONS_STATEMENT_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cusid != null">`CUSID`,</if>
            <if test="buttNo != null">`BUTT_NO`,</if>
            <if test="dealNo != null">`DEAL_NO`,</if>
            <if test="batchid != null">`BATCHID`,</if>
            <if test="orderNo != null">`ORDER_NO`,</if>
            <if test="bankCode != null">`BANK_CODE`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="dealType != null">`DEAL_TYPE`,</if>
            <if test="chnltrxid != null">`CHNLTRXID`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="voucherNo != null">`VOUCHER_NO`,</if>
            <if test="cardholder != null">`CARDHOLDER`,</if>
            <if test="terminalNo != null">`TERMINAL_NO`,</if>
            <if test="bankIssuing != null">`BANK_ISSUING`,</if>
            <if test="productType != null">`PRODUCT_TYPE`,</if>
            <if test="originalMoney != null">`ORIGINAL_MONEY`,</if>
            <if test="cardholderCard != null">`CARDHOLDER_CARD`,</if>
            <if test="installmentNum != null">`INSTALLMENT_NUM`,</if>
            <if test="merchantRemark != null">`MERCHANT_REMARK`,</if>
            <if test="dealDate != null">`DEAL_DATE`,</if>
            <if test="dealTime != null">`DEAL_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="dealMoney != null">`DEAL_MONEY`,</if>
            <if test="settleMoney != null">`SETTLE_MONEY`,</if>
            <if test="serviceCharge != null">`SERVICE_CHARGE`,</if>
            <if test="installmentFee != null">`INSTALLMENT_FEE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cusid != null">#{cusid,jdbcType=VARCHAR},</if>
            <if test="buttNo != null">#{buttNo,jdbcType=VARCHAR},</if>
            <if test="dealNo != null">#{dealNo,jdbcType=VARCHAR},</if>
            <if test="batchid != null">#{batchid,jdbcType=VARCHAR},</if>
            <if test="orderNo != null">#{orderNo,jdbcType=VARCHAR},</if>
            <if test="bankCode != null">#{bankCode,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=VARCHAR},</if>
            <if test="dealType != null">#{dealType,jdbcType=VARCHAR},</if>
            <if test="chnltrxid != null">#{chnltrxid,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="voucherNo != null">#{voucherNo,jdbcType=VARCHAR},</if>
            <if test="cardholder != null">#{cardholder,jdbcType=VARCHAR},</if>
            <if test="terminalNo != null">#{terminalNo,jdbcType=VARCHAR},</if>
            <if test="bankIssuing != null">#{bankIssuing,jdbcType=VARCHAR},</if>
            <if test="productType != null">#{productType,jdbcType=VARCHAR},</if>
            <if test="originalMoney != null">#{originalMoney,jdbcType=VARCHAR},</if>
            <if test="cardholderCard != null">#{cardholderCard,jdbcType=VARCHAR},</if>
            <if test="installmentNum != null">#{installmentNum,jdbcType=VARCHAR},</if>
            <if test="merchantRemark != null">#{merchantRemark,jdbcType=VARCHAR},</if>
            <if test="dealDate != null">#{dealDate,jdbcType=TIMESTAMP},</if>
            <if test="dealTime != null">#{dealTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="dealMoney != null">#{dealMoney,jdbcType=DECIMAL},</if>
            <if test="settleMoney != null">#{settleMoney,jdbcType=DECIMAL},</if>
            <if test="serviceCharge != null">#{serviceCharge,jdbcType=DECIMAL},</if>
            <if test="installmentFee != null">#{installmentFee,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
</table>
