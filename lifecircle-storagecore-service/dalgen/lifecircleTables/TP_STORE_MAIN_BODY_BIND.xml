<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_STORE_MAIN_BODY_BIND" physicalName="TP_STORE_MAIN_BODY_BIND"
       remark="直连乐刷门店独立结算门店与主体绑定表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_STORE_MAIN_BODY_BIND">
        INSERT INTO TP_STORE_MAIN_BODY_BIND
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="alipaySmid != null">`ALIPAY_SMID`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="platformOrgId != null">`PLATFORM_ORG_ID`,</if>
            <if test="wechatSubMchId != null">`WECHAT_SUB_MCH_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="alipaySmid != null">#{alipaySmid,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="platformOrgId != null">#{platformOrgId,jdbcType=VARCHAR},</if>
            <if test="wechatSubMchId != null">#{wechatSubMchId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="applyId != null">#{applyId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation multiplicity="one" name="getByStoreId" remark="通过门店ID查询绑定记录">
        SELECT *
        FROM `tp_store_main_body_bind`
        WHERE `store_id` = #{storeId,jdbcType=INTEGER}
        and `platform_org_id` = #{platformOrgId,jdbcType=VARCHAR}
        and `start_time` &lt; NOW()
        ORDER BY `start_time` desc,id DESC
        limit 1
    </operation>

</table>
