<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WEB_BANK" physicalName="TP_LIFECIRCLE_WEB_BANK"
       remark="TP_LIFECIRCLE_WEB_BANK">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WEB_BANK">
        INSERT INTO TP_LIFECIRCLE_WEB_BANK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="bgId != null">`BG_ID`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="appLogo != null">`APP_LOGO`,</if>
            <if test="bgColor != null">`BG_COLOR`,</if>
            <if test="bigLogo != null">`BIG_LOGO`,</if>
            <if test="fullName != null">`FULL_NAME`,</if>
            <if test="shortName != null">`SHORT_NAME`,</if>
            <if test="littleLogo != null">`LITTLE_LOGO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="bgId != null">#{bgId,jdbcType=INTEGER},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="appLogo != null">#{appLogo,jdbcType=VARCHAR},</if>
            <if test="bgColor != null">#{bgColor,jdbcType=VARCHAR},</if>
            <if test="bigLogo != null">#{bigLogo,jdbcType=VARCHAR},</if>
            <if test="fullName != null">#{fullName,jdbcType=VARCHAR},</if>
            <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
            <if test="littleLogo != null">#{littleLogo,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation name="getBankInfoByShortName" multiplicity="one" remark="根据shortName得到银行卡明细">
        SELECT
        *
        FROM
        tp_lifecircle_web_bank
        where
        full_name like concat('%', #{shortName,jdbcType=VARCHAR} , '%')
        limit 1
    </operation>

    <operation multiplicity="one" name="getByBankNo" remark="根据银行代码查询">
        SELECT
        id,
        bg_id,
        bank_no,
        app_logo,
        bg_color,
        big_logo,
        full_name,
        short_name,
        little_logo,
        bank_package_logo,
        bank_package_img

        FROM tp_lifecircle_web_bank
        WHERE bank_no = #{bankNo,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="findBankInfoByShortName" multiplicity="one" remark="根据shortName得到银行卡明细">
        SELECT
        *
        FROM
        tp_lifecircle_web_bank
        where
        short_name = #{shortName,jdbcType=VARCHAR}
        limit 1
    </operation>

    <resultmap name="pWebBank" type="PWebBankDO">
        <column javatype="String" jdbctype="string" name="bank_no" remark="超级网银号"/>
        <column javatype="String" jdbctype="string" name="short_name" remark="银行名"/>
        <column javatype="String" jdbctype="string" name="bank_code" remark="银行编号"/>
    </resultmap>
    <operation multiplicity="many" name="getAllBank" paramtype="object" resultmap="pWebBank">
        SELECT wb.bank_no,wb.short_name,pb.bank_code
        FROM tp_lifecircle_web_bank wb
        LEFT JOIN tp_pa_bank_code pb
        on wb.short_name=pb.bank_name
        <trim prefix="where" prefixOverrides="AND|OR">
            <if test="shortName !=null and shortName !=''">
                wb.short_name like CONCAT('%',#{shortName,jdbcType=VARCHAR},'%')
            </if>
        </trim>
    </operation>

    <operation name="updateBankImgByFullName" paramtype="primitive" remark="update:updateBankImgByFullName">
        update tp_lifecircle_web_bank set bank_package_logo=#{bankPackageLogo,jdbcType=VARCHAR},
        bank_package_img=#{bankPackageImg,jdbcType=VARCHAR}
        where full_name = #{fullName,jdbcType=VARCHAR}
    </operation>
</table>
