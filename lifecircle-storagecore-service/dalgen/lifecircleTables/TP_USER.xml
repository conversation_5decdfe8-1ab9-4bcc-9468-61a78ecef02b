<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USER" physicalName="TP_USER"
       remark="代理商/受理商 信息表">

    <operation remark="根据ID批量获取名称" name="findUserNameByIds" multiplicity="many">
        SELECT id,username
        FROM tp_user
        WHERE id in
        <foreach collection="list" item="merchantId" open="(" close=")" separator=",">
            #{merchantId,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation remark="根据ID批量获取受理商名称" name="findSalesmanUserNameByIds" multiplicity="many">
        SELECT id,username
        FROM tp_user
        WHERE is_salesman = 1 AND id in
        <foreach collection="list" item="merchantId" open="(" close=")" separator=",">
            #{merchantId,jdbcType=INTEGER}
        </foreach>
    </operation>
    <operation name="getBelongByIdAndSianaActiv" remark="查询所属代理商">
        SELECT * FROM `tp_user` WHERE `id` =#{id,jdbcType=INTEGER}
    </operation>

    <operation name="getBelongById">
        SELECT * FROM `tp_user` WHERE `id` =#{id,jdbcType=INTEGER}
    </operation>

    <operation name="getByUsername">
        SELECT id,password,is_salesman as isSalesman,is_pass as isPass,status,viptime,platform,belong FROM tp_user WHERE
        username=#{username,jdbcType=VARCHAR} AND role = 10 AND status != 4
    </operation>

    <operation name="findListByUserNameList" paramtype="primitive" multiplicity="many"
               remark="根据用户名批量获取代理商信息">
        SELECT * FROM tp_user WHERE username in
        <foreach collection="list" item="username" open="(" close=")" separator=",">
            #{username,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getById" multiplicity="one" remark="get:TP_USER">
        SELECT *
        FROM TP_USER
        WHERE
        ID
        = #{id,jdbcType=INTEGER}
    </operation>
</table>
