<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_EQUIPMENT_SN" physicalName="TP_EQUIPMENT_SN"
       remark="硬件sn码记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_EQUIPMENT_SN">
        INSERT INTO TP_EQUIPMENT_SN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="initSn != null">`INIT_SN`,</if>
            <if test="systemSn != null">`SYSTEM_SN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="depot != null">`DEPOT`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="grantId != null">`GRANT_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="lostTime != null">`LOST_TIME`,</if>
            <if test="snStatus != null">`SN_STATUS`,</if>
            <if test="cashierId != null">`CASHIER_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="rejectTime != null">`REJECT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cashierMode != null">`CASHIER_MODE`,</if>
            <if test="equipmentId != null">`EQUIPMENT_ID`,</if>
            <if test="receiveTime != null">`RECEIVE_TIME`,</if>
            <if test="recoverTime != null">`RECOVER_TIME`,</if>
            <if test="damageStatus != null">`DAMAGE_STATUS`,</if>
            <if test="receiveStatus != null">`RECEIVE_STATUS`,</if>
            <if test="businessStatus != null">`BUSINESS_STATUS`,</if>
            <if test="distributeTime != null">`DISTRIBUTE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="initSn != null">#{initSn,jdbcType=VARCHAR},</if>
            <if test="systemSn != null">#{systemSn,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="depot != null">#{depot,jdbcType=TINYINT},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="lostTime != null">#{lostTime,jdbcType=INTEGER},</if>
            <if test="snStatus != null">#{snStatus,jdbcType=TINYINT},</if>
            <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="rejectTime != null">#{rejectTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="cashierMode != null">#{cashierMode,jdbcType=TINYINT},</if>
            <if test="equipmentId != null">#{equipmentId,jdbcType=INTEGER},</if>
            <if test="receiveTime != null">#{receiveTime,jdbcType=INTEGER},</if>
            <if test="recoverTime != null">#{recoverTime,jdbcType=INTEGER},</if>
            <if test="damageStatus != null">#{damageStatus,jdbcType=TINYINT},</if>
            <if test="receiveStatus != null">#{receiveStatus,jdbcType=TINYINT},</if>
            <if test="businessStatus != null">#{businessStatus,jdbcType=TINYINT},</if>
            <if test="distributeTime != null">#{distributeTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>
    <resultmap name="UidAndInitSnResultMap" type="UidAndInitSnResultMap">
        <column name="uid" javatype="Integer" jdbctype="INTEGER" remark="用户 ID"/>
        <column name="init_Sn" javatype="String" jdbctype="VARCHAR" remark="设备号"/>
        <column name="SN_STATUS" javatype="Integer" jdbctype="INTEGER" remark="设备状态"/>
    </resultmap>
    <operation name="batchFindUidListByInitSnList" multiplicity="many" resultmap="UidAndInitSnResultMap"
               remark="根据InitSn 列表获得 uid 列表">
        SELECT
        a.init_sn,
        a.uid uid,
        a.SN_STATUS
        FROM
        tp_equipment_sn a
        <where>
            a.init_sn IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=VARCHAR}
            </foreach>
        </where>
    </operation>

    <operation name="associationAgent" paramtype="primitive" remark="设备关联代理商">
        UPDATE tp_equipment_sn
        SET
        agent_id = #{agentId,jdbcType=INTEGER}
        WHERE init_sn = #{initSn,jdbcType=VARCHAR}
        AND is_del = 0
    </operation>
</table>
