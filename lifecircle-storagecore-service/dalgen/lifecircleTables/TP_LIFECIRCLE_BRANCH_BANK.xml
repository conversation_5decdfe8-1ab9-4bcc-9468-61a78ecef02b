<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_BRANCH_BANK" physicalName="TP_LIFECIRCLE_BRANCH_BANK"
       remark="支行表(新)">

    <operation name="getByBankCode" multiplicity="one" remark="根据bankCode查询支行信息">
        select *
        from
        TP_LIFECIRCLE_BRANCH_BANK
        where
        bank_code = #{bankCode,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getByBankName" multiplicity="one" remark="根据bankName查询支行信息">
        select *
        from
        TP_LIFECIRCLE_BRANCH_BANK
        where
        bank_name = #{bankName,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
</table>
