<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CRM_STORE_OPERATE_LOG" physicalName="TP_CRM_STORE_OPERATE_LOG" remark="TP_CRM_STORE_OPERATE_LOG">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CRM_STORE_OPERATE_LOG">
        INSERT INTO TP_CRM_STORE_OPERATE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            UID,
            TOKEN,
            STORE_ID,
            IS_ONLINE,
            <if test="recordId != null">RECORD_ID,</if>
            <if test="createTime != null">CREATE_TIME,</if>
            OPERATOR_ID,
            <if test="updateTime != null">UPDATE_TIME,</if>
            OPERATE_TIME,
            OPERATE_TYPE,
            PRODUCT_NAME,
            <if test="snapshootId != null">SNAPSHOOT_ID,</if>
            OPERATOR_NAME,
            <if test="dismissReason != null">DISMISS_REASON,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            #{uid,jdbcType=INTEGER},
            #{token,jdbcType=VARCHAR},
            #{storeId,jdbcType=INTEGER},
            #{isOnline,jdbcType=TINYINT},
            <if test="recordId != null">#{recordId,jdbcType=BIGINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            #{operatorId,jdbcType=VARCHAR},
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            #{operateTime,jdbcType=TIMESTAMP},
            #{operateType,jdbcType=INTEGER},
            #{productName,jdbcType=INTEGER},
            <if test="snapshootId != null">#{snapshootId,jdbcType=BIGINT},</if>
            #{operatorName,jdbcType=VARCHAR},
            <if test="dismissReason != null">#{dismissReason,jdbcType=VARCHAR},</if>
        </trim>
    </operation>


    <operation name="update" paramtype="object" remark="update table:TP_CRM_STORE_OPERATE_LOG">
        UPDATE TP_CRM_STORE_OPERATE_LOG
        SET
            UID             = #{uid,jdbcType=INTEGER}
            ,TOKEN           = #{token,jdbcType=VARCHAR}
            ,STORE_ID        = #{storeId,jdbcType=INTEGER}
            ,IS_ONLINE       = #{isOnline,jdbcType=TINYINT}
            ,RECORD_ID       = #{recordId,jdbcType=BIGINT}
            ,CREATE_TIME     = #{createTime,jdbcType=TIMESTAMP}
            ,OPERATOR_ID     = #{operatorId,jdbcType=VARCHAR}
            ,UPDATE_TIME     = #{updateTime,jdbcType=TIMESTAMP}
            ,OPERATE_TIME    = #{operateTime,jdbcType=TIMESTAMP}
            ,OPERATE_TYPE    = #{operateType,jdbcType=INTEGER}
            ,PRODUCT_NAME    = #{productName,jdbcType=INTEGER}
            ,SNAPSHOOT_ID    = #{snapshootId,jdbcType=BIGINT}
            ,OPERATOR_NAME   = #{operatorName,jdbcType=VARCHAR}
            ,DISMISS_REASON  = #{dismissReason,jdbcType=VARCHAR}
        WHERE
            ID              = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="deleteByPrimary" multiplicity="one" remark="delete:TP_CRM_STORE_OPERATE_LOG">
        DELETE FROM
            TP_CRM_STORE_OPERATE_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="getByPrimary" multiplicity="one" remark="get:TP_CRM_STORE_OPERATE_LOG">
        SELECT *
        FROM TP_CRM_STORE_OPERATE_LOG
        WHERE
            ID = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="getStoreLogByStoreId" multiplicity="many" remark="根据门店id日志信息">
        select * from tp_crm_store_operate_log where store_id = #{storeId,jdbcType=INTEGER} order by operate_time ASC
    </operation>
</table>
