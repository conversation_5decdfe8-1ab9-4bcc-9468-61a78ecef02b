<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_OPEN" physicalName="TP_LIFECIRCLE_OPEN"
       remark="TP_LIFECIRCLE_OPEN">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_OPEN">
        INSERT INTO TP_LIFECIRCLE_OPEN
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appId != null">`APP_ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="callback != null">`CALLBACK`,</if>
            <if test="appSecret != null">`APP_SECRET`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="secondCallbackUrl != null">`SECOND_CALLBACK_URL`,</if>
            <if test="remitCallbackUrl != null">`REMIT_CALLBACK_URL`,</if>
            <if test="refundCallbackUrl != null">`REFUND_CALLBACK_URL`,</if>
            <if test="withdrawCallbackUrl != null">`WITHDRAW_CALLBACK_URL`,</if>
            <if test="accountRegisterCallbackUrl != null">`ACCOUNT_REGISTER_CALLBACK_URL`,</if>
            <if test="shareCallbackUrl != null">`SHARE_CALLBACK_URL`,</if>
            <if test="shareAuditCallbackUrl != null">`SHARE_AUDIT_CALLBACK_URL`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appId != null">#{appId,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=INTEGER},</if>
            <if test="source != null">#{source,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="callback != null">#{callback,jdbcType=VARCHAR},</if>
            <if test="appSecret != null">#{appSecret,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
            <if test="secondCallbackUrl != null">#{secondCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="remitCallbackUrl != null">#{remitCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="refundCallbackUrl != null">#{refundCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="withdrawCallbackUrl != null">#{withdrawCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="accountRegisterCallbackUrl != null">#{accountRegisterCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="shareCallbackUrl != null">#{shareCallbackUrl,jdbcType=VARCHAR},</if>
            <if test="shareAuditCallbackUrl != null">#{shareAuditCallbackUrl,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation name="getByToken" multiplicity="one">
        SELECT * FROM TP_LIFECIRCLE_OPEN
        WHERE
        token =#{token,jdbcType=INTEGER} LIMIT 1
    </operation>

    <operation name="updateCallBack" paramtype="object" remark="更新回调地址">
        UPDATE
        tp_lifecircle_open
        SET
        <if test="callback != null">
            CALLBACK = #{callback,jdbcType=VARCHAR},
        </if>
        <if test="secondCallbackUrl != null">
            SECOND_CALLBACK_URL = #{secondCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="remitCallbackUrl != null">
            REMIT_CALLBACK_URL = #{remitCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="refundCallbackUrl != null">
            REFUND_CALLBACK_URL = #{refundCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="withdrawCallbackUrl != null">
            WITHDRAW_CALLBACK_URL = #{withdrawCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="accountRegisterCallbackUrl != null">
            ACCOUNT_REGISTER_CALLBACK_URL = #{accountRegisterCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="shareCallbackUrl != null">
            SHARE_CALLBACK_URL = #{shareCallbackUrl,jdbcType=VARCHAR},
        </if>
        <if test="shareAuditCallbackUrl != null">
            SHARE_AUDIT_CALLBACK_URL = #{shareAuditCallbackUrl,jdbcType=VARCHAR},
        </if>
        UPDATE_TIME = #{updateTime,jdbcType=INTEGER}
        WHERE
        token = #{token,jdbcType=INTEGER}
    </operation>

    <operation name="updateAppSecret" paramtype="object" remark="更新ID及秘钥信息">
        UPDATE
        tp_lifecircle_open
        SET
        <if test="appId != null">
            app_Id = #{appId,jdbcType=VARCHAR},
        </if>
        <if test="appSecret != null">
            app_Secret = #{appSecret,jdbcType=VARCHAR},
        </if>
        <if test="callback != null">
            CALLBACK = #{callback,jdbcType=VARCHAR},
        </if>
        UPDATE_TIME = #{updateTime,jdbcType=INTEGER}
        WHERE
        token = #{token,jdbcType=INTEGER}
    </operation>


    <operation name="getByAppId" multiplicity="one">
        SELECT * FROM TP_LIFECIRCLE_OPEN
        WHERE
        app_id =#{appId,jdbcType=VARCHAR} LIMIT 1
    </operation>
</table>
