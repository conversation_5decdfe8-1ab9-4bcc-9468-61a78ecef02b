<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_BALANCE_ACCOUNT_FREEZE_RECORD" physicalName="TP_BALANCE_ACCOUNT_FREEZE_RECORD"
    remark="账户冻结表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_BALANCE_ACCOUNT_FREEZE_RECORD">
INSERT INTO TP_BALANCE_ACCOUNT_FREEZE_RECORD
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="reason != null">`REASON`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="settlementOrderSn != null">`SETTLEMENT_ORDER_SN`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="isShow != null">`IS_SHOW`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="tradeDate != null">`TRADE_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="freezeMoney != null">`FREEZE_MONEY`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="settlementOrderSn != null">#{settlementOrderSn,jdbcType=VARCHAR},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="isShow != null">#{isShow,jdbcType=TINYINT},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="tradeDate != null">#{tradeDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="freezeMoney != null">#{freezeMoney,jdbcType=DECIMAL},</if>
    </trim>
    </operation>
    </table>
