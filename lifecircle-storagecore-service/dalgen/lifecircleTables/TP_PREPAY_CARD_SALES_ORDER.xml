<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_SALES_ORDER" physicalName="TP_PREPAY_CARD_SALES_ORDER"
       remark="销售单主表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_SALES_ORDER">
        INSERT INTO TP_PREPAY_CARD_SALES_ORDER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="invoiceUrl != null">`INVOICE_URL`,</if>
            <if test="contractUrl != null">`CONTRACT_URL`,</if>
            <if test="handlerName != null">`HANDLER_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="contactEmail != null">`CONTACT_EMAIL`,</if>
            <if test="contactPhone != null">`CONTACT_PHONE`,</if>
            <if test="customerName != null">`CUSTOMER_NAME`,</if>
            <if test="receiptOrder != null">`RECEIPT_ORDER`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="contactAddress != null">`CONTACT_ADDRESS`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="orderSource != null">`ORDER_SOURCE`,</if>
            <if test="orderStatus != null">`ORDER_STATUS`,</if>
            <if test="deliveryTime != null">`DELIVERY_TIME`,</if>
            <if test="totalCardNumber != null">`TOTAL_CARD_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="totalCardReal != null">`TOTAL_CARD_REAL`,</if>
            <if test="totalCardPrice != null">`TOTAL_CARD_PRICE`,</if>
            <if test="totalCardAmount != null">`TOTAL_CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="invoiceUrl != null">#{invoiceUrl,jdbcType=VARCHAR},</if>
            <if test="contractUrl != null">#{contractUrl,jdbcType=VARCHAR},</if>
            <if test="handlerName != null">#{handlerName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="contactEmail != null">#{contactEmail,jdbcType=VARCHAR},</if>
            <if test="contactPhone != null">#{contactPhone,jdbcType=VARCHAR},</if>
            <if test="customerName != null">#{customerName,jdbcType=VARCHAR},</if>
            <if test="receiptOrder != null">#{receiptOrder,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="contactAddress != null">#{contactAddress,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="orderSource != null">#{orderSource,jdbcType=TINYINT},</if>
            <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
            <if test="deliveryTime != null">#{deliveryTime,jdbcType=INTEGER},</if>
            <if test="totalCardNumber != null">#{totalCardNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="totalCardReal != null">#{totalCardReal,jdbcType=DECIMAL},</if>
            <if test="totalCardPrice != null">#{totalCardPrice,jdbcType=DECIMAL},</if>
            <if test="totalCardAmount != null">#{totalCardAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="updateOrderStatusBySalesOrderNo" paramtype="primitive" remark="根据单号修改订单状态">
        UPDATE TP_PREPAY_CARD_SALES_ORDER
        SET
        order_status = #{orderStatus,jdbcType=TINYINT}
        WHERE
        is_del = 0
        AND sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
    </operation>

    <operation name="updateOrderBySalesOrderNo" paramtype="object" remark="根据销售单号修改销售单状态">
        UPDATE tp_prepay_card_sales_order
        SET order_status = #{orderStatus,jdbcType=TINYINT}
        <if test="deliveryTime != null and deliveryTime != 0">
            ,delivery_time = #{deliveryTime,jdbcType=INTEGER}
        </if>
        WHERE is_del = 0
        AND sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
    </operation>
</table>
