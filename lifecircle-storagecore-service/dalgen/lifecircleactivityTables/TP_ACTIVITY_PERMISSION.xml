<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ACTIVITY_PERMISSION" physicalName="TP_ACTIVITY_PERMISSION" remark="TP_ACTIVITY_PERMISSION">
    <!--    &lt;&gt;   <> -->

    <operation name="insert" paramtype="object" remark="insert:TP_ACTIVITY_PERMISSION">
        <selectKey resultType="java.lang.Long" keyProperty="id" order="AFTER">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_ACTIVITY_PERMISSION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="role != null">`ROLE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="userId != null">`USER_ID`,</if>
            <if test="endTime != null">`END_TIME`,</if>
            <if test="describe != null">`DESCRIBE`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="activityId != null">`ACTIVITY_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="role != null">#{role,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
            <if test="endTime != null">#{endTime,jdbcType=INTEGER},</if>
            <if test="describe != null">#{describe,jdbcType=VARCHAR},</if>
            <if test="startTime != null"> #{startTime,jdbcType=INTEGER},</if>
            <if test="activityId != null"> #{activityId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=INTEGER},</if>
        </trim>
    </operation>

    <operation name="getByUidActivityId" multiplicity="one"
               remark="根据商户id角色活动id查询活动权限">
        SELECT
        `id`,
        `describe`,
        `activity_id` as activityId,
        `role`,
        `status`,
        `user_id` as userId,
        `end_time` as endTime,
        `start_time` as startTime,
        `create_time` as createTime,
        `update_time` as updateTime
        from tp_activity_permission
        WHERE
        `user_id` = #{uid, jdbcType=INTEGER}
        and `role` = 3
        and `activity_id` = #{activityId, jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="updateById" paramtype="object" remark="update table:TP_ACTIVITY_PERMISSION">
        UPDATE TP_ACTIVITY_PERMISSION
        <set>
            <if test="role != null">`ROLE` = #{role,jdbcType=TINYINT},</if>
            <if test="status != null">`STATUS` = #{status,jdbcType=TINYINT},</if>
            <if test="userId != null">`USER_ID` = #{userId,jdbcType=INTEGER},</if>
            <if test="endTime != null">`END_TIME` = #{endTime,jdbcType=INTEGER},</if>
            <if test="describe != null">`DESCRIBE` = #{describe,jdbcType=VARCHAR},</if>
            <if test="startTime != null">`START_TIME` = #{startTime,jdbcType=INTEGER},</if>
            <if test="activityId != null">`ACTIVITY_ID` = #{activityId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">`CREATE_TIME` = #{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">`UPDATE_TIME` = #{updateTime,jdbcType=TIMESTAMP},</if>
        </set>
        WHERE
        `ID` = #{id,jdbcType=BIGINT}
    </operation>

</table>
