<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_CHANNEL" physicalName="TP_CHANNEL"
    remark="渠道配置表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_CHANNEL">
INSERT INTO TP_CHANNEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="serialNo != null">`SERIAL_NO`,</if>
        <if test="channelNum != null">`CHANNEL_NUM`,</if>
        <if test="jsapiPath1 != null">`JSAPI_PATH1`,</if>
        <if test="jsapiPath2 != null">`JSAPI_PATH2`,</if>
        <if test="jsapiPath3 != null">`JSAPI_PATH3`,</if>
        <if test="jsapiPath4 != null">`JSAPI_PATH4`,</if>
        <if test="jsapiPath5 != null">`JSAPI_PATH5`,</if>
        <if test="privateKey != null">`PRIVATE_KEY`,</if>
        <if test="subjectBody != null">`SUBJECT_BODY`,</if>
        <if test="wechatApiV2Key != null">`WECHAT_API_V2_KEY`,</if>
        <if test="wechatApiV3Key != null">`WECHAT_API_V3_KEY`,</if>
        <if test="status != null">`STATUS`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="channelId != null">`CHANNEL_ID`,</if>
        <if test="appletIncome != null">`APPLET_INCOME`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="serialNo != null">#{serialNo,jdbcType=VARCHAR},</if>
        <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
        <if test="jsapiPath1 != null">#{jsapiPath1,jdbcType=VARCHAR},</if>
        <if test="jsapiPath2 != null">#{jsapiPath2,jdbcType=VARCHAR},</if>
        <if test="jsapiPath3 != null">#{jsapiPath3,jdbcType=VARCHAR},</if>
        <if test="jsapiPath4 != null">#{jsapiPath4,jdbcType=VARCHAR},</if>
        <if test="jsapiPath5 != null">#{jsapiPath5,jdbcType=VARCHAR},</if>
        <if test="privateKey != null">#{privateKey,jdbcType=VARCHAR},</if>
        <if test="subjectBody != null">#{subjectBody,jdbcType=VARCHAR},</if>
        <if test="wechatApiV2Key != null">#{wechatApiV2Key,jdbcType=VARCHAR},</if>
        <if test="wechatApiV3Key != null">#{wechatApiV3Key,jdbcType=VARCHAR},</if>
        <if test="status != null">#{status,jdbcType=TINYINT},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
        <if test="appletIncome != null">#{appletIncome,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>
    
    <operation name="getByChannelId" multiplicity="one" remark="根据渠道号ID查询">
        select * from TP_CHANNEL
        where channel_id = #{channelId,jdbcType=INTEGER}
    </operation>
</table>
