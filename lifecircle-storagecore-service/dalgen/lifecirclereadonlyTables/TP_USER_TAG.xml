<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USER_TAG" physicalName="TP_USER_TAG"
       remark="代理商标签关系表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USER_TAG">
        INSERT INTO TP_USER_TAG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="tag != null">`TAG`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="sailTag != null">`SAIL_TAG`,</if>
            <if test="startTime != null">`START_TIME`,</if>
            <if test="oldSailTag != null">`OLD_SAIL_TAG`,</if>
            <if test="policyEndTime != null">`POLICY_END_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="tag != null">#{tag,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="sailTag != null">#{sailTag,jdbcType=TINYINT},</if>
            <if test="startTime != null">#{startTime,jdbcType=INTEGER},</if>
            <if test="oldSailTag != null">#{oldSailTag,jdbcType=TINYINT},</if>
            <if test="policyEndTime != null">#{policyEndTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>
    <operation name="getByAgentId" paramtype="primitive" multiplicity="one"
               remark="根据代理商id获取代理商标签">
        SELECT *
        FROM TP_USER_TAG
        WHERE AGENT_ID = #{agentId,jdbcType=INTEGER}
    </operation>
</table>
