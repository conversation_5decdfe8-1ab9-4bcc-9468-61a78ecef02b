<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_REFUND_DETAIL" physicalName="TP_PREPAY_CARD_REFUND_DETAIL"
       remark="退货明细单表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_REFUND_DETAIL">
        INSERT INTO TP_PREPAY_CARD_REFUND_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="refundNo != null">`REFUND_NO`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="salesOrderNo != null">`SALES_ORDER_NO`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="refundNumber != null">`REFUND_NUMBER`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="refundNo != null">#{refundNo,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="salesOrderNo != null">#{salesOrderNo,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="refundNumber != null">#{refundNumber,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findListBySalesOrderNo" multiplicity="many" remark="根据销售单no查询退款详情列表">
        SELECT
        *
        FROM tp_prepay_card_refund_detail
        WHERE
        sales_order_no = #{salesOrderNo,jdbcType=VARCHAR}
    </operation>

    <operation name="findRefundOrderDetailByAppointTime" multiplicity="many" remark="查询预付卡退货订单">
        SELECT
        *
        FROM tp_prepay_card_refund_detail
        WHERE is_del = 0
        and `create_time` <![CDATA[ >= ]]> #{startTime,jdbcType=TIMESTAMP}
        and `create_time` <![CDATA[ <= ]]> #{endTime,jdbcType=TIMESTAMP}
        and publish_org_id in
        <foreach close=")" collection="list" index="index" item="publishOrgId" open="(" separator=",">
            #{publishOrgId, jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
