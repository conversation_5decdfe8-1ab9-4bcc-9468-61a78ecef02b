<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_CONSUME" physicalName="TP_LIFECIRCLE_CONSUME"
    remark="订单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_CONSUME">
INSERT INTO TP_LIFECIRCLE_CONSUME
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="openid != null">`OPENID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="bankType != null">`BANK_TYPE`,</if>
        <if test="discount != null">`DISCOUNT`,</if>
        <if test="payToken != null">`PAY_TOKEN`,</if>
        <if test="callBackUrl != null">`CALL_BACK_URL`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="mode != null">`MODE`,</if>
        <if test="type != null">`TYPE`,</if>
        <if test="mchid != null">`MCHID`,</if>
        <if test="redId != null">`RED_ID`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="payTime != null">`PAY_TIME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="markread != null">`MARKREAD`,</if>
        <if test="printNum != null">`PRINT_NUM`,</if>
        <if test="redPreId != null">`RED_PRE_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="payStatus != null">`PAY_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="discountId != null">`DISCOUNT_ID`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="rechargeactId != null">`RECHARGEACT_ID`,</if>
        <if test="preferentialId != null">`PREFERENTIAL_ID`,</if>
        <if test="repairOrderStatus != null">`REPAIR_ORDER_STATUS`,</if>
        <if test="updateTimeAuto != null">`UPDATE_TIME_AUTO`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="refund != null">`REFUND`,</if>
        <if test="cashFee != null">`CASH_FEE`,</if>
        <if test="rateFee != null">`RATE_FEE`,</if>
        <if test="redMoney != null">`RED_MONEY`,</if>
        <if test="couponFee != null">`COUPON_FEE`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="commissionFee != null">`COMMISSION_FEE`,</if>
        <if test="discountMoney != null">`DISCOUNT_MONEY`,</if>
        <if test="orderSumprice != null">`ORDER_SUMPRICE`,</if>
        <if test="autowipingzero != null">`AUTOWIPINGZERO`,</if>
        <if test="additionalPrice != null">`ADDITIONAL_PRICE`,</if>
        <if test="commissionRateFee != null">`COMMISSION_RATE_FEE`,</if>
        <if test="rechargeactAmount != null">`RECHARGEACT_AMOUNT`,</if>
        <if test="preferentialAmount != null">`PREFERENTIAL_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="openid != null">#{openid,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="bankType != null">#{bankType,jdbcType=VARCHAR},</if>
        <if test="discount != null">#{discount,jdbcType=VARCHAR},</if>
        <if test="payToken != null">#{payToken,jdbcType=VARCHAR},</if>
        <if test="callBackUrl != null">#{callBackUrl,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="mode != null">#{mode,jdbcType=TINYINT},</if>
        <if test="type != null">#{type,jdbcType=TINYINT},</if>
        <if test="mchid != null">#{mchid,jdbcType=TINYINT},</if>
        <if test="redId != null">#{redId,jdbcType=INTEGER},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="markread != null">#{markread,jdbcType=TINYINT},</if>
        <if test="printNum != null">#{printNum,jdbcType=TINYINT},</if>
        <if test="redPreId != null">#{redPreId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="discountId != null">#{discountId,jdbcType=INTEGER},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="rechargeactId != null">#{rechargeactId,jdbcType=INTEGER},</if>
        <if test="preferentialId != null">#{preferentialId,jdbcType=INTEGER},</if>
        <if test="repairOrderStatus != null">#{repairOrderStatus,jdbcType=INTEGER},</if>
        <if test="updateTimeAuto != null">#{updateTimeAuto,jdbcType=TIMESTAMP},</if>
        <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
        <if test="refund != null">#{refund,jdbcType=DECIMAL},</if>
        <if test="cashFee != null">#{cashFee,jdbcType=DECIMAL},</if>
        <if test="rateFee != null">#{rateFee,jdbcType=DECIMAL},</if>
        <if test="redMoney != null">#{redMoney,jdbcType=DECIMAL},</if>
        <if test="couponFee != null">#{couponFee,jdbcType=DECIMAL},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
        <if test="commissionFee != null">#{commissionFee,jdbcType=DECIMAL},</if>
        <if test="discountMoney != null">#{discountMoney,jdbcType=DECIMAL},</if>
        <if test="orderSumprice != null">#{orderSumprice,jdbcType=DECIMAL},</if>
        <if test="autowipingzero != null">#{autowipingzero,jdbcType=DECIMAL},</if>
        <if test="additionalPrice != null">#{additionalPrice,jdbcType=DECIMAL},</if>
        <if test="commissionRateFee != null">#{commissionRateFee,jdbcType=DECIMAL},</if>
        <if test="rechargeactAmount != null">#{rechargeactAmount,jdbcType=DECIMAL},</if>
        <if test="preferentialAmount != null">#{preferentialAmount,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getMerchantOrderSnListByOrderSn" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantOrderSnDO"
               remark="根据订单号查询商户订单号">
        select
        order_sn as orderSn,merchant_order_sn as merchantOrderSn
        from tp_lifecircle_consume
        where
        order_sn in
        <foreach collection="list" open="(" item="orderSn" separator="," close=")">
            #{orderSn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    </table>
