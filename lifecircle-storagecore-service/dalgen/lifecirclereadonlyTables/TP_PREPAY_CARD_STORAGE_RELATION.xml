<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_PREPAY_CARD_STORAGE_RELATION" physicalName="TP_PREPAY_CARD_STORAGE_RELATION"
    remark="预付卡出入库关联表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_STORAGE_RELATION">
INSERT INTO TP_PREPAY_CARD_STORAGE_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
        <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
        <if test="storageOrder != null">`STORAGE_ORDER`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
        <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
        <if test="storageOrder != null">#{storageOrder,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>


    <operation name="findListStorageRecordByStorageOrder" multiplicity="many" remark="根据出入库订单号查询预付卡出入库关联列表">
        SELECT
        *
        FROM
        TP_PREPAY_CARD_STORAGE_RELATION
        WHERE
        is_del = 0
        AND storage_order = #{storageOrder, jdbcType=VARCHAR}
    </operation>

    <operation name="findListCardStorageRelationSegmentByCardNos" multiplicity="many"
               remark="根据出入库单号和卡号查询出入库记录明细"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.model.prepaycard.PrepayCardRefundCardInfoModel">
        SELECT
        t1.card_sku_id cardSkuId,
        t1.card_no cardNo,
        t2.card_price cardPrice
        FROM
        TP_PREPAY_CARD_STORAGE_RELATION t1
        left join tp_prepay_card t2 on t1.card_no = t2.card_no
        WHERE
        t1.is_del = 0 and t2.is_del = 0
        AND t1.storage_order = #{storageOrder, jdbcType=VARCHAR}
        AND t1.`card_no` IN
        <foreach close=")" collection="cardNos" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
    </operation>


    <operation name="findStorageRelationByStorageOrderNoList" multiplicity="many" remark="根据出库订单查询出库关联订单信息">
        select
        *
        from tp_prepay_card_storage_relation
        where storage_order in
        <foreach collection="list" item="orderNo" open="(" close=")" separator=",">
            #{orderNo, jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="findStorageRelationByStorageOrderNoListAndOperateType" multiplicity="many" remark="根据关联订单查询卡信息列表">
        select
        relation.*
        from TP_PREPAY_CARD_STORAGE_RECORD record
        left join tp_prepay_card_storage_relation relation on record.storage_order = relation.storage_order
        where record.storage_order in
        <foreach collection="list" item="storageOrderNo" open="(" close=")" separator=",">
            #{storageOrderNo, jdbcType=VARCHAR}
        </foreach>
        and record.operate_type = #{operateType,jdbcType=INTEGER}
    </operation>
</table>
