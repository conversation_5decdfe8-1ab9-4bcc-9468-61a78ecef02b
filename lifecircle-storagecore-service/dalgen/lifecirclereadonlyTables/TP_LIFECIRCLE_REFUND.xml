<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_REFUND" physicalName="TP_LIFECIRCLE_REFUND"
    remark="TP_LIFECIRCLE_REFUND">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_REFUND">
INSERT INTO TP_LIFECIRCLE_REFUND
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="ext5 != null">`EXT5`,</if>
        <if test="ext6 != null">`EXT6`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="orderSn != null">`ORDER_SN`,</if>
        <if test="deviceNo != null">`DEVICE_NO`,</if>
        <if test="refundSn != null">`REFUND_SN`,</if>
        <if test="notifyUrl != null">`NOTIFY_URL`,</if>
        <if test="activityId != null">`ACTIVITY_ID`,</if>
        <if test="refundCode != null">`REFUND_CODE`,</if>
        <if test="refundInfo != null">`REFUND_INFO`,</if>
        <if test="faceDeviceSn != null">`FACE_DEVICE_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="platformOrderSn != null">`PLATFORM_ORDER_SN`,</if>
        <if test="merchantRefundSn != null">`MERCHANT_REFUND_SN`,</if>
        <if test="platformRefundSn != null">`PLATFORM_REFUND_SN`,</if>
        <if test="liquidatorOrderSn != null">`LIQUIDATOR_ORDER_SN`,</if>
        <if test="goodsRefundOrderSn != null">`GOODS_REFUND_ORDER_SN`,</if>
        <if test="liquidatorRefundSn != null">`LIQUIDATOR_REFUND_SN`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="grantId != null">`GRANT_ID`,</if>
        <if test="handler != null">`HANDLER`,</if>
        <if test="payTime != null">`PAY_TIME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="marketId != null">`MARKET_ID`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="isMoreDay != null">`IS_MORE_DAY`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="settleMode != null">`SETTLE_MODE`,</if>
        <if test="consumeType != null">`CONSUME_TYPE`,</if>
        <if test="lastestTime != null">`LASTEST_TIME`,</if>
        <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
        <if test="isPartRefund != null">`IS_PART_REFUND`,</if>
        <if test="refundStatus != null">`REFUND_STATUS`,</if>
        <if test="refundAgentId != null">`REFUND_AGENT_ID`,</if>
        <if test="specialSettle != null">`SPECIAL_SETTLE`,</if>
        <if test="consumeChannel != null">`CONSUME_CHANNEL`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="isPositionRefund != null">`IS_POSITION_REFUND`,</if>
        <if test="organizationType != null">`ORGANIZATION_TYPE`,</if>
        <if test="refundFailedDeal != null">`REFUND_FAILED_DEAL`,</if>
        <if test="refundUpdateTime != null">`REFUND_UPDATE_TIME`,</if>
        <if test="refund != null">`REFUND`,</if>
        <if test="cashFee != null">`CASH_FEE`,</if>
        <if test="lifeFee != null">`LIFE_FEE`,</if>
        <if test="agentFee != null">`AGENT_FEE`,</if>
        <if test="poundage != null">`POUNDAGE`,</if>
        <if test="refundMoney != null">`REFUND_MONEY`,</if>
        <if test="agentRateFee != null">`AGENT_RATE_FEE`,</if>
        <if test="rechargeactAmount != null">`RECHARGEACT_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=INTEGER},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
        <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
        <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
        <if test="refundSn != null">#{refundSn,jdbcType=VARCHAR},</if>
        <if test="notifyUrl != null">#{notifyUrl,jdbcType=VARCHAR},</if>
        <if test="activityId != null">#{activityId,jdbcType=VARCHAR},</if>
        <if test="refundCode != null">#{refundCode,jdbcType=VARCHAR},</if>
        <if test="refundInfo != null">#{refundInfo,jdbcType=VARCHAR},</if>
        <if test="faceDeviceSn != null">#{faceDeviceSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="platformOrderSn != null">#{platformOrderSn,jdbcType=VARCHAR},</if>
        <if test="merchantRefundSn != null">#{merchantRefundSn,jdbcType=VARCHAR},</if>
        <if test="platformRefundSn != null">#{platformRefundSn,jdbcType=VARCHAR},</if>
        <if test="liquidatorOrderSn != null">#{liquidatorOrderSn,jdbcType=VARCHAR},</if>
        <if test="goodsRefundOrderSn != null">#{goodsRefundOrderSn,jdbcType=VARCHAR},</if>
        <if test="liquidatorRefundSn != null">#{liquidatorRefundSn,jdbcType=VARCHAR},</if>
        <if test="userId != null">#{userId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
        <if test="handler != null">#{handler,jdbcType=INTEGER},</if>
        <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="isMoreDay != null">#{isMoreDay,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=INTEGER},</if>
        <if test="settleMode != null">#{settleMode,jdbcType=TINYINT},</if>
        <if test="consumeType != null">#{consumeType,jdbcType=TINYINT},</if>
        <if test="lastestTime != null">#{lastestTime,jdbcType=INTEGER},</if>
        <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
        <if test="isPartRefund != null">#{isPartRefund,jdbcType=TINYINT},</if>
        <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
        <if test="refundAgentId != null">#{refundAgentId,jdbcType=INTEGER},</if>
        <if test="specialSettle != null">#{specialSettle,jdbcType=TINYINT},</if>
        <if test="consumeChannel != null">#{consumeChannel,jdbcType=TINYINT},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
        <if test="isPositionRefund != null">#{isPositionRefund,jdbcType=TINYINT},</if>
        <if test="organizationType != null">#{organizationType,jdbcType=TINYINT},</if>
        <if test="refundFailedDeal != null">#{refundFailedDeal,jdbcType=TINYINT},</if>
        <if test="refundUpdateTime != null">#{refundUpdateTime,jdbcType=TIMESTAMP},</if>
        <if test="refund != null">#{refund,jdbcType=DECIMAL},</if>
        <if test="cashFee != null">#{cashFee,jdbcType=DECIMAL},</if>
        <if test="lifeFee != null">#{lifeFee,jdbcType=DECIMAL},</if>
        <if test="agentFee != null">#{agentFee,jdbcType=DECIMAL},</if>
        <if test="poundage != null">#{poundage,jdbcType=DECIMAL},</if>
        <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
        <if test="agentRateFee != null">#{agentRateFee,jdbcType=DECIMAL},</if>
        <if test="rechargeactAmount != null">#{rechargeactAmount,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getMerchantRefundSnListByOrderSn" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantRefundSnDO"
               remark="根据订单号查询商户订单号">
        select
        refund_sn as refundSn,merchant_refund_sn as merchantRefundSn,merchant_order_sn as merchantOrderSn
        from tp_lifecircle_refund
        where
        refund_sn in
        <foreach collection="list" open="(" item="refundSn" separator="," close=")">
            #{refundSn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    </table>
