<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP" physicalName="TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP"
       remark="万达数据同步临时表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP">
        INSERT INTO TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="adId != null">`AD_ID`,</if>
            <if test="cityId != null">`CITY_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="provinceId != null">`PROVINCE_ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="owner != null">`OWNER`,</if>
            <if test="adName != null">`AD_NAME`,</if>
            <if test="bankNo != null">`BANK_NO`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="region != null">`REGION`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="license != null">`LICENSE`,</if>
            <if test="plazaId != null">`PLAZA_ID`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="cityName != null">`CITY_NAME`,</if>
            <if test="latitude != null">`LATITUDE`,</if>
            <if test="storePic != null">`STORE_PIC`,</if>
            <if test="errorCode != null">`ERROR_CODE`,</if>
            <if test="longitude != null">`LONGITUDE`,</if>
            <if test="plazaName != null">`PLAZA_NAME`,</if>
            <if test="storeName != null">`STORE_NAME`,</if>
            <if test="businessId != null">`BUSINESS_ID`,</if>
            <if test="traderName != null">`TRADER_NAME`,</if>
            <if test="bankCardPic != null">`BANK_CARD_PIC`,</if>
            <if test="cityCompany != null">`CITY_COMPANY`,</if>
            <if test="businessName != null">`BUSINESS_NAME`,</if>
            <if test="errorMessage != null">`ERROR_MESSAGE`,</if>
            <if test="idCardNumber != null">`ID_CARD_NUMBER`,</if>
            <if test="ownerBackUrl != null">`OWNER_BACK_URL`,</if>
            <if test="provinceName != null">`PROVINCE_NAME`,</if>
            <if test="unionpayCode != null">`UNIONPAY_CODE`,</if>
            <if test="accountMobile != null">`ACCOUNT_MOBILE`,</if>
            <if test="ownerFrontUrl != null">`OWNER_FRONT_URL`,</if>
            <if test="incomeErrorMsg != null">`INCOME_ERROR_MSG`,</if>
            <if test="insideScenePic != null">`INSIDE_SCENE_PIC`,</if>
            <if test="settlerIdCardNo != null">`SETTLER_ID_CARD_NO`,</if>
            <if test="businessPlacePic != null">`BUSINESS_PLACE_PIC`,</if>
            <if test="contactIdDocCopy != null">`CONTACT_ID_DOC_COPY`,</if>
            <if test="contactPeriodEnd != null">`CONTACT_PERIOD_END`,</if>
            <if test="licenseElectronic != null">`LICENSE_ELECTRONIC`,</if>
            <if test="settlerIdCardName != null">`SETTLER_ID_CARD_NAME`,</if>
            <if test="contactPeriodBegin != null">`CONTACT_PERIOD_BEGIN`,</if>
            <if test="ownerCertificateNo != null">`OWNER_CERTIFICATE_NO`,</if>
            <if test="contactIdDocCopyBack != null">`CONTACT_ID_DOC_COPY_BACK`,</if>
            <if test="settlerIdCardBackPic != null">`SETTLER_ID_CARD_BACK_PIC`,</if>
            <if test="settlerIdCardEndDate != null">`SETTLER_ID_CARD_END_DATE`,</if>
            <if test="authorizeSaveErrorMsg != null">`AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="settlerIdCardFrontPic != null">`SETTLER_ID_CARD_FRONT_PIC`,</if>
            <if test="settlerIdCardBeginDate != null">`SETTLER_ID_CARD_BEGIN_DATE`,</if>
            <if test="settlerNotLegalProvePic != null">`SETTLER_NOT_LEGAL_PROVE_PIC`,</if>
            <if test="alipayAuthorizeSaveErrorMsg != null">`ALIPAY_AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="businessAuthorizationLetter != null">`BUSINESS_AUTHORIZATION_LETTER`,</if>
            <if test="wechatAuthorizeSaveErrorMsg != null">`WECHAT_AUTHORIZE_SAVE_ERROR_MSG`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="syncFlag != null">`SYNC_FLAG`,</if>
            <if test="traderType != null">`TRADER_TYPE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="contactType != null">`CONTACT_TYPE`,</if>
            <if test="settlerType != null">`SETTLER_TYPE`,</if>
            <if test="callIncomeStatus != null">`CALL_INCOME_STATUS`,</if>
            <if test="contactIdDocType != null">`CONTACT_ID_DOC_TYPE`,</if>
            <if test="authorizeSaveFlag != null">`AUTHORIZE_SAVE_FLAG`,</if>
            <if test="settlerIdCardType != null">`SETTLER_ID_CARD_TYPE`,</if>
            <if test="contactPeriodIsLong != null">`CONTACT_PERIOD_IS_LONG`,</if>
            <if test="settlerIdCardIsLong != null">`SETTLER_ID_CARD_IS_LONG`,</if>
            <if test="storePicCheckStatus != null">`STORE_PIC_CHECK_STATUS`,</if>
            <if test="ownerCertificateType != null">`OWNER_CERTIFICATE_TYPE`,</if>
            <if test="alipayAuthorizeSaveFlag != null">`ALIPAY_AUTHORIZE_SAVE_FLAG`,</if>
            <if test="wechatAuthorizeSaveFlag != null">`WECHAT_AUTHORIZE_SAVE_FLAG`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="effectiveEndDate != null">`EFFECTIVE_END_DATE`,</if>
            <if test="certificateEndDate != null">`CERTIFICATE_END_DATE`,</if>
            <if test="effectiveStartDate != null">`EFFECTIVE_START_DATE`,</if>
            <if test="certificateStartDate != null">`CERTIFICATE_START_DATE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=BIGINT},</if>
            <if test="adId != null">#{adId,jdbcType=BIGINT},</if>
            <if test="cityId != null">#{cityId,jdbcType=BIGINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=BIGINT},</if>
            <if test="provinceId != null">#{provinceId,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="owner != null">#{owner,jdbcType=VARCHAR},</if>
            <if test="adName != null">#{adName,jdbcType=VARCHAR},</if>
            <if test="bankNo != null">#{bankNo,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=VARCHAR},</if>
            <if test="region != null">#{region,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="license != null">#{license,jdbcType=VARCHAR},</if>
            <if test="plazaId != null">#{plazaId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cityName != null">#{cityName,jdbcType=VARCHAR},</if>
            <if test="latitude != null">#{latitude,jdbcType=VARCHAR},</if>
            <if test="storePic != null">#{storePic,jdbcType=VARCHAR},</if>
            <if test="errorCode != null">#{errorCode,jdbcType=VARCHAR},</if>
            <if test="longitude != null">#{longitude,jdbcType=VARCHAR},</if>
            <if test="plazaName != null">#{plazaName,jdbcType=VARCHAR},</if>
            <if test="storeName != null">#{storeName,jdbcType=VARCHAR},</if>
            <if test="businessId != null">#{businessId,jdbcType=VARCHAR},</if>
            <if test="traderName != null">#{traderName,jdbcType=VARCHAR},</if>
            <if test="bankCardPic != null">#{bankCardPic,jdbcType=VARCHAR},</if>
            <if test="cityCompany != null">#{cityCompany,jdbcType=VARCHAR},</if>
            <if test="businessName != null">#{businessName,jdbcType=VARCHAR},</if>
            <if test="errorMessage != null">#{errorMessage,jdbcType=VARCHAR},</if>
            <if test="idCardNumber != null">#{idCardNumber,jdbcType=VARCHAR},</if>
            <if test="ownerBackUrl != null">#{ownerBackUrl,jdbcType=VARCHAR},</if>
            <if test="provinceName != null">#{provinceName,jdbcType=VARCHAR},</if>
            <if test="unionpayCode != null">#{unionpayCode,jdbcType=VARCHAR},</if>
            <if test="accountMobile != null">#{accountMobile,jdbcType=VARCHAR},</if>
            <if test="ownerFrontUrl != null">#{ownerFrontUrl,jdbcType=VARCHAR},</if>
            <if test="incomeErrorMsg != null">#{incomeErrorMsg,jdbcType=VARCHAR},</if>
            <if test="insideScenePic != null">#{insideScenePic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardNo != null">#{settlerIdCardNo,jdbcType=VARCHAR},</if>
            <if test="businessPlacePic != null">#{businessPlacePic,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopy != null">#{contactIdDocCopy,jdbcType=VARCHAR},</if>
            <if test="contactPeriodEnd != null">#{contactPeriodEnd,jdbcType=VARCHAR},</if>
            <if test="licenseElectronic != null">#{licenseElectronic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardName != null">#{settlerIdCardName,jdbcType=VARCHAR},</if>
            <if test="contactPeriodBegin != null">#{contactPeriodBegin,jdbcType=VARCHAR},</if>
            <if test="ownerCertificateNo != null">#{ownerCertificateNo,jdbcType=VARCHAR},</if>
            <if test="contactIdDocCopyBack != null">#{contactIdDocCopyBack,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBackPic != null">#{settlerIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardEndDate != null">#{settlerIdCardEndDate,jdbcType=VARCHAR},</if>
            <if test="authorizeSaveErrorMsg != null">#{authorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardFrontPic != null">#{settlerIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="settlerIdCardBeginDate != null">#{settlerIdCardBeginDate,jdbcType=VARCHAR},</if>
            <if test="settlerNotLegalProvePic != null">#{settlerNotLegalProvePic,jdbcType=VARCHAR},</if>
            <if test="alipayAuthorizeSaveErrorMsg != null">#{alipayAuthorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="businessAuthorizationLetter != null">#{businessAuthorizationLetter,jdbcType=VARCHAR},</if>
            <if test="wechatAuthorizeSaveErrorMsg != null">#{wechatAuthorizeSaveErrorMsg,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="syncFlag != null">#{syncFlag,jdbcType=TINYINT},</if>
            <if test="traderType != null">#{traderType,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=SMALLINT},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=TINYINT},</if>
            <if test="contactType != null">#{contactType,jdbcType=TINYINT},</if>
            <if test="settlerType != null">#{settlerType,jdbcType=TINYINT},</if>
            <if test="callIncomeStatus != null">#{callIncomeStatus,jdbcType=TINYINT},</if>
            <if test="contactIdDocType != null">#{contactIdDocType,jdbcType=TINYINT},</if>
            <if test="authorizeSaveFlag != null">#{authorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="settlerIdCardType != null">#{settlerIdCardType,jdbcType=TINYINT},</if>
            <if test="contactPeriodIsLong != null">#{contactPeriodIsLong,jdbcType=TINYINT},</if>
            <if test="settlerIdCardIsLong != null">#{settlerIdCardIsLong,jdbcType=TINYINT},</if>
            <if test="storePicCheckStatus != null">#{storePicCheckStatus,jdbcType=TINYINT},</if>
            <if test="ownerCertificateType != null">#{ownerCertificateType,jdbcType=TINYINT},</if>
            <if test="alipayAuthorizeSaveFlag != null">#{alipayAuthorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="wechatAuthorizeSaveFlag != null">#{wechatAuthorizeSaveFlag,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="effectiveEndDate != null">#{effectiveEndDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateEndDate != null">#{certificateEndDate,jdbcType=TIMESTAMP},</if>
            <if test="effectiveStartDate != null">#{effectiveStartDate,jdbcType=TIMESTAMP},</if>
            <if test="certificateStartDate != null">#{certificateStartDate,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByStoreId" paramtype="primitive" multiplicity="one" remark="根据门店ID获取商户资料">
        SELECT * FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        WHERE store_id = #{storeId,jdbcType=BIGINT} limit 1
    </operation>

    <operation name="findByStoreIdList" paramtype="primitive" multiplicity="many" remark="根据门店ID获取商户资料">
        SELECT * FROM TP_LIFECIRCLE_WANDA_DATA_SYNC_TEMP
        WHERE store_id in
        <foreach item="storeId" index="index" collection="list" open="(" separator="," close=")">
            #{storeId,jdbcType=BIGINT}
        </foreach>
    </operation>
</table>
