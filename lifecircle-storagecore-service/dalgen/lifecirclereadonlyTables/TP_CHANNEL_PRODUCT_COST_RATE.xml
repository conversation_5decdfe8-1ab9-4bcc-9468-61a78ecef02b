<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_CHANNEL_PRODUCT_COST_RATE" physicalName="TP_CHANNEL_PRODUCT_COST_RATE"
    remark="通道产品成本费率表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_CHANNEL_PRODUCT_COST_RATE">
INSERT INTO TP_CHANNEL_PRODUCT_COST_RATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="existMaxFee != null">`EXIST_MAX_FEE`,</if>
        <if test="productCode != null">`PRODUCT_CODE`,</if>
        <if test="paymentChannel != null">`PAYMENT_CHANNEL`,</if>
        <if test="existWithdrawalFee != null">`EXIST_WITHDRAWAL_FEE`,</if>
        <if test="paymentChannelName != null">`PAYMENT_CHANNEL_NAME`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="fbRate != null">`FB_RATE`,</if>
        <if test="maxFee != null">`MAX_FEE`,</if>
        <if test="maxRate != null">`MAX_RATE`,</if>
        <if test="maxFbFee != null">`MAX_FB_FEE`,</if>
        <if test="channelRate != null">`CHANNEL_RATE`,</if>
        <if test="maxChannelFee != null">`MAX_CHANNEL_FEE`,</if>
        <if test="fbWithdrawalFee != null">`FB_WITHDRAWAL_FEE`,</if>
        <if test="channelWithdrawalFee != null">`CHANNEL_WITHDRAWAL_FEE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="existMaxFee != null">#{existMaxFee,jdbcType=VARCHAR},</if>
        <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
        <if test="paymentChannel != null">#{paymentChannel,jdbcType=VARCHAR},</if>
        <if test="existWithdrawalFee != null">#{existWithdrawalFee,jdbcType=VARCHAR},</if>
        <if test="paymentChannelName != null">#{paymentChannelName,jdbcType=VARCHAR},</if>
        <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="fbRate != null">#{fbRate,jdbcType=DECIMAL},</if>
        <if test="maxFee != null">#{maxFee,jdbcType=DECIMAL},</if>
        <if test="maxRate != null">#{maxRate,jdbcType=DECIMAL},</if>
        <if test="maxFbFee != null">#{maxFbFee,jdbcType=DECIMAL},</if>
        <if test="channelRate != null">#{channelRate,jdbcType=DECIMAL},</if>
        <if test="maxChannelFee != null">#{maxChannelFee,jdbcType=DECIMAL},</if>
        <if test="fbWithdrawalFee != null">#{fbWithdrawalFee,jdbcType=DECIMAL},</if>
        <if test="channelWithdrawalFee != null">#{channelWithdrawalFee,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="findByPaymentChannel" multiplicity="many" remark="根据支付通道查询">
        select * from tp_channel_product_cost_rate
        where payment_channel = #{paymentChannel,jdbcType=VARCHAR}
    </operation>
</table>
