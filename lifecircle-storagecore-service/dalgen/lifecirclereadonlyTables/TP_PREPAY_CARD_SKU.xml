<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_SKU" physicalName="TP_PREPAY_CARD_SKU"
       remark="预付卡种类sku表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_SKU">
        INSERT INTO TP_PREPAY_CARD_SKU
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findBySkuIdList" multiplicity="many" remark="根据skuId列表查询Sku信息">
        select
        *
        from tp_prepay_card_sku
        where is_del = 0
        and card_sku_id in
        <foreach collection="list" item="cardSkuId" open="(" separator="," close=")">
            #{cardSkuId,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
