<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_DEPOSIT_ORDER" physicalName="TP_DEPOSIT_ORDER"
    remark="预授权订单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_DEPOSIT_ORDER">
INSERT INTO TP_DEPOSIT_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="body != null">`BODY`,</if>
        <if test="ext1 != null">`EXT1`,</if>
        <if test="ext2 != null">`EXT2`,</if>
        <if test="ext3 != null">`EXT3`,</if>
        <if test="ext4 != null">`EXT4`,</if>
        <if test="ext5 != null">`EXT5`,</if>
        <if test="ext6 != null">`EXT6`,</if>
        <if test="ext7 != null">`EXT7`,</if>
        <if test="ext8 != null">`EXT8`,</if>
        <if test="ext9 != null">`EXT9`,</if>
        <if test="ext10 != null">`EXT10`,</if>
        <if test="token != null">`TOKEN`,</if>
        <if test="cardId != null">`CARD_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="deviceNo != null">`DEVICE_NO`,</if>
        <if test="subMchId != null">`SUB_MCH_ID`,</if>
        <if test="posFlowId != null">`POS_FLOW_ID`,</if>
        <if test="channelNum != null">`CHANNEL_NUM`,</if>
        <if test="posBatchNo != null">`POS_BATCH_NO`,</if>
        <if test="posAuthCode != null">`POS_AUTH_CODE`,</if>
        <if test="depositOrderSn != null">`DEPOSIT_ORDER_SN`,</if>
        <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
        <if test="platformOrderSn != null">`PLATFORM_ORDER_SN`,</if>
        <if test="referenceNumber != null">`REFERENCE_NUMBER`,</if>
        <if test="merchantDepositOrderSn != null">`MERCHANT_DEPOSIT_ORDER_SN`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="agentId != null">`AGENT_ID`,</if>
        <if test="channel != null">`CHANNEL`,</if>
        <if test="grantId != null">`GRANT_ID`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="storeId != null">`STORE_ID`,</if>
        <if test="cardType != null">`CARD_TYPE`,</if>
        <if test="cashierId != null">`CASHIER_ID`,</if>
        <if test="thawStatus != null">`THAW_STATUS`,</if>
        <if test="depositType != null">`DEPOSIT_TYPE`,</if>
        <if test="orderStatus != null">`ORDER_STATUS`,</if>
        <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
        <if test="tradeStatus != null">`TRADE_STATUS`,</if>
        <if test="isPrintTicket != null">`IS_PRINT_TICKET`,</if>
        <if test="depositTradeType != null">`DEPOSIT_TRADE_TYPE`,</if>
        <if test="depositRevokeTime != null">`DEPOSIT_REVOKE_TIME`,</if>
        <if test="tradeTime != null">`TRADE_TIME`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="revokeTime != null">`REVOKE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="depositTime != null">`DEPOSIT_TIME`,</if>
        <if test="thawPrice != null">`THAW_PRICE`,</if>
        <if test="consumePrice != null">`CONSUME_PRICE`,</if>
        <if test="depositPrice != null">`DEPOSIT_PRICE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="body != null">#{body,jdbcType=VARCHAR},</if>
        <if test="ext1 != null">#{ext1,jdbcType=VARCHAR},</if>
        <if test="ext2 != null">#{ext2,jdbcType=VARCHAR},</if>
        <if test="ext3 != null">#{ext3,jdbcType=VARCHAR},</if>
        <if test="ext4 != null">#{ext4,jdbcType=VARCHAR},</if>
        <if test="ext5 != null">#{ext5,jdbcType=VARCHAR},</if>
        <if test="ext6 != null">#{ext6,jdbcType=VARCHAR},</if>
        <if test="ext7 != null">#{ext7,jdbcType=VARCHAR},</if>
        <if test="ext8 != null">#{ext8,jdbcType=VARCHAR},</if>
        <if test="ext9 != null">#{ext9,jdbcType=VARCHAR},</if>
        <if test="ext10 != null">#{ext10,jdbcType=VARCHAR},</if>
        <if test="token != null">#{token,jdbcType=VARCHAR},</if>
        <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="deviceNo != null">#{deviceNo,jdbcType=VARCHAR},</if>
        <if test="subMchId != null">#{subMchId,jdbcType=VARCHAR},</if>
        <if test="posFlowId != null">#{posFlowId,jdbcType=VARCHAR},</if>
        <if test="channelNum != null">#{channelNum,jdbcType=VARCHAR},</if>
        <if test="posBatchNo != null">#{posBatchNo,jdbcType=VARCHAR},</if>
        <if test="posAuthCode != null">#{posAuthCode,jdbcType=VARCHAR},</if>
        <if test="depositOrderSn != null">#{depositOrderSn,jdbcType=VARCHAR},</if>
        <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
        <if test="platformOrderSn != null">#{platformOrderSn,jdbcType=VARCHAR},</if>
        <if test="referenceNumber != null">#{referenceNumber,jdbcType=VARCHAR},</if>
        <if test="merchantDepositOrderSn != null">#{merchantDepositOrderSn,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
        <if test="channel != null">#{channel,jdbcType=TINYINT},</if>
        <if test="grantId != null">#{grantId,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
        <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
        <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
        <if test="cashierId != null">#{cashierId,jdbcType=INTEGER},</if>
        <if test="thawStatus != null">#{thawStatus,jdbcType=TINYINT},</if>
        <if test="depositType != null">#{depositType,jdbcType=TINYINT},</if>
        <if test="orderStatus != null">#{orderStatus,jdbcType=TINYINT},</if>
        <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
        <if test="tradeStatus != null">#{tradeStatus,jdbcType=TINYINT},</if>
        <if test="isPrintTicket != null">#{isPrintTicket,jdbcType=TINYINT},</if>
        <if test="depositTradeType != null">#{depositTradeType,jdbcType=TINYINT},</if>
        <if test="depositRevokeTime != null">#{depositRevokeTime,jdbcType=INTEGER},</if>
        <if test="tradeTime != null">#{tradeTime,jdbcType=TIMESTAMP},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="revokeTime != null">#{revokeTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="depositTime != null">#{depositTime,jdbcType=TIMESTAMP},</if>
        <if test="thawPrice != null">#{thawPrice,jdbcType=DECIMAL},</if>
        <if test="consumePrice != null">#{consumePrice,jdbcType=DECIMAL},</if>
        <if test="depositPrice != null">#{depositPrice,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="getMerchantDepositOrderSnListByDepositOrderSn" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.dal.lifecircle.dataobject.MerchantOrderSnDO"
               remark="根据预授权订单号查询商户预授权订单号">
        select deposit_order_sn as orderSn,merchant_deposit_order_sn as merchantOrderSn
        from tp_deposit_order
        where deposit_order_sn in
        <foreach collection="list" open="(" item="depositOrderSn" separator="," close=")">
            #{depositOrderSn,jdbcType=VARCHAR}
        </foreach>
    </operation>

    </table>
