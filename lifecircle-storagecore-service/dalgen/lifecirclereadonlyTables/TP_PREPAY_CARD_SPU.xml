<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_SPU" physicalName="TP_PREPAY_CARD_SPU"
       remark="预付卡种类spu表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_SPU">
        INSERT INTO TP_PREPAY_CARD_SPU
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="useDesc != null">`USE_DESC`,</if>
            <if test="coverUrl != null">`COVER_URL`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="operateId != null">`OPERATE_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="operateName != null">`OPERATE_NAME`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="spuStatus != null">`SPU_STATUS`,</if>
            <if test="isMinaSales != null">`IS_MINA_SALES`,</if>
            <if test="cardShapeType != null">`CARD_SHAPE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="useDesc != null">#{useDesc,jdbcType=VARCHAR},</if>
            <if test="coverUrl != null">#{coverUrl,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="operateId != null">#{operateId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="operateName != null">#{operateName,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="spuStatus != null">#{spuStatus,jdbcType=TINYINT},</if>
            <if test="isMinaSales != null">#{isMinaSales,jdbcType=TINYINT},</if>
            <if test="cardShapeType != null">#{cardShapeType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getSpuBySkuId" multiplicity="one" paramtype="primitive" remark="根据skuId查询spu信息">
        select
        spu.*
        from
        `tp_prepay_card_sku` sku
        LEFT JOIN `tp_prepay_card_spu` spu on sku.`card_spu_id` = spu.`card_spu_id`
        where sku.`card_sku_id` = #{cardSkuId,jdbcType=VARCHAR}
        and spu.is_del = 0
        limit 1
    </operation>

    <operation name="findBySpuIdList" multiplicity="many" remark="根据spuId集合查询spu集合">
        select
        *
        from
        tp_prepay_card_spu
        where
        is_del = 0
        and card_spu_id in
        <foreach collection="list" item="spuId" open="(" separator="," close=")">
            #{spuId,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
