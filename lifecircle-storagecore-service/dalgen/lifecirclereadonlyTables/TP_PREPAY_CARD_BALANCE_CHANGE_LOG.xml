<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_BALANCE_CHANGE_LOG" physicalName="TP_PREPAY_CARD_BALANCE_CHANGE_LOG"
       remark="预付卡余额变更记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_BALANCE_CHANGE_LOG">
        INSERT INTO TP_PREPAY_CARD_BALANCE_CHANGE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="changeRemark != null">`CHANGE_REMARK`,</if>
            <if test="changeType != null">`CHANGE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="afterBalance != null">`AFTER_BALANCE`,</if>
            <if test="changeBalance != null">`CHANGE_BALANCE`,</if>
            <if test="afterTodayConsumeAmount != null">`AFTER_TODAY_CONSUME_AMOUNT`,</if>
            <if test="changeTodayConsumeAmount != null">`CHANGE_TODAY_CONSUME_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
            <if test="changeType != null">#{changeType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="afterBalance != null">#{afterBalance,jdbcType=DECIMAL},</if>
            <if test="changeBalance != null">#{changeBalance,jdbcType=DECIMAL},</if>
            <if test="afterTodayConsumeAmount != null">#{afterTodayConsumeAmount,jdbcType=DECIMAL},</if>
            <if test="changeTodayConsumeAmount != null">#{changeTodayConsumeAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findBalanceChangeLogByCardNoList" multiplicity="many" remark="查询指定日期的余额变更日志">
        SELECT
        *
        FROM tp_prepay_card_balance_change_log
        where order_sn in
        <foreach close=")" collection="list" index="index" item="orderSn" open="(" separator=",">
            #{orderSn, jdbcType=VARCHAR}
        </foreach>
        and create_time <![CDATA[ >= ]]> #{startTime, jdbcType=TIMESTAMP}
        and create_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
    </operation>
</table>
