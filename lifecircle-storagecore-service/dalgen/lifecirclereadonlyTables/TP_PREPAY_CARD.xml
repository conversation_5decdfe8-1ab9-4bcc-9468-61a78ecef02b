<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD" physicalName="TP_PREPAY_CARD"
       remark="预付卡表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD">
        INSERT INTO TP_PREPAY_CARD
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="cardCode != null">`CARD_CODE`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="cardSpuName != null">`CARD_SPU_NAME`,</if>
            <if test="fubeiUnionId != null">`FUBEI_UNION_ID`,</if>
            <if test="activationCode != null">`ACTIVATION_CODE`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="bindTime != null">`BIND_TIME`,</if>
            <if test="cardType != null">`CARD_TYPE`,</if>
            <if test="bindSource != null">`BIND_SOURCE`,</if>
            <if test="bindStatus != null">`BIND_STATUS`,</if>
            <if test="cardExpiry != null">`CARD_EXPIRY`,</if>
            <if test="cardStatus != null">`CARD_STATUS`,</if>
            <if test="isMinaSales != null">`IS_MINA_SALES`,</if>
            <if test="stockStatus != null">`STOCK_STATUS`,</if>
            <if test="activationTime != null">`ACTIVATION_TIME`,</if>
            <if test="outStorageTime != null">`OUT_STORAGE_TIME`,</if>
            <if test="isLongEffective != null">`IS_LONG_EFFECTIVE`,</if>
            <if test="activationStatus != null">`ACTIVATION_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="cardPrice != null">`CARD_PRICE`,</if>
            <if test="cardAmount != null">`CARD_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="cardCode != null">#{cardCode,jdbcType=VARCHAR},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuName != null">#{cardSpuName,jdbcType=VARCHAR},</if>
            <if test="fubeiUnionId != null">#{fubeiUnionId,jdbcType=VARCHAR},</if>
            <if test="activationCode != null">#{activationCode,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="bindTime != null">#{bindTime,jdbcType=INTEGER},</if>
            <if test="cardType != null">#{cardType,jdbcType=TINYINT},</if>
            <if test="bindSource != null">#{bindSource,jdbcType=TINYINT},</if>
            <if test="bindStatus != null">#{bindStatus,jdbcType=TINYINT},</if>
            <if test="cardExpiry != null">#{cardExpiry,jdbcType=INTEGER},</if>
            <if test="cardStatus != null">#{cardStatus,jdbcType=TINYINT},</if>
            <if test="isMinaSales != null">#{isMinaSales,jdbcType=TINYINT},</if>
            <if test="stockStatus != null">#{stockStatus,jdbcType=TINYINT},</if>
            <if test="activationTime != null">#{activationTime,jdbcType=INTEGER},</if>
            <if test="outStorageTime != null">#{outStorageTime,jdbcType=INTEGER},</if>
            <if test="isLongEffective != null">#{isLongEffective,jdbcType=TINYINT},</if>
            <if test="activationStatus != null">#{activationStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="cardPrice != null">#{cardPrice,jdbcType=DECIMAL},</if>
            <if test="cardAmount != null">#{cardAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findListPrepayCardByCardNos" multiplicity="many" remark="根据预付卡查询卡列表">
        SELECT
        *
        FROM
        `tp_prepay_card`
        WHERE
        `is_del`= 0
        AND `card_no` IN
        <foreach close=")" collection="list" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="countPrepayCardByCardNos" resulttype="java.lang.Integer" remark="查询作废卡数量">
        SELECT
        count(*)
        FROM
        `tp_prepay_card`
        WHERE
        `is_del`= 0
        AND card_status = 4
        AND `card_no` IN
        <foreach close=")" collection="list" index="index" item="cardNo" open="(" separator=",">
            #{cardNo,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="findCardByCardNoList" multiplicity="many" remark="根据卡号查询卡信息">
        select
        *
        from tp_prepay_card
        where card_no in
        <foreach collection="list" item="cardNo" open="(" close=")" separator=",">
            #{cardNo, jdbcType=VARCHAR}
        </foreach>
        and is_del = 0
    </operation>

    <operation name="findEntityCardDOList" multiplicity="many" remark="根据卡号列表查询实体卡信息">
        select
        *
        from tp_prepay_card
        where card_no in
        <foreach collection="list" item="cardNo" open="(" close=")" separator=",">
            #{cardNo, jdbcType=VARCHAR}
        </foreach>
        and card_shape_type = 2
        and is_del = 0
    </operation>

    <operation name="findSatisfyOutStorageCardList" paging="satisfyOutStorageCardList" multiplicity="paging"
               remark="查询满足出库条件的卡信息">
        SELECT
        *
        FROM `tp_prepay_card`
        WHERE `is_del`= 0
        AND `stock_status` = 1
        and `card_status` = 1
        and `card_expiry` <![CDATA[ > ]]>  #{cardExpiry, jdbcType=INTEGER}
        and `card_spu_id` = #{cardSpuId, jdbcType=VARCHAR}
        and `card_sku_id` = #{cardSkuId, jdbcType=VARCHAR}
        and `publish_org_id` = #{publishOrgId, jdbcType=VARCHAR}
        ORDER BY `id` ASC
    </operation>

    <operation name="findCardByCardNoListAndSkuId" multiplicity="many" remark="根据卡号批量查询指定sku的预付卡信息">
        SELECT * FROM tp_prepay_card
        WHERE card_no in
        <foreach collection="list" item="cardNo" open="(" separator="," close=")">
            #{cardNo, jdbcType=VARCHAR}
        </foreach>
        and card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        and is_del = 0
    </operation>

    <operation name="getCardByCardNo" multiplicity="one" remark="根据卡号查询预付卡信息">
        SELECT * FROM tp_prepay_card WHERE card_no = #{cardNo, jdbcType=VARCHAR} and is_del = 0 limit 1
    </operation>
</table>
