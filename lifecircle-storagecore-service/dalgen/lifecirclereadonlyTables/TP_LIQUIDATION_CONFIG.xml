<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_LIQUIDATION_CONFIG" physicalName="TP_LIQUIDATION_CONFIG"
       remark="清算平台通道配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_LIQUIDATION_CONFIG">
        INSERT INTO TP_LIQUIDATION_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appId != null">`APP_ID`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="partners != null">`PARTNERS`,</if>
            <if test="phpPublicKey != null">`PHP_PUBLIC_KEY`,</if>
            <if test="javaPublicKey != null">`JAVA_PUBLIC_KEY`,</if>
            <if test="phpPrivateKey != null">`PHP_PRIVATE_KEY`,</if>
            <if test="javaPrivateKey != null">`JAVA_PRIVATE_KEY`,</if>
            <if test="imageGatewayUrl != null">`IMAGE_GATEWAY_URL`,</if>
            <if test="innerGatewayUrl != null">`INNER_GATEWAY_URL`,</if>
            <if test="liquidationName != null">`LIQUIDATION_NAME`,</if>
            <if test="outerGatewayUrl != null">`OUTER_GATEWAY_URL`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appId != null">#{appId,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="partners != null">#{partners,jdbcType=VARCHAR},</if>
            <if test="phpPublicKey != null">#{phpPublicKey,jdbcType=VARCHAR},</if>
            <if test="javaPublicKey != null">#{javaPublicKey,jdbcType=VARCHAR},</if>
            <if test="phpPrivateKey != null">#{phpPrivateKey,jdbcType=VARCHAR},</if>
            <if test="javaPrivateKey != null">#{javaPrivateKey,jdbcType=VARCHAR},</if>
            <if test="imageGatewayUrl != null">#{imageGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="innerGatewayUrl != null">#{innerGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="liquidationName != null">#{liquidationName,jdbcType=VARCHAR},</if>
            <if test="outerGatewayUrl != null">#{outerGatewayUrl,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation multiplicity="many" name="findByLiquidationTypeList" remark="获取支付通道配置列表">
        select * from TP_LIQUIDATION_CONFIG
        where liquidation_type in
        <foreach close=")" collection="list" index="index" item="liquidationType" open="(" separator=",">
            #{liquidationType,jdbcType=TINYINT}
        </foreach>
    </operation>
</table>
