<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_COST_RATE" physicalName="TP_COST_RATE"
    remark="成本费率表（包含oem商，代理商）">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_COST_RATE">
INSERT INTO TP_COST_RATE
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="isSync != null">`IS_SYNC`,</if>
        <if test="syncSub != null">`SYNC_SUB`,</if>
        <if test="roleType != null">`ROLE_TYPE`,</if>
        <if test="existMaxFee != null">`EXIST_MAX_FEE`,</if>
        <if test="isEffective != null">`IS_EFFECTIVE`,</if>
        <if test="productCode != null">`PRODUCT_CODE`,</if>
        <if test="paymentChannel != null">`PAYMENT_CHANNEL`,</if>
        <if test="existWithdrawalFee != null">`EXIST_WITHDRAWAL_FEE`,</if>
        <if test="uid != null">`UID`,</if>
        <if test="payType != null">`PAY_TYPE`,</if>
        <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="parentEffectiveTime != null">`PARENT_EFFECTIVE_TIME`,</if>
        <if test="fbRate != null">`FB_RATE`,</if>
        <if test="oemRate != null">`OEM_RATE`,</if>
        <if test="maxFbFee != null">`MAX_FB_FEE`,</if>
        <if test="agentRate != null">`AGENT_RATE`,</if>
        <if test="maxOemFee != null">`MAX_OEM_FEE`,</if>
        <if test="channelRate != null">`CHANNEL_RATE`,</if>
        <if test="maxAgentFee != null">`MAX_AGENT_FEE`,</if>
        <if test="salesmanRate != null">`SALESMAN_RATE`,</if>
        <if test="maxChannelFee != null">`MAX_CHANNEL_FEE`,</if>
        <if test="maxSalesmanFee != null">`MAX_SALESMAN_FEE`,</if>
        <if test="fbWithdrawalFee != null">`FB_WITHDRAWAL_FEE`,</if>
        <if test="oemWithdrawalFee != null">`OEM_WITHDRAWAL_FEE`,</if>
        <if test="agentWithdrawalFee != null">`AGENT_WITHDRAWAL_FEE`,</if>
        <if test="channelWithdrawalFee != null">`CHANNEL_WITHDRAWAL_FEE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="isSync != null">#{isSync,jdbcType=VARCHAR},</if>
        <if test="syncSub != null">#{syncSub,jdbcType=VARCHAR},</if>
        <if test="roleType != null">#{roleType,jdbcType=VARCHAR},</if>
        <if test="existMaxFee != null">#{existMaxFee,jdbcType=VARCHAR},</if>
        <if test="isEffective != null">#{isEffective,jdbcType=VARCHAR},</if>
        <if test="productCode != null">#{productCode,jdbcType=VARCHAR},</if>
        <if test="paymentChannel != null">#{paymentChannel,jdbcType=VARCHAR},</if>
        <if test="existWithdrawalFee != null">#{existWithdrawalFee,jdbcType=VARCHAR},</if>
        <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
        <if test="payType != null">#{payType,jdbcType=INTEGER},</if>
        <if test="liquidationType != null">#{liquidationType,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="parentEffectiveTime != null">#{parentEffectiveTime,jdbcType=TIMESTAMP},</if>
        <if test="fbRate != null">#{fbRate,jdbcType=DECIMAL},</if>
        <if test="oemRate != null">#{oemRate,jdbcType=DECIMAL},</if>
        <if test="maxFbFee != null">#{maxFbFee,jdbcType=DECIMAL},</if>
        <if test="agentRate != null">#{agentRate,jdbcType=DECIMAL},</if>
        <if test="maxOemFee != null">#{maxOemFee,jdbcType=DECIMAL},</if>
        <if test="channelRate != null">#{channelRate,jdbcType=DECIMAL},</if>
        <if test="maxAgentFee != null">#{maxAgentFee,jdbcType=DECIMAL},</if>
        <if test="salesmanRate != null">#{salesmanRate,jdbcType=DECIMAL},</if>
        <if test="maxChannelFee != null">#{maxChannelFee,jdbcType=DECIMAL},</if>
        <if test="maxSalesmanFee != null">#{maxSalesmanFee,jdbcType=DECIMAL},</if>
        <if test="fbWithdrawalFee != null">#{fbWithdrawalFee,jdbcType=DECIMAL},</if>
        <if test="oemWithdrawalFee != null">#{oemWithdrawalFee,jdbcType=DECIMAL},</if>
        <if test="agentWithdrawalFee != null">#{agentWithdrawalFee,jdbcType=DECIMAL},</if>
        <if test="channelWithdrawalFee != null">#{channelWithdrawalFee,jdbcType=DECIMAL},</if>
    </trim>
    </operation>

    <operation name="findByUidAndChannel" multiplicity="many" remark="根据用户和支付通道查询">
        SELECT * FROM tp_cost_rate
        where uid = #{uid,jdbcType=INTEGER}
        and payment_channel=#{paymentChannel,jdbcType=VARCHAR}
        and role_type =#{roleType,jdbcType=VARCHAR}
    </operation>
</table>
