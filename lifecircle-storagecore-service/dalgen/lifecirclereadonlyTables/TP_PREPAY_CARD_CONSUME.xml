<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_CONSUME" physicalName="TP_PREPAY_CARD_CONSUME"
       remark="预付卡消费记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_CONSUME">
        INSERT INTO TP_PREPAY_CARD_CONSUME
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="ext01 != null">`EXT01`,</if>
            <if test="ext02 != null">`EXT02`,</if>
            <if test="ext03 != null">`EXT03`,</if>
            <if test="ext04 != null">`EXT04`,</if>
            <if test="ext05 != null">`EXT05`,</if>
            <if test="ext06 != null">`EXT06`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="openId != null">`OPEN_ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="fubeiUnionId != null">`FUBEI_UNION_ID`,</if>
            <if test="level01OrgId != null">`LEVEL01_ORG_ID`,</if>
            <if test="level02OrgId != null">`LEVEL02_ORG_ID`,</if>
            <if test="level03OrgId != null">`LEVEL03_ORG_ID`,</if>
            <if test="level04OrgId != null">`LEVEL04_ORG_ID`,</if>
            <if test="publishOrgId != null">`PUBLISH_ORG_ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="payTime != null">`PAY_TIME`,</if>
            <if test="payType != null">`PAY_TYPE`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="payStatus != null">`PAY_STATUS`,</if>
            <if test="refundTime != null">`REFUND_TIME`,</if>
            <if test="refundStatus != null">`REFUND_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fee != null">`FEE`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="refundMoney != null">`REFUND_MONEY`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="ext01 != null">#{ext01,jdbcType=VARCHAR},</if>
            <if test="ext02 != null">#{ext02,jdbcType=VARCHAR},</if>
            <if test="ext03 != null">#{ext03,jdbcType=VARCHAR},</if>
            <if test="ext04 != null">#{ext04,jdbcType=VARCHAR},</if>
            <if test="ext05 != null">#{ext05,jdbcType=VARCHAR},</if>
            <if test="ext06 != null">#{ext06,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="openId != null">#{openId,jdbcType=VARCHAR},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="unionId != null">#{unionId,jdbcType=VARCHAR},</if>
            <if test="fubeiUnionId != null">#{fubeiUnionId,jdbcType=VARCHAR},</if>
            <if test="level01OrgId != null">#{level01OrgId,jdbcType=VARCHAR},</if>
            <if test="level02OrgId != null">#{level02OrgId,jdbcType=VARCHAR},</if>
            <if test="level03OrgId != null">#{level03OrgId,jdbcType=VARCHAR},</if>
            <if test="level04OrgId != null">#{level04OrgId,jdbcType=VARCHAR},</if>
            <if test="publishOrgId != null">#{publishOrgId,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="payTime != null">#{payTime,jdbcType=INTEGER},</if>
            <if test="payType != null">#{payType,jdbcType=TINYINT},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="orderType != null">#{orderType,jdbcType=TINYINT},</if>
            <if test="payStatus != null">#{payStatus,jdbcType=TINYINT},</if>
            <if test="refundTime != null">#{refundTime,jdbcType=INTEGER},</if>
            <if test="refundStatus != null">#{refundStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fee != null">#{fee,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="refundMoney != null">#{refundMoney,jdbcType=DECIMAL},</if>
        </trim>
    </operation>


    <operation name="findPrepayCardConsumeList" multiplicity="many" remark="查询预付卡消费记录列表">
        SELECT *
        FROM `tp_prepay_card_consume`
        WHERE `publish_org_id` in
        <foreach close=")" collection="list" index="index" item="publishOrgId" open="(" separator=",">
            #{publishOrgId, jdbcType=VARCHAR}
        </foreach>
        and create_time <![CDATA[ >= ]]> #{startTime, jdbcType=TIMESTAMP}
        and create_time <![CDATA[ <= ]]> #{endTime, jdbcType=TIMESTAMP}
    </operation>
</table>
