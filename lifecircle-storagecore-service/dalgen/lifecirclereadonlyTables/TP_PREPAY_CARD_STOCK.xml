<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_PREPAY_CARD_STOCK" physicalName="TP_PREPAY_CARD_STOCK"
       remark="预付卡库存表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_PREPAY_CARD_STOCK">
        INSERT INTO TP_PREPAY_CARD_STOCK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="cardSkuId != null">`CARD_SKU_ID`,</if>
            <if test="cardSpuId != null">`CARD_SPU_ID`,</if>
            <if test="isDel != null">`IS_DEL`,</if>
            <if test="stock != null">`STOCK`,</if>
            <if test="lockStock != null">`LOCK_STOCK`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="cardSkuId != null">#{cardSkuId,jdbcType=VARCHAR},</if>
            <if test="cardSpuId != null">#{cardSpuId,jdbcType=VARCHAR},</if>
            <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
            <if test="stock != null">#{stock,jdbcType=INTEGER},</if>
            <if test="lockStock != null">#{lockStock,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>


    <operation name="findCardStockListBySkuIdList" multiplicity="many" remark="获取库存列表根据skuId列表">
        SELECT *
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0">
            AND card_sku_id IN
            <foreach collection="list" item="cardSkuId" open="(" close=")" separator=",">
                #{cardSkuId,jdbcType=VARCHAR}
            </foreach>
        </if>
    </operation>


    <operation name="findCardStockBySkuId" multiplicity="one" remark="根据skuid获取stock信息">
        SELECT *
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
        AND card_sku_id = #{cardSkuId,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getCardStockBySkuIdAndPublishOrgId" resulttype="java.lang.Integer" remark="获取卡种类skuId获取卡库存">
        SELECT
        stock - lock_stock
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND card_spu_id = #{cardSpuId, jdbcType=VARCHAR}
        AND card_sku_id = #{cardSkuId, jdbcType=VARCHAR}
        AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
    </operation>

    <operation name="getCardStockByPublishOrgId" multiplicity="many" remark="根据发行组织ID获取卡库存信息">
        SELECT
        *
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId, jdbcType=VARCHAR}
        AND card_sku_id in
        <foreach collection="cardSkuIdList" item="cardSkuId" open="(" close=")" separator=",">
            #{cardSkuId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getCardRealStockByPublishOrgId" multiplicity="many" remark="根据发行组织ID获取卡真实库存信息">
        SELECT
        (stock - lock_stock) AS stock,
        card_sku_id AS cardSkuId
        FROM tp_prepay_card_stock
        WHERE is_del = 0
        AND publish_org_id = #{publishOrgId,jdbcType=VARCHAR}
        <if test="list != null and list.size() > 0">
            AND card_sku_id IN
            <foreach collection="list" item="cardSkuId" open="(" close=")" separator=",">
                #{cardSkuId,jdbcType=VARCHAR}
            </foreach>
        </if>
    </operation>

</table>
