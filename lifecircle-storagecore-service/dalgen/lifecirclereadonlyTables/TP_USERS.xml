<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USERS" physicalName="TP_USERS"
       remark="TP_USERS">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USERS">
        INSERT INTO TP_USERS
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="indirect != null">`INDIRECT`,</if>
            <if test="salespercent != null">`SALESPERCENT`,</if>
            <if test="distributorSalespercent != null">`DISTRIBUTOR_SALESPERCENT`,</if>
            <if test="mp != null">`MP`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="lastip != null">`LASTIP`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="people != null">`PEOPLE`,</if>
            <if test="qrcode != null">`QRCODE`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="contact != null">`CONTACT`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="bankUser != null">`BANK_USER`,</if>
            <if test="createip != null">`CREATEIP`,</if>
            <if test="lasttime != null">`LASTTIME`,</if>
            <if test="password != null">`PASSWORD`,</if>
            <if test="plugsave != null">`PLUGSAVE`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="realName != null">`REAL_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="alipaynum != null">`ALIPAYNUM`,</if>
            <if test="bankAcount != null">`BANK_ACOUNT`,</if>
            <if test="industryId != null">`INDUSTRY_ID`,</if>
            <if test="usersToken != null">`USERS_TOKEN`,</if>
            <if test="fundPassword != null">`FUND_PASSWORD`,</if>
            <if test="companyaddress != null">`COMPANYADDRESS`,</if>
            <if test="usersHeaderpic != null">`USERS_HEADERPIC`,</if>
            <if test="protocolVersion != null">`PROTOCOL_VERSION`,</if>
            <if test="fundPasswordSalt != null">`FUND_PASSWORD_SALT`,</if>
            <if test="storeDefaultLogo != null">`STORE_DEFAULT_LOGO`,</if>
            <if test="gid != null">`GID`,</if>
            <if test="focus != null">`FOCUS`,</if>
            <if test="money != null">`MONEY`,</if>
            <if test="amount != null">`AMOUNT`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="diynum != null">`DIYNUM`,</if>
            <if test="ispush != null">`ISPUSH`,</if>
            <if test="roleId != null">`ROLE_ID`,</if>
            <if test="vendor != null">`VENDOR`,</if>
            <if test="apiUser != null">`API_USER`,</if>
            <if test="cardNum != null">`CARD_NUM`,</if>
            <if test="isApply != null">`IS_APPLY`,</if>
            <if test="isJdpay != null">`IS_JDPAY`,</if>
            <if test="unionId != null">`UNION_ID`,</if>
            <if test="viptime != null">`VIPTIME`,</if>
            <if test="isAlipay != null">`IS_ALIPAY`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="parentId != null">`PARENT_ID`,</if>
            <if test="payLimit != null">`PAY_LIMIT`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="salesman != null">`SALESMAN`,</if>
            <if test="wxStatus != null">`WX_STATUS`,</if>
            <if test="applyTime != null">`APPLY_TIME`,</if>
            <if test="isConfirm != null">`IS_CONFIRM`,</if>
            <if test="versionId != null">`VERSION_ID`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="connectnum != null">`CONNECTNUM`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="dealamount != null">`DEALAMOUNT`,</if>
            <if test="isGroupBuy != null">`IS_GROUP_BUY`,</if>
            <if test="isOpenMina != null">`IS_OPEN_MINA`,</if>
            <if test="isProtocol != null">`IS_PROTOCOL`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="onlinetime != null">`ONLINETIME`,</if>
            <if test="protocolId != null">`PROTOCOL_ID`,</if>
            <if test="salerAudit != null">`SALER_AUDIT`,</if>
            <if test="voiceOnOff != null">`VOICE_ON_OFF`,</if>
            <if test="activitynum != null">`ACTIVITYNUM`,</if>
            <if test="cashoutLock != null">`CASHOUT_LOCK`,</if>
            <if test="confirmTime != null">`CONFIRM_TIME`,</if>
            <if test="isQuickCash != null">`IS_QUICK_CASH`,</if>
            <if test="mcardStatus != null">`MCARD_STATUS`,</if>
            <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
            <if test="totalsmsnum != null">`TOTALSMSNUM`,</if>
            <if test="incomeStatus != null">`INCOME_STATUS`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="protocolTime != null">`PROTOCOL_TIME`,</if>
            <if test="psModifyTime != null">`PS_MODIFY_TIME`,</if>
            <if test="totalsmsused != null">`TOTALSMSUSED`,</if>
            <if test="transferTime != null">`TRANSFER_TIME`,</if>
            <if test="isScanService != null">`IS_SCAN_SERVICE`,</if>
            <if test="isemscnplpush != null">`ISEMSCNPLPUSH`,</if>
            <if test="pwResetStatus != null">`PW_RESET_STATUS`,</if>
            <if test="rechargeLimit != null">`RECHARGE_LIMIT`,</if>
            <if test="wechatCardNum != null">`WECHAT_CARD_NUM`,</if>
            <if test="attachmentsize != null">`ATTACHMENTSIZE`,</if>
            <if test="autoWithdrawal != null">`AUTO_WITHDRAWAL`,</if>
            <if test="lastloginmonth != null">`LASTLOGINMONTH`,</if>
            <if test="lifecircleTime != null">`LIFECIRCLE_TIME`,</if>
            <if test="liquidationType != null">`LIQUIDATION_TYPE`,</if>
            <if test="cardCreateStatus != null">`CARD_CREATE_STATUS`,</if>
            <if test="latestonlinetime != null">`LATESTONLINETIME`,</if>
            <if test="isServicenoAccess != null">`IS_SERVICENO_ACCESS`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finance != null">`FINANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="indirect != null">#{indirect,jdbcType=DOUBLE},</if>
            <if test="salespercent != null">#{salespercent,jdbcType=DOUBLE},</if>
            <if test="distributorSalespercent != null">#{distributorSalespercent,jdbcType=DOUBLE},</if>
            <if test="mp != null">#{mp,jdbcType=VARCHAR},</if>
            <if test="qq != null">#{qq,jdbcType=VARCHAR},</if>
            <if test="area != null">#{area,jdbcType=CHAR},</if>
            <if test="city != null">#{city,jdbcType=CHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="lastip != null">#{lastip,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=CHAR},</if>
            <if test="people != null">#{people,jdbcType=VARCHAR},</if>
            <if test="qrcode != null">#{qrcode,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="contact != null">#{contact,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="bankUser != null">#{bankUser,jdbcType=VARCHAR},</if>
            <if test="createip != null">#{createip,jdbcType=VARCHAR},</if>
            <if test="lasttime != null">#{lasttime,jdbcType=VARCHAR},</if>
            <if test="password != null">#{password,jdbcType=VARCHAR},</if>
            <if test="plugsave != null">#{plugsave,jdbcType=VARCHAR},</if>
            <if test="province != null">#{province,jdbcType=CHAR},</if>
            <if test="realName != null">#{realName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="alipaynum != null">#{alipaynum,jdbcType=VARCHAR},</if>
            <if test="bankAcount != null">#{bankAcount,jdbcType=VARCHAR},</if>
            <if test="industryId != null">#{industryId,jdbcType=VARCHAR},</if>
            <if test="usersToken != null">#{usersToken,jdbcType=VARCHAR},</if>
            <if test="fundPassword != null">#{fundPassword,jdbcType=VARCHAR},</if>
            <if test="companyaddress != null">#{companyaddress,jdbcType=VARCHAR},</if>
            <if test="usersHeaderpic != null">#{usersHeaderpic,jdbcType=CHAR},</if>
            <if test="protocolVersion != null">#{protocolVersion,jdbcType=VARCHAR},</if>
            <if test="fundPasswordSalt != null">#{fundPasswordSalt,jdbcType=VARCHAR},</if>
            <if test="storeDefaultLogo != null">#{storeDefaultLogo,jdbcType=VARCHAR},</if>
            <if test="gid != null">#{gid,jdbcType=INTEGER},</if>
            <if test="focus != null">#{focus,jdbcType=TINYINT},</if>
            <if test="money != null">#{money,jdbcType=INTEGER},</if>
            <if test="amount != null">#{amount,jdbcType=INTEGER},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="diynum != null">#{diynum,jdbcType=INTEGER},</if>
            <if test="ispush != null">#{ispush,jdbcType=TINYINT},</if>
            <if test="roleId != null">#{roleId,jdbcType=INTEGER},</if>
            <if test="vendor != null">#{vendor,jdbcType=INTEGER},</if>
            <if test="apiUser != null">#{apiUser,jdbcType=TINYINT},</if>
            <if test="cardNum != null">#{cardNum,jdbcType=INTEGER},</if>
            <if test="isApply != null">#{isApply,jdbcType=TINYINT},</if>
            <if test="isJdpay != null">#{isJdpay,jdbcType=TINYINT},</if>
            <if test="unionId != null">#{unionId,jdbcType=INTEGER},</if>
            <if test="viptime != null">#{viptime,jdbcType=INTEGER},</if>
            <if test="isAlipay != null">#{isAlipay,jdbcType=TINYINT},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="parentId != null">#{parentId,jdbcType=INTEGER},</if>
            <if test="payLimit != null">#{payLimit,jdbcType=INTEGER},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="salesman != null">#{salesman,jdbcType=INTEGER},</if>
            <if test="wxStatus != null">#{wxStatus,jdbcType=TINYINT},</if>
            <if test="applyTime != null">#{applyTime,jdbcType=INTEGER},</if>
            <if test="isConfirm != null">#{isConfirm,jdbcType=TINYINT},</if>
            <if test="versionId != null">#{versionId,jdbcType=TINYINT},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="connectnum != null">#{connectnum,jdbcType=INTEGER},</if>
            <if test="createtime != null">#{createtime,jdbcType=INTEGER},</if>
            <if test="dealamount != null">#{dealamount,jdbcType=INTEGER},</if>
            <if test="isGroupBuy != null">#{isGroupBuy,jdbcType=TINYINT},</if>
            <if test="isOpenMina != null">#{isOpenMina,jdbcType=TINYINT},</if>
            <if test="isProtocol != null">#{isProtocol,jdbcType=INTEGER},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=TINYINT},</if>
            <if test="onlinetime != null">#{onlinetime,jdbcType=INTEGER},</if>
            <if test="protocolId != null">#{protocolId,jdbcType=INTEGER},</if>
            <if test="salerAudit != null">#{salerAudit,jdbcType=TINYINT},</if>
            <if test="voiceOnOff != null">#{voiceOnOff,jdbcType=TINYINT},</if>
            <if test="activitynum != null">#{activitynum,jdbcType=INTEGER},</if>
            <if test="cashoutLock != null">#{cashoutLock,jdbcType=TINYINT},</if>
            <if test="confirmTime != null">#{confirmTime,jdbcType=INTEGER},</if>
            <if test="isQuickCash != null">#{isQuickCash,jdbcType=TINYINT},</if>
            <if test="mcardStatus != null">#{mcardStatus,jdbcType=TINYINT},</if>
            <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
            <if test="totalsmsnum != null">#{totalsmsnum,jdbcType=INTEGER},</if>
            <if test="incomeStatus != null">#{incomeStatus,jdbcType=TINYINT},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=TINYINT},</if>
            <if test="protocolTime != null">#{protocolTime,jdbcType=INTEGER},</if>
            <if test="psModifyTime != null">#{psModifyTime,jdbcType=INTEGER},</if>
            <if test="totalsmsused != null">#{totalsmsused,jdbcType=INTEGER},</if>
            <if test="transferTime != null">#{transferTime,jdbcType=INTEGER},</if>
            <if test="isScanService != null">#{isScanService,jdbcType=TINYINT},</if>
            <if test="isemscnplpush != null">#{isemscnplpush,jdbcType=TINYINT},</if>
            <if test="pwResetStatus != null">#{pwResetStatus,jdbcType=TINYINT},</if>
            <if test="rechargeLimit != null">#{rechargeLimit,jdbcType=INTEGER},</if>
            <if test="wechatCardNum != null">#{wechatCardNum,jdbcType=INTEGER},</if>
            <if test="attachmentsize != null">#{attachmentsize,jdbcType=INTEGER},</if>
            <if test="autoWithdrawal != null">#{autoWithdrawal,jdbcType=TINYINT},</if>
            <if test="lastloginmonth != null">#{lastloginmonth,jdbcType=SMALLINT},</if>
            <if test="lifecircleTime != null">#{lifecircleTime,jdbcType=INTEGER},</if>
            <if test="liquidationType != null">#{liquidationType,jdbcType=TINYINT},</if>
            <if test="cardCreateStatus != null">#{cardCreateStatus,jdbcType=TINYINT},</if>
            <if test="latestonlinetime != null">#{latestonlinetime,jdbcType=INTEGER},</if>
            <if test="isServicenoAccess != null">#{isServicenoAccess,jdbcType=TINYINT},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finance != null">#{finance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getSalesmanUidBySalesman" multiplicity="many" resulttype="java.lang.Integer" remark="">
        SELECT id
        FROM tp_users
        WHERE
        salesman = #{salesman,jdbcType=INTEGER}
        AND status != 5
    </operation>

    <operation name="batchFindByUidList" multiplicity="many" remark="根据商户id列表获取商户信息列表">
        SELECT *
        FROM tp_users
        <where>
            id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </where>
    </operation>


    <operation name="findMerchantNameByIdList" multiplicity="many" remark="根据商户id列表获取商户信息列表">
        SELECT id,username
        FROM tp_users
        <where>
            id IN
            <foreach collection="list" item="item" open="(" close=")" separator=",">
                #{item,jdbcType=INTEGER}
            </foreach>
        </where>
    </operation>
</table>
