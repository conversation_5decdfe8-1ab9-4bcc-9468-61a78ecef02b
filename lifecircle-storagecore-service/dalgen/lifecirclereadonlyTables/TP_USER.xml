<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_USER" physicalName="TP_USER"
       remark="TP_USER">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_USER">
        INSERT INTO TP_USER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="qq != null">`QQ`,</if>
            <if test="tel != null">`TEL`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="bank != null">`BANK`,</if>
            <if test="city != null">`CITY`,</if>
            <if test="logo != null">`LOGO`,</if>
            <if test="email != null">`EMAIL`,</if>
            <if test="level != null">`LEVEL`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="uname != null">`UNAME`,</if>
            <if test="people != null">`PEOPLE`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="utoken != null">`UTOKEN`,</if>
            <if test="wechat != null">`WECHAT`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="cardNum != null">`CARD_NUM`,</if>
            <if test="cardWeb != null">`CARD_WEB`,</if>
            <if test="business != null">`BUSINESS`,</if>
            <if test="cardname != null">`CARDNAME`,</if>
            <if test="contacts != null">`CONTACTS`,</if>
            <if test="password != null">`PASSWORD`,</if>
            <if test="province != null">`PROVINCE`,</if>
            <if test="turnover != null">`TURNOVER`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="privileges != null">`PRIVILEGES`,</if>
            <if test="profession != null">`PROFESSION`,</if>
            <if test="cardAddress != null">`CARD_ADDRESS`,</if>
            <if test="companyname != null">`COMPANYNAME`,</if>
            <if test="lastLoginIp != null">`LAST_LOGIN_IP`,</if>
            <if test="businessArea != null">`BUSINESS_AREA`,</if>
            <if test="businessCity != null">`BUSINESS_CITY`,</if>
            <if test="customerNote != null">`CUSTOMER_NOTE`,</if>
            <if test="lastLocation != null">`LAST_LOCATION`,</if>
            <if test="verifyimages != null">`VERIFYIMAGES`,</if>
            <if test="alipayaccount != null">`ALIPAYACCOUNT`,</if>
            <if test="openApiCallback != null">`OPEN_API_CALLBACK`,</if>
            <if test="businessProvince != null">`BUSINESS_PROVINCE`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="cash != null">`CASH`,</if>
            <if test="role != null">`ROLE`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="isOpen != null">`IS_OPEN`,</if>
            <if test="isPass != null">`IS_PASS`,</if>
            <if test="ownRun != null">`OWN_RUN`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="isJdpay != null">`IS_JDPAY`,</if>
            <if test="isWhite != null">`IS_WHITE`,</if>
            <if test="viptime != null">`VIPTIME`,</if>
            <if test="childNum != null">`CHILD_NUM`,</if>
            <if test="groupNum != null">`GROUP_NUM`,</if>
            <if test="isAlipay != null">`IS_ALIPAY`,</if>
            <if test="platform != null">`PLATFORM`,</if>
            <if test="isSelfFee != null">`IS_SELF_FEE`,</if>
            <if test="configType != null">`CONFIG_TYPE`,</if>
            <if test="createtime != null">`CREATETIME`,</if>
            <if test="isOpenMina != null">`IS_OPEN_MINA`,</if>
            <if test="isSalesman != null">`IS_SALESMAN`,</if>
            <if test="isShowTips != null">`IS_SHOW_TIPS`,</if>
            <if test="loanStatus != null">`LOAN_STATUS`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="isQuickCash != null">`IS_QUICK_CASH`,</if>
            <if test="subConfigId != null">`SUB_CONFIG_ID`,</if>
            <if test="useGroupNum != null">`USE_GROUP_NUM`,</if>
            <if test="salesStaffId != null">`SALES_STAFF_ID`,</if>
            <if test="isScanService != null">`IS_SCAN_SERVICE`,</if>
            <if test="lastLoginTime != null">`LAST_LOGIN_TIME`,</if>
            <if test="customerService != null">`CUSTOMER_SERVICE`,</if>
            <if test="isOpenapiAccess != null">`IS_OPENAPI_ACCESS`,</if>
            <if test="isSuperSalesman != null">`IS_SUPER_SALESMAN`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="advertisementNum != null">`ADVERTISEMENT_NUM`,</if>
            <if test="operationService != null">`OPERATION_SERVICE`,</if>
            <if test="newVersionUsersNum != null">`NEW_VERSION_USERS_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="finance != null">`FINANCE`,</if>
            <if test="advertisementBalance != null">`ADVERTISEMENT_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="qq != null">#{qq,jdbcType=VARCHAR},</if>
            <if test="tel != null">#{tel,jdbcType=VARCHAR},</if>
            <if test="area != null">#{area,jdbcType=CHAR},</if>
            <if test="bank != null">#{bank,jdbcType=VARCHAR},</if>
            <if test="city != null">#{city,jdbcType=CHAR},</if>
            <if test="logo != null">#{logo,jdbcType=VARCHAR},</if>
            <if test="email != null">#{email,jdbcType=VARCHAR},</if>
            <if test="level != null">#{level,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="uname != null">#{uname,jdbcType=VARCHAR},</if>
            <if test="people != null">#{people,jdbcType=VARCHAR},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="utoken != null">#{utoken,jdbcType=VARCHAR},</if>
            <if test="wechat != null">#{wechat,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="cardNum != null">#{cardNum,jdbcType=VARCHAR},</if>
            <if test="cardWeb != null">#{cardWeb,jdbcType=VARCHAR},</if>
            <if test="business != null">#{business,jdbcType=VARCHAR},</if>
            <if test="cardname != null">#{cardname,jdbcType=VARCHAR},</if>
            <if test="contacts != null">#{contacts,jdbcType=VARCHAR},</if>
            <if test="password != null">#{password,jdbcType=CHAR},</if>
            <if test="province != null">#{province,jdbcType=CHAR},</if>
            <if test="turnover != null">#{turnover,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="privileges != null">#{privileges,jdbcType=VARCHAR},</if>
            <if test="profession != null">#{profession,jdbcType=VARCHAR},</if>
            <if test="cardAddress != null">#{cardAddress,jdbcType=VARCHAR},</if>
            <if test="companyname != null">#{companyname,jdbcType=VARCHAR},</if>
            <if test="lastLoginIp != null">#{lastLoginIp,jdbcType=VARCHAR},</if>
            <if test="businessArea != null">#{businessArea,jdbcType=CHAR},</if>
            <if test="businessCity != null">#{businessCity,jdbcType=CHAR},</if>
            <if test="customerNote != null">#{customerNote,jdbcType=VARCHAR},</if>
            <if test="lastLocation != null">#{lastLocation,jdbcType=VARCHAR},</if>
            <if test="verifyimages != null">#{verifyimages,jdbcType=VARCHAR},</if>
            <if test="alipayaccount != null">#{alipayaccount,jdbcType=VARCHAR},</if>
            <if test="openApiCallback != null">#{openApiCallback,jdbcType=VARCHAR},</if>
            <if test="businessProvince != null">#{businessProvince,jdbcType=CHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="cash != null">#{cash,jdbcType=INTEGER},</if>
            <if test="role != null">#{role,jdbcType=SMALLINT},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="isOpen != null">#{isOpen,jdbcType=TINYINT},</if>
            <if test="isPass != null">#{isPass,jdbcType=TINYINT},</if>
            <if test="ownRun != null">#{ownRun,jdbcType=TINYINT},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="isJdpay != null">#{isJdpay,jdbcType=TINYINT},</if>
            <if test="isWhite != null">#{isWhite,jdbcType=TINYINT},</if>
            <if test="viptime != null">#{viptime,jdbcType=INTEGER},</if>
            <if test="childNum != null">#{childNum,jdbcType=INTEGER},</if>
            <if test="groupNum != null">#{groupNum,jdbcType=INTEGER},</if>
            <if test="isAlipay != null">#{isAlipay,jdbcType=TINYINT},</if>
            <if test="platform != null">#{platform,jdbcType=TINYINT},</if>
            <if test="isSelfFee != null">#{isSelfFee,jdbcType=TINYINT},</if>
            <if test="configType != null">#{configType,jdbcType=INTEGER},</if>
            <if test="createtime != null">#{createtime,jdbcType=INTEGER},</if>
            <if test="isOpenMina != null">#{isOpenMina,jdbcType=TINYINT},</if>
            <if test="isSalesman != null">#{isSalesman,jdbcType=TINYINT},</if>
            <if test="isShowTips != null">#{isShowTips,jdbcType=TINYINT},</if>
            <if test="loanStatus != null">#{loanStatus,jdbcType=TINYINT},</if>
            <if test="accountType != null">#{accountType,jdbcType=TINYINT},</if>
            <if test="isQuickCash != null">#{isQuickCash,jdbcType=TINYINT},</if>
            <if test="subConfigId != null">#{subConfigId,jdbcType=INTEGER},</if>
            <if test="useGroupNum != null">#{useGroupNum,jdbcType=INTEGER},</if>
            <if test="salesStaffId != null">#{salesStaffId,jdbcType=INTEGER},</if>
            <if test="isScanService != null">#{isScanService,jdbcType=TINYINT},</if>
            <if test="lastLoginTime != null">#{lastLoginTime,jdbcType=INTEGER},</if>
            <if test="customerService != null">#{customerService,jdbcType=INTEGER},</if>
            <if test="isOpenapiAccess != null">#{isOpenapiAccess,jdbcType=TINYINT},</if>
            <if test="isSuperSalesman != null">#{isSuperSalesman,jdbcType=TINYINT},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="advertisementNum != null">#{advertisementNum,jdbcType=INTEGER},</if>
            <if test="operationService != null">#{operationService,jdbcType=INTEGER},</if>
            <if test="newVersionUsersNum != null">#{newVersionUsersNum,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="finance != null">#{finance,jdbcType=DECIMAL},</if>
            <if test="advertisementBalance != null">#{advertisementBalance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findUserNameByIdList" multiplicity="many">
        SELECT id,username,own_run,belong,companyname
        FROM tp_user
        WHERE id in
        <foreach collection="list" item="agentId" open="(" close=")" separator=",">
            #{agentId,jdbcType=INTEGER}
        </foreach>
    </operation>

    <operation name="findSimpleUserNameByIdList" multiplicity="many">
        SELECT id,username,own_run,belong
        FROM tp_user
        WHERE id in
        <foreach collection="list" item="agentId" open="(" close=")" separator=",">
            #{agentId,jdbcType=INTEGER}
        </foreach>
        and belong = 0;
    </operation>
</table>
