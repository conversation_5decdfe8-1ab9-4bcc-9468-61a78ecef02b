<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="TP_LIFECIRCLE_GENERAL_BANK" physicalName="TP_LIFECIRCLE_GENERAL_BANK"
    remark="总行表(新)">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:TP_LIFECIRCLE_GENERAL_BANK">
INSERT INTO TP_LIFECIRCLE_GENERAL_BANK
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="bankFullName != null">`BANK_FULL_NAME`,</if>
        <if test="bankShortName != null">`BANK_SHORT_NAME`,</if>
        <if test="bankPackageImg != null">`BANK_PACKAGE_IMG`,</if>
        <if test="bankPackageLogo != null">`BANK_PACKAGE_LOGO`,</if>
        <if test="generalBankCode != null">`GENERAL_BANK_CODE`,</if>
        <if test="isDel != null">`IS_DEL`,</if>
        <if test="showFlag != null">`SHOW_FLAG`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="bankFullName != null">#{bankFullName,jdbcType=VARCHAR},</if>
        <if test="bankShortName != null">#{bankShortName,jdbcType=VARCHAR},</if>
        <if test="bankPackageImg != null">#{bankPackageImg,jdbcType=VARCHAR},</if>
        <if test="bankPackageLogo != null">#{bankPackageLogo,jdbcType=VARCHAR},</if>
        <if test="generalBankCode != null">#{generalBankCode,jdbcType=VARCHAR},</if>
        <if test="isDel != null">#{isDel,jdbcType=TINYINT},</if>
        <if test="showFlag != null">#{showFlag,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getByBankCode" paramtype="primitive" multiplicity="one"
               remark="根据银行code查询信息">
        select *
        from TP_LIFECIRCLE_GENERAL_BANK
        where
        is_del = 0
        and general_bank_code = #{generalBankCode,jdbcType=VARCHAR}
        LIMIT 1
    </operation>
    </table>
