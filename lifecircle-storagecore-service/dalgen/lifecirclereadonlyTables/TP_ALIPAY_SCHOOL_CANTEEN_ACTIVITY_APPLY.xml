<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY" physicalName="TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY"
       remark="支付宝高校食堂活动报名表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY">
        INSERT INTO TP_ALIPAY_SCHOOL_CANTEEN_ACTIVITY_APPLY
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="smid != null">`SMID`,</if>
            <if test="cardNo != null">`CARD_NO`,</if>
            <if test="applyId != null">`APPLY_ID`,</if>
            <if test="company != null">`COMPANY`,</if>
            <if test="cardName != null">`CARD_NAME`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="merchantNo != null">`MERCHANT_NO`,</if>
            <if test="rejectInfo != null">`REJECT_INFO`,</if>
            <if test="activityType != null">`ACTIVITY_TYPE`,</if>
            <if test="industryCode != null">`INDUSTRY_CODE`,</if>
            <if test="bankBranchName != null">`BANK_BRANCH_NAME`,</if>
            <if test="storeInDoorImg != null">`STORE_IN_DOOR_IMG`,</if>
            <if test="storeEntranceImg != null">`STORE_ENTRANCE_IMG`,</if>
            <if test="businessLicensePic != null">`BUSINESS_LICENSE_PIC`,</if>
            <if test="legalIdCardBackPic != null">`LEGAL_ID_CARD_BACK_PIC`,</if>
            <if test="bankAccountProvePic != null">`BANK_ACCOUNT_PROVE_PIC`,</if>
            <if test="legalIdCardFrontPic != null">`LEGAL_ID_CARD_FRONT_PIC`,</if>
            <if test="bankCooperationAgreementPic != null">`BANK_COOPERATION_AGREEMENT_PIC`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="belong != null">`BELONG`,</if>
            <if test="channelId != null">`CHANNEL_ID`,</if>
            <if test="hasShopId != null">`HAS_SHOP_ID`,</if>
            <if test="schoolType != null">`SCHOOL_TYPE`,</if>
            <if test="activityStatus != null">`ACTIVITY_STATUS`,</if>
            <if test="activityOpenTime != null">`ACTIVITY_OPEN_TIME`,</if>
            <if test="activitySignTime != null">`ACTIVITY_SIGN_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="smid != null">#{smid,jdbcType=VARCHAR},</if>
            <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
            <if test="applyId != null">#{applyId,jdbcType=VARCHAR},</if>
            <if test="company != null">#{company,jdbcType=VARCHAR},</if>
            <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="merchantNo != null">#{merchantNo,jdbcType=VARCHAR},</if>
            <if test="rejectInfo != null">#{rejectInfo,jdbcType=VARCHAR},</if>
            <if test="activityType != null">#{activityType,jdbcType=VARCHAR},</if>
            <if test="industryCode != null">#{industryCode,jdbcType=VARCHAR},</if>
            <if test="bankBranchName != null">#{bankBranchName,jdbcType=VARCHAR},</if>
            <if test="storeInDoorImg != null">#{storeInDoorImg,jdbcType=VARCHAR},</if>
            <if test="storeEntranceImg != null">#{storeEntranceImg,jdbcType=VARCHAR},</if>
            <if test="businessLicensePic != null">#{businessLicensePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardBackPic != null">#{legalIdCardBackPic,jdbcType=VARCHAR},</if>
            <if test="bankAccountProvePic != null">#{bankAccountProvePic,jdbcType=VARCHAR},</if>
            <if test="legalIdCardFrontPic != null">#{legalIdCardFrontPic,jdbcType=VARCHAR},</if>
            <if test="bankCooperationAgreementPic != null">#{bankCooperationAgreementPic,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="belong != null">#{belong,jdbcType=INTEGER},</if>
            <if test="channelId != null">#{channelId,jdbcType=INTEGER},</if>
            <if test="hasShopId != null">#{hasShopId,jdbcType=TINYINT},</if>
            <if test="schoolType != null">#{schoolType,jdbcType=TINYINT},</if>
            <if test="activityStatus != null">#{activityStatus,jdbcType=TINYINT},</if>
            <if test="activityOpenTime != null">#{activityOpenTime,jdbcType=INTEGER},</if>
            <if test="activitySignTime != null">#{activitySignTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="findApplyList"
               multiplicity="paging"
               paging="FindApplyList"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.FindApplyPageDTO"
               remark="crm后台，查询支付宝间连高校食堂活动报名导出列表">
        SELECT
        users.id as merchantId,
        users.username as username,
        regist.merchant_no as merchantNo,
        regist.smid as smid,
        regist.activity_sign_time as activitySignTime,
        IFNULL(regist.company, users.company) as company,
        regist.belong as belong,
        regist.has_shop_id as hasShopId,
        IFNULL(regist.activity_status, 1) as activityStatus,
        regist.activity_open_time as activityOpenTime,
        regist.subject_type as subjectType
        FROM
        tp_alipay_school_canteen_activity_apply regist
        LEFT JOIN tp_users users
        on users.id = regist.uid
        <where>
            <if test="username != null and username != ''">
                and users.username LIKE CONCAT (#{username,jdbcType=VARCHAR},'%')
            </if>
            <if test="merchantNo != null">
                and regist.merchant_no = #{merchantNo,jdbcType=INTEGER}
            </if>
            <if test="company != null and company != ''">
                and users.company LIKE CONCAT (#{company,jdbcType=VARCHAR},'%')
            </if>
            <if test="list != null and list.size() > 0">
                and regist.belong in
                <foreach close=")" collection="list" index="index" item="belong" open="(" separator=",">
                    #{belong, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="uid != null">
                and users.id = #{uid,jdbcType=INTEGER}
            </if>
            <if test="activityStatus != null and activityStatus != -1">
                and regist.activity_status = #{activityStatus, jdbcType=INTEGER}
            </if>
            <if test="activityStatusList != null and activityStatusList.size() > 0">
                and regist.activity_status in
                <foreach close=")" collection="activityStatusList" index="index" item="status" open="(" separator=",">
                    #{status, jdbcType=INTEGER}
                </foreach>
            </if>
            <if test="null != hasShopId and hasShopId != -1">
                and regist.has_shop_id = #{hasShopId, jdbcType=INTEGER}
            </if>
            <if test="activityOpenTimeEndTime != null ">
                AND regist.activity_open_time <![CDATA[ <= ]]>  #{activityOpenTimeEndTime,jdbcType=INTEGER}
            </if>
            <if test="activityOpenTimeBeginTime != null ">
                AND regist.activity_open_time <![CDATA[ >= ]]>  #{activityOpenTimeBeginTime,jdbcType=INTEGER}
            </if>
            <if test="null != subjectType and subjectType != -1">
                and regist.subject_type = #{subjectType, jdbcType=INTEGER}
            </if>
        </where>
        order by regist.`create_time` DESC
    </operation>

</table>
