<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_OPEN_LOCATION" physicalName="TP_MERCHANT_OPEN_LOCATION"
       remark="开户人位置记录表">


    <resultmap name="MerchantOpenRiskList" type="MerchantOpenRiskList">
        <column name="operate_id" jdbctype="INTEGER" javatype="Integer" remark="操作人编号"/>
        <column name="operate_name" jdbctype="VARCHAR" javatype="String" remark="操作人名称"/>
        <column name="uid" jdbctype="INTEGER" javatype="Integer" remark="商户编号"/>
        <column name="user_name" jdbctype="VARCHAR" javatype="String" remark="商户名称"/>
        <column name="agent_id" jdbctype="INTEGER" javatype="Integer" remark="代理商编号"/>
        <column name="agent_name" jdbctype="VARCHAR" javatype="String" remark="代理商名称"/>
        <column name="sales_id" jdbctype="INTEGER" javatype="Integer" remark="授理商编号"/>
        <column name="sales_name" jdbctype="VARCHAR" javatype="String" remark="授理商名称"/>
        <column name="province_name" jdbctype="VARCHAR" javatype="String" remark="操作人省"/>
        <column name="city_name" jdbctype="VARCHAR" javatype="String" remark="操作人市"/>
        <column name="area_name" jdbctype="VARCHAR" javatype="String" remark="操作人区"/>
        <column name="merchant_province_name" jdbctype="VARCHAR" javatype="String" remark="商户开户选择省"/>
        <column name="merchant_city_name" jdbctype="VARCHAR" javatype="String" remark="商户开户选择市"/>
        <column name="merchant_area_name" jdbctype="VARCHAR" javatype="String" remark="商户开户选择区"/>
    </resultmap>


    <operation name="findOpenRiskList" multiplicity="many" resultmap="MerchantOpenRiskList" remark="根据商户id查询出异地开户情况信息">
        SELECT
        location.`user_id` as operateId,
        location.`username` as operateName,
        location.uid as uid,
        location.`merchant_username` as userName,
        b.`id` as agentId,
        b.`username` as agentName,
        c.id as salesId,
        c.`username` as salesName,
        location.`province_name` as provinceName,
        location.`city_name` as cityName,
        location.`area_name` as areaName,
        location.`merchant_province_name` as merchantProvinceName,
        location.`merchant_city_name` as merchantCityName,
        location.`merchant_area_name` as merchantAreaName
        FROM
        `tp_merchant_open_location` location
        LEFT JOIN `tp_users` users on location.`uid` = users.id
        LEFT JOIN tp_user b on users.`belong` = b.id
        LEFT JOIN `tp_user` c on users.`salesman` = c.id
        LEFT JOIN `tp_users_entry_ext` ext on users.`users_token` = ext.`token`
        LEFT JOIN `tp_merchant_number` number on location.`uid` = number.`uid`
        WHERE
        location.`city_code` != location.`merchant_city_code`
        and users.`id` in
        <foreach collection="list" item="item" open="(" close=")" separator=",">
            #{item,jdbcType=INTEGER}
        </foreach>
    </operation>

</table>
