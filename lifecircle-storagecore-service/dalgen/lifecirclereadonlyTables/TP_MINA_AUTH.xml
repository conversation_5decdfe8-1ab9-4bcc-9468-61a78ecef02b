<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MINA_AUTH" physicalName="TP_MINA_AUTH"
       remark="模板小程序授权记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MINA_AUTH">
        INSERT INTO TP_MINA_AUTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="appid != null">`APPID`,</if>
            <if test="openid != null">`OPENID`,</if>
            <if test="accountName != null">`ACCOUNT_NAME`,</if>
            <if test="minaHeadImg != null">`MINA_HEAD_IMG`,</if>
            <if test="preAuthCode != null">`PRE_AUTH_CODE`,</if>
            <if test="auditVersion != null">`AUDIT_VERSION`,</if>
            <if test="deployRemark != null">`DEPLOY_REMARK`,</if>
            <if test="minaNickName != null">`MINA_NICK_NAME`,</if>
            <if test="minaUserName != null">`MINA_USER_NAME`,</if>
            <if test="platformType != null">`PLATFORM_TYPE`,</if>
            <if test="auditDeployId != null">`AUDIT_DEPLOY_ID`,</if>
            <if test="minaIntroduce != null">`MINA_INTRODUCE`,</if>
            <if test="onlineVersion != null">`ONLINE_VERSION`,</if>
            <if test="principalName != null">`PRINCIPAL_NAME`,</if>
            <if test="onlineDeployId != null">`ONLINE_DEPLOY_ID`,</if>
            <if test="authorizerRefreshToken != null">`AUTHORIZER_REFRESH_TOKEN`,</if>
            <if test="step != null">`STEP`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="authType != null">`AUTH_TYPE`,</if>
            <if test="hasDeploy != null">`HAS_DEPLOY`,</if>
            <if test="authStatus != null">`AUTH_STATUS`,</if>
            <if test="merchantId != null">`MERCHANT_ID`,</if>
            <if test="syncStatus != null">`SYNC_STATUS`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="qrcodeStatus != null">`QRCODE_STATUS`,</if>
            <if test="updateAuditStatus != null">`UPDATE_AUDIT_STATUS`,</if>
            <if test="platformVerifyType != null">`PLATFORM_VERIFY_TYPE`,</if>
            <if test="serverDomainStatus != null">`SERVER_DOMAIN_STATUS`,</if>
            <if test="businessDomainStatus != null">`BUSINESS_DOMAIN_STATUS`,</if>
            <if test="authTime != null">`AUTH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="submitTime != null">`SUBMIT_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="statusUpdateTime != null">`STATUS_UPDATE_TIME`,</if>
            <if test="onlineVersionTime != null">`ONLINE_VERSION_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="appid != null">#{appid,jdbcType=VARCHAR},</if>
            <if test="openid != null">#{openid,jdbcType=VARCHAR},</if>
            <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
            <if test="minaHeadImg != null">#{minaHeadImg,jdbcType=VARCHAR},</if>
            <if test="preAuthCode != null">#{preAuthCode,jdbcType=VARCHAR},</if>
            <if test="auditVersion != null">#{auditVersion,jdbcType=VARCHAR},</if>
            <if test="deployRemark != null">#{deployRemark,jdbcType=VARCHAR},</if>
            <if test="minaNickName != null">#{minaNickName,jdbcType=VARCHAR},</if>
            <if test="minaUserName != null">#{minaUserName,jdbcType=VARCHAR},</if>
            <if test="platformType != null">#{platformType,jdbcType=VARCHAR},</if>
            <if test="auditDeployId != null">#{auditDeployId,jdbcType=VARCHAR},</if>
            <if test="minaIntroduce != null">#{minaIntroduce,jdbcType=VARCHAR},</if>
            <if test="onlineVersion != null">#{onlineVersion,jdbcType=VARCHAR},</if>
            <if test="principalName != null">#{principalName,jdbcType=VARCHAR},</if>
            <if test="onlineDeployId != null">#{onlineDeployId,jdbcType=VARCHAR},</if>
            <if test="authorizerRefreshToken != null">#{authorizerRefreshToken,jdbcType=VARCHAR},</if>
            <if test="step != null">#{step,jdbcType=TINYINT},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="authType != null">#{authType,jdbcType=TINYINT},</if>
            <if test="hasDeploy != null">#{hasDeploy,jdbcType=TINYINT},</if>
            <if test="authStatus != null">#{authStatus,jdbcType=TINYINT},</if>
            <if test="merchantId != null">#{merchantId,jdbcType=INTEGER},</if>
            <if test="syncStatus != null">#{syncStatus,jdbcType=TINYINT},</if>
            <if test="accountType != null">#{accountType,jdbcType=TINYINT},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=TINYINT},</if>
            <if test="qrcodeStatus != null">#{qrcodeStatus,jdbcType=TINYINT},</if>
            <if test="updateAuditStatus != null">#{updateAuditStatus,jdbcType=TINYINT},</if>
            <if test="platformVerifyType != null">#{platformVerifyType,jdbcType=TINYINT},</if>
            <if test="serverDomainStatus != null">#{serverDomainStatus,jdbcType=TINYINT},</if>
            <if test="businessDomainStatus != null">#{businessDomainStatus,jdbcType=TINYINT},</if>
            <if test="authTime != null">#{authTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="submitTime != null">#{submitTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="statusUpdateTime != null">#{statusUpdateTime,jdbcType=TIMESTAMP},</if>
            <if test="onlineVersionTime != null">#{onlineVersionTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getMinaAuthInfo" paramtype="primitive" multiplicity="one" remark="查询小程序信息">
        SELECT
        *
        FROM `tp_mina_auth`
        WHERE `merchant_id` = #{merchantId,jdbcType=INTEGER}
        <if test="platformType != null">
            and `platform_type` = #{platformType,jdbcType=VARCHAR}
        </if>
        <if test="hasDeploy != null">
            and `has_deploy` = #{hasDeploy,jdbcType=TINYINT}
        </if>
        and `del_flag` = 0
        order by `id` desc
        limit 1
    </operation>
</table>
