<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH" physicalName="FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH"
       remark="月维度流水奖励明细佣金表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH">
        INSERT INTO FP_FLOW_REWARD_DETAIL_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="currentAssessmentQuarter != null">`CURRENT_ASSESSMENT_QUARTER`,</if>
            <if test="highestAvgMonthlyFlowQuarter != null">`HIGHEST_AVG_MONTHLY_FLOW_QUARTER`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="netIncreaseFlow != null">`NET_INCREASE_FLOW`,</if>
            <if test="smallMerchantRatio != null">`SMALL_MERCHANT_RATIO`,</if>
            <if test="settlementCoefficient != null">`SETTLEMENT_COEFFICIENT`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
            <if test="currentAvgQuarterlyFlow != null">`CURRENT_AVG_QUARTERLY_FLOW`,</if>
            <if test="highestAvgQuarterlyFlow != null">`HIGHEST_AVG_QUARTERLY_FLOW`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="currentAssessmentQuarter != null">#{currentAssessmentQuarter,jdbcType=VARCHAR},</if>
            <if test="highestAvgMonthlyFlowQuarter != null">#{highestAvgMonthlyFlowQuarter,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="netIncreaseFlow != null">#{netIncreaseFlow,jdbcType=DECIMAL},</if>
            <if test="smallMerchantRatio != null">#{smallMerchantRatio,jdbcType=DECIMAL},</if>
            <if test="settlementCoefficient != null">#{settlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
            <if test="currentAvgQuarterlyFlow != null">#{currentAvgQuarterlyFlow,jdbcType=DECIMAL},</if>
            <if test="highestAvgQuarterlyFlow != null">#{highestAvgQuarterlyFlow,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getAgentFlowRewardCommissionList" multiplicity="many" remark="查询代理商流水奖励明细佣金">
        select
        *
        from fp_flow_reward_detail_commission_month
        where settlement_agent_id = #{agentId,jdbcType=INTEGER}
        AND business_date = #{businessDate,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        and fee_code = #{feeCode,jdbcType=VARCHAR}
    </operation>
</table>
