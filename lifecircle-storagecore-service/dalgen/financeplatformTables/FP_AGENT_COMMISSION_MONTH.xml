<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="fp_agent_commission_month" physicalName="fp_agent_commission_month" remark="月维度代理商佣金表">
    <!--  特殊字符说明 &lt;&gt; <> -->
    <!-- baseSql option中 select * 会自动替换为 include -->
    <sql id="Base_Column_List">
        ID,FEE_CODE,REMARKS,SOURCE_TYPE,BUSINESS_NUM
        ,AGENT_USERNAME,AGENT_COMPANYNAME,CPM_NUM,AGENT_ID,PT_MONTH
        ,BUSINESS_DATE,REVIEW_STATUS,EXPOSURE_NUM_HFIVE,NORMAL_TRADE_COUNT,REFUND_TRADE_COUNT
        ,EXPOSURE_NUM_APPLET,CREATE_TIME,UPDATE_TIME,TAX_RATE,NORMAL_TRADE_AMOUNT
        ,PAYABLE_COMMISSION,REFUND_TRADE_AMOUNT,DEDUCTION_COMMISSION,SETTLEMENT_COEFFICIENT,ACTUAL_PAYABLE_COMMISSION
    </sql>

    <!-- baseSql option中 select sf.* 会自动替换为 include -->
    <sql id="Base_SF_Column_List">
        sf.ID,sf.FEE_CODE,sf.REMARKS,sf.SOURCE_TYPE,sf.BUSINESS_NUM
        ,sf.AGENT_USERNAME,sf.AGENT_COMPANYNAME,sf.CPM_NUM,sf.AGENT_ID,sf.PT_MONTH
        ,sf.BUSINESS_DATE,sf.REVIEW_STATUS,sf.EXPOSURE_NUM_HFIVE,sf.NORMAL_TRADE_COUNT,sf.REFUND_TRADE_COUNT
        ,sf.EXPOSURE_NUM_APPLET,sf.CREATE_TIME,sf.UPDATE_TIME,sf.TAX_RATE,sf.NORMAL_TRADE_AMOUNT
        ,sf.PAYABLE_COMMISSION,sf.REFUND_TRADE_AMOUNT,sf.DEDUCTION_COMMISSION,sf.SETTLEMENT_COEFFICIENT,sf.ACTUAL_PAYABLE_COMMISSION
    </sql>

    <operation name="getByUniqBusinessNum" multiplicity="one"
               remark="根据唯一约束UniqBusinessNum获取数据:fp_agent_commission_month">
        SELECT *
        FROM fp_agent_commission_month
        WHERE
        <![CDATA[
            BUSINESS_NUM    = #{businessNum,jdbcType=VARCHAR}
        ]]>
    </operation>


</table>
