<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_MERCHANT_ORDER" physicalName="FP_MERCHANT_ORDER"
    remark="FP_MERCHANT_ORDER">


    <operation name="getSuperMerchantCommissionDetails" multiplicity="many" remark="超级商户佣金详情查询">
        select * from fp_merchant_order
        where 1 = 1
        <if test="merchantId != null and merchantId != ''">
            and uid = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="businessDate != null and businessDate != ''">
            and business_date = #{businessDate,jdbcType=INTEGER}
        </if>
        <if test="orderSn != null and orderSn != ''">
            and order_sn = #{orderSn,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null">
            and pay_time &gt;= #{startTime,jdbcType=TIMESTAMP}
        </if>
        <if test="endTime != null ">
            and pay_time &lt;= #{endTime,jdbcType=TIMESTAMP}
        </if>
        order by pay_time desc
    </operation>


</table>
