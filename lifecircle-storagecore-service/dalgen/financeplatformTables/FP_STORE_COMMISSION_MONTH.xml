<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_STORE_COMMISSION_MONTH" physicalName="FP_STORE_COMMISSION_MONTH"
       remark="月维度门店佣金表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FP_STORE_COMMISSION_MONTH">
        INSERT INTO FP_STORE_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="merchantType != null">`MERCHANT_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="marketManager != null">`MARKET_MANAGER`,</if>
            <if test="storeUsername != null">`STORE_USERNAME`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="merchantUsername != null">`MERCHANT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="superSalesmanUsername != null">`SUPER_SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="settlementCoefficient != null">`SETTLEMENT_COEFFICIENT`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="merchantType != null">#{merchantType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="marketManager != null">#{marketManager,jdbcType=VARCHAR},</if>
            <if test="storeUsername != null">#{storeUsername,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="merchantUsername != null">#{merchantUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="superSalesmanUsername != null">#{superSalesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="settlementCoefficient != null">#{settlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getStoreCommissionListPage" multiplicity="many" remark="根据代理商id查询商户月佣金列表">
        select * from FP_STORE_COMMISSION_MONTH
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </operation>

    <resultmap name="OpenAccountCommissionPayableDetailMap" type="OpenAccountCommissionPayableDetailMap">
        <column name="salesman_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="账号id"/>
        <column name="salesman_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="账号名称"/>
        <column name="normal_trade_count" javatype="java.lang.Integer" jdbctype="INTEGER" remark="交易笔数"/>
        <column name="normal_trade_amount" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="交易金额"/>
        <column name="actual_payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="实际应结佣金"/>
        <column name="settlement_coefficient" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="奖励系数"/>
    </resultmap>

    <operation name="getAgentCommissionList" multiplicity="many" resultmap="OpenAccountCommissionPayableDetailMap" remark="根据代理商id查询代理商月佣金列表">
        select
        agent_id salesman_id,
        agent_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
    </operation>

    <operation name="getSuperSalesmanCommissionList" multiplicity="many" resultmap="OpenAccountCommissionPayableDetailMap" remark="根据代理商id查询超级授理商月佣金列表">
        select
        super_salesman_id salesman_id,
        super_salesman_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
    </operation>

    <operation name="getSalesmanCommissionList" multiplicity="many" resultmap="OpenAccountCommissionPayableDetailMap" remark="根据代理商id查询授理商月佣金列表">
        select
        salesman_id salesman_id,
        salesman_username salesman_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(actual_payable_commission) actual_payable_commission,
        settlement_coefficient
        from fp_store_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
    </operation>
</table>
