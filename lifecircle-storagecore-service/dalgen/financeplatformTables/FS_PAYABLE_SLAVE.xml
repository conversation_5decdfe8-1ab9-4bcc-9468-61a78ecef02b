<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FS_PAYABLE_SLAVE" physicalName="FS_PAYABLE_SLAVE"
       remark="应付单明细表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FS_PAYABLE_SLAVE">
        INSERT INTO FS_PAYABLE_SLAVE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="erpSyncTimestamp != null">`ERP_SYNC_TIMESTAMP`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="payableSubNum != null">`PAYABLE_SUB_NUM`,</if>
            <if test="payableBillNum != null">`PAYABLE_BILL_NUM`,</if>
            <if test="deductDetailUrl != null">`DEDUCT_DETAIL_URL`,</if>
            <if test="normalDetailUrl != null">`NORMAL_DETAIL_URL`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="billStatus != null">`BILL_STATUS`,</if>
            <if test="erpSyncStatus != null">`ERP_SYNC_STATUS`,</if>
            <if test="businessStatus != null">`BUSINESS_STATUS`,</if>
            <if test="excelSyncStatus != null">`EXCEL_SYNC_STATUS`,</if>
            <if test="erpSyncFailTimes != null">`ERP_SYNC_FAIL_TIMES`,</if>
            <if test="excelSyncFailTimes != null">`EXCEL_SYNC_FAIL_TIMES`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="billAmount != null">`BILL_AMOUNT`,</if>
            <if test="lockAmount != null">`LOCK_AMOUNT`,</if>
            <if test="paidAmount != null">`PAID_AMOUNT`,</if>
            <if test="syncErpAmount != null">`SYNC_ERP_AMOUNT`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="erpSyncTimestamp != null">#{erpSyncTimestamp,jdbcType=BIGINT},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="payableSubNum != null">#{payableSubNum,jdbcType=VARCHAR},</if>
            <if test="payableBillNum != null">#{payableBillNum,jdbcType=VARCHAR},</if>
            <if test="deductDetailUrl != null">#{deductDetailUrl,jdbcType=VARCHAR},</if>
            <if test="normalDetailUrl != null">#{normalDetailUrl,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=INTEGER},</if>
            <if test="billStatus != null">#{billStatus,jdbcType=TINYINT},</if>
            <if test="erpSyncStatus != null">#{erpSyncStatus,jdbcType=TINYINT},</if>
            <if test="businessStatus != null">#{businessStatus,jdbcType=TINYINT},</if>
            <if test="excelSyncStatus != null">#{excelSyncStatus,jdbcType=TINYINT},</if>
            <if test="erpSyncFailTimes != null">#{erpSyncFailTimes,jdbcType=TINYINT},</if>
            <if test="excelSyncFailTimes != null">#{excelSyncFailTimes,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="billAmount != null">#{billAmount,jdbcType=DECIMAL},</if>
            <if test="lockAmount != null">#{lockAmount,jdbcType=DECIMAL},</if>
            <if test="paidAmount != null">#{paidAmount,jdbcType=DECIMAL},</if>
            <if test="syncErpAmount != null">#{syncErpAmount,jdbcType=DECIMAL},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <resultmap name="CommissionPayableInfoMap" type="CommissionPayableInfoMap">
        <column name="payable_sub_num" javatype="java.lang.String" jdbctype="VARCHAR" remark="应付子单号"/>
        <column name="payment_object_id" javatype="java.lang.String" jdbctype="VARCHAR" remark="代理商id"/>
        <column name="fee_code" javatype="java.lang.String" jdbctype="VARCHAR" remark="费用编码"/>
        <column name="fee_project_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="费用名称"/>
        <column name="is_details" javatype="java.lang.Integer" jdbctype="TINYINT" remark="是否有账单明细"/>
        <column name="bill_date" javatype="java.lang.Integer" jdbctype="INTEGER" remark="账单日期"/>
        <column name="settlement_coefficient" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="结算系数"/>
        <column name="actual_payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="实际应结佣金"/>
        <column name="payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="应结佣金"/>
        <column name="deduction_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="扣减金额"/>
        <column name="normal_trade_amount" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="交易金额"/>
        <column name="refund_trade_amount" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="退款金额"/>
        <column name="business_status" javatype="java.lang.Integer" jdbctype="TINYINT" remark="业务状态"/>
        <column name="payable_bill_num" javatype="java.lang.String" jdbctype="VARCHAR" remark="应付单号"/>
        <column name="is_t1_settle" javatype="java.lang.Integer" jdbctype="TINYINT" remark="是否t1结算"/>
    </resultmap>

    <operation name="getCommissionPayableInfo" multiplicity="one" resultmap="CommissionPayableInfoMap"
               remark="查询代理商佣金应付信息">
        select a.payable_sub_num,b.payment_object_id,a.bill_date,b.settlement_coefficient,a.normal_trade_amount,
        a.refund_trade_amount,a.bill_amount
        actual_payable_commission,a.payable_commission,a.deduction_commission,a.fee_code,b.business_num,fp.is_t1_settle
        from fs_payable_slave a
        JOIN fs_payable_master b on a.payable_bill_num = b.payable_bill_num
        JOIN fp_agent_commission_month fp on fp.business_num=b.business_num
        where a.payable_sub_num = #{payableSubNum,jdbcType=VARCHAR}
        and b.payment_object_id = #{agentId,jdbcType=VARCHAR}
        and b.bill_type = 1
        and b.source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        and b.payment_object_type = 1 and b.sync_bill_flag = 1
    </operation>

</table>
