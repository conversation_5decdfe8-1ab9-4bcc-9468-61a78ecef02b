<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_SALESMAN_BILL_DETAIL" physicalName="FP_SALESMAN_BILL_DETAIL"
       remark="月维度运营中心授理商账单明细表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FP_SALESMAN_BILL_DETAIL">
        INSERT INTO FP_SALESMAN_BILL_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="coefficientDiff != null">`COEFFICIENT_DIFF`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
            <if test="agentSettlementCoefficient != null">`AGENT_SETTLEMENT_COEFFICIENT`,</if>
            <if test="salesmanSettlementCoefficient != null">`SALESMAN_SETTLEMENT_COEFFICIENT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="coefficientDiff != null">#{coefficientDiff,jdbcType=DECIMAL},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
            <if test="agentSettlementCoefficient != null">#{agentSettlementCoefficient,jdbcType=DECIMAL},</if>
            <if test="salesmanSettlementCoefficient != null">#{salesmanSettlementCoefficient,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="findSalesmanCommissionList" multiplicity="many">
        select * from fp_salesman_bill_detail
        where agent_id = #{agentId,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and business_date= #{businessDate,jdbcType=INTEGER}
        and source_type in
        <foreach collection="sourceTypeList" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </operation>
</table>
