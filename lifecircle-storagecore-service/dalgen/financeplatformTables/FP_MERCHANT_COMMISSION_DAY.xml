<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_MERCHANT_COMMISSION_DAY" physicalName="FP_MERCHANT_COMMISSION_DAY"
       remark="FP_MERCHANT_COMMISSION_DAY">

    <resultmap name="CommissionPayableDetailMap" type="CommissionPayableDetailMap">
        <column name="account_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="账号id"/>
        <column name="account_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="账号名称"/>
        <column name="normal_trade_count" javatype="java.lang.Integer" jdbctype="INTEGER" remark="交易笔数"/>
        <column name="normal_trade_amount" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="交易金额"/>
        <column name="payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="应结佣金"/>
        <column name="deduction_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="扣减佣金"/>
        <column name="actual_payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="实际应结佣金"/>
        <column name="exposure_num_hfive" javatype="java.lang.Integer" jdbctype="INTEGER" remark="曝光数（h5）"/>
        <column name="exposure_num_applet" javatype="java.lang.Integer" jdbctype="INTEGER" remark="曝光数（小程序）"/>
        <column name="cpm_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="支付后广告cpm"/>
        <column name="exposure_total_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="月曝光总次数"/>
        <column name="grant_fee" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="授理商佣金"/>
        <column name="agent_fee" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="代理商佣金"/>
    </resultmap>

    <operation name="getAgentCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询代理商月佣金列表">
        select
        agent_id account_id,
        agent_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
    </operation>

    <operation name="getSalesmanCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询授理商月佣金列表">
        select
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
    </operation>


    <operation name="getSuperSalesmanCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询超级授理商月佣金列表">
        select
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
    </operation>

    <operation name="getMerchantCommissionListPage" multiplicity="paging" paging="merchantCommissionDayListQuery"
               remark="根据代理商id查询商户日佣金列表">
        select * from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        order by create_time desc
    </operation>
    <operation name="getSuperMerchantCommissionList" multiplicity="many" remark="超级商户佣金列表查询">
        select * from fp_merchant_commission_day
        where fee_code = 712
        <if test="agentId != null and agentId != ''">
            and agent_id = #{agentId,jdbcType=INTEGER}
        </if>
        <if test="salesmanId != null and salesmanId != ''">
            and (salesman_id = #{salesmanId,jdbcType=INTEGER} or super_salesman_id = #{salesmanId,jdbcType=INTEGER})
        </if>
        <if test="merchantId != null and merchantId != ''">
            and uid = #{merchantId,jdbcType=INTEGER}
        </if>
        <if test="merchantName != null and merchantName != ''">
            and merchant_username = #{merchantName,jdbcType=VARCHAR}
        </if>
        <if test="startTime != null and startTime != ''">
            and business_date &gt;= #{startTime,jdbcType=INTEGER}
        </if>
        <if test="endTime != null and endTime != ''">
            and business_date &lt;= #{endTime,jdbcType=INTEGER}
        </if>
        order by create_time desc
    </operation>

    <operation name="getMerchantCommissionList" multiplicity="many" remark="根据代理商id查询商户月佣金列表">
        select * from fp_merchant_commission_day
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type = #{sourceType,jdbcType=VARCHAR}
        order by create_time desc
    </operation>

</table>
