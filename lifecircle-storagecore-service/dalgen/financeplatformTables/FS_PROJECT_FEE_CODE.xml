<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FS_PROJECT_FEE_CODE" physicalName="FS_PROJECT_FEE_CODE"
       remark="项目费用编码表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FS_PROJECT_FEE_CODE">
        INSERT INTO FS_PROJECT_FEE_CODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="stopTime != null">`STOP_TIME`,</if>
            <if test="remark != null">`REMARK`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="feeType != null">`FEE_TYPE`,</if>
            <if test="storeCode != null">`STORE_CODE`,</if>
            <if test="projectFeeId != null">`PROJECT_FEE_ID`,</if>
            <if test="erpSubjectCode != null">`ERP_SUBJECT_CODE`,</if>
            <if test="feeProjectName != null">`FEE_PROJECT_NAME`,</if>
            <if test="status != null">`STATUS`,</if>
            <if test="isDetails != null">`IS_DETAILS`,</if>
            <if test="erpSyncStatus != null">`ERP_SYNC_STATUS`,</if>
            <if test="commissionRulesFlag != null">`COMMISSION_RULES_FLAG`,</if>
            <if test="feeProjectAttribute != null">`FEE_PROJECT_ATTRIBUTE`,</if>
            <if test="paymentCheckAccount != null">`PAYMENT_CHECK_ACCOUNT`,</if>
            <if test="projectCategoryCode != null">`PROJECT_CATEGORY_CODE`,</if>
            <if test="projectBigCategoryCode != null">`PROJECT_BIG_CATEGORY_CODE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeRate != null">`FEE_RATE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="stopTime != null">#{stopTime,jdbcType=BIGINT},</if>
            <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="feeType != null">#{feeType,jdbcType=VARCHAR},</if>
            <if test="storeCode != null">#{storeCode,jdbcType=VARCHAR},</if>
            <if test="projectFeeId != null">#{projectFeeId,jdbcType=VARCHAR},</if>
            <if test="erpSubjectCode != null">#{erpSubjectCode,jdbcType=VARCHAR},</if>
            <if test="feeProjectName != null">#{feeProjectName,jdbcType=VARCHAR},</if>
            <if test="status != null">#{status,jdbcType=TINYINT},</if>
            <if test="isDetails != null">#{isDetails,jdbcType=TINYINT},</if>
            <if test="erpSyncStatus != null">#{erpSyncStatus,jdbcType=TINYINT},</if>
            <if test="commissionRulesFlag != null">#{commissionRulesFlag,jdbcType=TINYINT},</if>
            <if test="feeProjectAttribute != null">#{feeProjectAttribute,jdbcType=TINYINT},</if>
            <if test="paymentCheckAccount != null">#{paymentCheckAccount,jdbcType=TINYINT},</if>
            <if test="projectCategoryCode != null">#{projectCategoryCode,jdbcType=TINYINT},</if>
            <if test="projectBigCategoryCode != null">#{projectBigCategoryCode,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeRate != null">#{feeRate,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getFeeInfoByFeeCode" multiplicity="one" remark="根据费用编码查询费用信息">
        select *
        from fs_project_fee_code
        where fee_code = #{feeCode,jdbcType=VARCHAR}
        and status = 1
        limit 1
    </operation>
</table>
