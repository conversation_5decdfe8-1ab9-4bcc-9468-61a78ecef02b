<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_MERCHANT_COMMISSION_MONTH" physicalName="FP_MERCHANT_COMMISSION_MONTH"
       remark="月维度商户佣金表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:FP_MERCHANT_COMMISSION_MONTH">
        INSERT INTO FP_MERCHANT_COMMISSION_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="feeCode != null">`FEE_CODE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="agentUsername != null">`AGENT_USERNAME`,</if>
            <if test="marketManager != null">`MARKET_MANAGER`,</if>
            <if test="agentCompanyname != null">`AGENT_COMPANYNAME`,</if>
            <if test="merchantUsername != null">`MERCHANT_USERNAME`,</if>
            <if test="salesmanUsername != null">`SALESMAN_USERNAME`,</if>
            <if test="superSalesmanUsername != null">`SUPER_SALESMAN_USERNAME`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="cpmNum != null">`CPM_NUM`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="ptMonth != null">`PT_MONTH`,</if>
            <if test="marketId != null">`MARKET_ID`,</if>
            <if test="salesmanId != null">`SALESMAN_ID`,</if>
            <if test="businessDate != null">`BUSINESS_DATE`,</if>
            <if test="superSalesmanId != null">`SUPER_SALESMAN_ID`,</if>
            <if test="exposureNumHfive != null">`EXPOSURE_NUM_HFIVE`,</if>
            <if test="normalTradeCount != null">`NORMAL_TRADE_COUNT`,</if>
            <if test="refundTradeCount != null">`REFUND_TRADE_COUNT`,</if>
            <if test="exposureNumApplet != null">`EXPOSURE_NUM_APPLET`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="normalTradeAmount != null">`NORMAL_TRADE_AMOUNT`,</if>
            <if test="payableCommission != null">`PAYABLE_COMMISSION`,</if>
            <if test="refundTradeAmount != null">`REFUND_TRADE_AMOUNT`,</if>
            <if test="deductionCommission != null">`DEDUCTION_COMMISSION`,</if>
            <if test="actualPayableCommission != null">`ACTUAL_PAYABLE_COMMISSION`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="feeCode != null">#{feeCode,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="agentUsername != null">#{agentUsername,jdbcType=VARCHAR},</if>
            <if test="marketManager != null">#{marketManager,jdbcType=VARCHAR},</if>
            <if test="agentCompanyname != null">#{agentCompanyname,jdbcType=VARCHAR},</if>
            <if test="merchantUsername != null">#{merchantUsername,jdbcType=VARCHAR},</if>
            <if test="salesmanUsername != null">#{salesmanUsername,jdbcType=VARCHAR},</if>
            <if test="superSalesmanUsername != null">#{superSalesmanUsername,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="cpmNum != null">#{cpmNum,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="ptMonth != null">#{ptMonth,jdbcType=INTEGER},</if>
            <if test="marketId != null">#{marketId,jdbcType=INTEGER},</if>
            <if test="salesmanId != null">#{salesmanId,jdbcType=INTEGER},</if>
            <if test="businessDate != null">#{businessDate,jdbcType=INTEGER},</if>
            <if test="superSalesmanId != null">#{superSalesmanId,jdbcType=INTEGER},</if>
            <if test="exposureNumHfive != null">#{exposureNumHfive,jdbcType=INTEGER},</if>
            <if test="normalTradeCount != null">#{normalTradeCount,jdbcType=INTEGER},</if>
            <if test="refundTradeCount != null">#{refundTradeCount,jdbcType=INTEGER},</if>
            <if test="exposureNumApplet != null">#{exposureNumApplet,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="normalTradeAmount != null">#{normalTradeAmount,jdbcType=DECIMAL},</if>
            <if test="payableCommission != null">#{payableCommission,jdbcType=DECIMAL},</if>
            <if test="refundTradeAmount != null">#{refundTradeAmount,jdbcType=DECIMAL},</if>
            <if test="deductionCommission != null">#{deductionCommission,jdbcType=DECIMAL},</if>
            <if test="actualPayableCommission != null">#{actualPayableCommission,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getMerchantCommissionList" multiplicity="many" remark="根据代理商id查询商户月佣金列表">
        select * from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and source_type = #{sourceType,jdbcType=VARCHAR}
        order by create_time desc
    </operation>

    <resultmap name="CommissionPayableDetailMap" type="CommissionPayableDetailMap">
        <column name="account_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="账号id"/>
        <column name="account_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="账号名称"/>
        <column name="salesman_name" javatype="java.lang.String" jdbctype="VARCHAR" remark="账号名称"/>
        <column name="salesman_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="账号id"/>
        <column name="normal_trade_count" javatype="java.lang.Integer" jdbctype="INTEGER" remark="交易笔数"/>
        <column name="normal_trade_amount" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="交易金额"/>
        <column name="payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="应结佣金"/>
        <column name="deduction_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="扣减佣金"/>
        <column name="actual_payable_commission" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="实际应结佣金"/>
        <column name="exposure_num_hfive" javatype="java.lang.Integer" jdbctype="INTEGER" remark="曝光数（h5）"/>
        <column name="exposure_num_applet" javatype="java.lang.Integer" jdbctype="INTEGER" remark="曝光数（小程序）"/>
        <column name="cpm_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="支付后广告cpm"/>
        <column name="exposure_total_num" javatype="java.lang.Integer" jdbctype="INTEGER" remark="月曝光总次数"/>
        <column name="grant_fee" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="授理商佣金"/>
        <column name="agent_fee" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="代理商佣金"/>
        <column name="agent_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="代理商id"/>
        <column name="super_salesman_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="超级授理商id"/>
        <column name="salesman_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="授理商id"/>
        <column name="market_id" javatype="java.lang.Integer" jdbctype="INTEGER" remark="市场经理id"/>
        <column name="market_manager" javatype="java.lang.String" jdbctype="VARCHAR" remark="市场经理名称"/>
        <column name="market_fee" javatype="java.math.BigDecimal" jdbctype="DECIMAL" remark="市场经理佣金"/>
        <column name="business_date" javatype="java.lang.Integer" jdbctype="INTEGER" remark="业务日期"/>
    </resultmap>


    <operation name="getAgentCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询代理商月佣金列表">
        select
        agent_id account_id,
        agent_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
    </operation>

    <operation name="getAgentCommissionV2List" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询代理商月佣金列表">
        select
        agent_id account_id,
        agent_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = -1 and super_salesman_id = -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by agent_id
        order by create_time desc
    </operation>

    <operation name="getSuperSalesmanCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询超级授理商月佣金列表">
        select
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
    </operation>

    <operation name="getSuperSalesmanCommissionV2List" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询超级授理商月佣金列表">
        select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        super_salesman_username salesman_name,
        market_id market_id,
        super_salesman_id account_id,
        super_salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        business_date,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by super_salesman_id
        order by create_time desc
    </operation>

    <operation name="getSalesmanCommissionList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询授理商月佣金列表">
        select
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
    </operation>

    <operation name="getSalesmanCommissionV2List" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据代理商id查询授理商月佣金列表">
        select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        salesman_id account_id,
        salesman_username account_name,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id != -1
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by salesman_id
        order by create_time desc
    </operation>

    <operation name="getMarketCommissionBySalesmanList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据授理商ID获取佣金列表">
        select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        sum(market_fee) AS market_fee,
        sum(grant_fee) AS grant_fee,
        market_manager market_manager,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) - sum(market_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and salesman_id = #{salesmanId,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by market_id
        order by create_time desc
    </operation>

    <operation name="getMarketCommissionBySuperSalesmanList" multiplicity="many" resultmap="CommissionPayableDetailMap"
               remark="根据超级授理商ID获取佣金列表">
        select
        agent_id,
        super_salesman_id,
        salesman_id,
        market_id,
        business_date,
        sum(market_fee) AS market_fee,
        sum(grant_fee) AS grant_fee,
        market_manager market_manager,
        sum(normal_trade_count) normal_trade_count,
        sum(normal_trade_amount+refund_trade_amount) normal_trade_amount,
        sum(payable_commission) payable_commission,
        sum(deduction_commission) deduction_commission,
        sum(actual_payable_commission) actual_payable_commission,
        sum(exposure_num_hfive) exposure_num_hfive,
        sum(exposure_num_applet) exposure_num_applet,
        cpm_num,
        sum(exposure_num_hfive+exposure_num_applet) exposure_total_num,
        sum(market_fee) market_fee,
        sum(grant_fee) grant_fee,
        sum(actual_payable_commission) - sum(grant_fee) - sum(market_fee) as agent_fee
        from fp_merchant_commission_month
        where agent_id = #{agentId,jdbcType=INTEGER}
        and business_date = #{businessDate,jdbcType=INTEGER}
        and fee_code = #{feeCode,jdbcType=VARCHAR}
        and super_salesman_id = #{superSalesmanId,jdbcType=INTEGER}
        and source_type in
        <foreach collection="list" item="sourceType" open="(" separator="," close=")">
            #{sourceType,jdbcType=VARCHAR}
        </foreach>
        group by market_id
        order by create_time desc
    </operation>

</table>
