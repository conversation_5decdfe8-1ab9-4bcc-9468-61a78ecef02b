<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="FP_EQUIPMENT_COMMISSION" physicalName="FP_EQUIPMENT_COMMISSION"
       remark="设备维度佣金明细表">
    <!--    &lt;&gt;   <> -->

    <resultmap name="EquipmentCommissionMap" type="EquipmentCommissionMap">
        <column name="commissionId" jdbctype="INTEGER" javatype="Integer" remark="佣金ID"/>
        <column name="equipment_sn" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="agent_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="agent_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="business_date" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="super_salesman_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="super_salesman_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="salesman_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="salesman_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="uid" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="merchant_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="effective_user_count" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="fee_code" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="fee_project_name" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="rebate_amount" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
        <column name="valid_trade_number" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="valid_trade_amount" jdbctype="DECIMAL" javatype="java.math.BigDecimal" remark=""/>
        <column name="market_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="market_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="belong_agent_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="belong_agent_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="belong_super_salesman_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="belong_super_salesman_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="belong_salesman_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="belong_salesman_username" jdbctype="VARCHAR" javatype="String" remark=""/>
        <column name="belong_market_id" jdbctype="INTEGER" javatype="Integer" remark=""/>
        <column name="belong_market_username" jdbctype="VARCHAR" javatype="String" remark=""/>

    </resultmap>

    <!-- 条件查询 -->
    <operation name="listByCondition" multiplicity="many" resultmap="EquipmentCommissionMap" remark="条件查询">
        SELECT
        a.id as commissionId,
        a.equipment_sn,
        a.agent_id,
        a.agent_username,
        a.business_date,
        a.super_salesman_id,
        a.super_salesman_username,
        a.salesman_id,
        a.salesman_username,
        a.uid,
        a.merchant_username,
        a.effective_user_count,
        a.fee_code,
        b.fee_project_name,
        a.rebate_amount,
        a.valid_trade_number,
        a.valid_trade_amount,
        a.market_id,
        a.market_name,
        a.belong_agent_id,
        a.belong_agent_username,
        a.belong_super_salesman_id,
        a.belong_super_salesman_username,
        a.belong_salesman_id,
        a.belong_salesman_username,
        a.belong_market_id,
        a.belong_market_username
        FROM
        fp_equipment_commission a
        INNER JOIN fs_project_fee_code b ON a.fee_code = b.fee_code
        <where>
            <if test="agentId != null">
                AND a.agent_id = #{agentId,jdbcType=BIGINT}
            </if>
            <if test="businessExtNum != null">
                AND a.business_ext_num = #{businessExtNum,jdbcType=VARCHAR}
            </if>
            <if test="businessData != null">
                AND a.business_date = #{businessData,jdbcType=INTEGER}
            </if>
            <if test="feeCode != null">
                AND a.fee_code = #{feeCode,jdbcType=VARCHAR}
            </if>
        </where>
        ORDER BY a.business_date desc
    </operation>

</table>
