<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_TRADE_STORE_MONTH" physicalName="TP_TRADE_STORE_MONTH"
       remark="月活门店信息">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_TRADE_STORE_MONTH">
        INSERT INTO TP_TRADE_STORE_MONTH
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="tradeMonth != null">`TRADE_MONTH`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="tradeMonth != null">#{tradeMonth,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getTradeMonthTokenCount" resulttype="java.lang.Integer" remark="获取交易商户Token总数">
        SELECT COUNT(DISTINCT token) FROM TP_TRADE_STORE_MONTH
        WHERE trade_month = #{tradeMonth,jdbcType=INTEGER}
    </operation>

    <operation name="getTradeMonthTokenListByPage" multiplicity="many" resulttype="java.lang.String"
               remark="获取交易商户Token总数">
        SELECT DISTINCT token FROM TP_TRADE_STORE_MONTH
        WHERE trade_month = #{tradeMonth,jdbcType=INTEGER}
        ORDER BY token
        LIMIT #{startRow,jdbcType=INTEGER},#{limit,jdbcType=INTEGER}
    </operation>

</table>
