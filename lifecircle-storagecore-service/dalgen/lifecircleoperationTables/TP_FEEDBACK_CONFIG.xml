<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FEEDBACK_CONFIG" physicalName="TP_FEEDBACK_CONFIG"
       remark="意见反馈来源配置表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FEEDBACK_CONFIG">
        INSERT INTO TP_FEEDBACK_CONFIG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="name != null">`NAME`,</if>
            <if test="source != null">`SOURCE`,</if>
            <if test="qiyuUrl != null">`QIYU_URL`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="name != null">#{name,jdbcType=VARCHAR},</if>
            <if test="source != null">#{source,jdbcType=VARCHAR},</if>
            <if test="qiyuUrl != null">#{qiyuUrl,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getFeedBackConfigList" multiplicity="many" remark="获取意见反馈配置列表">
        SELECT * FROM TP_FEEDBACK_CONFIG
    </operation>

</table>
