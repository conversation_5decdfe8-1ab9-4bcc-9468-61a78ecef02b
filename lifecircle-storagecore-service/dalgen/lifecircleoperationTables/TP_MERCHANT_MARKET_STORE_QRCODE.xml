<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MERCHANT_MARKET_STORE_QRCODE" physicalName="TP_MERCHANT_MARKET_STORE_QRCODE"
       remark="会员活动门店海报二维码表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_MERCHANT_MARKET_STORE_QRCODE">
        INSERT INTO TP_MERCHANT_MARKET_STORE_QRCODE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="qrcodeUrl != null">`QRCODE_URL`,</if>
            <if test="templateQrcodeUrl != null">`TEMPLATE_QRCODE_URL`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="qrcodeUrl != null">#{qrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="templateQrcodeUrl != null">#{templateQrcodeUrl,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation remark="获取门店二维码" name="getStoreQRCode" multiplicity="one">
        SELECT *
        FROM tp_merchant_market_store_qrcode
        WHERE uid=#{uid,jdbcType=INTEGER} and token=#{token,jdbcType=VARCHAR} and store_id=#{storeId,jdbcType=INTEGER}
        LIMIT 1
    </operation>

    <operation name="updateQrcodeUrl" paramtype="object" remark="更新二维码">
        UPDATE tp_merchant_market_store_qrcode
        SET `QRCODE_URL` = #{qrcodeUrl,jdbcType=VARCHAR}
        WHERE `ID` = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="updateTemplateQrcodeUrl" paramtype="object" remark="更新二维码">
        UPDATE tp_merchant_market_store_qrcode
        SET `TEMPLATE_QRCODE_URL` = #{templateQrcodeUrl,jdbcType=VARCHAR}
        WHERE `ID` = #{id,jdbcType=BIGINT}
    </operation>

</table>
