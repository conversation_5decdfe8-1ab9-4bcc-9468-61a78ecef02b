<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FEEDBACK" physicalName="TP_FEEDBACK"
       remark="付呗APP问题反馈表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FEEDBACK">
        INSERT INTO TP_FEEDBACK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uuid != null">`UUID`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="images != null">`IMAGES`,</if>
            <if test="mobile != null">`MOBILE`,</if>
            <if test="content != null">`CONTENT`,</if>
            <if test="username != null">`USERNAME`,</if>
            <if test="appVersion != null">`APP_VERSION`,</if>
            <if test="feedbackId != null">`FEEDBACK_ID`,</if>
            <if test="phoneModel != null">`PHONE_MODEL`,</if>
            <if test="phoneVersion != null">`PHONE_VERSION`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="appType != null">`APP_TYPE`,</if>
            <if test="roleType != null">`ROLE_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uuid != null">#{uuid,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="images != null">#{images,jdbcType=VARCHAR},</if>
            <if test="mobile != null">#{mobile,jdbcType=CHAR},</if>
            <if test="content != null">#{content,jdbcType=VARCHAR},</if>
            <if test="username != null">#{username,jdbcType=VARCHAR},</if>
            <if test="appVersion != null">#{appVersion,jdbcType=VARCHAR},</if>
            <if test="feedbackId != null">#{feedbackId,jdbcType=VARCHAR},</if>
            <if test="phoneModel != null">#{phoneModel,jdbcType=VARCHAR},</if>
            <if test="phoneVersion != null">#{phoneVersion,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="appType != null">#{appType,jdbcType=TINYINT},</if>
            <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getFeedBackList" remark="获取问题反馈列表" multiplicity="many">
        SELECT * FROM TP_FEEDBACK
        <where>
            <trim prefix="AND" prefixOverrides="AND|OR">
                <if test="startDate != null and endDate != null">
                    AND create_time BETWEEN #{startDate,jdbcType=VARCHAR} AND #{endDate,jdbcType=VARCHAR}
                </if>
                <if test="username != null">
                    AND username LIKE CONCAT(#{username,jdbcType=VARCHAR},'%')
                </if>
                <if test="appType != null">
                    AND app_type = #{appType,jdbcType=TINYINT}
                </if>
            </trim>
        </where>
        ORDER BY create_time DESC
    </operation>

</table>
