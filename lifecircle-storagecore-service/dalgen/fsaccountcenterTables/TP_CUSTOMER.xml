<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CUSTOMER" physicalName="TP_CUSTOMER"
       remark="客户表(分账方)">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CUSTOMER">
        INSERT INTO TP_CUSTOMER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="area != null">`AREA`,</if>
            <if test="phone != null">`PHONE`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="county != null">`COUNTY`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="address != null">`ADDRESS`,</if>
            <if test="provice != null">`PROVICE`,</if>
            <if test="legalMail != null">`LEGAL_MAIL`,</if>
            <if test="licenseNo != null">`LICENSE_NO`,</if>
            <if test="regStatus != null">`REG_STATUS`,</if>
            <if test="shortName != null">`SHORT_NAME`,</if>
            <if test="cprRegNmCn != null">`CPR_REG_NM_CN`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="excelOssUrl != null">`EXCEL_OSS_URL`,</if>
            <if test="legalCertId != null">`LEGAL_CERT_ID`,</if>
            <if test="legalPerson != null">`LEGAL_PERSON`,</if>
            <if test="unionPayMcc != null">`UNION_PAY_MCC`,</if>
            <if test="customerName != null">`CUSTOMER_NAME`,</if>
            <if test="customerType != null">`CUSTOMER_TYPE`,</if>
            <if test="importReason != null">`IMPORT_REASON`,</if>
            <if test="importStatus != null">`IMPORT_STATUS`,</if>
            <if test="licensePhoto != null">`LICENSE_PHOTO`,</if>
            <if test="shopFrontPic != null">`SHOP_FRONT_PIC`,</if>
            <if test="legalCertType != null">`LEGAL_CERT_TYPE`,</if>
            <if test="shopInsidePic != null">`SHOP_INSIDE_PIC`,</if>
            <if test="licenseExpires != null">`LICENSE_EXPIRES`,</if>
            <if test="merchantOrderSn != null">`MERCHANT_ORDER_SN`,</if>
            <if test="licenseExpiresEnd != null">`LICENSE_EXPIRES_END`,</if>
            <if test="shopCheckstandPic != null">`SHOP_CHECKSTAND_PIC`,</if>
            <if test="legalBackPersonPic != null">`LEGAL_BACK_PERSON_PIC`,</if>
            <if test="legalFrontPersonPic != null">`LEGAL_FRONT_PERSON_PIC`,</if>
            <if test="licenseExpiresBegain != null">`LICENSE_EXPIRES_BEGAIN`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="agentId != null">`AGENT_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="unityCatId != null">`UNITY_CAT_ID`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="pettyCash != null">`PETTY_CASH`,</if>
            <if test="totalBalance != null">`TOTAL_BALANCE`,</if>
            <if test="uid != null">`uid`,</if>
            <if test="legalPersonLicStt != null">`legal_person_lic_stt`,</if>
            <if test="legalPersonLicEnt != null">`legal_person_lic_ent`,</if>
            <if test="legalPersonLicEffect != null">`legal_person_lic_effect`,</if>
            <if test="residenceAddress != null">`residence_address`,</if>
            <if test="settlementPersonLicStt != null">`settlement_person_lic_stt`,</if>
            <if test="settlementPersonLicEnt != null">`settlement_person_lic_ent`,</if>
            <if test="settlementPersonLicEffect != null">`settlement_person_lic_effect`,</if>
            <if test="authorizationPic != null">`authorization_pic`,</if>
            <if test="settlementBackPersonPic != null">`SETTLEMENT_BACK_PERSON_PIC`,</if>
            <if test="settlementFrontPersonPic != null">`SETTLEMENT_FRONT_PERSON_PIC`,</if>
            <if test="settlementCertId != null">`SETTLEMENT_CERT_ID`,</if>
            <if test="settlementPerson != null">`SETTLEMENT_PERSON`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="area != null">#{area,jdbcType=VARCHAR},</if>
            <if test="phone != null">#{phone,jdbcType=VARCHAR},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="county != null">#{county,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="address != null">#{address,jdbcType=VARCHAR},</if>
            <if test="provice != null">#{provice,jdbcType=VARCHAR},</if>
            <if test="legalMail != null">#{legalMail,jdbcType=VARCHAR},</if>
            <if test="licenseNo != null">#{licenseNo,jdbcType=VARCHAR},</if>
            <if test="regStatus != null">#{regStatus,jdbcType=VARCHAR},</if>
            <if test="shortName != null">#{shortName,jdbcType=VARCHAR},</if>
            <if test="cprRegNmCn != null">#{cprRegNmCn,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="excelOssUrl != null">#{excelOssUrl,jdbcType=VARCHAR},</if>
            <if test="legalCertId != null">#{legalCertId,jdbcType=VARCHAR},</if>
            <if test="legalPerson != null">#{legalPerson,jdbcType=VARCHAR},</if>
            <if test="unionPayMcc != null">#{unionPayMcc,jdbcType=VARCHAR},</if>
            <if test="customerName != null">#{customerName,jdbcType=VARCHAR},</if>
            <if test="customerType != null">#{customerType,jdbcType=VARCHAR},</if>
            <if test="importReason != null">#{importReason,jdbcType=VARCHAR},</if>
            <if test="importStatus != null">#{importStatus,jdbcType=VARCHAR},</if>
            <if test="licensePhoto != null">#{licensePhoto,jdbcType=VARCHAR},</if>
            <if test="shopFrontPic != null">#{shopFrontPic,jdbcType=VARCHAR},</if>
            <if test="legalCertType != null">#{legalCertType,jdbcType=VARCHAR},</if>
            <if test="shopInsidePic != null">#{shopInsidePic,jdbcType=VARCHAR},</if>
            <if test="licenseExpires != null">#{licenseExpires,jdbcType=VARCHAR},</if>
            <if test="merchantOrderSn != null">#{merchantOrderSn,jdbcType=VARCHAR},</if>
            <if test="licenseExpiresEnd != null">#{licenseExpiresEnd,jdbcType=VARCHAR},</if>
            <if test="shopCheckstandPic != null">#{shopCheckstandPic,jdbcType=VARCHAR},</if>
            <if test="legalBackPersonPic != null">#{legalBackPersonPic,jdbcType=VARCHAR},</if>
            <if test="legalFrontPersonPic != null">#{legalFrontPersonPic,jdbcType=VARCHAR},</if>
            <if test="licenseExpiresBegain != null">#{licenseExpiresBegain,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="agentId != null">#{agentId,jdbcType=INTEGER},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="unityCatId != null">#{unityCatId,jdbcType=INTEGER},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="pettyCash != null">#{pettyCash,jdbcType=DECIMAL},</if>
            <if test="totalBalance != null">#{totalBalance,jdbcType=DECIMAL},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="legalPersonLicStt != null">#{legalPersonLicStt,jdbcType=VARCHAR},</if>
            <if test="legalPersonLicEnt != null">#{legalPersonLicEnt,jdbcType=VARCHAR},</if>
            <if test="legalPersonLicEffect != null">#{legalPersonLicEffect,jdbcType=VARCHAR},</if>
            <if test="residenceAddress != null">#{residenceAddress,jdbcType=VARCHAR},</if>
            <if test="settlementPersonLicStt != null">#{settlementPersonLicStt,jdbcType=VARCHAR},</if>
            <if test="settlementPersonLicEnt != null">#{settlementPersonLicEnt,jdbcType=VARCHAR},</if>
            <if test="settlementPersonLicEffect != null">#{settlementPersonLicEffect,jdbcType=VARCHAR},</if>
            <if test="authorizationPic != null">#{authorizationPic,jdbcType=VARCHAR},</if>
            <if test="settlementBackPersonPic != null">#{settlementBackPersonPic,jdbcType=VARCHAR},</if>
            <if test="settlementFrontPersonPic != null">#{settlementFrontPersonPic,jdbcType=VARCHAR},</if>
            <if test="settlementCertId != null">#{settlementCertId,jdbcType=VARCHAR},</if>
            <if test="settlementPerson != null">#{settlementPerson,jdbcType=VARCHAR},</if>
        </trim>
    </operation>

    <operation name="getCustomerByCustomerId" multiplicity="many" remark="根据customerId批量查询customerName">
        select
        *
        from
        tp_customer
        where
        active = 'YES'
        and
        customer_id in
        <foreach close=")" collection="list" index="index" item="customerId" open="(" separator=",">
            #{customerId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getByCustomerId" multiplicity="one" remark="根据customerId查询生效且成功的客户">
        SELECT *
        FROM
        tp_customer
        WHERE customer_id = #{customerId,jdbcType=VARCHAR}
        AND reg_status = 'SUCCESS'
        AND active = 'YES'
        limit 1
    </operation>

    <operation name="updateImportReasonByCustomerId" remark="根据客户id更新为三要素不唯一">
        UPDATE
        TP_CUSTOMER
        SET
        import_reason = "三要素不唯一",
        import_status = "FAIL"
        WHERE
        CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
    </operation>
    <operation name="updateTotalAmountByCustomerId" paramtype="object" remark="根据customerId更新总金额">
        update
        tp_customer
        set
        total_balance = #{totalBalance,jdbcType=DECIMAL}
        where
        customer_id = #{customerId,jdbcType=VARCHAR}
        and
        active = 'YES'
    </operation>
    <operation name="getByMerchantorderSn" multiplicity="one" remark="根据请求流水号查询子商户信息">
        SELECT *
        FROM
        tp_customer
        WHERE
        merchant_order_sn = #{merchantOrderSn,jdbcType=VARCHAR}
        LIMIT 1
    </operation>

    <operation name="getByCustomerIdWithoutActive" multiplicity="one" remark="根据customerId查询客户">
        SELECT *
        FROM
        tp_customer
        WHERE customer_id = #{customerId,jdbcType=VARCHAR}
        limit 1
    </operation>

</table>
