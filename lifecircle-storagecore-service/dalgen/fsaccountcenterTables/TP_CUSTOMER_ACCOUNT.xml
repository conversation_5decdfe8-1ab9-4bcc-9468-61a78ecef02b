<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CUSTOMER_ACCOUNT" physicalName="TP_CUSTOMER_ACCOUNT"
       remark="客户表账户表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CUSTOMER_ACCOUNT">
        INSERT INTO TP_CUSTOMER_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="outAccountId != null">`OUT_ACCOUNT_ID`,</if>
            <if test="outCustomerId != null">`OUT_CUSTOMER_ID`,</if>
            <if test="outMerchantId != null">`OUT_MERCHANT_ID`,</if>
            <if test="balanceAccountId != null">`BALANCE_ACCOUNT_ID`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="unionPayMerchantId != null">`UNION_PAY_MERCHANT_ID`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="withdrawableBalance != null">`WITHDRAWABLE_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="outAccountId != null">#{outAccountId,jdbcType=VARCHAR},</if>
            <if test="outCustomerId != null">#{outCustomerId,jdbcType=VARCHAR},</if>
            <if test="outMerchantId != null">#{outMerchantId,jdbcType=VARCHAR},</if>
            <if test="balanceAccountId != null">#{balanceAccountId,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="unionPayMerchantId != null">#{unionPayMerchantId,jdbcType=VARCHAR},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="withdrawableBalance != null">#{withdrawableBalance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getAccountByCustomerId" multiplicity="many" remark="根据customerId得到账户信息">
        select
        *
        from
        tp_customer_account
        where
        active = 'YES'
        and
        customer_id in
        <foreach close=")" collection="list" index="index" item="customerId" open="(" separator=",">
            #{customerId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="updateWithdrawableBalanceByCustomerId" paramtype="object" remark="根据customerId更新可提现金额">
        update
        tp_customer_account
        set
        withdrawable_balance = #{withdrawableBalance,jdbcType=DECIMAL}
        where
        customer_id = #{customerId,jdbcType=VARCHAR}
        and
        active = 'YES'

    </operation>

    <operation name="getActiveAccount" multiplicity="one" remark="根据客户查询生效的账户信息">
        SELECT
        *
        FROM
        TP_CUSTOMER_ACCOUNT
        WHERE
        CUSTOMER_ID = #{customerId,jdbcType=VARCHAR}
        AND ACTIVE = 'YES'
        AND CHANNEL_TYPE=#{channelType,jdbcType=TINYINT}
        AND BANK_TYPE = #{bankType,jdbcType=TINYINT}
        LIMIT 1
    </operation>
</table>
