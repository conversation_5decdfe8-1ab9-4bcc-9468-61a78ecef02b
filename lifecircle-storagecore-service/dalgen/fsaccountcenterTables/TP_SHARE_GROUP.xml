<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_GROUP" physicalName="TP_SHARE_GROUP"
       remark="分账组">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_GROUP">
        INSERT INTO TP_SHARE_GROUP
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="channelType != null">`CHANNEL_TYPE`,</if>
            <if test="bankType != null">`BANK_TYPE`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="settleType != null">`SETTLE_TYPE`,</if>
            <if test="settleCycleStart != null">`SETTLE_CYCLE_START`,</if>
            <if test="settleCycleEnd != null">`SETTLE_CYCLE_END`,</if>
            <if test="shareMode != null">`SHARE_MODE`,</if>
            <if test="shareType != null">`SHARE_TYPE`,</if>
            <if test="reqSerialNumber != null">`REQ_SERIAL_NUMBER`,</if>
            <if test="shareGroupName != null">`SHARE_GROUP_NAME`,</if>
            <if test="shareGroupId != null">`SHARE_GROUP_ID`,</if>
            <if test="shareMemberNum != null">`SHARE_MEMBER_NUM`,</if>
            <if test="passNum != null">`PASS_NUM`,</if>
            <if test="groupStatus != null">`GROUP_STATUS`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="sourceType != null">`SOURCE_TYPE`,</if>
            <if test="importStatus != null">`IMPORT_STATUS`,</if>
            <if test="importReason != null">`IMPORT_REASON`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="channelType != null">#{channelType,jdbcType=TINYINT},</if>
            <if test="bankType != null">#{bankType,jdbcType=TINYINT},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="settleType != null">#{settleType,jdbcType=VARCHAR},</if>
            <if test="settleCycleStart != null">#{settleCycleStart,jdbcType=VARCHAR},</if>
            <if test="settleCycleEnd != null">#{settleCycleEnd,jdbcType=VARCHAR},</if>
            <if test="shareMode != null">#{shareMode,jdbcType=VARCHAR},</if>
            <if test="shareType != null">#{shareType,jdbcType=VARCHAR},</if>
            <if test="reqSerialNumber != null">#{reqSerialNumber,jdbcType=VARCHAR},</if>
            <if test="shareGroupName != null">#{shareGroupName,jdbcType=VARCHAR},</if>
            <if test="shareGroupId != null">#{shareGroupId,jdbcType=VARCHAR},</if>
            <if test="shareMemberNum != null">#{shareMemberNum,jdbcType=INTEGER},</if>
            <if test="passNum != null">#{passNum,jdbcType=INTEGER},</if>
            <if test="groupStatus != null">#{groupStatus,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="sourceType != null">#{sourceType,jdbcType=VARCHAR},</if>
            <if test="importStatus != null">#{importStatus,jdbcType=VARCHAR},</if>
            <if test="importReason != null">#{importReason,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByUid" multiplicity="one" remark="根据Uid查询生效的分账组">
        SELECT *
        FROM
        tp_share_group
        WHERE uid = #{uid,jdbcType=INTEGER}
        AND bank_type = #{bankType,jdbcType=TINYINT}
        AND active = 'YES'
        limit 1
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO tp_share_group
        (
        group_id,channel_type,bank_type,token,uid,settle_type,req_serial_number,
        share_group_name,share_member_num,pass_num,group_status,active,import_status,
        import_reason,settle_cycle_start,settle_cycle_end,share_mode,share_type,protocol_pic,
        merchant_order_sn,source_type
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.groupId,jdbcType=VARCHAR},
            #{item.channelType,jdbcType=TINYINT},
            #{item.bankType,jdbcType=TINYINT},
            #{item.token,jdbcType=VARCHAR},
            #{item.uid,jdbcType=INTEGER},
            #{item.settleType,jdbcType=VARCHAR},
            #{item.reqSerialNumber,jdbcType=VARCHAR},
            #{item.shareGroupName,jdbcType=VARCHAR},
            #{item.shareMemberNum,jdbcType=INTEGER},
            #{item.passNum,jdbcType=INTEGER},
            #{item.groupStatus,jdbcType=VARCHAR},
            #{item.active,jdbcType=VARCHAR},
            #{item.importStatus,jdbcType=VARCHAR},
            #{item.importReason,jdbcType=VARCHAR},
            #{item.settleCycleStart,jdbcType=VARCHAR},
            #{item.settleCycleEnd,jdbcType=VARCHAR},
            #{item.shareMode,jdbcType=VARCHAR},
            #{item.shareType,jdbcType=VARCHAR},
            #{item.protocolPic,jdbcType=VARCHAR},
            #{item.merchantOrderSn,jdbcType=VARCHAR},
            #{item.sourceType,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="updateNumByGroupId" paramtype="primitive" remark="更新成员数量">
        UPDATE tp_share_group
        SET
        share_member_num = share_member_num + #{shareMemberNum,jdbcType=INTEGER},
        pass_num = pass_num + #{passNum,jdbcType=INTEGER}
        WHERE group_id = #{groupId,jdbcType=VARCHAR}
    </operation>

    <operation name="getShareGroupIdByTokenAndBankTypeAndChannelType" paramtype="primitive" resulttype="String"
               remark="通过 token、bank_type、channle_type 获得分账组 ID">
        SELECT
        GROUP_ID
        FROM
        TP_SHARE_GROUP
        WHERE
        token = #{token,jdbcType=VARCHAR}
        AND
        bank_type = #{bankType,jdbcType=TINYINT}
        AND
        channel_type = #{channelType,jdbcType=TINYINT}
        AND
        active = 'YES'
        AND
        group_status = 'SUCCESS'
        LIMIT 1

    </operation>

    <operation name="getShareGroupByGroupId" multiplicity="one" remark="根据groupId查询分账组">
        select
        *
        from
        tp_share_group
        where
        group_id = #{groupId,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="getShareGroupByMerchantOrderSn" multiplicity="one" remark="根据merchantOrderSn查询分账组">
        select
        *
        from
        tp_share_group
        where
        merchant_order_sn = #{merchantOrderSn,jdbcType=VARCHAR}
        limit 1
    </operation>

</table>
