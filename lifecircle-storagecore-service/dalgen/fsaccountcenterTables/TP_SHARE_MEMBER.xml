<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_MEMBER" physicalName="TP_SHARE_MEMBER"
       remark="分账成员表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_MEMBER">
        INSERT INTO TP_SHARE_MEMBER
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="groupId != null">`GROUP_ID`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="protocolPic != null">`PROTOCOL_PIC`,</if>
            <if test="signStartDate != null">`SIGN_START_DATE`,</if>
            <if test="signEndDate != null">`SIGN_END_DATE`,</if>
            <if test="auditStatus != null">`AUDIT_STATUS`,</if>
            <if test="rejectReason != null">`REJECT_REASON`,</if>
            <if test="importStatus != null">`IMPORT_STATUS`,</if>
            <if test="importReason != null">`IMPORT_REASON`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="groupId != null">#{groupId,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="protocolPic != null">#{protocolPic,jdbcType=VARCHAR},</if>
            <if test="signStartDate != null">#{signStartDate,jdbcType=VARCHAR},</if>
            <if test="signEndDate != null">#{signEndDate,jdbcType=VARCHAR},</if>
            <if test="auditStatus != null">#{auditStatus,jdbcType=VARCHAR},</if>
            <if test="rejectReason != null">#{rejectReason,jdbcType=VARCHAR},</if>
            <if test="importStatus != null">#{importStatus,jdbcType=VARCHAR},</if>
            <if test="importReason != null">#{importReason,jdbcType=VARCHAR},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByCustomerIdAndGroupId" multiplicity="one" remark="根据customerId和groupId查询生效的分账成员">
        SELECT *
        FROM
        tp_share_member
        WHERE customer_id = #{customerId,jdbcType=VARCHAR}
        AND group_id = #{groupId,jdbcType=VARCHAR}
        AND (active = 'YES' OR active = 'STOP')
        limit 1
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO tp_share_member
        (
        share_member_id,group_id,customer_id,sign_start_date,sign_end_date,
        audit_status,import_status,import_reason,active,protocol_pic
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shareMemberId,jdbcType=VARCHAR},
            #{item.groupId,jdbcType=VARCHAR},
            #{item.customerId,jdbcType=VARCHAR},
            #{item.signStartDate,jdbcType=VARCHAR},
            #{item.signEndDate,jdbcType=VARCHAR},
            #{item.auditStatus,jdbcType=VARCHAR},
            #{item.importStatus,jdbcType=VARCHAR},
            #{item.importReason,jdbcType=VARCHAR},
            #{item.active,jdbcType=VARCHAR},
            #{item.protocolPic,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="countBySuccess" paramtype="primitive" resulttype="int" remark="根据分账组 ID 获得状态正常的分组成员个数">
        SELECT
        COUNT(*)
        FROM
        tp_share_member
        WHERE
        audit_status = 'PASS'
        AND
        active = 'YES'
        AND
        GROUP_ID = #{groupId,jdbcType=VARCHAR}
    </operation>

    <operation name="getShareMemberByShareMemberIdList" multiplicity="many" remark="根据groupId查询分账成员">
        select
        *
        from
        tp_share_member
        where
        share_member_id in
        <foreach close=")" collection="list" index="index" item="shareMemberId" open="(" separator=",">
            #{shareMemberId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="getShareMemberByGroupId" multiplicity="many" remark="根据groupId查询分账成员">
        select
        *
        from
        tp_share_member
        where
        group_id = #{groupId,jdbcType=VARCHAR}
    </operation>

</table>
