<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_MCH_SHARE_ACCOUNT" physicalName="TP_MCH_SHARE_ACCOUNT"
       remark="佣金商户分账账户信息表">

    <operation name="queryListByParam" multiplicity="many" paramClass="com.fshows.lifecircle.storagecore.service.dal.fsaccountcenter.dataobject.MchShareAccountListDO" remark="根据查询条件查询商户信息" resulttype="com.fshows.lifecircle.storagecore.service.domain.model.mchshare.MchShareAccountListItemModel">
        SELECT
        sa.uid as uid,
        sa.agent_id as agentId,
        sa.agent_name as agentName,
        sa.mchnt_name as userName,
        sa.mchnt_cd as mchntCd,
        sa.company as company,
        cr.sign_status as signStatus,
        cr.update_time as signUpdateTimeDate,
        ai.income_status as incomeStatus,
        ai.update_time as incomeUpdateTimeDate,
        sa.mobile as mobile,
        sa.account_no as accountNo,
        sa.salesman_id as salesmanId,
        sa.salesman_name as salesmanName,
        cr.sign_rate as decimalRate,
        sa.create_time as createTimeDate,
        sa.update_time as updateTimeDate,
        sa.account_active_time as accountActiveTime
        FROM
        `tp_mch_share_account` sa
        LEFT JOIN `tp_mch_share_concentrate_relation` cr on sa.uid = cr.uid
        LEFT JOIN `tp_mch_users_account_info` ai on sa.uid = ai.uid

        WHERE sa.is_del = 0 and sa.role = #{role,jdbcType=TINYINT}
        <if test="agentId != null ">
            AND sa.agent_id  =  #{agentId,jdbcType=INTEGER}
        </if>
        <if test="salesmanId != null ">
            AND sa.salesman_id  =  #{salesmanId,jdbcType=INTEGER}
        </if>
        <if test="uid != null ">
            AND sa.uid =  #{uid,jdbcType=INTEGER}
        </if>
        <if test="mchntCd != null ">
            AND sa.mchnt_cd =  #{mchntCd,jdbcType=INTEGER}
        </if>
        <if test="mchntName != null ">
            AND sa.mchnt_name  LIKE concat('%',#{mchntName, jdbcType=VARCHAR},'%')
        </if>
        <if test="company != null ">
            AND sa.company  LIKE concat('%',#{company, jdbcType=VARCHAR},'%')
        </if>
        <if test="agentName != null ">
            AND sa.agent_name  LIKE concat('%',#{agentName, jdbcType=VARCHAR},'%')
        </if>

        <if test="signStatus != null and signStatus != -1">
            <choose>
                <when test="signStatus == 1">
                    AND cr.sign_status is null
                </when>
                <otherwise>
                    AND cr.sign_status = #{signStatus,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>
        <if test="incomeStatus != null and incomeStatus != -1">
            <choose>
                <when test="incomeStatus == 5">
                    AND (ai.income_status is null or ai.income_status = #{incomeStatus,jdbcType=INTEGER})
                </when>
                <otherwise>
                    AND ai.income_status = #{incomeStatus,jdbcType=INTEGER}
                </otherwise>
            </choose>
        </if>

        <if test="beginTime != null and endTime !=null">
            AND sa.create_time between #{beginTime,jdbcType=TIMESTAMP} and #{endTime,jdbcType=TIMESTAMP}
        </if>
        ORDER BY sa.create_time desc
    </operation>


</table>
