<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FBANK_YESTERDAY_BALANCE" physicalName="TP_FBANK_YESTERDAY_BALANCE"
       remark="富民昨日余额表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FBANK_YESTERDAY_BALANCE">
        INSERT INTO TP_FBANK_YESTERDAY_BALANCE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="accountNo != null">`ACCOUNT_NO`,</if>
            <if test="tradeDay != null">`TRADE_DAY`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="balance != null">`BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="accountNo != null">#{accountNo,jdbcType=VARCHAR},</if>
            <if test="tradeDay != null">#{tradeDay,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="balance != null">#{balance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO TP_FBANK_YESTERDAY_BALANCE(
        ACCOUNT_NO,
        TRADE_DAY,
        BALANCE
        )VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.accountNo,jdbcType=VARCHAR},
            #{item.tradeDay,jdbcType=INTEGER},
            #{item.balance,jdbcType=DECIMAL}
            )
        </foreach>
    </operation>

</table>
