<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_RULE" physicalName="TP_SHARE_RULE"
       remark="分账规则表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_RULE">
        INSERT INTO TP_SHARE_RULE
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="storeId != null">`STORE_ID`,</if>
            <if test="sharePortion != null">`SHARE_PORTION`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="storeId != null">#{storeId,jdbcType=INTEGER},</if>
            <if test="sharePortion != null">#{sharePortion,jdbcType=DECIMAL},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="insertBatch" paramtype="objectList" remark="批量插入">
        INSERT INTO tp_share_rule
        (
        share_member_id,store_id,share_portion
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.shareMemberId,jdbcType=VARCHAR},
            #{item.storeId,jdbcType=INTEGER},
            #{item.sharePortion,jdbcType=DECIMAL}
            )
        </foreach>
    </operation>

    <operation name="sumByStoreId" resulttype="java.math.BigDecimal" remark="根据storeId查询生效的分账比例总和">
        SELECT ifnull(sum(share_portion), 0)
        FROM
        tp_share_rule AS sr
        LEFT JOIN
        tp_share_member AS sm ON sr.share_member_id=sm.share_member_id
        WHERE sr.store_id = #{storeId,jdbcType=INTEGER}
        AND (sm.active = 'YES' OR sm.active = 'STOP')
        AND sm.group_id = #{groupId,jdbcType=VARCHAR}
        and sr.is_del = 0
    </operation>

    <operation name="getShareRule" multiplicity="one" remark="查询分账规则">
        SELECT
        *
        FROM
        tp_share_rule
        WHERE store_id = #{storeId,jdbcType=INTEGER}
        and share_member_id = #{shareMemberId,jdbcType=VARCHAR}
        and is_del = 0
    </operation>

    <operation name="deleteRule" paramtype="primitive" remark="删除分账规则数量">
        UPDATE tp_share_rule
        SET
        is_del = 1
        WHERE
        id in
        <foreach collection="list" item="id" open="(" close=")" separator=",">
            #{id,jdbcType=INTEGER}
        </foreach>
    </operation>
</table>
