<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CUSTOMER_BALANCE_CHANGE_LOG" physicalName="TP_CUSTOMER_BALANCE_CHANGE_LOG"
       remark="客户余额变更记录">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CUSTOMER_BALANCE_CHANGE_LOG">
        INSERT INTO TP_CUSTOMER_BALANCE_CHANGE_LOG
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderSn != null">`ORDER_SN`,</if>
            <if test="changeType != null">`CHANGE_TYPE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="changeRemark != null">`CHANGE_REMARK`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="afterPettyCash != null">`AFTER_PETTY_CASH`,</if>
            <if test="frontPettyCash != null">`FRONT_PETTY_CASH`,</if>
            <if test="changePettyCash != null">`CHANGE_PETTY_CASH`,</if>
            <if test="afterTotalBalance != null">`AFTER_TOTAL_BALANCE`,</if>
            <if test="frontTotalBalance != null">`FRONT_TOTAL_BALANCE`,</if>
            <if test="changeTotalBalance != null">`CHANGE_TOTAL_BALANCE`,</if>
            <if test="afterWithdrawableBalance != null">`AFTER_WITHDRAWABLE_BALANCE`,</if>
            <if test="frontWithdrawableBalance != null">`FRONT_WITHDRAWABLE_BALANCE`,</if>
            <if test="changeWithdrawableBalance != null">`CHANGE_WITHDRAWABLE_BALANCE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="orderSn != null">#{orderSn,jdbcType=VARCHAR},</if>
            <if test="changeType != null">#{changeType,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="changeRemark != null">#{changeRemark,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="afterPettyCash != null">#{afterPettyCash,jdbcType=DECIMAL},</if>
            <if test="frontPettyCash != null">#{frontPettyCash,jdbcType=DECIMAL},</if>
            <if test="changePettyCash != null">#{changePettyCash,jdbcType=DECIMAL},</if>
            <if test="afterTotalBalance != null">#{afterTotalBalance,jdbcType=DECIMAL},</if>
            <if test="frontTotalBalance != null">#{frontTotalBalance,jdbcType=DECIMAL},</if>
            <if test="changeTotalBalance != null">#{changeTotalBalance,jdbcType=DECIMAL},</if>
            <if test="afterWithdrawableBalance != null">#{afterWithdrawableBalance,jdbcType=DECIMAL},</if>
            <if test="frontWithdrawableBalance != null">#{frontWithdrawableBalance,jdbcType=DECIMAL},</if>
            <if test="changeWithdrawableBalance != null">#{changeWithdrawableBalance,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getListByOrderSnAndChangeType" multiplicity="many" remark="根据分账订单号和分账类型类型查询变更记录">
        select
        *
        from
        tp_customer_balance_change_log
        where
        change_type = 'SHARE'
        and
        order_sn in
        <foreach close=")" collection="list" index="index" item="orderSn" open="(" separator=",">
            #{orderSn,jdbcType=VARCHAR}
        </foreach>
    </operation>

</table>
