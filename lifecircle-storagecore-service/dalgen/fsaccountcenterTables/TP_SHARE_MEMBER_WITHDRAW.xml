<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_MEMBER_WITHDRAW" physicalName="TP_SHARE_MEMBER_WITHDRAW"
       remark="客户提现记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_MEMBER_WITHDRAW">
        INSERT INTO TP_SHARE_MEMBER_WITHDRAW
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="cashType != null">`CASH_TYPE`,</if>
            <if test="cashStatus != null">`CASH_STATUS`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
            <if test="withdrawStatus != null">`WITHDRAW_STATUS`,</if>
            <if test="withdrawalsMode != null">`WITHDRAWALS_MODE`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="tranTime != null">`TRAN_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="cashType != null">#{cashType,jdbcType=VARCHAR},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="withdrawStatus != null">#{withdrawStatus,jdbcType=VARCHAR},</if>
            <if test="withdrawalsMode != null">#{withdrawalsMode,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="tranTime != null">#{tranTime,jdbcType=INTEGER},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>


    <operation name="getDiffCustomerWithdrawList" multiplicity="many" remark="批量查询状态不为成功的提现数据">
        select
        *
        from
        tp_share_member_withdraw
        where
        serial_number in
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
        and
        cash_status = 'PROCESSING'
    </operation>
    <operation name="updateWithdrawBySerialNumber" paramtype="object" remark="更新客户的提现状态">
        update
        tp_share_member_withdraw
        set
        cash_status = #{cashStatus,jdbcType=VARCHAR},
        withdraw_status = #{withdrawStatus,jdbcType=VARCHAR}
        where
        serial_number = #{serialNumber,jdbcType=VARCHAR}
    </operation>
</table>
