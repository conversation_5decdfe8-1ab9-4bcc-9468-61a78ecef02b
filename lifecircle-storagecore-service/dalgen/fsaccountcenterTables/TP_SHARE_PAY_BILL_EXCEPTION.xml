<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_PAY_BILL_EXCEPTION" physicalName="TP_SHARE_PAY_BILL_EXCEPTION"
       remark="分账账单异常">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_PAY_BILL_EXCEPTION">
        INSERT INTO TP_SHARE_PAY_BILL_EXCEPTION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="mno != null">`MNO`,</if>
            <if test="token != null">`TOKEN`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="orderId != null">`ORDER_ID`,</if>
            <if test="remarks != null">`REMARKS`,</if>
            <if test="billDate != null">`BILL_DATE`,</if>
            <if test="shareRole != null">`SHARE_ROLE`,</if>
            <if test="shareType != null">`SHARE_TYPE`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="settleType != null">`SETTLE_TYPE`,</if>
            <if test="shareBillNo != null">`SHARE_BILL_NO`,</if>
            <if test="shareStatus != null">`SHARE_STATUS`,</if>
            <if test="targetInMno != null">`TARGET_IN_MNO`,</if>
            <if test="channelBillNo != null">`CHANNEL_BILL_NO`,</if>
            <if test="shareMemberId != null">`SHARE_MEMBER_ID`,</if>
            <if test="sharePaySubNo != null">`SHARE_PAY_SUB_NO`,</if>
            <if test="bankPayPurpose != null">`BANK_PAY_PURPOSE`,</if>
            <if test="withdrawStatus != null">`WITHDRAW_STATUS`,</if>
            <if test="reqSerialNumber != null">`REQ_SERIAL_NUMBER`,</if>
            <if test="withdrawSerialNumber != null">`WITHDRAW_SERIAL_NUMBER`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="settleEndTime != null">`SETTLE_END_TIME`,</if>
            <if test="settleStartTime != null">`SETTLE_START_TIME`,</if>
            <if test="setAmt != null">`SET_AMT`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="setFeeAmt != null">`SET_FEE_AMT`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="sharePrice != null">`SHARE_PRICE`,</if>
            <if test="realSharePrice != null">`REAL_SHARE_PRICE`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="mno != null">#{mno,jdbcType=VARCHAR},</if>
            <if test="token != null">#{token,jdbcType=VARCHAR},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="orderId != null">#{orderId,jdbcType=VARCHAR},</if>
            <if test="remarks != null">#{remarks,jdbcType=VARCHAR},</if>
            <if test="billDate != null">#{billDate,jdbcType=VARCHAR},</if>
            <if test="shareRole != null">#{shareRole,jdbcType=VARCHAR},</if>
            <if test="shareType != null">#{shareType,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="settleType != null">#{settleType,jdbcType=VARCHAR},</if>
            <if test="shareBillNo != null">#{shareBillNo,jdbcType=VARCHAR},</if>
            <if test="shareStatus != null">#{shareStatus,jdbcType=VARCHAR},</if>
            <if test="targetInMno != null">#{targetInMno,jdbcType=VARCHAR},</if>
            <if test="channelBillNo != null">#{channelBillNo,jdbcType=VARCHAR},</if>
            <if test="shareMemberId != null">#{shareMemberId,jdbcType=VARCHAR},</if>
            <if test="sharePaySubNo != null">#{sharePaySubNo,jdbcType=VARCHAR},</if>
            <if test="bankPayPurpose != null">#{bankPayPurpose,jdbcType=VARCHAR},</if>
            <if test="withdrawStatus != null">#{withdrawStatus,jdbcType=VARCHAR},</if>
            <if test="reqSerialNumber != null">#{reqSerialNumber,jdbcType=VARCHAR},</if>
            <if test="withdrawSerialNumber != null">#{withdrawSerialNumber,jdbcType=VARCHAR},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="settleEndTime != null">#{settleEndTime,jdbcType=TIMESTAMP},</if>
            <if test="settleStartTime != null">#{settleStartTime,jdbcType=TIMESTAMP},</if>
            <if test="setAmt != null">#{setAmt,jdbcType=DECIMAL},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="setFeeAmt != null">#{setFeeAmt,jdbcType=DECIMAL},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=DECIMAL},</if>
            <if test="sharePrice != null">#{sharePrice,jdbcType=DECIMAL},</if>
            <if test="realSharePrice != null">#{realSharePrice,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getExceptionByShareBillNo" multiplicity="many" remark="根据分账订单号批量查询异常订单">
        select
        *
        from
        tp_share_pay_bill_exception
        where
        share_bill_no in
        <foreach close=")" collection="list" index="index" item="shareBillNo" open="(" separator=",">
            #{shareBillNo,jdbcType=VARCHAR}
        </foreach>
    </operation>
</table>
