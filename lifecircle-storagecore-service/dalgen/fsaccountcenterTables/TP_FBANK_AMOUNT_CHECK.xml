<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_FBANK_AMOUNT_CHECK" physicalName="TP_FBANK_AMOUNT_CHECK"
       remark="富民金额核对表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_FBANK_AMOUNT_CHECK">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
            SELECT
            LAST_INSERT_ID()
        </selectKey>
        INSERT INTO TP_FBANK_AMOUNT_CHECK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="uid != null">`UID`,</if>
            <if test="checkDay != null">`CHECK_DAY`,</if>
            <if test="checkType != null">`CHECK_TYPE`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="fbankBalance != null">`FBANK_BALANCE`,</if>
            <if test="fubeiBalance != null">`FUBEI_BALANCE`,</if>
            <if test="incomeTotalAmount != null">`INCOME_TOTAL_AMOUNT`,</if>
            <if test="settleTotalAmount != null">`SETTLE_TOTAL_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="uid != null">#{uid,jdbcType=INTEGER},</if>
            <if test="checkDay != null">#{checkDay,jdbcType=INTEGER},</if>
            <if test="checkType != null">#{checkType,jdbcType=INTEGER},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="fbankBalance != null">#{fbankBalance,jdbcType=DECIMAL},</if>
            <if test="fubeiBalance != null">#{fubeiBalance,jdbcType=DECIMAL},</if>
            <if test="incomeTotalAmount != null">#{incomeTotalAmount,jdbcType=DECIMAL},</if>
            <if test="settleTotalAmount != null">#{settleTotalAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
    <operation name="update" paramtype="object" remark="update table:TP_FBANK_AMOUNT_CHECK">
        UPDATE TP_FBANK_AMOUNT_CHECK
        SET
        <trim prefix="" suffix="" suffixOverrides="," prefixOverrides=",">
            <if test="uid != null">UID = #{uid,jdbcType=INTEGER}</if>
            <if test="checkDay != null">,CHECK_DAY = #{checkDay,jdbcType=INTEGER}</if>
            <if test="checkType != null">,CHECK_TYPE = #{checkType,jdbcType=INTEGER}</if>
            <if test="checkStatus != null">,CHECK_STATUS = #{checkStatus,jdbcType=INTEGER}</if>
            <if test="createTime != null">,CREATE_TIME = #{createTime,jdbcType=TIMESTAMP}</if>
            <if test="updateTime != null">,UPDATE_TIME = #{updateTime,jdbcType=TIMESTAMP}</if>
            <if test="fbankBalance != null">,FBANK_BALANCE = #{fbankBalance,jdbcType=DECIMAL}</if>
            <if test="fubeiBalance != null">,FUBEI_BALANCE = #{fubeiBalance,jdbcType=DECIMAL}</if>
            <if test="incomeTotalAmount != null">,INCOME_TOTAL_AMOUNT = #{incomeTotalAmount,jdbcType=DECIMAL}</if>
            <if test="settleTotalAmount != null">,SETTLE_TOTAL_AMOUNT = #{settleTotalAmount,jdbcType=DECIMAL}</if>
        </trim>
        WHERE
        ID
        = #{id,jdbcType=BIGINT}
    </operation>

    <operation name="getByUidAndCheckTypeAndCheckDay" remark="根据 uid 、check_type、check_day获得信息" multiplicity="one">
        SELECT
        *
        FROM
        TP_FBANK_AMOUNT_CHECK
        WHERE
        UID = #{uid,jdbcType=INTEGER}
        AND
        CHECK_DAY = #{checkDay,jdbcType=INTEGER}
        AND
        CHECK_TYPE = #{checkType,jdbcType=INTEGER}
    </operation>
</table>
