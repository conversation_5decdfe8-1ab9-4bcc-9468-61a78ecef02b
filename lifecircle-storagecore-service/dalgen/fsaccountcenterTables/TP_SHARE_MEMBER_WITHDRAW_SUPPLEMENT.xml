<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT" physicalName="TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT"
       remark="客户补提记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT">
        INSERT INTO TP_SHARE_MEMBER_WITHDRAW_SUPPLEMENT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="cashStatus != null">`CASH_STATUS`,</if>
            <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
            <if test="operatorId != null">`OPERATOR_ID`,</if>
            <if test="operatorName != null">`OPERATOR_NAME`,</if>
            <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
            <if test="newSerialNumber != null">`NEW_SERIAL_NUMBER`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
            <if test="operatorName != null">#{operatorName,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="newSerialNumber != null">#{newSerialNumber,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getDiffSupplementWithdrawList" multiplicity="many" remark="批量查询状态不为成功的提现数据">
        select
        *
        from
        tp_share_member_withdraw_supplement
        where
        new_serial_number in
        <foreach close=")" collection="list" index="index" item="newSerialNumber" open="(" separator=",">
            #{newSerialNumber,jdbcType=VARCHAR}
        </foreach>
        and
        cash_status in ('NOSUMBIT', 'PROCESSING', 'FAIL')
    </operation>

    <operation name="updateSupplementWithdrawBySerialNumber" paramtype="object" remark="根据订单号更新打款状态">
        update
        tp_share_member_withdraw_supplement
        set
        cash_status = #{cashStatus,jdbcType=VARCHAR}
        where
        new_serial_number = #{newSerialNumber,jdbcType=VARCHAR}
    </operation>

</table>
