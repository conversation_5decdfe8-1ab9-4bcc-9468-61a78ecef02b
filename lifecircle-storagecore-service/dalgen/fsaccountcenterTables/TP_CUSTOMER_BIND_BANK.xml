<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_CUSTOMER_BIND_BANK" physicalName="TP_CUSTOMER_BIND_BANK"
       remark="客户绑卡表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_CUSTOMER_BIND_BANK">
        INSERT INTO TP_CUSTOMER_BIND_BANK
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="active != null">`ACTIVE`,</if>
            <if test="cardId != null">`CARD_ID`,</if>
            <if test="certId != null">`CERT_ID`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="bankName != null">`BANK_NAME`,</if>
            <if test="cardName != null">`CARD_NAME`,</if>
            <if test="certType != null">`CERT_TYPE`,</if>
            <if test="unionCode != null">`UNION_CODE`,</if>
            <if test="branchName != null">`BRANCH_NAME`,</if>
            <if test="customerId != null">`CUSTOMER_ID`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="bankAcctType != null">`BANK_ACCT_TYPE`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="cardFrontPic != null">`CARD_FRONT_PIC`,</if>
            <if test="cardBackPic != null">`CARD_BACK_PIC`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="active != null">#{active,jdbcType=VARCHAR},</if>
            <if test="cardId != null">#{cardId,jdbcType=VARCHAR},</if>
            <if test="certId != null">#{certId,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
            <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
            <if test="certType != null">#{certType,jdbcType=VARCHAR},</if>
            <if test="unionCode != null">#{unionCode,jdbcType=VARCHAR},</if>
            <if test="branchName != null">#{branchName,jdbcType=VARCHAR},</if>
            <if test="customerId != null">#{customerId,jdbcType=VARCHAR},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=VARCHAR},</if>
            <if test="bankAcctType != null">#{bankAcctType,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="cardFrontPic != null">#{cardFrontPic,jdbcType=VARCHAR},</if>
            <if test="cardBackPic != null">#{cardBackPic,jdbcType=VARCHAR},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getByIdcardAndCard" multiplicity="one" remark="根据持卡人证件号码+银行卡号+生效状态查询记录">
        SELECT
        `id`
        FROM tp_customer_bind_bank
        WHERE
        `cert_id` = #{certId,jdbcType=VARCHAR}
        AND
        `card_id` = #{cardId,jdbcType=VARCHAR}
        AND
        (`active` = "YES" OR (`active` = "NO" AND `check_status` = "TOBEAUDIT"))
        limit 1
    </operation>
    <operation name="getBindBankListByCardId" multiplicity="many" remark="根据卡号查询持卡信息">
        select
        *
        from
        tp_customer_bind_bank
        where
        check_status = 'PASS'
        and active = 'YES'
        and card_id in
        <foreach close=")" collection="list" index="index" item="cardId" open="(" separator=",">
            #{cardId,jdbcType=VARCHAR}
        </foreach>
    </operation>
    <operation name="getActiveBindBankByCustomerId" multiplicity="one" remark="根据客户id查询客户绑卡信息">
        SELECT
        *
        FROM
        TP_CUSTOMER_BIND_BANK
        WHERE
        customer_id = #{customerId,jdbcType=VARCHAR}
        AND
        active = 'YES'
        AND
        check_status = "PASS"
        LIMIT 1
    </operation>
    <operation name="getByCardIdWithOutActive" multiplicity="one" remark="根据银行卡号查询客户绑卡信息不需要判断生效">
        SELECT
        *
        FROM
        TP_CUSTOMER_BIND_BANK
        WHERE
        card_id = #{cardId,jdbcType=VARCHAR}
        AND
        customer_id = #{customerId,jdbcType=VARCHAR}
        ORDER BY ID DESC
        LIMIT 1
    </operation>

</table>
