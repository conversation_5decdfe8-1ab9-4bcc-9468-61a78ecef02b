<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="TP_SHARE_MEMBER_WITHDRAW_EXCEPTION" physicalName="TP_SHARE_MEMBER_WITHDRAW_EXCEPTION"
       remark="客户补提记录表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:TP_SHARE_MEMBER_WITHDRAW_EXCEPTION">
        INSERT INTO TP_SHARE_MEMBER_WITHDRAW_EXCEPTION
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="reason != null">`REASON`,</if>
            <if test="tokenNo != null">`TOKEN_NO`,</if>
            <if test="cashStatus != null">`CASH_STATUS`,</if>
            <if test="frontLogNo != null">`FRONT_LOG_NO`,</if>
            <if test="serialNumber != null">`SERIAL_NUMBER`,</if>
            <if test="newSerialNumber != null">`NEW_SERIAL_NUMBER`,</if>
            <if test="customerAccountId != null">`CUSTOMER_ACCOUNT_ID`,</if>
            <if test="customerBindBankId != null">`CUSTOMER_BIND_BANK_ID`,</if>
            <if test="finishTime != null">`FINISH_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="feeAmount != null">`FEE_AMOUNT`,</if>
            <if test="cashAmount != null">`CASH_AMOUNT`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=INTEGER},</if>
            <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
            <if test="tokenNo != null">#{tokenNo,jdbcType=VARCHAR},</if>
            <if test="cashStatus != null">#{cashStatus,jdbcType=VARCHAR},</if>
            <if test="frontLogNo != null">#{frontLogNo,jdbcType=VARCHAR},</if>
            <if test="serialNumber != null">#{serialNumber,jdbcType=VARCHAR},</if>
            <if test="newSerialNumber != null">#{newSerialNumber,jdbcType=VARCHAR},</if>
            <if test="customerAccountId != null">#{customerAccountId,jdbcType=VARCHAR},</if>
            <if test="customerBindBankId != null">#{customerBindBankId,jdbcType=VARCHAR},</if>
            <if test="finishTime != null">#{finishTime,jdbcType=INTEGER},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="feeAmount != null">#{feeAmount,jdbcType=DECIMAL},</if>
            <if test="cashAmount != null">#{cashAmount,jdbcType=DECIMAL},</if>
        </trim>
    </operation>

    <operation name="getExceptionWithdrawList" multiplicity="many" remark="查询已经存在的异常提现单">
        SELECT
        *
        FROM
        tp_share_member_withdraw_exception
        WHERE
        serial_number IN
        <foreach close=")" collection="list" index="index" item="serialNumber" open="(" separator=",">
            #{serialNumber,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="insertBatchBySxPay" paramtype="objectList" remark="批量插入异常提现记录">
        INSERT INTO tp_share_member_withdraw_exception (
        cash_amount,
        customer_bind_bank_id,
        reason,
        customer_account_id,
        front_log_no,
        serial_number,
        cash_status,
        finish_time
        ) VALUES
        <foreach collection="list" item="item" index="index" separator=",">
            (
            #{item.cashAmount,jdbcType=DECIMAL},
            #{item.customerBindBankId,jdbcType=VARCHAR},
            #{item.reason,jdbcType=VARCHAR},
            #{item.customerAccountId,jdbcType=VARCHAR},
            #{item.frontLogNo,jdbcType=VARCHAR},
            #{item.serialNumber,jdbcType=VARCHAR},
            #{item.cashStatus,jdbcType=VARCHAR},
            #{item.finishTime,jdbcType=INTEGER}
            )
        </foreach>
    </operation>
</table>
