<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PLATFORM_SETTLE_BILL_DATA" physicalName="ACC_PLATFORM_SETTLE_BILL_DATA"
    remark="平台账单下载原始数据表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PLATFORM_SETTLE_BILL_DATA">
INSERT INTO ACC_PLATFORM_SETTLE_BILL_DATA
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="dataId != null">`DATA_ID`,</if>
        <if test="ossUrl != null">`OSS_URL`,</if>
        <if test="configId != null">`CONFIG_ID`,</if>
        <if test="dataSource != null">`DATA_SOURCE`,</if>
        <if test="downloadId != null">`DOWNLOAD_ID`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="endDate != null">`END_DATE`,</if>
        <if test="runDate != null">`RUN_DATE`,</if>
        <if test="startDate != null">`START_DATE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="goodsSettleAmount != null">`goods_settle_amount`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="dataId != null">#{dataId,jdbcType=VARCHAR},</if>
        <if test="ossUrl != null">#{ossUrl,jdbcType=VARCHAR},</if>
        <if test="configId != null">#{configId,jdbcType=VARCHAR},</if>
        <if test="dataSource != null">#{dataSource,jdbcType=VARCHAR},</if>
        <if test="downloadId != null">#{downloadId,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="endDate != null">#{endDate,jdbcType=INTEGER},</if>
        <if test="runDate != null">#{runDate,jdbcType=INTEGER},</if>
        <if test="startDate != null">#{startDate,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="goodsSettleAmount != null">#{item.goodsSettleAmount,jdbcType=BIGINT},</if>
    </trim>
    </operation>

    <operation name="batchInsert" paramtype="objectList" remark="批量新增">
        INSERT INTO ACC_PLATFORM_SETTLE_BILL_DATA (
        data_id,
        config_id,
        platform_code,
        data_source,
        download_id,
        run_date,
        start_date,
        end_date)
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
            #{item.dataId,jdbcType=VARCHAR},
            #{item.configId,jdbcType=VARCHAR},
            #{item.platformCode,jdbcType=VARCHAR},
            #{item.dataSource,jdbcType=VARCHAR},
            #{item.downloadId,jdbcType=VARCHAR},
            #{item.runDate,jdbcType=INTEGER},
            #{item.startDate,jdbcType=INTEGER},
            #{item.endDate,jdbcType=INTEGER}
            )
        </foreach>
    </operation>

    <operation name="selectByDataIdList" multiplicity="many" remark="查询待处理的数据列表">
        select
        *
        from
        ACC_PLATFORM_SETTLE_BILL_DATA
        where
        data_id in
        <foreach collection="list" item="dataId" open="(" separator="," close=")">
            #{dataId,jdbcType=VARCHAR}
        </foreach>
    </operation>

    <operation name="updateOssUrlByDataId" remark="更新oss地址">
        update ACC_PLATFORM_SETTLE_BILL_DATA set oss_url = #{ossUrl,jdbcType=VARCHAR} where data_id = #{dataId,jdbcType=VARCHAR}
    </operation>
 </table>
