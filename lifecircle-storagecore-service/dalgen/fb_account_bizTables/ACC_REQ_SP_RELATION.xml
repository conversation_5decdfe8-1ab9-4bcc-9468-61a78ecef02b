<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_REQ_SP_RELATION" physicalName="ACC_REQ_SP_RELATION"
    remark="请求系统与平台商关联表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_REQ_SP_RELATION">
INSERT INTO ACC_REQ_SP_RELATION
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="reqId != null">`REQ_ID`,</if>
        <if test="reqName != null">`REQ_NAME`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="reqType != null">`REQ_TYPE`,</if>
        <if test="roleType != null">`ROLE_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="reqId != null">#{reqId,jdbcType=VARCHAR},</if>
        <if test="reqName != null">#{reqName,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="reqType != null">#{reqType,jdbcType=TINYINT},</if>
        <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getBySpId" remark="根据平台商查询">
        select * from acc_req_sp_relation
        where sp_id=#{spId,jdbcType=VARCHAR}
        AND del_flag = 1
        limit 1
    </operation>
</table>
