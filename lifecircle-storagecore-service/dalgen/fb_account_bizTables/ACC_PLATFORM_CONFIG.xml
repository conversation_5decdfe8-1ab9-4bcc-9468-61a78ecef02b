<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PLATFORM_CONFIG" physicalName="ACC_PLATFORM_CONFIG"
    remark="平台配置表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PLATFORM_CONFIG">
INSERT INTO ACC_PLATFORM_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="configId != null">`CONFIG_ID`,</if>
        <if test="extConfig != null">`EXT_CONFIG`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="platformName != null">`PLATFORM_NAME`,</if>
        <if test="platformUserId != null">`PLATFORM_USER_ID`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="activeFlag != null">`ACTIVE_FLAG`,</if>
        <if test="nextEndDay != null">`NEXT_END_DAY`,</if>
        <if test="nextStartDay != null">`NEXT_START_DAY`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="configId != null">#{configId,jdbcType=VARCHAR},</if>
        <if test="extConfig != null">#{extConfig,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="platformName != null">#{platformName,jdbcType=VARCHAR},</if>
        <if test="platformUserId != null">#{platformUserId,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="activeFlag != null">#{activeFlag,jdbcType=TINYINT},</if>
        <if test="nextEndDay != null">#{nextEndDay,jdbcType=INTEGER},</if>
        <if test="nextStartDay != null">#{nextStartDay,jdbcType=INTEGER},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="updateNextDay" remark="更新日期">
        update ACC_PLATFORM_CONFIG set nextStartDay = #{nextStartDay,jdbcType=INTEGER},nextEndDay=#{nextEndDay,jdbcType=INTEGER} where CONFIG_ID = #{configId,jdbcType=VARCHAR}
    </operation>

    <operation name="selectPlatformConfigData" multiplicity="many" resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.acctbiz.PlatformConfigDataDTO"
               remark="查询生效的配置列表">
        select
        a.config_id as configId,
        sp.sp_name as spName,
        a.spc_id as spcId,
        sp.sp_id as spId,
        spc.channel_code as channelCode,
        r.req_id as reqId,
        a.platform_code as platformCode,
        a.platform_name as platformName,
        a.platform_user_id as platformUserId,
        a.ext_config as extConfig,
        a.next_start_day as nextStartDay,
        a.next_end_day as nextEndDay,
        b.platform_settle_mode as platformSettleMode,
        b.platform_bill_time_span as platformBillTimeSpan,
        spb.card_no as cardNo,
        spb.card_name as cardName,
        spb.bank_inter_no as bankInterNo
        from
        ACC_PLATFORM_CONFIG a
        left join acc_service_fee_config b on a.spc_id = b.spc_id
        and a.platform_code = b.platform_code
        and b.del_flag = 1
        and b.profit_share_model = 1
        left join acc_service_provider_channel spc on a.spc_id = spc.spc_id
        left join acc_service_provider sp on spc.user_id = sp.sp_id
        left join acc_req_sp_relation r on sp.sp_id = r.sp_id and r.del_flag = 1
        left join acc_service_provider_bankcard spb on sp.sp_id = spb.sp_id and spb.del_flag = 1 and spb.card_type = 1
        where a.active_flag = 1
        and a.platform_code = #{platformCode,jdbcType=VARCHAR}
        <if test="configId != null and configId != ''">
            and a.config_id = #{configId,jdbcType=VARCHAR}
        </if>
        and a.del_flag = 1
        and b.id is not null
    </operation>

    <operation name="getPlatformConfigDataByDataId" multiplicity="one" resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.acctbiz.PlatformConfigDataDTO"
               remark="查询生效的配置列表">
        select
        a.config_id as configId,
        sp.sp_name as spName,
        a.spc_id as spcId,
        sp.sp_id as spId,
        spc.channel_code as channelCode,
        r.req_id as reqId,
        a.platform_code as platformCode,
        a.platform_name as platformName,
        a.platform_user_id as platformUserId,
        a.ext_config as extConfig,
        a.next_start_day as nextStartDay,
        a.next_end_day as nextEndDay,
        b.platform_settle_mode as platformSettleMode,
        b.platform_bill_time_span as platformBillTimeSpan,
        spb.card_no as cardNo,
        spb.card_name as cardName,
        spb.bank_inter_no as bankInterNo
        from
        ACC_PLATFORM_CONFIG a
        left join acc_service_fee_config b on a.spc_id = b.spc_id
        and a.platform_code = b.platform_code
        and b.del_flag = 1
        and b.profit_share_model = 1
        left join acc_platform_settle_bill_data pbd on a.config_id = pbd.config_id and pbd.data_id = #{dataId,jdbcType=VARCHAR}
        left join acc_service_provider_channel spc on a.spc_id = spc.spc_id
        left join acc_service_provider sp on spc.user_id = sp.sp_id
        left join acc_req_sp_relation r on sp.sp_id = r.sp_id and r.del_flag = 1
        left join acc_service_provider_bankcard spb on sp.sp_id = spb.sp_id and spb.del_flag = 1 and spb.card_type = 1
        where a.active_flag = 1
        and a.del_flag = 1
        and pbd.data_id = #{dataId,jdbcType=VARCHAR}
        limit 1
    </operation>
</table>
