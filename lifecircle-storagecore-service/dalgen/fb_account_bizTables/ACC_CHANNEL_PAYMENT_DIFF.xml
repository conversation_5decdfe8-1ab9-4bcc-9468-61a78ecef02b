<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
<table sqlname="ACC_CHANNEL_PAYMENT_DIFF" physicalName="ACC_CHANNEL_PAYMENT_DIFF"
    remark="入账差异表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:ACC_CHANNEL_PAYMENT_DIFF">
        INSERT INTO ACC_CHANNEL_PAYMENT_DIFF
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="billAmount != null">`BILL_AMOUNT`,</if>
            <if test="paymentAmount != null">`PAYMENT_AMOUNT`,</if>
            <if test="billNo != null">`BILL_NO`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="checkRemark != null">`CHECK_REMARK`,</if>
            <if test="checkStatus != null">`CHECK_STATUS`,</if>
            <if test="platformCode != null">`PLATFORM_CODE`,</if>
            <if test="applyOperatorId != null">`APPLY_OPERATOR_ID`,</if>
            <if test="checkOperatorId != null">`CHECK_OPERATOR_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="checkTime != null">`CHECK_TIME`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="paymentTime != null">`PAYMENT_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="billAmount != null">#{billAmount,jdbcType=BIGINT},</if>
            <if test="paymentAmount != null">#{paymentAmount,jdbcType=BIGINT},</if>
            <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="checkRemark != null">#{checkRemark,jdbcType=VARCHAR},</if>
            <if test="checkStatus != null">#{checkStatus,jdbcType=VARCHAR},</if>
            <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
            <if test="applyOperatorId != null">#{applyOperatorId,jdbcType=VARCHAR},</if>
            <if test="checkOperatorId != null">#{checkOperatorId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="checkTime != null">#{checkTime,jdbcType=TIMESTAMP},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="paymentTime != null">#{paymentTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="deleteDiffByBillNo" remark="删除分账待处理差异">
        delete
        from
        ACC_CHANNEL_PAYMENT_DIFF
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
        AND CHECK_STATUS = 'INIT'
    </operation>
</table>
