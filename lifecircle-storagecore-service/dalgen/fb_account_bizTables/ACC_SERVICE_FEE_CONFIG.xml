<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_SERVICE_FEE_CONFIG" physicalName="ACC_SERVICE_FEE_CONFIG"
    remark="计费规则表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_SERVICE_FEE_CONFIG">
INSERT INTO ACC_SERVICE_FEE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="configId != null">`CONFIG_ID`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="fubeiAccountId != null">`FUBEI_ACCOUNT_ID`,</if>
        <if test="virtualWalletId != null">`VIRTUAL_WALLET_ID`,</if>
        <if test="platformVirtualWalletId != null">`PLATFORM_VIRTUAL_WALLET_ID`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="platformSettleMode != null">`PLATFORM_SETTLE_MODE`,</if>
        <if test="platformBillTimeSpan != null">`PLATFORM_BILL_TIME_SPAN`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="serviceFeeRatio != null">`SERVICE_FEE_RATIO`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="configId != null">#{configId,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="fubeiAccountId != null">#{fubeiAccountId,jdbcType=VARCHAR},</if>
        <if test="virtualWalletId != null">#{virtualWalletId,jdbcType=VARCHAR},</if>
        <if test="platformVirtualWalletId != null">#{platformVirtualWalletId,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="platformSettleMode != null">#{platformSettleMode,jdbcType=TINYINT},</if>
        <if test="platformBillTimeSpan != null">#{platformBillTimeSpan,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="serviceFeeRatio != null">#{serviceFeeRatio,jdbcType=DECIMAL},</if>
    </trim>
    </operation>
    </table>
