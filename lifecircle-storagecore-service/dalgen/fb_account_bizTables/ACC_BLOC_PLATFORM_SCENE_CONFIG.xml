<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_BLOC_PLATFORM_SCENE_CONFIG" physicalName="ACC_BLOC_PLATFORM_SCENE_CONFIG"
    remark="集团平台场景配置表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_BLOC_PLATFORM_SCENE_CONFIG">
INSERT INTO ACC_BLOC_PLATFORM_SCENE_CONFIG
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="customServiceRate != null">`CUSTOM_SERVICE_RATE`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="sceneCode != null">`SCENE_CODE`,</if>
        <if test="sceneName != null">`SCENE_NAME`,</if>
        <if test="sceneType != null">`SCENE_TYPE`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="parentSceneCode != null">`PARENT_SCENE_CODE`,</if>
        <if test="relatedBillCode != null">`RELATED_BILL_CODE`,</if>
        <if test="ruleSwitch != null">`RULE_SWITCH`,</if>
        <if test="fundFlowType != null">`FUND_FLOW_TYPE`,</if>
        <if test="isPresetScene != null">`IS_PRESET_SCENE`,</if>
        <if test="serviceFeeSwitch != null">`SERVICE_FEE_SWITCH`,</if>
        <if test="matchBillProductType != null">`MATCH_BILL_PRODUCT_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="dataSource != null">`DATA_SOURCE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="customServiceRate != null">#{customServiceRate,jdbcType=DECIMAL},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="sceneCode != null">#{sceneCode,jdbcType=VARCHAR},</if>
        <if test="sceneName != null">#{sceneName,jdbcType=VARCHAR},</if>
        <if test="sceneType != null">#{sceneType,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="parentSceneCode != null">#{parentSceneCode,jdbcType=VARCHAR},</if>
        <if test="relatedBillCode != null">#{relatedBillCode,jdbcType=VARCHAR},</if>
        <if test="ruleSwitch != null">#{ruleSwitch,jdbcType=TINYINT},</if>
        <if test="fundFlowType != null">#{fundFlowType,jdbcType=TINYINT},</if>
        <if test="isPresetScene != null">#{isPresetScene,jdbcType=TINYINT},</if>
        <if test="serviceFeeSwitch != null">#{serviceFeeSwitch,jdbcType=TINYINT},</if>
        <if test="matchBillProductType != null">#{matchBillProductType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="dataSource != null">#{dataSource,jdbcType=VARCHAR},</if>
    </trim>
    </operation>
    
    <operation name="getByBlocIdAndPlatformCode" multiplicity="many">
        SELECT
        *
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
    </operation>
    <operation name="getByBlocIdAndPlatformCodeAndSceneType" multiplicity="many">
        SELECT
        *
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
    </operation>

    <operation name="getByParentSceneCode" multiplicity="many">
        SELECT
        *
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE PARENT_SCENE_CODE = #{parentSceneCode,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
        AND BLOC_ID = #{blocId,jdbcType=VARCHAR}
    </operation>

    <operation name="getByBlocIdAndPlatformCodeAndSceneTypeAndDataSource" multiplicity="many">
        SELECT
        *
        FROM ACC_BLOC_PLATFORM_SCENE_CONFIG
        WHERE BLOC_ID = #{blocId,jdbcType=VARCHAR}
        AND PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        AND SCENE_TYPE = #{sceneType,jdbcType=VARCHAR}
        AND DATA_SOURCE = #{dataSource,jdbcType=VARCHAR}
    </operation>
    </table>
