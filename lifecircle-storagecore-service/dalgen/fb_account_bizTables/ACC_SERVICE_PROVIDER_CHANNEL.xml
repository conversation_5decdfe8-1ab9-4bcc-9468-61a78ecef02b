<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_SERVICE_PROVIDER_CHANNEL" physicalName="ACC_SERVICE_PROVIDER_CHANNEL"
    remark="服务商账户通道配置表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_SERVICE_PROVIDER_CHANNEL">
INSERT INTO ACC_SERVICE_PROVIDER_CHANNEL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="active != null">`ACTIVE`,</if>
        <if test="userId != null">`USER_ID`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="outAccountId != null">`OUT_ACCOUNT_ID`,</if>
        <if test="extendConfiguration != null">`EXTEND_CONFIGURATION`,</if>
        <if test="isSync != null">`IS_SYNC`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="roleType != null">`ROLE_TYPE`,</if>
        <if test="isDefault != null">`IS_DEFAULT`,</if>
        <if test="configStatus != null">`CONFIG_STATUS`,</if>
        <if test="isAffiliation != null">`IS_AFFILIATION`,</if>
        <if test="fundSettleMode != null">`FUND_SETTLE_MODE`,</if>
        <if test="withdrawalMode != null">`WITHDRAWAL_MODE`,</if>
        <if test="profitShareMode != null">`PROFIT_SHARE_MODE`,</if>
        <if test="enableAccountWallet != null">`ENABLE_ACCOUNT_WALLET`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="active != null">#{active,jdbcType=VARCHAR},</if>
        <if test="userId != null">#{userId,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="outAccountId != null">#{outAccountId,jdbcType=VARCHAR},</if>
        <if test="extendConfiguration != null">#{extendConfiguration,jdbcType=VARCHAR},</if>
        <if test="isSync != null">#{isSync,jdbcType=TINYINT},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="roleType != null">#{roleType,jdbcType=TINYINT},</if>
        <if test="isDefault != null">#{isDefault,jdbcType=TINYINT},</if>
        <if test="configStatus != null">#{configStatus,jdbcType=TINYINT},</if>
        <if test="isAffiliation != null">#{isAffiliation,jdbcType=TINYINT},</if>
        <if test="fundSettleMode != null">#{fundSettleMode,jdbcType=TINYINT},</if>
        <if test="withdrawalMode != null">#{withdrawalMode,jdbcType=TINYINT},</if>
        <if test="profitShareMode != null">#{profitShareMode,jdbcType=TINYINT},</if>
        <if test="enableAccountWallet != null">#{enableAccountWallet,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="getBySpcId" paramtype="primitive" multiplicity="one" remark="根据spcId查询渠道配置信息">
        select *
        from acc_service_provider_channel
        where spc_id = #{spcId,jdbcType=VARCHAR}
        and active = 'YES' and del_flag = 1
        limit 1
    </operation>
</table>
