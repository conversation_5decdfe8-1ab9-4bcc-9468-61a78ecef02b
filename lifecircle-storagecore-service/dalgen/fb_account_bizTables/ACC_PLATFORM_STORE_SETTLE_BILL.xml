<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PLATFORM_STORE_SETTLE_BILL" physicalName="ACC_PLATFORM_STORE_SETTLE_BILL"
    remark="平台门店结算单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PLATFORM_STORE_SETTLE_BILL">
INSERT INTO ACC_PLATFORM_STORE_SETTLE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="settleAmount != null">`SETTLE_AMOUNT`,</if>
        <if test="refundSettleCount != null">`REFUND_SETTLE_COUNT`,</if>
        <if test="paymentSettleCount != null">`PAYMENT_SETTLE_COUNT`,</if>
        <if test="refundSettleAmount != null">`REFUND_SETTLE_AMOUNT`,</if>
        <if test="paymentSettleAmount != null">`PAYMENT_SETTLE_AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
        <if test="billHandleSource != null">`BILL_HANDLE_SOURCE`,</if>
        <if test="platformStoreName != null">`PLATFORM_STORE_NAME`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="profitShareBatchId != null">`PROFIT_SHARE_BATCH_ID`,</if>
        <if test="billHandleStatus != null">`BILL_HANDLE_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="frozenCount != null">`FROZEN_COUNT`,</if>
        <if test="outTaskId != null">`OUT_TASK_ID`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="settleAmount != null">#{settleAmount,jdbcType=BIGINT},</if>
        <if test="refundSettleCount != null">#{refundSettleCount,jdbcType=BIGINT},</if>
        <if test="paymentSettleCount != null">#{paymentSettleCount,jdbcType=BIGINT},</if>
        <if test="refundSettleAmount != null">#{refundSettleAmount,jdbcType=BIGINT},</if>
        <if test="paymentSettleAmount != null">#{paymentSettleAmount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
        <if test="billHandleSource != null">#{billHandleSource,jdbcType=VARCHAR},</if>
        <if test="platformStoreName != null">#{platformStoreName,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="profitShareBatchId != null">#{profitShareBatchId,jdbcType=VARCHAR},</if>
        <if test="billHandleStatus != null">#{billHandleStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="frozenCount != null">#{frozenCount,jdbcType=BIGINT},</if>
        <if test="outTaskId != null">#{outTaskId,jdbcType=VARCHAR},</if>
    </trim>
    </operation>

    <operation name="batchInsert" paramtype="objectList" remark="批量新增">
        INSERT INTO ACC_PLATFORM_STORE_SETTLE_BILL (
            `SETTLE_AMOUNT`,
            `REFUND_SETTLE_COUNT`,
            `PAYMENT_SETTLE_COUNT`,
            `REFUND_SETTLE_AMOUNT`,
            `PAYMENT_SETTLE_AMOUNT`,
            `SP_ID`,
            `BILL_NO`,
            `BLOC_ID`,
            `SOURCE`,
            `SETTLE_DATE`,
            `STORE_BILL_NO`,
            `PLATFORM_CODE`,
            `PLATFORM_STORE_ID`,
            `PLATFORM_STORE_NAME`,
            `PLATFORM_SETTLE_DATE`,
            `FROZEN_AMOUNT`,
            `FROZEN_COUNT`,
            `goods_settle_count`,
            `goods_settle_amount`,
            `reconciliation_amount`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.settleAmount,jdbcType=BIGINT},
                #{item.refundSettleCount,jdbcType=BIGINT},
                #{item.paymentSettleCount,jdbcType=BIGINT},
                #{item.refundSettleAmount,jdbcType=BIGINT},
                #{item.paymentSettleAmount,jdbcType=BIGINT},
                #{item.spId,jdbcType=VARCHAR},
                #{item.billNo,jdbcType=VARCHAR},
                #{item.blocId,jdbcType=VARCHAR},
                #{item.source,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.storeBillNo,jdbcType=VARCHAR},
                #{item.platformCode,jdbcType=VARCHAR},
                #{item.platformStoreId,jdbcType=VARCHAR},
                #{item.platformStoreName,jdbcType=VARCHAR},
                #{item.platformSettleDate,jdbcType=VARCHAR},
                #{item.frozenAmount,jdbcType=BIGINT},
                #{item.frozenCount,jdbcType=BIGINT},
                #{item.goodsSettleCount,jdbcType=BIGINT},
                #{item.goodsSettleAmount,jdbcType=BIGINT},
                #{item.reconciliationAmount,jdbcType=BIGINT}
            )
        </foreach>
    </operation>

    <operation name="deleteByBillNo" remark="查询待处理的数据列表">
        delete
        from
        ACC_PLATFORM_STORE_SETTLE_BILL
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
    </operation>
</table>
