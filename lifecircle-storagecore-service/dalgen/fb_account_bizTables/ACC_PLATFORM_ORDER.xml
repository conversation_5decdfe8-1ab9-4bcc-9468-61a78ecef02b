<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PLATFORM_ORDER" physicalName="ACC_PLATFORM_ORDER"
    remark="平台订单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PLATFORM_ORDER">
INSERT INTO ACC_PLATFORM_ORDER
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="orderPrice != null">`ORDER_PRICE`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="dataId != null">`DATA_ID`,</if>
        <if test="orderType != null">`ORDER_TYPE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="platformGoodsId != null">`PLATFORM_GOODS_ID`,</if>
        <if test="platformOrderNo != null">`PLATFORM_ORDER_NO`,</if>
        <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
        <if test="platformGoodsName != null">`PLATFORM_GOODS_NAME`,</if>
        <if test="platformStoreName != null">`PLATFORM_STORE_NAME`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="orderRealType != null">`ORDER_REAL_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="platformTradeTime != null">`PLATFORM_TRADE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="productType != null">`PRODUCT_TYPE`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="orderPrice != null">#{orderPrice,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="dataId != null">#{dataId,jdbcType=VARCHAR},</if>
        <if test="orderType != null">#{orderType,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="platformGoodsId != null">#{platformGoodsId,jdbcType=VARCHAR},</if>
        <if test="platformOrderNo != null">#{platformOrderNo,jdbcType=VARCHAR},</if>
        <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
        <if test="platformGoodsName != null">#{platformGoodsName,jdbcType=VARCHAR},</if>
        <if test="platformStoreName != null">#{platformStoreName,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="orderRealType != null">#{orderRealType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="platformTradeTime != null">#{platformTradeTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="productType != null">#{productType,jdbcType=VARCHAR},</if>
    </trim>
    </operation>

    <operation name="batchInsert" paramtype="objectList" remark="批量新增">
        INSERT INTO ACC_PLATFORM_ORDER (
            `ORDER_PRICE`,
            `SP_ID`,
            `BILL_NO`,
            `BLOC_ID`,
            `DATA_ID`,
            `ORDER_TYPE`,
            `SETTLE_DATE`,
            `STORE_BILL_NO`,
            `PLATFORM_CODE`,
            `PLATFORM_GOODS_ID`,
            `PLATFORM_ORDER_NO`,
            `PLATFORM_STORE_ID`,
            `PLATFORM_GOODS_NAME`,
            `PLATFORM_STORE_NAME`,
            `PLATFORM_SETTLE_DATE`,
            `PLATFORM_TRADE_TIME`,
            `FROZEN_AMOUNT`,
            `BUSINESS_SCENE_CODE`,
            `PRODUCT_SCENE_CODE`
        )
        VALUES
        <foreach collection="list" item="item" separator=",">
            (
                #{item.orderPrice,jdbcType=BIGINT},
                #{item.spId,jdbcType=VARCHAR},
                #{item.billNo,jdbcType=VARCHAR},
                #{item.blocId,jdbcType=VARCHAR},
                #{item.dataId,jdbcType=VARCHAR},
                #{item.orderType,jdbcType=VARCHAR},
                #{item.settleDate,jdbcType=VARCHAR},
                #{item.storeBillNo,jdbcType=VARCHAR},
                #{item.platformCode,jdbcType=VARCHAR},
                #{item.platformGoodsId,jdbcType=VARCHAR},
                #{item.platformOrderNo,jdbcType=VARCHAR},
                #{item.platformStoreId,jdbcType=VARCHAR},
                #{item.platformGoodsName,jdbcType=VARCHAR},
                #{item.platformStoreName,jdbcType=VARCHAR},
                #{item.platformSettleDate,jdbcType=VARCHAR},
                #{item.platformTradeTime,jdbcType=TIMESTAMP},
                #{item.frozenAmount,jdbcType=BIGINT},
                #{item.businessSceneCode,jdbcType=VARCHAR},
                #{item.productSceneCode,jdbcType=VARCHAR}
            )
        </foreach>
    </operation>

    <operation name="deleteByBillNo" remark="查询待处理的数据列表">
        delete
        from
        ACC_PLATFORM_ORDER
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
    </operation>
</table>
