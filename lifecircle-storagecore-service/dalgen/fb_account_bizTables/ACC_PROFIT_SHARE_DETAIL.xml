<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PROFIT_SHARE_DETAIL" physicalName="ACC_PROFIT_SHARE_DETAIL"
    remark="分账明细表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PROFIT_SHARE_DETAIL">
    INSERT INTO ACC_PROFIT_SHARE_DETAIL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="amount != null">`AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="cardNo != null">`CARD_NO`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="cardName != null">`CARD_NAME`,</if>
        <if test="walletId != null">`WALLET_ID`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="outTaskId != null">`OUT_TASK_ID`,</if>
        <if test="operatorId != null">`OPERATOR_ID`,</if>
        <if test="transferNo != null">`TRANSFER_NO`,</if>
        <if test="withdrawNo != null">`WITHDRAW_NO`,</if>
        <if test="autoWithdraw != null">`AUTO_WITHDRAW`,</if>
        <if test="accMerchantId != null">`ACC_MERCHANT_ID`,</if>
        <if test="outTransferNo != null">`OUT_TRANSFER_NO`,</if>
        <if test="outWithdrawNo != null">`OUT_WITHDRAW_NO`,</if>
        <if test="settleBankCode != null">`SETTLE_BANK_CODE`,</if>
        <if test="settleAccountNo != null">`SETTLE_ACCOUNT_NO`,</if>
        <if test="settleAccountName != null">`SETTLE_ACCOUNT_NAME`,</if>
        <if test="profitShareBatchId != null">`PROFIT_SHARE_BATCH_ID`,</if>
        <if test="profitShareDetailId != null">`PROFIT_SHARE_DETAIL_ID`,</if>
        <if test="profitShareDetailStatus != null">`PROFIT_SHARE_DETAIL_STATUS`,</if>
        <if test="profitShareMerchantType != null">`PROFIT_SHARE_MERCHANT_TYPE`,</if>
        <if test="profitShareWithdrawStatus != null">`PROFIT_SHARE_WITHDRAW_STATUS`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="profitShareCreateTime != null">`PROFIT_SHARE_CREATE_TIME`,</if>
        <if test="profitShareFinishTime != null">`PROFIT_SHARE_FINISH_TIME`,</if>
        <if test="profitShareWithdrawTime != null">`PROFIT_SHARE_WITHDRAW_TIME`,</if>
    </trim>
    VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fee != null">#{fee,jdbcType=BIGINT},</if>
        <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="cardNo != null">#{cardNo,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="cardName != null">#{cardName,jdbcType=VARCHAR},</if>
        <if test="walletId != null">#{walletId,jdbcType=VARCHAR},</if>
        <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
        <if test="outTaskId != null">#{outTaskId,jdbcType=VARCHAR},</if>
        <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
        <if test="transferNo != null">#{transferNo,jdbcType=VARCHAR},</if>
        <if test="withdrawNo != null">#{withdrawNo,jdbcType=VARCHAR},</if>
        <if test="autoWithdraw != null">#{autoWithdraw,jdbcType=VARCHAR},</if>
        <if test="accMerchantId != null">#{accMerchantId,jdbcType=VARCHAR},</if>
        <if test="outTransferNo != null">#{outTransferNo,jdbcType=VARCHAR},</if>
        <if test="outWithdrawNo != null">#{outWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="settleBankCode != null">#{settleBankCode,jdbcType=VARCHAR},</if>
        <if test="settleAccountNo != null">#{settleAccountNo,jdbcType=VARCHAR},</if>
        <if test="settleAccountName != null">#{settleAccountName,jdbcType=VARCHAR},</if>
        <if test="profitShareBatchId != null">#{profitShareBatchId,jdbcType=VARCHAR},</if>
        <if test="profitShareDetailId != null">#{profitShareDetailId,jdbcType=VARCHAR},</if>
        <if test="profitShareDetailStatus != null">#{profitShareDetailStatus,jdbcType=VARCHAR},</if>
        <if test="profitShareMerchantType != null">#{profitShareMerchantType,jdbcType=VARCHAR},</if>
        <if test="profitShareWithdrawStatus != null">#{profitShareWithdrawStatus,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareCreateTime != null">#{profitShareCreateTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareFinishTime != null">#{profitShareFinishTime,jdbcType=TIMESTAMP},</if>
        <if test="profitShareWithdrawTime != null">#{profitShareWithdrawTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="pageProfitShareOrderDetail" multiplicity="paging" paging="ProfitShareOrderDetail" remark="列表分页查询"
               resulttype="com.fshows.lifecircle.storagecore.service.domain.dto.acctbiz.ProfitShareDetailDTO">
        SELECT
        a.`profit_share_detail_id` AS profitShareDetailId,
        a.`amount` AS amount,
        a.`create_time` AS createTime,
        a.`account_id` AS accountId,
        b.`merchant_name` AS merchantName,
        a.`profit_share_merchant_type` AS profitShareMerchantType,
        a.actual_amount AS actualAmount
        FROM
        `acc_profit_share_detail` a
        LEFT JOIN `acc_merchant` b ON a.`acc_merchant_id` = b.`acc_merchant_id`
        WHERE
        a.`out_task_id` = #{outTaskId,jdbcType=VARCHAR}
        and a.del_flag = 1
    </operation>

    <operation name="getLimitOneByOutTaskId" multiplicity="one" remark="根据外部补单单号获取分账详情">
        SELECT
        *
        FROM
        ACC_PROFIT_SHARE_DETAIL
        WHERE
        out_task_id = #{outTaskId,jdbcType=VARCHAR}
        AND DEL_FLAG = 1
        limit 1
    </operation>
</table>
