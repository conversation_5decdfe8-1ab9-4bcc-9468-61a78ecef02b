<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_ACCOUNT_WITHDRAW" physicalName="ACC_ACCOUNT_WITHDRAW"
    remark="提现订单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_ACCOUNT_WITHDRAW">
INSERT INTO ACC_ACCOUNT_WITHDRAW
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="fee != null">`FEE`,</if>
        <if test="amount != null">`AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="spcId != null">`SPC_ID`,</if>
        <if test="reason != null">`REASON`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="bankName != null">`BANK_NAME`,</if>
        <if test="walletId != null">`WALLET_ID`,</if>
        <if test="accountId != null">`ACCOUNT_ID`,</if>
        <if test="voucherNo != null">`VOUCHER_NO`,</if>
        <if test="operatorId != null">`OPERATOR_ID`,</if>
        <if test="voucherUrl != null">`VOUCHER_URL`,</if>
        <if test="withdrawNo != null">`WITHDRAW_NO`,</if>
        <if test="accountName != null">`ACCOUNT_NAME`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="innerReason != null">`INNER_REASON`,</if>
        <if test="withdrawDate != null">`WITHDRAW_DATE`,</if>
        <if test="withdrawMode != null">`WITHDRAW_MODE`,</if>
        <if test="outWithdrawNo != null">`OUT_WITHDRAW_NO`,</if>
        <if test="bankBranchCode != null">`BANK_BRANCH_CODE`,</if>
        <if test="settleBankCode != null">`SETTLE_BANK_CODE`,</if>
        <if test="withdrawStatus != null">`WITHDRAW_STATUS`,</if>
        <if test="settleAccountId != null">`SETTLE_ACCOUNT_ID`,</if>
        <if test="settleAccountNo != null">`SETTLE_ACCOUNT_NO`,</if>
        <if test="virtualWalletId != null">`VIRTUAL_WALLET_ID`,</if>
        <if test="platformWithdrawNo != null">`PLATFORM_WITHDRAW_NO`,</if>
        <if test="trackWithdrawStatus != null">`TRACK_WITHDRAW_STATUS`,</if>
        <if test="delFlag != null">`DEL_FLAG`,</if>
        <if test="isRefunded != null">`IS_REFUNDED`,</if>
        <if test="settleType != null">`SETTLE_TYPE`,</if>
        <if test="operatorType != null">`OPERATOR_TYPE`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="finishTime != null">`FINISH_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="fee != null">#{fee,jdbcType=BIGINT},</if>
        <if test="amount != null">#{amount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="spcId != null">#{spcId,jdbcType=VARCHAR},</if>
        <if test="reason != null">#{reason,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="bankName != null">#{bankName,jdbcType=VARCHAR},</if>
        <if test="walletId != null">#{walletId,jdbcType=VARCHAR},</if>
        <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
        <if test="voucherNo != null">#{voucherNo,jdbcType=VARCHAR},</if>
        <if test="operatorId != null">#{operatorId,jdbcType=VARCHAR},</if>
        <if test="voucherUrl != null">#{voucherUrl,jdbcType=VARCHAR},</if>
        <if test="withdrawNo != null">#{withdrawNo,jdbcType=VARCHAR},</if>
        <if test="accountName != null">#{accountName,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="innerReason != null">#{innerReason,jdbcType=VARCHAR},</if>
        <if test="withdrawDate != null">#{withdrawDate,jdbcType=VARCHAR},</if>
        <if test="withdrawMode != null">#{withdrawMode,jdbcType=VARCHAR},</if>
        <if test="outWithdrawNo != null">#{outWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="bankBranchCode != null">#{bankBranchCode,jdbcType=VARCHAR},</if>
        <if test="settleBankCode != null">#{settleBankCode,jdbcType=VARCHAR},</if>
        <if test="withdrawStatus != null">#{withdrawStatus,jdbcType=VARCHAR},</if>
        <if test="settleAccountId != null">#{settleAccountId,jdbcType=VARCHAR},</if>
        <if test="settleAccountNo != null">#{settleAccountNo,jdbcType=VARCHAR},</if>
        <if test="virtualWalletId != null">#{virtualWalletId,jdbcType=VARCHAR},</if>
        <if test="platformWithdrawNo != null">#{platformWithdrawNo,jdbcType=VARCHAR},</if>
        <if test="trackWithdrawStatus != null">#{trackWithdrawStatus,jdbcType=VARCHAR},</if>
        <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
        <if test="isRefunded != null">#{isRefunded,jdbcType=TINYINT},</if>
        <if test="settleType != null">#{settleType,jdbcType=TINYINT},</if>
        <if test="operatorType != null">#{operatorType,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="finishTime != null">#{finishTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
    </trim>
    </operation>

    <operation name="updateByWithdrawNo" paramtype="object" remark="更新提现单信息">
        update acc_account_withdraw
        <set>
            <if test="fee != null">fee = #{fee,jdbcType=BIGINT},</if>
            <if test="amount != null">amount = #{amount,jdbcType=BIGINT},</if>
            <if test="spId != null">sp_id = #{spId,jdbcType=VARCHAR},</if>
            <if test="spcId != null">spc_id = #{spcId,jdbcType=VARCHAR},</if>
            <if test="reason != null">reason =#{reason,jdbcType=VARCHAR},</if>
            <if test="innerReason != null">inner_reason =#{innerReason,jdbcType=VARCHAR},</if>
            <if test="remark != null">remark = #{remark,jdbcType=VARCHAR},</if>
            <if test="withdrawStatus != null">withdraw_status = #{withdrawStatus,jdbcType=VARCHAR},</if>
            <if test="platformWithdrawNo != null">platform_withdraw_no = #{platformWithdrawNo,jdbcType=VARCHAR},</if>
            <if test="trackWithdrawStatus != null">track_withdraw_status = #{trackWithdrawStatus,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">del_flag = #{delFlag,jdbcType=TINYINT},</if>
            <if test="isRefunded != null">is_refunded = #{isRefunded,jdbcType=TINYINT},</if>
            <if test="finishTime != null">finish_time =#{finishTime,jdbcType=TIMESTAMP},</if>
            <if test="voucherUrl != null">voucher_url =#{voucherUrl,jdbcType=VARCHAR},</if>
            <if test="voucherNo != null">voucher_no =#{voucherNo,jdbcType=VARCHAR},</if>
            <if test="settleAccountNo != null">settle_account_no =#{settleAccountNo,jdbcType=VARCHAR},</if>
            <if test="bankBranchCode != null">bank_branch_code =#{bankBranchCode,jdbcType=VARCHAR},</if>
            <if test="bankName != null">bank_name =#{bankName,jdbcType=VARCHAR},</if>
            <if test="settleBankCode != null">settle_bank_code =#{settleBankCode,jdbcType=VARCHAR},</if>
        </set>
        where withdraw_no = #{withdrawNo,jdbcType=VARCHAR}
    </operation>

    <operation name="getByWithdrawNo" paramtype="primitive" multiplicity="one"  remark="根据提现单号查询提现单">
        select *
        from acc_account_withdraw
        where withdraw_no = #{withdrawNo,jdbcType=VARCHAR}
        and del_flag = 1
        limit 1
    </operation>
</table>
