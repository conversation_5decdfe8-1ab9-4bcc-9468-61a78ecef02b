<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PROFIT_SHARE_ORDER_DETAIL" physicalName="ACC_PROFIT_SHARE_ORDER_DETAIL"
    remark="分账订单明细表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:ACC_PROFIT_SHARE_ORDER_DETAIL">
        INSERT INTO ACC_PROFIT_SHARE_ORDER_DETAIL
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orderPrice != null">`ORDER_PRICE`,</if>
            <if test="profitSharePrice != null">`PROFIT_SHARE_PRICE`,</if>
            <if test="spId != null">`SP_ID`,</if>
            <if test="type != null">`TYPE`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="accountId != null">`ACCOUNT_ID`,</if>
            <if test="orderType != null">`ORDER_TYPE`,</if>
            <if test="storeBillNo != null">`STORE_BILL_NO`,</if>
            <if test="platformCode != null">`PLATFORM_CODE`,</if>
            <if test="platformOrderNo != null">`PLATFORM_ORDER_NO`,</if>
            <if test="platformStoreId != null">`PLATFORM_STORE_ID`,</if>
            <if test="relationOrderId != null">`RELATION_ORDER_ID`,</if>
            <if test="receiveMerchantId != null">`RECEIVE_MERCHANT_ID`,</if>
            <if test="profitShareDetailId != null">`PROFIT_SHARE_DETAIL_ID`,</if>
            <if test="profitShareMerchantId != null">`PROFIT_SHARE_MERCHANT_ID`,</if>
            <if test="profitShareOrderDetailId != null">`PROFIT_SHARE_ORDER_DETAIL_ID`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="dealStatus != null">`DEAL_STATUS`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
            <if test="ratio != null">`RATIO`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orderPrice != null">#{orderPrice,jdbcType=BIGINT},</if>
            <if test="profitSharePrice != null">#{profitSharePrice,jdbcType=BIGINT},</if>
            <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
            <if test="type != null">#{type,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
            <if test="orderType != null">#{orderType,jdbcType=VARCHAR},</if>
            <if test="storeBillNo != null">#{storeBillNo,jdbcType=VARCHAR},</if>
            <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
            <if test="platformOrderNo != null">#{platformOrderNo,jdbcType=VARCHAR},</if>
            <if test="platformStoreId != null">#{platformStoreId,jdbcType=VARCHAR},</if>
            <if test="relationOrderId != null">#{relationOrderId,jdbcType=BIGINT},</if>
            <if test="receiveMerchantId != null">#{receiveMerchantId,jdbcType=VARCHAR},</if>
            <if test="profitShareDetailId != null">#{profitShareDetailId,jdbcType=VARCHAR},</if>
            <if test="profitShareMerchantId != null">#{profitShareMerchantId,jdbcType=VARCHAR},</if>
            <if test="profitShareOrderDetailId != null">#{profitShareOrderDetailId,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="dealStatus != null">#{dealStatus,jdbcType=TINYINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
            <if test="ratio != null">#{ratio,jdbcType=DECIMAL},</if>
        </trim>
    </operation>
</table>
