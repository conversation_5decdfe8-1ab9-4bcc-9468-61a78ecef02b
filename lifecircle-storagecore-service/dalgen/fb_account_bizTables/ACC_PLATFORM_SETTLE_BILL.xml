<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_PLATFORM_SETTLE_BILL" physicalName="ACC_PLATFORM_SETTLE_BILL"
    remark="平台结算单表">
<!--    &lt;&gt;   <> -->
<operation name="insert" paramtype="object" remark="insert:ACC_PLATFORM_SETTLE_BILL">
INSERT INTO ACC_PLATFORM_SETTLE_BILL
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">`ID`,</if>
        <if test="settleAmount != null">`SETTLE_AMOUNT`,</if>
        <if test="refundSettleAmount != null">`REFUND_SETTLE_AMOUNT`,</if>
        <if test="paymentSettleAmount != null">`PAYMENT_SETTLE_AMOUNT`,</if>
        <if test="spId != null">`SP_ID`,</if>
        <if test="billNo != null">`BILL_NO`,</if>
        <if test="blocId != null">`BLOC_ID`,</if>
        <if test="remark != null">`REMARK`,</if>
        <if test="source != null">`SOURCE`,</if>
        <if test="settleDate != null">`SETTLE_DATE`,</if>
        <if test="channelCode != null">`CHANNEL_CODE`,</if>
        <if test="platformCode != null">`PLATFORM_CODE`,</if>
        <if test="billCheckSource != null">`BILL_CHECK_SOURCE`,</if>
        <if test="platformSettleDate != null">`PLATFORM_SETTLE_DATE`,</if>
        <if test="billCheckStatus != null">`BILL_CHECK_STATUS`,</if>
        <if test="createTime != null">`CREATE_TIME`,</if>
        <if test="updateTime != null">`UPDATE_TIME`,</if>
        <if test="frozenAmount != null">`FROZEN_AMOUNT`,</if>
        <if test="goodsSettleAmount != null">`GOODS_SETTLE_AMOUNT`,</if>
        <if test="reconciliationAmount != null">`RECONCILIATION_AMOUNT`,</if>
    </trim>
VALUES
    <trim prefix="(" suffix=")" suffixOverrides=",">
        <if test="id != null">#{id,jdbcType=BIGINT},</if>
        <if test="settleAmount != null">#{settleAmount,jdbcType=BIGINT},</if>
        <if test="refundSettleAmount != null">#{refundSettleAmount,jdbcType=BIGINT},</if>
        <if test="paymentSettleAmount != null">#{paymentSettleAmount,jdbcType=BIGINT},</if>
        <if test="spId != null">#{spId,jdbcType=VARCHAR},</if>
        <if test="billNo != null">#{billNo,jdbcType=VARCHAR},</if>
        <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
        <if test="remark != null">#{remark,jdbcType=VARCHAR},</if>
        <if test="source != null">#{source,jdbcType=VARCHAR},</if>
        <if test="settleDate != null">#{settleDate,jdbcType=VARCHAR},</if>
        <if test="channelCode != null">#{channelCode,jdbcType=VARCHAR},</if>
        <if test="platformCode != null">#{platformCode,jdbcType=VARCHAR},</if>
        <if test="billCheckSource != null">#{billCheckSource,jdbcType=VARCHAR},</if>
        <if test="platformSettleDate != null">#{platformSettleDate,jdbcType=VARCHAR},</if>
        <if test="billCheckStatus != null">#{billCheckStatus,jdbcType=TINYINT},</if>
        <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
        <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        <if test="frozenAmount != null">#{frozenAmount,jdbcType=BIGINT},</if>
        <if test="goodsSettleAmount != null">#{goodsSettleAmount,jdbcType=BIGINT},</if>
        <if test="reconciliationAmount != null">#{reconciliationAmount,jdbcType=BIGINT},</if>
    </trim>
    </operation>

    <operation name="getBySettleDayAndPlatform" multiplicity="one" remark="查询待处理的数据列表">
        select
        *
        from
        ACC_PLATFORM_SETTLE_BILL
        where
        SETTLE_DATE = #{settleDate,jdbcType=VARCHAR}
        and PLATFORM_CODE = #{platformCode,jdbcType=VARCHAR}
        and SP_ID = #{spId,jdbcType=VARCHAR}
        limit 1
    </operation>

    <operation name="deleteByBillNo" remark="查询待处理的数据列表">
        delete
        from
        ACC_PLATFORM_SETTLE_BILL
        where
        BILL_NO = #{billNo,jdbcType=VARCHAR}
    </operation>
</table>
