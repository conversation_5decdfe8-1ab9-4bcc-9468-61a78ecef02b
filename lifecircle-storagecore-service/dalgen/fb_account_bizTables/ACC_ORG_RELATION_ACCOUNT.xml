<!DOCTYPE table SYSTEM "../config/table-config-1.0.dtd">
    <table sqlname="ACC_ORG_RELATION_ACCOUNT" physicalName="ACC_ORG_RELATION_ACCOUNT"
    remark="账户组织关联表">
    <!--    &lt;&gt;   <> -->
    <operation name="insert" paramtype="object" remark="insert:ACC_ORG_RELATION_ACCOUNT">
        INSERT INTO ACC_ORG_RELATION_ACCOUNT
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">`ID`,</if>
            <if test="orgId != null">`ORG_ID`,</if>
            <if test="blocId != null">`BLOC_ID`,</if>
            <if test="fullPath != null">`FULL_PATH`,</if>
            <if test="accountId != null">`ACCOUNT_ID`,</if>
            <if test="accountType != null">`ACCOUNT_TYPE`,</if>
            <if test="delFlag != null">`DEL_FLAG`,</if>
            <if test="maxBindNum != null">`MAX_BIND_NUM`,</if>
            <if test="createTime != null">`CREATE_TIME`,</if>
            <if test="updateTime != null">`UPDATE_TIME`,</if>
        </trim>
        VALUES
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="id != null">#{id,jdbcType=BIGINT},</if>
            <if test="orgId != null">#{orgId,jdbcType=VARCHAR},</if>
            <if test="blocId != null">#{blocId,jdbcType=VARCHAR},</if>
            <if test="fullPath != null">#{fullPath,jdbcType=VARCHAR},</if>
            <if test="accountId != null">#{accountId,jdbcType=VARCHAR},</if>
            <if test="accountType != null">#{accountType,jdbcType=VARCHAR},</if>
            <if test="delFlag != null">#{delFlag,jdbcType=TINYINT},</if>
            <if test="maxBindNum != null">#{maxBindNum,jdbcType=SMALLINT},</if>
            <if test="createTime != null">#{createTime,jdbcType=TIMESTAMP},</if>
            <if test="updateTime != null">#{updateTime,jdbcType=TIMESTAMP},</if>
        </trim>
    </operation>

    <operation name="getBlocSubAccount" paramtype="primitive" remark="获取集团子户账户">
        select * from ACC_ORG_RELATION_ACCOUNT
        where bloc_id = #{blocId,jdbcType=VARCHAR}
        and account_type = '3'
        and del_flag = 1
        limit 1
    </operation>
</table>
